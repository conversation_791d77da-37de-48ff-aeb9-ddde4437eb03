{"ast": null, "code": "import React,{useState}from'react';import{Card,Table,Button,Space,Input,Select,Row,Col,Typography,Tag,Modal,Form,Descriptions,Tabs,Alert,Badge,Tooltip,QRCode,message,Divider,Timeline}from'antd';import{PlusOutlined,ExportOutlined,ImportOutlined,QrcodeOutlined,EyeOutlined,EditOutlined,DownloadOutlined,PrinterOutlined,ScanOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Search}=Input;const{Option}=Select;const{TabPane}=Tabs;const{TextArea}=Input;const AsBuiltBOMPage=()=>{const[loading,setLoading]=useState(false);const[searchKeyword,setSearchKeyword]=useState('');const[statusFilter,setStatusFilter]=useState('');const[selectedBOM,setSelectedBOM]=useState(null);const[isModalVisible,setIsModalVisible]=useState(false);const[modalType,setModalType]=useState('view');const[qrModalVisible,setQrModalVisible]=useState(false);const[form]=Form.useForm();// 模拟As-Built BOM数据\nconst mockAsBuiltBOMs=[{id:'1',bomCode:'ASBOM-5G-ANT-001',bomName:'5G天线系统As-Built BOM',productModel:'5G-ANT-64T64R',serialNumber:'SN202401001',orderNumber:'ORD202401001',customerName:'中国移动',productionDate:'2024-03-15T00:00:00Z',shipmentDate:'2024-03-20T00:00:00Z',qrCode:'https://example.com/qr/SN202401001',status:'shipped',version:'V1.0',description:'5G天线系统实际构建BOM，记录实际使用的物料和序列号',createdBy:'生产工程师',createdAt:'2024-03-15T00:00:00Z',lastModified:'2024-03-20T00:00:00Z',totalItems:25,totalValue:15600.00,actualItems:[{id:'1',itemCode:'ANT-ELEM-001',itemName:'天线阵元',specification:'ELEM-64T64R',quantity:64,unit:'PCS',unitPrice:150.00,totalPrice:9600.00,supplier:'华为技术',batchNumber:'BATCH-20240301',serialNumber:'ELEM-001-001~064',installDate:'2024-03-15T00:00:00Z',status:'installed',remarks:'主要天线阵元'},{id:'2',itemCode:'RF-CONN-001',itemName:'射频连接器',specification:'N-TYPE-50Ω',quantity:8,unit:'PCS',unitPrice:25.00,totalPrice:200.00,supplier:'安费诺',batchNumber:'BATCH-20240305',installDate:'2024-03-15T00:00:00Z',status:'installed'}],maintenanceRecords:[{id:'1',date:'2024-03-25T00:00:00Z',type:'preventive',description:'定期维护检查，清洁天线表面，检查连接器',technician:'维护工程师A',partsUsed:[],status:'completed',cost:200.00,nextMaintenanceDate:'2024-06-25T00:00:00Z'}],warrantyInfo:{warrantyPeriod:24,warrantyStartDate:'2024-03-20T00:00:00Z',warrantyEndDate:'2026-03-20T00:00:00Z',warrantyStatus:'active',warrantyTerms:'24个月质保，包含材料和人工费用'}},{id:'2',bomCode:'ASBOM-5G-RRU-001',bomName:'5G射频单元As-Built BOM',productModel:'5G-RRU-3200W',serialNumber:'SN202401002',orderNumber:'ORD202401002',customerName:'中国联通',productionDate:'2024-03-18T00:00:00Z',shipmentDate:'2024-03-22T00:00:00Z',qrCode:'https://example.com/qr/SN202401002',status:'maintenance',version:'V1.1',description:'5G射频拉远单元实际构建BOM',createdBy:'生产工程师',createdAt:'2024-03-18T00:00:00Z',lastModified:'2024-04-01T00:00:00Z',totalItems:18,totalValue:25800.00,actualItems:[{id:'1',itemCode:'PA-MOD-001',itemName:'功率放大器模块',specification:'PA-5G-3200W',quantity:2,unit:'PCS',unitPrice:2500.00,totalPrice:5000.00,supplier:'中兴通讯',batchNumber:'BATCH-20240310',serialNumber:'PA-001-001, PA-001-002',installDate:'2024-03-18T00:00:00Z',status:'replaced',remarks:'其中一个模块在维护时更换'}],maintenanceRecords:[{id:'1',date:'2024-04-01T00:00:00Z',type:'corrective',description:'功率放大器模块故障，更换新模块',technician:'维护工程师B',partsUsed:['PA-MOD-001'],status:'completed',cost:2800.00}],warrantyInfo:{warrantyPeriod:36,warrantyStartDate:'2024-03-22T00:00:00Z',warrantyEndDate:'2027-03-22T00:00:00Z',warrantyStatus:'active',warrantyTerms:'36个月质保，包含材料和人工费用'}}];const formatCurrency=amount=>{return\"\\xA5\".concat(amount.toLocaleString('zh-CN',{minimumFractionDigits:2}));};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString('zh-CN');};const getStatusColor=status=>{const colors={active:'blue',shipped:'green',maintenance:'orange',retired:'red'};return colors[status]||'default';};const getStatusText=status=>{const texts={active:'生产中',shipped:'已发货',maintenance:'维护中',retired:'已退役'};return texts[status]||status;};const handleView=record=>{setSelectedBOM(record);setModalType('view');setIsModalVisible(true);};const handleEdit=record=>{setSelectedBOM(record);setModalType('edit');form.setFieldsValue(record);setIsModalVisible(true);};const handleCreate=()=>{setSelectedBOM(null);setModalType('create');form.resetFields();setIsModalVisible(true);};const handleQRCode=record=>{setSelectedBOM(record);setQrModalVisible(true);};const handleExport=()=>{message.success('导出功能开发中...');};const handleImport=()=>{message.success('导入功能开发中...');};const columns=[{title:'BOM编码',dataIndex:'bomCode',key:'bomCode',width:150,fixed:'left'},{title:'BOM名称',dataIndex:'bomName',key:'bomName',ellipsis:true},{title:'产品型号',dataIndex:'productModel',key:'productModel',width:120},{title:'序列号',dataIndex:'serialNumber',key:'serialNumber',width:120,render:text=>/*#__PURE__*/_jsx(Text,{code:true,children:text})},{title:'订单号',dataIndex:'orderNumber',key:'orderNumber',width:120},{title:'客户',dataIndex:'customerName',key:'customerName',width:120},{title:'生产日期',dataIndex:'productionDate',key:'productionDate',width:100,render:date=>formatDate(date)},{title:'发货日期',dataIndex:'shipmentDate',key:'shipmentDate',width:100,render:date=>formatDate(date)},{title:'项目数',dataIndex:'totalItems',key:'totalItems',width:80,render:count=>/*#__PURE__*/_jsx(Badge,{count:count,showZero:true,color:\"#1890ff\"})},{title:'总价值',dataIndex:'totalValue',key:'totalValue',width:100,render:value=>formatCurrency(value)},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>/*#__PURE__*/_jsx(Tag,{color:getStatusColor(status),children:getStatusText(status)})},{title:'操作',key:'action',width:200,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\\u8BE6\\u60C5\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u4E8C\\u7EF4\\u7801\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(QrcodeOutlined,{}),onClick:()=>handleQRCode(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5BFC\\u51FA\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>message.success('导出功能开发中...')})})]})}];const itemColumns=[{title:'物料编码',dataIndex:'itemCode',key:'itemCode',width:120},{title:'物料名称',dataIndex:'itemName',key:'itemName',ellipsis:true},{title:'规格',dataIndex:'specification',key:'specification',width:150},{title:'数量',dataIndex:'quantity',key:'quantity',width:80},{title:'单位',dataIndex:'unit',key:'unit',width:60},{title:'批次号',dataIndex:'batchNumber',key:'batchNumber',width:120,render:text=>/*#__PURE__*/_jsx(Text,{code:true,children:text})},{title:'序列号',dataIndex:'serialNumber',key:'serialNumber',width:150,render:text=>text&&/*#__PURE__*/_jsx(Text,{code:true,children:text})},{title:'安装日期',dataIndex:'installDate',key:'installDate',width:100,render:date=>formatDate(date)},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>{const colors={installed:'green',replaced:'orange',removed:'red'};const texts={installed:'已安装',replaced:'已更换',removed:'已移除'};return/*#__PURE__*/_jsx(Tag,{color:colors[status],children:texts[status]});}},{title:'单价',dataIndex:'unitPrice',key:'unitPrice',width:100,render:price=>formatCurrency(price)},{title:'总价',dataIndex:'totalPrice',key:'totalPrice',width:100,render:price=>formatCurrency(price)}];const renderStatistics=()=>{const totalBOMs=mockAsBuiltBOMs.length;const activeBOMs=mockAsBuiltBOMs.filter(bom=>bom.status==='active').length;const shippedBOMs=mockAsBuiltBOMs.filter(bom=>bom.status==='shipped').length;const maintenanceBOMs=mockAsBuiltBOMs.filter(bom=>bom.status==='maintenance').length;return/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'24px',fontWeight:'bold',color:'#1890ff'},children:totalBOMs}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#666'},children:\"\\u603B\\u6570\\u91CF\"})]})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'24px',fontWeight:'bold',color:'#52c41a'},children:shippedBOMs}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#666'},children:\"\\u5DF2\\u53D1\\u8D27\"})]})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'24px',fontWeight:'bold',color:'#faad14'},children:maintenanceBOMs}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#666'},children:\"\\u7EF4\\u62A4\\u4E2D\"})]})})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'24px',fontWeight:'bold',color:'#1890ff'},children:activeBOMs}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#666'},children:\"\\u751F\\u4EA7\\u4E2D\"})]})})})]});};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"As-Built BOM\\u7BA1\\u7406\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u7BA1\\u7406\\u4EA7\\u54C1\\u5B9E\\u9645\\u6784\\u5EFABOM\\uFF0C\\u8BB0\\u5F55\\u5B9E\\u9645\\u4F7F\\u7528\\u7684\\u7269\\u6599\\u3001\\u5E8F\\u5217\\u53F7\\u548C\\u7EF4\\u62A4\\u5386\\u53F2\"})]}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22BOM\\u7F16\\u7801\\u3001\\u5E8F\\u5217\\u53F7\",allowClear:true,style:{width:200},onSearch:setSearchKeyword}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u72B6\\u6001\",allowClear:true,style:{width:100},value:statusFilter,onChange:setStatusFilter,options:[{label:'生产中',value:'active'},{label:'已发货',value:'shipped'},{label:'维护中',value:'maintenance'},{label:'已退役',value:'retired'}]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ScanOutlined,{}),children:\"\\u626B\\u7801\\u67E5\\u8BE2\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ImportOutlined,{}),onClick:handleImport,children:\"\\u5BFC\\u5165\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleCreate,children:\"\\u65B0\\u5EFAAs-Built BOM\"})]})})]}),/*#__PURE__*/_jsx(Alert,{message:\"\\u8D28\\u91CF\\u8FFD\\u6EAF\",description:\"As-Built BOM\\u8BB0\\u5F55\\u4E86\\u4EA7\\u54C1\\u7684\\u5B9E\\u9645\\u6784\\u5EFA\\u4FE1\\u606F\\uFF0C\\u652F\\u6301\\u5B8C\\u6574\\u7684\\u8D28\\u91CF\\u8FFD\\u6EAF\\u548C\\u7EF4\\u62A4\\u7BA1\\u7406\\u3002\",type:\"info\",showIcon:true,closable:true,style:{marginBottom:16}}),renderStatistics(),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:mockAsBuiltBOMs,loading:loading,rowKey:\"id\",scroll:{x:1400},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsxs(Modal,{title:modalType==='view'?'As-Built BOM详情':modalType==='edit'?'编辑As-Built BOM':'新建As-Built BOM',open:isModalVisible,onCancel:()=>setIsModalVisible(false),width:1200,footer:modalType==='view'?[/*#__PURE__*/_jsx(Button,{onClick:()=>setIsModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")]:[/*#__PURE__*/_jsx(Button,{onClick:()=>setIsModalVisible(false),children:\"\\u53D6\\u6D88\"},\"cancel\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",children:modalType==='edit'?'保存':'创建'},\"submit\")],children:[selectedBOM&&modalType==='view'&&/*#__PURE__*/_jsxs(Tabs,{defaultActiveKey:\"basic\",children:[/*#__PURE__*/_jsx(TabPane,{tab:\"\\u57FA\\u672C\\u4FE1\\u606F\",children:/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:2,children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"BOM\\u7F16\\u7801\",children:selectedBOM.bomCode}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"BOM\\u540D\\u79F0\",children:selectedBOM.bomName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u578B\\u53F7\",children:selectedBOM.productModel}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5E8F\\u5217\\u53F7\",children:/*#__PURE__*/_jsx(Text,{code:true,children:selectedBOM.serialNumber})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u53F7\",children:selectedBOM.orderNumber}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u540D\\u79F0\",children:selectedBOM.customerName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u751F\\u4EA7\\u65E5\\u671F\",children:formatDate(selectedBOM.productionDate)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u53D1\\u8D27\\u65E5\\u671F\",children:formatDate(selectedBOM.shipmentDate)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7248\\u672C\",children:selectedBOM.version}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Tag,{color:getStatusColor(selectedBOM.status),children:getStatusText(selectedBOM.status)})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u9879\\u76EE\\u6570\\u91CF\",children:selectedBOM.totalItems}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u603B\\u4EF7\\u503C\",children:formatCurrency(selectedBOM.totalValue)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u521B\\u5EFA\\u4EBA\",children:selectedBOM.createdBy}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u521B\\u5EFA\\u65F6\\u95F4\",children:formatDate(selectedBOM.createdAt)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u63CF\\u8FF0\",span:2,children:selectedBOM.description})]})},\"basic\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u7269\\u6599\\u660E\\u7EC6\",children:/*#__PURE__*/_jsx(Table,{columns:itemColumns,dataSource:selectedBOM.actualItems,rowKey:\"id\",scroll:{x:1200},pagination:false,size:\"small\"})},\"items\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u7EF4\\u62A4\\u8BB0\\u5F55\",children:/*#__PURE__*/_jsx(Timeline,{children:selectedBOM.maintenanceRecords.map(record=>/*#__PURE__*/_jsxs(Timeline.Item,{color:record.status==='completed'?'green':'blue',children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:record.type==='preventive'?'预防性维护':record.type==='corrective'?'纠正性维护':'升级维护'}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{marginLeft:8},children:formatDate(record.date)})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:/*#__PURE__*/_jsx(Text,{children:record.description})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:4},children:[/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u6280\\u672F\\u5458: \",record.technician]}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{marginLeft:16},children:[\"\\u8D39\\u7528: \",formatCurrency(record.cost)]}),/*#__PURE__*/_jsx(Tag,{color:record.status==='completed'?'green':'processing',style:{marginLeft:8},children:record.status==='completed'?'已完成':record.status==='in_progress'?'进行中':'已计划'})]}),record.partsUsed.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u4F7F\\u7528\\u5907\\u4EF6: \",record.partsUsed.join(', ')]})})]},record.id))})},\"maintenance\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u8D28\\u4FDD\\u4FE1\\u606F\",children:/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:2,children:[/*#__PURE__*/_jsxs(Descriptions.Item,{label:\"\\u8D28\\u4FDD\\u671F\",children:[selectedBOM.warrantyInfo.warrantyPeriod,\"\\u4E2A\\u6708\"]}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D28\\u4FDD\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Tag,{color:selectedBOM.warrantyInfo.warrantyStatus==='active'?'green':'red',children:selectedBOM.warrantyInfo.warrantyStatus==='active'?'有效':selectedBOM.warrantyInfo.warrantyStatus==='expired'?'已过期':'已失效'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D28\\u4FDD\\u5F00\\u59CB\\u65E5\\u671F\",children:formatDate(selectedBOM.warrantyInfo.warrantyStartDate)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D28\\u4FDD\\u7ED3\\u675F\\u65E5\\u671F\",children:formatDate(selectedBOM.warrantyInfo.warrantyEndDate)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D28\\u4FDD\\u6761\\u6B3E\",span:2,children:selectedBOM.warrantyInfo.warrantyTerms})]})},\"warranty\")]}),modalType!=='view'&&/*#__PURE__*/_jsx(Form,{form:form,layout:\"vertical\",children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"BOM\\u7F16\\u7801\",name:\"bomCode\",rules:[{required:true}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u7F16\\u7801\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"BOM\\u540D\\u79F0\",name:\"bomName\",rules:[{required:true}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u4EA7\\u54C1\\u578B\\u53F7\",name:\"productModel\",rules:[{required:true}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u4EA7\\u54C1\\u578B\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5E8F\\u5217\\u53F7\",name:\"serialNumber\",rules:[{required:true}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5E8F\\u5217\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u8BA2\\u5355\\u53F7\",name:\"orderNumber\",rules:[{required:true}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BA2\\u5355\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BA2\\u6237\\u540D\\u79F0\",name:\"customerName\",rules:[{required:true}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u63CF\\u8FF0\",name:\"description\",children:/*#__PURE__*/_jsx(TextArea,{rows:3,placeholder:\"\\u8BF7\\u8F93\\u5165\\u63CF\\u8FF0\"})})})]})})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u8BBE\\u5907\\u4E8C\\u7EF4\\u7801\",open:qrModalVisible,onCancel:()=>setQrModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(PrinterOutlined,{}),children:\"\\u6253\\u5370\"},\"print\"),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),children:\"\\u4E0B\\u8F7D\"},\"download\"),/*#__PURE__*/_jsx(Button,{onClick:()=>setQrModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],children:selectedBOM&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(QRCode,{value:\"\".concat(selectedBOM.qrCode,\"?sn=\").concat(selectedBOM.serialNumber),size:200}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(Descriptions,{column:1,size:\"small\",children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u578B\\u53F7\",children:selectedBOM.productModel}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5E8F\\u5217\\u53F7\",children:selectedBOM.serialNumber}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u751F\\u4EA7\\u65E5\\u671F\",children:formatDate(selectedBOM.productionDate)})]})]})})]});};export default AsBuiltBOMPage;", "map": {"version": 3, "names": ["React", "useState", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Row", "Col", "Typography", "Tag", "Modal", "Form", "Descriptions", "Tabs", "<PERSON><PERSON>", "Badge", "<PERSON><PERSON><PERSON>", "QRCode", "message", "Divider", "Timeline", "PlusOutlined", "ExportOutlined", "ImportOutlined", "QrcodeOutlined", "EyeOutlined", "EditOutlined", "DownloadOutlined", "PrinterOutlined", "ScanOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Search", "Option", "TabPane", "TextArea", "AsBuiltBOMPage", "loading", "setLoading", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "selectedBOM", "setSelectedBOM", "isModalVisible", "setIsModalVisible", "modalType", "setModalType", "qrModalVisible", "setQrModalVisible", "form", "useForm", "mockAsBuiltBOMs", "id", "bomCode", "bom<PERSON>ame", "productModel", "serialNumber", "orderNumber", "customerName", "productionDate", "shipmentDate", "qrCode", "status", "version", "description", "created<PERSON>y", "createdAt", "lastModified", "totalItems", "totalValue", "actualItems", "itemCode", "itemName", "specification", "quantity", "unit", "unitPrice", "totalPrice", "supplier", "batchNumber", "installDate", "remarks", "maintenanceRecords", "date", "type", "technician", "partsUsed", "cost", "nextMaintenanceDate", "warrantyInfo", "warrantyPeriod", "warrantyStartDate", "warrantyEndDate", "warrantyStatus", "warrantyTerms", "formatCurrency", "amount", "concat", "toLocaleString", "minimumFractionDigits", "formatDate", "dateString", "Date", "toLocaleDateString", "getStatusColor", "colors", "active", "shipped", "maintenance", "retired", "getStatusText", "texts", "handleView", "record", "handleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleCreate", "resetFields", "handleQRCode", "handleExport", "success", "handleImport", "columns", "title", "dataIndex", "key", "width", "fixed", "ellipsis", "render", "text", "code", "children", "count", "showZero", "color", "value", "_", "size", "icon", "onClick", "itemColumns", "installed", "replaced", "removed", "price", "renderStatistics", "totalBOMs", "length", "activeBOMs", "filter", "bom", "shippedBOMs", "maintenanceBOMs", "gutter", "style", "marginBottom", "span", "textAlign", "fontSize", "fontWeight", "justify", "align", "level", "margin", "placeholder", "allowClear", "onSearch", "onChange", "options", "label", "showIcon", "closable", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onCancel", "footer", "defaultActiveKey", "tab", "bordered", "column", "<PERSON><PERSON>", "map", "strong", "marginLeft", "marginTop", "join", "layout", "name", "rules", "required", "rows"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/service/AsBuiltBOMPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Row,\n  Col,\n  Typography,\n  Tag,\n  Modal,\n  Form,\n  Descriptions,\n  Tabs,\n  Alert,\n  Badge,\n  Tooltip,\n  QRCode,\n  message,\n  Divider,\n  Timeline,\n  Upload,\n  DatePicker,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  QrcodeOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  DownloadOutlined,\n  PrinterOutlined,\n  ScanOutlined,\n  HistoryOutlined,\n  ToolOutlined,\n  FileTextOutlined,\n  CloudUploadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\nconst { TextArea } = Input;\n\ninterface AsBuiltBOM {\n  id: string;\n  bomCode: string;\n  bomName: string;\n  productModel: string;\n  serialNumber: string;\n  orderNumber: string;\n  customerName: string;\n  productionDate: string;\n  shipmentDate: string;\n  qrCode: string;\n  status: 'active' | 'shipped' | 'maintenance' | 'retired';\n  version: string;\n  description: string;\n  createdBy: string;\n  createdAt: string;\n  lastModified: string;\n  totalItems: number;\n  totalValue: number;\n  actualItems: AsBuiltItem[];\n  maintenanceRecords: MaintenanceRecord[];\n  warrantyInfo: WarrantyInfo;\n}\n\ninterface AsBuiltItem {\n  id: string;\n  itemCode: string;\n  itemName: string;\n  specification: string;\n  quantity: number;\n  unit: string;\n  unitPrice: number;\n  totalPrice: number;\n  supplier: string;\n  batchNumber: string;\n  serialNumber?: string;\n  installDate: string;\n  status: 'installed' | 'replaced' | 'removed';\n  remarks?: string;\n}\n\ninterface MaintenanceRecord {\n  id: string;\n  date: string;\n  type: 'preventive' | 'corrective' | 'upgrade';\n  description: string;\n  technician: string;\n  partsUsed: string[];\n  status: 'completed' | 'in_progress' | 'scheduled';\n  cost: number;\n  nextMaintenanceDate?: string;\n}\n\ninterface WarrantyInfo {\n  warrantyPeriod: number; // 月\n  warrantyStartDate: string;\n  warrantyEndDate: string;\n  warrantyStatus: 'active' | 'expired' | 'void';\n  warrantyTerms: string;\n}\n\nconst AsBuiltBOMPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [selectedBOM, setSelectedBOM] = useState<AsBuiltBOM | null>(null);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [modalType, setModalType] = useState<'view' | 'create' | 'edit'>('view');\n  const [qrModalVisible, setQrModalVisible] = useState(false);\n  const [form] = Form.useForm();\n\n  // 模拟As-Built BOM数据\n  const mockAsBuiltBOMs: AsBuiltBOM[] = [\n    {\n      id: '1',\n      bomCode: 'ASBOM-5G-ANT-001',\n      bomName: '5G天线系统As-Built BOM',\n      productModel: '5G-ANT-64T64R',\n      serialNumber: 'SN202401001',\n      orderNumber: 'ORD202401001',\n      customerName: '中国移动',\n      productionDate: '2024-03-15T00:00:00Z',\n      shipmentDate: '2024-03-20T00:00:00Z',\n      qrCode: 'https://example.com/qr/SN202401001',\n      status: 'shipped',\n      version: 'V1.0',\n      description: '5G天线系统实际构建BOM，记录实际使用的物料和序列号',\n      createdBy: '生产工程师',\n      createdAt: '2024-03-15T00:00:00Z',\n      lastModified: '2024-03-20T00:00:00Z',\n      totalItems: 25,\n      totalValue: 15600.00,\n      actualItems: [\n        {\n          id: '1',\n          itemCode: 'ANT-ELEM-001',\n          itemName: '天线阵元',\n          specification: 'ELEM-64T64R',\n          quantity: 64,\n          unit: 'PCS',\n          unitPrice: 150.00,\n          totalPrice: 9600.00,\n          supplier: '华为技术',\n          batchNumber: 'BATCH-20240301',\n          serialNumber: 'ELEM-001-001~064',\n          installDate: '2024-03-15T00:00:00Z',\n          status: 'installed',\n          remarks: '主要天线阵元',\n        },\n        {\n          id: '2',\n          itemCode: 'RF-CONN-001',\n          itemName: '射频连接器',\n          specification: 'N-TYPE-50Ω',\n          quantity: 8,\n          unit: 'PCS',\n          unitPrice: 25.00,\n          totalPrice: 200.00,\n          supplier: '安费诺',\n          batchNumber: 'BATCH-20240305',\n          installDate: '2024-03-15T00:00:00Z',\n          status: 'installed',\n        },\n      ],\n      maintenanceRecords: [\n        {\n          id: '1',\n          date: '2024-03-25T00:00:00Z',\n          type: 'preventive',\n          description: '定期维护检查，清洁天线表面，检查连接器',\n          technician: '维护工程师A',\n          partsUsed: [],\n          status: 'completed',\n          cost: 200.00,\n          nextMaintenanceDate: '2024-06-25T00:00:00Z',\n        },\n      ],\n      warrantyInfo: {\n        warrantyPeriod: 24,\n        warrantyStartDate: '2024-03-20T00:00:00Z',\n        warrantyEndDate: '2026-03-20T00:00:00Z',\n        warrantyStatus: 'active',\n        warrantyTerms: '24个月质保，包含材料和人工费用',\n      },\n    },\n    {\n      id: '2',\n      bomCode: 'ASBOM-5G-RRU-001',\n      bomName: '5G射频单元As-Built BOM',\n      productModel: '5G-RRU-3200W',\n      serialNumber: 'SN202401002',\n      orderNumber: 'ORD202401002',\n      customerName: '中国联通',\n      productionDate: '2024-03-18T00:00:00Z',\n      shipmentDate: '2024-03-22T00:00:00Z',\n      qrCode: 'https://example.com/qr/SN202401002',\n      status: 'maintenance',\n      version: 'V1.1',\n      description: '5G射频拉远单元实际构建BOM',\n      createdBy: '生产工程师',\n      createdAt: '2024-03-18T00:00:00Z',\n      lastModified: '2024-04-01T00:00:00Z',\n      totalItems: 18,\n      totalValue: 25800.00,\n      actualItems: [\n        {\n          id: '1',\n          itemCode: 'PA-MOD-001',\n          itemName: '功率放大器模块',\n          specification: 'PA-5G-3200W',\n          quantity: 2,\n          unit: 'PCS',\n          unitPrice: 2500.00,\n          totalPrice: 5000.00,\n          supplier: '中兴通讯',\n          batchNumber: 'BATCH-20240310',\n          serialNumber: 'PA-001-001, PA-001-002',\n          installDate: '2024-03-18T00:00:00Z',\n          status: 'replaced',\n          remarks: '其中一个模块在维护时更换',\n        },\n      ],\n      maintenanceRecords: [\n        {\n          id: '1',\n          date: '2024-04-01T00:00:00Z',\n          type: 'corrective',\n          description: '功率放大器模块故障，更换新模块',\n          technician: '维护工程师B',\n          partsUsed: ['PA-MOD-001'],\n          status: 'completed',\n          cost: 2800.00,\n        },\n      ],\n      warrantyInfo: {\n        warrantyPeriod: 36,\n        warrantyStartDate: '2024-03-22T00:00:00Z',\n        warrantyEndDate: '2027-03-22T00:00:00Z',\n        warrantyStatus: 'active',\n        warrantyTerms: '36个月质保，包含材料和人工费用',\n      },\n    },\n  ];\n\n  const formatCurrency = (amount: number) => {\n    return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN');\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors = {\n      active: 'blue',\n      shipped: 'green',\n      maintenance: 'orange',\n      retired: 'red',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n  const getStatusText = (status: string) => {\n    const texts = {\n      active: '生产中',\n      shipped: '已发货',\n      maintenance: '维护中',\n      retired: '已退役',\n    };\n    return texts[status as keyof typeof texts] || status;\n  };\n\n  const handleView = (record: AsBuiltBOM) => {\n    setSelectedBOM(record);\n    setModalType('view');\n    setIsModalVisible(true);\n  };\n\n  const handleEdit = (record: AsBuiltBOM) => {\n    setSelectedBOM(record);\n    setModalType('edit');\n    form.setFieldsValue(record);\n    setIsModalVisible(true);\n  };\n\n  const handleCreate = () => {\n    setSelectedBOM(null);\n    setModalType('create');\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleQRCode = (record: AsBuiltBOM) => {\n    setSelectedBOM(record);\n    setQrModalVisible(true);\n  };\n\n  const handleExport = () => {\n    message.success('导出功能开发中...');\n  };\n\n  const handleImport = () => {\n    message.success('导入功能开发中...');\n  };\n\n  const columns: ColumnsType<AsBuiltBOM> = [\n    {\n      title: 'BOM编码',\n      dataIndex: 'bomCode',\n      key: 'bomCode',\n      width: 150,\n      fixed: 'left',\n    },\n    {\n      title: 'BOM名称',\n      dataIndex: 'bomName',\n      key: 'bomName',\n      ellipsis: true,\n    },\n    {\n      title: '产品型号',\n      dataIndex: 'productModel',\n      key: 'productModel',\n      width: 120,\n    },\n    {\n      title: '序列号',\n      dataIndex: 'serialNumber',\n      key: 'serialNumber',\n      width: 120,\n      render: (text: string) => (\n        <Text code>{text}</Text>\n      ),\n    },\n    {\n      title: '订单号',\n      dataIndex: 'orderNumber',\n      key: 'orderNumber',\n      width: 120,\n    },\n    {\n      title: '客户',\n      dataIndex: 'customerName',\n      key: 'customerName',\n      width: 120,\n    },\n    {\n      title: '生产日期',\n      dataIndex: 'productionDate',\n      key: 'productionDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '发货日期',\n      dataIndex: 'shipmentDate',\n      key: 'shipmentDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '项目数',\n      dataIndex: 'totalItems',\n      key: 'totalItems',\n      width: 80,\n      render: (count: number) => (\n        <Badge count={count} showZero color=\"#1890ff\" />\n      ),\n    },\n    {\n      title: '总价值',\n      dataIndex: 'totalValue',\n      key: 'totalValue',\n      width: 100,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"二维码\">\n            <Button\n              type=\"text\"\n              icon={<QrcodeOutlined />}\n              onClick={() => handleQRCode(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"导出\">\n            <Button\n              type=\"text\"\n              icon={<DownloadOutlined />}\n              onClick={() => message.success('导出功能开发中...')}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  const itemColumns: ColumnsType<AsBuiltItem> = [\n    {\n      title: '物料编码',\n      dataIndex: 'itemCode',\n      key: 'itemCode',\n      width: 120,\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'itemName',\n      key: 'itemName',\n      ellipsis: true,\n    },\n    {\n      title: '规格',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 150,\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n    },\n    {\n      title: '单位',\n      dataIndex: 'unit',\n      key: 'unit',\n      width: 60,\n    },\n    {\n      title: '批次号',\n      dataIndex: 'batchNumber',\n      key: 'batchNumber',\n      width: 120,\n      render: (text: string) => (\n        <Text code>{text}</Text>\n      ),\n    },\n    {\n      title: '序列号',\n      dataIndex: 'serialNumber',\n      key: 'serialNumber',\n      width: 150,\n      render: (text: string) => text && (\n        <Text code>{text}</Text>\n      ),\n    },\n    {\n      title: '安装日期',\n      dataIndex: 'installDate',\n      key: 'installDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => {\n        const colors = {\n          installed: 'green',\n          replaced: 'orange',\n          removed: 'red',\n        };\n        const texts = {\n          installed: '已安装',\n          replaced: '已更换',\n          removed: '已移除',\n        };\n        return (\n          <Tag color={colors[status as keyof typeof colors]}>\n            {texts[status as keyof typeof texts]}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      width: 100,\n      render: (price: number) => formatCurrency(price),\n    },\n    {\n      title: '总价',\n      dataIndex: 'totalPrice',\n      key: 'totalPrice',\n      width: 100,\n      render: (price: number) => formatCurrency(price),\n    },\n  ];\n\n  const renderStatistics = () => {\n    const totalBOMs = mockAsBuiltBOMs.length;\n    const activeBOMs = mockAsBuiltBOMs.filter(bom => bom.status === 'active').length;\n    const shippedBOMs = mockAsBuiltBOMs.filter(bom => bom.status === 'shipped').length;\n    const maintenanceBOMs = mockAsBuiltBOMs.filter(bom => bom.status === 'maintenance').length;\n\n    return (\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card size=\"small\">\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>\n                {totalBOMs}\n              </div>\n              <div style={{ color: '#666' }}>总数量</div>\n            </div>\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>\n                {shippedBOMs}\n              </div>\n              <div style={{ color: '#666' }}>已发货</div>\n            </div>\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>\n                {maintenanceBOMs}\n              </div>\n              <div style={{ color: '#666' }}>维护中</div>\n            </div>\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>\n                {activeBOMs}\n              </div>\n              <div style={{ color: '#666' }}>生产中</div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    );\n  };\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              As-Built BOM管理\n            </Title>\n            <Text type=\"secondary\">\n              管理产品实际构建BOM，记录实际使用的物料、序列号和维护历史\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索BOM编码、序列号\"\n                allowClear\n                style={{ width: 200 }}\n                onSearch={setSearchKeyword}\n              />\n              <Select\n                placeholder=\"状态\"\n                allowClear\n                style={{ width: 100 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '生产中', value: 'active' },\n                  { label: '已发货', value: 'shipped' },\n                  { label: '维护中', value: 'maintenance' },\n                  { label: '已退役', value: 'retired' },\n                ]}\n              />\n              <Button icon={<ScanOutlined />}>\n                扫码查询\n              </Button>\n              <Button icon={<ImportOutlined />} onClick={handleImport}>\n                导入\n              </Button>\n              <Button icon={<ExportOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleCreate}>\n                新建As-Built BOM\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Alert\n          message=\"质量追溯\"\n          description=\"As-Built BOM记录了产品的实际构建信息，支持完整的质量追溯和维护管理。\"\n          type=\"info\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n\n        {renderStatistics()}\n\n        <Table\n          columns={columns}\n          dataSource={mockAsBuiltBOMs}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 详情/编辑模态框 */}\n      <Modal\n        title={\n          modalType === 'view' ? 'As-Built BOM详情' :\n          modalType === 'edit' ? '编辑As-Built BOM' : '新建As-Built BOM'\n        }\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        width={1200}\n        footer={\n          modalType === 'view' ? [\n            <Button key=\"close\" onClick={() => setIsModalVisible(false)}>\n              关闭\n            </Button>\n          ] : [\n            <Button key=\"cancel\" onClick={() => setIsModalVisible(false)}>\n              取消\n            </Button>,\n            <Button key=\"submit\" type=\"primary\">\n              {modalType === 'edit' ? '保存' : '创建'}\n            </Button>\n          ]\n        }\n      >\n        {selectedBOM && modalType === 'view' && (\n          <Tabs defaultActiveKey=\"basic\">\n            <TabPane tab=\"基本信息\" key=\"basic\">\n              <Descriptions bordered column={2}>\n                <Descriptions.Item label=\"BOM编码\">{selectedBOM.bomCode}</Descriptions.Item>\n                <Descriptions.Item label=\"BOM名称\">{selectedBOM.bomName}</Descriptions.Item>\n                <Descriptions.Item label=\"产品型号\">{selectedBOM.productModel}</Descriptions.Item>\n                <Descriptions.Item label=\"序列号\">\n                  <Text code>{selectedBOM.serialNumber}</Text>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"订单号\">{selectedBOM.orderNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"客户名称\">{selectedBOM.customerName}</Descriptions.Item>\n                <Descriptions.Item label=\"生产日期\">{formatDate(selectedBOM.productionDate)}</Descriptions.Item>\n                <Descriptions.Item label=\"发货日期\">{formatDate(selectedBOM.shipmentDate)}</Descriptions.Item>\n                <Descriptions.Item label=\"版本\">{selectedBOM.version}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Tag color={getStatusColor(selectedBOM.status)}>\n                    {getStatusText(selectedBOM.status)}\n                  </Tag>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"项目数量\">{selectedBOM.totalItems}</Descriptions.Item>\n                <Descriptions.Item label=\"总价值\">{formatCurrency(selectedBOM.totalValue)}</Descriptions.Item>\n                <Descriptions.Item label=\"创建人\">{selectedBOM.createdBy}</Descriptions.Item>\n                <Descriptions.Item label=\"创建时间\">{formatDate(selectedBOM.createdAt)}</Descriptions.Item>\n                <Descriptions.Item label=\"描述\" span={2}>{selectedBOM.description}</Descriptions.Item>\n              </Descriptions>\n            </TabPane>\n            <TabPane tab=\"物料明细\" key=\"items\">\n              <Table\n                columns={itemColumns}\n                dataSource={selectedBOM.actualItems}\n                rowKey=\"id\"\n                scroll={{ x: 1200 }}\n                pagination={false}\n                size=\"small\"\n              />\n            </TabPane>\n            <TabPane tab=\"维护记录\" key=\"maintenance\">\n              <Timeline>\n                {selectedBOM.maintenanceRecords.map((record) => (\n                  <Timeline.Item\n                    key={record.id}\n                    color={record.status === 'completed' ? 'green' : 'blue'}\n                  >\n                    <div>\n                      <Text strong>{record.type === 'preventive' ? '预防性维护' : \n                                   record.type === 'corrective' ? '纠正性维护' : '升级维护'}</Text>\n                      <Text type=\"secondary\" style={{ marginLeft: 8 }}>\n                        {formatDate(record.date)}\n                      </Text>\n                    </div>\n                    <div style={{ marginTop: 4 }}>\n                      <Text>{record.description}</Text>\n                    </div>\n                    <div style={{ marginTop: 4 }}>\n                      <Text type=\"secondary\">技术员: {record.technician}</Text>\n                      <Text type=\"secondary\" style={{ marginLeft: 16 }}>\n                        费用: {formatCurrency(record.cost)}\n                      </Text>\n                      <Tag\n                        color={record.status === 'completed' ? 'green' : 'processing'}\n                        style={{ marginLeft: 8 }}\n                      >\n                        {record.status === 'completed' ? '已完成' : \n                         record.status === 'in_progress' ? '进行中' : '已计划'}\n                      </Tag>\n                    </div>\n                    {record.partsUsed.length > 0 && (\n                      <div style={{ marginTop: 4 }}>\n                        <Text type=\"secondary\">使用备件: {record.partsUsed.join(', ')}</Text>\n                      </div>\n                    )}\n                  </Timeline.Item>\n                ))}\n              </Timeline>\n            </TabPane>\n            <TabPane tab=\"质保信息\" key=\"warranty\">\n              <Descriptions bordered column={2}>\n                <Descriptions.Item label=\"质保期\">{selectedBOM.warrantyInfo.warrantyPeriod}个月</Descriptions.Item>\n                <Descriptions.Item label=\"质保状态\">\n                  <Tag color={selectedBOM.warrantyInfo.warrantyStatus === 'active' ? 'green' : 'red'}>\n                    {selectedBOM.warrantyInfo.warrantyStatus === 'active' ? '有效' : \n                     selectedBOM.warrantyInfo.warrantyStatus === 'expired' ? '已过期' : '已失效'}\n                  </Tag>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"质保开始日期\">\n                  {formatDate(selectedBOM.warrantyInfo.warrantyStartDate)}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"质保结束日期\">\n                  {formatDate(selectedBOM.warrantyInfo.warrantyEndDate)}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"质保条款\" span={2}>\n                  {selectedBOM.warrantyInfo.warrantyTerms}\n                </Descriptions.Item>\n              </Descriptions>\n            </TabPane>\n          </Tabs>\n        )}\n\n        {modalType !== 'view' && (\n          <Form form={form} layout=\"vertical\">\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item label=\"BOM编码\" name=\"bomCode\" rules={[{ required: true }]}>\n                  <Input placeholder=\"请输入BOM编码\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item label=\"BOM名称\" name=\"bomName\" rules={[{ required: true }]}>\n                  <Input placeholder=\"请输入BOM名称\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item label=\"产品型号\" name=\"productModel\" rules={[{ required: true }]}>\n                  <Input placeholder=\"请输入产品型号\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item label=\"序列号\" name=\"serialNumber\" rules={[{ required: true }]}>\n                  <Input placeholder=\"请输入序列号\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item label=\"订单号\" name=\"orderNumber\" rules={[{ required: true }]}>\n                  <Input placeholder=\"请输入订单号\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item label=\"客户名称\" name=\"customerName\" rules={[{ required: true }]}>\n                  <Input placeholder=\"请输入客户名称\" />\n                </Form.Item>\n              </Col>\n              <Col span={24}>\n                <Form.Item label=\"描述\" name=\"description\">\n                  <TextArea rows={3} placeholder=\"请输入描述\" />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Form>\n        )}\n      </Modal>\n\n      {/* 二维码模态框 */}\n      <Modal\n        title=\"设备二维码\"\n        open={qrModalVisible}\n        onCancel={() => setQrModalVisible(false)}\n        footer={[\n          <Button key=\"print\" icon={<PrinterOutlined />}>\n            打印\n          </Button>,\n          <Button key=\"download\" icon={<DownloadOutlined />}>\n            下载\n          </Button>,\n          <Button key=\"close\" onClick={() => setQrModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n      >\n        {selectedBOM && (\n          <div style={{ textAlign: 'center' }}>\n            <QRCode\n              value={`${selectedBOM.qrCode}?sn=${selectedBOM.serialNumber}`}\n              size={200}\n            />\n            <Divider />\n            <Descriptions column={1} size=\"small\">\n              <Descriptions.Item label=\"产品型号\">{selectedBOM.productModel}</Descriptions.Item>\n              <Descriptions.Item label=\"序列号\">{selectedBOM.serialNumber}</Descriptions.Item>\n              <Descriptions.Item label=\"生产日期\">{formatDate(selectedBOM.productionDate)}</Descriptions.Item>\n            </Descriptions>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default AsBuiltBOMPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAmB,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,GAAG,CACHC,GAAG,CACHC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,YAAY,CACZC,IAAI,CACJC,KAAK,CACLC,KAAK,CACLC,OAAO,CACPC,MAAM,CACNC,OAAO,CACPC,OAAO,CACPC,QAAQ,KAGH,MAAM,CACb,OACEC,YAAY,CAEZC,cAAc,CACdC,cAAc,CACdC,cAAc,CACdC,WAAW,CACXC,YAAY,CAEZC,gBAAgB,CAChBC,eAAe,CACfC,YAAY,KAKP,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG3B,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG3B,UAAU,CAClC,KAAM,CAAE4B,MAAO,CAAC,CAAGhC,KAAK,CACxB,KAAM,CAAEiC,MAAO,CAAC,CAAGhC,MAAM,CACzB,KAAM,CAAEiC,OAAQ,CAAC,CAAGzB,IAAI,CACxB,KAAM,CAAE0B,QAAS,CAAC,CAAGnC,KAAK,CA+D1B,KAAM,CAAAoC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC4C,aAAa,CAAEC,gBAAgB,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC8C,YAAY,CAAEC,eAAe,CAAC,CAAG/C,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACgD,WAAW,CAAEC,cAAc,CAAC,CAAGjD,QAAQ,CAAoB,IAAI,CAAC,CACvE,KAAM,CAACkD,cAAc,CAAEC,iBAAiB,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACoD,SAAS,CAAEC,YAAY,CAAC,CAAGrD,QAAQ,CAA6B,MAAM,CAAC,CAC9E,KAAM,CAACsD,cAAc,CAAEC,iBAAiB,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACwD,IAAI,CAAC,CAAG5C,IAAI,CAAC6C,OAAO,CAAC,CAAC,CAE7B;AACA,KAAM,CAAAC,eAA6B,CAAG,CACpC,CACEC,EAAE,CAAE,GAAG,CACPC,OAAO,CAAE,kBAAkB,CAC3BC,OAAO,CAAE,oBAAoB,CAC7BC,YAAY,CAAE,eAAe,CAC7BC,YAAY,CAAE,aAAa,CAC3BC,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,MAAM,CACpBC,cAAc,CAAE,sBAAsB,CACtCC,YAAY,CAAE,sBAAsB,CACpCC,MAAM,CAAE,oCAAoC,CAC5CC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,MAAM,CACfC,WAAW,CAAE,6BAA6B,CAC1CC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,sBAAsB,CACjCC,YAAY,CAAE,sBAAsB,CACpCC,UAAU,CAAE,EAAE,CACdC,UAAU,CAAE,QAAQ,CACpBC,WAAW,CAAE,CACX,CACElB,EAAE,CAAE,GAAG,CACPmB,QAAQ,CAAE,cAAc,CACxBC,QAAQ,CAAE,MAAM,CAChBC,aAAa,CAAE,aAAa,CAC5BC,QAAQ,CAAE,EAAE,CACZC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,MAAM,CACjBC,UAAU,CAAE,OAAO,CACnBC,QAAQ,CAAE,MAAM,CAChBC,WAAW,CAAE,gBAAgB,CAC7BvB,YAAY,CAAE,kBAAkB,CAChCwB,WAAW,CAAE,sBAAsB,CACnClB,MAAM,CAAE,WAAW,CACnBmB,OAAO,CAAE,QACX,CAAC,CACD,CACE7B,EAAE,CAAE,GAAG,CACPmB,QAAQ,CAAE,aAAa,CACvBC,QAAQ,CAAE,OAAO,CACjBC,aAAa,CAAE,YAAY,CAC3BC,QAAQ,CAAE,CAAC,CACXC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,KAAK,CAChBC,UAAU,CAAE,MAAM,CAClBC,QAAQ,CAAE,KAAK,CACfC,WAAW,CAAE,gBAAgB,CAC7BC,WAAW,CAAE,sBAAsB,CACnClB,MAAM,CAAE,WACV,CAAC,CACF,CACDoB,kBAAkB,CAAE,CAClB,CACE9B,EAAE,CAAE,GAAG,CACP+B,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,YAAY,CAClBpB,WAAW,CAAE,qBAAqB,CAClCqB,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,EAAE,CACbxB,MAAM,CAAE,WAAW,CACnByB,IAAI,CAAE,MAAM,CACZC,mBAAmB,CAAE,sBACvB,CAAC,CACF,CACDC,YAAY,CAAE,CACZC,cAAc,CAAE,EAAE,CAClBC,iBAAiB,CAAE,sBAAsB,CACzCC,eAAe,CAAE,sBAAsB,CACvCC,cAAc,CAAE,QAAQ,CACxBC,aAAa,CAAE,kBACjB,CACF,CAAC,CACD,CACE1C,EAAE,CAAE,GAAG,CACPC,OAAO,CAAE,kBAAkB,CAC3BC,OAAO,CAAE,oBAAoB,CAC7BC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,aAAa,CAC3BC,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,MAAM,CACpBC,cAAc,CAAE,sBAAsB,CACtCC,YAAY,CAAE,sBAAsB,CACpCC,MAAM,CAAE,oCAAoC,CAC5CC,MAAM,CAAE,aAAa,CACrBC,OAAO,CAAE,MAAM,CACfC,WAAW,CAAE,iBAAiB,CAC9BC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,sBAAsB,CACjCC,YAAY,CAAE,sBAAsB,CACpCC,UAAU,CAAE,EAAE,CACdC,UAAU,CAAE,QAAQ,CACpBC,WAAW,CAAE,CACX,CACElB,EAAE,CAAE,GAAG,CACPmB,QAAQ,CAAE,YAAY,CACtBC,QAAQ,CAAE,SAAS,CACnBC,aAAa,CAAE,aAAa,CAC5BC,QAAQ,CAAE,CAAC,CACXC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,OAAO,CACnBC,QAAQ,CAAE,MAAM,CAChBC,WAAW,CAAE,gBAAgB,CAC7BvB,YAAY,CAAE,wBAAwB,CACtCwB,WAAW,CAAE,sBAAsB,CACnClB,MAAM,CAAE,UAAU,CAClBmB,OAAO,CAAE,cACX,CAAC,CACF,CACDC,kBAAkB,CAAE,CAClB,CACE9B,EAAE,CAAE,GAAG,CACP+B,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,YAAY,CAClBpB,WAAW,CAAE,iBAAiB,CAC9BqB,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,CAAC,YAAY,CAAC,CACzBxB,MAAM,CAAE,WAAW,CACnByB,IAAI,CAAE,OACR,CAAC,CACF,CACDE,YAAY,CAAE,CACZC,cAAc,CAAE,EAAE,CAClBC,iBAAiB,CAAE,sBAAsB,CACzCC,eAAe,CAAE,sBAAsB,CACvCC,cAAc,CAAE,QAAQ,CACxBC,aAAa,CAAE,kBACjB,CACF,CAAC,CACF,CAED,KAAM,CAAAC,cAAc,CAAIC,MAAc,EAAK,CACzC,aAAAC,MAAA,CAAWD,MAAM,CAACE,cAAc,CAAC,OAAO,CAAE,CAAEC,qBAAqB,CAAE,CAAE,CAAC,CAAC,EACzE,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC,CACzD,CAAC,CAED,KAAM,CAAAC,cAAc,CAAI1C,MAAc,EAAK,CACzC,KAAM,CAAA2C,MAAM,CAAG,CACbC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,OAAO,CAChBC,WAAW,CAAE,QAAQ,CACrBC,OAAO,CAAE,KACX,CAAC,CACD,MAAO,CAAAJ,MAAM,CAAC3C,MAAM,CAAwB,EAAI,SAAS,CAC3D,CAAC,CAED,KAAM,CAAAgD,aAAa,CAAIhD,MAAc,EAAK,CACxC,KAAM,CAAAiD,KAAK,CAAG,CACZL,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,KAAK,CACdC,WAAW,CAAE,KAAK,CAClBC,OAAO,CAAE,KACX,CAAC,CACD,MAAO,CAAAE,KAAK,CAACjD,MAAM,CAAuB,EAAIA,MAAM,CACtD,CAAC,CAED,KAAM,CAAAkD,UAAU,CAAIC,MAAkB,EAAK,CACzCvE,cAAc,CAACuE,MAAM,CAAC,CACtBnE,YAAY,CAAC,MAAM,CAAC,CACpBF,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAAsE,UAAU,CAAID,MAAkB,EAAK,CACzCvE,cAAc,CAACuE,MAAM,CAAC,CACtBnE,YAAY,CAAC,MAAM,CAAC,CACpBG,IAAI,CAACkE,cAAc,CAACF,MAAM,CAAC,CAC3BrE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAAwE,YAAY,CAAGA,CAAA,GAAM,CACzB1E,cAAc,CAAC,IAAI,CAAC,CACpBI,YAAY,CAAC,QAAQ,CAAC,CACtBG,IAAI,CAACoE,WAAW,CAAC,CAAC,CAClBzE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAA0E,YAAY,CAAIL,MAAkB,EAAK,CAC3CvE,cAAc,CAACuE,MAAM,CAAC,CACtBjE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAAuE,YAAY,CAAGA,CAAA,GAAM,CACzB3G,OAAO,CAAC4G,OAAO,CAAC,YAAY,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB7G,OAAO,CAAC4G,OAAO,CAAC,YAAY,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAE,OAAgC,CAAG,CACvC,CACEC,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,MACT,CAAC,CACD,CACEJ,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdG,QAAQ,CAAE,IACZ,CAAC,CACD,CACEL,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGC,IAAY,eACnBzG,IAAA,CAACI,IAAI,EAACsG,IAAI,MAAAC,QAAA,CAAEF,IAAI,CAAO,CAE3B,CAAC,CACD,CACEP,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,gBAAgB,CAC3BC,GAAG,CAAE,gBAAgB,CACrBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAG9C,IAAY,EAAKiB,UAAU,CAACjB,IAAI,CAC3C,CAAC,CACD,CACEwC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAG9C,IAAY,EAAKiB,UAAU,CAACjB,IAAI,CAC3C,CAAC,CACD,CACEwC,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,EAAE,CACTG,MAAM,CAAGI,KAAa,eACpB5G,IAAA,CAAChB,KAAK,EAAC4H,KAAK,CAAEA,KAAM,CAACC,QAAQ,MAACC,KAAK,CAAC,SAAS,CAAE,CAEnD,CAAC,CACD,CACEZ,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGO,KAAa,EAAKzC,cAAc,CAACyC,KAAK,CACjD,CAAC,CACD,CACEb,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,EAAE,CACTG,MAAM,CAAGnE,MAAc,eACrBrC,IAAA,CAACtB,GAAG,EAACoI,KAAK,CAAE/B,cAAc,CAAC1C,MAAM,CAAE,CAAAsE,QAAA,CAChCtB,aAAa,CAAChD,MAAM,CAAC,CACnB,CAET,CAAC,CACD,CACE6D,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,OAAO,CACdE,MAAM,CAAEA,CAACQ,CAAC,CAAExB,MAAM,gBAChBtF,KAAA,CAAC9B,KAAK,EAAC6I,IAAI,CAAC,OAAO,CAAAN,QAAA,eACjB3G,IAAA,CAACf,OAAO,EAACiH,KAAK,CAAC,0BAAM,CAAAS,QAAA,cACnB3G,IAAA,CAAC7B,MAAM,EACLwF,IAAI,CAAC,MAAM,CACXuD,IAAI,cAAElH,IAAA,CAACN,WAAW,GAAE,CAAE,CACtByH,OAAO,CAAEA,CAAA,GAAM5B,UAAU,CAACC,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACVxF,IAAA,CAACf,OAAO,EAACiH,KAAK,CAAC,cAAI,CAAAS,QAAA,cACjB3G,IAAA,CAAC7B,MAAM,EACLwF,IAAI,CAAC,MAAM,CACXuD,IAAI,cAAElH,IAAA,CAACL,YAAY,GAAE,CAAE,CACvBwH,OAAO,CAAEA,CAAA,GAAM1B,UAAU,CAACD,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACVxF,IAAA,CAACf,OAAO,EAACiH,KAAK,CAAC,oBAAK,CAAAS,QAAA,cAClB3G,IAAA,CAAC7B,MAAM,EACLwF,IAAI,CAAC,MAAM,CACXuD,IAAI,cAAElH,IAAA,CAACP,cAAc,GAAE,CAAE,CACzB0H,OAAO,CAAEA,CAAA,GAAMtB,YAAY,CAACL,MAAM,CAAE,CACrC,CAAC,CACK,CAAC,cACVxF,IAAA,CAACf,OAAO,EAACiH,KAAK,CAAC,cAAI,CAAAS,QAAA,cACjB3G,IAAA,CAAC7B,MAAM,EACLwF,IAAI,CAAC,MAAM,CACXuD,IAAI,cAAElH,IAAA,CAACJ,gBAAgB,GAAE,CAAE,CAC3BuH,OAAO,CAAEA,CAAA,GAAMhI,OAAO,CAAC4G,OAAO,CAAC,YAAY,CAAE,CAC9C,CAAC,CACK,CAAC,EACL,CAEX,CAAC,CACF,CAED,KAAM,CAAAqB,WAAqC,CAAG,CAC5C,CACElB,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfG,QAAQ,CAAE,IACZ,CAAC,CACD,CACEL,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,EACT,CAAC,CACD,CACEH,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGC,IAAY,eACnBzG,IAAA,CAACI,IAAI,EAACsG,IAAI,MAAAC,QAAA,CAAEF,IAAI,CAAO,CAE3B,CAAC,CACD,CACEP,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGC,IAAY,EAAKA,IAAI,eAC5BzG,IAAA,CAACI,IAAI,EAACsG,IAAI,MAAAC,QAAA,CAAEF,IAAI,CAAO,CAE3B,CAAC,CACD,CACEP,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAG9C,IAAY,EAAKiB,UAAU,CAACjB,IAAI,CAC3C,CAAC,CACD,CACEwC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,EAAE,CACTG,MAAM,CAAGnE,MAAc,EAAK,CAC1B,KAAM,CAAA2C,MAAM,CAAG,CACbqC,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,KACX,CAAC,CACD,KAAM,CAAAjC,KAAK,CAAG,CACZ+B,SAAS,CAAE,KAAK,CAChBC,QAAQ,CAAE,KAAK,CACfC,OAAO,CAAE,KACX,CAAC,CACD,mBACEvH,IAAA,CAACtB,GAAG,EAACoI,KAAK,CAAE9B,MAAM,CAAC3C,MAAM,CAAyB,CAAAsE,QAAA,CAC/CrB,KAAK,CAACjD,MAAM,CAAuB,CACjC,CAAC,CAEV,CACF,CAAC,CACD,CACE6D,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGgB,KAAa,EAAKlD,cAAc,CAACkD,KAAK,CACjD,CAAC,CACD,CACEtB,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGgB,KAAa,EAAKlD,cAAc,CAACkD,KAAK,CACjD,CAAC,CACF,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,SAAS,CAAGhG,eAAe,CAACiG,MAAM,CACxC,KAAM,CAAAC,UAAU,CAAGlG,eAAe,CAACmG,MAAM,CAACC,GAAG,EAAIA,GAAG,CAACzF,MAAM,GAAK,QAAQ,CAAC,CAACsF,MAAM,CAChF,KAAM,CAAAI,WAAW,CAAGrG,eAAe,CAACmG,MAAM,CAACC,GAAG,EAAIA,GAAG,CAACzF,MAAM,GAAK,SAAS,CAAC,CAACsF,MAAM,CAClF,KAAM,CAAAK,eAAe,CAAGtG,eAAe,CAACmG,MAAM,CAACC,GAAG,EAAIA,GAAG,CAACzF,MAAM,GAAK,aAAa,CAAC,CAACsF,MAAM,CAE1F,mBACEzH,KAAA,CAAC3B,GAAG,EAAC0J,MAAM,CAAE,EAAG,CAACC,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAAxB,QAAA,eAC3C3G,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,CAAE,CAAAzB,QAAA,cACX3G,IAAA,CAAC/B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAN,QAAA,cAChBzG,KAAA,QAAKgI,KAAK,CAAE,CAAEG,SAAS,CAAE,QAAS,CAAE,CAAA1B,QAAA,eAClC3G,IAAA,QAAKkI,KAAK,CAAE,CAAEI,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEzB,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,CACpEe,SAAS,CACP,CAAC,cACN1H,IAAA,QAAKkI,KAAK,CAAE,CAAEpB,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,CAAC,oBAAG,CAAK,CAAC,EACrC,CAAC,CACF,CAAC,CACJ,CAAC,cACN3G,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,CAAE,CAAAzB,QAAA,cACX3G,IAAA,CAAC/B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAN,QAAA,cAChBzG,KAAA,QAAKgI,KAAK,CAAE,CAAEG,SAAS,CAAE,QAAS,CAAE,CAAA1B,QAAA,eAClC3G,IAAA,QAAKkI,KAAK,CAAE,CAAEI,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEzB,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,CACpEoB,WAAW,CACT,CAAC,cACN/H,IAAA,QAAKkI,KAAK,CAAE,CAAEpB,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,CAAC,oBAAG,CAAK,CAAC,EACrC,CAAC,CACF,CAAC,CACJ,CAAC,cACN3G,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,CAAE,CAAAzB,QAAA,cACX3G,IAAA,CAAC/B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAN,QAAA,cAChBzG,KAAA,QAAKgI,KAAK,CAAE,CAAEG,SAAS,CAAE,QAAS,CAAE,CAAA1B,QAAA,eAClC3G,IAAA,QAAKkI,KAAK,CAAE,CAAEI,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEzB,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,CACpEqB,eAAe,CACb,CAAC,cACNhI,IAAA,QAAKkI,KAAK,CAAE,CAAEpB,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,CAAC,oBAAG,CAAK,CAAC,EACrC,CAAC,CACF,CAAC,CACJ,CAAC,cACN3G,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,CAAE,CAAAzB,QAAA,cACX3G,IAAA,CAAC/B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAN,QAAA,cAChBzG,KAAA,QAAKgI,KAAK,CAAE,CAAEG,SAAS,CAAE,QAAS,CAAE,CAAA1B,QAAA,eAClC3G,IAAA,QAAKkI,KAAK,CAAE,CAAEI,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEzB,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,CACpEiB,UAAU,CACR,CAAC,cACN5H,IAAA,QAAKkI,KAAK,CAAE,CAAEpB,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,CAAC,oBAAG,CAAK,CAAC,EACrC,CAAC,CACF,CAAC,CACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,mBACEzG,KAAA,QAAAyG,QAAA,eACEzG,KAAA,CAACjC,IAAI,EAAA0I,QAAA,eACHzG,KAAA,CAAC3B,GAAG,EAACiK,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACP,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAAxB,QAAA,eACtEzG,KAAA,CAAC1B,GAAG,EAAAmI,QAAA,eACF3G,IAAA,CAACG,KAAK,EAACuI,KAAK,CAAE,CAAE,CAACR,KAAK,CAAE,CAAES,MAAM,CAAE,CAAE,CAAE,CAAAhC,QAAA,CAAC,0BAEvC,CAAO,CAAC,cACR3G,IAAA,CAACI,IAAI,EAACuD,IAAI,CAAC,WAAW,CAAAgD,QAAA,CAAC,uKAEvB,CAAM,CAAC,EACJ,CAAC,cACN3G,IAAA,CAACxB,GAAG,EAAAmI,QAAA,cACFzG,KAAA,CAAC9B,KAAK,EAAAuI,QAAA,eACJ3G,IAAA,CAACK,MAAM,EACLuI,WAAW,CAAC,qDAAa,CACzBC,UAAU,MACVX,KAAK,CAAE,CAAE7B,KAAK,CAAE,GAAI,CAAE,CACtByC,QAAQ,CAAEjI,gBAAiB,CAC5B,CAAC,cACFb,IAAA,CAAC1B,MAAM,EACLsK,WAAW,CAAC,cAAI,CAChBC,UAAU,MACVX,KAAK,CAAE,CAAE7B,KAAK,CAAE,GAAI,CAAE,CACtBU,KAAK,CAAEjG,YAAa,CACpBiI,QAAQ,CAAEhI,eAAgB,CAC1BiI,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,KAAK,CAAElC,KAAK,CAAE,QAAS,CAAC,CACjC,CAAEkC,KAAK,CAAE,KAAK,CAAElC,KAAK,CAAE,SAAU,CAAC,CAClC,CAAEkC,KAAK,CAAE,KAAK,CAAElC,KAAK,CAAE,aAAc,CAAC,CACtC,CAAEkC,KAAK,CAAE,KAAK,CAAElC,KAAK,CAAE,SAAU,CAAC,CAClC,CACH,CAAC,cACF/G,IAAA,CAAC7B,MAAM,EAAC+I,IAAI,cAAElH,IAAA,CAACF,YAAY,GAAE,CAAE,CAAA6G,QAAA,CAAC,0BAEhC,CAAQ,CAAC,cACT3G,IAAA,CAAC7B,MAAM,EAAC+I,IAAI,cAAElH,IAAA,CAACR,cAAc,GAAE,CAAE,CAAC2H,OAAO,CAAEnB,YAAa,CAAAW,QAAA,CAAC,cAEzD,CAAQ,CAAC,cACT3G,IAAA,CAAC7B,MAAM,EAAC+I,IAAI,cAAElH,IAAA,CAACT,cAAc,GAAE,CAAE,CAAC4H,OAAO,CAAErB,YAAa,CAAAa,QAAA,CAAC,cAEzD,CAAQ,CAAC,cACT3G,IAAA,CAAC7B,MAAM,EAACwF,IAAI,CAAC,SAAS,CAACuD,IAAI,cAAElH,IAAA,CAACV,YAAY,GAAE,CAAE,CAAC6H,OAAO,CAAExB,YAAa,CAAAgB,QAAA,CAAC,0BAEtE,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAEN3G,IAAA,CAACjB,KAAK,EACJI,OAAO,CAAC,0BAAM,CACdoD,WAAW,CAAC,sLAA0C,CACtDoB,IAAI,CAAC,MAAM,CACXuF,QAAQ,MACRC,QAAQ,MACRjB,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,CAEDV,gBAAgB,CAAC,CAAC,cAEnBzH,IAAA,CAAC9B,KAAK,EACJ+H,OAAO,CAAEA,OAAQ,CACjBmD,UAAU,CAAE1H,eAAgB,CAC5BhB,OAAO,CAAEA,OAAQ,CACjB2I,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAArF,MAAA,CACjBqF,KAAK,CAAC,CAAC,CAAC,MAAArF,MAAA,CAAIqF,KAAK,CAAC,CAAC,CAAC,oBAAArF,MAAA,CAAQoF,KAAK,WAC1C,CAAE,CACH,CAAC,EACE,CAAC,cAGP1J,KAAA,CAACvB,KAAK,EACJuH,KAAK,CACH9E,SAAS,GAAK,MAAM,CAAG,gBAAgB,CACvCA,SAAS,GAAK,MAAM,CAAG,gBAAgB,CAAG,gBAC3C,CACD0I,IAAI,CAAE5I,cAAe,CACrB6I,QAAQ,CAAEA,CAAA,GAAM5I,iBAAiB,CAAC,KAAK,CAAE,CACzCkF,KAAK,CAAE,IAAK,CACZ2D,MAAM,CACJ5I,SAAS,GAAK,MAAM,CAAG,cACrBpB,IAAA,CAAC7B,MAAM,EAAagJ,OAAO,CAAEA,CAAA,GAAMhG,iBAAiB,CAAC,KAAK,CAAE,CAAAwF,QAAA,CAAC,cAE7D,EAFY,OAEJ,CAAC,CACV,CAAG,cACF3G,IAAA,CAAC7B,MAAM,EAAcgJ,OAAO,CAAEA,CAAA,GAAMhG,iBAAiB,CAAC,KAAK,CAAE,CAAAwF,QAAA,CAAC,cAE9D,EAFY,QAEJ,CAAC,cACT3G,IAAA,CAAC7B,MAAM,EAAcwF,IAAI,CAAC,SAAS,CAAAgD,QAAA,CAChCvF,SAAS,GAAK,MAAM,CAAG,IAAI,CAAG,IAAI,EADzB,QAEJ,CAAC,CAEZ,CAAAuF,QAAA,EAEA3F,WAAW,EAAII,SAAS,GAAK,MAAM,eAClClB,KAAA,CAACpB,IAAI,EAACmL,gBAAgB,CAAC,OAAO,CAAAtD,QAAA,eAC5B3G,IAAA,CAACO,OAAO,EAAC2J,GAAG,CAAC,0BAAM,CAAAvD,QAAA,cACjBzG,KAAA,CAACrB,YAAY,EAACsL,QAAQ,MAACC,MAAM,CAAE,CAAE,CAAAzD,QAAA,eAC/B3G,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,iBAAO,CAAAtC,QAAA,CAAE3F,WAAW,CAACY,OAAO,CAAoB,CAAC,cAC1E5B,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,iBAAO,CAAAtC,QAAA,CAAE3F,WAAW,CAACa,OAAO,CAAoB,CAAC,cAC1E7B,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,CAAE3F,WAAW,CAACc,YAAY,CAAoB,CAAC,cAC9E9B,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAAAtC,QAAA,cAC5B3G,IAAA,CAACI,IAAI,EAACsG,IAAI,MAAAC,QAAA,CAAE3F,WAAW,CAACe,YAAY,CAAO,CAAC,CAC3B,CAAC,cACpB/B,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAAAtC,QAAA,CAAE3F,WAAW,CAACgB,WAAW,CAAoB,CAAC,cAC5EhC,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,CAAE3F,WAAW,CAACiB,YAAY,CAAoB,CAAC,cAC9EjC,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,CAAEhC,UAAU,CAAC3D,WAAW,CAACkB,cAAc,CAAC,CAAoB,CAAC,cAC5FlC,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,CAAEhC,UAAU,CAAC3D,WAAW,CAACmB,YAAY,CAAC,CAAoB,CAAC,cAC1FnC,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,cAAI,CAAAtC,QAAA,CAAE3F,WAAW,CAACsB,OAAO,CAAoB,CAAC,cACvEtC,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,cAAI,CAAAtC,QAAA,cAC3B3G,IAAA,CAACtB,GAAG,EAACoI,KAAK,CAAE/B,cAAc,CAAC/D,WAAW,CAACqB,MAAM,CAAE,CAAAsE,QAAA,CAC5CtB,aAAa,CAACrE,WAAW,CAACqB,MAAM,CAAC,CAC/B,CAAC,CACW,CAAC,cACpBrC,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,CAAE3F,WAAW,CAAC2B,UAAU,CAAoB,CAAC,cAC5E3C,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAAAtC,QAAA,CAAErC,cAAc,CAACtD,WAAW,CAAC4B,UAAU,CAAC,CAAoB,CAAC,cAC3F5C,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAAAtC,QAAA,CAAE3F,WAAW,CAACwB,SAAS,CAAoB,CAAC,cAC1ExC,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,CAAEhC,UAAU,CAAC3D,WAAW,CAACyB,SAAS,CAAC,CAAoB,CAAC,cACvFzC,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,cAAI,CAACb,IAAI,CAAE,CAAE,CAAAzB,QAAA,CAAE3F,WAAW,CAACuB,WAAW,CAAoB,CAAC,EACxE,CAAC,EAvBO,OAwBf,CAAC,cACVvC,IAAA,CAACO,OAAO,EAAC2J,GAAG,CAAC,0BAAM,CAAAvD,QAAA,cACjB3G,IAAA,CAAC9B,KAAK,EACJ+H,OAAO,CAAEmB,WAAY,CACrBgC,UAAU,CAAEpI,WAAW,CAAC6B,WAAY,CACpCwG,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,KAAM,CAClBvC,IAAI,CAAC,OAAO,CACb,CAAC,EARoB,OASf,CAAC,cACVjH,IAAA,CAACO,OAAO,EAAC2J,GAAG,CAAC,0BAAM,CAAAvD,QAAA,cACjB3G,IAAA,CAACX,QAAQ,EAAAsH,QAAA,CACN3F,WAAW,CAACyC,kBAAkB,CAAC6G,GAAG,CAAE9E,MAAM,eACzCtF,KAAA,CAACb,QAAQ,CAACgL,IAAI,EAEZvD,KAAK,CAAEtB,MAAM,CAACnD,MAAM,GAAK,WAAW,CAAG,OAAO,CAAG,MAAO,CAAAsE,QAAA,eAExDzG,KAAA,QAAAyG,QAAA,eACE3G,IAAA,CAACI,IAAI,EAACmK,MAAM,MAAA5D,QAAA,CAAEnB,MAAM,CAAC7B,IAAI,GAAK,YAAY,CAAG,OAAO,CACvC6B,MAAM,CAAC7B,IAAI,GAAK,YAAY,CAAG,OAAO,CAAG,MAAM,CAAO,CAAC,cACpE3D,IAAA,CAACI,IAAI,EAACuD,IAAI,CAAC,WAAW,CAACuE,KAAK,CAAE,CAAEsC,UAAU,CAAE,CAAE,CAAE,CAAA7D,QAAA,CAC7ChC,UAAU,CAACa,MAAM,CAAC9B,IAAI,CAAC,CACpB,CAAC,EACJ,CAAC,cACN1D,IAAA,QAAKkI,KAAK,CAAE,CAAEuC,SAAS,CAAE,CAAE,CAAE,CAAA9D,QAAA,cAC3B3G,IAAA,CAACI,IAAI,EAAAuG,QAAA,CAAEnB,MAAM,CAACjD,WAAW,CAAO,CAAC,CAC9B,CAAC,cACNrC,KAAA,QAAKgI,KAAK,CAAE,CAAEuC,SAAS,CAAE,CAAE,CAAE,CAAA9D,QAAA,eAC3BzG,KAAA,CAACE,IAAI,EAACuD,IAAI,CAAC,WAAW,CAAAgD,QAAA,EAAC,sBAAK,CAACnB,MAAM,CAAC5B,UAAU,EAAO,CAAC,cACtD1D,KAAA,CAACE,IAAI,EAACuD,IAAI,CAAC,WAAW,CAACuE,KAAK,CAAE,CAAEsC,UAAU,CAAE,EAAG,CAAE,CAAA7D,QAAA,EAAC,gBAC5C,CAACrC,cAAc,CAACkB,MAAM,CAAC1B,IAAI,CAAC,EAC5B,CAAC,cACP9D,IAAA,CAACtB,GAAG,EACFoI,KAAK,CAAEtB,MAAM,CAACnD,MAAM,GAAK,WAAW,CAAG,OAAO,CAAG,YAAa,CAC9D6F,KAAK,CAAE,CAAEsC,UAAU,CAAE,CAAE,CAAE,CAAA7D,QAAA,CAExBnB,MAAM,CAACnD,MAAM,GAAK,WAAW,CAAG,KAAK,CACrCmD,MAAM,CAACnD,MAAM,GAAK,aAAa,CAAG,KAAK,CAAG,KAAK,CAC7C,CAAC,EACH,CAAC,CACLmD,MAAM,CAAC3B,SAAS,CAAC8D,MAAM,CAAG,CAAC,eAC1B3H,IAAA,QAAKkI,KAAK,CAAE,CAAEuC,SAAS,CAAE,CAAE,CAAE,CAAA9D,QAAA,cAC3BzG,KAAA,CAACE,IAAI,EAACuD,IAAI,CAAC,WAAW,CAAAgD,QAAA,EAAC,4BAAM,CAACnB,MAAM,CAAC3B,SAAS,CAAC6G,IAAI,CAAC,IAAI,CAAC,EAAO,CAAC,CAC9D,CACN,GA9BIlF,MAAM,CAAC7D,EA+BC,CAChB,CAAC,CACM,CAAC,EArCW,aAsCf,CAAC,cACV3B,IAAA,CAACO,OAAO,EAAC2J,GAAG,CAAC,0BAAM,CAAAvD,QAAA,cACjBzG,KAAA,CAACrB,YAAY,EAACsL,QAAQ,MAACC,MAAM,CAAE,CAAE,CAAAzD,QAAA,eAC/BzG,KAAA,CAACrB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAAAtC,QAAA,EAAE3F,WAAW,CAACgD,YAAY,CAACC,cAAc,CAAC,cAAE,EAAmB,CAAC,cAC9FjE,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,cAC7B3G,IAAA,CAACtB,GAAG,EAACoI,KAAK,CAAE9F,WAAW,CAACgD,YAAY,CAACI,cAAc,GAAK,QAAQ,CAAG,OAAO,CAAG,KAAM,CAAAuC,QAAA,CAChF3F,WAAW,CAACgD,YAAY,CAACI,cAAc,GAAK,QAAQ,CAAG,IAAI,CAC3DpD,WAAW,CAACgD,YAAY,CAACI,cAAc,GAAK,SAAS,CAAG,KAAK,CAAG,KAAK,CACnE,CAAC,CACW,CAAC,cACpBpE,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,sCAAQ,CAAAtC,QAAA,CAC9BhC,UAAU,CAAC3D,WAAW,CAACgD,YAAY,CAACE,iBAAiB,CAAC,CACtC,CAAC,cACpBlE,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,sCAAQ,CAAAtC,QAAA,CAC9BhC,UAAU,CAAC3D,WAAW,CAACgD,YAAY,CAACG,eAAe,CAAC,CACpC,CAAC,cACpBnE,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAACb,IAAI,CAAE,CAAE,CAAAzB,QAAA,CACrC3F,WAAW,CAACgD,YAAY,CAACK,aAAa,CACtB,CAAC,EACR,CAAC,EAlBO,UAmBf,CAAC,EACN,CACP,CAEAjD,SAAS,GAAK,MAAM,eACnBpB,IAAA,CAACpB,IAAI,EAAC4C,IAAI,CAAEA,IAAK,CAACmJ,MAAM,CAAC,UAAU,CAAAhE,QAAA,cACjCzG,KAAA,CAAC3B,GAAG,EAAC0J,MAAM,CAAE,EAAG,CAAAtB,QAAA,eACd3G,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,EAAG,CAAAzB,QAAA,cACZ3G,IAAA,CAACpB,IAAI,CAACyL,IAAI,EAACpB,KAAK,CAAC,iBAAO,CAAC2B,IAAI,CAAC,SAAS,CAACC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAE,CAAAnE,QAAA,cAClE3G,IAAA,CAAC3B,KAAK,EAACuK,WAAW,CAAC,mCAAU,CAAE,CAAC,CACvB,CAAC,CACT,CAAC,cACN5I,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,EAAG,CAAAzB,QAAA,cACZ3G,IAAA,CAACpB,IAAI,CAACyL,IAAI,EAACpB,KAAK,CAAC,iBAAO,CAAC2B,IAAI,CAAC,SAAS,CAACC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAE,CAAAnE,QAAA,cAClE3G,IAAA,CAAC3B,KAAK,EAACuK,WAAW,CAAC,mCAAU,CAAE,CAAC,CACvB,CAAC,CACT,CAAC,cACN5I,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,EAAG,CAAAzB,QAAA,cACZ3G,IAAA,CAACpB,IAAI,CAACyL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,cAAc,CAACC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAE,CAAAnE,QAAA,cACtE3G,IAAA,CAAC3B,KAAK,EAACuK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACN5I,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,EAAG,CAAAzB,QAAA,cACZ3G,IAAA,CAACpB,IAAI,CAACyL,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAAC2B,IAAI,CAAC,cAAc,CAACC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAE,CAAAnE,QAAA,cACrE3G,IAAA,CAAC3B,KAAK,EAACuK,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACN5I,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,EAAG,CAAAzB,QAAA,cACZ3G,IAAA,CAACpB,IAAI,CAACyL,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAAC2B,IAAI,CAAC,aAAa,CAACC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAE,CAAAnE,QAAA,cACpE3G,IAAA,CAAC3B,KAAK,EAACuK,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACN5I,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,EAAG,CAAAzB,QAAA,cACZ3G,IAAA,CAACpB,IAAI,CAACyL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAC2B,IAAI,CAAC,cAAc,CAACC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAE,CAAAnE,QAAA,cACtE3G,IAAA,CAAC3B,KAAK,EAACuK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACN5I,IAAA,CAACxB,GAAG,EAAC4J,IAAI,CAAE,EAAG,CAAAzB,QAAA,cACZ3G,IAAA,CAACpB,IAAI,CAACyL,IAAI,EAACpB,KAAK,CAAC,cAAI,CAAC2B,IAAI,CAAC,aAAa,CAAAjE,QAAA,cACtC3G,IAAA,CAACQ,QAAQ,EAACuK,IAAI,CAAE,CAAE,CAACnC,WAAW,CAAC,gCAAO,CAAE,CAAC,CAChC,CAAC,CACT,CAAC,EACH,CAAC,CACF,CACP,EACI,CAAC,cAGR5I,IAAA,CAACrB,KAAK,EACJuH,KAAK,CAAC,gCAAO,CACb4D,IAAI,CAAExI,cAAe,CACrByI,QAAQ,CAAEA,CAAA,GAAMxI,iBAAiB,CAAC,KAAK,CAAE,CACzCyI,MAAM,CAAE,cACNhK,IAAA,CAAC7B,MAAM,EAAa+I,IAAI,cAAElH,IAAA,CAACH,eAAe,GAAE,CAAE,CAAA8G,QAAA,CAAC,cAE/C,EAFY,OAEJ,CAAC,cACT3G,IAAA,CAAC7B,MAAM,EAAgB+I,IAAI,cAAElH,IAAA,CAACJ,gBAAgB,GAAE,CAAE,CAAA+G,QAAA,CAAC,cAEnD,EAFY,UAEJ,CAAC,cACT3G,IAAA,CAAC7B,MAAM,EAAagJ,OAAO,CAAEA,CAAA,GAAM5F,iBAAiB,CAAC,KAAK,CAAE,CAAAoF,QAAA,CAAC,cAE7D,EAFY,OAEJ,CAAC,CACT,CAAAA,QAAA,CAED3F,WAAW,eACVd,KAAA,QAAKgI,KAAK,CAAE,CAAEG,SAAS,CAAE,QAAS,CAAE,CAAA1B,QAAA,eAClC3G,IAAA,CAACd,MAAM,EACL6H,KAAK,IAAAvC,MAAA,CAAKxD,WAAW,CAACoB,MAAM,SAAAoC,MAAA,CAAOxD,WAAW,CAACe,YAAY,CAAG,CAC9DkF,IAAI,CAAE,GAAI,CACX,CAAC,cACFjH,IAAA,CAACZ,OAAO,GAAE,CAAC,cACXc,KAAA,CAACrB,YAAY,EAACuL,MAAM,CAAE,CAAE,CAACnD,IAAI,CAAC,OAAO,CAAAN,QAAA,eACnC3G,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,CAAE3F,WAAW,CAACc,YAAY,CAAoB,CAAC,cAC9E9B,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAAAtC,QAAA,CAAE3F,WAAW,CAACe,YAAY,CAAoB,CAAC,cAC7E/B,IAAA,CAACnB,YAAY,CAACwL,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAtC,QAAA,CAAEhC,UAAU,CAAC3D,WAAW,CAACkB,cAAc,CAAC,CAAoB,CAAC,EAChF,CAAC,EACZ,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}