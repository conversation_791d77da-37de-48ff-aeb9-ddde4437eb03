import React, { useEffect, useState, useMemo, useCallback } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Table,
  List,
  Avatar,
  Badge,
  Typography,
  Space,
  Button,
  Select,
  DatePicker,
  Tabs,
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  InboxOutlined,
  WarningOutlined,
  RiseOutlined,
  FileTextOutlined,
  ToolOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import dayjs from 'dayjs';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { formatCurrency, formatPercentage } from '../../utils';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

// 模拟数据
const mockMetrics = {
  totalOrders: 156,
  totalRevenue: 2580000,
  wasteReduction: 0.15,
  costSavings: 380000,
  inventoryTurnover: 8.5,
  onTimeDelivery: 0.95,
};

const mockWasteTrend = [
  { month: '1月', packaging: 2.5, moq: 3.2, cutting: 1.8, expiry: 0.5 },
  { month: '2月', packaging: 2.1, moq: 2.8, cutting: 1.5, expiry: 0.3 },
  { month: '3月', packaging: 1.9, moq: 2.5, cutting: 1.3, expiry: 0.4 },
  { month: '4月', packaging: 1.7, moq: 2.2, cutting: 1.1, expiry: 0.2 },
  { month: '5月', packaging: 1.5, moq: 2.0, cutting: 1.0, expiry: 0.3 },
  { month: '6月', packaging: 1.3, moq: 1.8, cutting: 0.9, expiry: 0.1 },
];

const mockCostTrend = [
  { month: '1月', material: 850000, labor: 120000, overhead: 80000 },
  { month: '2月', material: 920000, labor: 125000, overhead: 82000 },
  { month: '3月', material: 880000, labor: 118000, overhead: 78000 },
  { month: '4月', material: 950000, labor: 130000, overhead: 85000 },
  { month: '5月', material: 890000, labor: 122000, overhead: 79000 },
  { month: '6月', material: 910000, labor: 128000, overhead: 83000 },
];

const mockOrderStatus = [
  { name: '已完成', value: 45, color: '#52c41a' },
  { name: '进行中', value: 30, color: '#1890ff' },
  { name: '待开始', value: 20, color: '#faad14' },
  { name: '已延期', value: 5, color: '#f5222d' },
];

const mockRecentOrders = [
  { id: 'ORD-001', customer: '华为技术', amount: 125000, status: '进行中', date: '2024-01-15' },
  { id: 'ORD-002', customer: '中兴通讯', amount: 98000, status: '已完成', date: '2024-01-14' },
  { id: 'ORD-003', customer: '大唐移动', amount: 156000, status: '待开始', date: '2024-01-13' },
  { id: 'ORD-004', customer: '爱立信', amount: 89000, status: '进行中', date: '2024-01-12' },
  { id: 'ORD-005', customer: '诺基亚', amount: 234000, status: '已完成', date: '2024-01-11' },
];

const mockAlerts = [
  { id: 1, type: 'warning', message: '物料 ANT-001 库存不足，当前库存：50件', time: '2小时前' },
  { id: 2, type: 'error', message: '订单 ORD-003 交期可能延误', time: '4小时前' },
  { id: 3, type: 'info', message: 'ECN-2024-001 已通过审批', time: '6小时前' },
  { id: 4, type: 'warning', message: '供应商价格异常波动：RF-002 上涨15%', time: '8小时前' },
];

const DashboardPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user, currentRole } = useAppSelector(state => state.auth);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);

  useEffect(() => {
    // 加载仪表板数据
    // dispatch(fetchDashboardMetrics());
  }, [dispatch, dateRange]);

  // 使用useCallback优化函数，避免不必要的重新渲染
  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case '已完成': return 'success';
      case '进行中': return 'processing';
      case '待开始': return 'warning';
      case '已延期': return 'error';
      default: return 'default';
    }
  }, []);

  const getAlertIcon = useCallback((type: string) => {
    switch (type) {
      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'error': return <WarningOutlined style={{ color: '#f5222d' }} />;
      case 'info': return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      default: return <ClockCircleOutlined />;
    }
  }, []);

  // 使用useMemo优化计算密集型数据
  const processedMetrics = useMemo(() => ({
    ...mockMetrics,
    wasteReductionFormatted: formatPercentage(mockMetrics.wasteReduction),
    totalRevenueFormatted: formatCurrency(mockMetrics.totalRevenue),
    costSavingsFormatted: formatCurrency(mockMetrics.costSavings),
  }), []);

  const handleDateRangeChange = useCallback((dates: any, dateStrings: [string, string]) => {
    if (dates) {
      setDateRange(dates);
    }
  }, []);

  const handleRefreshData = useCallback(() => {
    // TODO: 实现数据刷新逻辑
    console.log('刷新数据');
  }, []);

  return (
    <div>
      {/* 页面标题和控制 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            仪表板
          </Title>
          <Text type="secondary">
            欢迎回来，{user?.name}！当前角色：{currentRole?.name}
          </Text>
        </Col>
        <Col>
          <Space>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
            />
            <Button type="primary" onClick={handleRefreshData}>
              刷新数据
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 关键指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={processedMetrics.totalOrders}
              prefix={<FileTextOutlined />}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收入"
              value={processedMetrics.totalRevenue}
              prefix={<DollarOutlined />}
              formatter={() => processedMetrics.totalRevenueFormatted}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="浪费减少率"
              value={processedMetrics.wasteReduction}
              prefix={<ArrowDownOutlined />}
              formatter={() => processedMetrics.wasteReductionFormatted}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="成本节约"
              value={processedMetrics.costSavings}
              prefix={<RiseOutlined />}
              formatter={() => processedMetrics.costSavingsFormatted}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 运营指标 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12}>
          <Card title="库存周转率" extra={<Text type="secondary">次/年</Text>}>
            <Statistic
              value={processedMetrics.inventoryTurnover}
              precision={1}
              valueStyle={{ fontSize: 32, color: '#1890ff' }}
            />
            <Progress
              percent={85}
              strokeColor="#1890ff"
              showInfo={false}
              style={{ marginTop: 16 }}
            />
            <Text type="secondary">目标：10次/年</Text>
          </Card>
        </Col>
        <Col xs={24} sm={12}>
          <Card title="准时交付率" extra={<Text type="secondary">%</Text>}>
            <Statistic
              value={processedMetrics.onTimeDelivery}
              formatter={(value) => formatPercentage(Number(value))}
              valueStyle={{ fontSize: 32, color: '#52c41a' }}
            />
            <Progress
              percent={95}
              strokeColor="#52c41a"
              showInfo={false}
              style={{ marginTop: 16 }}
            />
            <Text type="secondary">目标：≥95%</Text>
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="浪费趋势分析" extra={<Text type="secondary">%</Text>}>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={mockWasteTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="packaging"
                  stackId="1"
                  stroke="#8884d8"
                  fill="#8884d8"
                  name="包装浪费"
                />
                <Area
                  type="monotone"
                  dataKey="moq"
                  stackId="1"
                  stroke="#82ca9d"
                  fill="#82ca9d"
                  name="MOQ浪费"
                />
                <Area
                  type="monotone"
                  dataKey="cutting"
                  stackId="1"
                  stroke="#ffc658"
                  fill="#ffc658"
                  name="切割浪费"
                />
                <Area
                  type="monotone"
                  dataKey="expiry"
                  stackId="1"
                  stroke="#ff7300"
                  fill="#ff7300"
                  name="过期浪费"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="成本构成趋势">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={mockCostTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Legend />
                <Bar dataKey="material" fill="#8884d8" name="材料成本" />
                <Bar dataKey="labor" fill="#82ca9d" name="人工成本" />
                <Bar dataKey="overhead" fill="#ffc658" name="制造费用" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 订单状态和最近订单 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={8}>
          <Card title="订单状态分布">
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={mockOrderStatus}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {mockOrderStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={16}>
          <Card title="最近订单" extra={<Button type="link">查看全部</Button>}>
            <Table
              dataSource={mockRecentOrders}
              pagination={false}
              size="small"
              columns={[
                {
                  title: '订单号',
                  dataIndex: 'id',
                  key: 'id',
                  render: (text) => <Text strong>{text}</Text>,
                },
                {
                  title: '客户',
                  dataIndex: 'customer',
                  key: 'customer',
                },
                {
                  title: '金额',
                  dataIndex: 'amount',
                  key: 'amount',
                  render: (value) => formatCurrency(value),
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  render: (status) => (
                    <Badge status={getStatusColor(status)} text={status} />
                  ),
                },
                {
                  title: '日期',
                  dataIndex: 'date',
                  key: 'date',
                },
              ]}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统预警 */}
      <Row>
        <Col span={24}>
          <Card title="系统预警" extra={<Button type="link">查看全部</Button>}>
            <List
              dataSource={mockAlerts}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getAlertIcon(item.type)}
                    title={item.message}
                    description={item.time}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default React.memo(DashboardPage);
