{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\cost\\\\CostReportsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Table, Button, Space, Select, DatePicker, Row, Col, Statistic, Tag, Modal, Form, Input, Checkbox, Divider, Alert, Progress, Tooltip } from 'antd';\nimport { FileExcelOutlined, FilePdfOutlined, PrinterOutlined, DownloadOutlined, EyeOutlined, SettingOutlined, CalendarOutlined, DollarOutlined, WarningOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCostReports } from '../../store/slices/costSlice';\nimport { formatCurrency, formatDate } from '../../utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TextArea\n} = Input;\nconst CostReportsPage = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    costReports,\n    loading\n  } = useAppSelector(state => state.cost);\n  const [reportType, setReportType] = useState('monthly');\n  const [dateRange, setDateRange] = useState([dayjs().subtract(1, 'month'), dayjs()]);\n  const [customReportVisible, setCustomReportVisible] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [customForm] = Form.useForm();\n  useEffect(() => {\n    loadData();\n  }, [reportType, dateRange]);\n  const loadData = () => {\n    dispatch(fetchCostReports({\n      type: reportType,\n      startDate: dateRange[0].format('YYYY-MM-DD'),\n      endDate: dateRange[1].format('YYYY-MM-DD')\n    }));\n  };\n  const handleExport = (format, reportId) => {\n    // TODO: 实现导出功能\n    Modal.success({\n      title: '导出成功',\n      content: `报告已导出为 ${format.toUpperCase()} 格式`\n    });\n  };\n  const handlePreview = report => {\n    setSelectedReport(report);\n    setPreviewVisible(true);\n  };\n  const handleCustomReport = async () => {\n    try {\n      const values = await customForm.validateFields();\n      // TODO: 生成自定义报告\n      console.log('自定义报告参数:', values);\n      setCustomReportVisible(false);\n      Modal.success({\n        title: '报告生成成功',\n        content: '自定义报告已生成，请在报告列表中查看'\n      });\n    } catch (error) {\n      console.error('生成报告失败:', error);\n    }\n  };\n\n  // 模拟报告数据\n  const mockReports = [{\n    id: '1',\n    name: '2024年3月成本分析报告',\n    type: '月度报告',\n    period: '2024-03',\n    totalCost: 2850000,\n    wasteAmount: 156000,\n    wastePercentage: 5.47,\n    marginAmount: 712500,\n    marginPercentage: 25.0,\n    status: '已完成',\n    createdAt: '2024-04-01T00:00:00Z',\n    createdBy: 'finance_manager'\n  }, {\n    id: '2',\n    name: '2024年Q1季度成本报告',\n    type: '季度报告',\n    period: '2024-Q1',\n    totalCost: 8250000,\n    wasteAmount: 452000,\n    wastePercentage: 5.48,\n    marginAmount: 2062500,\n    marginPercentage: 25.0,\n    status: '已完成',\n    createdAt: '2024-04-05T00:00:00Z',\n    createdBy: 'finance_manager'\n  }, {\n    id: '3',\n    name: '华为项目成本专项报告',\n    type: '专项报告',\n    period: '2024-03',\n    totalCost: 1250000,\n    wasteAmount: 68000,\n    wastePercentage: 5.44,\n    marginAmount: 312500,\n    marginPercentage: 25.0,\n    status: '进行中',\n    createdAt: '2024-03-28T00:00:00Z',\n    createdBy: 'finance_manager'\n  }];\n  const reportColumns = [{\n    title: '报告名称',\n    dataIndex: 'name',\n    key: 'name',\n    ellipsis: true\n  }, {\n    title: '报告类型',\n    dataIndex: 'type',\n    key: 'type',\n    width: 100,\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: type === '月度报告' ? 'blue' : type === '季度报告' ? 'green' : 'orange',\n      children: type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '报告期间',\n    dataIndex: 'period',\n    key: 'period',\n    width: 100\n  }, {\n    title: '总成本',\n    dataIndex: 'totalCost',\n    key: 'totalCost',\n    width: 120,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '浪费金额',\n    dataIndex: 'wasteAmount',\n    key: 'wasteAmount',\n    width: 120,\n    render: (amount, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: '#ff4d4f'\n        },\n        children: formatCurrency(amount)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [record.wastePercentage.toFixed(2), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '毛利',\n    dataIndex: 'marginAmount',\n    key: 'marginAmount',\n    width: 120,\n    render: (margin, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: '#52c41a'\n        },\n        children: formatCurrency(margin)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [record.marginPercentage.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === '已完成' ? 'green' : 'processing',\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 120,\n    render: date => formatDate(date)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u9884\\u89C8\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 21\n          }, this),\n          onClick: () => handlePreview(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5BFC\\u51FAExcel\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(FileExcelOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleExport('excel', record.id)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5BFC\\u51FAPDF\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(FilePdfOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleExport('pdf', record.id)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u6253\\u5370\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(PrinterOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 21\n          }, this),\n          onClick: () => window.print()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 统计数据\n  const stats = {\n    totalReports: mockReports.length,\n    completedReports: mockReports.filter(r => r.status === '已完成').length,\n    avgWasteRate: mockReports.reduce((sum, r) => sum + r.wastePercentage, 0) / mockReports.length,\n    totalCostSum: mockReports.reduce((sum, r) => sum + r.totalCost, 0)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u62A5\\u544A\\u603B\\u6570\",\n            value: stats.totalReports,\n            prefix: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: stats.completedReports,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u6D6A\\u8D39\\u7387\",\n            value: stats.avgWasteRate,\n            precision: 2,\n            suffix: \"%\",\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u6210\\u672C\",\n            value: stats.totalCostSum,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 23\n            }, this),\n            formatter: value => formatCurrency(Number(value)),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u6210\\u672C\\u62A5\\u544A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              value: reportType,\n              onChange: setReportType,\n              style: {\n                width: 120\n              },\n              options: [{\n                label: '月度报告',\n                value: 'monthly'\n              }, {\n                label: '季度报告',\n                value: 'quarterly'\n              }, {\n                label: '年度报告',\n                value: 'yearly'\n              }, {\n                label: '专项报告',\n                value: 'special'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              value: dateRange,\n              onChange: dates => dates && setDateRange(dates),\n              style: {\n                width: 240\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 23\n              }, this),\n              onClick: () => setCustomReportVisible(true),\n              children: \"\\u81EA\\u5B9A\\u4E49\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleExport('excel'),\n              children: \"\\u6279\\u91CF\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: reportColumns,\n        dataSource: mockReports,\n        loading: loading,\n        rowKey: \"id\",\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u751F\\u6210\\u81EA\\u5B9A\\u4E49\\u62A5\\u544A\",\n      open: customReportVisible,\n      onOk: handleCustomReport,\n      onCancel: () => setCustomReportVisible(false),\n      width: 600,\n      okText: \"\\u751F\\u6210\\u62A5\\u544A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: customForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"reportName\",\n          label: \"\\u62A5\\u544A\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入报告名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62A5\\u544A\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"reportType\",\n          label: \"\\u62A5\\u544A\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择报告类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u62A5\\u544A\\u7C7B\\u578B\",\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"cost_analysis\",\n              children: \"\\u6210\\u672C\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"waste_analysis\",\n              children: \"\\u6D6A\\u8D39\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"margin_analysis\",\n              children: \"\\u6BDB\\u5229\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"trend_analysis\",\n              children: \"\\u8D8B\\u52BF\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"dateRange\",\n          label: \"\\u62A5\\u544A\\u671F\\u95F4\",\n          rules: [{\n            required: true,\n            message: '请选择报告期间'\n          }],\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"includeItems\",\n          label: \"\\u5305\\u542B\\u5185\\u5BB9\",\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"cost_overview\",\n                  children: \"\\u6210\\u672C\\u6982\\u89C8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"waste_analysis\",\n                  children: \"\\u6D6A\\u8D39\\u5206\\u6790\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"trend_charts\",\n                  children: \"\\u8D8B\\u52BF\\u56FE\\u8868\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"order_details\",\n                  children: \"\\u8BA2\\u5355\\u660E\\u7EC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"supplier_analysis\",\n                  children: \"\\u4F9B\\u5E94\\u5546\\u5206\\u6790\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"recommendations\",\n                  children: \"\\u6539\\u5584\\u5EFA\\u8BAE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u62A5\\u544A\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62A5\\u544A\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `预览报告: ${selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.name}`,\n      open: previewVisible,\n      onCancel: () => setPreviewVisible(false),\n      width: 800,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setPreviewVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 53\n        }, this),\n        children: \"\\u5BFC\\u51FA\"\n      }, \"export\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 11\n      }, this)],\n      children: selectedReport && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u62A5\\u544A\\u9884\\u89C8\",\n          description: \"\\u8FD9\\u662F\\u62A5\\u544A\\u7684\\u9884\\u89C8\\u7248\\u672C\\uFF0C\\u5B8C\\u6574\\u5185\\u5BB9\\u8BF7\\u5BFC\\u51FA\\u67E5\\u770B\",\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u62A5\\u544A\\u6982\\u8981\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u6210\\u672C\",\n              value: selectedReport.totalCost,\n              formatter: value => formatCurrency(Number(value))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6D6A\\u8D39\\u91D1\\u989D\",\n              value: selectedReport.wasteAmount,\n              formatter: value => formatCurrency(Number(value)),\n              valueStyle: {\n                color: '#ff4d4f'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6BDB\\u5229\",\n              value: selectedReport.marginAmount,\n              formatter: value => formatCurrency(Number(value)),\n              valueStyle: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u5173\\u952E\\u6307\\u6807\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u6D6A\\u8D39\\u7387: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedReport.wastePercentage,\n              strokeColor: \"#ff4d4f\",\n              format: percent => `${percent === null || percent === void 0 ? void 0 : percent.toFixed(2)}%`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u6BDB\\u5229\\u7387: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedReport.marginPercentage,\n              strokeColor: \"#52c41a\",\n              format: percent => `${percent === null || percent === void 0 ? void 0 : percent.toFixed(1)}%`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n};\n_s(CostReportsPage, \"598G8NqxjG0ADDCykjRxn1hcIew=\", false, function () {\n  return [useAppDispatch, useAppSelector, Form.useForm];\n});\n_c = CostReportsPage;\nexport default CostReportsPage;\nvar _c;\n$RefreshReg$(_c, \"CostReportsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Table", "<PERSON><PERSON>", "Space", "Select", "DatePicker", "Row", "Col", "Statistic", "Tag", "Modal", "Form", "Input", "Checkbox", "Divider", "<PERSON><PERSON>", "Progress", "<PERSON><PERSON><PERSON>", "FileExcelOutlined", "FilePdfOutlined", "PrinterOutlined", "DownloadOutlined", "EyeOutlined", "SettingOutlined", "CalendarOutlined", "DollarOutlined", "WarningOutlined", "dayjs", "useAppDispatch", "useAppSelector", "fetchCostReports", "formatCurrency", "formatDate", "jsxDEV", "_jsxDEV", "Title", "Text", "RangePicker", "TextArea", "CostReportsPage", "_s", "dispatch", "costReports", "loading", "state", "cost", "reportType", "setReportType", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "customReportVisible", "setCustomReportVisible", "previewVisible", "setPreviewVisible", "selectedReport", "setSelectedReport", "customForm", "useForm", "loadData", "type", "startDate", "format", "endDate", "handleExport", "reportId", "success", "title", "content", "toUpperCase", "handlePreview", "report", "handleCustomReport", "values", "validateFields", "console", "log", "error", "mockReports", "id", "name", "period", "totalCost", "wasteAmount", "wastePercentage", "marginAmount", "marginPercentage", "status", "createdAt", "created<PERSON>y", "reportColumns", "dataIndex", "key", "ellipsis", "width", "render", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "amount", "record", "direction", "size", "style", "fontSize", "toFixed", "margin", "date", "fixed", "_", "icon", "onClick", "window", "print", "stats", "totalReports", "length", "completedReports", "filter", "r", "avgWasteRate", "reduce", "sum", "totalCostSum", "gutter", "marginBottom", "xs", "sm", "value", "prefix", "valueStyle", "precision", "suffix", "formatter", "Number", "justify", "align", "level", "onChange", "options", "label", "dates", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onOk", "onCancel", "okText", "cancelText", "form", "layout", "<PERSON><PERSON>", "rules", "required", "message", "placeholder", "Option", "Group", "span", "rows", "footer", "description", "showIcon", "orientation", "percent", "strokeColor", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/cost/CostReportsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Table,\n  Button,\n  Space,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Checkbox,\n  Divider,\n  Alert,\n  Progress,\n  Tooltip,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  FileExcelOutlined,\n  FilePdfOutlined,\n  PrinterOutlined,\n  DownloadOutlined,\n  EyeOutlined,\n  SettingOutlined,\n  CalendarOutlined,\n  DollarOutlined,\n  RiseOutlined,\n  WarningOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCostReports } from '../../store/slices/costSlice';\nimport { formatCurrency, formatDate } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\nconst { TextArea } = Input;\n\nconst CostReportsPage: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { costReports, loading } = useAppSelector(state => state.cost);\n\n  const [reportType, setReportType] = useState<string>('monthly');\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([\n    dayjs().subtract(1, 'month'),\n    dayjs(),\n  ]);\n  const [customReportVisible, setCustomReportVisible] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [selectedReport, setSelectedReport] = useState<any>(null);\n  const [customForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [reportType, dateRange]);\n\n  const loadData = () => {\n    dispatch(fetchCostReports({\n      type: reportType,\n      startDate: dateRange[0].format('YYYY-MM-DD'),\n      endDate: dateRange[1].format('YYYY-MM-DD'),\n    }));\n  };\n\n  const handleExport = (format: string, reportId?: string) => {\n    // TODO: 实现导出功能\n    Modal.success({\n      title: '导出成功',\n      content: `报告已导出为 ${format.toUpperCase()} 格式`,\n    });\n  };\n\n  const handlePreview = (report: any) => {\n    setSelectedReport(report);\n    setPreviewVisible(true);\n  };\n\n  const handleCustomReport = async () => {\n    try {\n      const values = await customForm.validateFields();\n      // TODO: 生成自定义报告\n      console.log('自定义报告参数:', values);\n      setCustomReportVisible(false);\n      Modal.success({\n        title: '报告生成成功',\n        content: '自定义报告已生成，请在报告列表中查看',\n      });\n    } catch (error) {\n      console.error('生成报告失败:', error);\n    }\n  };\n\n  // 模拟报告数据\n  const mockReports = [\n    {\n      id: '1',\n      name: '2024年3月成本分析报告',\n      type: '月度报告',\n      period: '2024-03',\n      totalCost: 2850000,\n      wasteAmount: 156000,\n      wastePercentage: 5.47,\n      marginAmount: 712500,\n      marginPercentage: 25.0,\n      status: '已完成',\n      createdAt: '2024-04-01T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n    {\n      id: '2',\n      name: '2024年Q1季度成本报告',\n      type: '季度报告',\n      period: '2024-Q1',\n      totalCost: 8250000,\n      wasteAmount: 452000,\n      wastePercentage: 5.48,\n      marginAmount: 2062500,\n      marginPercentage: 25.0,\n      status: '已完成',\n      createdAt: '2024-04-05T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n    {\n      id: '3',\n      name: '华为项目成本专项报告',\n      type: '专项报告',\n      period: '2024-03',\n      totalCost: 1250000,\n      wasteAmount: 68000,\n      wastePercentage: 5.44,\n      marginAmount: 312500,\n      marginPercentage: 25.0,\n      status: '进行中',\n      createdAt: '2024-03-28T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n  ];\n\n  const reportColumns = [\n    {\n      title: '报告名称',\n      dataIndex: 'name',\n      key: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '报告类型',\n      dataIndex: 'type',\n      key: 'type',\n      width: 100,\n      render: (type: string) => (\n        <Tag color={type === '月度报告' ? 'blue' : type === '季度报告' ? 'green' : 'orange'}>\n          {type}\n        </Tag>\n      ),\n    },\n    {\n      title: '报告期间',\n      dataIndex: 'period',\n      key: 'period',\n      width: 100,\n    },\n    {\n      title: '总成本',\n      dataIndex: 'totalCost',\n      key: 'totalCost',\n      width: 120,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '浪费金额',\n      dataIndex: 'wasteAmount',\n      key: 'wasteAmount',\n      width: 120,\n      render: (amount: number, record: any) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ color: '#ff4d4f' }}>{formatCurrency(amount)}</Text>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.wastePercentage.toFixed(2)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '毛利',\n      dataIndex: 'marginAmount',\n      key: 'marginAmount',\n      width: 120,\n      render: (margin: number, record: any) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ color: '#52c41a' }}>{formatCurrency(margin)}</Text>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.marginPercentage.toFixed(1)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={status === '已完成' ? 'green' : 'processing'}>\n          {status}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right' as const,\n      render: (_: any, record: any) => (\n        <Space size=\"small\">\n          <Tooltip title=\"预览\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handlePreview(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"导出Excel\">\n            <Button\n              type=\"text\"\n              icon={<FileExcelOutlined />}\n              onClick={() => handleExport('excel', record.id)}\n            />\n          </Tooltip>\n          <Tooltip title=\"导出PDF\">\n            <Button\n              type=\"text\"\n              icon={<FilePdfOutlined />}\n              onClick={() => handleExport('pdf', record.id)}\n            />\n          </Tooltip>\n          <Tooltip title=\"打印\">\n            <Button\n              type=\"text\"\n              icon={<PrinterOutlined />}\n              onClick={() => window.print()}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  // 统计数据\n  const stats = {\n    totalReports: mockReports.length,\n    completedReports: mockReports.filter(r => r.status === '已完成').length,\n    avgWasteRate: mockReports.reduce((sum, r) => sum + r.wastePercentage, 0) / mockReports.length,\n    totalCostSum: mockReports.reduce((sum, r) => sum + r.totalCost, 0),\n  };\n\n  return (\n    <div>\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"报告总数\"\n              value={stats.totalReports}\n              prefix={<CalendarOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={stats.completedReports}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"平均浪费率\"\n              value={stats.avgWasteRate}\n              precision={2}\n              suffix=\"%\"\n              prefix={<WarningOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总成本\"\n              value={stats.totalCostSum}\n              prefix={<DollarOutlined />}\n              formatter={(value) => formatCurrency(Number(value))}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              成本报告\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Select\n                value={reportType}\n                onChange={setReportType}\n                style={{ width: 120 }}\n                options={[\n                  { label: '月度报告', value: 'monthly' },\n                  { label: '季度报告', value: 'quarterly' },\n                  { label: '年度报告', value: 'yearly' },\n                  { label: '专项报告', value: 'special' },\n                ]}\n              />\n              <RangePicker\n                value={dateRange}\n                onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n                style={{ width: 240 }}\n              />\n              <Button\n                icon={<SettingOutlined />}\n                onClick={() => setCustomReportVisible(true)}\n              >\n                自定义报告\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<DownloadOutlined />}\n                onClick={() => handleExport('excel')}\n              >\n                批量导出\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Table\n          columns={reportColumns}\n          dataSource={mockReports}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1200 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 自定义报告模态框 */}\n      <Modal\n        title=\"生成自定义报告\"\n        open={customReportVisible}\n        onOk={handleCustomReport}\n        onCancel={() => setCustomReportVisible(false)}\n        width={600}\n        okText=\"生成报告\"\n        cancelText=\"取消\"\n      >\n        <Form form={customForm} layout=\"vertical\">\n          <Form.Item\n            name=\"reportName\"\n            label=\"报告名称\"\n            rules={[{ required: true, message: '请输入报告名称' }]}\n          >\n            <Input placeholder=\"请输入报告名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"reportType\"\n            label=\"报告类型\"\n            rules={[{ required: true, message: '请选择报告类型' }]}\n          >\n            <Select placeholder=\"请选择报告类型\">\n              <Select.Option value=\"cost_analysis\">成本分析报告</Select.Option>\n              <Select.Option value=\"waste_analysis\">浪费分析报告</Select.Option>\n              <Select.Option value=\"margin_analysis\">毛利分析报告</Select.Option>\n              <Select.Option value=\"trend_analysis\">趋势分析报告</Select.Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"dateRange\"\n            label=\"报告期间\"\n            rules={[{ required: true, message: '请选择报告期间' }]}\n          >\n            <RangePicker style={{ width: '100%' }} />\n          </Form.Item>\n\n          <Form.Item name=\"includeItems\" label=\"包含内容\">\n            <Checkbox.Group>\n              <Row>\n                <Col span={12}>\n                  <Checkbox value=\"cost_overview\">成本概览</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"waste_analysis\">浪费分析</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"trend_charts\">趋势图表</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"order_details\">订单明细</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"supplier_analysis\">供应商分析</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"recommendations\">改善建议</Checkbox>\n                </Col>\n              </Row>\n            </Checkbox.Group>\n          </Form.Item>\n\n          <Form.Item name=\"description\" label=\"报告描述\">\n            <TextArea rows={3} placeholder=\"请输入报告描述\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 报告预览模态框 */}\n      <Modal\n        title={`预览报告: ${selectedReport?.name}`}\n        open={previewVisible}\n        onCancel={() => setPreviewVisible(false)}\n        width={800}\n        footer={[\n          <Button key=\"close\" onClick={() => setPreviewVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"export\" type=\"primary\" icon={<DownloadOutlined />}>\n            导出\n          </Button>,\n        ]}\n      >\n        {selectedReport && (\n          <div>\n            <Alert\n              message=\"报告预览\"\n              description=\"这是报告的预览版本，完整内容请导出查看\"\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n\n            <Divider orientation=\"left\">报告概要</Divider>\n            <Row gutter={[16, 16]}>\n              <Col span={8}>\n                <Statistic\n                  title=\"总成本\"\n                  value={selectedReport.totalCost}\n                  formatter={(value) => formatCurrency(Number(value))}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"浪费金额\"\n                  value={selectedReport.wasteAmount}\n                  formatter={(value) => formatCurrency(Number(value))}\n                  valueStyle={{ color: '#ff4d4f' }}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"毛利\"\n                  value={selectedReport.marginAmount}\n                  formatter={(value) => formatCurrency(Number(value))}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            </Row>\n\n            <Divider orientation=\"left\">关键指标</Divider>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text>浪费率: </Text>\n                <Progress\n                  percent={selectedReport.wastePercentage}\n                  strokeColor=\"#ff4d4f\"\n                  format={(percent) => `${percent?.toFixed(2)}%`}\n                />\n              </div>\n              <div>\n                <Text>毛利率: </Text>\n                <Progress\n                  percent={selectedReport.marginPercentage}\n                  strokeColor=\"#52c41a\"\n                  format={(percent) => `${percent?.toFixed(1)}%`}\n                />\n              </div>\n            </Space>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default CostReportsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,MAAM;AAEb,SACEC,iBAAiB,EACjBC,eAAe,EACfC,eAAe,EACfC,gBAAgB,EAChBC,WAAW,EACXC,eAAe,EACfC,gBAAgB,EAChBC,cAAc,EAEdC,eAAe,QACV,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,cAAc,EAAEC,UAAU,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpC,UAAU;AAClC,MAAM;EAAEqC;AAAY,CAAC,GAAGhC,UAAU;AAClC,MAAM;EAAEiC;AAAS,CAAC,GAAG1B,KAAK;AAE1B,MAAM2B,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGb,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEc,WAAW;IAAEC;EAAQ,CAAC,GAAGd,cAAc,CAACe,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAEpE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAS,SAAS,CAAC;EAC/D,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAA6B,CACrE8B,KAAK,CAAC,CAAC,CAACuB,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,EAC5BvB,KAAK,CAAC,CAAC,CACR,CAAC;EACF,MAAM,CAACwB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAM,IAAI,CAAC;EAC/D,MAAM,CAAC4D,UAAU,CAAC,GAAG9C,IAAI,CAAC+C,OAAO,CAAC,CAAC;EAEnC5D,SAAS,CAAC,MAAM;IACd6D,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACb,UAAU,EAAEE,SAAS,CAAC,CAAC;EAE3B,MAAMW,QAAQ,GAAGA,CAAA,KAAM;IACrBlB,QAAQ,CAACX,gBAAgB,CAAC;MACxB8B,IAAI,EAAEd,UAAU;MAChBe,SAAS,EAAEb,SAAS,CAAC,CAAC,CAAC,CAACc,MAAM,CAAC,YAAY,CAAC;MAC5CC,OAAO,EAAEf,SAAS,CAAC,CAAC,CAAC,CAACc,MAAM,CAAC,YAAY;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAACF,MAAc,EAAEG,QAAiB,KAAK;IAC1D;IACAvD,KAAK,CAACwD,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,UAAUN,MAAM,CAACO,WAAW,CAAC,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,aAAa,GAAIC,MAAW,IAAK;IACrCf,iBAAiB,CAACe,MAAM,CAAC;IACzBjB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMkB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMhB,UAAU,CAACiB,cAAc,CAAC,CAAC;MAChD;MACAC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,MAAM,CAAC;MAC/BrB,sBAAsB,CAAC,KAAK,CAAC;MAC7B1C,KAAK,CAACwD,OAAO,CAAC;QACZC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,eAAe;IACrBpB,IAAI,EAAE,MAAM;IACZqB,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,OAAO;IAClBC,WAAW,EAAE,MAAM;IACnBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,MAAM;IACpBC,gBAAgB,EAAE,IAAI;IACtBC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,eAAe;IACrBpB,IAAI,EAAE,MAAM;IACZqB,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,OAAO;IAClBC,WAAW,EAAE,MAAM;IACnBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,OAAO;IACrBC,gBAAgB,EAAE,IAAI;IACtBC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEV,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,YAAY;IAClBpB,IAAI,EAAE,MAAM;IACZqB,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,OAAO;IAClBC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,MAAM;IACpBC,gBAAgB,EAAE,IAAI;IACtBC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF;EAED,MAAMC,aAAa,GAAG,CACpB;IACEvB,KAAK,EAAE,MAAM;IACbwB,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE1B,KAAK,EAAE,MAAM;IACbwB,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGnC,IAAY,iBACnB1B,OAAA,CAACzB,GAAG;MAACuF,KAAK,EAAEpC,IAAI,KAAK,MAAM,GAAG,MAAM,GAAGA,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,QAAS;MAAAqC,QAAA,EACzErC;IAAI;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACElC,KAAK,EAAE,MAAM;IACbwB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE;EACT,CAAC,EACD;IACE3B,KAAK,EAAE,KAAK;IACZwB,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGlD,IAAY,IAAKd,cAAc,CAACc,IAAI;EAC/C,CAAC,EACD;IACEsB,KAAK,EAAE,MAAM;IACbwB,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACO,MAAc,EAAEC,MAAW,kBAClCrE,OAAA,CAAC/B,KAAK;MAACqG,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAR,QAAA,gBAClC/D,OAAA,CAACE,IAAI;QAACsE,KAAK,EAAE;UAAEV,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EAAElE,cAAc,CAACuE,MAAM;MAAC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEnE,OAAA,CAACE,IAAI;QAACwB,IAAI,EAAC,WAAW;QAAC8C,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAAAV,QAAA,GAC5CM,MAAM,CAACnB,eAAe,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,GACrC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACElC,KAAK,EAAE,IAAI;IACXwB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACc,MAAc,EAAEN,MAAW,kBAClCrE,OAAA,CAAC/B,KAAK;MAACqG,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAR,QAAA,gBAClC/D,OAAA,CAACE,IAAI;QAACsE,KAAK,EAAE;UAAEV,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EAAElE,cAAc,CAAC8E,MAAM;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEnE,OAAA,CAACE,IAAI;QAACwB,IAAI,EAAC,WAAW;QAAC8C,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAAAV,QAAA,GAC5CM,MAAM,CAACjB,gBAAgB,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACElC,KAAK,EAAE,IAAI;IACXwB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGR,MAAc,iBACrBrD,OAAA,CAACzB,GAAG;MAACuF,KAAK,EAAET,MAAM,KAAK,KAAK,GAAG,OAAO,GAAG,YAAa;MAAAU,QAAA,EACnDV;IAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACElC,KAAK,EAAE,MAAM;IACbwB,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGe,IAAY,IAAK9E,UAAU,CAAC8E,IAAI;EAC3C,CAAC,EACD;IACE3C,KAAK,EAAE,IAAI;IACXyB,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE,GAAG;IACViB,KAAK,EAAE,OAAgB;IACvBhB,MAAM,EAAEA,CAACiB,CAAM,EAAET,MAAW,kBAC1BrE,OAAA,CAAC/B,KAAK;MAACsG,IAAI,EAAC,OAAO;MAAAR,QAAA,gBACjB/D,OAAA,CAACjB,OAAO;QAACkD,KAAK,EAAC,cAAI;QAAA8B,QAAA,eACjB/D,OAAA,CAAChC,MAAM;UACL0D,IAAI,EAAC,MAAM;UACXqD,IAAI,eAAE/E,OAAA,CAACZ,WAAW;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBa,OAAO,EAAEA,CAAA,KAAM5C,aAAa,CAACiC,MAAM;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVnE,OAAA,CAACjB,OAAO;QAACkD,KAAK,EAAC,mBAAS;QAAA8B,QAAA,eACtB/D,OAAA,CAAChC,MAAM;UACL0D,IAAI,EAAC,MAAM;UACXqD,IAAI,eAAE/E,OAAA,CAAChB,iBAAiB;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5Ba,OAAO,EAAEA,CAAA,KAAMlD,YAAY,CAAC,OAAO,EAAEuC,MAAM,CAACxB,EAAE;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVnE,OAAA,CAACjB,OAAO;QAACkD,KAAK,EAAC,iBAAO;QAAA8B,QAAA,eACpB/D,OAAA,CAAChC,MAAM;UACL0D,IAAI,EAAC,MAAM;UACXqD,IAAI,eAAE/E,OAAA,CAACf,eAAe;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Ba,OAAO,EAAEA,CAAA,KAAMlD,YAAY,CAAC,KAAK,EAAEuC,MAAM,CAACxB,EAAE;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVnE,OAAA,CAACjB,OAAO;QAACkD,KAAK,EAAC,cAAI;QAAA8B,QAAA,eACjB/D,OAAA,CAAChC,MAAM;UACL0D,IAAI,EAAC,MAAM;UACXqD,IAAI,eAAE/E,OAAA,CAACd,eAAe;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Ba,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,KAAK,CAAC;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;;EAED;EACA,MAAMgB,KAAK,GAAG;IACZC,YAAY,EAAExC,WAAW,CAACyC,MAAM;IAChCC,gBAAgB,EAAE1C,WAAW,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnC,MAAM,KAAK,KAAK,CAAC,CAACgC,MAAM;IACpEI,YAAY,EAAE7C,WAAW,CAAC8C,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAGH,CAAC,CAACtC,eAAe,EAAE,CAAC,CAAC,GAAGN,WAAW,CAACyC,MAAM;IAC7FO,YAAY,EAAEhD,WAAW,CAAC8C,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAGH,CAAC,CAACxC,SAAS,EAAE,CAAC;EACnE,CAAC;EAED,oBACEhD,OAAA;IAAA+D,QAAA,gBAEE/D,OAAA,CAAC5B,GAAG;MAACyH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrB,KAAK,EAAE;QAAEsB,YAAY,EAAE;MAAG,CAAE;MAAA/B,QAAA,gBACjD/D,OAAA,CAAC3B,GAAG;QAAC0H,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACjB/D,OAAA,CAACnC,IAAI;UAAAkG,QAAA,eACH/D,OAAA,CAAC1B,SAAS;YACR2D,KAAK,EAAC,0BAAM;YACZgE,KAAK,EAAEd,KAAK,CAACC,YAAa;YAC1Bc,MAAM,eAAElG,OAAA,CAACV,gBAAgB;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BgC,UAAU,EAAE;cAAErC,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAAC3B,GAAG;QAAC0H,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACjB/D,OAAA,CAACnC,IAAI;UAAAkG,QAAA,eACH/D,OAAA,CAAC1B,SAAS;YACR2D,KAAK,EAAC,oBAAK;YACXgE,KAAK,EAAEd,KAAK,CAACG,gBAAiB;YAC9Ba,UAAU,EAAE;cAAErC,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAAC3B,GAAG;QAAC0H,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACjB/D,OAAA,CAACnC,IAAI;UAAAkG,QAAA,eACH/D,OAAA,CAAC1B,SAAS;YACR2D,KAAK,EAAC,gCAAO;YACbgE,KAAK,EAAEd,KAAK,CAACM,YAAa;YAC1BW,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC,GAAG;YACVH,MAAM,eAAElG,OAAA,CAACR,eAAe;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BgC,UAAU,EAAE;cAAErC,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAAC3B,GAAG;QAAC0H,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACjB/D,OAAA,CAACnC,IAAI;UAAAkG,QAAA,eACH/D,OAAA,CAAC1B,SAAS;YACR2D,KAAK,EAAC,oBAAK;YACXgE,KAAK,EAAEd,KAAK,CAACS,YAAa;YAC1BM,MAAM,eAAElG,OAAA,CAACT,cAAc;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BmC,SAAS,EAAGL,KAAK,IAAKpG,cAAc,CAAC0G,MAAM,CAACN,KAAK,CAAC,CAAE;YACpDE,UAAU,EAAE;cAAErC,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnE,OAAA,CAACnC,IAAI;MAAAkG,QAAA,gBACH/D,OAAA,CAAC5B,GAAG;QAACoI,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACjC,KAAK,EAAE;UAAEsB,YAAY,EAAE;QAAG,CAAE;QAAA/B,QAAA,gBACtE/D,OAAA,CAAC3B,GAAG;UAAA0F,QAAA,eACF/D,OAAA,CAACC,KAAK;YAACyG,KAAK,EAAE,CAAE;YAAClC,KAAK,EAAE;cAAEG,MAAM,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNnE,OAAA,CAAC3B,GAAG;UAAA0F,QAAA,eACF/D,OAAA,CAAC/B,KAAK;YAAA8F,QAAA,gBACJ/D,OAAA,CAAC9B,MAAM;cACL+H,KAAK,EAAErF,UAAW;cAClB+F,QAAQ,EAAE9F,aAAc;cACxB2D,KAAK,EAAE;gBAAEZ,KAAK,EAAE;cAAI,CAAE;cACtBgD,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,MAAM;gBAAEZ,KAAK,EAAE;cAAU,CAAC,EACnC;gBAAEY,KAAK,EAAE,MAAM;gBAAEZ,KAAK,EAAE;cAAY,CAAC,EACrC;gBAAEY,KAAK,EAAE,MAAM;gBAAEZ,KAAK,EAAE;cAAS,CAAC,EAClC;gBAAEY,KAAK,EAAE,MAAM;gBAAEZ,KAAK,EAAE;cAAU,CAAC;YACnC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnE,OAAA,CAACG,WAAW;cACV8F,KAAK,EAAEnF,SAAU;cACjB6F,QAAQ,EAAGG,KAAK,IAAKA,KAAK,IAAI/F,YAAY,CAAC+F,KAAmC,CAAE;cAChFtC,KAAK,EAAE;gBAAEZ,KAAK,EAAE;cAAI;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFnE,OAAA,CAAChC,MAAM;cACL+G,IAAI,eAAE/E,OAAA,CAACX,eAAe;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1Ba,OAAO,EAAEA,CAAA,KAAM9D,sBAAsB,CAAC,IAAI,CAAE;cAAA6C,QAAA,EAC7C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnE,OAAA,CAAChC,MAAM;cACL0D,IAAI,EAAC,SAAS;cACdqD,IAAI,eAAE/E,OAAA,CAACb,gBAAgB;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3Ba,OAAO,EAAEA,CAAA,KAAMlD,YAAY,CAAC,OAAO,CAAE;cAAAiC,QAAA,EACtC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnE,OAAA,CAACjC,KAAK;QACJgJ,OAAO,EAAEvD,aAAc;QACvBwD,UAAU,EAAEpE,WAAY;QACxBnC,OAAO,EAAEA,OAAQ;QACjBwG,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnE,OAAA,CAACxB,KAAK;MACJyD,KAAK,EAAC,4CAAS;MACfyF,IAAI,EAAEzG,mBAAoB;MAC1B0G,IAAI,EAAErF,kBAAmB;MACzBsF,QAAQ,EAAEA,CAAA,KAAM1G,sBAAsB,CAAC,KAAK,CAAE;MAC9C0C,KAAK,EAAE,GAAI;MACXiE,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MAAA/D,QAAA,eAEf/D,OAAA,CAACvB,IAAI;QAACsJ,IAAI,EAAExG,UAAW;QAACyG,MAAM,EAAC,UAAU;QAAAjE,QAAA,gBACvC/D,OAAA,CAACvB,IAAI,CAACwJ,IAAI;UACRnF,IAAI,EAAC,YAAY;UACjB+D,KAAK,EAAC,0BAAM;UACZqB,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAArE,QAAA,eAEhD/D,OAAA,CAACtB,KAAK;YAAC2J,WAAW,EAAC;UAAS;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZnE,OAAA,CAACvB,IAAI,CAACwJ,IAAI;UACRnF,IAAI,EAAC,YAAY;UACjB+D,KAAK,EAAC,0BAAM;UACZqB,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAArE,QAAA,eAEhD/D,OAAA,CAAC9B,MAAM;YAACmK,WAAW,EAAC,4CAAS;YAAAtE,QAAA,gBAC3B/D,OAAA,CAAC9B,MAAM,CAACoK,MAAM;cAACrC,KAAK,EAAC,eAAe;cAAAlC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC3DnE,OAAA,CAAC9B,MAAM,CAACoK,MAAM;cAACrC,KAAK,EAAC,gBAAgB;cAAAlC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC5DnE,OAAA,CAAC9B,MAAM,CAACoK,MAAM;cAACrC,KAAK,EAAC,iBAAiB;cAAAlC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC7DnE,OAAA,CAAC9B,MAAM,CAACoK,MAAM;cAACrC,KAAK,EAAC,gBAAgB;cAAAlC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZnE,OAAA,CAACvB,IAAI,CAACwJ,IAAI;UACRnF,IAAI,EAAC,WAAW;UAChB+D,KAAK,EAAC,0BAAM;UACZqB,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAArE,QAAA,eAEhD/D,OAAA,CAACG,WAAW;YAACqE,KAAK,EAAE;cAAEZ,KAAK,EAAE;YAAO;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEZnE,OAAA,CAACvB,IAAI,CAACwJ,IAAI;UAACnF,IAAI,EAAC,cAAc;UAAC+D,KAAK,EAAC,0BAAM;UAAA9C,QAAA,eACzC/D,OAAA,CAACrB,QAAQ,CAAC4J,KAAK;YAAAxE,QAAA,eACb/D,OAAA,CAAC5B,GAAG;cAAA2F,QAAA,gBACF/D,OAAA,CAAC3B,GAAG;gBAACmK,IAAI,EAAE,EAAG;gBAAAzE,QAAA,eACZ/D,OAAA,CAACrB,QAAQ;kBAACsH,KAAK,EAAC,eAAe;kBAAAlC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNnE,OAAA,CAAC3B,GAAG;gBAACmK,IAAI,EAAE,EAAG;gBAAAzE,QAAA,eACZ/D,OAAA,CAACrB,QAAQ;kBAACsH,KAAK,EAAC,gBAAgB;kBAAAlC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNnE,OAAA,CAAC3B,GAAG;gBAACmK,IAAI,EAAE,EAAG;gBAAAzE,QAAA,eACZ/D,OAAA,CAACrB,QAAQ;kBAACsH,KAAK,EAAC,cAAc;kBAAAlC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNnE,OAAA,CAAC3B,GAAG;gBAACmK,IAAI,EAAE,EAAG;gBAAAzE,QAAA,eACZ/D,OAAA,CAACrB,QAAQ;kBAACsH,KAAK,EAAC,eAAe;kBAAAlC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNnE,OAAA,CAAC3B,GAAG;gBAACmK,IAAI,EAAE,EAAG;gBAAAzE,QAAA,eACZ/D,OAAA,CAACrB,QAAQ;kBAACsH,KAAK,EAAC,mBAAmB;kBAAAlC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNnE,OAAA,CAAC3B,GAAG;gBAACmK,IAAI,EAAE,EAAG;gBAAAzE,QAAA,eACZ/D,OAAA,CAACrB,QAAQ;kBAACsH,KAAK,EAAC,iBAAiB;kBAAAlC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEZnE,OAAA,CAACvB,IAAI,CAACwJ,IAAI;UAACnF,IAAI,EAAC,aAAa;UAAC+D,KAAK,EAAC,0BAAM;UAAA9C,QAAA,eACxC/D,OAAA,CAACI,QAAQ;YAACqI,IAAI,EAAE,CAAE;YAACJ,WAAW,EAAC;UAAS;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRnE,OAAA,CAACxB,KAAK;MACJyD,KAAK,EAAE,SAASZ,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyB,IAAI,EAAG;MACvC4E,IAAI,EAAEvG,cAAe;MACrByG,QAAQ,EAAEA,CAAA,KAAMxG,iBAAiB,CAAC,KAAK,CAAE;MACzCwC,KAAK,EAAE,GAAI;MACX8E,MAAM,EAAE,cACN1I,OAAA,CAAChC,MAAM;QAAagH,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAAC,KAAK,CAAE;QAAA2C,QAAA,EAAC;MAE7D,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACTnE,OAAA,CAAChC,MAAM;QAAc0D,IAAI,EAAC,SAAS;QAACqD,IAAI,eAAE/E,OAAA,CAACb,gBAAgB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAAC;MAEhE,GAFY,QAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEZ,CAAC,CACT;MAAAJ,QAAA,EAED1C,cAAc,iBACbrB,OAAA;QAAA+D,QAAA,gBACE/D,OAAA,CAACnB,KAAK;UACJuJ,OAAO,EAAC,0BAAM;UACdO,WAAW,EAAC,oHAAqB;UACjCjH,IAAI,EAAC,MAAM;UACXkH,QAAQ;UACRpE,KAAK,EAAE;YAAEsB,YAAY,EAAE;UAAG;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFnE,OAAA,CAACpB,OAAO;UAACiK,WAAW,EAAC,MAAM;UAAA9E,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1CnE,OAAA,CAAC5B,GAAG;UAACyH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACpB/D,OAAA,CAAC3B,GAAG;YAACmK,IAAI,EAAE,CAAE;YAAAzE,QAAA,eACX/D,OAAA,CAAC1B,SAAS;cACR2D,KAAK,EAAC,oBAAK;cACXgE,KAAK,EAAE5E,cAAc,CAAC2B,SAAU;cAChCsD,SAAS,EAAGL,KAAK,IAAKpG,cAAc,CAAC0G,MAAM,CAACN,KAAK,CAAC;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA,CAAC3B,GAAG;YAACmK,IAAI,EAAE,CAAE;YAAAzE,QAAA,eACX/D,OAAA,CAAC1B,SAAS;cACR2D,KAAK,EAAC,0BAAM;cACZgE,KAAK,EAAE5E,cAAc,CAAC4B,WAAY;cAClCqD,SAAS,EAAGL,KAAK,IAAKpG,cAAc,CAAC0G,MAAM,CAACN,KAAK,CAAC,CAAE;cACpDE,UAAU,EAAE;gBAAErC,KAAK,EAAE;cAAU;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA,CAAC3B,GAAG;YAACmK,IAAI,EAAE,CAAE;YAAAzE,QAAA,eACX/D,OAAA,CAAC1B,SAAS;cACR2D,KAAK,EAAC,cAAI;cACVgE,KAAK,EAAE5E,cAAc,CAAC8B,YAAa;cACnCmD,SAAS,EAAGL,KAAK,IAAKpG,cAAc,CAAC0G,MAAM,CAACN,KAAK,CAAC,CAAE;cACpDE,UAAU,EAAE;gBAAErC,KAAK,EAAE;cAAU;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnE,OAAA,CAACpB,OAAO;UAACiK,WAAW,EAAC,MAAM;UAAA9E,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1CnE,OAAA,CAAC/B,KAAK;UAACqG,SAAS,EAAC,UAAU;UAACE,KAAK,EAAE;YAAEZ,KAAK,EAAE;UAAO,CAAE;UAAAG,QAAA,gBACnD/D,OAAA;YAAA+D,QAAA,gBACE/D,OAAA,CAACE,IAAI;cAAA6D,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBnE,OAAA,CAAClB,QAAQ;cACPgK,OAAO,EAAEzH,cAAc,CAAC6B,eAAgB;cACxC6F,WAAW,EAAC,SAAS;cACrBnH,MAAM,EAAGkH,OAAO,IAAK,GAAGA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEpE,OAAO,CAAC,CAAC,CAAC;YAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnE,OAAA;YAAA+D,QAAA,gBACE/D,OAAA,CAACE,IAAI;cAAA6D,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBnE,OAAA,CAAClB,QAAQ;cACPgK,OAAO,EAAEzH,cAAc,CAAC+B,gBAAiB;cACzC2F,WAAW,EAAC,SAAS;cACrBnH,MAAM,EAAGkH,OAAO,IAAK,GAAGA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEpE,OAAO,CAAC,CAAC,CAAC;YAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA7dID,eAAyB;EAAA,QACZX,cAAc,EACEC,cAAc,EAU1BlB,IAAI,CAAC+C,OAAO;AAAA;AAAAwH,EAAA,GAZ7B3I,eAAyB;AA+d/B,eAAeA,eAAe;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}