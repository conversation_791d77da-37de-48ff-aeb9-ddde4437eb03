[{"D:\\customerDemo\\Link-BOM-S\\frontend\\src\\index.tsx": "1", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\App.tsx": "2", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\hooks\\redux.ts": "3", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\authSlice.ts": "4", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMListPage.tsx": "5", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMEditPage.tsx": "6", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMViewPage.tsx": "7", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMCreatePage.tsx": "8", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\auth\\LoginPage.tsx": "9", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMViewPage.tsx": "10", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\dashboard\\DashboardPage.tsx": "11", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMDerivePage.tsx": "12", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMCreatePage.tsx": "13", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMListPage.tsx": "14", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialEditPage.tsx": "15", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialListPage.tsx": "16", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialCreatePage.tsx": "17", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseListPage.tsx": "18", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\MRPCalculationPage.tsx": "19", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseRequisitionPage.tsx": "20", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryReceivePage.tsx": "21", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryAdjustPage.tsx": "22", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryListPage.tsx": "23", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryIssuePage.tsx": "24", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\RemnantListPage.tsx": "25", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\MaintenancePage.tsx": "26", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\DeviceArchivePage.tsx": "27", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\ServiceBOMListPage.tsx": "28", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\CostReportsPage.tsx": "29", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\WasteTrackingPage.tsx": "30", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\CostAnalysisPage.tsx": "31", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNReviewPage.tsx": "32", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNCreatePage.tsx": "33", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNListPage.tsx": "34", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\RoleListPage.tsx": "35", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\PermissionListPage.tsx": "36", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\SystemConfigPage.tsx": "37", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\UserListPage.tsx": "38", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\bomSlice.ts": "39", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\index.ts": "40", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobileInventoryPage.tsx": "41", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\constants\\index.ts": "42", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\reports\\ReportsPage.tsx": "43", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\authService.ts": "44", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobilePage.tsx": "45", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\reports\\DashboardConfigPage.tsx": "46", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobileScanPage.tsx": "47", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\layout\\MainLayout.tsx": "48", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "49", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\index.ts": "50", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\costSlice.ts": "51", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\serviceSlice.ts": "52", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\systemSlice.ts": "53", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\ecnSlice.ts": "54", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\purchaseSlice.ts": "55", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\inventorySlice.ts": "56", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\bomService.ts": "57", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\dashboardSlice.ts": "58", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\materialSlice.ts": "59", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\api.ts": "60", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\mockService.ts": "61", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\reportSlice.ts": "62", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\mobileSlice.ts": "63", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseOptimizationPage.tsx": "64", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\CuttingPlanPage.tsx": "65", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\StandardCostPage.tsx": "66", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\AsBuiltBOMPage.tsx": "67", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\SparePartsPage.tsx": "68", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\BatchTrackingPage.tsx": "69", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\AuditLogPage.tsx": "70", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\format.ts": "71", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\errorHandler.ts": "72", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\ErrorBoundary.tsx": "73", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\index.ts": "74", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\LoadingSpinner.tsx": "75", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\ConfirmDialog.tsx": "76"}, {"size": 274, "mtime": 1755518446419, "results": "77", "hashOfConfig": "78"}, {"size": 8931, "mtime": 1755600184192, "results": "79", "hashOfConfig": "78"}, {"size": 302, "mtime": 1755519336391, "results": "80", "hashOfConfig": "78"}, {"size": 4582, "mtime": 1755519350062, "results": "81", "hashOfConfig": "78"}, {"size": 31519, "mtime": 1755657341502, "results": "82", "hashOfConfig": "78"}, {"size": 13884, "mtime": 1755657381564, "results": "83", "hashOfConfig": "78"}, {"size": 21603, "mtime": 1755581011049, "results": "84", "hashOfConfig": "78"}, {"size": 22899, "mtime": 1755565866646, "results": "85", "hashOfConfig": "78"}, {"size": 10283, "mtime": 1755657416648, "results": "86", "hashOfConfig": "78"}, {"size": 13460, "mtime": 1755562238203, "results": "87", "hashOfConfig": "78"}, {"size": 13634, "mtime": 1755656403206, "results": "88", "hashOfConfig": "78"}, {"size": 20684, "mtime": 1755520159683, "results": "89", "hashOfConfig": "78"}, {"size": 23406, "mtime": 1755657401114, "results": "90", "hashOfConfig": "78"}, {"size": 21223, "mtime": 1755657286265, "results": "91", "hashOfConfig": "78"}, {"size": 13648, "mtime": 1755573231678, "results": "92", "hashOfConfig": "78"}, {"size": 36327, "mtime": 1755572839571, "results": "93", "hashOfConfig": "78"}, {"size": 11298, "mtime": 1755573217826, "results": "94", "hashOfConfig": "78"}, {"size": 12594, "mtime": 1755520828797, "results": "95", "hashOfConfig": "78"}, {"size": 20576, "mtime": 1755523135141, "results": "96", "hashOfConfig": "78"}, {"size": 34813, "mtime": 1755571791179, "results": "97", "hashOfConfig": "78"}, {"size": 15203, "mtime": 1755557741610, "results": "98", "hashOfConfig": "78"}, {"size": 27134, "mtime": 1755562150437, "results": "99", "hashOfConfig": "78"}, {"size": 13085, "mtime": 1755520668101, "results": "100", "hashOfConfig": "78"}, {"size": 17716, "mtime": 1755557839009, "results": "101", "hashOfConfig": "78"}, {"size": 17612, "mtime": 1755562223558, "results": "102", "hashOfConfig": "78"}, {"size": 25043, "mtime": 1755561862802, "results": "103", "hashOfConfig": "78"}, {"size": 29223, "mtime": 1755580736326, "results": "104", "hashOfConfig": "78"}, {"size": 28044, "mtime": 1755562324181, "results": "105", "hashOfConfig": "78"}, {"size": 17016, "mtime": 1755580759818, "results": "106", "hashOfConfig": "78"}, {"size": 23346, "mtime": 1755522967724, "results": "107", "hashOfConfig": "78"}, {"size": 19158, "mtime": 1755522887496, "results": "108", "hashOfConfig": "78"}, {"size": 16947, "mtime": 1755574373580, "results": "109", "hashOfConfig": "78"}, {"size": 23457, "mtime": 1755566270710, "results": "110", "hashOfConfig": "78"}, {"size": 19479, "mtime": 1755574804133, "results": "111", "hashOfConfig": "78"}, {"size": 20258, "mtime": 1755575685171, "results": "112", "hashOfConfig": "78"}, {"size": 20345, "mtime": 1755575798570, "results": "113", "hashOfConfig": "78"}, {"size": 16420, "mtime": 1755564532370, "results": "114", "hashOfConfig": "78"}, {"size": 24769, "mtime": 1755575576802, "results": "115", "hashOfConfig": "78"}, {"size": 9478, "mtime": 1755558105594, "results": "116", "hashOfConfig": "78"}, {"size": 1258, "mtime": 1755524203787, "results": "117", "hashOfConfig": "78"}, {"size": 24719, "mtime": 1755574967863, "results": "118", "hashOfConfig": "78"}, {"size": 7005, "mtime": 1755586779650, "results": "119", "hashOfConfig": "78"}, {"size": 15653, "mtime": 1755562269601, "results": "120", "hashOfConfig": "78"}, {"size": 1979, "mtime": 1755519494522, "results": "121", "hashOfConfig": "78"}, {"size": 11377, "mtime": 1755557630337, "results": "122", "hashOfConfig": "78"}, {"size": 14180, "mtime": 1755564589861, "results": "123", "hashOfConfig": "78"}, {"size": 2055, "mtime": 1755557657795, "results": "124", "hashOfConfig": "78"}, {"size": 9712, "mtime": 1755586828007, "results": "125", "hashOfConfig": "78"}, {"size": 1699, "mtime": 1755557454415, "results": "126", "hashOfConfig": "78"}, {"size": 8344, "mtime": 1755600158373, "results": "127", "hashOfConfig": "78"}, {"size": 4305, "mtime": 1755522368618, "results": "128", "hashOfConfig": "78"}, {"size": 5046, "mtime": 1755522780403, "results": "129", "hashOfConfig": "78"}, {"size": 3286, "mtime": 1755524346737, "results": "130", "hashOfConfig": "78"}, {"size": 750, "mtime": 1755519439483, "results": "131", "hashOfConfig": "78"}, {"size": 5785, "mtime": 1755523200785, "results": "132", "hashOfConfig": "78"}, {"size": 9392, "mtime": 1755558000151, "results": "133", "hashOfConfig": "78"}, {"size": 5254, "mtime": 1755519512471, "results": "134", "hashOfConfig": "78"}, {"size": 863, "mtime": 1755519453612, "results": "135", "hashOfConfig": "78"}, {"size": 2495, "mtime": 1755520540514, "results": "136", "hashOfConfig": "78"}, {"size": 7297, "mtime": 1755656328547, "results": "137", "hashOfConfig": "78"}, {"size": 11688, "mtime": 1755519525516, "results": "138", "hashOfConfig": "78"}, {"size": 5356, "mtime": 1755524378863, "results": "139", "hashOfConfig": "78"}, {"size": 5943, "mtime": 1755524179452, "results": "140", "hashOfConfig": "78"}, {"size": 18216, "mtime": 1755590155728, "results": "141", "hashOfConfig": "78"}, {"size": 25123, "mtime": 1755584942836, "results": "142", "hashOfConfig": "78"}, {"size": 22873, "mtime": 1755596815082, "results": "143", "hashOfConfig": "78"}, {"size": 26860, "mtime": 1755585650333, "results": "144", "hashOfConfig": "78"}, {"size": 25963, "mtime": 1755585992648, "results": "145", "hashOfConfig": "78"}, {"size": 24621, "mtime": 1755593153838, "results": "146", "hashOfConfig": "78"}, {"size": 19519, "mtime": 1755656370953, "results": "147", "hashOfConfig": "78"}, {"size": 351, "mtime": 1755596741541, "results": "148", "hashOfConfig": "78"}, {"size": 6702, "mtime": 1755656248440, "results": "149", "hashOfConfig": "78"}, {"size": 3071, "mtime": 1755600088021, "results": "150", "hashOfConfig": "78"}, {"size": 240, "mtime": 1755600517591, "results": "151", "hashOfConfig": "78"}, {"size": 1119, "mtime": 1755600470587, "results": "152", "hashOfConfig": "78"}, {"size": 4483, "mtime": 1755657490791, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jl5yua", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\index.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\App.tsx", ["382"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\hooks\\redux.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\authSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMListPage.tsx", ["383", "384", "385", "386", "387", "388", "389", "390", "391", "392"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMEditPage.tsx", ["393", "394", "395", "396", "397"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMViewPage.tsx", ["398"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMCreatePage.tsx", ["399", "400", "401", "402", "403"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\auth\\LoginPage.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMViewPage.tsx", ["404", "405"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\dashboard\\DashboardPage.tsx", ["406", "407", "408", "409", "410", "411", "412", "413", "414"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMDerivePage.tsx", ["415", "416", "417", "418", "419"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMCreatePage.tsx", ["420", "421", "422", "423", "424", "425", "426", "427"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMListPage.tsx", ["428", "429", "430", "431", "432", "433", "434", "435"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialEditPage.tsx", ["436"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialListPage.tsx", ["437", "438", "439", "440", "441", "442"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialCreatePage.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseListPage.tsx", ["443", "444", "445", "446"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\MRPCalculationPage.tsx", ["447", "448", "449", "450", "451", "452", "453", "454", "455", "456", "457", "458"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseRequisitionPage.tsx", ["459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471", "472", "473", "474"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryReceivePage.tsx", ["475", "476", "477", "478", "479", "480", "481"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryAdjustPage.tsx", ["482", "483", "484", "485"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryListPage.tsx", ["486", "487", "488", "489", "490", "491"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryIssuePage.tsx", ["492", "493", "494", "495", "496"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\RemnantListPage.tsx", ["497", "498", "499", "500", "501", "502", "503", "504"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\MaintenancePage.tsx", ["505", "506", "507"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\DeviceArchivePage.tsx", ["508", "509", "510", "511", "512", "513"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\ServiceBOMListPage.tsx", ["514", "515", "516", "517", "518", "519"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\CostReportsPage.tsx", ["520", "521", "522"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\WasteTrackingPage.tsx", ["523", "524", "525", "526", "527"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\CostAnalysisPage.tsx", ["528", "529", "530", "531", "532", "533"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNReviewPage.tsx", ["534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNCreatePage.tsx", ["559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNListPage.tsx", ["574", "575", "576", "577", "578", "579", "580", "581"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\RoleListPage.tsx", ["582", "583", "584", "585", "586", "587", "588"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\PermissionListPage.tsx", ["589", "590", "591", "592", "593", "594", "595", "596", "597", "598"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\SystemConfigPage.tsx", ["599", "600", "601", "602", "603", "604", "605", "606", "607", "608"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\UserListPage.tsx", ["609", "610", "611", "612", "613", "614", "615"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\bomSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\index.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobileInventoryPage.tsx", ["616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\constants\\index.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\reports\\ReportsPage.tsx", ["627", "628", "629", "630", "631", "632"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\authService.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobilePage.tsx", ["633", "634", "635", "636"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\reports\\DashboardConfigPage.tsx", ["637", "638", "639", "640"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobileScanPage.tsx", ["641"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\layout\\MainLayout.tsx", ["642"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\index.ts", ["643", "644", "645"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\costSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\serviceSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\systemSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\ecnSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\purchaseSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\inventorySlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\bomService.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\dashboardSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\materialSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\api.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\mockService.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\reportSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\mobileSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseOptimizationPage.tsx", ["646", "647", "648", "649", "650", "651"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\CuttingPlanPage.tsx", ["652", "653", "654", "655"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\StandardCostPage.tsx", ["656"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\AsBuiltBOMPage.tsx", ["657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\SparePartsPage.tsx", ["669", "670", "671", "672", "673", "674", "675", "676", "677"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\BatchTrackingPage.tsx", ["678", "679", "680", "681"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\AuditLogPage.tsx", ["682", "683"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\format.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\errorHandler.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\ErrorBoundary.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\index.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\ConfirmDialog.tsx", [], [], {"ruleId": "684", "severity": 1, "message": "685", "line": 90, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 90, "endColumn": 26}, {"ruleId": "684", "severity": 1, "message": "688", "line": 1, "column": 38, "nodeType": "686", "messageId": "687", "endLine": 1, "endColumn": 45}, {"ruleId": "684", "severity": 1, "message": "689", "line": 31, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 31, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "690", "line": 43, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 43, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "691", "line": 55, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 55, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "692", "line": 64, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 64, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "693", "line": 69, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 69, "endColumn": 20}, {"ruleId": "694", "severity": 1, "message": "695", "line": 90, "column": 6, "nodeType": "696", "endLine": 90, "endColumn": 76, "suggestions": "697"}, {"ruleId": "694", "severity": 1, "message": "698", "line": 98, "column": 6, "nodeType": "696", "endLine": 98, "endColumn": 72, "suggestions": "699"}, {"ruleId": "684", "severity": 1, "message": "700", "line": 194, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 194, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "700", "line": 247, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 247, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "701", "line": 35, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 35, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "691", "line": 36, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 36, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "702", "line": 37, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "703", "line": 37, "column": 24, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 33}, {"ruleId": "684", "severity": 1, "message": "700", "line": 98, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 98, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "704", "line": 436, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 436, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "705", "line": 21, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 21, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "706", "line": 22, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 22, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "689", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "707", "line": 32, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 32, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "708", "line": 667, "column": 29, "nodeType": "686", "messageId": "687", "endLine": 667, "endColumn": 33}, {"ruleId": "684", "severity": 1, "message": "709", "line": 16, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 16, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "710", "line": 48, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 48, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "711", "line": 10, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 10, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "712", "line": 15, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 15, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "713", "line": 17, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 17, "endColumn": 7}, {"ruleId": "684", "severity": 1, "message": "714", "line": 20, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 20, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "715", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "716", "line": 24, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 24, "endColumn": 16}, {"ruleId": "684", "severity": 1, "message": "717", "line": 28, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 28, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "718", "line": 32, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 32, "endColumn": 12}, {"ruleId": "684", "severity": 1, "message": "719", "line": 33, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 33, "endColumn": 7}, {"ruleId": "684", "severity": 1, "message": "720", "line": 17, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 17, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "721", "line": 20, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 20, "endColumn": 6}, {"ruleId": "684", "severity": 1, "message": "722", "line": 27, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 27, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "723", "line": 28, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 28, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "724", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "709", "line": 21, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 21, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "725", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "726", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "727", "line": 31, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 31, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "728", "line": 37, "column": 18, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 37}, {"ruleId": "684", "severity": 1, "message": "691", "line": 38, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 38, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "702", "line": 39, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 39, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "703", "line": 39, "column": 24, "nodeType": "686", "messageId": "687", "endLine": 39, "endColumn": 33}, {"ruleId": "684", "severity": 1, "message": "706", "line": 20, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 20, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "689", "line": 27, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 27, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "729", "line": 39, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 39, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "691", "line": 47, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 47, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "730", "line": 56, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 56, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "731", "line": 65, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 65, "endColumn": 22}, {"ruleId": "694", "severity": 1, "message": "695", "line": 70, "column": 6, "nodeType": "696", "endLine": 70, "endColumn": 92, "suggestions": "732"}, {"ruleId": "684", "severity": 1, "message": "700", "line": 230, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 230, "endColumn": 19}, {"ruleId": "694", "severity": 1, "message": "733", "line": 64, "column": 6, "nodeType": "696", "endLine": 64, "endColumn": 10, "suggestions": "734"}, {"ruleId": "684", "severity": 1, "message": "689", "line": 32, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 32, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "735", "line": 41, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 41, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "690", "line": 44, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 44, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "736", "line": 64, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 64, "endColumn": 20}, {"ruleId": "694", "severity": 1, "message": "695", "line": 90, "column": 6, "nodeType": "696", "endLine": 90, "endColumn": 94, "suggestions": "737"}, {"ruleId": "684", "severity": 1, "message": "700", "line": 150, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 150, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "738", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "739", "line": 32, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 32, "endColumn": 28}, {"ruleId": "684", "severity": 1, "message": "740", "line": 49, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 49, "endColumn": 25}, {"ruleId": "694", "severity": 1, "message": "741", "line": 59, "column": 6, "nodeType": "696", "endLine": 59, "endColumn": 51, "suggestions": "742"}, {"ruleId": "684", "severity": 1, "message": "712", "line": 8, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 8, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "706", "line": 18, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 18, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "709", "line": 19, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 19, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "743", "line": 27, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 27, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "744", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "745", "line": 32, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 32, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "746", "line": 44, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 44, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "747", "line": 65, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 65, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "748", "line": 65, "column": 23, "nodeType": "686", "messageId": "687", "endLine": 65, "endColumn": 30}, {"ruleId": "684", "severity": 1, "message": "749", "line": 71, "column": 26, "nodeType": "686", "messageId": "687", "endLine": 71, "endColumn": 43}, {"ruleId": "684", "severity": 1, "message": "750", "line": 73, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 73, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "751", "line": 73, "column": 23, "nodeType": "686", "messageId": "687", "endLine": 73, "endColumn": 37}, {"ruleId": "684", "severity": 1, "message": "705", "line": 24, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 24, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "706", "line": 25, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 25, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "752", "line": 26, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 26, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "753", "line": 28, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 28, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "711", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "720", "line": 31, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 31, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "689", "line": 38, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 38, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "754", "line": 41, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 41, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "745", "line": 43, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 43, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "729", "line": 45, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 45, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "715", "line": 46, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 46, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "755", "line": 47, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 47, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "725", "line": 49, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 49, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "756", "line": 50, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 50, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "757", "line": 57, "column": 22, "nodeType": "686", "messageId": "687", "endLine": 57, "endColumn": 31}, {"ruleId": "684", "severity": 1, "message": "758", "line": 64, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 64, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "759", "line": 15, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 15, "endColumn": 14}, {"ruleId": "684", "severity": 1, "message": "752", "line": 19, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 19, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "745", "line": 28, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 28, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "760", "line": 41, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 41, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "761", "line": 45, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 45, "endColumn": 28}, {"ruleId": "694", "severity": 1, "message": "741", "line": 57, "column": 6, "nodeType": "696", "endLine": 57, "endColumn": 35, "suggestions": "762"}, {"ruleId": "684", "severity": 1, "message": "700", "line": 88, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 88, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "706", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "763", "line": 38, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 38, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "700", "line": 102, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 102, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "700", "line": 119, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 119, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "721", "line": 10, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 10, "endColumn": 6}, {"ruleId": "684", "severity": 1, "message": "689", "line": 21, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 21, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "764", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "765", "line": 35, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 35, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "766", "line": 45, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 45, "endColumn": 20}, {"ruleId": "694", "severity": 1, "message": "741", "line": 54, "column": 6, "nodeType": "696", "endLine": 54, "endColumn": 73, "suggestions": "767"}, {"ruleId": "684", "severity": 1, "message": "759", "line": 15, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 15, "endColumn": 14}, {"ruleId": "684", "severity": 1, "message": "763", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "768", "line": 44, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 44, "endColumn": 26}, {"ruleId": "694", "severity": 1, "message": "741", "line": 56, "column": 6, "nodeType": "696", "endLine": 56, "endColumn": 35, "suggestions": "769"}, {"ruleId": "684", "severity": 1, "message": "700", "line": 87, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 87, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "770", "line": 22, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 22, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "771", "line": 26, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 26, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "763", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "723", "line": 35, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 35, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "745", "line": 36, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 36, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "772", "line": 38, "column": 8, "nodeType": "686", "messageId": "687", "endLine": 38, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "773", "line": 51, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 51, "endColumn": 19}, {"ruleId": "694", "severity": 1, "message": "741", "line": 64, "column": 6, "nodeType": "696", "endLine": 64, "endColumn": 55, "suggestions": "774"}, {"ruleId": "684", "severity": 1, "message": "775", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "763", "line": 39, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 39, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "700", "line": 99, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 99, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "690", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "689", "line": 37, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "776", "line": 40, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 40, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "777", "line": 55, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 55, "endColumn": 25}, {"ruleId": "694", "severity": 1, "message": "741", "line": 71, "column": 6, "nodeType": "696", "endLine": 71, "endColumn": 35, "suggestions": "778"}, {"ruleId": "684", "severity": 1, "message": "700", "line": 108, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 108, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "779", "line": 33, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 33, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "780", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "745", "line": 40, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 40, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "763", "line": 41, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 41, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "758", "line": 51, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 51, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "700", "line": 94, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 94, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "781", "line": 33, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 33, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "782", "line": 48, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 48, "endColumn": 22}, {"ruleId": "694", "severity": 1, "message": "741", "line": 62, "column": 6, "nodeType": "696", "endLine": 62, "endColumn": 29, "suggestions": "783"}, {"ruleId": "684", "severity": 1, "message": "784", "line": 22, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 22, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "763", "line": 33, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 33, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "785", "line": 65, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 65, "endColumn": 24}, {"ruleId": "694", "severity": 1, "message": "741", "line": 79, "column": 6, "nodeType": "696", "endLine": 79, "endColumn": 28, "suggestions": "786"}, {"ruleId": "684", "severity": 1, "message": "700", "line": 119, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 119, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "784", "line": 18, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 18, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "787", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "701", "line": 52, "column": 26, "nodeType": "686", "messageId": "687", "endLine": 52, "endColumn": 36}, {"ruleId": "684", "severity": 1, "message": "788", "line": 60, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 60, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "748", "line": 60, "column": 25, "nodeType": "686", "messageId": "687", "endLine": 60, "endColumn": 32}, {"ruleId": "694", "severity": 1, "message": "741", "line": 71, "column": 6, "nodeType": "696", "endLine": 71, "endColumn": 32, "suggestions": "789"}, {"ruleId": "684", "severity": 1, "message": "790", "line": 11, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 11, "endColumn": 6}, {"ruleId": "684", "severity": 1, "message": "791", "line": 12, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 12, "endColumn": 6}, {"ruleId": "684", "severity": 1, "message": "792", "line": 18, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 18, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "784", "line": 19, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 19, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "753", "line": 21, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 21, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "793", "line": 24, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 24, "endColumn": 7}, {"ruleId": "684", "severity": 1, "message": "705", "line": 26, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 26, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "709", "line": 27, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 27, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "794", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "771", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "795", "line": 35, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 35, "endColumn": 14}, {"ruleId": "684", "severity": 1, "message": "776", "line": 36, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 36, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "739", "line": 37, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 28}, {"ruleId": "684", "severity": 1, "message": "707", "line": 41, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 41, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "690", "line": 42, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 42, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "796", "line": 44, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 44, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "755", "line": 45, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 45, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "701", "line": 49, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 49, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "797", "line": 50, "column": 15, "nodeType": "686", "messageId": "687", "endLine": 50, "endColumn": 27}, {"ruleId": "684", "severity": 1, "message": "760", "line": 55, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 55, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "708", "line": 60, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 60, "endColumn": 14}, {"ruleId": "684", "severity": 1, "message": "798", "line": 68, "column": 27, "nodeType": "686", "messageId": "687", "endLine": 68, "endColumn": 45}, {"ruleId": "684", "severity": 1, "message": "799", "line": 69, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 69, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "800", "line": 69, "column": 21, "nodeType": "686", "messageId": "687", "endLine": 69, "endColumn": 33}, {"ruleId": "694", "severity": 1, "message": "801", "line": 74, "column": 6, "nodeType": "696", "endLine": 74, "endColumn": 10, "suggestions": "802"}, {"ruleId": "684", "severity": 1, "message": "720", "line": 20, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 20, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "705", "line": 22, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 22, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "709", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "753", "line": 25, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 25, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "771", "line": 33, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 33, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "795", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 14}, {"ruleId": "684", "severity": 1, "message": "776", "line": 35, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 35, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "723", "line": 37, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "745", "line": 38, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 38, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "690", "line": 39, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 39, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "729", "line": 40, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 40, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "701", "line": 44, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 44, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "803", "line": 45, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 45, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "760", "line": 50, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 50, "endColumn": 13}, {"ruleId": "694", "severity": 1, "message": "804", "line": 75, "column": 6, "nodeType": "696", "endLine": 75, "endColumn": 8, "suggestions": "805"}, {"ruleId": "684", "severity": 1, "message": "806", "line": 13, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 13, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "807", "line": 14, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 14, "endColumn": 7}, {"ruleId": "684", "severity": 1, "message": "784", "line": 27, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 27, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "689", "line": 31, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 31, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "756", "line": 38, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 38, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "738", "line": 45, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 45, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "808", "line": 53, "column": 16, "nodeType": "686", "messageId": "687", "endLine": 53, "endColumn": 20}, {"ruleId": "694", "severity": 1, "message": "809", "line": 76, "column": 6, "nodeType": "696", "endLine": 76, "endColumn": 103, "suggestions": "810"}, {"ruleId": "684", "severity": 1, "message": "784", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "689", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "811", "line": 42, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 42, "endColumn": 16}, {"ruleId": "684", "severity": 1, "message": "812", "line": 42, "column": 18, "nodeType": "686", "messageId": "687", "endLine": 42, "endColumn": 29}, {"ruleId": "684", "severity": 1, "message": "701", "line": 43, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 43, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "758", "line": 57, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 57, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "700", "line": 181, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 181, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "813", "line": 21, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 21, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "752", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "689", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "814", "line": 36, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 36, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "815", "line": 37, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "811", "line": 41, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 41, "endColumn": 16}, {"ruleId": "684", "severity": 1, "message": "812", "line": 41, "column": 18, "nodeType": "686", "messageId": "687", "endLine": 41, "endColumn": 29}, {"ruleId": "684", "severity": 1, "message": "701", "line": 42, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 42, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "758", "line": 50, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 50, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "700", "line": 221, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 221, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "792", "line": 15, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 15, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "784", "line": 16, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 16, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "705", "line": 17, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 17, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "690", "line": 27, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 27, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "707", "line": 28, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 28, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "816", "line": 32, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 32, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "817", "line": 33, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 33, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "818", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "819", "line": 49, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 49, "endColumn": 23}, {"ruleId": "694", "severity": 1, "message": "741", "line": 59, "column": 6, "nodeType": "696", "endLine": 59, "endColumn": 8, "suggestions": "820"}, {"ruleId": "684", "severity": 1, "message": "770", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "784", "line": 26, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 26, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "689", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "811", "line": 44, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 44, "endColumn": 16}, {"ruleId": "684", "severity": 1, "message": "821", "line": 44, "column": 18, "nodeType": "686", "messageId": "687", "endLine": 44, "endColumn": 28}, {"ruleId": "684", "severity": 1, "message": "758", "line": 52, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 52, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "700", "line": 190, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 190, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "752", "line": 10, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 10, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "792", "line": 24, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 24, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "813", "line": 27, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 27, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "822", "line": 28, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 28, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "709", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "689", "line": 33, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 33, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "796", "line": 37, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "715", "line": 44, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 44, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "745", "line": 47, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 47, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "795", "line": 48, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 48, "endColumn": 14}, {"ruleId": "684", "severity": 1, "message": "823", "line": 56, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 56, "endColumn": 14}, {"ruleId": "684", "severity": 1, "message": "824", "line": 1, "column": 27, "nodeType": "686", "messageId": "687", "endLine": 1, "endColumn": 36}, {"ruleId": "684", "severity": 1, "message": "706", "line": 17, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 17, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "771", "line": 33, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 33, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "825", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "745", "line": 36, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 36, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "739", "line": 38, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 38, "endColumn": 28}, {"ruleId": "684", "severity": 1, "message": "792", "line": 15, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 15, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "701", "line": 35, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 35, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "826", "line": 42, "column": 11, "nodeType": "686", "messageId": "687", "endLine": 42, "endColumn": 24}, {"ruleId": "684", "severity": 1, "message": "748", "line": 42, "column": 26, "nodeType": "686", "messageId": "687", "endLine": 42, "endColumn": 33}, {"ruleId": "684", "severity": 1, "message": "784", "line": 20, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 20, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "827", "line": 28, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 28, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "814", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 18}, {"ruleId": "694", "severity": 1, "message": "741", "line": 115, "column": 6, "nodeType": "696", "endLine": 115, "endColumn": 8, "suggestions": "828"}, {"ruleId": "684", "severity": 1, "message": "808", "line": 6, "column": 16, "nodeType": "686", "messageId": "687", "endLine": 6, "endColumn": 20}, {"ruleId": "684", "severity": 1, "message": "821", "line": 36, "column": 18, "nodeType": "686", "messageId": "687", "endLine": 36, "endColumn": 28}, {"ruleId": "684", "severity": 1, "message": "829", "line": 2, "column": 24, "nodeType": "686", "messageId": "687", "endLine": 2, "endColumn": 38}, {"ruleId": "830", "severity": 1, "message": "831", "line": 262, "column": 34, "nodeType": "832", "messageId": "833", "endLine": 262, "endColumn": 35}, {"ruleId": "830", "severity": 1, "message": "831", "line": 262, "column": 40, "nodeType": "832", "messageId": "833", "endLine": 262, "endColumn": 41}, {"ruleId": "684", "severity": 1, "message": "834", "line": 12, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 12, "endColumn": 8}, {"ruleId": "684", "severity": 1, "message": "722", "line": 27, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 27, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "763", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "723", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "701", "line": 34, "column": 26, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 36}, {"ruleId": "684", "severity": 1, "message": "700", "line": 86, "column": 13, "nodeType": "686", "messageId": "687", "endLine": 86, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "780", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "835", "line": 37, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "723", "line": 38, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 38, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "836", "line": 40, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 40, "endColumn": 24}, {"ruleId": "684", "severity": 1, "message": "722", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 21}, {"ruleId": "684", "severity": 1, "message": "824", "line": 1, "column": 27, "nodeType": "686", "messageId": "687", "endLine": 1, "endColumn": 36}, {"ruleId": "684", "severity": 1, "message": "705", "line": 24, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 24, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "770", "line": 25, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 25, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "689", "line": 29, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 29, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "825", "line": 35, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 35, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "796", "line": 39, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 39, "endColumn": 18}, {"ruleId": "684", "severity": 1, "message": "717", "line": 40, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 40, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "776", "line": 41, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 41, "endColumn": 19}, {"ruleId": "684", "severity": 1, "message": "837", "line": 42, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 42, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "838", "line": 48, "column": 9, "nodeType": "686", "messageId": "687", "endLine": 48, "endColumn": 15}, {"ruleId": "684", "severity": 1, "message": "839", "line": 114, "column": 19, "nodeType": "686", "messageId": "687", "endLine": 114, "endColumn": 29}, {"ruleId": "684", "severity": 1, "message": "840", "line": 115, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 115, "endColumn": 23}, {"ruleId": "684", "severity": 1, "message": "706", "line": 17, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 17, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "784", "line": 20, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 20, "endColumn": 10}, {"ruleId": "684", "severity": 1, "message": "770", "line": 22, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 22, "endColumn": 13}, {"ruleId": "684", "severity": 1, "message": "705", "line": 23, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 23, "endColumn": 9}, {"ruleId": "684", "severity": 1, "message": "689", "line": 30, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 30, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "723", "line": 34, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 34, "endColumn": 22}, {"ruleId": "684", "severity": 1, "message": "690", "line": 37, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 37, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "840", "line": 94, "column": 10, "nodeType": "686", "messageId": "687", "endLine": 94, "endColumn": 23}, {"ruleId": "694", "severity": 1, "message": "741", "line": 218, "column": 6, "nodeType": "696", "endLine": 218, "endColumn": 8, "suggestions": "841"}, {"ruleId": "684", "severity": 1, "message": "807", "line": 11, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 11, "endColumn": 7}, {"ruleId": "684", "severity": 1, "message": "689", "line": 26, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 26, "endColumn": 17}, {"ruleId": "684", "severity": 1, "message": "796", "line": 28, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 28, "endColumn": 18}, {"ruleId": "694", "severity": 1, "message": "842", "line": 262, "column": 6, "nodeType": "696", "endLine": 262, "endColumn": 8, "suggestions": "843"}, {"ruleId": "684", "severity": 1, "message": "706", "line": 20, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 20, "endColumn": 11}, {"ruleId": "684", "severity": 1, "message": "689", "line": 24, "column": 3, "nodeType": "686", "messageId": "687", "endLine": 24, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'isAuthenticated' is assigned a value but never used.", "Identifier", "unusedVar", "'useMemo' is defined but never used.", "'SearchOutlined' is defined but never used.", "'UploadOutlined' is defined but never used.", "'ConfirmDialog' is defined but never used.", "'coreBOMs' is assigned a value but never used.", "'copyingBOM' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["844"], "React Hook useCallback has a missing dependency: 'pagination'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["845"], "'values' is assigned a value but never used.", "'formatDate' is defined but never used.", "'errorHandler' is defined but never used.", "'ErrorType' is defined but never used.", "'pendingItems' is assigned a value but never used.", "'Upload' is defined but never used.", "'Progress' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'form' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'currentBOM' is assigned a value but never used.", "'Avatar' is defined but never used.", "'Select' is defined but never used.", "'Tabs' is defined but never used.", "'ArrowUpOutlined' is defined but never used.", "'ShoppingCartOutlined' is defined but never used.", "'InboxOutlined' is defined but never used.", "'ToolOutlined' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Checkbox' is defined but never used.", "'Tag' is defined but never used.", "'CalculatorOutlined' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'InfoCircleOutlined' is defined but never used.", "'CopyOutlined' is defined but never used.", "'UpOutlined' is defined but never used.", "'DownOutlined' is defined but never used.", "'MATERIAL_CATEGORIES' is defined but never used.", "'UserOutlined' is defined but never used.", "'orderBOMs' is assigned a value but never used.", "'copyingOrder' is assigned a value but never used.", ["846"], "React Hook useEffect has a missing dependency: 'loadMaterialData'. Either include it or remove the dependency array.", ["847"], "'DollarOutlined' is defined but never used.", "'materials' is assigned a value but never used.", ["848"], "'CalendarOutlined' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'purchaseOrders' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["849"], "'PlayCircleOutlined' is defined but never used.", "'ExportOutlined' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'Legend' is defined but never used.", "'mrpResults' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'setSelectedOrders' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'Badge' is defined but never used.", "'Timeline' is defined but never used.", "'FileExcelOutlined' is defined but never used.", "'SendOutlined' is defined but never used.", "'FilterOutlined' is defined but never used.", "'Paragraph' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'InputNumber' is defined but never used.", "'Step' is assigned a value but never used.", "'inventoryReceives' is assigned a value but never used.", ["850"], "'WarningOutlined' is defined but never used.", "'ImportOutlined' is defined but never used.", "'Inventory' is defined but never used.", "'inventory' is assigned a value but never used.", ["851"], "'inventoryIssues' is assigned a value but never used.", ["852"], "'DatePicker' is defined but never used.", "'EditOutlined' is defined but never used.", "'dayjs' is defined but never used.", "'remnants' is assigned a value but never used.", ["853"], "'FileImageOutlined' is defined but never used.", "'FileTextOutlined' is defined but never used.", "'deviceArchives' is assigned a value but never used.", ["854"], "'ApartmentOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'RiseOutlined' is defined but never used.", "'costReports' is assigned a value but never used.", ["855"], "'Divider' is defined but never used.", "'wasteTracking' is assigned a value but never used.", ["856"], "'FallOutlined' is defined but never used.", "'costAnalysis' is assigned a value but never used.", ["857"], "'Row' is defined but never used.", "'Col' is defined but never used.", "'Alert' is defined but never used.", "'Rate' is defined but never used.", "'Popconfirm' is defined but never used.", "'EyeOutlined' is defined but never used.", "'HistoryOutlined' is defined but never used.", "'AffectedItem' is defined but never used.", "'setCurrentUserRole' is assigned a value but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadECNData'. Either include it or remove the dependency array.", ["858"], "'ECN' is defined but never used.", "React Hook useEffect has a missing dependency: 'form'. Either include it or remove the dependency array.", ["859"], "'Modal' is defined but never used.", "'Form' is defined but never used.", "'Text' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'pagination.current'. Either exclude it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["860"], "'ROUTES' is defined but never used.", "'PERMISSIONS' is defined but never used.", "'Switch' is defined but never used.", "'SettingOutlined' is defined but never used.", "'SecurityScanOutlined' is defined but never used.", "'MailOutlined' is defined but never used.", "'BellOutlined' is defined but never used.", "'GlobalOutlined' is defined but never used.", "'systemConfig' is assigned a value but never used.", ["861"], "'USER_ROLES' is defined but never used.", "'Radio' is defined but never used.", "'Title' is assigned a value but never used.", "'useEffect' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'workbenchData' is assigned a value but never used.", "'DragOutlined' is defined but never used.", ["862"], "'NUMBER_FORMATS' is defined but never used.", "no-mixed-operators", "Unexpected mix of '&' and '|'. Use parentheses to clarify the intended order of operations.", "BinaryExpression", "unexpectedMixedOperator", "'Input' is defined but never used.", "'PauseCircleOutlined' is defined but never used.", "'formatCurrency' is defined but never used.", "'CloudUploadOutlined' is defined but never used.", "'Option' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'searchKeyword' is assigned a value but never used.", ["863"], "React Hook useEffect has missing dependencies: 'loadBatchData' and 'loadStatistics'. Either include them or remove the dependency array.", ["864"], {"desc": "865", "fix": "866"}, {"desc": "867", "fix": "868"}, {"desc": "869", "fix": "870"}, {"desc": "871", "fix": "872"}, {"desc": "873", "fix": "874"}, {"desc": "875", "fix": "876"}, {"desc": "877", "fix": "878"}, {"desc": "879", "fix": "880"}, {"desc": "877", "fix": "881"}, {"desc": "882", "fix": "883"}, {"desc": "877", "fix": "884"}, {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, {"desc": "889", "fix": "890"}, {"desc": "891", "fix": "892"}, {"desc": "893", "fix": "894"}, {"desc": "895", "fix": "896"}, {"desc": "897", "fix": "898"}, {"desc": "897", "fix": "899"}, {"desc": "897", "fix": "900"}, {"desc": "901", "fix": "902"}, "Update the dependencies array to be: [loadData, pagination.pageSize, searchKeyword, statusFilter]", {"range": "903", "text": "904"}, "Update the dependencies array to be: [dispatch, pagination, searchKeyword]", {"range": "905", "text": "906"}, "Update the dependencies array to be: [pagination.pageSize, searchKeyword, statusFilter, customerFilter, loadData]", {"range": "907", "text": "908"}, "Update the dependencies array to be: [id, loadMaterialData]", {"range": "909", "text": "910"}, "Update the dependencies array to be: [pagination.pageSize, searchKeyword, categoryFilter, supplierFilter, loadData]", {"range": "911", "text": "912"}, "Update the dependencies array to be: [loadData, searchKeyword, statusFilter, supplierFilter]", {"range": "913", "text": "914"}, "Update the dependencies array to be: [loadData, searchKeyword, statusFilter]", {"range": "915", "text": "916"}, "Update the dependencies array to be: [searchKeyword, categoryFilter, warehouseFilter, stockStatusFilter, loadData]", {"range": "917", "text": "918"}, {"range": "919", "text": "916"}, "Update the dependencies array to be: [searchKeyword, statusFilter, materialTypeFilter, loadData]", {"range": "920", "text": "921"}, {"range": "922", "text": "916"}, "Update the dependencies array to be: [reportType, dateRange, loadData]", {"range": "923", "text": "924"}, "Update the dependencies array to be: [dateRange, loadData, wasteType]", {"range": "925", "text": "926"}, "Update the dependencies array to be: [dateRange, loadData, selectedOrder]", {"range": "927", "text": "928"}, "Update the dependencies array to be: [id, loadECNData]", {"range": "929", "text": "930"}, "Update the dependencies array to be: [form]", {"range": "931", "text": "932"}, "Update the dependencies array to be: [pagination.pageSize, searchKeyword, statusFilter, priorityFilter, dateRange]", {"range": "933", "text": "934"}, "Update the dependencies array to be: [loadData]", {"range": "935", "text": "936"}, {"range": "937", "text": "936"}, {"range": "938", "text": "936"}, "Update the dependencies array to be: [loadBatchData, loadStatistics]", {"range": "939", "text": "940"}, [2679, 2749], "[loadData, pagination.pageSize, searchKeyword, statusFilter]", [2934, 3000], "[dispatch, pagination, searchKeyword]", [2020, 2106], "[pagination.pageSize, searchKeyword, statusFilter, customerFilter, loadData]", [1465, 1469], "[id, loadMaterialData]", [2638, 2726], "[pagination.pageSize, searchKeyword, categoryFilter, supplierFilter, loadData]", [1442, 1487], "[loadData, searchKeyword, statusFilter, supplierFilter]", [1423, 1452], "[loadData, searchKeyword, statusFilter]", [1325, 1392], "[searchKeyword, categoryFilter, warehouseFilter, stockStatusFilter, loadData]", [1394, 1423], [1600, 1649], "[searchKeyword, statusFilter, materialTypeFilter, loadData]", [1927, 1956], [1470, 1493], "[reportType, dateRange, loadData]", [1675, 1697], "[dateRange, loadData, wasteType]", [1437, 1463], "[date<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, selected<PERSON>rder]", [1714, 1718], "[id, loadECNData]", [1850, 1852], "[form]", [1650, 1747], "[pagination.pageSize, searchKeyword, statusFilter, priorityFilter, dateRange]", [1252, 1254], "[loadData]", [2429, 2431], [5255, 5257], [6631, 6633], "[loadBatchData, loadStatistics]"]