{"ast": null, "code": "import React,{useState}from'react';import{Outlet,useNavigate,useLocation}from'react-router-dom';import{Layout,Menu,Avatar,Dropdown,Button,Space,Badge,Breadcrumb,theme}from'antd';import{MenuFoldOutlined,MenuUnfoldOutlined,DashboardOutlined,FileTextOutlined,ShoppingCartOutlined,InboxOutlined,DollarOutlined,ToolOutlined,SwapOutlined,Bar<PERSON><PERSON>Outlined,SettingOutlined,MobileOutlined,UserOutlined,LogoutOutlined,BellOutlined,SwitcherOutlined}from'@ant-design/icons';import{useAppDispatch,useAppSelector}from'../../hooks/redux';import{logout,switchRole}from'../../store/slices/authSlice';import{ROUTES}from'../../constants';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Head<PERSON>,Sider,Content}=Layout;// 菜单配置\nconst menuItems=[{key:ROUTES.DASHBOARD,icon:/*#__PURE__*/_jsx(DashboardOutlined,{}),label:'仪表板'},{key:'bom',icon:/*#__PURE__*/_jsx(FileTextOutlined,{}),label:'BOM管理',children:[{key:ROUTES.CORE_BOM,label:'核心BOM'},{key:ROUTES.ORDER_BOM,label:'订单BOM'}]},{key:'material',icon:/*#__PURE__*/_jsx(InboxOutlined,{}),label:'物料管理',children:[{key:ROUTES.MATERIALS,label:'物料清单'}]},{key:'inventory',icon:/*#__PURE__*/_jsx(InboxOutlined,{}),label:'库存管理',children:[{key:ROUTES.INVENTORY,label:'库存查询'},{key:ROUTES.INVENTORY_RECEIVE,label:'入库管理'},{key:ROUTES.INVENTORY_ISSUE,label:'出库管理'},{key:ROUTES.REMNANTS,label:'余料管理'},{key:ROUTES.CUTTING_PLAN,label:'切割计划'},{key:ROUTES.BATCH_TRACKING,label:'批次跟踪'}]},{key:'purchase',icon:/*#__PURE__*/_jsx(ShoppingCartOutlined,{}),label:'采购管理',children:[{key:ROUTES.PURCHASE,label:'采购订单'},{key:ROUTES.PURCHASE_REQUISITION,label:'采购申请'},{key:ROUTES.MRP_CALCULATION,label:'MRP计算'},{key:ROUTES.PURCHASE_OPTIMIZATION,label:'采购优化'}]},{key:'cost',icon:/*#__PURE__*/_jsx(DollarOutlined,{}),label:'成本管理',children:[{key:ROUTES.COST_ANALYSIS,label:'成本分析'},{key:ROUTES.COST_REPORTS,label:'成本报告'},{key:ROUTES.WASTE_TRACKING,label:'浪费跟踪'},{key:ROUTES.STANDARD_COST,label:'标准成本'}]},{key:'service',icon:/*#__PURE__*/_jsx(ToolOutlined,{}),label:'服务管理',children:[{key:ROUTES.SERVICE_BOM,label:'服务BOM'},{key:ROUTES.DEVICE_ARCHIVE,label:'设备档案'},{key:ROUTES.MAINTENANCE,label:'维护记录'},{key:ROUTES.AS_BUILT_BOM,label:'As-Built BOM'},{key:ROUTES.SPARE_PARTS,label:'备件管理'}]},{key:'ecn',icon:/*#__PURE__*/_jsx(SwapOutlined,{}),label:'ECN管理',children:[{key:ROUTES.ECN,label:'ECN列表'}]},{key:'reports',icon:/*#__PURE__*/_jsx(BarChartOutlined,{}),label:'报告分析',children:[{key:ROUTES.REPORTS,label:'报告中心'},{key:ROUTES.DASHBOARD_CONFIG,label:'仪表板配置'}]},{key:'system',icon:/*#__PURE__*/_jsx(SettingOutlined,{}),label:'系统管理',children:[{key:ROUTES.USERS,label:'用户管理'},{key:ROUTES.ROLES,label:'角色管理'},{key:ROUTES.PERMISSIONS,label:'权限管理'},{key:ROUTES.SYSTEM_CONFIG,label:'系统配置'},{key:ROUTES.AUDIT_LOG,label:'审计日志'}]},{key:'mobile',icon:/*#__PURE__*/_jsx(MobileOutlined,{}),label:'移动端',children:[{key:ROUTES.MOBILE,label:'移动首页'},{key:ROUTES.MOBILE_SCAN,label:'扫码操作'},{key:ROUTES.MOBILE_INVENTORY,label:'移动库存'}]}];const MainLayout=()=>{const[collapsed,setCollapsed]=useState(false);const navigate=useNavigate();const location=useLocation();const dispatch=useAppDispatch();const{user,currentRole}=useAppSelector(state=>state.auth);const{token:{colorBgContainer}}=theme.useToken();// 处理菜单点击\nconst handleMenuClick=_ref=>{let{key}=_ref;navigate(key);};// 处理用户菜单点击\nconst handleUserMenuClick=_ref2=>{let{key}=_ref2;switch(key){case'logout':dispatch(logout());navigate(ROUTES.LOGIN);break;case'profile':// 跳转到个人资料页面\nbreak;default:break;}};// 处理角色切换\nconst handleRoleSwitch=roleId=>{dispatch(switchRole(roleId));};// 用户下拉菜单\nconst userMenuItems=[{key:'profile',icon:/*#__PURE__*/_jsx(UserOutlined,{}),label:'个人资料'},{type:'divider'},{key:'logout',icon:/*#__PURE__*/_jsx(LogoutOutlined,{}),label:'退出登录'}];// 角色切换菜单\nconst roleMenuItems=(user===null||user===void 0?void 0:user.roles.map(role=>({key:role.id,label:role.name,onClick:()=>handleRoleSwitch(role.id)})))||[];// 生成面包屑\nconst generateBreadcrumb=()=>{const pathSnippets=location.pathname.split('/').filter(i=>i);const breadcrumbItems=[{title:'首页'}];pathSnippets.forEach((snippet,index)=>{const url=\"/\".concat(pathSnippets.slice(0,index+1).join('/'));const menuItem=findMenuItemByKey(menuItems,url);if(menuItem){breadcrumbItems.push({title:menuItem.label});}});return breadcrumbItems;};// 查找菜单项\nconst findMenuItemByKey=(items,key)=>{for(const item of items){if(item.key===key){return item;}if(item.children){const found=findMenuItemByKey(item.children,key);if(found)return found;}}return null;};return/*#__PURE__*/_jsxs(Layout,{style:{minHeight:'100vh'},children:[/*#__PURE__*/_jsxs(Sider,{trigger:null,collapsible:true,collapsed:collapsed,children:[/*#__PURE__*/_jsx(\"div\",{style:{height:32,margin:16,background:'rgba(255, 255, 255, 0.3)',borderRadius:6,display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontWeight:'bold'},children:collapsed?'LBS':'Link-BOM-S'}),/*#__PURE__*/_jsx(Menu,{theme:\"dark\",mode:\"inline\",selectedKeys:[location.pathname],items:menuItems,onClick:handleMenuClick})]}),/*#__PURE__*/_jsxs(Layout,{children:[/*#__PURE__*/_jsxs(Header,{style:{padding:'0 16px',background:colorBgContainer,display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(Button,{type:\"text\",icon:collapsed?/*#__PURE__*/_jsx(MenuUnfoldOutlined,{}):/*#__PURE__*/_jsx(MenuFoldOutlined,{}),onClick:()=>setCollapsed(!collapsed),style:{fontSize:'16px',width:64,height:64}}),/*#__PURE__*/_jsxs(Space,{size:\"middle\",children:[(user===null||user===void 0?void 0:user.roles)&&user.roles.length>1&&/*#__PURE__*/_jsx(Dropdown,{menu:{items:roleMenuItems},placement:\"bottomRight\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(SwitcherOutlined,{}),children:currentRole===null||currentRole===void 0?void 0:currentRole.name})}),/*#__PURE__*/_jsx(Badge,{count:5,children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(BellOutlined,{})})}),/*#__PURE__*/_jsx(Dropdown,{menu:{items:userMenuItems,onClick:handleUserMenuClick},placement:\"bottomRight\",children:/*#__PURE__*/_jsxs(Space,{style:{cursor:'pointer'},children:[/*#__PURE__*/_jsx(Avatar,{icon:/*#__PURE__*/_jsx(UserOutlined,{}),src:user===null||user===void 0?void 0:user.avatar}),/*#__PURE__*/_jsx(\"span\",{children:user===null||user===void 0?void 0:user.name})]})})]})]}),/*#__PURE__*/_jsxs(Content,{style:{margin:'16px'},children:[/*#__PURE__*/_jsx(Breadcrumb,{style:{marginBottom:16},items:generateBreadcrumb()}),/*#__PURE__*/_jsx(\"div\",{style:{padding:24,minHeight:360,background:colorBgContainer,borderRadius:6},children:/*#__PURE__*/_jsx(Outlet,{})})]})]})]});};export default MainLayout;", "map": {"version": 3, "names": ["React", "useState", "Outlet", "useNavigate", "useLocation", "Layout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "Space", "Badge", "Breadcrumb", "theme", "MenuFoldOutlined", "MenuUnfoldOutlined", "DashboardOutlined", "FileTextOutlined", "ShoppingCartOutlined", "InboxOutlined", "DollarOutlined", "ToolOutlined", "SwapOutlined", "BarChartOutlined", "SettingOutlined", "MobileOutlined", "UserOutlined", "LogoutOutlined", "BellOutlined", "SwitcherOutlined", "useAppDispatch", "useAppSelector", "logout", "switchRole", "ROUTES", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "<PERSON><PERSON>", "Content", "menuItems", "key", "DASHBOARD", "icon", "label", "children", "CORE_BOM", "ORDER_BOM", "MATERIALS", "INVENTORY", "INVENTORY_RECEIVE", "INVENTORY_ISSUE", "REMNANTS", "CUTTING_PLAN", "BATCH_TRACKING", "PURCHASE", "PURCHASE_REQUISITION", "MRP_CALCULATION", "PURCHASE_OPTIMIZATION", "COST_ANALYSIS", "COST_REPORTS", "WASTE_TRACKING", "STANDARD_COST", "SERVICE_BOM", "DEVICE_ARCHIVE", "MAINTENANCE", "AS_BUILT_BOM", "SPARE_PARTS", "ECN", "REPORTS", "DASHBOARD_CONFIG", "USERS", "ROLES", "PERMISSIONS", "SYSTEM_CONFIG", "AUDIT_LOG", "MOBILE", "MOBILE_SCAN", "MOBILE_INVENTORY", "MainLayout", "collapsed", "setCollapsed", "navigate", "location", "dispatch", "user", "currentRole", "state", "auth", "token", "colorBgContainer", "useToken", "handleMenuClick", "_ref", "handleUserMenuClick", "_ref2", "LOGIN", "handleRoleSwitch", "roleId", "userMenuItems", "type", "roleMenuItems", "roles", "map", "role", "id", "name", "onClick", "generateBreadcrumb", "pathSnippets", "pathname", "split", "filter", "i", "breadcrumbItems", "title", "for<PERSON>ach", "snippet", "index", "url", "concat", "slice", "join", "menuItem", "findMenuItemByKey", "push", "items", "item", "found", "style", "minHeight", "trigger", "collapsible", "height", "margin", "background", "borderRadius", "display", "alignItems", "justifyContent", "color", "fontWeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "padding", "fontSize", "width", "size", "length", "menu", "placement", "count", "cursor", "src", "avatar", "marginBottom"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Layout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Button,\n  Space,\n  Badge,\n  Breadcrumb,\n  theme,\n  MenuProps,\n} from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  DashboardOutlined,\n  FileTextOutlined,\n  ShoppingCartOutlined,\n  InboxOutlined,\n  DollarOutlined,\n  ToolOutlined,\n  SwapOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  SettingOutlined,\n  MobileOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  SwitcherOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { logout, switchRole } from '../../store/slices/authSlice';\nimport { ROUTES, USER_ROLES } from '../../constants';\n\nconst { Header, Sider, Content } = Layout;\n\n// 菜单配置\nconst menuItems: MenuProps['items'] = [\n  {\n    key: ROUTES.DASHBOARD,\n    icon: <DashboardOutlined />,\n    label: '仪表板',\n  },\n  {\n    key: 'bom',\n    icon: <FileTextOutlined />,\n    label: 'BOM管理',\n    children: [\n      {\n        key: ROUTES.CORE_BOM,\n        label: '核心BOM',\n      },\n      {\n        key: ROUTES.ORDER_BOM,\n        label: '订单BOM',\n      },\n    ],\n  },\n  {\n    key: 'material',\n    icon: <InboxOutlined />,\n    label: '物料管理',\n    children: [\n      {\n        key: ROUTES.MATERIALS,\n        label: '物料清单',\n      },\n    ],\n  },\n  {\n    key: 'inventory',\n    icon: <InboxOutlined />,\n    label: '库存管理',\n    children: [\n      {\n        key: ROUTES.INVENTORY,\n        label: '库存查询',\n      },\n      {\n        key: ROUTES.INVENTORY_RECEIVE,\n        label: '入库管理',\n      },\n      {\n        key: ROUTES.INVENTORY_ISSUE,\n        label: '出库管理',\n      },\n      {\n        key: ROUTES.REMNANTS,\n        label: '余料管理',\n      },\n      {\n        key: ROUTES.CUTTING_PLAN,\n        label: '切割计划',\n      },\n      {\n        key: ROUTES.BATCH_TRACKING,\n        label: '批次跟踪',\n      },\n    ],\n  },\n  {\n    key: 'purchase',\n    icon: <ShoppingCartOutlined />,\n    label: '采购管理',\n    children: [\n      {\n        key: ROUTES.PURCHASE,\n        label: '采购订单',\n      },\n      {\n        key: ROUTES.PURCHASE_REQUISITION,\n        label: '采购申请',\n      },\n      {\n        key: ROUTES.MRP_CALCULATION,\n        label: 'MRP计算',\n      },\n      {\n        key: ROUTES.PURCHASE_OPTIMIZATION,\n        label: '采购优化',\n      },\n    ],\n  },\n  {\n    key: 'cost',\n    icon: <DollarOutlined />,\n    label: '成本管理',\n    children: [\n      {\n        key: ROUTES.COST_ANALYSIS,\n        label: '成本分析',\n      },\n      {\n        key: ROUTES.COST_REPORTS,\n        label: '成本报告',\n      },\n      {\n        key: ROUTES.WASTE_TRACKING,\n        label: '浪费跟踪',\n      },\n      {\n        key: ROUTES.STANDARD_COST,\n        label: '标准成本',\n      },\n    ],\n  },\n  {\n    key: 'service',\n    icon: <ToolOutlined />,\n    label: '服务管理',\n    children: [\n      {\n        key: ROUTES.SERVICE_BOM,\n        label: '服务BOM',\n      },\n      {\n        key: ROUTES.DEVICE_ARCHIVE,\n        label: '设备档案',\n      },\n      {\n        key: ROUTES.MAINTENANCE,\n        label: '维护记录',\n      },\n      {\n        key: ROUTES.AS_BUILT_BOM,\n        label: 'As-Built BOM',\n      },\n      {\n        key: ROUTES.SPARE_PARTS,\n        label: '备件管理',\n      },\n    ],\n  },\n  {\n    key: 'ecn',\n    icon: <SwapOutlined />,\n    label: 'ECN管理',\n    children: [\n      {\n        key: ROUTES.ECN,\n        label: 'ECN列表',\n      },\n    ],\n  },\n  {\n    key: 'reports',\n    icon: <BarChartOutlined />,\n    label: '报告分析',\n    children: [\n      {\n        key: ROUTES.REPORTS,\n        label: '报告中心',\n      },\n      {\n        key: ROUTES.DASHBOARD_CONFIG,\n        label: '仪表板配置',\n      },\n    ],\n  },\n  {\n    key: 'system',\n    icon: <SettingOutlined />,\n    label: '系统管理',\n    children: [\n      {\n        key: ROUTES.USERS,\n        label: '用户管理',\n      },\n      {\n        key: ROUTES.ROLES,\n        label: '角色管理',\n      },\n      {\n        key: ROUTES.PERMISSIONS,\n        label: '权限管理',\n      },\n      {\n        key: ROUTES.SYSTEM_CONFIG,\n        label: '系统配置',\n      },\n      {\n        key: ROUTES.AUDIT_LOG,\n        label: '审计日志',\n      },\n    ],\n  },\n  {\n    key: 'mobile',\n    icon: <MobileOutlined />,\n    label: '移动端',\n    children: [\n      {\n        key: ROUTES.MOBILE,\n        label: '移动首页',\n      },\n      {\n        key: ROUTES.MOBILE_SCAN,\n        label: '扫码操作',\n      },\n      {\n        key: ROUTES.MOBILE_INVENTORY,\n        label: '移动库存',\n      },\n    ],\n  },\n];\n\nconst MainLayout: React.FC = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const { user, currentRole } = useAppSelector(state => state.auth);\n  const {\n    token: { colorBgContainer },\n  } = theme.useToken();\n\n  // 处理菜单点击\n  const handleMenuClick = ({ key }: { key: string }) => {\n    navigate(key);\n  };\n\n  // 处理用户菜单点击\n  const handleUserMenuClick = ({ key }: { key: string }) => {\n    switch (key) {\n      case 'logout':\n        dispatch(logout());\n        navigate(ROUTES.LOGIN);\n        break;\n      case 'profile':\n        // 跳转到个人资料页面\n        break;\n      default:\n        break;\n    }\n  };\n\n  // 处理角色切换\n  const handleRoleSwitch = (roleId: string) => {\n    dispatch(switchRole(roleId));\n  };\n\n  // 用户下拉菜单\n  const userMenuItems: MenuProps['items'] = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n    },\n  ];\n\n  // 角色切换菜单\n  const roleMenuItems: MenuProps['items'] = user?.roles.map(role => ({\n    key: role.id,\n    label: role.name,\n    onClick: () => handleRoleSwitch(role.id),\n  })) || [];\n\n  // 生成面包屑\n  const generateBreadcrumb = () => {\n    const pathSnippets = location.pathname.split('/').filter(i => i);\n    const breadcrumbItems = [\n      {\n        title: '首页',\n      },\n    ];\n\n    pathSnippets.forEach((snippet, index) => {\n      const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;\n      const menuItem = findMenuItemByKey(menuItems, url);\n      if (menuItem) {\n        breadcrumbItems.push({\n          title: menuItem.label as string,\n        });\n      }\n    });\n\n    return breadcrumbItems;\n  };\n\n  // 查找菜单项\n  const findMenuItemByKey = (items: any[], key: string): any => {\n    for (const item of items) {\n      if (item.key === key) {\n        return item;\n      }\n      if (item.children) {\n        const found = findMenuItemByKey(item.children, key);\n        if (found) return found;\n      }\n    }\n    return null;\n  };\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed}>\n        <div style={{ \n          height: 32, \n          margin: 16, \n          background: 'rgba(255, 255, 255, 0.3)',\n          borderRadius: 6,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: 'bold',\n        }}>\n          {collapsed ? 'LBS' : 'Link-BOM-S'}\n        </div>\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={handleMenuClick}\n        />\n      </Sider>\n      \n      <Layout>\n        <Header style={{ \n          padding: '0 16px', \n          background: colorBgContainer,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n        }}>\n          <Button\n            type=\"text\"\n            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => setCollapsed(!collapsed)}\n            style={{\n              fontSize: '16px',\n              width: 64,\n              height: 64,\n            }}\n          />\n          \n          <Space size=\"middle\">\n            {/* 角色切换 */}\n            {user?.roles && user.roles.length > 1 && (\n              <Dropdown\n                menu={{ items: roleMenuItems }}\n                placement=\"bottomRight\"\n              >\n                <Button type=\"text\" icon={<SwitcherOutlined />}>\n                  {currentRole?.name}\n                </Button>\n              </Dropdown>\n            )}\n            \n            {/* 通知 */}\n            <Badge count={5}>\n              <Button type=\"text\" icon={<BellOutlined />} />\n            </Badge>\n            \n            {/* 用户信息 */}\n            <Dropdown\n              menu={{ items: userMenuItems, onClick: handleUserMenuClick }}\n              placement=\"bottomRight\"\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} src={user?.avatar} />\n                <span>{user?.name}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n        \n        <Content style={{ margin: '16px' }}>\n          <Breadcrumb\n            style={{ marginBottom: 16 }}\n            items={generateBreadcrumb()}\n          />\n          \n          <div\n            style={{\n              padding: 24,\n              minHeight: 360,\n              background: colorBgContainer,\n              borderRadius: 6,\n            }}\n          >\n            <Outlet />\n          </div>\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACnE,OACEC,MAAM,CACNC,IAAI,CACJC,MAAM,CACNC,QAAQ,CACRC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,UAAU,CACVC,KAAK,KAEA,MAAM,CACb,OACEC,gBAAgB,CAChBC,kBAAkB,CAClBC,iBAAiB,CACjBC,gBAAgB,CAChBC,oBAAoB,CACpBC,aAAa,CACbC,cAAc,CACdC,YAAY,CACZC,YAAY,CACZC,gBAAgB,CAChBC,eAAe,CACfC,cAAc,CACdC,YAAY,CACZC,cAAc,CACdC,YAAY,CACZC,gBAAgB,KACX,mBAAmB,CAE1B,OAASC,cAAc,CAAEC,cAAc,KAAQ,mBAAmB,CAClE,OAASC,MAAM,CAAEC,UAAU,KAAQ,8BAA8B,CACjE,OAASC,MAAM,KAAoB,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAGpC,MAAM,CAEzC;AACA,KAAM,CAAAqC,SAA6B,CAAG,CACpC,CACEC,GAAG,CAAET,MAAM,CAACU,SAAS,CACrBC,IAAI,cAAET,IAAA,CAACpB,iBAAiB,GAAE,CAAC,CAC3B8B,KAAK,CAAE,KACT,CAAC,CACD,CACEH,GAAG,CAAE,KAAK,CACVE,IAAI,cAAET,IAAA,CAACnB,gBAAgB,GAAE,CAAC,CAC1B6B,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAACc,QAAQ,CACpBF,KAAK,CAAE,OACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACe,SAAS,CACrBH,KAAK,CAAE,OACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,UAAU,CACfE,IAAI,cAAET,IAAA,CAACjB,aAAa,GAAE,CAAC,CACvB2B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAACgB,SAAS,CACrBJ,KAAK,CAAE,MACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,WAAW,CAChBE,IAAI,cAAET,IAAA,CAACjB,aAAa,GAAE,CAAC,CACvB2B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAACiB,SAAS,CACrBL,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACkB,iBAAiB,CAC7BN,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACmB,eAAe,CAC3BP,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACoB,QAAQ,CACpBR,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACqB,YAAY,CACxBT,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACsB,cAAc,CAC1BV,KAAK,CAAE,MACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,UAAU,CACfE,IAAI,cAAET,IAAA,CAAClB,oBAAoB,GAAE,CAAC,CAC9B4B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAACuB,QAAQ,CACpBX,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACwB,oBAAoB,CAChCZ,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACyB,eAAe,CAC3Bb,KAAK,CAAE,OACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAAC0B,qBAAqB,CACjCd,KAAK,CAAE,MACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,MAAM,CACXE,IAAI,cAAET,IAAA,CAAChB,cAAc,GAAE,CAAC,CACxB0B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAAC2B,aAAa,CACzBf,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAAC4B,YAAY,CACxBhB,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAAC6B,cAAc,CAC1BjB,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAAC8B,aAAa,CACzBlB,KAAK,CAAE,MACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,SAAS,CACdE,IAAI,cAAET,IAAA,CAACf,YAAY,GAAE,CAAC,CACtByB,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAAC+B,WAAW,CACvBnB,KAAK,CAAE,OACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACgC,cAAc,CAC1BpB,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACiC,WAAW,CACvBrB,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACkC,YAAY,CACxBtB,KAAK,CAAE,cACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACmC,WAAW,CACvBvB,KAAK,CAAE,MACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,KAAK,CACVE,IAAI,cAAET,IAAA,CAACd,YAAY,GAAE,CAAC,CACtBwB,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAACoC,GAAG,CACfxB,KAAK,CAAE,OACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,SAAS,CACdE,IAAI,cAAET,IAAA,CAACb,gBAAgB,GAAE,CAAC,CAC1BuB,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAACqC,OAAO,CACnBzB,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACsC,gBAAgB,CAC5B1B,KAAK,CAAE,OACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,QAAQ,CACbE,IAAI,cAAET,IAAA,CAACZ,eAAe,GAAE,CAAC,CACzBsB,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAACuC,KAAK,CACjB3B,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACwC,KAAK,CACjB5B,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAACyC,WAAW,CACvB7B,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAAC0C,aAAa,CACzB9B,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAAC2C,SAAS,CACrB/B,KAAK,CAAE,MACT,CAAC,CAEL,CAAC,CACD,CACEH,GAAG,CAAE,QAAQ,CACbE,IAAI,cAAET,IAAA,CAACX,cAAc,GAAE,CAAC,CACxBqB,KAAK,CAAE,KAAK,CACZC,QAAQ,CAAE,CACR,CACEJ,GAAG,CAAET,MAAM,CAAC4C,MAAM,CAClBhC,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAAC6C,WAAW,CACvBjC,KAAK,CAAE,MACT,CAAC,CACD,CACEH,GAAG,CAAET,MAAM,CAAC8C,gBAAgB,CAC5BlC,KAAK,CAAE,MACT,CAAC,CAEL,CAAC,CACF,CAED,KAAM,CAAAmC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGlF,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAAmF,QAAQ,CAAGjF,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkF,QAAQ,CAAGjF,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkF,QAAQ,CAAGxD,cAAc,CAAC,CAAC,CACjC,KAAM,CAAEyD,IAAI,CAAEC,WAAY,CAAC,CAAGzD,cAAc,CAAC0D,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CACjE,KAAM,CACJC,KAAK,CAAE,CAAEC,gBAAiB,CAC5B,CAAC,CAAG/E,KAAK,CAACgF,QAAQ,CAAC,CAAC,CAEpB;AACA,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAA8B,IAA7B,CAAEpD,GAAqB,CAAC,CAAAoD,IAAA,CAC/CX,QAAQ,CAACzC,GAAG,CAAC,CACf,CAAC,CAED;AACA,KAAM,CAAAqD,mBAAmB,CAAGC,KAAA,EAA8B,IAA7B,CAAEtD,GAAqB,CAAC,CAAAsD,KAAA,CACnD,OAAQtD,GAAG,EACT,IAAK,QAAQ,CACX2C,QAAQ,CAACtD,MAAM,CAAC,CAAC,CAAC,CAClBoD,QAAQ,CAAClD,MAAM,CAACgE,KAAK,CAAC,CACtB,MACF,IAAK,SAAS,CACZ;AACA,MACF,QACE,MACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAIC,MAAc,EAAK,CAC3Cd,QAAQ,CAACrD,UAAU,CAACmE,MAAM,CAAC,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAAC,aAAiC,CAAG,CACxC,CACE1D,GAAG,CAAE,SAAS,CACdE,IAAI,cAAET,IAAA,CAACV,YAAY,GAAE,CAAC,CACtBoB,KAAK,CAAE,MACT,CAAC,CACD,CACEwD,IAAI,CAAE,SACR,CAAC,CACD,CACE3D,GAAG,CAAE,QAAQ,CACbE,IAAI,cAAET,IAAA,CAACT,cAAc,GAAE,CAAC,CACxBmB,KAAK,CAAE,MACT,CAAC,CACF,CAED;AACA,KAAM,CAAAyD,aAAiC,CAAG,CAAAhB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,KAAK,CAACC,GAAG,CAACC,IAAI,GAAK,CACjE/D,GAAG,CAAE+D,IAAI,CAACC,EAAE,CACZ7D,KAAK,CAAE4D,IAAI,CAACE,IAAI,CAChBC,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAACO,IAAI,CAACC,EAAE,CACzC,CAAC,CAAC,CAAC,GAAI,EAAE,CAET;AACA,KAAM,CAAAG,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,YAAY,CAAG1B,QAAQ,CAAC2B,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC,CAChE,KAAM,CAAAC,eAAe,CAAG,CACtB,CACEC,KAAK,CAAE,IACT,CAAC,CACF,CAEDN,YAAY,CAACO,OAAO,CAAC,CAACC,OAAO,CAAEC,KAAK,GAAK,CACvC,KAAM,CAAAC,GAAG,KAAAC,MAAA,CAAOX,YAAY,CAACY,KAAK,CAAC,CAAC,CAAEH,KAAK,CAAG,CAAC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC,CAAE,CAC5D,KAAM,CAAAC,QAAQ,CAAGC,iBAAiB,CAACpF,SAAS,CAAE+E,GAAG,CAAC,CAClD,GAAII,QAAQ,CAAE,CACZT,eAAe,CAACW,IAAI,CAAC,CACnBV,KAAK,CAAEQ,QAAQ,CAAC/E,KAClB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,MAAO,CAAAsE,eAAe,CACxB,CAAC,CAED;AACA,KAAM,CAAAU,iBAAiB,CAAGA,CAACE,KAAY,CAAErF,GAAW,GAAU,CAC5D,IAAK,KAAM,CAAAsF,IAAI,GAAI,CAAAD,KAAK,CAAE,CACxB,GAAIC,IAAI,CAACtF,GAAG,GAAKA,GAAG,CAAE,CACpB,MAAO,CAAAsF,IAAI,CACb,CACA,GAAIA,IAAI,CAAClF,QAAQ,CAAE,CACjB,KAAM,CAAAmF,KAAK,CAAGJ,iBAAiB,CAACG,IAAI,CAAClF,QAAQ,CAAEJ,GAAG,CAAC,CACnD,GAAIuF,KAAK,CAAE,MAAO,CAAAA,KAAK,CACzB,CACF,CACA,MAAO,KAAI,CACb,CAAC,CAED,mBACE5F,KAAA,CAACjC,MAAM,EAAC8H,KAAK,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAArF,QAAA,eACpCT,KAAA,CAACE,KAAK,EAAC6F,OAAO,CAAE,IAAK,CAACC,WAAW,MAACpD,SAAS,CAAEA,SAAU,CAAAnC,QAAA,eACrDX,IAAA,QAAK+F,KAAK,CAAE,CACVI,MAAM,CAAE,EAAE,CACVC,MAAM,CAAE,EAAE,CACVC,UAAU,CAAE,0BAA0B,CACtCC,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,KAAK,CAAE,OAAO,CACdC,UAAU,CAAE,MACd,CAAE,CAAAhG,QAAA,CACCmC,SAAS,CAAG,KAAK,CAAG,YAAY,CAC9B,CAAC,cACN9C,IAAA,CAAC9B,IAAI,EACHO,KAAK,CAAC,MAAM,CACZmI,IAAI,CAAC,QAAQ,CACbC,YAAY,CAAE,CAAC5D,QAAQ,CAAC2B,QAAQ,CAAE,CAClCgB,KAAK,CAAEtF,SAAU,CACjBmE,OAAO,CAAEf,eAAgB,CAC1B,CAAC,EACG,CAAC,cAERxD,KAAA,CAACjC,MAAM,EAAA0C,QAAA,eACLT,KAAA,CAACC,MAAM,EAAC4F,KAAK,CAAE,CACbe,OAAO,CAAE,QAAQ,CACjBT,UAAU,CAAE7C,gBAAgB,CAC5B+C,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAClB,CAAE,CAAA9F,QAAA,eACAX,IAAA,CAAC3B,MAAM,EACL6F,IAAI,CAAC,MAAM,CACXzD,IAAI,CAAEqC,SAAS,cAAG9C,IAAA,CAACrB,kBAAkB,GAAE,CAAC,cAAGqB,IAAA,CAACtB,gBAAgB,GAAE,CAAE,CAChE+F,OAAO,CAAEA,CAAA,GAAM1B,YAAY,CAAC,CAACD,SAAS,CAAE,CACxCiD,KAAK,CAAE,CACLgB,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,EAAE,CACTb,MAAM,CAAE,EACV,CAAE,CACH,CAAC,cAEFjG,KAAA,CAAC5B,KAAK,EAAC2I,IAAI,CAAC,QAAQ,CAAAtG,QAAA,EAEjB,CAAAwC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,KAAK,GAAIjB,IAAI,CAACiB,KAAK,CAAC8C,MAAM,CAAG,CAAC,eACnClH,IAAA,CAAC5B,QAAQ,EACP+I,IAAI,CAAE,CAAEvB,KAAK,CAAEzB,aAAc,CAAE,CAC/BiD,SAAS,CAAC,aAAa,CAAAzG,QAAA,cAEvBX,IAAA,CAAC3B,MAAM,EAAC6F,IAAI,CAAC,MAAM,CAACzD,IAAI,cAAET,IAAA,CAACP,gBAAgB,GAAE,CAAE,CAAAkB,QAAA,CAC5CyC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEoB,IAAI,CACZ,CAAC,CACD,CACX,cAGDxE,IAAA,CAACzB,KAAK,EAAC8I,KAAK,CAAE,CAAE,CAAA1G,QAAA,cACdX,IAAA,CAAC3B,MAAM,EAAC6F,IAAI,CAAC,MAAM,CAACzD,IAAI,cAAET,IAAA,CAACR,YAAY,GAAE,CAAE,CAAE,CAAC,CACzC,CAAC,cAGRQ,IAAA,CAAC5B,QAAQ,EACP+I,IAAI,CAAE,CAAEvB,KAAK,CAAE3B,aAAa,CAAEQ,OAAO,CAAEb,mBAAoB,CAAE,CAC7DwD,SAAS,CAAC,aAAa,CAAAzG,QAAA,cAEvBT,KAAA,CAAC5B,KAAK,EAACyH,KAAK,CAAE,CAAEuB,MAAM,CAAE,SAAU,CAAE,CAAA3G,QAAA,eAClCX,IAAA,CAAC7B,MAAM,EAACsC,IAAI,cAAET,IAAA,CAACV,YAAY,GAAE,CAAE,CAACiI,GAAG,CAAEpE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEqE,MAAO,CAAE,CAAC,cACrDxH,IAAA,SAAAW,QAAA,CAAOwC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEqB,IAAI,CAAO,CAAC,EACpB,CAAC,CACA,CAAC,EACN,CAAC,EACF,CAAC,cAETtE,KAAA,CAACG,OAAO,EAAC0F,KAAK,CAAE,CAAEK,MAAM,CAAE,MAAO,CAAE,CAAAzF,QAAA,eACjCX,IAAA,CAACxB,UAAU,EACTuH,KAAK,CAAE,CAAE0B,YAAY,CAAE,EAAG,CAAE,CAC5B7B,KAAK,CAAElB,kBAAkB,CAAC,CAAE,CAC7B,CAAC,cAEF1E,IAAA,QACE+F,KAAK,CAAE,CACLe,OAAO,CAAE,EAAE,CACXd,SAAS,CAAE,GAAG,CACdK,UAAU,CAAE7C,gBAAgB,CAC5B8C,YAAY,CAAE,CAChB,CAAE,CAAA3F,QAAA,cAEFX,IAAA,CAAClC,MAAM,GAAE,CAAC,CACP,CAAC,EACC,CAAC,EACJ,CAAC,EACH,CAAC,CAEb,CAAC,CAED,cAAe,CAAA+E,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}