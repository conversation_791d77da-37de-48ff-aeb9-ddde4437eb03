{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Typography,Table,Button,Space,Select,DatePicker,Row,Col,Statistic,Tag,Modal,Form,Input,Checkbox,Divider,Alert,Progress,Tooltip}from'antd';import*as XLSX from'xlsx';import{FileExcelOutlined,FilePdfOutlined,PrinterOutlined,DownloadOutlined,EyeOutlined,SettingOutlined,CalendarOutlined,DollarOutlined,WarningOutlined}from'@ant-design/icons';import dayjs from'dayjs';import{useAppDispatch,useAppSelector}from'../../hooks/redux';import{fetchCostReports}from'../../store/slices/costSlice';import{formatCurrency,formatDate}from'../../utils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{RangePicker}=DatePicker;const{TextArea}=Input;const CostReportsPage=()=>{const dispatch=useAppDispatch();const{costReports,loading}=useAppSelector(state=>state.cost);const[reportType,setReportType]=useState('monthly');const[dateRange,setDateRange]=useState([dayjs().subtract(1,'month'),dayjs()]);const[customReportVisible,setCustomReportVisible]=useState(false);const[previewVisible,setPreviewVisible]=useState(false);const[selectedReport,setSelectedReport]=useState(null);const[customForm]=Form.useForm();useEffect(()=>{loadData();},[reportType,dateRange]);const loadData=()=>{dispatch(fetchCostReports({type:reportType,startDate:dateRange[0].format('YYYY-MM-DD'),endDate:dateRange[1].format('YYYY-MM-DD')}));};const handleExport=(format,reportId)=>{try{let exportData=[];let fileName='';if(reportId){// 导出特定报告\nconst report=mockReports.find(r=>r.id===reportId);if(report){exportData=[{'报告名称':report.name,'报告类型':report.type,'生成时间':report.createdAt,'总成本':\"\\xA5\".concat(report.totalCost.toLocaleString()),'状态':report.status}];fileName=\"\\u6210\\u672C\\u62A5\\u544A_\".concat(report.name,\"_\").concat(new Date().toISOString().split('T')[0]);}}else{// 导出所有报告\nexportData=mockReports.map(report=>({'报告名称':report.name,'报告类型':report.type,'生成时间':report.createdAt,'总成本':\"\\xA5\".concat(report.totalCost.toLocaleString()),'状态':report.status}));fileName=\"\\u6210\\u672C\\u62A5\\u544A\\u6C47\\u603B_\".concat(new Date().toISOString().split('T')[0]);}if(format==='excel'){const ws=XLSX.utils.json_to_sheet(exportData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'成本报告');XLSX.writeFile(wb,\"\".concat(fileName,\".xlsx\"));}else if(format==='csv'){const ws=XLSX.utils.json_to_sheet(exportData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'成本报告');XLSX.writeFile(wb,\"\".concat(fileName,\".csv\"));}Modal.success({title:'导出成功',content:\"\\u62A5\\u544A\\u5DF2\\u5BFC\\u51FA\\u4E3A \".concat(format.toUpperCase(),\" \\u683C\\u5F0F\")});}catch(error){Modal.error({title:'导出失败',content:'导出报告时发生错误'});}};const handlePreview=report=>{setSelectedReport(report);setPreviewVisible(true);};const handleCustomReport=async()=>{try{const values=await customForm.validateFields();// TODO: 生成自定义报告\nconsole.log('自定义报告参数:',values);setCustomReportVisible(false);Modal.success({title:'报告生成成功',content:'自定义报告已生成，请在报告列表中查看'});}catch(error){console.error('生成报告失败:',error);}};// 模拟报告数据\nconst mockReports=[{id:'1',name:'2024年3月成本分析报告',type:'月度报告',period:'2024-03',totalCost:2850000,wasteAmount:156000,wastePercentage:5.47,marginAmount:712500,marginPercentage:25.0,status:'已完成',createdAt:'2024-04-01T00:00:00Z',createdBy:'finance_manager'},{id:'2',name:'2024年Q1季度成本报告',type:'季度报告',period:'2024-Q1',totalCost:8250000,wasteAmount:452000,wastePercentage:5.48,marginAmount:2062500,marginPercentage:25.0,status:'已完成',createdAt:'2024-04-05T00:00:00Z',createdBy:'finance_manager'},{id:'3',name:'华为项目成本专项报告',type:'专项报告',period:'2024-03',totalCost:1250000,wasteAmount:68000,wastePercentage:5.44,marginAmount:312500,marginPercentage:25.0,status:'进行中',createdAt:'2024-03-28T00:00:00Z',createdBy:'finance_manager'}];const reportColumns=[{title:'报告名称',dataIndex:'name',key:'name',ellipsis:true},{title:'报告类型',dataIndex:'type',key:'type',width:100,render:type=>/*#__PURE__*/_jsx(Tag,{color:type==='月度报告'?'blue':type==='季度报告'?'green':'orange',children:type})},{title:'报告期间',dataIndex:'period',key:'period',width:100},{title:'总成本',dataIndex:'totalCost',key:'totalCost',width:120,render:cost=>formatCurrency(cost)},{title:'浪费金额',dataIndex:'wasteAmount',key:'wasteAmount',width:120,render:(amount,record)=>/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:0,children:[/*#__PURE__*/_jsx(Text,{style:{color:'#ff4d4f'},children:formatCurrency(amount)}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:12},children:[record.wastePercentage.toFixed(2),\"%\"]})]})},{title:'毛利',dataIndex:'marginAmount',key:'marginAmount',width:120,render:(margin,record)=>/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:0,children:[/*#__PURE__*/_jsx(Text,{style:{color:'#52c41a'},children:formatCurrency(margin)}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:12},children:[record.marginPercentage.toFixed(1),\"%\"]})]})},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>/*#__PURE__*/_jsx(Tag,{color:status==='已完成'?'green':'processing',children:status})},{title:'创建时间',dataIndex:'createdAt',key:'createdAt',width:120,render:date=>formatDate(date)},{title:'操作',key:'action',width:200,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u9884\\u89C8\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handlePreview(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5BFC\\u51FAExcel\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(FileExcelOutlined,{}),onClick:()=>handleExport('excel',record.id)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5BFC\\u51FAPDF\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(FilePdfOutlined,{}),onClick:()=>handleExport('pdf',record.id)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u6253\\u5370\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(PrinterOutlined,{}),onClick:()=>window.print()})})]})}];// 统计数据\nconst stats={totalReports:mockReports.length,completedReports:mockReports.filter(r=>r.status==='已完成').length,avgWasteRate:mockReports.reduce((sum,r)=>sum+r.wastePercentage,0)/mockReports.length,totalCostSum:mockReports.reduce((sum,r)=>sum+r.totalCost,0)};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u62A5\\u544A\\u603B\\u6570\",value:stats.totalReports,prefix:/*#__PURE__*/_jsx(CalendarOutlined,{}),valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u5B8C\\u6210\",value:stats.completedReports,valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E73\\u5747\\u6D6A\\u8D39\\u7387\",value:stats.avgWasteRate,precision:2,suffix:\"%\",prefix:/*#__PURE__*/_jsx(WarningOutlined,{}),valueStyle:{color:'#faad14'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u6210\\u672C\",value:stats.totalCostSum,prefix:/*#__PURE__*/_jsx(DollarOutlined,{}),formatter:value=>formatCurrency(Number(value)),valueStyle:{color:'#722ed1'}})})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u6210\\u672C\\u62A5\\u544A\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Select,{value:reportType,onChange:setReportType,style:{width:120},options:[{label:'月度报告',value:'monthly'},{label:'季度报告',value:'quarterly'},{label:'年度报告',value:'yearly'},{label:'专项报告',value:'special'}]}),/*#__PURE__*/_jsx(RangePicker,{value:dateRange,onChange:dates=>dates&&setDateRange(dates),style:{width:240}}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(SettingOutlined,{}),onClick:()=>setCustomReportVisible(true),children:\"\\u81EA\\u5B9A\\u4E49\\u62A5\\u544A\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>handleExport('excel'),children:\"\\u6279\\u91CF\\u5BFC\\u51FA\"})]})})]}),/*#__PURE__*/_jsx(Table,{columns:reportColumns,dataSource:mockReports,loading:loading,rowKey:\"id\",scroll:{x:1200},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u751F\\u6210\\u81EA\\u5B9A\\u4E49\\u62A5\\u544A\",open:customReportVisible,onOk:handleCustomReport,onCancel:()=>setCustomReportVisible(false),width:600,okText:\"\\u751F\\u6210\\u62A5\\u544A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsxs(Form,{form:customForm,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"reportName\",label:\"\\u62A5\\u544A\\u540D\\u79F0\",rules:[{required:true,message:'请输入报告名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u62A5\\u544A\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"reportType\",label:\"\\u62A5\\u544A\\u7C7B\\u578B\",rules:[{required:true,message:'请选择报告类型'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u62A5\\u544A\\u7C7B\\u578B\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"cost_analysis\",children:\"\\u6210\\u672C\\u5206\\u6790\\u62A5\\u544A\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"waste_analysis\",children:\"\\u6D6A\\u8D39\\u5206\\u6790\\u62A5\\u544A\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"margin_analysis\",children:\"\\u6BDB\\u5229\\u5206\\u6790\\u62A5\\u544A\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"trend_analysis\",children:\"\\u8D8B\\u52BF\\u5206\\u6790\\u62A5\\u544A\"})]})}),/*#__PURE__*/_jsx(Form.Item,{name:\"dateRange\",label:\"\\u62A5\\u544A\\u671F\\u95F4\",rules:[{required:true,message:'请选择报告期间'}],children:/*#__PURE__*/_jsx(RangePicker,{style:{width:'100%'}})}),/*#__PURE__*/_jsx(Form.Item,{name:\"includeItems\",label:\"\\u5305\\u542B\\u5185\\u5BB9\",children:/*#__PURE__*/_jsx(Checkbox.Group,{children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"cost_overview\",children:\"\\u6210\\u672C\\u6982\\u89C8\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"waste_analysis\",children:\"\\u6D6A\\u8D39\\u5206\\u6790\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"trend_charts\",children:\"\\u8D8B\\u52BF\\u56FE\\u8868\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"order_details\",children:\"\\u8BA2\\u5355\\u660E\\u7EC6\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"supplier_analysis\",children:\"\\u4F9B\\u5E94\\u5546\\u5206\\u6790\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"recommendations\",children:\"\\u6539\\u5584\\u5EFA\\u8BAE\"})})]})})}),/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u62A5\\u544A\\u63CF\\u8FF0\",children:/*#__PURE__*/_jsx(TextArea,{rows:3,placeholder:\"\\u8BF7\\u8F93\\u5165\\u62A5\\u544A\\u63CF\\u8FF0\"})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u9884\\u89C8\\u62A5\\u544A: \".concat(selectedReport===null||selectedReport===void 0?void 0:selectedReport.name),open:previewVisible,onCancel:()=>setPreviewVisible(false),width:800,footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setPreviewVisible(false),children:\"\\u5173\\u95ED\"},\"close\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),children:\"\\u5BFC\\u51FA\"},\"export\")],children:selectedReport&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u62A5\\u544A\\u9884\\u89C8\",description:\"\\u8FD9\\u662F\\u62A5\\u544A\\u7684\\u9884\\u89C8\\u7248\\u672C\\uFF0C\\u5B8C\\u6574\\u5185\\u5BB9\\u8BF7\\u5BFC\\u51FA\\u67E5\\u770B\",type:\"info\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Divider,{orientation:\"left\",children:\"\\u62A5\\u544A\\u6982\\u8981\"}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u6210\\u672C\",value:selectedReport.totalCost,formatter:value=>formatCurrency(Number(value))})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6D6A\\u8D39\\u91D1\\u989D\",value:selectedReport.wasteAmount,formatter:value=>formatCurrency(Number(value)),valueStyle:{color:'#ff4d4f'}})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6BDB\\u5229\",value:selectedReport.marginAmount,formatter:value=>formatCurrency(Number(value)),valueStyle:{color:'#52c41a'}})})]}),/*#__PURE__*/_jsx(Divider,{orientation:\"left\",children:\"\\u5173\\u952E\\u6307\\u6807\"}),/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{children:\"\\u6D6A\\u8D39\\u7387: \"}),/*#__PURE__*/_jsx(Progress,{percent:selectedReport.wastePercentage,strokeColor:\"#ff4d4f\",format:percent=>\"\".concat(percent===null||percent===void 0?void 0:percent.toFixed(2),\"%\")})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{children:\"\\u6BDB\\u5229\\u7387: \"}),/*#__PURE__*/_jsx(Progress,{percent:selectedReport.marginPercentage,strokeColor:\"#52c41a\",format:percent=>\"\".concat(percent===null||percent===void 0?void 0:percent.toFixed(1),\"%\")})]})]})]})})]});};export default CostReportsPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Table", "<PERSON><PERSON>", "Space", "Select", "DatePicker", "Row", "Col", "Statistic", "Tag", "Modal", "Form", "Input", "Checkbox", "Divider", "<PERSON><PERSON>", "Progress", "<PERSON><PERSON><PERSON>", "XLSX", "FileExcelOutlined", "FilePdfOutlined", "PrinterOutlined", "DownloadOutlined", "EyeOutlined", "SettingOutlined", "CalendarOutlined", "DollarOutlined", "WarningOutlined", "dayjs", "useAppDispatch", "useAppSelector", "fetchCostReports", "formatCurrency", "formatDate", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "RangePicker", "TextArea", "CostReportsPage", "dispatch", "costReports", "loading", "state", "cost", "reportType", "setReportType", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "customReportVisible", "setCustomReportVisible", "previewVisible", "setPreviewVisible", "selectedReport", "setSelectedReport", "customForm", "useForm", "loadData", "type", "startDate", "format", "endDate", "handleExport", "reportId", "exportData", "fileName", "report", "mockReports", "find", "r", "id", "name", "createdAt", "concat", "totalCost", "toLocaleString", "status", "Date", "toISOString", "split", "map", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "writeFile", "success", "title", "content", "toUpperCase", "error", "handlePreview", "handleCustomReport", "values", "validateFields", "console", "log", "period", "wasteAmount", "wastePercentage", "marginAmount", "marginPercentage", "created<PERSON>y", "reportColumns", "dataIndex", "key", "ellipsis", "width", "render", "color", "children", "amount", "record", "direction", "size", "style", "fontSize", "toFixed", "margin", "date", "fixed", "_", "icon", "onClick", "window", "print", "stats", "totalReports", "length", "completedReports", "filter", "avgWasteRate", "reduce", "sum", "totalCostSum", "gutter", "marginBottom", "xs", "sm", "value", "prefix", "valueStyle", "precision", "suffix", "formatter", "Number", "justify", "align", "level", "onChange", "options", "label", "dates", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onOk", "onCancel", "okText", "cancelText", "form", "layout", "<PERSON><PERSON>", "rules", "required", "message", "placeholder", "Option", "Group", "span", "rows", "footer", "description", "showIcon", "orientation", "percent", "strokeColor"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/cost/CostReportsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Table,\n  Button,\n  Space,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Checkbox,\n  Divider,\n  Alert,\n  Progress,\n  Tooltip,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  FileExcelOutlined,\n  FilePdfOutlined,\n  PrinterOutlined,\n  DownloadOutlined,\n  EyeOutlined,\n  SettingOutlined,\n  CalendarOutlined,\n  DollarOutlined,\n  RiseOutlined,\n  WarningOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCostReports } from '../../store/slices/costSlice';\nimport { formatCurrency, formatDate } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\nconst { TextArea } = Input;\n\nconst CostReportsPage: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { costReports, loading } = useAppSelector(state => state.cost);\n\n  const [reportType, setReportType] = useState<string>('monthly');\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([\n    dayjs().subtract(1, 'month'),\n    dayjs(),\n  ]);\n  const [customReportVisible, setCustomReportVisible] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [selectedReport, setSelectedReport] = useState<any>(null);\n  const [customForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [reportType, dateRange]);\n\n  const loadData = () => {\n    dispatch(fetchCostReports({\n      type: reportType,\n      startDate: dateRange[0].format('YYYY-MM-DD'),\n      endDate: dateRange[1].format('YYYY-MM-DD'),\n    }));\n  };\n\n  const handleExport = (format: string, reportId?: string) => {\n    try {\n      let exportData: any[] = [];\n      let fileName = '';\n\n      if (reportId) {\n        // 导出特定报告\n        const report = mockReports.find(r => r.id === reportId);\n        if (report) {\n          exportData = [{\n            '报告名称': report.name,\n            '报告类型': report.type,\n            '生成时间': report.createdAt,\n            '总成本': `¥${report.totalCost.toLocaleString()}`,\n            '状态': report.status,\n          }];\n          fileName = `成本报告_${report.name}_${new Date().toISOString().split('T')[0]}`;\n        }\n      } else {\n        // 导出所有报告\n        exportData = mockReports.map(report => ({\n          '报告名称': report.name,\n          '报告类型': report.type,\n          '生成时间': report.createdAt,\n          '总成本': `¥${report.totalCost.toLocaleString()}`,\n          '状态': report.status,\n        }));\n        fileName = `成本报告汇总_${new Date().toISOString().split('T')[0]}`;\n      }\n\n      if (format === 'excel') {\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, '成本报告');\n        XLSX.writeFile(wb, `${fileName}.xlsx`);\n      } else if (format === 'csv') {\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, '成本报告');\n        XLSX.writeFile(wb, `${fileName}.csv`);\n      }\n\n      Modal.success({\n        title: '导出成功',\n        content: `报告已导出为 ${format.toUpperCase()} 格式`,\n      });\n    } catch (error) {\n      Modal.error({\n        title: '导出失败',\n        content: '导出报告时发生错误',\n      });\n    }\n  };\n\n  const handlePreview = (report: any) => {\n    setSelectedReport(report);\n    setPreviewVisible(true);\n  };\n\n  const handleCustomReport = async () => {\n    try {\n      const values = await customForm.validateFields();\n      // TODO: 生成自定义报告\n      console.log('自定义报告参数:', values);\n      setCustomReportVisible(false);\n      Modal.success({\n        title: '报告生成成功',\n        content: '自定义报告已生成，请在报告列表中查看',\n      });\n    } catch (error) {\n      console.error('生成报告失败:', error);\n    }\n  };\n\n  // 模拟报告数据\n  const mockReports = [\n    {\n      id: '1',\n      name: '2024年3月成本分析报告',\n      type: '月度报告',\n      period: '2024-03',\n      totalCost: 2850000,\n      wasteAmount: 156000,\n      wastePercentage: 5.47,\n      marginAmount: 712500,\n      marginPercentage: 25.0,\n      status: '已完成',\n      createdAt: '2024-04-01T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n    {\n      id: '2',\n      name: '2024年Q1季度成本报告',\n      type: '季度报告',\n      period: '2024-Q1',\n      totalCost: 8250000,\n      wasteAmount: 452000,\n      wastePercentage: 5.48,\n      marginAmount: 2062500,\n      marginPercentage: 25.0,\n      status: '已完成',\n      createdAt: '2024-04-05T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n    {\n      id: '3',\n      name: '华为项目成本专项报告',\n      type: '专项报告',\n      period: '2024-03',\n      totalCost: 1250000,\n      wasteAmount: 68000,\n      wastePercentage: 5.44,\n      marginAmount: 312500,\n      marginPercentage: 25.0,\n      status: '进行中',\n      createdAt: '2024-03-28T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n  ];\n\n  const reportColumns = [\n    {\n      title: '报告名称',\n      dataIndex: 'name',\n      key: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '报告类型',\n      dataIndex: 'type',\n      key: 'type',\n      width: 100,\n      render: (type: string) => (\n        <Tag color={type === '月度报告' ? 'blue' : type === '季度报告' ? 'green' : 'orange'}>\n          {type}\n        </Tag>\n      ),\n    },\n    {\n      title: '报告期间',\n      dataIndex: 'period',\n      key: 'period',\n      width: 100,\n    },\n    {\n      title: '总成本',\n      dataIndex: 'totalCost',\n      key: 'totalCost',\n      width: 120,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '浪费金额',\n      dataIndex: 'wasteAmount',\n      key: 'wasteAmount',\n      width: 120,\n      render: (amount: number, record: any) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ color: '#ff4d4f' }}>{formatCurrency(amount)}</Text>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.wastePercentage.toFixed(2)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '毛利',\n      dataIndex: 'marginAmount',\n      key: 'marginAmount',\n      width: 120,\n      render: (margin: number, record: any) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ color: '#52c41a' }}>{formatCurrency(margin)}</Text>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.marginPercentage.toFixed(1)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={status === '已完成' ? 'green' : 'processing'}>\n          {status}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right' as const,\n      render: (_: any, record: any) => (\n        <Space size=\"small\">\n          <Tooltip title=\"预览\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handlePreview(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"导出Excel\">\n            <Button\n              type=\"text\"\n              icon={<FileExcelOutlined />}\n              onClick={() => handleExport('excel', record.id)}\n            />\n          </Tooltip>\n          <Tooltip title=\"导出PDF\">\n            <Button\n              type=\"text\"\n              icon={<FilePdfOutlined />}\n              onClick={() => handleExport('pdf', record.id)}\n            />\n          </Tooltip>\n          <Tooltip title=\"打印\">\n            <Button\n              type=\"text\"\n              icon={<PrinterOutlined />}\n              onClick={() => window.print()}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  // 统计数据\n  const stats = {\n    totalReports: mockReports.length,\n    completedReports: mockReports.filter(r => r.status === '已完成').length,\n    avgWasteRate: mockReports.reduce((sum, r) => sum + r.wastePercentage, 0) / mockReports.length,\n    totalCostSum: mockReports.reduce((sum, r) => sum + r.totalCost, 0),\n  };\n\n  return (\n    <div>\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"报告总数\"\n              value={stats.totalReports}\n              prefix={<CalendarOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={stats.completedReports}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"平均浪费率\"\n              value={stats.avgWasteRate}\n              precision={2}\n              suffix=\"%\"\n              prefix={<WarningOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总成本\"\n              value={stats.totalCostSum}\n              prefix={<DollarOutlined />}\n              formatter={(value) => formatCurrency(Number(value))}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              成本报告\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Select\n                value={reportType}\n                onChange={setReportType}\n                style={{ width: 120 }}\n                options={[\n                  { label: '月度报告', value: 'monthly' },\n                  { label: '季度报告', value: 'quarterly' },\n                  { label: '年度报告', value: 'yearly' },\n                  { label: '专项报告', value: 'special' },\n                ]}\n              />\n              <RangePicker\n                value={dateRange}\n                onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n                style={{ width: 240 }}\n              />\n              <Button\n                icon={<SettingOutlined />}\n                onClick={() => setCustomReportVisible(true)}\n              >\n                自定义报告\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<DownloadOutlined />}\n                onClick={() => handleExport('excel')}\n              >\n                批量导出\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Table\n          columns={reportColumns}\n          dataSource={mockReports}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1200 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 自定义报告模态框 */}\n      <Modal\n        title=\"生成自定义报告\"\n        open={customReportVisible}\n        onOk={handleCustomReport}\n        onCancel={() => setCustomReportVisible(false)}\n        width={600}\n        okText=\"生成报告\"\n        cancelText=\"取消\"\n      >\n        <Form form={customForm} layout=\"vertical\">\n          <Form.Item\n            name=\"reportName\"\n            label=\"报告名称\"\n            rules={[{ required: true, message: '请输入报告名称' }]}\n          >\n            <Input placeholder=\"请输入报告名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"reportType\"\n            label=\"报告类型\"\n            rules={[{ required: true, message: '请选择报告类型' }]}\n          >\n            <Select placeholder=\"请选择报告类型\">\n              <Select.Option value=\"cost_analysis\">成本分析报告</Select.Option>\n              <Select.Option value=\"waste_analysis\">浪费分析报告</Select.Option>\n              <Select.Option value=\"margin_analysis\">毛利分析报告</Select.Option>\n              <Select.Option value=\"trend_analysis\">趋势分析报告</Select.Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"dateRange\"\n            label=\"报告期间\"\n            rules={[{ required: true, message: '请选择报告期间' }]}\n          >\n            <RangePicker style={{ width: '100%' }} />\n          </Form.Item>\n\n          <Form.Item name=\"includeItems\" label=\"包含内容\">\n            <Checkbox.Group>\n              <Row>\n                <Col span={12}>\n                  <Checkbox value=\"cost_overview\">成本概览</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"waste_analysis\">浪费分析</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"trend_charts\">趋势图表</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"order_details\">订单明细</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"supplier_analysis\">供应商分析</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"recommendations\">改善建议</Checkbox>\n                </Col>\n              </Row>\n            </Checkbox.Group>\n          </Form.Item>\n\n          <Form.Item name=\"description\" label=\"报告描述\">\n            <TextArea rows={3} placeholder=\"请输入报告描述\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 报告预览模态框 */}\n      <Modal\n        title={`预览报告: ${selectedReport?.name}`}\n        open={previewVisible}\n        onCancel={() => setPreviewVisible(false)}\n        width={800}\n        footer={[\n          <Button key=\"close\" onClick={() => setPreviewVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"export\" type=\"primary\" icon={<DownloadOutlined />}>\n            导出\n          </Button>,\n        ]}\n      >\n        {selectedReport && (\n          <div>\n            <Alert\n              message=\"报告预览\"\n              description=\"这是报告的预览版本，完整内容请导出查看\"\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n\n            <Divider orientation=\"left\">报告概要</Divider>\n            <Row gutter={[16, 16]}>\n              <Col span={8}>\n                <Statistic\n                  title=\"总成本\"\n                  value={selectedReport.totalCost}\n                  formatter={(value) => formatCurrency(Number(value))}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"浪费金额\"\n                  value={selectedReport.wasteAmount}\n                  formatter={(value) => formatCurrency(Number(value))}\n                  valueStyle={{ color: '#ff4d4f' }}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"毛利\"\n                  value={selectedReport.marginAmount}\n                  formatter={(value) => formatCurrency(Number(value))}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            </Row>\n\n            <Divider orientation=\"left\">关键指标</Divider>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text>浪费率: </Text>\n                <Progress\n                  percent={selectedReport.wastePercentage}\n                  strokeColor=\"#ff4d4f\"\n                  format={(percent) => `${percent?.toFixed(2)}%`}\n                />\n              </div>\n              <div>\n                <Text>毛利率: </Text>\n                <Progress\n                  percent={selectedReport.marginPercentage}\n                  strokeColor=\"#52c41a\"\n                  format={(percent) => `${percent?.toFixed(1)}%`}\n                />\n              </div>\n            </Space>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default CostReportsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,KAAK,CACLC,QAAQ,CACRC,OAAO,CACPC,KAAK,CACLC,QAAQ,CACRC,OAAO,KACF,MAAM,CACb,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,OACEC,iBAAiB,CACjBC,eAAe,CACfC,eAAe,CACfC,gBAAgB,CAChBC,WAAW,CACXC,eAAe,CACfC,gBAAgB,CAChBC,cAAc,CAEdC,eAAe,KACV,mBAAmB,CAC1B,MAAO,CAAAC,KAAK,KAAM,OAAO,CAEzB,OAASC,cAAc,CAAEC,cAAc,KAAQ,mBAAmB,CAClE,OAASC,gBAAgB,KAAQ,8BAA8B,CAC/D,OAASC,cAAc,CAAEC,UAAU,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGvC,UAAU,CAClC,KAAM,CAAEwC,WAAY,CAAC,CAAGnC,UAAU,CAClC,KAAM,CAAEoC,QAAS,CAAC,CAAG7B,KAAK,CAE1B,KAAM,CAAA8B,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAAC,QAAQ,CAAGd,cAAc,CAAC,CAAC,CACjC,KAAM,CAAEe,WAAW,CAAEC,OAAQ,CAAC,CAAGf,cAAc,CAACgB,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CAEpE,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGpD,QAAQ,CAAS,SAAS,CAAC,CAC/D,KAAM,CAACqD,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAA6B,CACrE+B,KAAK,CAAC,CAAC,CAACwB,QAAQ,CAAC,CAAC,CAAE,OAAO,CAAC,CAC5BxB,KAAK,CAAC,CAAC,CACR,CAAC,CACF,KAAM,CAACyB,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAAC0D,cAAc,CAAEC,iBAAiB,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC4D,cAAc,CAAEC,iBAAiB,CAAC,CAAG7D,QAAQ,CAAM,IAAI,CAAC,CAC/D,KAAM,CAAC8D,UAAU,CAAC,CAAGhD,IAAI,CAACiD,OAAO,CAAC,CAAC,CAEnC9D,SAAS,CAAC,IAAM,CACd+D,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACb,UAAU,CAAEE,SAAS,CAAC,CAAC,CAE3B,KAAM,CAAAW,QAAQ,CAAGA,CAAA,GAAM,CACrBlB,QAAQ,CAACZ,gBAAgB,CAAC,CACxB+B,IAAI,CAAEd,UAAU,CAChBe,SAAS,CAAEb,SAAS,CAAC,CAAC,CAAC,CAACc,MAAM,CAAC,YAAY,CAAC,CAC5CC,OAAO,CAAEf,SAAS,CAAC,CAAC,CAAC,CAACc,MAAM,CAAC,YAAY,CAC3C,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,YAAY,CAAGA,CAACF,MAAc,CAAEG,QAAiB,GAAK,CAC1D,GAAI,CACF,GAAI,CAAAC,UAAiB,CAAG,EAAE,CAC1B,GAAI,CAAAC,QAAQ,CAAG,EAAE,CAEjB,GAAIF,QAAQ,CAAE,CACZ;AACA,KAAM,CAAAG,MAAM,CAAGC,WAAW,CAACC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,EAAE,GAAKP,QAAQ,CAAC,CACvD,GAAIG,MAAM,CAAE,CACVF,UAAU,CAAG,CAAC,CACZ,MAAM,CAAEE,MAAM,CAACK,IAAI,CACnB,MAAM,CAAEL,MAAM,CAACR,IAAI,CACnB,MAAM,CAAEQ,MAAM,CAACM,SAAS,CACxB,KAAK,QAAAC,MAAA,CAAMP,MAAM,CAACQ,SAAS,CAACC,cAAc,CAAC,CAAC,CAAE,CAC9C,IAAI,CAAET,MAAM,CAACU,MACf,CAAC,CAAC,CACFX,QAAQ,6BAAAQ,MAAA,CAAWP,MAAM,CAACK,IAAI,MAAAE,MAAA,CAAI,GAAI,CAAAI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE,CAC5E,CACF,CAAC,IAAM,CACL;AACAf,UAAU,CAAGG,WAAW,CAACa,GAAG,CAACd,MAAM,GAAK,CACtC,MAAM,CAAEA,MAAM,CAACK,IAAI,CACnB,MAAM,CAAEL,MAAM,CAACR,IAAI,CACnB,MAAM,CAAEQ,MAAM,CAACM,SAAS,CACxB,KAAK,QAAAC,MAAA,CAAMP,MAAM,CAACQ,SAAS,CAACC,cAAc,CAAC,CAAC,CAAE,CAC9C,IAAI,CAAET,MAAM,CAACU,MACf,CAAC,CAAC,CAAC,CACHX,QAAQ,yCAAAQ,MAAA,CAAa,GAAI,CAAAI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE,CAC/D,CAEA,GAAInB,MAAM,GAAK,OAAO,CAAE,CACtB,KAAM,CAAAqB,EAAE,CAAGnE,IAAI,CAACoE,KAAK,CAACC,aAAa,CAACnB,UAAU,CAAC,CAC/C,KAAM,CAAAoB,EAAE,CAAGtE,IAAI,CAACoE,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChCvE,IAAI,CAACoE,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,MAAM,CAAC,CAC5CnE,IAAI,CAACyE,SAAS,CAACH,EAAE,IAAAX,MAAA,CAAKR,QAAQ,SAAO,CAAC,CACxC,CAAC,IAAM,IAAIL,MAAM,GAAK,KAAK,CAAE,CAC3B,KAAM,CAAAqB,EAAE,CAAGnE,IAAI,CAACoE,KAAK,CAACC,aAAa,CAACnB,UAAU,CAAC,CAC/C,KAAM,CAAAoB,EAAE,CAAGtE,IAAI,CAACoE,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChCvE,IAAI,CAACoE,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,MAAM,CAAC,CAC5CnE,IAAI,CAACyE,SAAS,CAACH,EAAE,IAAAX,MAAA,CAAKR,QAAQ,QAAM,CAAC,CACvC,CAEA3D,KAAK,CAACkF,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,yCAAAjB,MAAA,CAAYb,MAAM,CAAC+B,WAAW,CAAC,CAAC,iBACzC,CAAC,CAAC,CACJ,CAAE,MAAOC,KAAK,CAAE,CACdtF,KAAK,CAACsF,KAAK,CAAC,CACVH,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,WACX,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAG,aAAa,CAAI3B,MAAW,EAAK,CACrCZ,iBAAiB,CAACY,MAAM,CAAC,CACzBd,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAA0C,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAxC,UAAU,CAACyC,cAAc,CAAC,CAAC,CAChD;AACAC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEH,MAAM,CAAC,CAC/B7C,sBAAsB,CAAC,KAAK,CAAC,CAC7B5C,KAAK,CAACkF,OAAO,CAAC,CACZC,KAAK,CAAE,QAAQ,CACfC,OAAO,CAAE,oBACX,CAAC,CAAC,CACJ,CAAE,MAAOE,KAAK,CAAE,CACdK,OAAO,CAACL,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAAzB,WAAW,CAAG,CAClB,CACEG,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,eAAe,CACrBb,IAAI,CAAE,MAAM,CACZyC,MAAM,CAAE,SAAS,CACjBzB,SAAS,CAAE,OAAO,CAClB0B,WAAW,CAAE,MAAM,CACnBC,eAAe,CAAE,IAAI,CACrBC,YAAY,CAAE,MAAM,CACpBC,gBAAgB,CAAE,IAAI,CACtB3B,MAAM,CAAE,KAAK,CACbJ,SAAS,CAAE,sBAAsB,CACjCgC,SAAS,CAAE,iBACb,CAAC,CACD,CACElC,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,eAAe,CACrBb,IAAI,CAAE,MAAM,CACZyC,MAAM,CAAE,SAAS,CACjBzB,SAAS,CAAE,OAAO,CAClB0B,WAAW,CAAE,MAAM,CACnBC,eAAe,CAAE,IAAI,CACrBC,YAAY,CAAE,OAAO,CACrBC,gBAAgB,CAAE,IAAI,CACtB3B,MAAM,CAAE,KAAK,CACbJ,SAAS,CAAE,sBAAsB,CACjCgC,SAAS,CAAE,iBACb,CAAC,CACD,CACElC,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,YAAY,CAClBb,IAAI,CAAE,MAAM,CACZyC,MAAM,CAAE,SAAS,CACjBzB,SAAS,CAAE,OAAO,CAClB0B,WAAW,CAAE,KAAK,CAClBC,eAAe,CAAE,IAAI,CACrBC,YAAY,CAAE,MAAM,CACpBC,gBAAgB,CAAE,IAAI,CACtB3B,MAAM,CAAE,KAAK,CACbJ,SAAS,CAAE,sBAAsB,CACjCgC,SAAS,CAAE,iBACb,CAAC,CACF,CAED,KAAM,CAAAC,aAAa,CAAG,CACpB,CACEhB,KAAK,CAAE,MAAM,CACbiB,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEnB,KAAK,CAAE,MAAM,CACbiB,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXE,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGpD,IAAY,eACnB3B,IAAA,CAAC1B,GAAG,EAAC0G,KAAK,CAAErD,IAAI,GAAK,MAAM,CAAG,MAAM,CAAGA,IAAI,GAAK,MAAM,CAAG,OAAO,CAAG,QAAS,CAAAsD,QAAA,CACzEtD,IAAI,CACF,CAET,CAAC,CACD,CACE+B,KAAK,CAAE,MAAM,CACbiB,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbE,KAAK,CAAE,GACT,CAAC,CACD,CACEpB,KAAK,CAAE,KAAK,CACZiB,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBE,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGnE,IAAY,EAAKf,cAAc,CAACe,IAAI,CAC/C,CAAC,CACD,CACE8C,KAAK,CAAE,MAAM,CACbiB,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBE,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACG,MAAc,CAAEC,MAAW,gBAClCjF,KAAA,CAAClC,KAAK,EAACoH,SAAS,CAAC,UAAU,CAACC,IAAI,CAAE,CAAE,CAAAJ,QAAA,eAClCjF,IAAA,CAACI,IAAI,EAACkF,KAAK,CAAE,CAAEN,KAAK,CAAE,SAAU,CAAE,CAAAC,QAAA,CAAEpF,cAAc,CAACqF,MAAM,CAAC,CAAO,CAAC,cAClEhF,KAAA,CAACE,IAAI,EAACuB,IAAI,CAAC,WAAW,CAAC2D,KAAK,CAAE,CAAEC,QAAQ,CAAE,EAAG,CAAE,CAAAN,QAAA,EAC5CE,MAAM,CAACb,eAAe,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC,GACrC,EAAM,CAAC,EACF,CAEX,CAAC,CACD,CACE9B,KAAK,CAAE,IAAI,CACXiB,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBE,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACU,MAAc,CAAEN,MAAW,gBAClCjF,KAAA,CAAClC,KAAK,EAACoH,SAAS,CAAC,UAAU,CAACC,IAAI,CAAE,CAAE,CAAAJ,QAAA,eAClCjF,IAAA,CAACI,IAAI,EAACkF,KAAK,CAAE,CAAEN,KAAK,CAAE,SAAU,CAAE,CAAAC,QAAA,CAAEpF,cAAc,CAAC4F,MAAM,CAAC,CAAO,CAAC,cAClEvF,KAAA,CAACE,IAAI,EAACuB,IAAI,CAAC,WAAW,CAAC2D,KAAK,CAAE,CAAEC,QAAQ,CAAE,EAAG,CAAE,CAAAN,QAAA,EAC5CE,MAAM,CAACX,gBAAgB,CAACgB,OAAO,CAAC,CAAC,CAAC,CAAC,GACtC,EAAM,CAAC,EACF,CAEX,CAAC,CACD,CACE9B,KAAK,CAAE,IAAI,CACXiB,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbE,KAAK,CAAE,EAAE,CACTC,MAAM,CAAGlC,MAAc,eACrB7C,IAAA,CAAC1B,GAAG,EAAC0G,KAAK,CAAEnC,MAAM,GAAK,KAAK,CAAG,OAAO,CAAG,YAAa,CAAAoC,QAAA,CACnDpC,MAAM,CACJ,CAET,CAAC,CACD,CACEa,KAAK,CAAE,MAAM,CACbiB,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBE,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGW,IAAY,EAAK5F,UAAU,CAAC4F,IAAI,CAC3C,CAAC,CACD,CACEhC,KAAK,CAAE,IAAI,CACXkB,GAAG,CAAE,QAAQ,CACbE,KAAK,CAAE,GAAG,CACVa,KAAK,CAAE,OAAgB,CACvBZ,MAAM,CAAEA,CAACa,CAAM,CAAET,MAAW,gBAC1BjF,KAAA,CAAClC,KAAK,EAACqH,IAAI,CAAC,OAAO,CAAAJ,QAAA,eACjBjF,IAAA,CAAClB,OAAO,EAAC4E,KAAK,CAAC,cAAI,CAAAuB,QAAA,cACjBjF,IAAA,CAACjC,MAAM,EACL4D,IAAI,CAAC,MAAM,CACXkE,IAAI,cAAE7F,IAAA,CAACZ,WAAW,GAAE,CAAE,CACtB0G,OAAO,CAAEA,CAAA,GAAMhC,aAAa,CAACqB,MAAM,CAAE,CACtC,CAAC,CACK,CAAC,cACVnF,IAAA,CAAClB,OAAO,EAAC4E,KAAK,CAAC,mBAAS,CAAAuB,QAAA,cACtBjF,IAAA,CAACjC,MAAM,EACL4D,IAAI,CAAC,MAAM,CACXkE,IAAI,cAAE7F,IAAA,CAAChB,iBAAiB,GAAE,CAAE,CAC5B8G,OAAO,CAAEA,CAAA,GAAM/D,YAAY,CAAC,OAAO,CAAEoD,MAAM,CAAC5C,EAAE,CAAE,CACjD,CAAC,CACK,CAAC,cACVvC,IAAA,CAAClB,OAAO,EAAC4E,KAAK,CAAC,iBAAO,CAAAuB,QAAA,cACpBjF,IAAA,CAACjC,MAAM,EACL4D,IAAI,CAAC,MAAM,CACXkE,IAAI,cAAE7F,IAAA,CAACf,eAAe,GAAE,CAAE,CAC1B6G,OAAO,CAAEA,CAAA,GAAM/D,YAAY,CAAC,KAAK,CAAEoD,MAAM,CAAC5C,EAAE,CAAE,CAC/C,CAAC,CACK,CAAC,cACVvC,IAAA,CAAClB,OAAO,EAAC4E,KAAK,CAAC,cAAI,CAAAuB,QAAA,cACjBjF,IAAA,CAACjC,MAAM,EACL4D,IAAI,CAAC,MAAM,CACXkE,IAAI,cAAE7F,IAAA,CAACd,eAAe,GAAE,CAAE,CAC1B4G,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,KAAK,CAAC,CAAE,CAC/B,CAAC,CACK,CAAC,EACL,CAEX,CAAC,CACF,CAED;AACA,KAAM,CAAAC,KAAK,CAAG,CACZC,YAAY,CAAE9D,WAAW,CAAC+D,MAAM,CAChCC,gBAAgB,CAAEhE,WAAW,CAACiE,MAAM,CAAC/D,CAAC,EAAIA,CAAC,CAACO,MAAM,GAAK,KAAK,CAAC,CAACsD,MAAM,CACpEG,YAAY,CAAElE,WAAW,CAACmE,MAAM,CAAC,CAACC,GAAG,CAAElE,CAAC,GAAKkE,GAAG,CAAGlE,CAAC,CAACgC,eAAe,CAAE,CAAC,CAAC,CAAGlC,WAAW,CAAC+D,MAAM,CAC7FM,YAAY,CAAErE,WAAW,CAACmE,MAAM,CAAC,CAACC,GAAG,CAAElE,CAAC,GAAKkE,GAAG,CAAGlE,CAAC,CAACK,SAAS,CAAE,CAAC,CACnE,CAAC,CAED,mBACEzC,KAAA,QAAA+E,QAAA,eAEE/E,KAAA,CAAC/B,GAAG,EAACuI,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACpB,KAAK,CAAE,CAAEqB,YAAY,CAAE,EAAG,CAAE,CAAA1B,QAAA,eACjDjF,IAAA,CAAC5B,GAAG,EAACwI,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5B,QAAA,cACjBjF,IAAA,CAACpC,IAAI,EAAAqH,QAAA,cACHjF,IAAA,CAAC3B,SAAS,EACRqF,KAAK,CAAC,0BAAM,CACZoD,KAAK,CAAEb,KAAK,CAACC,YAAa,CAC1Ba,MAAM,cAAE/G,IAAA,CAACV,gBAAgB,GAAE,CAAE,CAC7B0H,UAAU,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNhF,IAAA,CAAC5B,GAAG,EAACwI,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5B,QAAA,cACjBjF,IAAA,CAACpC,IAAI,EAAAqH,QAAA,cACHjF,IAAA,CAAC3B,SAAS,EACRqF,KAAK,CAAC,oBAAK,CACXoD,KAAK,CAAEb,KAAK,CAACG,gBAAiB,CAC9BY,UAAU,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNhF,IAAA,CAAC5B,GAAG,EAACwI,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5B,QAAA,cACjBjF,IAAA,CAACpC,IAAI,EAAAqH,QAAA,cACHjF,IAAA,CAAC3B,SAAS,EACRqF,KAAK,CAAC,gCAAO,CACboD,KAAK,CAAEb,KAAK,CAACK,YAAa,CAC1BW,SAAS,CAAE,CAAE,CACbC,MAAM,CAAC,GAAG,CACVH,MAAM,cAAE/G,IAAA,CAACR,eAAe,GAAE,CAAE,CAC5BwH,UAAU,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNhF,IAAA,CAAC5B,GAAG,EAACwI,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5B,QAAA,cACjBjF,IAAA,CAACpC,IAAI,EAAAqH,QAAA,cACHjF,IAAA,CAAC3B,SAAS,EACRqF,KAAK,CAAC,oBAAK,CACXoD,KAAK,CAAEb,KAAK,CAACQ,YAAa,CAC1BM,MAAM,cAAE/G,IAAA,CAACT,cAAc,GAAE,CAAE,CAC3B4H,SAAS,CAAGL,KAAK,EAAKjH,cAAc,CAACuH,MAAM,CAACN,KAAK,CAAC,CAAE,CACpDE,UAAU,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAEN9E,KAAA,CAACtC,IAAI,EAAAqH,QAAA,eACH/E,KAAA,CAAC/B,GAAG,EAACkJ,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAAChC,KAAK,CAAE,CAAEqB,YAAY,CAAE,EAAG,CAAE,CAAA1B,QAAA,eACtEjF,IAAA,CAAC5B,GAAG,EAAA6G,QAAA,cACFjF,IAAA,CAACG,KAAK,EAACoH,KAAK,CAAE,CAAE,CAACjC,KAAK,CAAE,CAAEG,MAAM,CAAE,CAAE,CAAE,CAAAR,QAAA,CAAC,0BAEvC,CAAO,CAAC,CACL,CAAC,cACNjF,IAAA,CAAC5B,GAAG,EAAA6G,QAAA,cACF/E,KAAA,CAAClC,KAAK,EAAAiH,QAAA,eACJjF,IAAA,CAAC/B,MAAM,EACL6I,KAAK,CAAEjG,UAAW,CAClB2G,QAAQ,CAAE1G,aAAc,CACxBwE,KAAK,CAAE,CAAER,KAAK,CAAE,GAAI,CAAE,CACtB2C,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,MAAM,CAAEZ,KAAK,CAAE,SAAU,CAAC,CACnC,CAAEY,KAAK,CAAE,MAAM,CAAEZ,KAAK,CAAE,WAAY,CAAC,CACrC,CAAEY,KAAK,CAAE,MAAM,CAAEZ,KAAK,CAAE,QAAS,CAAC,CAClC,CAAEY,KAAK,CAAE,MAAM,CAAEZ,KAAK,CAAE,SAAU,CAAC,CACnC,CACH,CAAC,cACF9G,IAAA,CAACK,WAAW,EACVyG,KAAK,CAAE/F,SAAU,CACjByG,QAAQ,CAAGG,KAAK,EAAKA,KAAK,EAAI3G,YAAY,CAAC2G,KAAmC,CAAE,CAChFrC,KAAK,CAAE,CAAER,KAAK,CAAE,GAAI,CAAE,CACvB,CAAC,cACF9E,IAAA,CAACjC,MAAM,EACL8H,IAAI,cAAE7F,IAAA,CAACX,eAAe,GAAE,CAAE,CAC1ByG,OAAO,CAAEA,CAAA,GAAM3E,sBAAsB,CAAC,IAAI,CAAE,CAAA8D,QAAA,CAC7C,gCAED,CAAQ,CAAC,cACTjF,IAAA,CAACjC,MAAM,EACL4D,IAAI,CAAC,SAAS,CACdkE,IAAI,cAAE7F,IAAA,CAACb,gBAAgB,GAAE,CAAE,CAC3B2G,OAAO,CAAEA,CAAA,GAAM/D,YAAY,CAAC,OAAO,CAAE,CAAAkD,QAAA,CACtC,0BAED,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAENjF,IAAA,CAAClC,KAAK,EACJ8J,OAAO,CAAElD,aAAc,CACvBmD,UAAU,CAAEzF,WAAY,CACxB1B,OAAO,CAAEA,OAAQ,CACjBoH,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAA5F,MAAA,CACjB4F,KAAK,CAAC,CAAC,CAAC,MAAA5F,MAAA,CAAI4F,KAAK,CAAC,CAAC,CAAC,oBAAA5F,MAAA,CAAQ2F,KAAK,WAC1C,CAAE,CACH,CAAC,EACE,CAAC,cAGPrI,IAAA,CAACzB,KAAK,EACJmF,KAAK,CAAC,4CAAS,CACf6E,IAAI,CAAErH,mBAAoB,CAC1BsH,IAAI,CAAEzE,kBAAmB,CACzB0E,QAAQ,CAAEA,CAAA,GAAMtH,sBAAsB,CAAC,KAAK,CAAE,CAC9C2D,KAAK,CAAE,GAAI,CACX4D,MAAM,CAAC,0BAAM,CACbC,UAAU,CAAC,cAAI,CAAA1D,QAAA,cAEf/E,KAAA,CAAC1B,IAAI,EAACoK,IAAI,CAAEpH,UAAW,CAACqH,MAAM,CAAC,UAAU,CAAA5D,QAAA,eACvCjF,IAAA,CAACxB,IAAI,CAACsK,IAAI,EACRtG,IAAI,CAAC,YAAY,CACjBkF,KAAK,CAAC,0BAAM,CACZqB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAhE,QAAA,cAEhDjF,IAAA,CAACvB,KAAK,EAACyK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,cAEZlJ,IAAA,CAACxB,IAAI,CAACsK,IAAI,EACRtG,IAAI,CAAC,YAAY,CACjBkF,KAAK,CAAC,0BAAM,CACZqB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAhE,QAAA,cAEhD/E,KAAA,CAACjC,MAAM,EAACiL,WAAW,CAAC,4CAAS,CAAAjE,QAAA,eAC3BjF,IAAA,CAAC/B,MAAM,CAACkL,MAAM,EAACrC,KAAK,CAAC,eAAe,CAAA7B,QAAA,CAAC,sCAAM,CAAe,CAAC,cAC3DjF,IAAA,CAAC/B,MAAM,CAACkL,MAAM,EAACrC,KAAK,CAAC,gBAAgB,CAAA7B,QAAA,CAAC,sCAAM,CAAe,CAAC,cAC5DjF,IAAA,CAAC/B,MAAM,CAACkL,MAAM,EAACrC,KAAK,CAAC,iBAAiB,CAAA7B,QAAA,CAAC,sCAAM,CAAe,CAAC,cAC7DjF,IAAA,CAAC/B,MAAM,CAACkL,MAAM,EAACrC,KAAK,CAAC,gBAAgB,CAAA7B,QAAA,CAAC,sCAAM,CAAe,CAAC,EACtD,CAAC,CACA,CAAC,cAEZjF,IAAA,CAACxB,IAAI,CAACsK,IAAI,EACRtG,IAAI,CAAC,WAAW,CAChBkF,KAAK,CAAC,0BAAM,CACZqB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAhE,QAAA,cAEhDjF,IAAA,CAACK,WAAW,EAACiF,KAAK,CAAE,CAAER,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAChC,CAAC,cAEZ9E,IAAA,CAACxB,IAAI,CAACsK,IAAI,EAACtG,IAAI,CAAC,cAAc,CAACkF,KAAK,CAAC,0BAAM,CAAAzC,QAAA,cACzCjF,IAAA,CAACtB,QAAQ,CAAC0K,KAAK,EAAAnE,QAAA,cACb/E,KAAA,CAAC/B,GAAG,EAAA8G,QAAA,eACFjF,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjF,IAAA,CAACtB,QAAQ,EAACoI,KAAK,CAAC,eAAe,CAAA7B,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC5C,CAAC,cACNjF,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjF,IAAA,CAACtB,QAAQ,EAACoI,KAAK,CAAC,gBAAgB,CAAA7B,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC7C,CAAC,cACNjF,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjF,IAAA,CAACtB,QAAQ,EAACoI,KAAK,CAAC,cAAc,CAAA7B,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC3C,CAAC,cACNjF,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjF,IAAA,CAACtB,QAAQ,EAACoI,KAAK,CAAC,eAAe,CAAA7B,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC5C,CAAC,cACNjF,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjF,IAAA,CAACtB,QAAQ,EAACoI,KAAK,CAAC,mBAAmB,CAAA7B,QAAA,CAAC,gCAAK,CAAU,CAAC,CACjD,CAAC,cACNjF,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjF,IAAA,CAACtB,QAAQ,EAACoI,KAAK,CAAC,iBAAiB,CAAA7B,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC9C,CAAC,EACH,CAAC,CACQ,CAAC,CACR,CAAC,cAEZjF,IAAA,CAACxB,IAAI,CAACsK,IAAI,EAACtG,IAAI,CAAC,aAAa,CAACkF,KAAK,CAAC,0BAAM,CAAAzC,QAAA,cACxCjF,IAAA,CAACM,QAAQ,EAACgJ,IAAI,CAAE,CAAE,CAACJ,WAAW,CAAC,4CAAS,CAAE,CAAC,CAClC,CAAC,EACR,CAAC,CACF,CAAC,cAGRlJ,IAAA,CAACzB,KAAK,EACJmF,KAAK,8BAAAhB,MAAA,CAAWpB,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEkB,IAAI,CAAG,CACvC+F,IAAI,CAAEnH,cAAe,CACrBqH,QAAQ,CAAEA,CAAA,GAAMpH,iBAAiB,CAAC,KAAK,CAAE,CACzCyD,KAAK,CAAE,GAAI,CACXyE,MAAM,CAAE,cACNvJ,IAAA,CAACjC,MAAM,EAAa+H,OAAO,CAAEA,CAAA,GAAMzE,iBAAiB,CAAC,KAAK,CAAE,CAAA4D,QAAA,CAAC,cAE7D,EAFY,OAEJ,CAAC,cACTjF,IAAA,CAACjC,MAAM,EAAc4D,IAAI,CAAC,SAAS,CAACkE,IAAI,cAAE7F,IAAA,CAACb,gBAAgB,GAAE,CAAE,CAAA8F,QAAA,CAAC,cAEhE,EAFY,QAEJ,CAAC,CACT,CAAAA,QAAA,CAED3D,cAAc,eACbpB,KAAA,QAAA+E,QAAA,eACEjF,IAAA,CAACpB,KAAK,EACJqK,OAAO,CAAC,0BAAM,CACdO,WAAW,CAAC,oHAAqB,CACjC7H,IAAI,CAAC,MAAM,CACX8H,QAAQ,MACRnE,KAAK,CAAE,CAAEqB,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEF3G,IAAA,CAACrB,OAAO,EAAC+K,WAAW,CAAC,MAAM,CAAAzE,QAAA,CAAC,0BAAI,CAAS,CAAC,cAC1C/E,KAAA,CAAC/B,GAAG,EAACuI,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAzB,QAAA,eACpBjF,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAApE,QAAA,cACXjF,IAAA,CAAC3B,SAAS,EACRqF,KAAK,CAAC,oBAAK,CACXoD,KAAK,CAAExF,cAAc,CAACqB,SAAU,CAChCwE,SAAS,CAAGL,KAAK,EAAKjH,cAAc,CAACuH,MAAM,CAACN,KAAK,CAAC,CAAE,CACrD,CAAC,CACC,CAAC,cACN9G,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAApE,QAAA,cACXjF,IAAA,CAAC3B,SAAS,EACRqF,KAAK,CAAC,0BAAM,CACZoD,KAAK,CAAExF,cAAc,CAAC+C,WAAY,CAClC8C,SAAS,CAAGL,KAAK,EAAKjH,cAAc,CAACuH,MAAM,CAACN,KAAK,CAAC,CAAE,CACpDE,UAAU,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNhF,IAAA,CAAC5B,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAApE,QAAA,cACXjF,IAAA,CAAC3B,SAAS,EACRqF,KAAK,CAAC,cAAI,CACVoD,KAAK,CAAExF,cAAc,CAACiD,YAAa,CACnC4C,SAAS,CAAGL,KAAK,EAAKjH,cAAc,CAACuH,MAAM,CAACN,KAAK,CAAC,CAAE,CACpDE,UAAU,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,EACH,CAAC,cAENhF,IAAA,CAACrB,OAAO,EAAC+K,WAAW,CAAC,MAAM,CAAAzE,QAAA,CAAC,0BAAI,CAAS,CAAC,cAC1C/E,KAAA,CAAClC,KAAK,EAACoH,SAAS,CAAC,UAAU,CAACE,KAAK,CAAE,CAAER,KAAK,CAAE,MAAO,CAAE,CAAAG,QAAA,eACnD/E,KAAA,QAAA+E,QAAA,eACEjF,IAAA,CAACI,IAAI,EAAA6E,QAAA,CAAC,sBAAK,CAAM,CAAC,cAClBjF,IAAA,CAACnB,QAAQ,EACP8K,OAAO,CAAErI,cAAc,CAACgD,eAAgB,CACxCsF,WAAW,CAAC,SAAS,CACrB/H,MAAM,CAAG8H,OAAO,KAAAjH,MAAA,CAAQiH,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEnE,OAAO,CAAC,CAAC,CAAC,KAAI,CAChD,CAAC,EACC,CAAC,cACNtF,KAAA,QAAA+E,QAAA,eACEjF,IAAA,CAACI,IAAI,EAAA6E,QAAA,CAAC,sBAAK,CAAM,CAAC,cAClBjF,IAAA,CAACnB,QAAQ,EACP8K,OAAO,CAAErI,cAAc,CAACkD,gBAAiB,CACzCoF,WAAW,CAAC,SAAS,CACrB/H,MAAM,CAAG8H,OAAO,KAAAjH,MAAA,CAAQiH,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEnE,OAAO,CAAC,CAAC,CAAC,KAAI,CAChD,CAAC,EACC,CAAC,EACD,CAAC,EACL,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}