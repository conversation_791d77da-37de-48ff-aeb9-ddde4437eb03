import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Select,
  Row,
  Col,
  Tag,
  DatePicker,
  Tooltip,
  Modal,
  Descriptions,
  Timeline,
  Alert,
  Statistic,
  Progress,
  Tabs,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  ExportOutlined,
  ReloadOutlined,
  UserOutlined,
  FileTextOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SecurityScanOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { formatDate } from '../../utils';

const { Title, Text } = Typography;
const { Search } = Input;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  userRole: string;
  operation: string;
  module: string;
  resourceType: string;
  resourceId: string;
  resourceName: string;
  oldValue?: any;
  newValue?: any;
  reason?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  status: 'SUCCESS' | 'FAILED' | 'WARNING';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
}

const AuditLogPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [userFilter, setUserFilter] = useState<string | undefined>();
  const [moduleFilter, setModuleFilter] = useState<string | undefined>();
  const [operationFilter, setOperationFilter] = useState<string | undefined>();
  const [statusFilter, setStatusFilter] = useState<string | undefined>();
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {
    setDateRange(dates);
  };
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('list');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // TODO: 调用API获取审计日志数据
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('加载审计日志失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetail = (record: AuditLog) => {
    setSelectedLog(record);
    setDetailModalVisible(true);
  };

  const handleExport = () => {
    // TODO: 实现导出功能
    Modal.success({
      title: '导出成功',
      content: '审计日志已导出到Excel文件',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS': return 'success';
      case 'FAILED': return 'error';
      case 'WARNING': return 'warning';
      default: return 'default';
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'LOW': return 'green';
      case 'MEDIUM': return 'orange';
      case 'HIGH': return 'red';
      case 'CRITICAL': return 'purple';
      default: return 'default';
    }
  };

  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case 'CREATE': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'UPDATE': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'DELETE': return <WarningOutlined style={{ color: '#f5222d' }} />;
      case 'LOGIN': return <UserOutlined style={{ color: '#1890ff' }} />;
      case 'LOGOUT': return <UserOutlined style={{ color: '#8c8c8c' }} />;
      default: return <FileTextOutlined />;
    }
  };

  // 模拟审计日志数据
  const mockAuditLogs: AuditLog[] = [
    {
      id: '1',
      userId: 'user001',
      userName: '张三',
      userRole: 'BOM管理员',
      operation: 'UPDATE',
      module: 'BOM管理',
      resourceType: 'CoreBOM',
      resourceId: 'BOM-001',
      resourceName: '主控板BOM',
      oldValue: { version: '1.0', status: 'DRAFT' },
      newValue: { version: '1.1', status: 'ACTIVE' },
      reason: '更新产品规格',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      timestamp: '2024-03-25T10:30:00Z',
      status: 'SUCCESS',
      riskLevel: 'MEDIUM',
      description: '更新BOM版本并激活',
    },
    {
      id: '2',
      userId: 'user002',
      userName: '李四',
      userRole: '采购经理',
      operation: 'DELETE',
      module: '采购管理',
      resourceType: 'PurchaseOrder',
      resourceId: 'PO-2024-001',
      resourceName: '电子元器件采购订单',
      reason: '订单取消',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      timestamp: '2024-03-25T09:15:00Z',
      status: 'SUCCESS',
      riskLevel: 'HIGH',
      description: '删除采购订单',
    },
    {
      id: '3',
      userId: 'user003',
      userName: '王五',
      userRole: '系统管理员',
      operation: 'CREATE',
      module: '用户管理',
      resourceType: 'User',
      resourceId: 'user004',
      resourceName: '赵六',
      newValue: { userName: '赵六', role: 'OPERATOR', status: 'ACTIVE' },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      timestamp: '2024-03-25T08:45:00Z',
      status: 'SUCCESS',
      riskLevel: 'LOW',
      description: '创建新用户账户',
    },
    {
      id: '4',
      userId: 'user001',
      userName: '张三',
      userRole: 'BOM管理员',
      operation: 'LOGIN',
      module: '认证',
      resourceType: 'Session',
      resourceId: 'session-001',
      resourceName: '用户登录',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      timestamp: '2024-03-25T08:00:00Z',
      status: 'SUCCESS',
      riskLevel: 'LOW',
      description: '用户成功登录系统',
    },
    {
      id: '5',
      userId: 'unknown',
      userName: '未知用户',
      userRole: '',
      operation: 'LOGIN',
      module: '认证',
      resourceType: 'Session',
      resourceId: 'session-002',
      resourceName: '登录失败',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      timestamp: '2024-03-25T07:30:00Z',
      status: 'FAILED',
      riskLevel: 'CRITICAL',
      description: '多次登录失败，可能存在安全风险',
    },
  ];

  const auditLogColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
      render: (timestamp: string) => (
        <div>
          <div>{formatDate(timestamp, 'YYYY-MM-DD')}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {formatDate(timestamp, 'HH:mm:ss')}
          </Text>
        </div>
      ),
      sorter: (a: AuditLog, b: AuditLog) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),
    },
    {
      title: '用户',
      key: 'user',
      width: 120,
      render: (_: any, record: AuditLog) => (
        <div>
          <div>{record.userName}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.userRole}
          </Text>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'operation',
      width: 120,
      render: (_: any, record: AuditLog) => (
        <Space>
          {getOperationIcon(record.operation)}
          <span>{record.operation}</span>
        </Space>
      ),
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      width: 100,
    },
    {
      title: '资源',
      key: 'resource',
      width: 150,
      render: (_: any, record: AuditLog) => (
        <div>
          <div>{record.resourceName}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.resourceType}: {record.resourceId}
          </Text>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      width: 100,
      render: (level: string) => (
        <Tag color={getRiskLevelColor(level)}>{level}</Tag>
      ),
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right' as const,
      render: (_: any, record: AuditLog) => (
        <Tooltip title="查看详情">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          />
        </Tooltip>
      ),
    },
  ];

  const renderStatistics = () => {
    const totalLogs = mockAuditLogs.length;
    const successLogs = mockAuditLogs.filter(log => log.status === 'SUCCESS').length;
    const failedLogs = mockAuditLogs.filter(log => log.status === 'FAILED').length;
    const highRiskLogs = mockAuditLogs.filter(log => log.riskLevel === 'HIGH' || log.riskLevel === 'CRITICAL').length;

    return (
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="总操作数"
              value={totalLogs}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="成功操作"
              value={successLogs}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="失败操作"
              value={failedLogs}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="高风险操作"
              value={highRiskLogs}
              prefix={<SecurityScanOutlined />}
              valueStyle={{ color: '#d4380d' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  const renderSecurityAlerts = () => {
    const criticalLogs = mockAuditLogs.filter(log => log.riskLevel === 'CRITICAL');
    
    if (criticalLogs.length === 0) {
      return null;
    }

    return (
      <Alert
        message="安全警告"
        description={`检测到 ${criticalLogs.length} 个高风险操作，请及时处理`}
        type="error"
        showIcon
        closable
        style={{ marginBottom: 16 }}
        action={
          <Button size="small" danger>
            查看详情
          </Button>
        }
      />
    );
  };

  const renderAuditTimeline = () => {
    const timelineItems = mockAuditLogs.slice(0, 10).map(log => ({
      color: log.status === 'FAILED' ? 'red' : log.riskLevel === 'HIGH' ? 'orange' : 'blue',
      children: (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Space>
              {getOperationIcon(log.operation)}
              <Text strong>{log.userName}</Text>
              <Text>{log.operation}</Text>
              <Text type="secondary">{log.resourceName}</Text>
            </Space>
          </div>
          <div style={{ marginBottom: 4 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {formatDate(log.timestamp)} • {log.ipAddress}
            </Text>
          </div>
          <div>
            <Tag color={getStatusColor(log.status)}>{log.status}</Tag>
            <Tag color={getRiskLevelColor(log.riskLevel)}>{log.riskLevel}</Tag>
          </div>
        </div>
      ),
    }));

    return (
      <Card title="操作时间线" size="small">
        <Timeline items={timelineItems} />
      </Card>
    );
  };

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>审计日志</Title>
            <Text type="secondary">
              系统操作记录和安全审计跟踪
            </Text>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索用户、操作、资源"
                allowClear
                style={{ width: 200 }}
                onSearch={(value) => {
                  // TODO: 实现搜索功能
                  console.log('搜索:', value);
                }}
              />
              <Select
                placeholder="用户"
                allowClear
                style={{ width: 100 }}
                value={userFilter}
                onChange={setUserFilter}
                options={[
                  { label: '张三', value: 'user001' },
                  { label: '李四', value: 'user002' },
                  { label: '王五', value: 'user003' },
                ]}
              />
              <Select
                placeholder="模块"
                allowClear
                style={{ width: 100 }}
                value={moduleFilter}
                onChange={setModuleFilter}
                options={[
                  { label: 'BOM管理', value: 'BOM管理' },
                  { label: '采购管理', value: '采购管理' },
                  { label: '用户管理', value: '用户管理' },
                  { label: '认证', value: '认证' },
                ]}
              />
              <Select
                placeholder="操作类型"
                allowClear
                style={{ width: 100 }}
                value={operationFilter}
                onChange={setOperationFilter}
                options={[
                  { label: '创建', value: 'CREATE' },
                  { label: '更新', value: 'UPDATE' },
                  { label: '删除', value: 'DELETE' },
                  { label: '登录', value: 'LOGIN' },
                  { label: '登出', value: 'LOGOUT' },
                ]}
              />
              <Select
                placeholder="状态"
                allowClear
                style={{ width: 100 }}
                value={statusFilter}
                onChange={setStatusFilter}
                options={[
                  { label: '成功', value: 'SUCCESS' },
                  { label: '失败', value: 'FAILED' },
                  { label: '警告', value: 'WARNING' },
                ]}
              />
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                style={{ width: 240 }}
              />
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button icon={<ReloadOutlined />} onClick={loadData}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>

        {renderSecurityAlerts()}

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="审计列表" key="list">
            {renderStatistics()}
            <Table
              columns={auditLogColumns}
              dataSource={mockAuditLogs}
              loading={loading}
              rowKey="id"
              scroll={{ x: 1200 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </TabPane>

          <TabPane tab="操作时间线" key="timeline">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={16}>
                {renderAuditTimeline()}
              </Col>
              <Col xs={24} lg={8}>
                {renderStatistics()}
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="审计日志详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {selectedLog && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="操作时间">
              {formatDate(selectedLog.timestamp)}
            </Descriptions.Item>
            <Descriptions.Item label="操作用户">
              {selectedLog.userName} ({selectedLog.userRole})
            </Descriptions.Item>
            <Descriptions.Item label="操作类型">
              <Space>
                {getOperationIcon(selectedLog.operation)}
                {selectedLog.operation}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="所属模块">
              {selectedLog.module}
            </Descriptions.Item>
            <Descriptions.Item label="资源类型">
              {selectedLog.resourceType}
            </Descriptions.Item>
            <Descriptions.Item label="资源ID">
              {selectedLog.resourceId}
            </Descriptions.Item>
            <Descriptions.Item label="资源名称" span={2}>
              {selectedLog.resourceName}
            </Descriptions.Item>
            <Descriptions.Item label="操作状态">
              <Tag color={getStatusColor(selectedLog.status)}>
                {selectedLog.status}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="风险等级">
              <Tag color={getRiskLevelColor(selectedLog.riskLevel)}>
                {selectedLog.riskLevel}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="IP地址">
              {selectedLog.ipAddress}
            </Descriptions.Item>
            <Descriptions.Item label="用户代理" span={2}>
              <Text code style={{ fontSize: 12 }}>
                {selectedLog.userAgent}
              </Text>
            </Descriptions.Item>
            {selectedLog.reason && (
              <Descriptions.Item label="操作原因" span={2}>
                {selectedLog.reason}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="操作描述" span={2}>
              {selectedLog.description}
            </Descriptions.Item>
            {selectedLog.oldValue && (
              <Descriptions.Item label="修改前" span={2}>
                <pre style={{ fontSize: 12, background: '#f5f5f5', padding: 8 }}>
                  {JSON.stringify(selectedLog.oldValue, null, 2)}
                </pre>
              </Descriptions.Item>
            )}
            {selectedLog.newValue && (
              <Descriptions.Item label="修改后" span={2}>
                <pre style={{ fontSize: 12, background: '#f5f5f5', padding: 8 }}>
                  {JSON.stringify(selectedLog.newValue, null, 2)}
                </pre>
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default AuditLogPage;