import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Row,
  Col,
  Typography,
  Tag,
  Modal,
  Form,
  Descriptions,
  Tabs,
  Alert,
  Badge,
  Tooltip,
  QRCode,
  message,
  Divider,
  Timeline,
  Upload,
  DatePicker,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ExportOutlined,
  ImportOutlined,
  QrcodeOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  PrinterOutlined,
  ScanOutlined,
  HistoryOutlined,
  ToolOutlined,
  FileTextOutlined,
  CloudUploadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface AsBuiltBOM {
  id: string;
  bomCode: string;
  bomName: string;
  productModel: string;
  serialNumber: string;
  orderNumber: string;
  customerName: string;
  productionDate: string;
  shipmentDate: string;
  qrCode: string;
  status: 'active' | 'shipped' | 'maintenance' | 'retired';
  version: string;
  description: string;
  createdBy: string;
  createdAt: string;
  lastModified: string;
  totalItems: number;
  totalValue: number;
  actualItems: AsBuiltItem[];
  maintenanceRecords: MaintenanceRecord[];
  warrantyInfo: WarrantyInfo;
}

interface AsBuiltItem {
  id: string;
  itemCode: string;
  itemName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  supplier: string;
  batchNumber: string;
  serialNumber?: string;
  installDate: string;
  status: 'installed' | 'replaced' | 'removed';
  remarks?: string;
}

interface MaintenanceRecord {
  id: string;
  date: string;
  type: 'preventive' | 'corrective' | 'upgrade';
  description: string;
  technician: string;
  partsUsed: string[];
  status: 'completed' | 'in_progress' | 'scheduled';
  cost: number;
  nextMaintenanceDate?: string;
}

interface WarrantyInfo {
  warrantyPeriod: number; // 月
  warrantyStartDate: string;
  warrantyEndDate: string;
  warrantyStatus: 'active' | 'expired' | 'void';
  warrantyTerms: string;
}

const AsBuiltBOMPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [selectedBOM, setSelectedBOM] = useState<AsBuiltBOM | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'view' | 'create' | 'edit'>('view');
  const [qrModalVisible, setQrModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 模拟As-Built BOM数据
  const mockAsBuiltBOMs: AsBuiltBOM[] = [
    {
      id: '1',
      bomCode: 'ASBOM-5G-ANT-001',
      bomName: '5G天线系统As-Built BOM',
      productModel: '5G-ANT-64T64R',
      serialNumber: 'SN202401001',
      orderNumber: 'ORD202401001',
      customerName: '中国移动',
      productionDate: '2024-03-15T00:00:00Z',
      shipmentDate: '2024-03-20T00:00:00Z',
      qrCode: 'https://example.com/qr/SN202401001',
      status: 'shipped',
      version: 'V1.0',
      description: '5G天线系统实际构建BOM，记录实际使用的物料和序列号',
      createdBy: '生产工程师',
      createdAt: '2024-03-15T00:00:00Z',
      lastModified: '2024-03-20T00:00:00Z',
      totalItems: 25,
      totalValue: 15600.00,
      actualItems: [
        {
          id: '1',
          itemCode: 'ANT-ELEM-001',
          itemName: '天线阵元',
          specification: 'ELEM-64T64R',
          quantity: 64,
          unit: 'PCS',
          unitPrice: 150.00,
          totalPrice: 9600.00,
          supplier: '华为技术',
          batchNumber: 'BATCH-20240301',
          serialNumber: 'ELEM-001-001~064',
          installDate: '2024-03-15T00:00:00Z',
          status: 'installed',
          remarks: '主要天线阵元',
        },
        {
          id: '2',
          itemCode: 'RF-CONN-001',
          itemName: '射频连接器',
          specification: 'N-TYPE-50Ω',
          quantity: 8,
          unit: 'PCS',
          unitPrice: 25.00,
          totalPrice: 200.00,
          supplier: '安费诺',
          batchNumber: 'BATCH-20240305',
          installDate: '2024-03-15T00:00:00Z',
          status: 'installed',
        },
      ],
      maintenanceRecords: [
        {
          id: '1',
          date: '2024-03-25T00:00:00Z',
          type: 'preventive',
          description: '定期维护检查，清洁天线表面，检查连接器',
          technician: '维护工程师A',
          partsUsed: [],
          status: 'completed',
          cost: 200.00,
          nextMaintenanceDate: '2024-06-25T00:00:00Z',
        },
      ],
      warrantyInfo: {
        warrantyPeriod: 24,
        warrantyStartDate: '2024-03-20T00:00:00Z',
        warrantyEndDate: '2026-03-20T00:00:00Z',
        warrantyStatus: 'active',
        warrantyTerms: '24个月质保，包含材料和人工费用',
      },
    },
    {
      id: '2',
      bomCode: 'ASBOM-5G-RRU-001',
      bomName: '5G射频单元As-Built BOM',
      productModel: '5G-RRU-3200W',
      serialNumber: 'SN202401002',
      orderNumber: 'ORD202401002',
      customerName: '中国联通',
      productionDate: '2024-03-18T00:00:00Z',
      shipmentDate: '2024-03-22T00:00:00Z',
      qrCode: 'https://example.com/qr/SN202401002',
      status: 'maintenance',
      version: 'V1.1',
      description: '5G射频拉远单元实际构建BOM',
      createdBy: '生产工程师',
      createdAt: '2024-03-18T00:00:00Z',
      lastModified: '2024-04-01T00:00:00Z',
      totalItems: 18,
      totalValue: 25800.00,
      actualItems: [
        {
          id: '1',
          itemCode: 'PA-MOD-001',
          itemName: '功率放大器模块',
          specification: 'PA-5G-3200W',
          quantity: 2,
          unit: 'PCS',
          unitPrice: 2500.00,
          totalPrice: 5000.00,
          supplier: '中兴通讯',
          batchNumber: 'BATCH-20240310',
          serialNumber: 'PA-001-001, PA-001-002',
          installDate: '2024-03-18T00:00:00Z',
          status: 'replaced',
          remarks: '其中一个模块在维护时更换',
        },
      ],
      maintenanceRecords: [
        {
          id: '1',
          date: '2024-04-01T00:00:00Z',
          type: 'corrective',
          description: '功率放大器模块故障，更换新模块',
          technician: '维护工程师B',
          partsUsed: ['PA-MOD-001'],
          status: 'completed',
          cost: 2800.00,
        },
      ],
      warrantyInfo: {
        warrantyPeriod: 36,
        warrantyStartDate: '2024-03-22T00:00:00Z',
        warrantyEndDate: '2027-03-22T00:00:00Z',
        warrantyStatus: 'active',
        warrantyTerms: '36个月质保，包含材料和人工费用',
      },
    },
  ];

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'blue',
      shipped: 'green',
      maintenance: 'orange',
      retired: 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      active: '生产中',
      shipped: '已发货',
      maintenance: '维护中',
      retired: '已退役',
    };
    return texts[status as keyof typeof texts] || status;
  };

  const handleView = (record: AsBuiltBOM) => {
    setSelectedBOM(record);
    setModalType('view');
    setIsModalVisible(true);
  };

  const handleEdit = (record: AsBuiltBOM) => {
    setSelectedBOM(record);
    setModalType('edit');
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleCreate = () => {
    setSelectedBOM(null);
    setModalType('create');
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleQRCode = (record: AsBuiltBOM) => {
    setSelectedBOM(record);
    setQrModalVisible(true);
  };

  const handleExport = () => {
    message.success('导出功能开发中...');
  };

  const handleImport = () => {
    message.success('导入功能开发中...');
  };

  const columns: ColumnsType<AsBuiltBOM> = [
    {
      title: 'BOM编码',
      dataIndex: 'bomCode',
      key: 'bomCode',
      width: 150,
      fixed: 'left',
    },
    {
      title: 'BOM名称',
      dataIndex: 'bomName',
      key: 'bomName',
      ellipsis: true,
    },
    {
      title: '产品型号',
      dataIndex: 'productModel',
      key: 'productModel',
      width: 120,
    },
    {
      title: '序列号',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 120,
      render: (text: string) => (
        <Text code>{text}</Text>
      ),
    },
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120,
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 120,
    },
    {
      title: '生产日期',
      dataIndex: 'productionDate',
      key: 'productionDate',
      width: 100,
      render: (date: string) => formatDate(date),
    },
    {
      title: '发货日期',
      dataIndex: 'shipmentDate',
      key: 'shipmentDate',
      width: 100,
      render: (date: string) => formatDate(date),
    },
    {
      title: '项目数',
      dataIndex: 'totalItems',
      key: 'totalItems',
      width: 80,
      render: (count: number) => (
        <Badge count={count} showZero color="#1890ff" />
      ),
    },
    {
      title: '总价值',
      dataIndex: 'totalValue',
      key: 'totalValue',
      width: 100,
      render: (value: number) => formatCurrency(value),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="二维码">
            <Button
              type="text"
              icon={<QrcodeOutlined />}
              onClick={() => handleQRCode(record)}
            />
          </Tooltip>
          <Tooltip title="导出">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => message.success('导出功能开发中...')}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const itemColumns: ColumnsType<AsBuiltItem> = [
    {
      title: '物料编码',
      dataIndex: 'itemCode',
      key: 'itemCode',
      width: 120,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      key: 'itemName',
      ellipsis: true,
    },
    {
      title: '规格',
      dataIndex: 'specification',
      key: 'specification',
      width: 150,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 60,
    },
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      key: 'batchNumber',
      width: 120,
      render: (text: string) => (
        <Text code>{text}</Text>
      ),
    },
    {
      title: '序列号',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 150,
      render: (text: string) => text && (
        <Text code>{text}</Text>
      ),
    },
    {
      title: '安装日期',
      dataIndex: 'installDate',
      key: 'installDate',
      width: 100,
      render: (date: string) => formatDate(date),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const colors = {
          installed: 'green',
          replaced: 'orange',
          removed: 'red',
        };
        const texts = {
          installed: '已安装',
          replaced: '已更换',
          removed: '已移除',
        };
        return (
          <Tag color={colors[status as keyof typeof colors]}>
            {texts[status as keyof typeof texts]}
          </Tag>
        );
      },
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (price: number) => formatCurrency(price),
    },
    {
      title: '总价',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 100,
      render: (price: number) => formatCurrency(price),
    },
  ];

  const renderStatistics = () => {
    const totalBOMs = mockAsBuiltBOMs.length;
    const activeBOMs = mockAsBuiltBOMs.filter(bom => bom.status === 'active').length;
    const shippedBOMs = mockAsBuiltBOMs.filter(bom => bom.status === 'shipped').length;
    const maintenanceBOMs = mockAsBuiltBOMs.filter(bom => bom.status === 'maintenance').length;

    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {totalBOMs}
              </div>
              <div style={{ color: '#666' }}>总数量</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {shippedBOMs}
              </div>
              <div style={{ color: '#666' }}>已发货</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                {maintenanceBOMs}
              </div>
              <div style={{ color: '#666' }}>维护中</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {activeBOMs}
              </div>
              <div style={{ color: '#666' }}>生产中</div>
            </div>
          </Card>
        </Col>
      </Row>
    );
  };

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              As-Built BOM管理
            </Title>
            <Text type="secondary">
              管理产品实际构建BOM，记录实际使用的物料、序列号和维护历史
            </Text>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索BOM编码、序列号"
                allowClear
                style={{ width: 200 }}
                onSearch={setSearchKeyword}
              />
              <Select
                placeholder="状态"
                allowClear
                style={{ width: 100 }}
                value={statusFilter}
                onChange={setStatusFilter}
                options={[
                  { label: '生产中', value: 'active' },
                  { label: '已发货', value: 'shipped' },
                  { label: '维护中', value: 'maintenance' },
                  { label: '已退役', value: 'retired' },
                ]}
              />
              <Button icon={<ScanOutlined />}>
                扫码查询
              </Button>
              <Button icon={<ImportOutlined />} onClick={handleImport}>
                导入
              </Button>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                新建As-Built BOM
              </Button>
            </Space>
          </Col>
        </Row>

        <Alert
          message="质量追溯"
          description="As-Built BOM记录了产品的实际构建信息，支持完整的质量追溯和维护管理。"
          type="info"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />

        {renderStatistics()}

        <Table
          columns={columns}
          dataSource={mockAsBuiltBOMs}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1400 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 详情/编辑模态框 */}
      <Modal
        title={
          modalType === 'view' ? 'As-Built BOM详情' :
          modalType === 'edit' ? '编辑As-Built BOM' : '新建As-Built BOM'
        }
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        width={1200}
        footer={
          modalType === 'view' ? [
            <Button key="close" onClick={() => setIsModalVisible(false)}>
              关闭
            </Button>
          ] : [
            <Button key="cancel" onClick={() => setIsModalVisible(false)}>
              取消
            </Button>,
            <Button key="submit" type="primary">
              {modalType === 'edit' ? '保存' : '创建'}
            </Button>
          ]
        }
      >
        {selectedBOM && modalType === 'view' && (
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="BOM编码">{selectedBOM.bomCode}</Descriptions.Item>
                <Descriptions.Item label="BOM名称">{selectedBOM.bomName}</Descriptions.Item>
                <Descriptions.Item label="产品型号">{selectedBOM.productModel}</Descriptions.Item>
                <Descriptions.Item label="序列号">
                  <Text code>{selectedBOM.serialNumber}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="订单号">{selectedBOM.orderNumber}</Descriptions.Item>
                <Descriptions.Item label="客户名称">{selectedBOM.customerName}</Descriptions.Item>
                <Descriptions.Item label="生产日期">{formatDate(selectedBOM.productionDate)}</Descriptions.Item>
                <Descriptions.Item label="发货日期">{formatDate(selectedBOM.shipmentDate)}</Descriptions.Item>
                <Descriptions.Item label="版本">{selectedBOM.version}</Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={getStatusColor(selectedBOM.status)}>
                    {getStatusText(selectedBOM.status)}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="项目数量">{selectedBOM.totalItems}</Descriptions.Item>
                <Descriptions.Item label="总价值">{formatCurrency(selectedBOM.totalValue)}</Descriptions.Item>
                <Descriptions.Item label="创建人">{selectedBOM.createdBy}</Descriptions.Item>
                <Descriptions.Item label="创建时间">{formatDate(selectedBOM.createdAt)}</Descriptions.Item>
                <Descriptions.Item label="描述" span={2}>{selectedBOM.description}</Descriptions.Item>
              </Descriptions>
            </TabPane>
            <TabPane tab="物料明细" key="items">
              <Table
                columns={itemColumns}
                dataSource={selectedBOM.actualItems}
                rowKey="id"
                scroll={{ x: 1200 }}
                pagination={false}
                size="small"
              />
            </TabPane>
            <TabPane tab="维护记录" key="maintenance">
              <Timeline>
                {selectedBOM.maintenanceRecords.map((record) => (
                  <Timeline.Item
                    key={record.id}
                    color={record.status === 'completed' ? 'green' : 'blue'}
                  >
                    <div>
                      <Text strong>{record.type === 'preventive' ? '预防性维护' : 
                                   record.type === 'corrective' ? '纠正性维护' : '升级维护'}</Text>
                      <Text type="secondary" style={{ marginLeft: 8 }}>
                        {formatDate(record.date)}
                      </Text>
                    </div>
                    <div style={{ marginTop: 4 }}>
                      <Text>{record.description}</Text>
                    </div>
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary">技术员: {record.technician}</Text>
                      <Text type="secondary" style={{ marginLeft: 16 }}>
                        费用: {formatCurrency(record.cost)}
                      </Text>
                      <Tag
                        color={record.status === 'completed' ? 'green' : 'processing'}
                        style={{ marginLeft: 8 }}
                      >
                        {record.status === 'completed' ? '已完成' : 
                         record.status === 'in_progress' ? '进行中' : '已计划'}
                      </Tag>
                    </div>
                    {record.partsUsed.length > 0 && (
                      <div style={{ marginTop: 4 }}>
                        <Text type="secondary">使用备件: {record.partsUsed.join(', ')}</Text>
                      </div>
                    )}
                  </Timeline.Item>
                ))}
              </Timeline>
            </TabPane>
            <TabPane tab="质保信息" key="warranty">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="质保期">{selectedBOM.warrantyInfo.warrantyPeriod}个月</Descriptions.Item>
                <Descriptions.Item label="质保状态">
                  <Tag color={selectedBOM.warrantyInfo.warrantyStatus === 'active' ? 'green' : 'red'}>
                    {selectedBOM.warrantyInfo.warrantyStatus === 'active' ? '有效' : 
                     selectedBOM.warrantyInfo.warrantyStatus === 'expired' ? '已过期' : '已失效'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="质保开始日期">
                  {formatDate(selectedBOM.warrantyInfo.warrantyStartDate)}
                </Descriptions.Item>
                <Descriptions.Item label="质保结束日期">
                  {formatDate(selectedBOM.warrantyInfo.warrantyEndDate)}
                </Descriptions.Item>
                <Descriptions.Item label="质保条款" span={2}>
                  {selectedBOM.warrantyInfo.warrantyTerms}
                </Descriptions.Item>
              </Descriptions>
            </TabPane>
          </Tabs>
        )}

        {modalType !== 'view' && (
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="BOM编码" name="bomCode" rules={[{ required: true }]}>
                  <Input placeholder="请输入BOM编码" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="BOM名称" name="bomName" rules={[{ required: true }]}>
                  <Input placeholder="请输入BOM名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="产品型号" name="productModel" rules={[{ required: true }]}>
                  <Input placeholder="请输入产品型号" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="序列号" name="serialNumber" rules={[{ required: true }]}>
                  <Input placeholder="请输入序列号" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="订单号" name="orderNumber" rules={[{ required: true }]}>
                  <Input placeholder="请输入订单号" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="客户名称" name="customerName" rules={[{ required: true }]}>
                  <Input placeholder="请输入客户名称" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="描述" name="description">
                  <TextArea rows={3} placeholder="请输入描述" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        )}
      </Modal>

      {/* 二维码模态框 */}
      <Modal
        title="设备二维码"
        open={qrModalVisible}
        onCancel={() => setQrModalVisible(false)}
        footer={[
          <Button key="print" icon={<PrinterOutlined />}>
            打印
          </Button>,
          <Button key="download" icon={<DownloadOutlined />}>
            下载
          </Button>,
          <Button key="close" onClick={() => setQrModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        {selectedBOM && (
          <div style={{ textAlign: 'center' }}>
            <QRCode
              value={`${selectedBOM.qrCode}?sn=${selectedBOM.serialNumber}`}
              size={200}
            />
            <Divider />
            <Descriptions column={1} size="small">
              <Descriptions.Item label="产品型号">{selectedBOM.productModel}</Descriptions.Item>
              <Descriptions.Item label="序列号">{selectedBOM.serialNumber}</Descriptions.Item>
              <Descriptions.Item label="生产日期">{formatDate(selectedBOM.productionDate)}</Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AsBuiltBOMPage;