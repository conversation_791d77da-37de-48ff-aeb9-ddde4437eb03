{"ast": null, "code": "import React,{useEffect,useState,useCallback}from'react';import{useNavigate}from'react-router-dom';import{Table,Button,Space,Input,Select,Card,Tag,message,Modal,Form,Typography,Row,Col,Tooltip,Dropdown,Upload,Progress,Alert,Divider,Timeline,Descriptions,Checkbox,Radio}from'antd';import{PlusOutlined,EditOutlined,DeleteOutlined,EyeOutlined,CopyOutlined,LockOutlined,UnlockOutlined,MoreOutlined,ExportOutlined,ImportOutlined,HistoryOutlined,DownloadOutlined,FileExcelOutlined,CheckCircleOutlined,ExclamationCircleOutlined,ClockCircleOutlined}from'@ant-design/icons';import{useAppDispatch,useAppSelector}from'../../hooks/redux';import{fetchCoreBOMs,deleteCoreBOM,freezeCoreBOM}from'../../store/slices/bomSlice';import{ROUTES,BOM_STATUS}from'../../constants';import{formatDate}from'../../utils';import{ConfirmDialog}from'../../components';import{errorHandler,ErrorType}from'../../utils/errorHandler';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const{Search}=Input;const CoreBOMListPage=()=>{const navigate=useNavigate();const dispatch=useAppDispatch();const{coreBOMs,loading,pagination}=useAppSelector(state=>state.bom);const[searchKeyword,setSearchKeyword]=useState('');const[statusFilter,setStatusFilter]=useState('');const[copyModalVisible,setCopyModalVisible]=useState(false);const[copyingBOM,setCopyingBOM]=useState(null);const[copyForm]=Form.useForm();// 版本历史相关状态\nconst[versionHistoryVisible,setVersionHistoryVisible]=useState(false);const[currentBOMForHistory,setCurrentBOMForHistory]=useState(null);// 导出导入相关状态\nconst[exportModalVisible,setExportModalVisible]=useState(false);const[importModalVisible,setImportModalVisible]=useState(false);const[exportFormat,setExportFormat]=useState('excel');const[exportFields,setExportFields]=useState(['code','name','version','status','description']);const[importProgress,setImportProgress]=useState(0);const[importStatus,setImportStatus]=useState(null);const[importResult,setImportResult]=useState(null);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[exportForm]=Form.useForm();const[importForm]=Form.useForm();useEffect(()=>{loadData();},[pagination.current,pagination.pageSize,searchKeyword,statusFilter]);const loadData=useCallback(()=>{dispatch(fetchCoreBOMs({page:pagination.current,pageSize:pagination.pageSize,keyword:searchKeyword}));},[dispatch,pagination.current,pagination.pageSize,searchKeyword]);const handleSearch=useCallback(value=>{setSearchKeyword(value);},[]);const handleStatusFilter=useCallback(value=>{setStatusFilter(value);},[]);const handleCreate=useCallback(()=>{navigate(ROUTES.CORE_BOM_CREATE);},[navigate]);const handleEdit=useCallback(record=>{navigate(ROUTES.CORE_BOM_EDIT.replace(':id',record.id));},[navigate]);const handleView=useCallback(record=>{navigate(ROUTES.CORE_BOM_VIEW.replace(':id',record.id));},[navigate]);const handleDelete=useCallback(async record=>{ConfirmDialog.confirm({title:'确认删除',content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u4EE5\\u4E0BBOM\\u5417\\uFF1F\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"BOM\\u7F16\\u7801\\uFF1A\"}),record.code]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"BOM\\u540D\\u79F0\\uFF1A\"}),record.name]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#ff4d4f',marginTop:12},children:\"\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\uFF01\"})]}),type:'warning',onConfirm:async()=>{try{await dispatch(deleteCoreBOM(record.id)).unwrap();message.success('删除成功');loadData();}catch(error){errorHandler.handleError({type:ErrorType.BUSINESS,message:error.message||'删除失败',details:error});}}});},[dispatch,loadData]);const handleFreeze=useCallback(async record=>{const isActive=record.status===BOM_STATUS.ACTIVE;const action=isActive?'冻结':'解冻';ConfirmDialog.confirm({title:\"\\u786E\\u8BA4\".concat(action),content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u786E\\u5B9A\\u8981\",action,\"\\u4EE5\\u4E0BBOM\\u5417\\uFF1F\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"BOM\\u7F16\\u7801\\uFF1A\"}),record.code]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"BOM\\u540D\\u79F0\\uFF1A\"}),record.name]}),isActive&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#faad14',marginTop:12},children:\"\\u51BB\\u7ED3\\u540E\\u8BE5BOM\\u5C06\\u65E0\\u6CD5\\u88AB\\u4F7F\\u7528\\uFF01\"})]}),type:isActive?'warning':'info',onConfirm:async()=>{try{await dispatch(freezeCoreBOM(record.id)).unwrap();message.success(\"\".concat(action,\"\\u6210\\u529F\"));loadData();}catch(error){errorHandler.handleError({type:ErrorType.BUSINESS,message:error.message||\"\".concat(action,\"\\u5931\\u8D25\"),details:error});}}});},[dispatch,loadData]);const handleCopy=useCallback(record=>{setCopyingBOM(record);copyForm.setFieldsValue({name:\"\".concat(record.name,\"_\\u526F\\u672C\"),code:\"\".concat(record.code,\"_COPY\")});setCopyModalVisible(true);},[copyForm]);const handleCopyConfirm=async()=>{try{const values=await copyForm.validateFields();// TODO: 实现复制BOM的API调用\nmessage.success('复制成功');setCopyModalVisible(false);loadData();}catch(error){message.error('复制失败');}};const handleVersionHistory=record=>{setCurrentBOMForHistory(record);setVersionHistoryVisible(true);};const handleExport=()=>{setExportModalVisible(true);};const handleImport=()=>{setImportModalVisible(true);};// 下载导入模板\nconst handleDownloadTemplate=()=>{// TODO: 实现下载模板功能\nconst templateData=[{'BOM编码':'BOM001','BOM名称':'示例BOM','版本':'V1.0','状态':'draft','描述':'这是一个示例BOM'}];// 创建CSV内容\nconst headers=Object.keys(templateData[0]);const csvContent=[headers.join(','),...templateData.map(row=>Object.values(row).join(','))].join('\\n');// 下载文件\nconst blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const link=document.createElement('a');link.href=URL.createObjectURL(blob);link.download='core_bom_template.csv';link.click();message.success('模板下载成功');};// 执行导出\nconst handleExportConfirm=async()=>{try{const values=await exportForm.validateFields();// 获取要导出的数据\nlet exportData=mockData;if(selectedRowKeys.length>0){exportData=mockData.filter(item=>selectedRowKeys.includes(item.id));}// 根据选择的字段过滤数据\nconst filteredData=exportData.map(item=>{const filteredItem={};exportFields.forEach(field=>{switch(field){case'code':filteredItem['BOM编码']=item.code;break;case'name':filteredItem['BOM名称']=item.name;break;case'version':filteredItem['版本']=item.version;break;case'status':filteredItem['状态']=getStatusText(item.status);break;case'description':filteredItem['描述']=item.description;break;case'createdBy':filteredItem['创建人']=item.createdBy;break;case'createdAt':filteredItem['创建时间']=formatDate(item.createdAt);break;case'updatedAt':filteredItem['更新时间']=formatDate(item.updatedAt);break;}});return filteredItem;});// 创建并下载文件\nif(exportFormat==='excel'){// TODO: 使用xlsx库导出Excel\nmessage.info('Excel导出功能需要集成xlsx库');}else{// CSV导出\nconst headers=Object.keys(filteredData[0]||{});const csvContent=[headers.join(','),...filteredData.map(row=>Object.values(row).join(','))].join('\\n');const blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const link=document.createElement('a');link.href=URL.createObjectURL(blob);link.download=\"core_bom_export_\".concat(new Date().getTime(),\".csv\");link.click();}setExportModalVisible(false);message.success('导出成功');}catch(error){message.error('导出失败');}};// 处理文件上传\nconst handleFileUpload=file=>{setImportStatus('uploading');setImportProgress(0);// 模拟上传进度\nconst interval=setInterval(()=>{setImportProgress(prev=>{if(prev>=100){clearInterval(interval);setImportStatus('processing');// 模拟处理过程\nsetTimeout(()=>{processImportFile(file);},1000);return 100;}return prev+10;});},200);return false;// 阻止默认上传\n};// 处理导入文件\nconst processImportFile=file=>{const reader=new FileReader();reader.onload=e=>{try{var _e$target;const content=(_e$target=e.target)===null||_e$target===void 0?void 0:_e$target.result;const lines=content.split('\\n');const headers=lines[0].split(',');const importedData=[];const errors=[];for(let i=1;i<lines.length;i++){if(lines[i].trim()){const values=lines[i].split(',');const row={};headers.forEach((header,index)=>{var _values$index;row[header.trim()]=(_values$index=values[index])===null||_values$index===void 0?void 0:_values$index.trim();});// 验证数据\nif(!row['BOM编码']||!row['BOM名称']){errors.push(\"\\u7B2C\".concat(i+1,\"\\u884C\\uFF1ABOM\\u7F16\\u7801\\u548C\\u540D\\u79F0\\u4E0D\\u80FD\\u4E3A\\u7A7A\"));}else{importedData.push({code:row['BOM编码'],name:row['BOM名称'],version:row['版本']||'V1.0',status:row['状态']||'draft',description:row['描述']||''});}}}setImportResult({total:lines.length-1,success:importedData.length,errors:errors.length,data:importedData,errorMessages:errors});setImportStatus(errors.length>0?'error':'success');if(errors.length===0){message.success(\"\\u6210\\u529F\\u5BFC\\u5165\".concat(importedData.length,\"\\u6761\\u6570\\u636E\"));// TODO: 调用API保存数据\n}else{message.warning(\"\\u5BFC\\u5165\\u5B8C\\u6210\\uFF0C\".concat(errors.length,\"\\u6761\\u6570\\u636E\\u6709\\u9519\\u8BEF\"));}}catch(error){setImportStatus('error');setImportResult({total:0,success:0,errors:1,errorMessages:['文件格式错误，请检查文件内容']});message.error('文件解析失败');}};reader.readAsText(file);};// 表格行选择\nconst rowSelection={selectedRowKeys,onChange:newSelectedRowKeys=>{setSelectedRowKeys(newSelectedRowKeys);},onSelectAll:(selected,selectedRows,changeRows)=>{console.log('Select all:',selected,selectedRows,changeRows);}};// 清空选择\nconst handleClearSelection=()=>{setSelectedRowKeys([]);};// 导出选中项\nconst handleExportSelected=()=>{if(selectedRowKeys.length===0){message.warning('请先选择要导出的数据');return;}setExportModalVisible(true);};// 模拟版本历史数据\nconst getVersionHistory=bom=>{return[{version:'V1.2',status:'active',description:'优化物料清单，更新供应商信息',createdBy:'李工程师',createdAt:'2024-03-25 14:30:00',changes:['更新供应商信息','调整物料数量','优化成本结构']},{version:'V1.1',status:'frozen',description:'修复物料规格错误',createdBy:'王工程师',createdAt:'2024-03-20 10:15:00',changes:['修正物料规格','更新技术参数']},{version:'V1.0',status:'obsolete',description:'初始版本',createdBy:'张工程师',createdAt:'2024-03-15 09:00:00',changes:['创建初始BOM结构']}];};const getStatusColor=status=>{switch(status){case BOM_STATUS.DRAFT:return'default';case BOM_STATUS.ACTIVE:return'success';case BOM_STATUS.FROZEN:return'blue';case BOM_STATUS.OBSOLETE:return'error';default:return'default';}};const getStatusText=status=>{switch(status){case BOM_STATUS.DRAFT:return'草稿';case BOM_STATUS.ACTIVE:return'激活';case BOM_STATUS.FROZEN:return'冻结';case BOM_STATUS.OBSOLETE:return'废弃';default:return status;}};// 可导出字段选项\nconst exportFieldOptions=[{label:'BOM编码',value:'code'},{label:'BOM名称',value:'name'},{label:'版本',value:'version'},{label:'状态',value:'status'},{label:'描述',value:'description'},{label:'创建人',value:'createdBy'},{label:'创建时间',value:'createdAt'},{label:'更新时间',value:'updatedAt'}];const getActionMenuItems=record=>[{key:'copy',icon:/*#__PURE__*/_jsx(CopyOutlined,{}),label:'复制',onClick:()=>handleCopy(record)},{key:'history',icon:/*#__PURE__*/_jsx(HistoryOutlined,{}),label:'版本历史',onClick:()=>handleVersionHistory(record)},{key:'freeze',icon:record.status===BOM_STATUS.FROZEN?/*#__PURE__*/_jsx(UnlockOutlined,{}):/*#__PURE__*/_jsx(LockOutlined,{}),label:record.status===BOM_STATUS.FROZEN?'解冻':'冻结',onClick:()=>handleFreeze(record),disabled:record.status===BOM_STATUS.DRAFT}];const columns=[{title:'BOM编码',dataIndex:'code',key:'code',width:120,render:(text,record)=>/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:()=>handleView(record),children:text})},{title:'BOM名称',dataIndex:'name',key:'name',ellipsis:true},{title:'版本',dataIndex:'version',key:'version',width:80},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>/*#__PURE__*/_jsx(Tag,{color:getStatusColor(status),children:getStatusText(status)})},{title:'描述',dataIndex:'description',key:'description',ellipsis:true},{title:'创建人',dataIndex:'createdBy',key:'createdBy',width:100},{title:'创建时间',dataIndex:'createdAt',key:'createdAt',width:120,render:date=>formatDate(date)},{title:'更新时间',dataIndex:'updatedAt',key:'updatedAt',width:120,render:date=>formatDate(date)},{title:'操作',key:'action',width:150,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record),disabled:record.status===BOM_STATUS.FROZEN})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>handleDelete(record),disabled:record.status!==BOM_STATUS.DRAFT})}),/*#__PURE__*/_jsx(Dropdown,{menu:{items:getActionMenuItems(record)},trigger:['click'],children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(MoreOutlined,{})})})]})}];// 模拟数据\nconst mockData=[{id:'1',name:'5G基站天线BOM',code:'ANT-5G-001',version:'V1.0',description:'5G基站用高增益天线物料清单',status:'ACTIVE',items:[],configRules:[],createdBy:'admin',createdAt:'2024-01-01T00:00:00Z',updatedAt:'2024-01-15T00:00:00Z'},{id:'2',name:'4G室内天线BOM',code:'ANT-4G-002',version:'V2.1',description:'4G室内覆盖天线物料清单',status:'FROZEN',items:[],configRules:[],createdBy:'bom_manager',createdAt:'2024-01-02T00:00:00Z',updatedAt:'2024-01-16T00:00:00Z',frozenAt:'2024-01-16T00:00:00Z',frozenBy:'admin'},{id:'3',name:'WiFi天线BOM',code:'ANT-WIFI-003',version:'V1.5',description:'WiFi6天线物料清单',status:'DRAFT',items:[],configRules:[],createdBy:'bom_manager',createdAt:'2024-01-03T00:00:00Z',updatedAt:'2024-01-17T00:00:00Z'}];return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u6838\\u5FC3BOM\\u7BA1\\u7406\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ImportOutlined,{}),onClick:handleImport,children:\"\\u5BFC\\u5165\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleCreate,children:\"\\u65B0\\u5EFABOM\"})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:8,children:/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22BOM\\u7F16\\u7801\\u6216\\u540D\\u79F0\",allowClear:true,onSearch:handleSearch,style:{width:'100%'}})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u72B6\\u6001\\u7B5B\\u9009\",allowClear:true,style:{width:'100%'},onChange:handleStatusFilter,options:[{label:'草稿',value:BOM_STATUS.DRAFT},{label:'激活',value:BOM_STATUS.ACTIVE},{label:'冻结',value:BOM_STATUS.FROZEN},{label:'废弃',value:BOM_STATUS.OBSOLETE}]})})]}),selectedRowKeys.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16,padding:'8px 16px',backgroundColor:'#f0f2f5',borderRadius:6},children:[/*#__PURE__*/_jsxs(\"span\",{style:{marginRight:16},children:[\"\\u5DF2\\u9009\\u62E9 \",selectedRowKeys.length,\" \\u9879\"]}),/*#__PURE__*/_jsx(Button,{size:\"small\",onClick:handleClearSelection,style:{marginRight:8},children:\"\\u6E05\\u7A7A\\u9009\\u62E9\"}),/*#__PURE__*/_jsx(Button,{size:\"small\",type:\"primary\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:handleExportSelected,children:\"\\u5BFC\\u51FA\\u9009\\u4E2D\"})]}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:mockData,loading:loading,rowKey:\"id\",rowSelection:rowSelection,scroll:{x:1200},pagination:{current:pagination.current,pageSize:pagination.pageSize,total:pagination.total,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u590D\\u5236BOM\",open:copyModalVisible,onOk:handleCopyConfirm,onCancel:()=>setCopyModalVisible(false),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsxs(Form,{form:copyForm,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"BOM\\u540D\\u79F0\",rules:[{required:true,message:'请输入BOM名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"code\",label:\"BOM\\u7F16\\u7801\",rules:[{required:true,message:'请输入BOM编码'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u7F16\\u7801\"})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u7248\\u672C\\u5386\\u53F2 - \".concat(currentBOMForHistory===null||currentBOMForHistory===void 0?void 0:currentBOMForHistory.name),open:versionHistoryVisible,onCancel:()=>setVersionHistoryVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setVersionHistoryVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:currentBOMForHistory&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Descriptions,{title:\"\\u5F53\\u524DBOM\\u4FE1\\u606F\",bordered:true,size:\"small\",style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"BOM\\u7F16\\u7801\",children:currentBOMForHistory.code}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"BOM\\u540D\\u79F0\",children:currentBOMForHistory.name}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5F53\\u524D\\u7248\\u672C\",children:currentBOMForHistory.version}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",span:3,children:/*#__PURE__*/_jsx(Tag,{color:getStatusColor(currentBOMForHistory.status),children:getStatusText(currentBOMForHistory.status)})})]}),/*#__PURE__*/_jsx(Divider,{children:\"\\u7248\\u672C\\u5386\\u53F2\"}),/*#__PURE__*/_jsx(Timeline,{children:getVersionHistory(currentBOMForHistory).map((version,index)=>/*#__PURE__*/_jsx(Timeline.Item,{dot:/*#__PURE__*/_jsx(ClockCircleOutlined,{style:{fontSize:'16px'}}),color:version.status==='active'?'green':version.status==='frozen'?'blue':'gray',children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:8},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:16,fontWeight:'bold'},children:version.version}),/*#__PURE__*/_jsx(Tag,{color:getStatusColor(version.status),children:getStatusText(version.status)})]}),/*#__PURE__*/_jsxs(\"div\",{style:{color:'#666',marginBottom:8},children:[version.createdBy,\" \\xB7 \",version.createdAt]}),/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:8},children:version.description}),version.changes&&version.changes.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:4},children:\"\\u4E3B\\u8981\\u53D8\\u66F4\\uFF1A\"}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:0,paddingLeft:20},children:version.changes.map((change,changeIndex)=>/*#__PURE__*/_jsx(\"li\",{children:change},changeIndex))})]})]})},index))})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5BFC\\u51FABOM\\u6570\\u636E\",open:exportModalVisible,onOk:handleExportConfirm,onCancel:()=>setExportModalVisible(false),width:600,children:/*#__PURE__*/_jsxs(Form,{form:exportForm,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u5BFC\\u51FA\\u8BF4\\u660E\",description:selectedRowKeys.length>0?\"\\u5C06\\u5BFC\\u51FA\\u5DF2\\u9009\\u62E9\\u7684 \".concat(selectedRowKeys.length,\" \\u6761BOM\\u6570\\u636E\"):\"将导出所有BOM数据\",type:\"info\",style:{marginBottom:16}}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BFC\\u51FA\\u683C\\u5F0F\",name:\"format\",initialValue:exportFormat,children:/*#__PURE__*/_jsxs(Radio.Group,{onChange:e=>setExportFormat(e.target.value),children:[/*#__PURE__*/_jsx(Radio,{value:\"excel\",children:\"Excel\\u683C\\u5F0F (.xlsx)\"}),/*#__PURE__*/_jsx(Radio,{value:\"csv\",children:\"CSV\\u683C\\u5F0F (.csv)\"})]})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BFC\\u51FA\\u5B57\\u6BB5\",name:\"fields\",initialValue:exportFields,children:/*#__PURE__*/_jsx(Checkbox.Group,{options:exportFieldOptions,value:exportFields,onChange:setExportFields})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5BFC\\u5165BOM\\u6570\\u636E\",open:importModalVisible,onCancel:()=>{setImportModalVisible(false);setImportStatus(null);setImportProgress(0);setImportResult(null);importForm.resetFields();},footer:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:handleDownloadTemplate,children:\"\\u4E0B\\u8F7D\\u6A21\\u677F\"},\"template\"),/*#__PURE__*/_jsx(Button,{onClick:()=>{setImportModalVisible(false);setImportStatus(null);setImportProgress(0);setImportResult(null);importForm.resetFields();},children:\"\\u53D6\\u6D88\"},\"cancel\")],width:600,children:/*#__PURE__*/_jsxs(Form,{form:importForm,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u5BFC\\u5165\\u8BF4\\u660E\",description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:\"1. \\u8BF7\\u4E0B\\u8F7D\\u5BFC\\u5165\\u6A21\\u677F\\uFF0C\\u6309\\u7167\\u6A21\\u677F\\u683C\\u5F0F\\u586B\\u5199\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"p\",{children:\"2. \\u652F\\u6301CSV\\u683C\\u5F0F\\u6587\\u4EF6\\u5BFC\\u5165\"}),/*#__PURE__*/_jsx(\"p\",{children:\"3. BOM\\u7F16\\u7801\\u548C\\u540D\\u79F0\\u4E3A\\u5FC5\\u586B\\u5B57\\u6BB5\"}),/*#__PURE__*/_jsx(\"p\",{children:\"4. \\u5BFC\\u5165\\u524D\\u8BF7\\u786E\\u4FDD\\u6570\\u636E\\u683C\\u5F0F\\u6B63\\u786E\"})]}),type:\"info\",style:{marginBottom:16}}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u9009\\u62E9\\u6587\\u4EF6\",children:/*#__PURE__*/_jsxs(Upload.Dragger,{accept:\".csv,.xlsx,.xls\",beforeUpload:handleFileUpload,showUploadList:false,disabled:importStatus==='uploading'||importStatus==='processing',children:[/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-drag-icon\",children:/*#__PURE__*/_jsx(FileExcelOutlined,{style:{fontSize:48,color:'#1890ff'}})}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-text\",children:\"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-hint\",children:\"\\u652F\\u6301CSV\\u3001Excel\\u683C\\u5F0F\\u6587\\u4EF6\"})]})}),importStatus&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:16},children:[importStatus==='uploading'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:8},children:\"\\u6B63\\u5728\\u4E0A\\u4F20\\u6587\\u4EF6...\"}),/*#__PURE__*/_jsx(Progress,{percent:importProgress,status:\"active\"})]}),importStatus==='processing'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:8},children:\"\\u6B63\\u5728\\u5904\\u7406\\u6570\\u636E...\"}),/*#__PURE__*/_jsx(Progress,{percent:100,status:\"active\"})]}),importStatus==='success'&&importResult&&/*#__PURE__*/_jsx(Alert,{message:\"\\u5BFC\\u5165\\u6210\\u529F\",description:\"\\u6210\\u529F\\u5BFC\\u5165 \".concat(importResult.success,\" \\u6761\\u6570\\u636E\"),type:\"success\",icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{})}),importStatus==='error'&&importResult&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u5BFC\\u5165\\u5B8C\\u6210\\uFF0C\\u90E8\\u5206\\u6570\\u636E\\u6709\\u9519\\u8BEF\",description:\"\\u6210\\u529F\\uFF1A\".concat(importResult.success,\" \\u6761\\uFF0C\\u9519\\u8BEF\\uFF1A\").concat(importResult.errors,\" \\u6761\"),type:\"warning\",icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{}),style:{marginBottom:16}}),importResult.errorMessages&&importResult.errorMessages.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:8},children:\"\\u9519\\u8BEF\\u8BE6\\u60C5\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:200,overflow:'auto',backgroundColor:'#f5f5f5',padding:8,borderRadius:4},children:importResult.errorMessages.map((error,index)=>/*#__PURE__*/_jsx(\"div\",{style:{color:'#ff4d4f',marginBottom:4},children:error},index))})]})]})]})]})})]});};export default/*#__PURE__*/React.memo(CoreBOMListPage);", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "message", "Modal", "Form", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Upload", "Progress", "<PERSON><PERSON>", "Divider", "Timeline", "Descriptions", "Checkbox", "Radio", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "LockOutlined", "UnlockOutlined", "MoreOutlined", "ExportOutlined", "ImportOutlined", "HistoryOutlined", "DownloadOutlined", "FileExcelOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "ClockCircleOutlined", "useAppDispatch", "useAppSelector", "fetchCoreBOMs", "deleteCoreBOM", "freezeCoreBOM", "ROUTES", "BOM_STATUS", "formatDate", "ConfirmDialog", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Search", "CoreBOMListPage", "navigate", "dispatch", "coreBOMs", "loading", "pagination", "state", "bom", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "copyModalVisible", "setCopyModalVisible", "copyingBOM", "setCopyingBOM", "copyForm", "useForm", "versionHistoryVisible", "setVersionHistoryVisible", "currentBOMForHistory", "setCurrentBOMForHistory", "exportModalVisible", "setExportModalVisible", "importModalVisible", "setImportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "importProgress", "setImportProgress", "importStatus", "setImportStatus", "importResult", "setImportResult", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "exportForm", "importForm", "loadData", "current", "pageSize", "page", "keyword", "handleSearch", "value", "handleStatusFilter", "handleCreate", "CORE_BOM_CREATE", "handleEdit", "record", "CORE_BOM_EDIT", "replace", "id", "handleView", "CORE_BOM_VIEW", "handleDelete", "confirm", "title", "content", "children", "code", "name", "style", "color", "marginTop", "type", "onConfirm", "unwrap", "success", "error", "handleError", "BUSINESS", "details", "handleFreeze", "isActive", "status", "ACTIVE", "action", "concat", "handleCopy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleCopyConfirm", "values", "validateFields", "handleVersionHistory", "handleExport", "handleImport", "handleDownloadTemplate", "templateData", "headers", "Object", "keys", "csv<PERSON><PERSON>nt", "join", "map", "row", "blob", "Blob", "link", "document", "createElement", "href", "URL", "createObjectURL", "download", "click", "handleExportConfirm", "exportData", "mockData", "length", "filter", "item", "includes", "filteredData", "filteredItem", "for<PERSON>ach", "field", "version", "getStatusText", "description", "created<PERSON>y", "createdAt", "updatedAt", "info", "Date", "getTime", "handleFileUpload", "file", "interval", "setInterval", "prev", "clearInterval", "setTimeout", "processImportFile", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "lines", "split", "importedData", "errors", "i", "trim", "header", "index", "_values$index", "push", "total", "data", "errorMessages", "warning", "readAsText", "rowSelection", "onChange", "newSelectedRowKeys", "onSelectAll", "selected", "selectedRows", "changeRows", "console", "log", "handleClearSelection", "handleExportSelected", "getVersionHistory", "changes", "getStatusColor", "DRAFT", "FROZEN", "OBSOLETE", "exportFieldOptions", "label", "getActionMenuItems", "key", "icon", "onClick", "disabled", "columns", "dataIndex", "width", "render", "text", "ellipsis", "date", "fixed", "_", "size", "danger", "menu", "items", "trigger", "configRules", "frozenAt", "frozenBy", "justify", "align", "marginBottom", "level", "margin", "gutter", "xs", "sm", "md", "placeholder", "allowClear", "onSearch", "options", "padding", "backgroundColor", "borderRadius", "marginRight", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onOk", "onCancel", "okText", "cancelText", "form", "layout", "<PERSON><PERSON>", "rules", "required", "footer", "bordered", "span", "dot", "fontSize", "display", "justifyContent", "alignItems", "fontWeight", "paddingLeft", "change", "changeIndex", "initialValue", "Group", "resetFields", "<PERSON><PERSON>", "accept", "beforeUpload", "showUploadList", "className", "percent", "maxHeight", "overflow", "memo"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/CoreBOMListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState, useMemo, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Upload,\n  Progress,\n  Alert,\n  Divider,\n  Timeline,\n  Descriptions,\n  Checkbox,\n  Radio\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  LockOutlined,\n  UnlockOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  HistoryOutlined,\n  DownloadOutlined,\n  UploadOutlined,\n  FileExcelOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCoreBOMs, deleteCoreBOM, freezeCoreBOM } from '../../store/slices/bomSlice';\nimport { CoreBOM } from '../../types';\nimport { ROUTES, BOM_STATUS } from '../../constants';\nimport { formatDate } from '../../utils';\nimport { ConfirmDialog } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title } = Typography;\nconst { Search } = Input;\n\nconst CoreBOMListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const { coreBOMs, loading, pagination } = useAppSelector(state => state.bom);\n  \n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [copyModalVisible, setCopyModalVisible] = useState(false);\n  const [copyingBOM, setCopyingBOM] = useState<CoreBOM | null>(null);\n  const [copyForm] = Form.useForm();\n  \n  // 版本历史相关状态\n  const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);\n  const [currentBOMForHistory, setCurrentBOMForHistory] = useState<CoreBOM | null>(null);\n  \n  // 导出导入相关状态\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [importModalVisible, setImportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['code', 'name', 'version', 'status', 'description']);\n  const [importProgress, setImportProgress] = useState(0);\n  const [importStatus, setImportStatus] = useState<'uploading' | 'processing' | 'success' | 'error' | null>(null);\n  const [importResult, setImportResult] = useState<any>(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [exportForm] = Form.useForm();\n  const [importForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter]);\n\n  const loadData = useCallback(() => {\n    dispatch(fetchCoreBOMs({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      keyword: searchKeyword,\n    }));\n  }, [dispatch, pagination.current, pagination.pageSize, searchKeyword]);\n\n  const handleSearch = useCallback((value: string) => {\n    setSearchKeyword(value);\n  }, []);\n\n  const handleStatusFilter = useCallback((value: string) => {\n    setStatusFilter(value);\n  }, []);\n\n  const handleCreate = useCallback(() => {\n    navigate(ROUTES.CORE_BOM_CREATE);\n  }, [navigate]);\n\n  const handleEdit = useCallback((record: CoreBOM) => {\n    navigate(ROUTES.CORE_BOM_EDIT.replace(':id', record.id));\n  }, [navigate]);\n\n  const handleView = useCallback((record: CoreBOM) => {\n    navigate(ROUTES.CORE_BOM_VIEW.replace(':id', record.id));\n  }, [navigate]);\n\n  const handleDelete = useCallback(async (record: CoreBOM) => {\n    ConfirmDialog.confirm({\n      title: '确认删除',\n      content: (\n        <div>\n          <p>确定要删除以下BOM吗？</p>\n          <p><strong>BOM编码：</strong>{record.code}</p>\n          <p><strong>BOM名称：</strong>{record.name}</p>\n          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复，请谨慎操作！</p>\n        </div>\n      ),\n      type: 'warning',\n      onConfirm: async () => {\n        try {\n          await dispatch(deleteCoreBOM(record.id)).unwrap();\n          message.success('删除成功');\n          loadData();\n        } catch (error: any) {\n          errorHandler.handleError({\n            type: ErrorType.BUSINESS,\n            message: error.message || '删除失败',\n            details: error\n          });\n        }\n      }\n    });\n  }, [dispatch, loadData]);\n\n  const handleFreeze = useCallback(async (record: CoreBOM) => {\n    const isActive = record.status === BOM_STATUS.ACTIVE;\n    const action = isActive ? '冻结' : '解冻';\n    \n    ConfirmDialog.confirm({\n      title: `确认${action}`,\n      content: (\n        <div>\n          <p>确定要{action}以下BOM吗？</p>\n          <p><strong>BOM编码：</strong>{record.code}</p>\n          <p><strong>BOM名称：</strong>{record.name}</p>\n          {isActive && (\n            <p style={{ color: '#faad14', marginTop: 12 }}>冻结后该BOM将无法被使用！</p>\n          )}\n        </div>\n      ),\n      type: isActive ? 'warning' : 'info',\n      onConfirm: async () => {\n        try {\n          await dispatch(freezeCoreBOM(record.id)).unwrap();\n          message.success(`${action}成功`);\n          loadData();\n        } catch (error: any) {\n          errorHandler.handleError({\n            type: ErrorType.BUSINESS,\n            message: error.message || `${action}失败`,\n            details: error\n          });\n        }\n      }\n    });\n  }, [dispatch, loadData]);\n\n  const handleCopy = useCallback((record: CoreBOM) => {\n    setCopyingBOM(record);\n    copyForm.setFieldsValue({\n      name: `${record.name}_副本`,\n      code: `${record.code}_COPY`,\n    });\n    setCopyModalVisible(true);\n  }, [copyForm]);\n\n  const handleCopyConfirm = async () => {\n    try {\n      const values = await copyForm.validateFields();\n      // TODO: 实现复制BOM的API调用\n      message.success('复制成功');\n      setCopyModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('复制失败');\n    }\n  };\n\n  const handleVersionHistory = (record: CoreBOM) => {\n    setCurrentBOMForHistory(record);\n    setVersionHistoryVisible(true);\n  };\n\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const handleImport = () => {\n    setImportModalVisible(true);\n  };\n  \n  // 下载导入模板\n  const handleDownloadTemplate = () => {\n    // TODO: 实现下载模板功能\n    const templateData = [\n      {\n        'BOM编码': 'BOM001',\n        'BOM名称': '示例BOM',\n        '版本': 'V1.0',\n        '状态': 'draft',\n        '描述': '这是一个示例BOM'\n      }\n    ];\n    \n    // 创建CSV内容\n    const headers = Object.keys(templateData[0]);\n    const csvContent = [headers.join(','), ...templateData.map(row => Object.values(row).join(','))].join('\\n');\n    \n    // 下载文件\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    link.href = URL.createObjectURL(blob);\n    link.download = 'core_bom_template.csv';\n    link.click();\n    \n    message.success('模板下载成功');\n  };\n  \n  // 执行导出\n  const handleExportConfirm = async () => {\n    try {\n      const values = await exportForm.validateFields();\n      \n      // 获取要导出的数据\n      let exportData = mockData;\n      if (selectedRowKeys.length > 0) {\n        exportData = mockData.filter(item => selectedRowKeys.includes(item.id));\n      }\n      \n      // 根据选择的字段过滤数据\n      const filteredData = exportData.map(item => {\n        const filteredItem: any = {};\n        exportFields.forEach(field => {\n          switch (field) {\n            case 'code':\n              filteredItem['BOM编码'] = item.code;\n              break;\n            case 'name':\n              filteredItem['BOM名称'] = item.name;\n              break;\n            case 'version':\n              filteredItem['版本'] = item.version;\n              break;\n            case 'status':\n              filteredItem['状态'] = getStatusText(item.status);\n              break;\n            case 'description':\n              filteredItem['描述'] = item.description;\n              break;\n            case 'createdBy':\n              filteredItem['创建人'] = item.createdBy;\n              break;\n            case 'createdAt':\n              filteredItem['创建时间'] = formatDate(item.createdAt);\n              break;\n            case 'updatedAt':\n              filteredItem['更新时间'] = formatDate(item.updatedAt);\n              break;\n          }\n        });\n        return filteredItem;\n      });\n      \n      // 创建并下载文件\n      if (exportFormat === 'excel') {\n        // TODO: 使用xlsx库导出Excel\n        message.info('Excel导出功能需要集成xlsx库');\n      } else {\n        // CSV导出\n        const headers = Object.keys(filteredData[0] || {});\n        const csvContent = [headers.join(','), ...filteredData.map(row => Object.values(row).join(','))].join('\\n');\n        \n        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n        const link = document.createElement('a');\n        link.href = URL.createObjectURL(blob);\n        link.download = `core_bom_export_${new Date().getTime()}.csv`;\n        link.click();\n      }\n      \n      setExportModalVisible(false);\n      message.success('导出成功');\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n  \n  // 处理文件上传\n  const handleFileUpload = (file: File) => {\n    setImportStatus('uploading');\n    setImportProgress(0);\n    \n    // 模拟上传进度\n    const interval = setInterval(() => {\n      setImportProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(interval);\n          setImportStatus('processing');\n          \n          // 模拟处理过程\n          setTimeout(() => {\n            processImportFile(file);\n          }, 1000);\n          \n          return 100;\n        }\n        return prev + 10;\n      });\n    }, 200);\n    \n    return false; // 阻止默认上传\n  };\n  \n  // 处理导入文件\n  const processImportFile = (file: File) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const content = e.target?.result as string;\n        const lines = content.split('\\n');\n        const headers = lines[0].split(',');\n        \n        const importedData = [];\n        const errors = [];\n        \n        for (let i = 1; i < lines.length; i++) {\n          if (lines[i].trim()) {\n            const values = lines[i].split(',');\n            const row: any = {};\n            \n            headers.forEach((header, index) => {\n              row[header.trim()] = values[index]?.trim();\n            });\n            \n            // 验证数据\n            if (!row['BOM编码'] || !row['BOM名称']) {\n              errors.push(`第${i + 1}行：BOM编码和名称不能为空`);\n            } else {\n              importedData.push({\n                code: row['BOM编码'],\n                name: row['BOM名称'],\n                version: row['版本'] || 'V1.0',\n                status: row['状态'] || 'draft',\n                description: row['描述'] || ''\n              });\n            }\n          }\n        }\n        \n        setImportResult({\n          total: lines.length - 1,\n          success: importedData.length,\n          errors: errors.length,\n          data: importedData,\n          errorMessages: errors\n        });\n        \n        setImportStatus(errors.length > 0 ? 'error' : 'success');\n        \n        if (errors.length === 0) {\n          message.success(`成功导入${importedData.length}条数据`);\n          // TODO: 调用API保存数据\n        } else {\n          message.warning(`导入完成，${errors.length}条数据有错误`);\n        }\n        \n      } catch (error) {\n        setImportStatus('error');\n        setImportResult({\n          total: 0,\n          success: 0,\n          errors: 1,\n          errorMessages: ['文件格式错误，请检查文件内容']\n        });\n        message.error('文件解析失败');\n      }\n    };\n    \n    reader.readAsText(file);\n  };\n  \n  // 表格行选择\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    onSelectAll: (selected: boolean, selectedRows: CoreBOM[], changeRows: CoreBOM[]) => {\n      console.log('Select all:', selected, selectedRows, changeRows);\n    },\n  };\n  \n  // 清空选择\n  const handleClearSelection = () => {\n    setSelectedRowKeys([]);\n  };\n  \n  // 导出选中项\n  const handleExportSelected = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请先选择要导出的数据');\n      return;\n    }\n    setExportModalVisible(true);\n  };\n  \n  // 模拟版本历史数据\n  const getVersionHistory = (bom: CoreBOM) => {\n    return [\n      {\n        version: 'V1.2',\n        status: 'active',\n        description: '优化物料清单，更新供应商信息',\n        createdBy: '李工程师',\n        createdAt: '2024-03-25 14:30:00',\n        changes: ['更新供应商信息', '调整物料数量', '优化成本结构']\n      },\n      {\n        version: 'V1.1',\n        status: 'frozen',\n        description: '修复物料规格错误',\n        createdBy: '王工程师',\n        createdAt: '2024-03-20 10:15:00',\n        changes: ['修正物料规格', '更新技术参数']\n      },\n      {\n        version: 'V1.0',\n        status: 'obsolete',\n        description: '初始版本',\n        createdBy: '张工程师',\n        createdAt: '2024-03-15 09:00:00',\n        changes: ['创建初始BOM结构']\n      }\n    ];\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case BOM_STATUS.DRAFT: return 'default';\n      case BOM_STATUS.ACTIVE: return 'success';\n      case BOM_STATUS.FROZEN: return 'blue';\n      case BOM_STATUS.OBSOLETE: return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case BOM_STATUS.DRAFT: return '草稿';\n      case BOM_STATUS.ACTIVE: return '激活';\n      case BOM_STATUS.FROZEN: return '冻结';\n      case BOM_STATUS.OBSOLETE: return '废弃';\n      default: return status;\n    }\n  };\n\n  // 可导出字段选项\n  const exportFieldOptions = [\n    { label: 'BOM编码', value: 'code' },\n    { label: 'BOM名称', value: 'name' },\n    { label: '版本', value: 'version' },\n    { label: '状态', value: 'status' },\n    { label: '描述', value: 'description' },\n    { label: '创建人', value: 'createdBy' },\n    { label: '创建时间', value: 'createdAt' },\n    { label: '更新时间', value: 'updatedAt' }\n  ];\n\n  const getActionMenuItems = (record: CoreBOM): MenuProps['items'] => [\n    {\n      key: 'copy',\n      icon: <CopyOutlined />,\n      label: '复制',\n      onClick: () => handleCopy(record),\n    },\n    {\n      key: 'history',\n      icon: <HistoryOutlined />,\n      label: '版本历史',\n      onClick: () => handleVersionHistory(record),\n    },\n    {\n      key: 'freeze',\n      icon: record.status === BOM_STATUS.FROZEN ? <UnlockOutlined /> : <LockOutlined />,\n      label: record.status === BOM_STATUS.FROZEN ? '解冻' : '冻结',\n      onClick: () => handleFreeze(record),\n      disabled: record.status === BOM_STATUS.DRAFT,\n    },\n  ];\n\n  const columns = [\n    {\n      title: 'BOM编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 120,\n      render: (text: string, record: CoreBOM) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: 'BOM名称',\n      dataIndex: 'name',\n      key: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '版本',\n      dataIndex: 'version',\n      key: 'version',\n      width: 80,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true,\n    },\n    {\n      title: '创建人',\n      dataIndex: 'createdBy',\n      key: 'createdBy',\n      width: 100,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: CoreBOM) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n              disabled={record.status === BOM_STATUS.FROZEN}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => handleDelete(record)}\n              disabled={record.status !== BOM_STATUS.DRAFT}\n            />\n          </Tooltip>\n          <Dropdown\n            menu={{ items: getActionMenuItems(record) }}\n            trigger={['click']}\n          >\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  // 模拟数据\n  const mockData: CoreBOM[] = [\n    {\n      id: '1',\n      name: '5G基站天线BOM',\n      code: 'ANT-5G-001',\n      version: 'V1.0',\n      description: '5G基站用高增益天线物料清单',\n      status: 'ACTIVE',\n      items: [],\n      configRules: [],\n      createdBy: 'admin',\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-15T00:00:00Z',\n    },\n    {\n      id: '2',\n      name: '4G室内天线BOM',\n      code: 'ANT-4G-002',\n      version: 'V2.1',\n      description: '4G室内覆盖天线物料清单',\n      status: 'FROZEN',\n      items: [],\n      configRules: [],\n      createdBy: 'bom_manager',\n      createdAt: '2024-01-02T00:00:00Z',\n      updatedAt: '2024-01-16T00:00:00Z',\n      frozenAt: '2024-01-16T00:00:00Z',\n      frozenBy: 'admin',\n    },\n    {\n      id: '3',\n      name: 'WiFi天线BOM',\n      code: 'ANT-WIFI-003',\n      version: 'V1.5',\n      description: 'WiFi6天线物料清单',\n      status: 'DRAFT',\n      items: [],\n      configRules: [],\n      createdBy: 'bom_manager',\n      createdAt: '2024-01-03T00:00:00Z',\n      updatedAt: '2024-01-17T00:00:00Z',\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              核心BOM管理\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<ImportOutlined />} onClick={handleImport}>\n                导入\n              </Button>\n              <Button icon={<ExportOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleCreate}>\n                新建BOM\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n          <Col xs={24} sm={12} md={8}>\n            <Search\n              placeholder=\"搜索BOM编码或名称\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Select\n              placeholder=\"状态筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              onChange={handleStatusFilter}\n              options={[\n                { label: '草稿', value: BOM_STATUS.DRAFT },\n                { label: '激活', value: BOM_STATUS.ACTIVE },\n                { label: '冻结', value: BOM_STATUS.FROZEN },\n                { label: '废弃', value: BOM_STATUS.OBSOLETE },\n              ]}\n            />\n          </Col>\n        </Row>\n\n        {selectedRowKeys.length > 0 && (\n          <div style={{ marginBottom: 16, padding: '8px 16px', backgroundColor: '#f0f2f5', borderRadius: 6 }}>\n            <span style={{ marginRight: 16 }}>已选择 {selectedRowKeys.length} 项</span>\n            <Button size=\"small\" onClick={handleClearSelection} style={{ marginRight: 8 }}>\n              清空选择\n            </Button>\n            <Button size=\"small\" type=\"primary\" icon={<DownloadOutlined />} onClick={handleExportSelected}>\n              导出选中\n            </Button>\n          </div>\n        )}\n\n        <Table\n          columns={columns}\n          dataSource={mockData}\n          loading={loading}\n          rowKey=\"id\"\n          rowSelection={rowSelection}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 复制BOM模态框 */}\n      <Modal\n        title=\"复制BOM\"\n        open={copyModalVisible}\n        onOk={handleCopyConfirm}\n        onCancel={() => setCopyModalVisible(false)}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form form={copyForm} layout=\"vertical\">\n          <Form.Item\n            name=\"name\"\n            label=\"BOM名称\"\n            rules={[{ required: true, message: '请输入BOM名称' }]}\n          >\n            <Input placeholder=\"请输入BOM名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"code\"\n            label=\"BOM编码\"\n            rules={[{ required: true, message: '请输入BOM编码' }]}\n          >\n            <Input placeholder=\"请输入BOM编码\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n      \n      {/* 版本历史模态框 */}\n      <Modal\n          title={`版本历史 - ${currentBOMForHistory?.name}`}\n          open={versionHistoryVisible}\n          onCancel={() => setVersionHistoryVisible(false)}\n          footer={[\n            <Button key=\"close\" onClick={() => setVersionHistoryVisible(false)}>\n              关闭\n            </Button>\n          ]}\n          width={800}\n        >\n          {currentBOMForHistory && (\n            <div>\n              <Descriptions\n                title=\"当前BOM信息\"\n                bordered\n                size=\"small\"\n                style={{ marginBottom: 24 }}\n              >\n                <Descriptions.Item label=\"BOM编码\">{currentBOMForHistory.code}</Descriptions.Item>\n                <Descriptions.Item label=\"BOM名称\">{currentBOMForHistory.name}</Descriptions.Item>\n                <Descriptions.Item label=\"当前版本\">{currentBOMForHistory.version}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\" span={3}>\n                  <Tag color={getStatusColor(currentBOMForHistory.status)}>\n                    {getStatusText(currentBOMForHistory.status)}\n                  </Tag>\n                </Descriptions.Item>\n              </Descriptions>\n              \n              <Divider>版本历史</Divider>\n              \n              <Timeline>\n                {getVersionHistory(currentBOMForHistory).map((version, index) => (\n                  <Timeline.Item\n                    key={index}\n                    dot={<ClockCircleOutlined style={{ fontSize: '16px' }} />}\n                    color={version.status === 'active' ? 'green' : version.status === 'frozen' ? 'blue' : 'gray'}\n                  >\n                    <div style={{ marginBottom: 16 }}>\n                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>\n                        <span style={{ fontSize: 16, fontWeight: 'bold' }}>{version.version}</span>\n                        <Tag color={getStatusColor(version.status)}>\n                          {getStatusText(version.status)}\n                        </Tag>\n                      </div>\n                      <div style={{ color: '#666', marginBottom: 8 }}>\n                        {version.createdBy} · {version.createdAt}\n                      </div>\n                      <div style={{ marginBottom: 8 }}>{version.description}</div>\n                      {version.changes && version.changes.length > 0 && (\n                        <div>\n                          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>主要变更：</div>\n                          <ul style={{ margin: 0, paddingLeft: 20 }}>\n                            {version.changes.map((change, changeIndex) => (\n                              <li key={changeIndex}>{change}</li>\n                            ))}\n                          </ul>\n                        </div>\n                      )}\n                    </div>\n                  </Timeline.Item>\n                ))}\n              </Timeline>\n            </div>\n          )}\n      </Modal>\n      \n      {/* 导出模态框 */}\n      <Modal\n          title=\"导出BOM数据\"\n          open={exportModalVisible}\n          onOk={handleExportConfirm}\n          onCancel={() => setExportModalVisible(false)}\n          width={600}\n        >\n          <Form form={exportForm} layout=\"vertical\">\n            <Alert\n              message=\"导出说明\"\n              description={selectedRowKeys.length > 0 ? `将导出已选择的 ${selectedRowKeys.length} 条BOM数据` : \"将导出所有BOM数据\"}\n              type=\"info\"\n              style={{ marginBottom: 16 }}\n            />\n            \n            <Form.Item label=\"导出格式\" name=\"format\" initialValue={exportFormat}>\n              <Radio.Group onChange={(e) => setExportFormat(e.target.value)}>\n                <Radio value=\"excel\">Excel格式 (.xlsx)</Radio>\n                <Radio value=\"csv\">CSV格式 (.csv)</Radio>\n              </Radio.Group>\n            </Form.Item>\n            \n            <Form.Item label=\"导出字段\" name=\"fields\" initialValue={exportFields}>\n              <Checkbox.Group\n                options={exportFieldOptions}\n                value={exportFields}\n                onChange={setExportFields}\n              />\n            </Form.Item>\n          </Form>\n      </Modal>\n      \n      {/* 导入模态框 */}\n      <Modal\n          title=\"导入BOM数据\"\n          open={importModalVisible}\n          onCancel={() => {\n            setImportModalVisible(false);\n            setImportStatus(null);\n            setImportProgress(0);\n            setImportResult(null);\n            importForm.resetFields();\n          }}\n          footer={[\n            <Button key=\"template\" icon={<DownloadOutlined />} onClick={handleDownloadTemplate}>\n              下载模板\n            </Button>,\n            <Button key=\"cancel\" onClick={() => {\n              setImportModalVisible(false);\n              setImportStatus(null);\n              setImportProgress(0);\n              setImportResult(null);\n              importForm.resetFields();\n            }}>\n              取消\n            </Button>\n          ]}\n          width={600}\n        >\n          <Form form={importForm} layout=\"vertical\">\n            <Alert\n              message=\"导入说明\"\n              description={\n                <div>\n                  <p>1. 请下载导入模板，按照模板格式填写数据</p>\n                  <p>2. 支持CSV格式文件导入</p>\n                  <p>3. BOM编码和名称为必填字段</p>\n                  <p>4. 导入前请确保数据格式正确</p>\n                </div>\n              }\n              type=\"info\"\n              style={{ marginBottom: 16 }}\n            />\n            \n            <Form.Item label=\"选择文件\">\n              <Upload.Dragger\n                accept=\".csv,.xlsx,.xls\"\n                beforeUpload={handleFileUpload}\n                showUploadList={false}\n                disabled={importStatus === 'uploading' || importStatus === 'processing'}\n              >\n                <p className=\"ant-upload-drag-icon\">\n                  <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">支持CSV、Excel格式文件</p>\n              </Upload.Dragger>\n            </Form.Item>\n            \n            {importStatus && (\n              <div style={{ marginTop: 16 }}>\n                {importStatus === 'uploading' && (\n                  <div>\n                    <div style={{ marginBottom: 8 }}>正在上传文件...</div>\n                    <Progress percent={importProgress} status=\"active\" />\n                  </div>\n                )}\n                \n                {importStatus === 'processing' && (\n                  <div>\n                    <div style={{ marginBottom: 8 }}>正在处理数据...</div>\n                    <Progress percent={100} status=\"active\" />\n                  </div>\n                )}\n                \n                {importStatus === 'success' && importResult && (\n                  <Alert\n                    message=\"导入成功\"\n                    description={`成功导入 ${importResult.success} 条数据`}\n                    type=\"success\"\n                    icon={<CheckCircleOutlined />}\n                  />\n                )}\n                \n                {importStatus === 'error' && importResult && (\n                  <div>\n                    <Alert\n                      message=\"导入完成，部分数据有错误\"\n                      description={`成功：${importResult.success} 条，错误：${importResult.errors} 条`}\n                      type=\"warning\"\n                      icon={<ExclamationCircleOutlined />}\n                      style={{ marginBottom: 16 }}\n                    />\n                    \n                    {importResult.errorMessages && importResult.errorMessages.length > 0 && (\n                      <div>\n                        <div style={{ fontWeight: 'bold', marginBottom: 8 }}>错误详情：</div>\n                        <div style={{ maxHeight: 200, overflow: 'auto', backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>\n                          {importResult.errorMessages.map((error: string, index: number) => (\n                            <div key={index} style={{ color: '#ff4d4f', marginBottom: 4 }}>\n                              {error}\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            )}\n          </Form>\n        </Modal>\n    </div>\n  );\n};\n\nexport default React.memo(CoreBOMListPage);\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAWC,WAAW,KAAQ,OAAO,CACxE,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,OAAO,CACPC,KAAK,CACLC,IAAI,CACJC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,OAAO,CACPC,QAAQ,CAERC,MAAM,CACNC,QAAQ,CACRC,KAAK,CACLC,OAAO,CACPC,QAAQ,CACRC,YAAY,CACZC,QAAQ,CACRC,KAAK,KACA,MAAM,CACb,OACEC,YAAY,CAEZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,YAAY,CACZC,cAAc,CACdC,cAAc,CACdC,eAAe,CACfC,gBAAgB,CAEhBC,iBAAiB,CACjBC,mBAAmB,CACnBC,yBAAyB,CACzBC,mBAAmB,KACd,mBAAmB,CAE1B,OAASC,cAAc,CAAEC,cAAc,KAAQ,mBAAmB,CAClE,OAASC,aAAa,CAAEC,aAAa,CAAEC,aAAa,KAAQ,6BAA6B,CAEzF,OAASC,MAAM,CAAEC,UAAU,KAAQ,iBAAiB,CACpD,OAASC,UAAU,KAAQ,aAAa,CACxC,OAASC,aAAa,KAAQ,kBAAkB,CAChD,OAASC,YAAY,CAAEC,SAAS,KAAQ,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAEC,KAAM,CAAC,CAAG5C,UAAU,CAC5B,KAAM,CAAE6C,MAAO,CAAC,CAAGpD,KAAK,CAExB,KAAM,CAAAqD,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAAC,QAAQ,CAAG1D,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA2D,QAAQ,CAAGnB,cAAc,CAAC,CAAC,CACjC,KAAM,CAAEoB,QAAQ,CAAEC,OAAO,CAAEC,UAAW,CAAC,CAAGrB,cAAc,CAACsB,KAAK,EAAIA,KAAK,CAACC,GAAG,CAAC,CAE5E,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACuE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACyE,UAAU,CAAEC,aAAa,CAAC,CAAG1E,QAAQ,CAAiB,IAAI,CAAC,CAClE,KAAM,CAAC2E,QAAQ,CAAC,CAAG/D,IAAI,CAACgE,OAAO,CAAC,CAAC,CAEjC;AACA,KAAM,CAACC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CACzE,KAAM,CAAC+E,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGhF,QAAQ,CAAiB,IAAI,CAAC,CAEtF;AACA,KAAM,CAACiF,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlF,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACmF,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpF,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACqF,YAAY,CAAEC,eAAe,CAAC,CAAGtF,QAAQ,CAAC,OAAO,CAAC,CACzD,KAAM,CAACuF,YAAY,CAAEC,eAAe,CAAC,CAAGxF,QAAQ,CAAW,CAAC,MAAM,CAAE,MAAM,CAAE,SAAS,CAAE,QAAQ,CAAE,aAAa,CAAC,CAAC,CAChH,KAAM,CAACyF,cAAc,CAAEC,iBAAiB,CAAC,CAAG1F,QAAQ,CAAC,CAAC,CAAC,CACvD,KAAM,CAAC2F,YAAY,CAAEC,eAAe,CAAC,CAAG5F,QAAQ,CAA0D,IAAI,CAAC,CAC/G,KAAM,CAAC6F,YAAY,CAAEC,eAAe,CAAC,CAAG9F,QAAQ,CAAM,IAAI,CAAC,CAC3D,KAAM,CAAC+F,eAAe,CAAEC,kBAAkB,CAAC,CAAGhG,QAAQ,CAAc,EAAE,CAAC,CACvE,KAAM,CAACiG,UAAU,CAAC,CAAGrF,IAAI,CAACgE,OAAO,CAAC,CAAC,CACnC,KAAM,CAACsB,UAAU,CAAC,CAAGtF,IAAI,CAACgE,OAAO,CAAC,CAAC,CAEnC7E,SAAS,CAAC,IAAM,CACdoG,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACnC,UAAU,CAACoC,OAAO,CAAEpC,UAAU,CAACqC,QAAQ,CAAElC,aAAa,CAAEE,YAAY,CAAC,CAAC,CAE1E,KAAM,CAAA8B,QAAQ,CAAGlG,WAAW,CAAC,IAAM,CACjC4D,QAAQ,CAACjB,aAAa,CAAC,CACrB0D,IAAI,CAAEtC,UAAU,CAACoC,OAAO,CACxBC,QAAQ,CAAErC,UAAU,CAACqC,QAAQ,CAC7BE,OAAO,CAAEpC,aACX,CAAC,CAAC,CAAC,CACL,CAAC,CAAE,CAACN,QAAQ,CAAEG,UAAU,CAACoC,OAAO,CAAEpC,UAAU,CAACqC,QAAQ,CAAElC,aAAa,CAAC,CAAC,CAEtE,KAAM,CAAAqC,YAAY,CAAGvG,WAAW,CAAEwG,KAAa,EAAK,CAClDrC,gBAAgB,CAACqC,KAAK,CAAC,CACzB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,kBAAkB,CAAGzG,WAAW,CAAEwG,KAAa,EAAK,CACxDnC,eAAe,CAACmC,KAAK,CAAC,CACxB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAE,YAAY,CAAG1G,WAAW,CAAC,IAAM,CACrC2D,QAAQ,CAACb,MAAM,CAAC6D,eAAe,CAAC,CAClC,CAAC,CAAE,CAAChD,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAiD,UAAU,CAAG5G,WAAW,CAAE6G,MAAe,EAAK,CAClDlD,QAAQ,CAACb,MAAM,CAACgE,aAAa,CAACC,OAAO,CAAC,KAAK,CAAEF,MAAM,CAACG,EAAE,CAAC,CAAC,CAC1D,CAAC,CAAE,CAACrD,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAsD,UAAU,CAAGjH,WAAW,CAAE6G,MAAe,EAAK,CAClDlD,QAAQ,CAACb,MAAM,CAACoE,aAAa,CAACH,OAAO,CAAC,KAAK,CAAEF,MAAM,CAACG,EAAE,CAAC,CAAC,CAC1D,CAAC,CAAE,CAACrD,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAwD,YAAY,CAAGnH,WAAW,CAAC,KAAO,CAAA6G,MAAe,EAAK,CAC1D5D,aAAa,CAACmE,OAAO,CAAC,CACpBC,KAAK,CAAE,MAAM,CACbC,OAAO,cACL/D,KAAA,QAAAgE,QAAA,eACElE,IAAA,MAAAkE,QAAA,CAAG,2DAAY,CAAG,CAAC,cACnBhE,KAAA,MAAAgE,QAAA,eAAGlE,IAAA,WAAAkE,QAAA,CAAQ,uBAAM,CAAQ,CAAC,CAACV,MAAM,CAACW,IAAI,EAAI,CAAC,cAC3CjE,KAAA,MAAAgE,QAAA,eAAGlE,IAAA,WAAAkE,QAAA,CAAQ,uBAAM,CAAQ,CAAC,CAACV,MAAM,CAACY,IAAI,EAAI,CAAC,cAC3CpE,IAAA,MAAGqE,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,EAAG,CAAE,CAAAL,QAAA,CAAC,sFAAc,CAAG,CAAC,EAC9D,CACN,CACDM,IAAI,CAAE,SAAS,CACfC,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI,CACF,KAAM,CAAAlE,QAAQ,CAAChB,aAAa,CAACiE,MAAM,CAACG,EAAE,CAAC,CAAC,CAACe,MAAM,CAAC,CAAC,CACjDtH,OAAO,CAACuH,OAAO,CAAC,MAAM,CAAC,CACvB9B,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAO+B,KAAU,CAAE,CACnB/E,YAAY,CAACgF,WAAW,CAAC,CACvBL,IAAI,CAAE1E,SAAS,CAACgF,QAAQ,CACxB1H,OAAO,CAAEwH,KAAK,CAACxH,OAAO,EAAI,MAAM,CAChC2H,OAAO,CAAEH,KACX,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,CAACrE,QAAQ,CAAEsC,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAAmC,YAAY,CAAGrI,WAAW,CAAC,KAAO,CAAA6G,MAAe,EAAK,CAC1D,KAAM,CAAAyB,QAAQ,CAAGzB,MAAM,CAAC0B,MAAM,GAAKxF,UAAU,CAACyF,MAAM,CACpD,KAAM,CAAAC,MAAM,CAAGH,QAAQ,CAAG,IAAI,CAAG,IAAI,CAErCrF,aAAa,CAACmE,OAAO,CAAC,CACpBC,KAAK,gBAAAqB,MAAA,CAAOD,MAAM,CAAE,CACpBnB,OAAO,cACL/D,KAAA,QAAAgE,QAAA,eACEhE,KAAA,MAAAgE,QAAA,EAAG,oBAAG,CAACkB,MAAM,CAAC,6BAAO,EAAG,CAAC,cACzBlF,KAAA,MAAAgE,QAAA,eAAGlE,IAAA,WAAAkE,QAAA,CAAQ,uBAAM,CAAQ,CAAC,CAACV,MAAM,CAACW,IAAI,EAAI,CAAC,cAC3CjE,KAAA,MAAAgE,QAAA,eAAGlE,IAAA,WAAAkE,QAAA,CAAQ,uBAAM,CAAQ,CAAC,CAACV,MAAM,CAACY,IAAI,EAAI,CAAC,CAC1Ca,QAAQ,eACPjF,IAAA,MAAGqE,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,EAAG,CAAE,CAAAL,QAAA,CAAC,uEAAc,CAAG,CACjE,EACE,CACN,CACDM,IAAI,CAAES,QAAQ,CAAG,SAAS,CAAG,MAAM,CACnCR,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI,CACF,KAAM,CAAAlE,QAAQ,CAACf,aAAa,CAACgE,MAAM,CAACG,EAAE,CAAC,CAAC,CAACe,MAAM,CAAC,CAAC,CACjDtH,OAAO,CAACuH,OAAO,IAAAU,MAAA,CAAID,MAAM,gBAAI,CAAC,CAC9BvC,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAO+B,KAAU,CAAE,CACnB/E,YAAY,CAACgF,WAAW,CAAC,CACvBL,IAAI,CAAE1E,SAAS,CAACgF,QAAQ,CACxB1H,OAAO,CAAEwH,KAAK,CAACxH,OAAO,KAAAiI,MAAA,CAAOD,MAAM,gBAAI,CACvCL,OAAO,CAAEH,KACX,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,CAACrE,QAAQ,CAAEsC,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAAyC,UAAU,CAAG3I,WAAW,CAAE6G,MAAe,EAAK,CAClDpC,aAAa,CAACoC,MAAM,CAAC,CACrBnC,QAAQ,CAACkE,cAAc,CAAC,CACtBnB,IAAI,IAAAiB,MAAA,CAAK7B,MAAM,CAACY,IAAI,iBAAK,CACzBD,IAAI,IAAAkB,MAAA,CAAK7B,MAAM,CAACW,IAAI,SACtB,CAAC,CAAC,CACFjD,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAAE,CAACG,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAmE,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAApE,QAAQ,CAACqE,cAAc,CAAC,CAAC,CAC9C;AACAtI,OAAO,CAACuH,OAAO,CAAC,MAAM,CAAC,CACvBzD,mBAAmB,CAAC,KAAK,CAAC,CAC1B2B,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAO+B,KAAK,CAAE,CACdxH,OAAO,CAACwH,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAe,oBAAoB,CAAInC,MAAe,EAAK,CAChD9B,uBAAuB,CAAC8B,MAAM,CAAC,CAC/BhC,wBAAwB,CAAC,IAAI,CAAC,CAChC,CAAC,CAED,KAAM,CAAAoE,YAAY,CAAGA,CAAA,GAAM,CACzBhE,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAiE,YAAY,CAAGA,CAAA,GAAM,CACzB/D,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAAgE,sBAAsB,CAAGA,CAAA,GAAM,CACnC;AACA,KAAM,CAAAC,YAAY,CAAG,CACnB,CACE,OAAO,CAAE,QAAQ,CACjB,OAAO,CAAE,OAAO,CAChB,IAAI,CAAE,MAAM,CACZ,IAAI,CAAE,OAAO,CACb,IAAI,CAAE,WACR,CAAC,CACF,CAED;AACA,KAAM,CAAAC,OAAO,CAAGC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAAC,CAAC,CAAC,CAC5C,KAAM,CAAAI,UAAU,CAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,CAAE,GAAGL,YAAY,CAACM,GAAG,CAACC,GAAG,EAAIL,MAAM,CAACR,MAAM,CAACa,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAE3G;AACA,KAAM,CAAAG,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACL,UAAU,CAAC,CAAE,CAAE3B,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAiC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CACrCE,IAAI,CAACM,QAAQ,CAAG,uBAAuB,CACvCN,IAAI,CAACO,KAAK,CAAC,CAAC,CAEZ5J,OAAO,CAACuH,OAAO,CAAC,QAAQ,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAsC,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACF,KAAM,CAAAxB,MAAM,CAAG,KAAM,CAAA9C,UAAU,CAAC+C,cAAc,CAAC,CAAC,CAEhD;AACA,GAAI,CAAAwB,UAAU,CAAGC,QAAQ,CACzB,GAAI1E,eAAe,CAAC2E,MAAM,CAAG,CAAC,CAAE,CAC9BF,UAAU,CAAGC,QAAQ,CAACE,MAAM,CAACC,IAAI,EAAI7E,eAAe,CAAC8E,QAAQ,CAACD,IAAI,CAAC3D,EAAE,CAAC,CAAC,CACzE,CAEA;AACA,KAAM,CAAA6D,YAAY,CAAGN,UAAU,CAACb,GAAG,CAACiB,IAAI,EAAI,CAC1C,KAAM,CAAAG,YAAiB,CAAG,CAAC,CAAC,CAC5BxF,YAAY,CAACyF,OAAO,CAACC,KAAK,EAAI,CAC5B,OAAQA,KAAK,EACX,IAAK,MAAM,CACTF,YAAY,CAAC,OAAO,CAAC,CAAGH,IAAI,CAACnD,IAAI,CACjC,MACF,IAAK,MAAM,CACTsD,YAAY,CAAC,OAAO,CAAC,CAAGH,IAAI,CAAClD,IAAI,CACjC,MACF,IAAK,SAAS,CACZqD,YAAY,CAAC,IAAI,CAAC,CAAGH,IAAI,CAACM,OAAO,CACjC,MACF,IAAK,QAAQ,CACXH,YAAY,CAAC,IAAI,CAAC,CAAGI,aAAa,CAACP,IAAI,CAACpC,MAAM,CAAC,CAC/C,MACF,IAAK,aAAa,CAChBuC,YAAY,CAAC,IAAI,CAAC,CAAGH,IAAI,CAACQ,WAAW,CACrC,MACF,IAAK,WAAW,CACdL,YAAY,CAAC,KAAK,CAAC,CAAGH,IAAI,CAACS,SAAS,CACpC,MACF,IAAK,WAAW,CACdN,YAAY,CAAC,MAAM,CAAC,CAAG9H,UAAU,CAAC2H,IAAI,CAACU,SAAS,CAAC,CACjD,MACF,IAAK,WAAW,CACdP,YAAY,CAAC,MAAM,CAAC,CAAG9H,UAAU,CAAC2H,IAAI,CAACW,SAAS,CAAC,CACjD,MACJ,CACF,CAAC,CAAC,CACF,MAAO,CAAAR,YAAY,CACrB,CAAC,CAAC,CAEF;AACA,GAAI1F,YAAY,GAAK,OAAO,CAAE,CAC5B;AACA3E,OAAO,CAAC8K,IAAI,CAAC,oBAAoB,CAAC,CACpC,CAAC,IAAM,CACL;AACA,KAAM,CAAAlC,OAAO,CAAGC,MAAM,CAACC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAAC,EAAI,CAAC,CAAC,CAAC,CAClD,KAAM,CAAArB,UAAU,CAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,CAAE,GAAGoB,YAAY,CAACnB,GAAG,CAACC,GAAG,EAAIL,MAAM,CAACR,MAAM,CAACa,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAE3G,KAAM,CAAAG,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACL,UAAU,CAAC,CAAE,CAAE3B,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAiC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CACrCE,IAAI,CAACM,QAAQ,oBAAA1B,MAAA,CAAsB,GAAI,CAAA8C,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,QAAM,CAC7D3B,IAAI,CAACO,KAAK,CAAC,CAAC,CACd,CAEApF,qBAAqB,CAAC,KAAK,CAAC,CAC5BxE,OAAO,CAACuH,OAAO,CAAC,MAAM,CAAC,CACzB,CAAE,MAAOC,KAAK,CAAE,CACdxH,OAAO,CAACwH,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAAyD,gBAAgB,CAAIC,IAAU,EAAK,CACvChG,eAAe,CAAC,WAAW,CAAC,CAC5BF,iBAAiB,CAAC,CAAC,CAAC,CAEpB;AACA,KAAM,CAAAmG,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjCpG,iBAAiB,CAACqG,IAAI,EAAI,CACxB,GAAIA,IAAI,EAAI,GAAG,CAAE,CACfC,aAAa,CAACH,QAAQ,CAAC,CACvBjG,eAAe,CAAC,YAAY,CAAC,CAE7B;AACAqG,UAAU,CAAC,IAAM,CACfC,iBAAiB,CAACN,IAAI,CAAC,CACzB,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAG,CACZ,CACA,MAAO,CAAAG,IAAI,CAAG,EAAE,CAClB,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,MAAK,CAAE;AAChB,CAAC,CAED;AACA,KAAM,CAAAG,iBAAiB,CAAIN,IAAU,EAAK,CACxC,KAAM,CAAAO,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAIC,CAAC,EAAK,CACrB,GAAI,KAAAC,SAAA,CACF,KAAM,CAAAhF,OAAO,EAAAgF,SAAA,CAAGD,CAAC,CAACE,MAAM,UAAAD,SAAA,iBAARA,SAAA,CAAUE,MAAgB,CAC1C,KAAM,CAAAC,KAAK,CAAGnF,OAAO,CAACoF,KAAK,CAAC,IAAI,CAAC,CACjC,KAAM,CAAArD,OAAO,CAAGoD,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAEnC,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,KAAM,CAAAC,MAAM,CAAG,EAAE,CAEjB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,KAAK,CAAChC,MAAM,CAAEoC,CAAC,EAAE,CAAE,CACrC,GAAIJ,KAAK,CAACI,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAE,CACnB,KAAM,CAAAhE,MAAM,CAAG2D,KAAK,CAACI,CAAC,CAAC,CAACH,KAAK,CAAC,GAAG,CAAC,CAClC,KAAM,CAAA/C,GAAQ,CAAG,CAAC,CAAC,CAEnBN,OAAO,CAAC0B,OAAO,CAAC,CAACgC,MAAM,CAAEC,KAAK,GAAK,KAAAC,aAAA,CACjCtD,GAAG,CAACoD,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,EAAAG,aAAA,CAAGnE,MAAM,CAACkE,KAAK,CAAC,UAAAC,aAAA,iBAAbA,aAAA,CAAeH,IAAI,CAAC,CAAC,CAC5C,CAAC,CAAC,CAEF;AACA,GAAI,CAACnD,GAAG,CAAC,OAAO,CAAC,EAAI,CAACA,GAAG,CAAC,OAAO,CAAC,CAAE,CAClCiD,MAAM,CAACM,IAAI,UAAAxE,MAAA,CAAKmE,CAAC,CAAG,CAAC,yEAAgB,CAAC,CACxC,CAAC,IAAM,CACLF,YAAY,CAACO,IAAI,CAAC,CAChB1F,IAAI,CAAEmC,GAAG,CAAC,OAAO,CAAC,CAClBlC,IAAI,CAAEkC,GAAG,CAAC,OAAO,CAAC,CAClBsB,OAAO,CAAEtB,GAAG,CAAC,IAAI,CAAC,EAAI,MAAM,CAC5BpB,MAAM,CAAEoB,GAAG,CAAC,IAAI,CAAC,EAAI,OAAO,CAC5BwB,WAAW,CAAExB,GAAG,CAAC,IAAI,CAAC,EAAI,EAC5B,CAAC,CAAC,CACJ,CACF,CACF,CAEA9D,eAAe,CAAC,CACdsH,KAAK,CAAEV,KAAK,CAAChC,MAAM,CAAG,CAAC,CACvBzC,OAAO,CAAE2E,YAAY,CAAClC,MAAM,CAC5BmC,MAAM,CAAEA,MAAM,CAACnC,MAAM,CACrB2C,IAAI,CAAET,YAAY,CAClBU,aAAa,CAAET,MACjB,CAAC,CAAC,CAEFjH,eAAe,CAACiH,MAAM,CAACnC,MAAM,CAAG,CAAC,CAAG,OAAO,CAAG,SAAS,CAAC,CAExD,GAAImC,MAAM,CAACnC,MAAM,GAAK,CAAC,CAAE,CACvBhK,OAAO,CAACuH,OAAO,4BAAAU,MAAA,CAAQiE,YAAY,CAAClC,MAAM,sBAAK,CAAC,CAChD;AACF,CAAC,IAAM,CACLhK,OAAO,CAAC6M,OAAO,kCAAA5E,MAAA,CAASkE,MAAM,CAACnC,MAAM,wCAAQ,CAAC,CAChD,CAEF,CAAE,MAAOxC,KAAK,CAAE,CACdtC,eAAe,CAAC,OAAO,CAAC,CACxBE,eAAe,CAAC,CACdsH,KAAK,CAAE,CAAC,CACRnF,OAAO,CAAE,CAAC,CACV4E,MAAM,CAAE,CAAC,CACTS,aAAa,CAAE,CAAC,gBAAgB,CAClC,CAAC,CAAC,CACF5M,OAAO,CAACwH,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CAAC,CAEDiE,MAAM,CAACqB,UAAU,CAAC5B,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAA6B,YAAY,CAAG,CACnB1H,eAAe,CACf2H,QAAQ,CAAGC,kBAA+B,EAAK,CAC7C3H,kBAAkB,CAAC2H,kBAAkB,CAAC,CACxC,CAAC,CACDC,WAAW,CAAEA,CAACC,QAAiB,CAAEC,YAAuB,CAAEC,UAAqB,GAAK,CAClFC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEJ,QAAQ,CAAEC,YAAY,CAAEC,UAAU,CAAC,CAChE,CACF,CAAC,CAED;AACA,KAAM,CAAAG,oBAAoB,CAAGA,CAAA,GAAM,CACjClI,kBAAkB,CAAC,EAAE,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAmI,oBAAoB,CAAGA,CAAA,GAAM,CACjC,GAAIpI,eAAe,CAAC2E,MAAM,GAAK,CAAC,CAAE,CAChChK,OAAO,CAAC6M,OAAO,CAAC,YAAY,CAAC,CAC7B,OACF,CACArI,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAAkJ,iBAAiB,CAAIlK,GAAY,EAAK,CAC1C,MAAO,CACL,CACEgH,OAAO,CAAE,MAAM,CACf1C,MAAM,CAAE,QAAQ,CAChB4C,WAAW,CAAE,gBAAgB,CAC7BC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,qBAAqB,CAChC+C,OAAO,CAAE,CAAC,SAAS,CAAE,QAAQ,CAAE,QAAQ,CACzC,CAAC,CACD,CACEnD,OAAO,CAAE,MAAM,CACf1C,MAAM,CAAE,QAAQ,CAChB4C,WAAW,CAAE,UAAU,CACvBC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,qBAAqB,CAChC+C,OAAO,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAC9B,CAAC,CACD,CACEnD,OAAO,CAAE,MAAM,CACf1C,MAAM,CAAE,UAAU,CAClB4C,WAAW,CAAE,MAAM,CACnBC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,qBAAqB,CAChC+C,OAAO,CAAE,CAAC,WAAW,CACvB,CAAC,CACF,CACH,CAAC,CAED,KAAM,CAAAC,cAAc,CAAI9F,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,CAAAxF,UAAU,CAACuL,KAAK,CAAE,MAAO,SAAS,CACvC,IAAK,CAAAvL,UAAU,CAACyF,MAAM,CAAE,MAAO,SAAS,CACxC,IAAK,CAAAzF,UAAU,CAACwL,MAAM,CAAE,MAAO,MAAM,CACrC,IAAK,CAAAxL,UAAU,CAACyL,QAAQ,CAAE,MAAO,OAAO,CACxC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAtD,aAAa,CAAI3C,MAAc,EAAK,CACxC,OAAQA,MAAM,EACZ,IAAK,CAAAxF,UAAU,CAACuL,KAAK,CAAE,MAAO,IAAI,CAClC,IAAK,CAAAvL,UAAU,CAACyF,MAAM,CAAE,MAAO,IAAI,CACnC,IAAK,CAAAzF,UAAU,CAACwL,MAAM,CAAE,MAAO,IAAI,CACnC,IAAK,CAAAxL,UAAU,CAACyL,QAAQ,CAAE,MAAO,IAAI,CACrC,QAAS,MAAO,CAAAjG,MAAM,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAAkG,kBAAkB,CAAG,CACzB,CAAEC,KAAK,CAAE,OAAO,CAAElI,KAAK,CAAE,MAAO,CAAC,CACjC,CAAEkI,KAAK,CAAE,OAAO,CAAElI,KAAK,CAAE,MAAO,CAAC,CACjC,CAAEkI,KAAK,CAAE,IAAI,CAAElI,KAAK,CAAE,SAAU,CAAC,CACjC,CAAEkI,KAAK,CAAE,IAAI,CAAElI,KAAK,CAAE,QAAS,CAAC,CAChC,CAAEkI,KAAK,CAAE,IAAI,CAAElI,KAAK,CAAE,aAAc,CAAC,CACrC,CAAEkI,KAAK,CAAE,KAAK,CAAElI,KAAK,CAAE,WAAY,CAAC,CACpC,CAAEkI,KAAK,CAAE,MAAM,CAAElI,KAAK,CAAE,WAAY,CAAC,CACrC,CAAEkI,KAAK,CAAE,MAAM,CAAElI,KAAK,CAAE,WAAY,CAAC,CACtC,CAED,KAAM,CAAAmI,kBAAkB,CAAI9H,MAAe,EAAyB,CAClE,CACE+H,GAAG,CAAE,MAAM,CACXC,IAAI,cAAExL,IAAA,CAACxB,YAAY,GAAE,CAAC,CACtB6M,KAAK,CAAE,IAAI,CACXI,OAAO,CAAEA,CAAA,GAAMnG,UAAU,CAAC9B,MAAM,CAClC,CAAC,CACD,CACE+H,GAAG,CAAE,SAAS,CACdC,IAAI,cAAExL,IAAA,CAAClB,eAAe,GAAE,CAAC,CACzBuM,KAAK,CAAE,MAAM,CACbI,OAAO,CAAEA,CAAA,GAAM9F,oBAAoB,CAACnC,MAAM,CAC5C,CAAC,CACD,CACE+H,GAAG,CAAE,QAAQ,CACbC,IAAI,CAAEhI,MAAM,CAAC0B,MAAM,GAAKxF,UAAU,CAACwL,MAAM,cAAGlL,IAAA,CAACtB,cAAc,GAAE,CAAC,cAAGsB,IAAA,CAACvB,YAAY,GAAE,CAAC,CACjF4M,KAAK,CAAE7H,MAAM,CAAC0B,MAAM,GAAKxF,UAAU,CAACwL,MAAM,CAAG,IAAI,CAAG,IAAI,CACxDO,OAAO,CAAEA,CAAA,GAAMzG,YAAY,CAACxB,MAAM,CAAC,CACnCkI,QAAQ,CAAElI,MAAM,CAAC0B,MAAM,GAAKxF,UAAU,CAACuL,KACzC,CAAC,CACF,CAED,KAAM,CAAAU,OAAO,CAAG,CACd,CACE3H,KAAK,CAAE,OAAO,CACd4H,SAAS,CAAE,MAAM,CACjBL,GAAG,CAAE,MAAM,CACXM,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACC,IAAY,CAAEvI,MAAe,gBACpCxD,IAAA,CAAClD,MAAM,EAAC0H,IAAI,CAAC,MAAM,CAACiH,OAAO,CAAEA,CAAA,GAAM7H,UAAU,CAACJ,MAAM,CAAE,CAAAU,QAAA,CACnD6H,IAAI,CACC,CAEZ,CAAC,CACD,CACE/H,KAAK,CAAE,OAAO,CACd4H,SAAS,CAAE,MAAM,CACjBL,GAAG,CAAE,MAAM,CACXS,QAAQ,CAAE,IACZ,CAAC,CACD,CACEhI,KAAK,CAAE,IAAI,CACX4H,SAAS,CAAE,SAAS,CACpBL,GAAG,CAAE,SAAS,CACdM,KAAK,CAAE,EACT,CAAC,CACD,CACE7H,KAAK,CAAE,IAAI,CACX4H,SAAS,CAAE,QAAQ,CACnBL,GAAG,CAAE,QAAQ,CACbM,KAAK,CAAE,EAAE,CACTC,MAAM,CAAG5G,MAAc,eACrBlF,IAAA,CAAC7C,GAAG,EAACmH,KAAK,CAAE0G,cAAc,CAAC9F,MAAM,CAAE,CAAAhB,QAAA,CAChC2D,aAAa,CAAC3C,MAAM,CAAC,CACnB,CAET,CAAC,CACD,CACElB,KAAK,CAAE,IAAI,CACX4H,SAAS,CAAE,aAAa,CACxBL,GAAG,CAAE,aAAa,CAClBS,QAAQ,CAAE,IACZ,CAAC,CACD,CACEhI,KAAK,CAAE,KAAK,CACZ4H,SAAS,CAAE,WAAW,CACtBL,GAAG,CAAE,WAAW,CAChBM,KAAK,CAAE,GACT,CAAC,CACD,CACE7H,KAAK,CAAE,MAAM,CACb4H,SAAS,CAAE,WAAW,CACtBL,GAAG,CAAE,WAAW,CAChBM,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGG,IAAY,EAAKtM,UAAU,CAACsM,IAAI,CAC3C,CAAC,CACD,CACEjI,KAAK,CAAE,MAAM,CACb4H,SAAS,CAAE,WAAW,CACtBL,GAAG,CAAE,WAAW,CAChBM,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGG,IAAY,EAAKtM,UAAU,CAACsM,IAAI,CAC3C,CAAC,CACD,CACEjI,KAAK,CAAE,IAAI,CACXuH,GAAG,CAAE,QAAQ,CACbM,KAAK,CAAE,GAAG,CACVK,KAAK,CAAE,OAAgB,CACvBJ,MAAM,CAAEA,CAACK,CAAM,CAAE3I,MAAe,gBAC9BtD,KAAA,CAACnD,KAAK,EAACqP,IAAI,CAAC,OAAO,CAAAlI,QAAA,eACjBlE,IAAA,CAACtC,OAAO,EAACsG,KAAK,CAAC,cAAI,CAAAE,QAAA,cACjBlE,IAAA,CAAClD,MAAM,EACL0H,IAAI,CAAC,MAAM,CACXgH,IAAI,cAAExL,IAAA,CAACzB,WAAW,GAAE,CAAE,CACtBkN,OAAO,CAAEA,CAAA,GAAM7H,UAAU,CAACJ,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACVxD,IAAA,CAACtC,OAAO,EAACsG,KAAK,CAAC,cAAI,CAAAE,QAAA,cACjBlE,IAAA,CAAClD,MAAM,EACL0H,IAAI,CAAC,MAAM,CACXgH,IAAI,cAAExL,IAAA,CAAC3B,YAAY,GAAE,CAAE,CACvBoN,OAAO,CAAEA,CAAA,GAAMlI,UAAU,CAACC,MAAM,CAAE,CAClCkI,QAAQ,CAAElI,MAAM,CAAC0B,MAAM,GAAKxF,UAAU,CAACwL,MAAO,CAC/C,CAAC,CACK,CAAC,cACVlL,IAAA,CAACtC,OAAO,EAACsG,KAAK,CAAC,cAAI,CAAAE,QAAA,cACjBlE,IAAA,CAAClD,MAAM,EACL0H,IAAI,CAAC,MAAM,CACX6H,MAAM,MACNb,IAAI,cAAExL,IAAA,CAAC1B,cAAc,GAAE,CAAE,CACzBmN,OAAO,CAAEA,CAAA,GAAM3H,YAAY,CAACN,MAAM,CAAE,CACpCkI,QAAQ,CAAElI,MAAM,CAAC0B,MAAM,GAAKxF,UAAU,CAACuL,KAAM,CAC9C,CAAC,CACK,CAAC,cACVjL,IAAA,CAACrC,QAAQ,EACP2O,IAAI,CAAE,CAAEC,KAAK,CAAEjB,kBAAkB,CAAC9H,MAAM,CAAE,CAAE,CAC5CgJ,OAAO,CAAE,CAAC,OAAO,CAAE,CAAAtI,QAAA,cAEnBlE,IAAA,CAAClD,MAAM,EAAC0H,IAAI,CAAC,MAAM,CAACgH,IAAI,cAAExL,IAAA,CAACrB,YAAY,GAAE,CAAE,CAAE,CAAC,CACtC,CAAC,EACN,CAEX,CAAC,CACF,CAED;AACA,KAAM,CAAAwI,QAAmB,CAAG,CAC1B,CACExD,EAAE,CAAE,GAAG,CACPS,IAAI,CAAE,WAAW,CACjBD,IAAI,CAAE,YAAY,CAClByD,OAAO,CAAE,MAAM,CACfE,WAAW,CAAE,gBAAgB,CAC7B5C,MAAM,CAAE,QAAQ,CAChBqH,KAAK,CAAE,EAAE,CACTE,WAAW,CAAE,EAAE,CACf1E,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBACb,CAAC,CACD,CACEtE,EAAE,CAAE,GAAG,CACPS,IAAI,CAAE,WAAW,CACjBD,IAAI,CAAE,YAAY,CAClByD,OAAO,CAAE,MAAM,CACfE,WAAW,CAAE,cAAc,CAC3B5C,MAAM,CAAE,QAAQ,CAChBqH,KAAK,CAAE,EAAE,CACTE,WAAW,CAAE,EAAE,CACf1E,SAAS,CAAE,aAAa,CACxBC,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBAAsB,CACjCyE,QAAQ,CAAE,sBAAsB,CAChCC,QAAQ,CAAE,OACZ,CAAC,CACD,CACEhJ,EAAE,CAAE,GAAG,CACPS,IAAI,CAAE,WAAW,CACjBD,IAAI,CAAE,cAAc,CACpByD,OAAO,CAAE,MAAM,CACfE,WAAW,CAAE,aAAa,CAC1B5C,MAAM,CAAE,OAAO,CACfqH,KAAK,CAAE,EAAE,CACTE,WAAW,CAAE,EAAE,CACf1E,SAAS,CAAE,aAAa,CACxBC,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBACb,CAAC,CACF,CAED,mBACE/H,KAAA,QAAAgE,QAAA,eACEhE,KAAA,CAAChD,IAAI,EAAAgH,QAAA,eACHhE,KAAA,CAAC1C,GAAG,EAACoP,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACxI,KAAK,CAAE,CAAEyI,YAAY,CAAE,EAAG,CAAE,CAAA5I,QAAA,eACtElE,IAAA,CAACvC,GAAG,EAAAyG,QAAA,cACFlE,IAAA,CAACG,KAAK,EAAC4M,KAAK,CAAE,CAAE,CAAC1I,KAAK,CAAE,CAAE2I,MAAM,CAAE,CAAE,CAAE,CAAA9I,QAAA,CAAC,6BAEvC,CAAO,CAAC,CACL,CAAC,cACNlE,IAAA,CAACvC,GAAG,EAAAyG,QAAA,cACFhE,KAAA,CAACnD,KAAK,EAAAmH,QAAA,eACJlE,IAAA,CAAClD,MAAM,EAAC0O,IAAI,cAAExL,IAAA,CAACnB,cAAc,GAAE,CAAE,CAAC4M,OAAO,CAAE5F,YAAa,CAAA3B,QAAA,CAAC,cAEzD,CAAQ,CAAC,cACTlE,IAAA,CAAClD,MAAM,EAAC0O,IAAI,cAAExL,IAAA,CAACpB,cAAc,GAAE,CAAE,CAAC6M,OAAO,CAAE7F,YAAa,CAAA1B,QAAA,CAAC,cAEzD,CAAQ,CAAC,cACTlE,IAAA,CAAClD,MAAM,EAAC0H,IAAI,CAAC,SAAS,CAACgH,IAAI,cAAExL,IAAA,CAAC5B,YAAY,GAAE,CAAE,CAACqN,OAAO,CAAEpI,YAAa,CAAAa,QAAA,CAAC,iBAEtE,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAENhE,KAAA,CAAC1C,GAAG,EAACyP,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC5I,KAAK,CAAE,CAAEyI,YAAY,CAAE,EAAG,CAAE,CAAA5I,QAAA,eACjDlE,IAAA,CAACvC,GAAG,EAACyP,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlJ,QAAA,cACzBlE,IAAA,CAACI,MAAM,EACLiN,WAAW,CAAC,+CAAY,CACxBC,UAAU,MACVC,QAAQ,CAAErK,YAAa,CACvBmB,KAAK,CAAE,CAAEwH,KAAK,CAAE,MAAO,CAAE,CAC1B,CAAC,CACC,CAAC,cACN7L,IAAA,CAACvC,GAAG,EAACyP,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlJ,QAAA,cACzBlE,IAAA,CAAC/C,MAAM,EACLoQ,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVjJ,KAAK,CAAE,CAAEwH,KAAK,CAAE,MAAO,CAAE,CACzBzB,QAAQ,CAAEhH,kBAAmB,CAC7BoK,OAAO,CAAE,CACP,CAAEnC,KAAK,CAAE,IAAI,CAAElI,KAAK,CAAEzD,UAAU,CAACuL,KAAM,CAAC,CACxC,CAAEI,KAAK,CAAE,IAAI,CAAElI,KAAK,CAAEzD,UAAU,CAACyF,MAAO,CAAC,CACzC,CAAEkG,KAAK,CAAE,IAAI,CAAElI,KAAK,CAAEzD,UAAU,CAACwL,MAAO,CAAC,CACzC,CAAEG,KAAK,CAAE,IAAI,CAAElI,KAAK,CAAEzD,UAAU,CAACyL,QAAS,CAAC,CAC3C,CACH,CAAC,CACC,CAAC,EACH,CAAC,CAEL1I,eAAe,CAAC2E,MAAM,CAAG,CAAC,eACzBlH,KAAA,QAAKmE,KAAK,CAAE,CAAEyI,YAAY,CAAE,EAAE,CAAEW,OAAO,CAAE,UAAU,CAAEC,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAzJ,QAAA,eACjGhE,KAAA,SAAMmE,KAAK,CAAE,CAAEuJ,WAAW,CAAE,EAAG,CAAE,CAAA1J,QAAA,EAAC,qBAAI,CAACzB,eAAe,CAAC2E,MAAM,CAAC,SAAE,EAAM,CAAC,cACvEpH,IAAA,CAAClD,MAAM,EAACsP,IAAI,CAAC,OAAO,CAACX,OAAO,CAAEb,oBAAqB,CAACvG,KAAK,CAAE,CAAEuJ,WAAW,CAAE,CAAE,CAAE,CAAA1J,QAAA,CAAC,0BAE/E,CAAQ,CAAC,cACTlE,IAAA,CAAClD,MAAM,EAACsP,IAAI,CAAC,OAAO,CAAC5H,IAAI,CAAC,SAAS,CAACgH,IAAI,cAAExL,IAAA,CAACjB,gBAAgB,GAAE,CAAE,CAAC0M,OAAO,CAAEZ,oBAAqB,CAAA3G,QAAA,CAAC,0BAE/F,CAAQ,CAAC,EACN,CACN,cAEDlE,IAAA,CAACnD,KAAK,EACJ8O,OAAO,CAAEA,OAAQ,CACjBkC,UAAU,CAAE1G,QAAS,CACrB1G,OAAO,CAAEA,OAAQ,CACjBqN,MAAM,CAAC,IAAI,CACX3D,YAAY,CAAEA,YAAa,CAC3B4D,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBtN,UAAU,CAAE,CACVoC,OAAO,CAAEpC,UAAU,CAACoC,OAAO,CAC3BC,QAAQ,CAAErC,UAAU,CAACqC,QAAQ,CAC7B+G,KAAK,CAAEpJ,UAAU,CAACoJ,KAAK,CACvBmE,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACrE,KAAK,CAAEsE,KAAK,aAAA/I,MAAA,CACjB+I,KAAK,CAAC,CAAC,CAAC,MAAA/I,MAAA,CAAI+I,KAAK,CAAC,CAAC,CAAC,oBAAA/I,MAAA,CAAQyE,KAAK,WAC1C,CAAE,CACH,CAAC,EACE,CAAC,cAGP9J,IAAA,CAAC3C,KAAK,EACJ2G,KAAK,CAAC,iBAAO,CACbqK,IAAI,CAAEpN,gBAAiB,CACvBqN,IAAI,CAAE9I,iBAAkB,CACxB+I,QAAQ,CAAEA,CAAA,GAAMrN,mBAAmB,CAAC,KAAK,CAAE,CAC3CsN,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAvK,QAAA,cAEfhE,KAAA,CAAC5C,IAAI,EAACoR,IAAI,CAAErN,QAAS,CAACsN,MAAM,CAAC,UAAU,CAAAzK,QAAA,eACrClE,IAAA,CAAC1C,IAAI,CAACsR,IAAI,EACRxK,IAAI,CAAC,MAAM,CACXiH,KAAK,CAAC,iBAAO,CACbwD,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE1R,OAAO,CAAE,UAAW,CAAC,CAAE,CAAA8G,QAAA,cAEjDlE,IAAA,CAAChD,KAAK,EAACqQ,WAAW,CAAC,mCAAU,CAAE,CAAC,CACvB,CAAC,cACZrN,IAAA,CAAC1C,IAAI,CAACsR,IAAI,EACRxK,IAAI,CAAC,MAAM,CACXiH,KAAK,CAAC,iBAAO,CACbwD,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE1R,OAAO,CAAE,UAAW,CAAC,CAAE,CAAA8G,QAAA,cAEjDlE,IAAA,CAAChD,KAAK,EAACqQ,WAAW,CAAC,mCAAU,CAAE,CAAC,CACvB,CAAC,EACR,CAAC,CACF,CAAC,cAGRrN,IAAA,CAAC3C,KAAK,EACF2G,KAAK,+BAAAqB,MAAA,CAAY5D,oBAAoB,SAApBA,oBAAoB,iBAApBA,oBAAoB,CAAE2C,IAAI,CAAG,CAC9CiK,IAAI,CAAE9M,qBAAsB,CAC5BgN,QAAQ,CAAEA,CAAA,GAAM/M,wBAAwB,CAAC,KAAK,CAAE,CAChDuN,MAAM,CAAE,cACN/O,IAAA,CAAClD,MAAM,EAAa2O,OAAO,CAAEA,CAAA,GAAMjK,wBAAwB,CAAC,KAAK,CAAE,CAAA0C,QAAA,CAAC,cAEpE,EAFY,OAEJ,CAAC,CACT,CACF2H,KAAK,CAAE,GAAI,CAAA3H,QAAA,CAEVzC,oBAAoB,eACnBvB,KAAA,QAAAgE,QAAA,eACEhE,KAAA,CAACjC,YAAY,EACX+F,KAAK,CAAC,6BAAS,CACfgL,QAAQ,MACR5C,IAAI,CAAC,OAAO,CACZ/H,KAAK,CAAE,CAAEyI,YAAY,CAAE,EAAG,CAAE,CAAA5I,QAAA,eAE5BlE,IAAA,CAAC/B,YAAY,CAAC2Q,IAAI,EAACvD,KAAK,CAAC,iBAAO,CAAAnH,QAAA,CAAEzC,oBAAoB,CAAC0C,IAAI,CAAoB,CAAC,cAChFnE,IAAA,CAAC/B,YAAY,CAAC2Q,IAAI,EAACvD,KAAK,CAAC,iBAAO,CAAAnH,QAAA,CAAEzC,oBAAoB,CAAC2C,IAAI,CAAoB,CAAC,cAChFpE,IAAA,CAAC/B,YAAY,CAAC2Q,IAAI,EAACvD,KAAK,CAAC,0BAAM,CAAAnH,QAAA,CAAEzC,oBAAoB,CAACmG,OAAO,CAAoB,CAAC,cAClF5H,IAAA,CAAC/B,YAAY,CAAC2Q,IAAI,EAACvD,KAAK,CAAC,cAAI,CAAC4D,IAAI,CAAE,CAAE,CAAA/K,QAAA,cACpClE,IAAA,CAAC7C,GAAG,EAACmH,KAAK,CAAE0G,cAAc,CAACvJ,oBAAoB,CAACyD,MAAM,CAAE,CAAAhB,QAAA,CACrD2D,aAAa,CAACpG,oBAAoB,CAACyD,MAAM,CAAC,CACxC,CAAC,CACW,CAAC,EACR,CAAC,cAEflF,IAAA,CAACjC,OAAO,EAAAmG,QAAA,CAAC,0BAAI,CAAS,CAAC,cAEvBlE,IAAA,CAAChC,QAAQ,EAAAkG,QAAA,CACN4G,iBAAiB,CAACrJ,oBAAoB,CAAC,CAAC4E,GAAG,CAAC,CAACuB,OAAO,CAAE+B,KAAK,gBAC1D3J,IAAA,CAAChC,QAAQ,CAAC4Q,IAAI,EAEZM,GAAG,cAAElP,IAAA,CAACb,mBAAmB,EAACkF,KAAK,CAAE,CAAE8K,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAE,CAC1D7K,KAAK,CAAEsD,OAAO,CAAC1C,MAAM,GAAK,QAAQ,CAAG,OAAO,CAAG0C,OAAO,CAAC1C,MAAM,GAAK,QAAQ,CAAG,MAAM,CAAG,MAAO,CAAAhB,QAAA,cAE7FhE,KAAA,QAAKmE,KAAK,CAAE,CAAEyI,YAAY,CAAE,EAAG,CAAE,CAAA5I,QAAA,eAC/BhE,KAAA,QAAKmE,KAAK,CAAE,CAAE+K,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAExC,YAAY,CAAE,CAAE,CAAE,CAAA5I,QAAA,eACtGlE,IAAA,SAAMqE,KAAK,CAAE,CAAE8K,QAAQ,CAAE,EAAE,CAAEI,UAAU,CAAE,MAAO,CAAE,CAAArL,QAAA,CAAE0D,OAAO,CAACA,OAAO,CAAO,CAAC,cAC3E5H,IAAA,CAAC7C,GAAG,EAACmH,KAAK,CAAE0G,cAAc,CAACpD,OAAO,CAAC1C,MAAM,CAAE,CAAAhB,QAAA,CACxC2D,aAAa,CAACD,OAAO,CAAC1C,MAAM,CAAC,CAC3B,CAAC,EACH,CAAC,cACNhF,KAAA,QAAKmE,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEwI,YAAY,CAAE,CAAE,CAAE,CAAA5I,QAAA,EAC5C0D,OAAO,CAACG,SAAS,CAAC,QAAG,CAACH,OAAO,CAACI,SAAS,EACrC,CAAC,cACNhI,IAAA,QAAKqE,KAAK,CAAE,CAAEyI,YAAY,CAAE,CAAE,CAAE,CAAA5I,QAAA,CAAE0D,OAAO,CAACE,WAAW,CAAM,CAAC,CAC3DF,OAAO,CAACmD,OAAO,EAAInD,OAAO,CAACmD,OAAO,CAAC3D,MAAM,CAAG,CAAC,eAC5ClH,KAAA,QAAAgE,QAAA,eACElE,IAAA,QAAKqE,KAAK,CAAE,CAAEkL,UAAU,CAAE,MAAM,CAAEzC,YAAY,CAAE,CAAE,CAAE,CAAA5I,QAAA,CAAC,gCAAK,CAAK,CAAC,cAChElE,IAAA,OAAIqE,KAAK,CAAE,CAAE2I,MAAM,CAAE,CAAC,CAAEwC,WAAW,CAAE,EAAG,CAAE,CAAAtL,QAAA,CACvC0D,OAAO,CAACmD,OAAO,CAAC1E,GAAG,CAAC,CAACoJ,MAAM,CAAEC,WAAW,gBACvC1P,IAAA,OAAAkE,QAAA,CAAuBuL,MAAM,EAApBC,WAAyB,CACnC,CAAC,CACA,CAAC,EACF,CACN,EACE,CAAC,EAzBD/F,KA0BQ,CAChB,CAAC,CACM,CAAC,EACR,CACN,CACE,CAAC,cAGR3J,IAAA,CAAC3C,KAAK,EACF2G,KAAK,CAAC,6BAAS,CACfqK,IAAI,CAAE1M,kBAAmB,CACzB2M,IAAI,CAAErH,mBAAoB,CAC1BsH,QAAQ,CAAEA,CAAA,GAAM3M,qBAAqB,CAAC,KAAK,CAAE,CAC7CiK,KAAK,CAAE,GAAI,CAAA3H,QAAA,cAEXhE,KAAA,CAAC5C,IAAI,EAACoR,IAAI,CAAE/L,UAAW,CAACgM,MAAM,CAAC,UAAU,CAAAzK,QAAA,eACvClE,IAAA,CAAClC,KAAK,EACJV,OAAO,CAAC,0BAAM,CACd0K,WAAW,CAAErF,eAAe,CAAC2E,MAAM,CAAG,CAAC,+CAAA/B,MAAA,CAAc5C,eAAe,CAAC2E,MAAM,2BAAY,YAAa,CACpG5C,IAAI,CAAC,MAAM,CACXH,KAAK,CAAE,CAAEyI,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEF9M,IAAA,CAAC1C,IAAI,CAACsR,IAAI,EAACvD,KAAK,CAAC,0BAAM,CAACjH,IAAI,CAAC,QAAQ,CAACuL,YAAY,CAAE5N,YAAa,CAAAmC,QAAA,cAC/DhE,KAAA,CAAC/B,KAAK,CAACyR,KAAK,EAACxF,QAAQ,CAAGpB,CAAC,EAAKhH,eAAe,CAACgH,CAAC,CAACE,MAAM,CAAC/F,KAAK,CAAE,CAAAe,QAAA,eAC5DlE,IAAA,CAAC7B,KAAK,EAACgF,KAAK,CAAC,OAAO,CAAAe,QAAA,CAAC,2BAAe,CAAO,CAAC,cAC5ClE,IAAA,CAAC7B,KAAK,EAACgF,KAAK,CAAC,KAAK,CAAAe,QAAA,CAAC,wBAAY,CAAO,CAAC,EAC5B,CAAC,CACL,CAAC,cAEZlE,IAAA,CAAC1C,IAAI,CAACsR,IAAI,EAACvD,KAAK,CAAC,0BAAM,CAACjH,IAAI,CAAC,QAAQ,CAACuL,YAAY,CAAE1N,YAAa,CAAAiC,QAAA,cAC/DlE,IAAA,CAAC9B,QAAQ,CAAC0R,KAAK,EACbpC,OAAO,CAAEpC,kBAAmB,CAC5BjI,KAAK,CAAElB,YAAa,CACpBmI,QAAQ,CAAElI,eAAgB,CAC3B,CAAC,CACO,CAAC,EACR,CAAC,CACJ,CAAC,cAGRlC,IAAA,CAAC3C,KAAK,EACF2G,KAAK,CAAC,6BAAS,CACfqK,IAAI,CAAExM,kBAAmB,CACzB0M,QAAQ,CAAEA,CAAA,GAAM,CACdzM,qBAAqB,CAAC,KAAK,CAAC,CAC5BQ,eAAe,CAAC,IAAI,CAAC,CACrBF,iBAAiB,CAAC,CAAC,CAAC,CACpBI,eAAe,CAAC,IAAI,CAAC,CACrBI,UAAU,CAACiN,WAAW,CAAC,CAAC,CAC1B,CAAE,CACFd,MAAM,CAAE,cACN/O,IAAA,CAAClD,MAAM,EAAgB0O,IAAI,cAAExL,IAAA,CAACjB,gBAAgB,GAAE,CAAE,CAAC0M,OAAO,CAAE3F,sBAAuB,CAAA5B,QAAA,CAAC,0BAEpF,EAFY,UAEJ,CAAC,cACTlE,IAAA,CAAClD,MAAM,EAAc2O,OAAO,CAAEA,CAAA,GAAM,CAClC3J,qBAAqB,CAAC,KAAK,CAAC,CAC5BQ,eAAe,CAAC,IAAI,CAAC,CACrBF,iBAAiB,CAAC,CAAC,CAAC,CACpBI,eAAe,CAAC,IAAI,CAAC,CACrBI,UAAU,CAACiN,WAAW,CAAC,CAAC,CAC1B,CAAE,CAAA3L,QAAA,CAAC,cAEH,EARY,QAQJ,CAAC,CACT,CACF2H,KAAK,CAAE,GAAI,CAAA3H,QAAA,cAEXhE,KAAA,CAAC5C,IAAI,EAACoR,IAAI,CAAE9L,UAAW,CAAC+L,MAAM,CAAC,UAAU,CAAAzK,QAAA,eACvClE,IAAA,CAAClC,KAAK,EACJV,OAAO,CAAC,0BAAM,CACd0K,WAAW,cACT5H,KAAA,QAAAgE,QAAA,eACElE,IAAA,MAAAkE,QAAA,CAAG,iHAAqB,CAAG,CAAC,cAC5BlE,IAAA,MAAAkE,QAAA,CAAG,wDAAc,CAAG,CAAC,cACrBlE,IAAA,MAAAkE,QAAA,CAAG,oEAAgB,CAAG,CAAC,cACvBlE,IAAA,MAAAkE,QAAA,CAAG,6EAAe,CAAG,CAAC,EACnB,CACN,CACDM,IAAI,CAAC,MAAM,CACXH,KAAK,CAAE,CAAEyI,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEF9M,IAAA,CAAC1C,IAAI,CAACsR,IAAI,EAACvD,KAAK,CAAC,0BAAM,CAAAnH,QAAA,cACrBhE,KAAA,CAACtC,MAAM,CAACkS,OAAO,EACbC,MAAM,CAAC,iBAAiB,CACxBC,YAAY,CAAE3H,gBAAiB,CAC/B4H,cAAc,CAAE,KAAM,CACtBvE,QAAQ,CAAErJ,YAAY,GAAK,WAAW,EAAIA,YAAY,GAAK,YAAa,CAAA6B,QAAA,eAExElE,IAAA,MAAGkQ,SAAS,CAAC,sBAAsB,CAAAhM,QAAA,cACjClE,IAAA,CAAChB,iBAAiB,EAACqF,KAAK,CAAE,CAAE8K,QAAQ,CAAE,EAAE,CAAE7K,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC/D,CAAC,cACJtE,IAAA,MAAGkQ,SAAS,CAAC,iBAAiB,CAAAhM,QAAA,CAAC,gFAAa,CAAG,CAAC,cAChDlE,IAAA,MAAGkQ,SAAS,CAAC,iBAAiB,CAAAhM,QAAA,CAAC,oDAAe,CAAG,CAAC,EACpC,CAAC,CACR,CAAC,CAEX7B,YAAY,eACXnC,KAAA,QAAKmE,KAAK,CAAE,CAAEE,SAAS,CAAE,EAAG,CAAE,CAAAL,QAAA,EAC3B7B,YAAY,GAAK,WAAW,eAC3BnC,KAAA,QAAAgE,QAAA,eACElE,IAAA,QAAKqE,KAAK,CAAE,CAAEyI,YAAY,CAAE,CAAE,CAAE,CAAA5I,QAAA,CAAC,yCAAS,CAAK,CAAC,cAChDlE,IAAA,CAACnC,QAAQ,EAACsS,OAAO,CAAEhO,cAAe,CAAC+C,MAAM,CAAC,QAAQ,CAAE,CAAC,EAClD,CACN,CAEA7C,YAAY,GAAK,YAAY,eAC5BnC,KAAA,QAAAgE,QAAA,eACElE,IAAA,QAAKqE,KAAK,CAAE,CAAEyI,YAAY,CAAE,CAAE,CAAE,CAAA5I,QAAA,CAAC,yCAAS,CAAK,CAAC,cAChDlE,IAAA,CAACnC,QAAQ,EAACsS,OAAO,CAAE,GAAI,CAACjL,MAAM,CAAC,QAAQ,CAAE,CAAC,EACvC,CACN,CAEA7C,YAAY,GAAK,SAAS,EAAIE,YAAY,eACzCvC,IAAA,CAAClC,KAAK,EACJV,OAAO,CAAC,0BAAM,CACd0K,WAAW,6BAAAzC,MAAA,CAAU9C,YAAY,CAACoC,OAAO,uBAAO,CAChDH,IAAI,CAAC,SAAS,CACdgH,IAAI,cAAExL,IAAA,CAACf,mBAAmB,GAAE,CAAE,CAC/B,CACF,CAEAoD,YAAY,GAAK,OAAO,EAAIE,YAAY,eACvCrC,KAAA,QAAAgE,QAAA,eACElE,IAAA,CAAClC,KAAK,EACJV,OAAO,CAAC,0EAAc,CACtB0K,WAAW,sBAAAzC,MAAA,CAAQ9C,YAAY,CAACoC,OAAO,oCAAAU,MAAA,CAAS9C,YAAY,CAACgH,MAAM,WAAK,CACxE/E,IAAI,CAAC,SAAS,CACdgH,IAAI,cAAExL,IAAA,CAACd,yBAAyB,GAAE,CAAE,CACpCmF,KAAK,CAAE,CAAEyI,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,CAEDvK,YAAY,CAACyH,aAAa,EAAIzH,YAAY,CAACyH,aAAa,CAAC5C,MAAM,CAAG,CAAC,eAClElH,KAAA,QAAAgE,QAAA,eACElE,IAAA,QAAKqE,KAAK,CAAE,CAAEkL,UAAU,CAAE,MAAM,CAAEzC,YAAY,CAAE,CAAE,CAAE,CAAA5I,QAAA,CAAC,gCAAK,CAAK,CAAC,cAChElE,IAAA,QAAKqE,KAAK,CAAE,CAAE+L,SAAS,CAAE,GAAG,CAAEC,QAAQ,CAAE,MAAM,CAAE3C,eAAe,CAAE,SAAS,CAAED,OAAO,CAAE,CAAC,CAAEE,YAAY,CAAE,CAAE,CAAE,CAAAzJ,QAAA,CACvG3B,YAAY,CAACyH,aAAa,CAAC3D,GAAG,CAAC,CAACzB,KAAa,CAAE+E,KAAa,gBAC3D3J,IAAA,QAAiBqE,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEwI,YAAY,CAAE,CAAE,CAAE,CAAA5I,QAAA,CAC3DU,KAAK,EADE+E,KAEL,CACN,CAAC,CACC,CAAC,EACH,CACN,EACE,CACN,EACE,CACN,EACG,CAAC,CACF,CAAC,EACP,CAAC,CAEV,CAAC,CAED,2BAAenN,KAAK,CAAC8T,IAAI,CAACjQ,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}