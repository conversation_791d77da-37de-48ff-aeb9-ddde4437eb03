import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Form,
  Input,
  Select,
  Button,
  Space,
  Row,
  Col,
  Table,
  Modal,
  InputNumber,
  message,
  Breadcrumb,
  Tabs,
  Tag,
  Tooltip,
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  FileTextOutlined,
  HistoryOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchCoreBOM, updateCoreBOM } from '../../store/slices/bomSlice';
import { formatDate } from '../../utils';
import { ConfirmDialog } from '../../components';
import { errorHandler, ErrorType } from '../../utils/errorHandler';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

const CoreBOMEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentBOM, loading } = useAppSelector(state => state.bom);

  const [form] = Form.useForm();
  const [materialModalVisible, setMaterialModalVisible] = useState(false);
  const [editingMaterial, setEditingMaterial] = useState<any>(null);
  const [materialForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (id) {
      dispatch(fetchCoreBOM(id));
    }
  }, [id, dispatch]);

  useEffect(() => {
    if (currentBOM) {
      form.setFieldsValue({
        bomCode: currentBOM.code,
        bomName: currentBOM.name,
        version: currentBOM.version,
        productLine: 'productLine' in currentBOM ? currentBOM.productLine : '5G基站',
        description: currentBOM.description,
        status: currentBOM.status,
      });
    }
  }, [currentBOM, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      await dispatch(updateCoreBOM({ id: id!, data: values }));
      message.success('BOM保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    }
  };

  const handleAddMaterial = () => {
    setEditingMaterial(null);
    materialForm.resetFields();
    setMaterialModalVisible(true);
  };

  const handleEditMaterial = (record: any) => {
    setEditingMaterial(record);
    materialForm.setFieldsValue(record);
    setMaterialModalVisible(true);
  };

  const handleMaterialModalOk = async () => {
    try {
      const values = await materialForm.validateFields();

      if (editingMaterial) {
        message.success('物料更新成功');
      } else {
        message.success('物料添加成功');
      }

      setMaterialModalVisible(false);
      // TODO: 更新物料列表
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 模拟BOM物料数据
  const mockMaterials = [
    {
      id: '1',
      materialCode: 'ANT-MAIN-001',
      materialName: '5G主天线单元',
      specification: 'ANT-5G-001',
      quantity: 1,
      unit: 'PCS',
      unitPrice: 1500,
      totalPrice: 1500,
      supplier: '华为技术',
      leadTime: 14,
      level: 1,
      parentId: null,
    },
    {
      id: '2',
      materialCode: 'RF-AMP-001',
      materialName: 'RF功率放大器',
      specification: 'RF-PA-5G-001',
      quantity: 2,
      unit: 'PCS',
      unitPrice: 2500,
      totalPrice: 5000,
      supplier: '中兴通讯',
      leadTime: 21,
      level: 2,
      parentId: '1',
    },
  ];

  const materialColumns = [
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      width: 60,
      render: (level: number) => <Tag color="blue">L{level}</Tag>,
    },
    {
      title: '物料编码',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 120,
    },
    {
      title: '物料名称',
      dataIndex: 'materialName',
      key: 'materialName',
      ellipsis: true,
    },
    {
      title: '规格型号',
      dataIndex: 'specification',
      key: 'specification',
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      render: (quantity: number, record: any) => `${quantity} ${record.unit}`,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (price: number) => `¥${price.toLocaleString()}`,
    },
    {
      title: '总价',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 100,
      render: (price: number) => `¥${price.toLocaleString()}`,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 120,
    },
    {
      title: '交期',
      dataIndex: 'leadTime',
      key: 'leadTime',
      width: 80,
      render: (days: number) => `${days}天`,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: any) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditMaterial(record)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => {
                message.success('物料已复制');
              }}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: (
                    <div>
                      <p>确定要删除这个物料吗？</p>
                      <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复！</p>
                    </div>
                  ),
                  type: 'warning',
                  onOk: () => {
                    message.success('物料已删除');
                  }
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Title level={4}>加载中...</Title>
      </div>
    );
  }

  return (
    <div>
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>BOM管理</Breadcrumb.Item>
        <Breadcrumb.Item>核心BOM</Breadcrumb.Item>
        <Breadcrumb.Item>编辑BOM</Breadcrumb.Item>
      </Breadcrumb>

      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate(-1)}
              >
                返回
              </Button>
              <Title level={4} style={{ margin: 0 }}>
                编辑核心BOM
              </Title>
              {currentBOM && (
                <Tag color="blue">{currentBOM.code}</Tag>
              )}
            </Space>
          </Col>
          <Col>
            <Space>
              <Button icon={<HistoryOutlined />}>
                版本历史
              </Button>
              <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
                保存
              </Button>
            </Space>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<span><FileTextOutlined />基本信息</span>} key="basic">
            <Form form={form} layout="vertical">
              <Row gutter={[16, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item
                    name="bomCode"
                    label="BOM编码"
                    rules={[{ required: true, message: '请输入BOM编码' }]}
                  >
                    <Input placeholder="请输入BOM编码" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name="bomName"
                    label="BOM名称"
                    rules={[{ required: true, message: '请输入BOM名称' }]}
                  >
                    <Input placeholder="请输入BOM名称" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    name="version"
                    label="版本"
                    rules={[{ required: true, message: '请输入版本' }]}
                  >
                    <Input placeholder="请输入版本" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="productLine"
                    label="产品线"
                    rules={[{ required: true, message: '请选择产品线' }]}
                  >
                    <Select placeholder="请选择产品线">
                      <Select.Option value="5G基站">5G基站</Select.Option>
                      <Select.Option value="4G基站">4G基站</Select.Option>
                      <Select.Option value="传输设备">传输设备</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="status"
                    label="状态"
                    rules={[{ required: true, message: '请选择状态' }]}
                  >
                    <Select placeholder="请选择状态">
                      <Select.Option value="draft">草稿</Select.Option>
                      <Select.Option value="active">生效</Select.Option>
                      <Select.Option value="obsolete">废弃</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item name="description" label="描述">
                    <TextArea rows={4} placeholder="请输入BOM描述" />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </TabPane>

          <TabPane tab="物料清单" key="materials">
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Text strong>物料清单</Text>
              </Col>
              <Col>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddMaterial}
                >
                  添加物料
                </Button>
              </Col>
            </Row>

            <Table
              columns={materialColumns}
              dataSource={mockMaterials}
              rowKey="id"
              scroll={{ x: 1000 }}
              pagination={false}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 物料编辑模态框 */}
      <Modal
        title={editingMaterial ? '编辑物料' : '添加物料'}
        open={materialModalVisible}
        onOk={handleMaterialModalOk}
        onCancel={() => setMaterialModalVisible(false)}
        width={600}
      >
        <Form form={materialForm} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="materialCode"
                label="物料编码"
                rules={[{ required: true, message: '请输入物料编码' }]}
              >
                <Input placeholder="请输入物料编码" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="materialName"
                label="物料名称"
                rules={[{ required: true, message: '请输入物料名称' }]}
              >
                <Input placeholder="请输入物料名称" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="specification"
                label="规格型号"
              >
                <Input placeholder="请输入规格型号" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="quantity"
                label="数量"
                rules={[{ required: true, message: '请输入数量' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} placeholder="请输入数量" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Select.Option value="PCS">PCS</Select.Option>
                  <Select.Option value="KG">KG</Select.Option>
                  <Select.Option value="M">M</Select.Option>
                  <Select.Option value="SET">SET</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="unitPrice"
                label="单价"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入单价"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default CoreBOMEditPage;
