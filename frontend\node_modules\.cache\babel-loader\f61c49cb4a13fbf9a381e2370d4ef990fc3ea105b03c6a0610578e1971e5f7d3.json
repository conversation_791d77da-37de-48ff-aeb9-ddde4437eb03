{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\cost\\\\StandardCostPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, DatePicker, Modal, Form, InputNumber, Row, Col, Statistic, Progress, Alert, Tag, Typography, Tabs, Tooltip, message } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, ExportOutlined, WarningOutlined, CheckCircleOutlined, CloseCircleOutlined, SyncOutlined, DollarOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { formatCurrency } from '../../utils/format';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Search\n} = Input;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst StandardCostPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [searchText, setSearchText] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [dateRange, setDateRange] = useState(null);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingRecord, setEditingRecord] = useState(null);\n  const [activeTab, setActiveTab] = useState('standards');\n  const [form] = Form.useForm();\n\n  // 模拟标准成本数据\n  const mockStandardCosts = [{\n    id: '1',\n    materialCode: 'ANT-MAIN-001',\n    materialName: '5G主天线单元',\n    specification: '3.5GHz 64T64R',\n    unit: 'PCS',\n    standardCost: 12500,\n    actualCost: 13200,\n    variance: 700,\n    variancePercentage: 5.6,\n    lastUpdated: '2024-03-15T00:00:00Z',\n    status: 'warning',\n    category: '天线组件',\n    supplier: '华为技术',\n    effectiveDate: '2024-01-01',\n    expiryDate: '2024-12-31'\n  }, {\n    id: '2',\n    materialCode: 'RRU-001',\n    materialName: '射频拉远单元',\n    specification: '200W 3.5GHz',\n    unit: 'PCS',\n    standardCost: 8500,\n    actualCost: 8200,\n    variance: -300,\n    variancePercentage: -3.5,\n    lastUpdated: '2024-03-14T00:00:00Z',\n    status: 'normal',\n    category: '射频器件',\n    supplier: '中兴通讯',\n    effectiveDate: '2024-01-01',\n    expiryDate: '2024-12-31'\n  }, {\n    id: '3',\n    materialCode: 'CABLE-001',\n    materialName: '同轴电缆',\n    specification: '7/8\" 50Ω',\n    unit: 'M',\n    standardCost: 45,\n    actualCost: 52,\n    variance: 7,\n    variancePercentage: 15.6,\n    lastUpdated: '2024-03-13T00:00:00Z',\n    status: 'alert',\n    category: '连接器件',\n    supplier: '安费诺',\n    effectiveDate: '2024-01-01',\n    expiryDate: '2024-12-31'\n  }];\n\n  // 模拟成本差异数据\n  const mockVariances = [{\n    id: '1',\n    orderNumber: 'ORD-2024-001',\n    materialCode: 'ANT-MAIN-001',\n    materialName: '5G主天线单元',\n    standardCost: 12500,\n    actualCost: 13200,\n    variance: 700,\n    variancePercentage: 5.6,\n    quantity: 10,\n    totalVariance: 7000,\n    reason: '供应商价格上涨',\n    responsiblePerson: '采购经理',\n    status: 'investigating',\n    createdAt: '2024-03-15T00:00:00Z'\n  }, {\n    id: '2',\n    orderNumber: 'ORD-2024-002',\n    materialCode: 'CABLE-001',\n    materialName: '同轴电缆',\n    standardCost: 45,\n    actualCost: 52,\n    variance: 7,\n    variancePercentage: 15.6,\n    quantity: 500,\n    totalVariance: 3500,\n    reason: '汇率波动影响',\n    responsiblePerson: '财务经理',\n    status: 'pending',\n    createdAt: '2024-03-13T00:00:00Z'\n  }];\n\n  // 模拟概览数据\n  const mockOverview = {\n    totalMaterials: 156,\n    normalCount: 120,\n    warningCount: 25,\n    alertCount: 11,\n    avgVariance: 3.2,\n    totalVarianceAmount: 45600,\n    lastUpdateTime: '2024-03-15T10:30:00Z'\n  };\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    setLoading(true);\n    // 模拟API调用\n    setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n  };\n  const handleAdd = () => {\n    setEditingRecord(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n  const handleEdit = record => {\n    setEditingRecord(record);\n    form.setFieldsValue({\n      ...record,\n      effectiveDate: dayjs(record.effectiveDate),\n      expiryDate: dayjs(record.expiryDate)\n    });\n    setIsModalVisible(true);\n  };\n  const handleDelete = id => {\n    Modal.confirm({\n      title: '确认删除',\n      content: '确定要删除这条标准成本记录吗？',\n      onOk: () => {\n        message.success('删除成功');\n      }\n    });\n  };\n  const handleSubmit = async values => {\n    try {\n      setLoading(true);\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      message.success(editingRecord ? '更新成功' : '创建成功');\n      setIsModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('操作失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRecalculate = () => {\n    Modal.confirm({\n      title: '重新计算标准成本',\n      content: '这将基于最新的采购价格和市场数据重新计算所有标准成本，确定继续吗？',\n      onOk: () => {\n        message.success('重新计算完成');\n        loadData();\n      }\n    });\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'normal':\n        return 'green';\n      case 'warning':\n        return 'orange';\n      case 'alert':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'normal':\n        return '正常';\n      case 'warning':\n        return '预警';\n      case 'alert':\n        return '异常';\n      default:\n        return '未知';\n    }\n  };\n  const getVarianceIcon = variance => {\n    if (variance > 0) {\n      return /*#__PURE__*/_jsxDEV(TrendingUpOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 14\n      }, this);\n    } else if (variance < 0) {\n      return /*#__PURE__*/_jsxDEV(TrendingDownOutlined, {\n        style: {\n          color: '#52c41a'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n      style: {\n        color: '#1890ff'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 12\n    }, this);\n  };\n  const standardCostColumns = [{\n    title: '物料编码',\n    dataIndex: 'materialCode',\n    key: 'materialCode',\n    width: 120,\n    fixed: 'left'\n  }, {\n    title: '物料名称',\n    dataIndex: 'materialName',\n    key: 'materialName',\n    ellipsis: true\n  }, {\n    title: '规格型号',\n    dataIndex: 'specification',\n    key: 'specification',\n    ellipsis: true\n  }, {\n    title: '单位',\n    dataIndex: 'unit',\n    key: 'unit',\n    width: 60\n  }, {\n    title: '标准成本',\n    dataIndex: 'standardCost',\n    key: 'standardCost',\n    width: 100,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '实际成本',\n    dataIndex: 'actualCost',\n    key: 'actualCost',\n    width: 100,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '差异',\n    dataIndex: 'variance',\n    key: 'variance',\n    width: 120,\n    render: (variance, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        children: [getVarianceIcon(variance), /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            color: variance > 0 ? '#ff4d4f' : variance < 0 ? '#52c41a' : '#1890ff'\n          },\n          children: formatCurrency(Math.abs(variance))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [record.variancePercentage > 0 ? '+' : '', record.variancePercentage.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '供应商',\n    dataIndex: 'supplier',\n    key: 'supplier',\n    width: 100\n  }, {\n    title: '更新时间',\n    dataIndex: 'lastUpdated',\n    key: 'lastUpdated',\n    width: 120,\n    render: date => dayjs(date).format('YYYY-MM-DD')\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 120,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 9\n    }, this)\n  }];\n  const varianceColumns = [{\n    title: '订单号',\n    dataIndex: 'orderNumber',\n    key: 'orderNumber',\n    width: 120\n  }, {\n    title: '物料编码',\n    dataIndex: 'materialCode',\n    key: 'materialCode',\n    width: 120\n  }, {\n    title: '物料名称',\n    dataIndex: 'materialName',\n    key: 'materialName',\n    ellipsis: true\n  }, {\n    title: '标准成本',\n    dataIndex: 'standardCost',\n    key: 'standardCost',\n    width: 100,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '实际成本',\n    dataIndex: 'actualCost',\n    key: 'actualCost',\n    width: 100,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '单位差异',\n    dataIndex: 'variance',\n    key: 'variance',\n    width: 100,\n    render: (variance, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: variance > 0 ? '#ff4d4f' : '#52c41a'\n        },\n        children: formatCurrency(Math.abs(variance))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [record.variancePercentage > 0 ? '+' : '', record.variancePercentage.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    key: 'quantity',\n    width: 80\n  }, {\n    title: '总差异',\n    dataIndex: 'totalVariance',\n    key: 'totalVariance',\n    width: 100,\n    render: variance => /*#__PURE__*/_jsxDEV(Text, {\n      style: {\n        color: variance > 0 ? '#ff4d4f' : '#52c41a'\n      },\n      children: formatCurrency(Math.abs(variance))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '差异原因',\n    dataIndex: 'reason',\n    key: 'reason',\n    ellipsis: true\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => {\n      const statusMap = {\n        pending: {\n          color: 'orange',\n          text: '待处理'\n        },\n        investigating: {\n          color: 'blue',\n          text: '调查中'\n        },\n        resolved: {\n          color: 'green',\n          text: '已解决'\n        }\n      };\n      const config = statusMap[status];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: config.color,\n        children: config.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 16\n      }, this);\n    }\n  }];\n  const renderOverviewTab = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [mockOverview.alertCount > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u6210\\u672C\\u5F02\\u5E38\\u9884\\u8B66\",\n      description: `发现 ${mockOverview.alertCount} 个物料成本差异超过预警阈值，建议及时处理。`,\n      type: \"error\",\n      showIcon: true,\n      closable: true,\n      style: {\n        marginBottom: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7269\\u6599\\u603B\\u6570\",\n            value: mockOverview.totalMaterials,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6B63\\u5E38\\u7269\\u6599\",\n            value: mockOverview.normalCount,\n            valueStyle: {\n              color: '#52c41a'\n            },\n            suffix: /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: `占比: ${(mockOverview.normalCount / mockOverview.totalMaterials * 100).toFixed(1)}%`,\n              children: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                style: {\n                  color: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: mockOverview.normalCount / mockOverview.totalMaterials * 100,\n            size: \"small\",\n            showInfo: false,\n            strokeColor: \"#52c41a\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9884\\u8B66\\u7269\\u6599\",\n            value: mockOverview.warningCount,\n            valueStyle: {\n              color: '#faad14'\n            },\n            suffix: /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: `占比: ${(mockOverview.warningCount / mockOverview.totalMaterials * 100).toFixed(1)}%`,\n              children: /*#__PURE__*/_jsxDEV(WarningOutlined, {\n                style: {\n                  color: '#faad14'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: mockOverview.warningCount / mockOverview.totalMaterials * 100,\n            size: \"small\",\n            showInfo: false,\n            strokeColor: \"#faad14\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F02\\u5E38\\u7269\\u6599\",\n            value: mockOverview.alertCount,\n            valueStyle: {\n              color: '#ff4d4f'\n            },\n            suffix: /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: `占比: ${(mockOverview.alertCount / mockOverview.totalMaterials * 100).toFixed(1)}%`,\n              children: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n                style: {\n                  color: '#ff4d4f'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: mockOverview.alertCount / mockOverview.totalMaterials * 100,\n            size: \"small\",\n            showInfo: false,\n            strokeColor: \"#ff4d4f\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6807\\u51C6\\u6210\\u672C\\u7BA1\\u7406\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 25\n          }, this),\n          onClick: handleRecalculate,\n          children: \"\\u91CD\\u65B0\\u8BA1\\u7B97\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 40\n          }, this),\n          onClick: handleAdd,\n          children: \"\\u65B0\\u589E\\u6807\\u51C6\\u6210\\u672C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 9\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: standardCostColumns,\n        dataSource: mockStandardCosts,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          total: mockStandardCosts.length,\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 488,\n    columnNumber: 5\n  }, this);\n  const renderVarianceTab = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6210\\u672C\\u5DEE\\u5F02\\u5206\\u6790\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FA\\u5DEE\\u5F02\\u62A5\\u544A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 9\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: varianceColumns,\n        dataSource: mockVariances,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          total: mockVariances.length,\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 605,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u6807\\u51C6\\u6210\\u672C\\u7BA1\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              placeholder: \"\\u641C\\u7D22\\u7269\\u6599\\u7F16\\u7801\\u6216\\u540D\\u79F0\",\n              allowClear: true,\n              style: {\n                width: 200\n              },\n              value: searchText,\n              onChange: e => setSearchText(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u7269\\u6599\\u7C7B\\u522B\",\n              allowClear: true,\n              style: {\n                width: 120\n              },\n              value: selectedCategory,\n              onChange: setSelectedCategory,\n              options: [{\n                label: '天线组件',\n                value: '天线组件'\n              }, {\n                label: '射频器件',\n                value: '射频器件'\n              }, {\n                label: '连接器件',\n                value: '连接器件'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              value: dateRange,\n              onChange: dates => setDateRange(dates),\n              style: {\n                width: 240\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u6807\\u51C6\\u6210\\u672C\",\n          children: renderOverviewTab()\n        }, \"standards\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u6210\\u672C\\u5DEE\\u5F02\",\n          children: renderVarianceTab()\n        }, \"variances\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 672,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingRecord ? '编辑标准成本' : '新增标准成本',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"materialCode\",\n              label: \"\\u7269\\u6599\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入物料编码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u7F16\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"materialName\",\n              label: \"\\u7269\\u6599\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入物料名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"specification\",\n              label: \"\\u89C4\\u683C\\u578B\\u53F7\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89C4\\u683C\\u578B\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"unit\",\n              label: \"\\u5355\\u4F4D\",\n              rules: [{\n                required: true,\n                message: '请选择单位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5355\\u4F4D\",\n                options: [{\n                  label: 'PCS',\n                  value: 'PCS'\n                }, {\n                  label: 'M',\n                  value: 'M'\n                }, {\n                  label: 'KG',\n                  value: 'KG'\n                }, {\n                  label: 'SET',\n                  value: 'SET'\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"standardCost\",\n              label: \"\\u6807\\u51C6\\u6210\\u672C\",\n              rules: [{\n                required: true,\n                message: '请输入标准成本'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u6807\\u51C6\\u6210\\u672C\",\n                style: {\n                  width: '100%'\n                },\n                min: 0,\n                precision: 2,\n                formatter: value => `¥ ${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ','),\n                parser: value => value.replace(/¥\\s?|(,*)/g, '')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"category\",\n              label: \"\\u7269\\u6599\\u7C7B\\u522B\",\n              rules: [{\n                required: true,\n                message: '请选择物料类别'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u7269\\u6599\\u7C7B\\u522B\",\n                options: [{\n                  label: '天线组件',\n                  value: '天线组件'\n                }, {\n                  label: '射频器件',\n                  value: '射频器件'\n                }, {\n                  label: '连接器件',\n                  value: '连接器件'\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"supplier\",\n              label: \"\\u4E3B\\u8981\\u4F9B\\u5E94\\u5546\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4E3B\\u8981\\u4F9B\\u5E94\\u5546\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"effectiveDate\",\n              label: \"\\u751F\\u6548\\u65E5\\u671F\",\n              rules: [{\n                required: true,\n                message: '请选择生效日期'\n              }],\n              children: /*#__PURE__*/_jsxDEV(DatePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"expiryDate\",\n          label: \"\\u5931\\u6548\\u65E5\\u671F\",\n          children: /*#__PURE__*/_jsxDEV(DatePicker, {\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0,\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              children: editingRecord ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 631,\n    columnNumber: 5\n  }, this);\n};\n_s(StandardCostPage, \"aOgZchsD5HDnMkhYcLjL+Zbzel0=\", false, function () {\n  return [Form.useForm];\n});\n_c = StandardCostPage;\nexport default StandardCostPage;\nvar _c;\n$RefreshReg$(_c, \"StandardCostPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "DatePicker", "Modal", "Form", "InputNumber", "Row", "Col", "Statistic", "Progress", "<PERSON><PERSON>", "Tag", "Typography", "Tabs", "<PERSON><PERSON><PERSON>", "message", "PlusOutlined", "EditOutlined", "DeleteOutlined", "ExportOutlined", "WarningOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "SyncOutlined", "DollarOutlined", "dayjs", "formatCurrency", "jsxDEV", "_jsxDEV", "Search", "RangePicker", "Title", "Text", "TabPane", "StandardCostPage", "_s", "loading", "setLoading", "searchText", "setSearchText", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "date<PERSON><PERSON><PERSON>", "setDateRange", "isModalVisible", "setIsModalVisible", "editingRecord", "setEditingRecord", "activeTab", "setActiveTab", "form", "useForm", "mockStandardCosts", "id", "materialCode", "materialName", "specification", "unit", "standardCost", "actualCost", "variance", "variancePercentage", "lastUpdated", "status", "category", "supplier", "effectiveDate", "expiryDate", "mockVariances", "orderNumber", "quantity", "totalVariance", "reason", "<PERSON><PERSON><PERSON>", "createdAt", "mockOverview", "totalMaterials", "normalCount", "warningCount", "alertCount", "avg<PERSON><PERSON><PERSON>", "totalVarianceAmount", "lastUpdateTime", "loadData", "setTimeout", "handleAdd", "resetFields", "handleEdit", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "confirm", "title", "content", "onOk", "success", "handleSubmit", "values", "Promise", "resolve", "error", "handleRecalculate", "getStatusColor", "getStatusText", "getVarianceIcon", "TrendingUpOutlined", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "TrendingDownOutlined", "standardCostColumns", "dataIndex", "key", "width", "fixed", "ellipsis", "render", "cost", "direction", "size", "children", "Math", "abs", "type", "fontSize", "toFixed", "date", "format", "_", "icon", "onClick", "danger", "varianceColumns", "statusMap", "pending", "text", "investigating", "resolved", "config", "renderOverviewTab", "description", "showIcon", "closable", "marginBottom", "gutter", "xs", "sm", "value", "prefix", "valueStyle", "suffix", "percent", "showInfo", "strokeColor", "extra", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "total", "length", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "renderVarianceTab", "justify", "align", "level", "margin", "placeholder", "allowClear", "onChange", "e", "target", "options", "label", "dates", "active<PERSON><PERSON>", "tab", "open", "onCancel", "footer", "layout", "onFinish", "span", "<PERSON><PERSON>", "name", "rules", "required", "min", "precision", "formatter", "replace", "parser", "textAlign", "htmlType", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/cost/StandardCostPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  DatePicker,\n  Modal,\n  Form,\n  InputNumber,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Alert,\n  Tag,\n  Typography,\n  Tabs,\n  Tooltip,\n  message,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExportOutlined,\n  CalculatorOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  SyncOutlined,\n  DollarOutlined,\n  ArrowUpOutlined,\n  ArrowDownOutlined,\n} from '@ant-design/icons';\nimport { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\nimport { formatCurrency } from '../../utils/format';\n\nconst { Search } = Input;\nconst { RangePicker } = DatePicker;\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\n\ninterface StandardCost {\n  id: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  unit: string;\n  standardCost: number;\n  actualCost: number;\n  variance: number;\n  variancePercentage: number;\n  lastUpdated: string;\n  status: 'normal' | 'warning' | 'alert';\n  category: string;\n  supplier: string;\n  effectiveDate: string;\n  expiryDate: string;\n}\n\ninterface CostVariance {\n  id: string;\n  orderNumber: string;\n  materialCode: string;\n  materialName: string;\n  standardCost: number;\n  actualCost: number;\n  variance: number;\n  variancePercentage: number;\n  quantity: number;\n  totalVariance: number;\n  reason: string;\n  responsiblePerson: string;\n  status: 'pending' | 'investigating' | 'resolved';\n  createdAt: string;\n}\n\nconst StandardCostPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [searchText, setSearchText] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingRecord, setEditingRecord] = useState<StandardCost | null>(null);\n  const [activeTab, setActiveTab] = useState('standards');\n  const [form] = Form.useForm();\n\n  // 模拟标准成本数据\n  const mockStandardCosts: StandardCost[] = [\n    {\n      id: '1',\n      materialCode: 'ANT-MAIN-001',\n      materialName: '5G主天线单元',\n      specification: '3.5GHz 64T64R',\n      unit: 'PCS',\n      standardCost: 12500,\n      actualCost: 13200,\n      variance: 700,\n      variancePercentage: 5.6,\n      lastUpdated: '2024-03-15T00:00:00Z',\n      status: 'warning',\n      category: '天线组件',\n      supplier: '华为技术',\n      effectiveDate: '2024-01-01',\n      expiryDate: '2024-12-31',\n    },\n    {\n      id: '2',\n      materialCode: 'RRU-001',\n      materialName: '射频拉远单元',\n      specification: '200W 3.5GHz',\n      unit: 'PCS',\n      standardCost: 8500,\n      actualCost: 8200,\n      variance: -300,\n      variancePercentage: -3.5,\n      lastUpdated: '2024-03-14T00:00:00Z',\n      status: 'normal',\n      category: '射频器件',\n      supplier: '中兴通讯',\n      effectiveDate: '2024-01-01',\n      expiryDate: '2024-12-31',\n    },\n    {\n      id: '3',\n      materialCode: 'CABLE-001',\n      materialName: '同轴电缆',\n      specification: '7/8\" 50Ω',\n      unit: 'M',\n      standardCost: 45,\n      actualCost: 52,\n      variance: 7,\n      variancePercentage: 15.6,\n      lastUpdated: '2024-03-13T00:00:00Z',\n      status: 'alert',\n      category: '连接器件',\n      supplier: '安费诺',\n      effectiveDate: '2024-01-01',\n      expiryDate: '2024-12-31',\n    },\n  ];\n\n  // 模拟成本差异数据\n  const mockVariances: CostVariance[] = [\n    {\n      id: '1',\n      orderNumber: 'ORD-2024-001',\n      materialCode: 'ANT-MAIN-001',\n      materialName: '5G主天线单元',\n      standardCost: 12500,\n      actualCost: 13200,\n      variance: 700,\n      variancePercentage: 5.6,\n      quantity: 10,\n      totalVariance: 7000,\n      reason: '供应商价格上涨',\n      responsiblePerson: '采购经理',\n      status: 'investigating',\n      createdAt: '2024-03-15T00:00:00Z',\n    },\n    {\n      id: '2',\n      orderNumber: 'ORD-2024-002',\n      materialCode: 'CABLE-001',\n      materialName: '同轴电缆',\n      standardCost: 45,\n      actualCost: 52,\n      variance: 7,\n      variancePercentage: 15.6,\n      quantity: 500,\n      totalVariance: 3500,\n      reason: '汇率波动影响',\n      responsiblePerson: '财务经理',\n      status: 'pending',\n      createdAt: '2024-03-13T00:00:00Z',\n    },\n  ];\n\n  // 模拟概览数据\n  const mockOverview = {\n    totalMaterials: 156,\n    normalCount: 120,\n    warningCount: 25,\n    alertCount: 11,\n    avgVariance: 3.2,\n    totalVarianceAmount: 45600,\n    lastUpdateTime: '2024-03-15T10:30:00Z',\n  };\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    // 模拟API调用\n    setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n  };\n\n  const handleAdd = () => {\n    setEditingRecord(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEdit = (record: StandardCost) => {\n    setEditingRecord(record);\n    form.setFieldsValue({\n      ...record,\n      effectiveDate: dayjs(record.effectiveDate),\n      expiryDate: dayjs(record.expiryDate),\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDelete = (id: string) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: '确定要删除这条标准成本记录吗？',\n      onOk: () => {\n        message.success('删除成功');\n      },\n    });\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      setLoading(true);\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      message.success(editingRecord ? '更新成功' : '创建成功');\n      setIsModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('操作失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRecalculate = () => {\n    Modal.confirm({\n      title: '重新计算标准成本',\n      content: '这将基于最新的采购价格和市场数据重新计算所有标准成本，确定继续吗？',\n      onOk: () => {\n        message.success('重新计算完成');\n        loadData();\n      },\n    });\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'normal': return 'green';\n      case 'warning': return 'orange';\n      case 'alert': return 'red';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'normal': return '正常';\n      case 'warning': return '预警';\n      case 'alert': return '异常';\n      default: return '未知';\n    }\n  };\n\n  const getVarianceIcon = (variance: number) => {\n    if (variance > 0) {\n      return <TrendingUpOutlined style={{ color: '#ff4d4f' }} />;\n    } else if (variance < 0) {\n      return <TrendingDownOutlined style={{ color: '#52c41a' }} />;\n    }\n    return <CheckCircleOutlined style={{ color: '#1890ff' }} />;\n  };\n\n  const standardCostColumns: ColumnsType<StandardCost> = [\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n      fixed: 'left',\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      ellipsis: true,\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      key: 'specification',\n      ellipsis: true,\n    },\n    {\n      title: '单位',\n      dataIndex: 'unit',\n      key: 'unit',\n      width: 60,\n    },\n    {\n      title: '标准成本',\n      dataIndex: 'standardCost',\n      key: 'standardCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '实际成本',\n      dataIndex: 'actualCost',\n      key: 'actualCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '差异',\n      dataIndex: 'variance',\n      key: 'variance',\n      width: 120,\n      render: (variance: number, record: StandardCost) => (\n        <Space direction=\"vertical\" size={0}>\n          <Space>\n            {getVarianceIcon(variance)}\n            <Text style={{ color: variance > 0 ? '#ff4d4f' : variance < 0 ? '#52c41a' : '#1890ff' }}>\n              {formatCurrency(Math.abs(variance))}\n            </Text>\n          </Space>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.variancePercentage > 0 ? '+' : ''}{record.variancePercentage.toFixed(1)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 100,\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'lastUpdated',\n      key: 'lastUpdated',\n      width: 120,\n      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      fixed: 'right',\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n          >\n            删除\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  const varianceColumns: ColumnsType<CostVariance> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNumber',\n      key: 'orderNumber',\n      width: 120,\n    },\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      ellipsis: true,\n    },\n    {\n      title: '标准成本',\n      dataIndex: 'standardCost',\n      key: 'standardCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '实际成本',\n      dataIndex: 'actualCost',\n      key: 'actualCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '单位差异',\n      dataIndex: 'variance',\n      key: 'variance',\n      width: 100,\n      render: (variance: number, record: CostVariance) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ color: variance > 0 ? '#ff4d4f' : '#52c41a' }}>\n            {formatCurrency(Math.abs(variance))}\n          </Text>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.variancePercentage > 0 ? '+' : ''}{record.variancePercentage.toFixed(1)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n    },\n    {\n      title: '总差异',\n      dataIndex: 'totalVariance',\n      key: 'totalVariance',\n      width: 100,\n      render: (variance: number) => (\n        <Text style={{ color: variance > 0 ? '#ff4d4f' : '#52c41a' }}>\n          {formatCurrency(Math.abs(variance))}\n        </Text>\n      ),\n    },\n    {\n      title: '差异原因',\n      dataIndex: 'reason',\n      key: 'reason',\n      ellipsis: true,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => {\n        const statusMap = {\n          pending: { color: 'orange', text: '待处理' },\n          investigating: { color: 'blue', text: '调查中' },\n          resolved: { color: 'green', text: '已解决' },\n        };\n        const config = statusMap[status as keyof typeof statusMap];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n  ];\n\n  const renderOverviewTab = () => (\n    <div>\n      {/* 预警信息 */}\n      {mockOverview.alertCount > 0 && (\n        <Alert\n          message=\"成本异常预警\"\n          description={`发现 ${mockOverview.alertCount} 个物料成本差异超过预警阈值，建议及时处理。`}\n          type=\"error\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n      )}\n\n      {/* 概览统计 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"物料总数\"\n              value={mockOverview.totalMaterials}\n              prefix={<DollarOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"正常物料\"\n              value={mockOverview.normalCount}\n              valueStyle={{ color: '#52c41a' }}\n              suffix={\n                <Tooltip title={`占比: ${((mockOverview.normalCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>\n                  <CheckCircleOutlined style={{ color: '#52c41a' }} />\n                </Tooltip>\n              }\n            />\n            <Progress\n              percent={(mockOverview.normalCount / mockOverview.totalMaterials) * 100}\n              size=\"small\"\n              showInfo={false}\n              strokeColor=\"#52c41a\"\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"预警物料\"\n              value={mockOverview.warningCount}\n              valueStyle={{ color: '#faad14' }}\n              suffix={\n                <Tooltip title={`占比: ${((mockOverview.warningCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>\n                  <WarningOutlined style={{ color: '#faad14' }} />\n                </Tooltip>\n              }\n            />\n            <Progress\n              percent={(mockOverview.warningCount / mockOverview.totalMaterials) * 100}\n              size=\"small\"\n              showInfo={false}\n              strokeColor=\"#faad14\"\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"异常物料\"\n              value={mockOverview.alertCount}\n              valueStyle={{ color: '#ff4d4f' }}\n              suffix={\n                <Tooltip title={`占比: ${((mockOverview.alertCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>\n                  <CloseCircleOutlined style={{ color: '#ff4d4f' }} />\n                </Tooltip>\n              }\n            />\n            <Progress\n              percent={(mockOverview.alertCount / mockOverview.totalMaterials) * 100}\n              size=\"small\"\n              showInfo={false}\n              strokeColor=\"#ff4d4f\"\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 标准成本列表 */}\n      <Card title=\"标准成本管理\" extra={\n        <Space>\n          <Button icon={<SyncOutlined />} onClick={handleRecalculate}>\n            重新计算\n          </Button>\n          <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAdd}>\n            新增标准成本\n          </Button>\n        </Space>\n      }>\n        <Table\n          columns={standardCostColumns}\n          dataSource={mockStandardCosts}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            total: mockStandardCosts.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  const renderVarianceTab = () => (\n    <div>\n      <Card title=\"成本差异分析\" extra={\n        <Space>\n          <Button icon={<ExportOutlined />}>\n            导出差异报告\n          </Button>\n        </Space>\n      }>\n        <Table\n          columns={varianceColumns}\n          dataSource={mockVariances}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            total: mockVariances.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              标准成本管理\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索物料编码或名称\"\n                allowClear\n                style={{ width: 200 }}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n              />\n              <Select\n                placeholder=\"物料类别\"\n                allowClear\n                style={{ width: 120 }}\n                value={selectedCategory}\n                onChange={setSelectedCategory}\n                options={[\n                  { label: '天线组件', value: '天线组件' },\n                  { label: '射频器件', value: '射频器件' },\n                  { label: '连接器件', value: '连接器件' },\n                ]}\n              />\n              <RangePicker\n                value={dateRange}\n                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n                style={{ width: 240 }}\n              />\n              <Button icon={<ExportOutlined />}>\n                导出\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab=\"标准成本\" key=\"standards\">\n            {renderOverviewTab()}\n          </TabPane>\n          <TabPane tab=\"成本差异\" key=\"variances\">\n            {renderVarianceTab()}\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 新增/编辑模态框 */}\n      <Modal\n        title={editingRecord ? '编辑标准成本' : '新增标准成本'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"materialCode\"\n                label=\"物料编码\"\n                rules={[{ required: true, message: '请输入物料编码' }]}\n              >\n                <Input placeholder=\"请输入物料编码\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"materialName\"\n                label=\"物料名称\"\n                rules={[{ required: true, message: '请输入物料名称' }]}\n              >\n                <Input placeholder=\"请输入物料名称\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"specification\"\n                label=\"规格型号\"\n              >\n                <Input placeholder=\"请输入规格型号\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"unit\"\n                label=\"单位\"\n                rules={[{ required: true, message: '请选择单位' }]}\n              >\n                <Select\n                  placeholder=\"请选择单位\"\n                  options={[\n                    { label: 'PCS', value: 'PCS' },\n                    { label: 'M', value: 'M' },\n                    { label: 'KG', value: 'KG' },\n                    { label: 'SET', value: 'SET' },\n                  ]}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"standardCost\"\n                label=\"标准成本\"\n                rules={[{ required: true, message: '请输入标准成本' }]}\n              >\n                <InputNumber\n                  placeholder=\"请输入标准成本\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  precision={2}\n                  formatter={(value) => `¥ ${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n                  parser={(value) => value!.replace(/¥\\s?|(,*)/g, '')}\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"category\"\n                label=\"物料类别\"\n                rules={[{ required: true, message: '请选择物料类别' }]}\n              >\n                <Select\n                  placeholder=\"请选择物料类别\"\n                  options={[\n                    { label: '天线组件', value: '天线组件' },\n                    { label: '射频器件', value: '射频器件' },\n                    { label: '连接器件', value: '连接器件' },\n                  ]}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"supplier\"\n                label=\"主要供应商\"\n              >\n                <Input placeholder=\"请输入主要供应商\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"effectiveDate\"\n                label=\"生效日期\"\n                rules={[{ required: true, message: '请选择生效日期' }]}\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"expiryDate\"\n            label=\"失效日期\"\n          >\n            <DatePicker style={{ width: '100%' }} />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setIsModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n                {editingRecord ? '更新' : '创建'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default StandardCostPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,cAAc,EAEdC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,cAAc,QAGT,mBAAmB;AAE1B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAM;EAAEC;AAAO,CAAC,GAAG7B,KAAK;AACxB,MAAM;EAAE8B;AAAY,CAAC,GAAG5B,UAAU;AAClC,MAAM;EAAE6B,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAQ,CAAC,GAAGpB,IAAI;AAqCxB,MAAMqB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAoC,IAAI,CAAC;EACnF,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAsB,IAAI,CAAC;EAC7E,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACwD,IAAI,CAAC,GAAG9C,IAAI,CAAC+C,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,iBAAiC,GAAG,CACxC;IACEC,EAAE,EAAE,GAAG;IACPC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,eAAe;IAC9BC,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,GAAG;IACbC,kBAAkB,EAAE,GAAG;IACvBC,WAAW,EAAE,sBAAsB;IACnCC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEd,EAAE,EAAE,GAAG;IACPC,YAAY,EAAE,SAAS;IACvBC,YAAY,EAAE,QAAQ;IACtBC,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,CAAC,GAAG;IACdC,kBAAkB,EAAE,CAAC,GAAG;IACxBC,WAAW,EAAE,sBAAsB;IACnCC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEd,EAAE,EAAE,GAAG;IACPC,YAAY,EAAE,WAAW;IACzBC,YAAY,EAAE,MAAM;IACpBC,aAAa,EAAE,UAAU;IACzBC,IAAI,EAAE,GAAG;IACTC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,CAAC;IACXC,kBAAkB,EAAE,IAAI;IACxBC,WAAW,EAAE,sBAAsB;IACnCC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,YAAY;IAC3BC,UAAU,EAAE;EACd,CAAC,CACF;;EAED;EACA,MAAMC,aAA6B,GAAG,CACpC;IACEf,EAAE,EAAE,GAAG;IACPgB,WAAW,EAAE,cAAc;IAC3Bf,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,SAAS;IACvBG,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,GAAG;IACbC,kBAAkB,EAAE,GAAG;IACvBS,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,IAAI;IACnBC,MAAM,EAAE,SAAS;IACjBC,iBAAiB,EAAE,MAAM;IACzBV,MAAM,EAAE,eAAe;IACvBW,SAAS,EAAE;EACb,CAAC,EACD;IACErB,EAAE,EAAE,GAAG;IACPgB,WAAW,EAAE,cAAc;IAC3Bf,YAAY,EAAE,WAAW;IACzBC,YAAY,EAAE,MAAM;IACpBG,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,CAAC;IACXC,kBAAkB,EAAE,IAAI;IACxBS,QAAQ,EAAE,GAAG;IACbC,aAAa,EAAE,IAAI;IACnBC,MAAM,EAAE,QAAQ;IAChBC,iBAAiB,EAAE,MAAM;IACzBV,MAAM,EAAE,SAAS;IACjBW,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,cAAc,EAAE,GAAG;IACnBC,WAAW,EAAE,GAAG;IAChBC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,GAAG;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,cAAc,EAAE;EAClB,CAAC;EAEDvF,SAAS,CAAC,MAAM;IACdwF,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B9C,UAAU,CAAC,IAAI,CAAC;IAChB;IACA+C,UAAU,CAAC,MAAM;MACf/C,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMgD,SAAS,GAAGA,CAAA,KAAM;IACtBtC,gBAAgB,CAAC,IAAI,CAAC;IACtBG,IAAI,CAACoC,WAAW,CAAC,CAAC;IAClBzC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM0C,UAAU,GAAIC,MAAoB,IAAK;IAC3CzC,gBAAgB,CAACyC,MAAM,CAAC;IACxBtC,IAAI,CAACuC,cAAc,CAAC;MAClB,GAAGD,MAAM;MACTtB,aAAa,EAAEzC,KAAK,CAAC+D,MAAM,CAACtB,aAAa,CAAC;MAC1CC,UAAU,EAAE1C,KAAK,CAAC+D,MAAM,CAACrB,UAAU;IACrC,CAAC,CAAC;IACFtB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM6C,YAAY,GAAIrC,EAAU,IAAK;IACnClD,KAAK,CAACwF,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAEA,CAAA,KAAM;QACV/E,OAAO,CAACgF,OAAO,CAAC,MAAM,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF5D,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIf,UAAU,CAACe,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDpF,OAAO,CAACgF,OAAO,CAACjD,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;MAChDD,iBAAiB,CAAC,KAAK,CAAC;MACxBsC,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdrF,OAAO,CAACqF,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACR/D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlG,KAAK,CAACwF,OAAO,CAAC;MACZC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAE,mCAAmC;MAC5CC,IAAI,EAAEA,CAAA,KAAM;QACV/E,OAAO,CAACgF,OAAO,CAAC,QAAQ,CAAC;QACzBZ,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmB,cAAc,GAAIvC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,OAAO;MAC7B,KAAK,SAAS;QAAE,OAAO,QAAQ;MAC/B,KAAK,OAAO;QAAE,OAAO,KAAK;MAC1B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMwC,aAAa,GAAIxC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,IAAI;MAC3B,KAAK,OAAO;QAAE,OAAO,IAAI;MACzB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMyC,eAAe,GAAI5C,QAAgB,IAAK;IAC5C,IAAIA,QAAQ,GAAG,CAAC,EAAE;MAChB,oBAAOhC,OAAA,CAAC6E,kBAAkB;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC5D,CAAC,MAAM,IAAInD,QAAQ,GAAG,CAAC,EAAE;MACvB,oBAAOhC,OAAA,CAACoF,oBAAoB;QAACN,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC9D;IACA,oBAAOnF,OAAA,CAACP,mBAAmB;MAACqF,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7D,CAAC;EAED,MAAME,mBAA8C,GAAG,CACrD;IACErB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEzB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBG,QAAQ,EAAE;EACZ,CAAC,EACD;IACE1B,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBG,QAAQ,EAAE;EACZ,CAAC,EACD;IACE1B,KAAK,EAAE,IAAI;IACXsB,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACExB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVG,MAAM,EAAGC,IAAY,IAAK9F,cAAc,CAAC8F,IAAI;EAC/C,CAAC,EACD;IACE5B,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVG,MAAM,EAAGC,IAAY,IAAK9F,cAAc,CAAC8F,IAAI;EAC/C,CAAC,EACD;IACE5B,KAAK,EAAE,IAAI;IACXsB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVG,MAAM,EAAEA,CAAC3D,QAAgB,EAAE4B,MAAoB,kBAC7C5D,OAAA,CAAC7B,KAAK;MAAC0H,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,gBAClC/F,OAAA,CAAC7B,KAAK;QAAA4H,QAAA,GACHnB,eAAe,CAAC5C,QAAQ,CAAC,eAC1BhC,OAAA,CAACI,IAAI;UAAC0E,KAAK,EAAE;YAAEC,KAAK,EAAE/C,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAGA,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG;UAAU,CAAE;UAAA+D,QAAA,EACrFjG,cAAc,CAACkG,IAAI,CAACC,GAAG,CAACjE,QAAQ,CAAC;QAAC;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACRnF,OAAA,CAACI,IAAI;QAAC8F,IAAI,EAAC,WAAW;QAACpB,KAAK,EAAE;UAAEqB,QAAQ,EAAE;QAAG,CAAE;QAAAJ,QAAA,GAC5CnC,MAAM,CAAC3B,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE2B,MAAM,CAAC3B,kBAAkB,CAACmE,OAAO,CAAC,CAAC,CAAC,EAAC,GAClF;MAAA;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACEnB,KAAK,EAAE,IAAI;IACXsB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTG,MAAM,EAAGxD,MAAc,iBACrBnC,OAAA,CAACjB,GAAG;MAACgG,KAAK,EAAEL,cAAc,CAACvC,MAAM,CAAE;MAAA4D,QAAA,EAChCpB,aAAa,CAACxC,MAAM;IAAC;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEnB,KAAK,EAAE,KAAK;IACZsB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACExB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVG,MAAM,EAAGU,IAAY,IAAKxG,KAAK,CAACwG,IAAI,CAAC,CAACC,MAAM,CAAC,YAAY;EAC3D,CAAC,EACD;IACEtC,KAAK,EAAE,IAAI;IACXuB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAO;IACdE,MAAM,EAAEA,CAACY,CAAC,EAAE3C,MAAM,kBAChB5D,OAAA,CAAC7B,KAAK;MAAA4H,QAAA,gBACJ/F,OAAA,CAAC9B,MAAM;QACLgI,IAAI,EAAC,MAAM;QACXJ,IAAI,EAAC,OAAO;QACZU,IAAI,eAAExG,OAAA,CAACX,YAAY;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBsB,OAAO,EAAEA,CAAA,KAAM9C,UAAU,CAACC,MAAM,CAAE;QAAAmC,QAAA,EACnC;MAED;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnF,OAAA,CAAC9B,MAAM;QACLgI,IAAI,EAAC,MAAM;QACXJ,IAAI,EAAC,OAAO;QACZY,MAAM;QACNF,IAAI,eAAExG,OAAA,CAACV,cAAc;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBsB,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAACF,MAAM,CAACnC,EAAE,CAAE;QAAAsE,QAAA,EACxC;MAED;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,MAAMwB,eAA0C,GAAG,CACjD;IACE3C,KAAK,EAAE,KAAK;IACZsB,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACExB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACExB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBG,QAAQ,EAAE;EACZ,CAAC,EACD;IACE1B,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVG,MAAM,EAAGC,IAAY,IAAK9F,cAAc,CAAC8F,IAAI;EAC/C,CAAC,EACD;IACE5B,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVG,MAAM,EAAGC,IAAY,IAAK9F,cAAc,CAAC8F,IAAI;EAC/C,CAAC,EACD;IACE5B,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVG,MAAM,EAAEA,CAAC3D,QAAgB,EAAE4B,MAAoB,kBAC7C5D,OAAA,CAAC7B,KAAK;MAAC0H,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,gBAClC/F,OAAA,CAACI,IAAI;QAAC0E,KAAK,EAAE;UAAEC,KAAK,EAAE/C,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG;QAAU,CAAE;QAAA+D,QAAA,EAC1DjG,cAAc,CAACkG,IAAI,CAACC,GAAG,CAACjE,QAAQ,CAAC;MAAC;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACPnF,OAAA,CAACI,IAAI;QAAC8F,IAAI,EAAC,WAAW;QAACpB,KAAK,EAAE;UAAEqB,QAAQ,EAAE;QAAG,CAAE;QAAAJ,QAAA,GAC5CnC,MAAM,CAAC3B,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE2B,MAAM,CAAC3B,kBAAkB,CAACmE,OAAO,CAAC,CAAC,CAAC,EAAC,GAClF;MAAA;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACEnB,KAAK,EAAE,IAAI;IACXsB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACExB,KAAK,EAAE,KAAK;IACZsB,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVG,MAAM,EAAG3D,QAAgB,iBACvBhC,OAAA,CAACI,IAAI;MAAC0E,KAAK,EAAE;QAAEC,KAAK,EAAE/C,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG;MAAU,CAAE;MAAA+D,QAAA,EAC1DjG,cAAc,CAACkG,IAAI,CAACC,GAAG,CAACjE,QAAQ,CAAC;IAAC;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAEV,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbG,QAAQ,EAAE;EACZ,CAAC,EACD;IACE1B,KAAK,EAAE,IAAI;IACXsB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTG,MAAM,EAAGxD,MAAc,IAAK;MAC1B,MAAMyE,SAAS,GAAG;QAChBC,OAAO,EAAE;UAAE9B,KAAK,EAAE,QAAQ;UAAE+B,IAAI,EAAE;QAAM,CAAC;QACzCC,aAAa,EAAE;UAAEhC,KAAK,EAAE,MAAM;UAAE+B,IAAI,EAAE;QAAM,CAAC;QAC7CE,QAAQ,EAAE;UAAEjC,KAAK,EAAE,OAAO;UAAE+B,IAAI,EAAE;QAAM;MAC1C,CAAC;MACD,MAAMG,MAAM,GAAGL,SAAS,CAACzE,MAAM,CAA2B;MAC1D,oBAAOnC,OAAA,CAACjB,GAAG;QAACgG,KAAK,EAAEkC,MAAM,CAAClC,KAAM;QAAAgB,QAAA,EAAEkB,MAAM,CAACH;MAAI;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACtD;EACF,CAAC,CACF;EAED,MAAM+B,iBAAiB,GAAGA,CAAA,kBACxBlH,OAAA;IAAA+F,QAAA,GAEGhD,YAAY,CAACI,UAAU,GAAG,CAAC,iBAC1BnD,OAAA,CAAClB,KAAK;MACJK,OAAO,EAAC,sCAAQ;MAChBgI,WAAW,EAAE,MAAMpE,YAAY,CAACI,UAAU,wBAAyB;MACnE+C,IAAI,EAAC,OAAO;MACZkB,QAAQ;MACRC,QAAQ;MACRvC,KAAK,EAAE;QAAEwC,YAAY,EAAE;MAAG;IAAE;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACF,eAGDnF,OAAA,CAACtB,GAAG;MAAC6I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACzC,KAAK,EAAE;QAAEwC,YAAY,EAAE;MAAG,CAAE;MAAAvB,QAAA,gBACjD/F,OAAA,CAACrB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACjB/F,OAAA,CAAChC,IAAI;UAAA+H,QAAA,eACH/F,OAAA,CAACpB,SAAS;YACRoF,KAAK,EAAC,0BAAM;YACZ0D,KAAK,EAAE3E,YAAY,CAACC,cAAe;YACnC2E,MAAM,eAAE3H,OAAA,CAACJ,cAAc;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3ByC,UAAU,EAAE;cAAE7C,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnF,OAAA,CAACrB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACjB/F,OAAA,CAAChC,IAAI;UAAA+H,QAAA,gBACH/F,OAAA,CAACpB,SAAS;YACRoF,KAAK,EAAC,0BAAM;YACZ0D,KAAK,EAAE3E,YAAY,CAACE,WAAY;YAChC2E,UAAU,EAAE;cAAE7C,KAAK,EAAE;YAAU,CAAE;YACjC8C,MAAM,eACJ7H,OAAA,CAACd,OAAO;cAAC8E,KAAK,EAAE,OAAO,CAAEjB,YAAY,CAACE,WAAW,GAAGF,YAAY,CAACC,cAAc,GAAI,GAAG,EAAEoD,OAAO,CAAC,CAAC,CAAC,GAAI;cAAAL,QAAA,eACpG/F,OAAA,CAACP,mBAAmB;gBAACqF,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UACV;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnF,OAAA,CAACnB,QAAQ;YACPiJ,OAAO,EAAG/E,YAAY,CAACE,WAAW,GAAGF,YAAY,CAACC,cAAc,GAAI,GAAI;YACxE8C,IAAI,EAAC,OAAO;YACZiC,QAAQ,EAAE,KAAM;YAChBC,WAAW,EAAC;UAAS;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnF,OAAA,CAACrB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACjB/F,OAAA,CAAChC,IAAI;UAAA+H,QAAA,gBACH/F,OAAA,CAACpB,SAAS;YACRoF,KAAK,EAAC,0BAAM;YACZ0D,KAAK,EAAE3E,YAAY,CAACG,YAAa;YACjC0E,UAAU,EAAE;cAAE7C,KAAK,EAAE;YAAU,CAAE;YACjC8C,MAAM,eACJ7H,OAAA,CAACd,OAAO;cAAC8E,KAAK,EAAE,OAAO,CAAEjB,YAAY,CAACG,YAAY,GAAGH,YAAY,CAACC,cAAc,GAAI,GAAG,EAAEoD,OAAO,CAAC,CAAC,CAAC,GAAI;cAAAL,QAAA,eACrG/F,OAAA,CAACR,eAAe;gBAACsF,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UACV;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnF,OAAA,CAACnB,QAAQ;YACPiJ,OAAO,EAAG/E,YAAY,CAACG,YAAY,GAAGH,YAAY,CAACC,cAAc,GAAI,GAAI;YACzE8C,IAAI,EAAC,OAAO;YACZiC,QAAQ,EAAE,KAAM;YAChBC,WAAW,EAAC;UAAS;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnF,OAAA,CAACrB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eACjB/F,OAAA,CAAChC,IAAI;UAAA+H,QAAA,gBACH/F,OAAA,CAACpB,SAAS;YACRoF,KAAK,EAAC,0BAAM;YACZ0D,KAAK,EAAE3E,YAAY,CAACI,UAAW;YAC/ByE,UAAU,EAAE;cAAE7C,KAAK,EAAE;YAAU,CAAE;YACjC8C,MAAM,eACJ7H,OAAA,CAACd,OAAO;cAAC8E,KAAK,EAAE,OAAO,CAAEjB,YAAY,CAACI,UAAU,GAAGJ,YAAY,CAACC,cAAc,GAAI,GAAG,EAAEoD,OAAO,CAAC,CAAC,CAAC,GAAI;cAAAL,QAAA,eACnG/F,OAAA,CAACN,mBAAmB;gBAACoF,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UACV;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFnF,OAAA,CAACnB,QAAQ;YACPiJ,OAAO,EAAG/E,YAAY,CAACI,UAAU,GAAGJ,YAAY,CAACC,cAAc,GAAI,GAAI;YACvE8C,IAAI,EAAC,OAAO;YACZiC,QAAQ,EAAE,KAAM;YAChBC,WAAW,EAAC;UAAS;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA,CAAChC,IAAI;MAACgG,KAAK,EAAC,sCAAQ;MAACiE,KAAK,eACxBjI,OAAA,CAAC7B,KAAK;QAAA4H,QAAA,gBACJ/F,OAAA,CAAC9B,MAAM;UAACsI,IAAI,eAAExG,OAAA,CAACL,YAAY;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACsB,OAAO,EAAEhC,iBAAkB;UAAAsB,QAAA,EAAC;QAE5D;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnF,OAAA,CAAC9B,MAAM;UAACgI,IAAI,EAAC,SAAS;UAACM,IAAI,eAAExG,OAAA,CAACZ,YAAY;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACsB,OAAO,EAAEhD,SAAU;UAAAsC,QAAA,EAAC;QAEnE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAY,QAAA,eACC/F,OAAA,CAAC/B,KAAK;QACJiK,OAAO,EAAE7C,mBAAoB;QAC7B8C,UAAU,EAAE3G,iBAAkB;QAC9B4G,MAAM,EAAC,IAAI;QACX5H,OAAO,EAAEA,OAAQ;QACjB6H,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,KAAK,EAAEhH,iBAAiB,CAACiH,MAAM;UAC/BC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGL,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,MAAM2D,iBAAiB,GAAGA,CAAA,kBACxB9I,OAAA;IAAA+F,QAAA,eACE/F,OAAA,CAAChC,IAAI;MAACgG,KAAK,EAAC,sCAAQ;MAACiE,KAAK,eACxBjI,OAAA,CAAC7B,KAAK;QAAA4H,QAAA,eACJ/F,OAAA,CAAC9B,MAAM;UAACsI,IAAI,eAAExG,OAAA,CAACT,cAAc;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAY,QAAA,EAAC;QAElC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAY,QAAA,eACC/F,OAAA,CAAC/B,KAAK;QACJiK,OAAO,EAAEvB,eAAgB;QACzBwB,UAAU,EAAE3F,aAAc;QAC1B4F,MAAM,EAAC,IAAI;QACX5H,OAAO,EAAEA,OAAQ;QACjB+H,UAAU,EAAE;UACVC,KAAK,EAAEhG,aAAa,CAACiG,MAAM;UAC3BC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGL,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,oBACEnF,OAAA;IAAA+F,QAAA,gBACE/F,OAAA,CAAChC,IAAI;MAAA+H,QAAA,gBACH/F,OAAA,CAACtB,GAAG;QAACqK,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAClE,KAAK,EAAE;UAAEwC,YAAY,EAAE;QAAG,CAAE;QAAAvB,QAAA,gBACtE/F,OAAA,CAACrB,GAAG;UAAAoH,QAAA,eACF/F,OAAA,CAACG,KAAK;YAAC8I,KAAK,EAAE,CAAE;YAACnE,KAAK,EAAE;cAAEoE,MAAM,EAAE;YAAE,CAAE;YAAAnD,QAAA,EAAC;UAEvC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNnF,OAAA,CAACrB,GAAG;UAAAoH,QAAA,eACF/F,OAAA,CAAC7B,KAAK;YAAA4H,QAAA,gBACJ/F,OAAA,CAACC,MAAM;cACLkJ,WAAW,EAAC,wDAAW;cACvBC,UAAU;cACVtE,KAAK,EAAE;gBAAEU,KAAK,EAAE;cAAI,CAAE;cACtBkC,KAAK,EAAEhH,UAAW;cAClB2I,QAAQ,EAAGC,CAAC,IAAK3I,aAAa,CAAC2I,CAAC,CAACC,MAAM,CAAC7B,KAAK;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACFnF,OAAA,CAAC3B,MAAM;cACL8K,WAAW,EAAC,0BAAM;cAClBC,UAAU;cACVtE,KAAK,EAAE;gBAAEU,KAAK,EAAE;cAAI,CAAE;cACtBkC,KAAK,EAAE9G,gBAAiB;cACxByI,QAAQ,EAAExI,mBAAoB;cAC9B2I,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,MAAM;gBAAE/B,KAAK,EAAE;cAAO,CAAC,EAChC;gBAAE+B,KAAK,EAAE,MAAM;gBAAE/B,KAAK,EAAE;cAAO,CAAC,EAChC;gBAAE+B,KAAK,EAAE,MAAM;gBAAE/B,KAAK,EAAE;cAAO,CAAC;YAChC;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnF,OAAA,CAACE,WAAW;cACVwH,KAAK,EAAE5G,SAAU;cACjBuI,QAAQ,EAAGK,KAAK,IAAK3I,YAAY,CAAC2I,KAAmC,CAAE;cACvE5E,KAAK,EAAE;gBAAEU,KAAK,EAAE;cAAI;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFnF,OAAA,CAAC9B,MAAM;cAACsI,IAAI,eAAExG,OAAA,CAACT,cAAc;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAY,QAAA,EAAC;YAElC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnF,OAAA,CAACf,IAAI;QAAC0K,SAAS,EAAEvI,SAAU;QAACiI,QAAQ,EAAEhI,YAAa;QAAA0E,QAAA,gBACjD/F,OAAA,CAACK,OAAO;UAACuJ,GAAG,EAAC,0BAAM;UAAA7D,QAAA,EAChBmB,iBAAiB,CAAC;QAAC,GADE,WAAW;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1B,CAAC,eACVnF,OAAA,CAACK,OAAO;UAACuJ,GAAG,EAAC,0BAAM;UAAA7D,QAAA,EAChB+C,iBAAiB,CAAC;QAAC,GADE,WAAW;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPnF,OAAA,CAACzB,KAAK;MACJyF,KAAK,EAAE9C,aAAa,GAAG,QAAQ,GAAG,QAAS;MAC3C2I,IAAI,EAAE7I,cAAe;MACrB8I,QAAQ,EAAEA,CAAA,KAAM7I,iBAAiB,CAAC,KAAK,CAAE;MACzC8I,MAAM,EAAE,IAAK;MACbvE,KAAK,EAAE,GAAI;MAAAO,QAAA,eAEX/F,OAAA,CAACxB,IAAI;QACH8C,IAAI,EAAEA,IAAK;QACX0I,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE7F,YAAa;QAAA2B,QAAA,gBAEvB/F,OAAA,CAACtB,GAAG;UAAC6I,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd/F,OAAA,CAACrB,GAAG;YAACuL,IAAI,EAAE,EAAG;YAAAnE,QAAA,eACZ/F,OAAA,CAACxB,IAAI,CAAC2L,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBX,KAAK,EAAC,0BAAM;cACZY,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnL,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA4G,QAAA,eAEhD/F,OAAA,CAAC5B,KAAK;gBAAC+K,WAAW,EAAC;cAAS;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnF,OAAA,CAACrB,GAAG;YAACuL,IAAI,EAAE,EAAG;YAAAnE,QAAA,eACZ/F,OAAA,CAACxB,IAAI,CAAC2L,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBX,KAAK,EAAC,0BAAM;cACZY,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnL,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA4G,QAAA,eAEhD/F,OAAA,CAAC5B,KAAK;gBAAC+K,WAAW,EAAC;cAAS;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA,CAACtB,GAAG;UAAC6I,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd/F,OAAA,CAACrB,GAAG;YAACuL,IAAI,EAAE,EAAG;YAAAnE,QAAA,eACZ/F,OAAA,CAACxB,IAAI,CAAC2L,IAAI;cACRC,IAAI,EAAC,eAAe;cACpBX,KAAK,EAAC,0BAAM;cAAA1D,QAAA,eAEZ/F,OAAA,CAAC5B,KAAK;gBAAC+K,WAAW,EAAC;cAAS;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnF,OAAA,CAACrB,GAAG;YAACuL,IAAI,EAAE,EAAG;YAAAnE,QAAA,eACZ/F,OAAA,CAACxB,IAAI,CAAC2L,IAAI;cACRC,IAAI,EAAC,MAAM;cACXX,KAAK,EAAC,cAAI;cACVY,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnL,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA4G,QAAA,eAE9C/F,OAAA,CAAC3B,MAAM;gBACL8K,WAAW,EAAC,gCAAO;gBACnBK,OAAO,EAAE,CACP;kBAAEC,KAAK,EAAE,KAAK;kBAAE/B,KAAK,EAAE;gBAAM,CAAC,EAC9B;kBAAE+B,KAAK,EAAE,GAAG;kBAAE/B,KAAK,EAAE;gBAAI,CAAC,EAC1B;kBAAE+B,KAAK,EAAE,IAAI;kBAAE/B,KAAK,EAAE;gBAAK,CAAC,EAC5B;kBAAE+B,KAAK,EAAE,KAAK;kBAAE/B,KAAK,EAAE;gBAAM,CAAC;cAC9B;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA,CAACtB,GAAG;UAAC6I,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd/F,OAAA,CAACrB,GAAG;YAACuL,IAAI,EAAE,EAAG;YAAAnE,QAAA,eACZ/F,OAAA,CAACxB,IAAI,CAAC2L,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBX,KAAK,EAAC,0BAAM;cACZY,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnL,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA4G,QAAA,eAEhD/F,OAAA,CAACvB,WAAW;gBACV0K,WAAW,EAAC,4CAAS;gBACrBrE,KAAK,EAAE;kBAAEU,KAAK,EAAE;gBAAO,CAAE;gBACzB+E,GAAG,EAAE,CAAE;gBACPC,SAAS,EAAE,CAAE;gBACbC,SAAS,EAAG/C,KAAK,IAAK,KAAKA,KAAK,EAAE,CAACgD,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAE;gBACzEC,MAAM,EAAGjD,KAAK,IAAKA,KAAK,CAAEgD,OAAO,CAAC,YAAY,EAAE,EAAE;cAAE;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnF,OAAA,CAACrB,GAAG;YAACuL,IAAI,EAAE,EAAG;YAAAnE,QAAA,eACZ/F,OAAA,CAACxB,IAAI,CAAC2L,IAAI;cACRC,IAAI,EAAC,UAAU;cACfX,KAAK,EAAC,0BAAM;cACZY,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnL,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA4G,QAAA,eAEhD/F,OAAA,CAAC3B,MAAM;gBACL8K,WAAW,EAAC,4CAAS;gBACrBK,OAAO,EAAE,CACP;kBAAEC,KAAK,EAAE,MAAM;kBAAE/B,KAAK,EAAE;gBAAO,CAAC,EAChC;kBAAE+B,KAAK,EAAE,MAAM;kBAAE/B,KAAK,EAAE;gBAAO,CAAC,EAChC;kBAAE+B,KAAK,EAAE,MAAM;kBAAE/B,KAAK,EAAE;gBAAO,CAAC;cAChC;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA,CAACtB,GAAG;UAAC6I,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd/F,OAAA,CAACrB,GAAG;YAACuL,IAAI,EAAE,EAAG;YAAAnE,QAAA,eACZ/F,OAAA,CAACxB,IAAI,CAAC2L,IAAI;cACRC,IAAI,EAAC,UAAU;cACfX,KAAK,EAAC,gCAAO;cAAA1D,QAAA,eAEb/F,OAAA,CAAC5B,KAAK;gBAAC+K,WAAW,EAAC;cAAU;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnF,OAAA,CAACrB,GAAG;YAACuL,IAAI,EAAE,EAAG;YAAAnE,QAAA,eACZ/F,OAAA,CAACxB,IAAI,CAAC2L,IAAI;cACRC,IAAI,EAAC,eAAe;cACpBX,KAAK,EAAC,0BAAM;cACZY,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnL,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA4G,QAAA,eAEhD/F,OAAA,CAAC1B,UAAU;gBAACwG,KAAK,EAAE;kBAAEU,KAAK,EAAE;gBAAO;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA,CAACxB,IAAI,CAAC2L,IAAI;UACRC,IAAI,EAAC,YAAY;UACjBX,KAAK,EAAC,0BAAM;UAAA1D,QAAA,eAEZ/F,OAAA,CAAC1B,UAAU;YAACwG,KAAK,EAAE;cAAEU,KAAK,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAEZnF,OAAA,CAACxB,IAAI,CAAC2L,IAAI;UAACrF,KAAK,EAAE;YAAEwC,YAAY,EAAE,CAAC;YAAEsD,SAAS,EAAE;UAAQ,CAAE;UAAA7E,QAAA,eACxD/F,OAAA,CAAC7B,KAAK;YAAA4H,QAAA,gBACJ/F,OAAA,CAAC9B,MAAM;cAACuI,OAAO,EAAEA,CAAA,KAAMxF,iBAAiB,CAAC,KAAK,CAAE;cAAA8E,QAAA,EAAC;YAEjD;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnF,OAAA,CAAC9B,MAAM;cAACgI,IAAI,EAAC,SAAS;cAAC2E,QAAQ,EAAC,QAAQ;cAACrK,OAAO,EAAEA,OAAQ;cAAAuF,QAAA,EACvD7E,aAAa,GAAG,IAAI,GAAG;YAAI;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAluBID,gBAA0B;EAAA,QAQf9B,IAAI,CAAC+C,OAAO;AAAA;AAAAuJ,EAAA,GARvBxK,gBAA0B;AAouBhC,eAAeA,gBAAgB;AAAC,IAAAwK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}