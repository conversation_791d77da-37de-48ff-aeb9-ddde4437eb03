{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Card,Typography,Table,Button,Space,Input,Select,Row,Col,Tag,Modal,Form,DatePicker,Upload,Image,Descriptions,Tabs,Timeline,Alert,QRCode,Tooltip,Divider,Checkbox}from'antd';import*as XLSX from'xlsx';import{PlusOutlined,EditOutlined,DeleteOutlined,EyeOutlined,QrcodeOutlined,DownloadOutlined,PrinterOutlined,ToolOutlined,HistoryOutlined,CameraOutlined}from'@ant-design/icons';import dayjs from'dayjs';import{useAppDispatch,useAppSelector}from'../../hooks/redux';import{fetchDeviceArchives}from'../../store/slices/serviceSlice';import{formatDate}from'../../utils';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Search}=Input;const{TabPane}=Tabs;const DeviceArchivePage=()=>{var _selectedDevice$maint;const dispatch=useAppDispatch();const{deviceArchives,loading}=useAppSelector(state=>state.service);const[searchKeyword,setSearchKeyword]=useState('');const[statusFilter,setStatusFilter]=useState('');const[deviceModalVisible,setDeviceModalVisible]=useState(false);const[qrModalVisible,setQrModalVisible]=useState(false);const[detailModalVisible,setDetailModalVisible]=useState(false);const[editingDevice,setEditingDevice]=useState(null);const[selectedDevice,setSelectedDevice]=useState(null);const[deviceForm]=Form.useForm();const[exportModalVisible,setExportModalVisible]=useState(false);const[exportFormat,setExportFormat]=useState('excel');const[exportFields,setExportFields]=useState(['deviceCode','deviceName','deviceType','model','serialNumber','manufacturer','installLocation','status']);useEffect(()=>{loadData();},[searchKeyword,statusFilter]);const loadData=()=>{dispatch(fetchDeviceArchives({keyword:searchKeyword,status:statusFilter}));};const handleAddDevice=()=>{setEditingDevice(null);deviceForm.resetFields();setDeviceModalVisible(true);};const handleEditDevice=record=>{setEditingDevice(record);deviceForm.setFieldsValue(_objectSpread(_objectSpread({},record),{},{installDate:record.installDate?dayjs(record.installDate):null,warrantyEndDate:record.warrantyEndDate?dayjs(record.warrantyEndDate):null}));setDeviceModalVisible(true);};const handleViewDevice=record=>{setSelectedDevice(record);setDetailModalVisible(true);};const handleShowQR=record=>{setSelectedDevice(record);setQrModalVisible(true);};const handleDeviceModalOk=async()=>{try{const values=await deviceForm.validateFields();if(editingDevice){// TODO: 实现更新API\nModal.success({title:'更新成功',content:'设备档案已更新'});}else{// TODO: 实现创建API\nModal.success({title:'添加成功',content:'设备档案已添加'});}setDeviceModalVisible(false);loadData();}catch(error){Modal.error({title:'操作失败',content:'保存设备档案时发生错误'});}};const handleDeleteDevice=async record=>{try{// TODO: 实现删除API\nModal.success({title:'删除成功',content:'设备档案已删除'});loadData();}catch(error){Modal.error({title:'删除失败',content:'删除设备档案时发生错误'});}};// 处理导出\nconst handleExport=()=>{setExportModalVisible(true);};const executeExport=()=>{try{// 准备导出数据\nconst exportData=mockDevices.map(device=>{const data={};if(exportFields.includes('deviceCode'))data['设备编码']=device.deviceCode;if(exportFields.includes('deviceName'))data['设备名称']=device.deviceName;if(exportFields.includes('deviceType'))data['设备类型']=device.deviceType;if(exportFields.includes('model'))data['设备型号']=device.model;if(exportFields.includes('serialNumber'))data['序列号']=device.serialNumber;if(exportFields.includes('manufacturer'))data['制造商']=device.manufacturer;if(exportFields.includes('installLocation'))data['安装位置']=device.installLocation;if(exportFields.includes('installDate'))data['安装日期']=formatDate(device.installDate);if(exportFields.includes('warrantyEndDate'))data['保修到期']=formatDate(device.warrantyEndDate);if(exportFields.includes('status'))data['状态']=device.status;if(exportFields.includes('responsiblePerson'))data['责任人']=device.responsiblePerson;if(exportFields.includes('lastMaintenanceDate'))data['上次维护']=formatDate(device.lastMaintenanceDate);if(exportFields.includes('nextMaintenanceDate'))data['下次维护']=formatDate(device.nextMaintenanceDate);return data;});// 创建工作簿\nconst ws=XLSX.utils.json_to_sheet(exportData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'设备档案');// 下载文件\nconst fileName=\"\\u8BBE\\u5907\\u6863\\u6848_\".concat(new Date().toISOString().split('T')[0],\".\").concat(exportFormat==='excel'?'xlsx':'csv');XLSX.writeFile(wb,fileName);Modal.success({title:'导出成功',content:'设备档案数据已导出'});setExportModalVisible(false);}catch(error){Modal.error({title:'导出失败',content:'导出设备档案数据时发生错误'});}};// 模拟设备档案数据\nconst mockDevices=[{id:'1',deviceCode:'DEV-ANT-001',deviceName:'5G基站天线系统',deviceType:'天线设备',model:'ANT-5G-V2.0',serialNumber:'SN20240001',manufacturer:'华为技术有限公司',installLocation:'北京市朝阳区CBD基站',installDate:'2024-01-15T00:00:00Z',warrantyEndDate:'2027-01-15T00:00:00Z',status:'运行中',responsiblePerson:'service_tech',lastMaintenanceDate:'2024-03-01T00:00:00Z',nextMaintenanceDate:'2024-06-01T00:00:00Z',qrCode:'QR-DEV-ANT-001',photos:['/api/photos/device1.jpg'],specifications:{frequency:'3.5GHz',gain:'18dBi',power:'200W',weight:'25kg'},maintenanceRecords:[{id:'1',date:'2024-03-01T00:00:00Z',type:'定期保养',description:'清洁天线表面，检查连接器',technician:'service_tech',status:'已完成'},{id:'2',date:'2024-01-15T00:00:00Z',type:'安装调试',description:'设备安装及初始调试',technician:'service_tech',status:'已完成'}]},{id:'2',deviceCode:'DEV-RF-002',deviceName:'RF功率放大器',deviceType:'RF设备',model:'RF-AMP-100W',serialNumber:'SN20240002',manufacturer:'中兴通讯股份有限公司',installLocation:'上海市浦东新区陆家嘴基站',installDate:'2024-02-01T00:00:00Z',warrantyEndDate:'2027-02-01T00:00:00Z',status:'维护中',responsiblePerson:'service_tech',lastMaintenanceDate:'2024-03-15T00:00:00Z',nextMaintenanceDate:'2024-06-15T00:00:00Z',qrCode:'QR-DEV-RF-002',photos:['/api/photos/device2.jpg'],specifications:{frequency:'2.6GHz',power:'100W',efficiency:'45%',weight:'15kg'},maintenanceRecords:[{id:'1',date:'2024-03-15T00:00:00Z',type:'故障维修',description:'功率输出异常，更换功率模块',technician:'service_tech',status:'进行中'}]},{id:'3',deviceCode:'DEV-CTL-003',deviceName:'基站控制器',deviceType:'控制设备',model:'BSC-V3.0',serialNumber:'SN20240003',manufacturer:'大唐移动通信设备有限公司',installLocation:'广州市天河区珠江新城基站',installDate:'2024-01-20T00:00:00Z',warrantyEndDate:'2027-01-20T00:00:00Z',status:'运行中',responsiblePerson:'service_tech',lastMaintenanceDate:'2024-02-20T00:00:00Z',nextMaintenanceDate:'2024-05-20T00:00:00Z',qrCode:'QR-DEV-CTL-003',photos:['/api/photos/device3.jpg'],specifications:{channels:'64',capacity:'1000用户',power:'500W',weight:'50kg'},maintenanceRecords:[{id:'1',date:'2024-02-20T00:00:00Z',type:'定期保养',description:'系统软件更新，硬件检查',technician:'service_tech',status:'已完成'}]}];const deviceColumns=[{title:'设备编码',dataIndex:'deviceCode',key:'deviceCode',width:120,fixed:'left'},{title:'设备信息',key:'deviceInfo',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:record.deviceName}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:record.model}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:12},children:[\"SN: \",record.serialNumber]})]})},{title:'设备类型',dataIndex:'deviceType',key:'deviceType',width:100,render:type=>/*#__PURE__*/_jsx(Tag,{color:type==='天线设备'?'blue':type==='RF设备'?'green':'orange',children:type})},{title:'制造商',dataIndex:'manufacturer',key:'manufacturer',ellipsis:true},{title:'安装位置',dataIndex:'installLocation',key:'installLocation',ellipsis:true},{title:'安装日期',dataIndex:'installDate',key:'installDate',width:100,render:date=>formatDate(date)},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>{const color=status==='运行中'?'green':status==='维护中'?'orange':status==='故障'?'red':'default';return/*#__PURE__*/_jsx(Tag,{color:color,children:status});}},{title:'下次维护',dataIndex:'nextMaintenanceDate',key:'nextMaintenanceDate',width:100,render:date=>{const isOverdue=dayjs(date).isBefore(dayjs());return/*#__PURE__*/_jsx(Text,{style:{color:isOverdue?'#ff4d4f':undefined},children:formatDate(date)});}},{title:'操作',key:'action',width:200,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\\u8BE6\\u60C5\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewDevice(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u4E8C\\u7EF4\\u7801\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(QrcodeOutlined,{}),onClick:()=>handleShowQR(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEditDevice(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>{Modal.confirm({title:'确认删除',content:'确定要删除这个设备档案吗？',onOk:()=>handleDeleteDevice(record)});}})})]})}];return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u8BBE\\u5907\\u6863\\u6848\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u8BBE\\u5907\\u7F16\\u7801\\u3001\\u540D\\u79F0\",allowClear:true,style:{width:200},onSearch:setSearchKeyword}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BBE\\u5907\\u72B6\\u6001\",allowClear:true,style:{width:120},value:statusFilter,onChange:setStatusFilter,options:[{label:'运行中',value:'运行中'},{label:'维护中',value:'维护中'},{label:'故障',value:'故障'},{label:'停用',value:'停用'}]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAddDevice,children:\"\\u65B0\\u589E\\u8BBE\\u5907\"})]})})]}),/*#__PURE__*/_jsx(Table,{columns:deviceColumns,dataSource:mockDevices,loading:loading,rowKey:\"id\",scroll:{x:1400},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsx(Modal,{title:editingDevice?'编辑设备档案':'新增设备档案',open:deviceModalVisible,onOk:handleDeviceModalOk,onCancel:()=>setDeviceModalVisible(false),width:800,okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Form,{form:deviceForm,layout:\"vertical\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"deviceCode\",label:\"\\u8BBE\\u5907\\u7F16\\u7801\",rules:[{required:true,message:'请输入设备编码'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u7F16\\u7801\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"deviceName\",label:\"\\u8BBE\\u5907\\u540D\\u79F0\",rules:[{required:true,message:'请输入设备名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"deviceType\",label:\"\\u8BBE\\u5907\\u7C7B\\u578B\",rules:[{required:true,message:'请选择设备类型'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"\\u5929\\u7EBF\\u8BBE\\u5907\",children:\"\\u5929\\u7EBF\\u8BBE\\u5907\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"RF\\u8BBE\\u5907\",children:\"RF\\u8BBE\\u5907\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u63A7\\u5236\\u8BBE\\u5907\",children:\"\\u63A7\\u5236\\u8BBE\\u5907\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4F20\\u8F93\\u8BBE\\u5907\",children:\"\\u4F20\\u8F93\\u8BBE\\u5907\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u7535\\u6E90\\u8BBE\\u5907\",children:\"\\u7535\\u6E90\\u8BBE\\u5907\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"model\",label:\"\\u8BBE\\u5907\\u578B\\u53F7\",rules:[{required:true,message:'请输入设备型号'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u578B\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"serialNumber\",label:\"\\u5E8F\\u5217\\u53F7\",rules:[{required:true,message:'请输入序列号'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5E8F\\u5217\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"manufacturer\",label:\"\\u5236\\u9020\\u5546\",rules:[{required:true,message:'请输入制造商'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,children:/*#__PURE__*/_jsx(Form.Item,{name:\"installLocation\",label:\"\\u5B89\\u88C5\\u4F4D\\u7F6E\",rules:[{required:true,message:'请输入安装位置'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5B89\\u88C5\\u4F4D\\u7F6E\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"installDate\",label:\"\\u5B89\\u88C5\\u65E5\\u671F\",rules:[{required:true,message:'请选择安装日期'}],children:/*#__PURE__*/_jsx(DatePicker,{style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"warrantyEndDate\",label:\"\\u4FDD\\u4FEE\\u5230\\u671F\\u65E5\\u671F\",children:/*#__PURE__*/_jsx(DatePicker,{style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"status\",label:\"\\u8BBE\\u5907\\u72B6\\u6001\",initialValue:\"\\u8FD0\\u884C\\u4E2D\",children:/*#__PURE__*/_jsxs(Select,{children:[/*#__PURE__*/_jsx(Select.Option,{value:\"\\u8FD0\\u884C\\u4E2D\",children:\"\\u8FD0\\u884C\\u4E2D\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u7EF4\\u62A4\\u4E2D\",children:\"\\u7EF4\\u62A4\\u4E2D\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u6545\\u969C\",children:\"\\u6545\\u969C\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u505C\\u7528\",children:\"\\u505C\\u7528\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"responsiblePerson\",label:\"\\u8D23\\u4EFB\\u4EBA\",rules:[{required:true,message:'请输入责任人'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8D23\\u4EFB\\u4EBA\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,children:/*#__PURE__*/_jsx(Form.Item,{name:\"photos\",label:\"\\u8BBE\\u5907\\u7167\\u7247\",children:/*#__PURE__*/_jsx(Upload,{listType:\"picture-card\",maxCount:5,beforeUpload:()=>false,children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(CameraOutlined,{}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:\"\\u4E0A\\u4F20\\u7167\\u7247\"})]})})})})]})})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u8BBE\\u5907\\u8BE6\\u60C5\",open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),width:1000,footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\"),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(PrinterOutlined,{}),children:\"\\u6253\\u5370\"},\"print\"),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(QrcodeOutlined,{}),onClick:()=>handleShowQR(selectedDevice),children:\"\\u4E8C\\u7EF4\\u7801\"},\"qr\")],children:selectedDevice&&/*#__PURE__*/_jsxs(Tabs,{defaultActiveKey:\"basic\",children:[/*#__PURE__*/_jsxs(TabPane,{tab:\"\\u57FA\\u672C\\u4FE1\\u606F\",children:[/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:2,children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BBE\\u5907\\u7F16\\u7801\",children:selectedDevice.deviceCode}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BBE\\u5907\\u540D\\u79F0\",children:selectedDevice.deviceName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BBE\\u5907\\u7C7B\\u578B\",children:selectedDevice.deviceType}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BBE\\u5907\\u578B\\u53F7\",children:selectedDevice.model}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5E8F\\u5217\\u53F7\",children:selectedDevice.serialNumber}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5236\\u9020\\u5546\",children:selectedDevice.manufacturer}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5B89\\u88C5\\u4F4D\\u7F6E\",span:2,children:selectedDevice.installLocation}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5B89\\u88C5\\u65E5\\u671F\",children:formatDate(selectedDevice.installDate)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4FDD\\u4FEE\\u5230\\u671F\",children:formatDate(selectedDevice.warrantyEndDate)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BBE\\u5907\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Tag,{color:selectedDevice.status==='运行中'?'green':'orange',children:selectedDevice.status})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D23\\u4EFB\\u4EBA\",children:selectedDevice.responsiblePerson})]}),/*#__PURE__*/_jsx(Divider,{orientation:\"left\",children:\"\\u6280\\u672F\\u89C4\\u683C\"}),/*#__PURE__*/_jsx(Descriptions,{bordered:true,column:2,children:Object.entries(selectedDevice.specifications||{}).map(_ref=>{let[key,value]=_ref;return/*#__PURE__*/_jsx(Descriptions.Item,{label:key,children:String(value)},key);})}),selectedDevice.photos&&selectedDevice.photos.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Divider,{orientation:\"left\",children:\"\\u8BBE\\u5907\\u7167\\u7247\"}),/*#__PURE__*/_jsx(Space,{children:selectedDevice.photos.map((photo,index)=>/*#__PURE__*/_jsx(Image,{width:100,height:100,src:photo,fallback:\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"},index))})]})]},\"basic\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u7EF4\\u62A4\\u8BB0\\u5F55\",children:/*#__PURE__*/_jsx(Timeline,{children:(_selectedDevice$maint=selectedDevice.maintenanceRecords)===null||_selectedDevice$maint===void 0?void 0:_selectedDevice$maint.map(record=>/*#__PURE__*/_jsxs(Timeline.Item,{color:record.status==='已完成'?'green':'blue',dot:record.status==='已完成'?/*#__PURE__*/_jsx(ToolOutlined,{}):/*#__PURE__*/_jsx(HistoryOutlined,{}),children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:record.type}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{marginLeft:8},children:formatDate(record.date)})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:/*#__PURE__*/_jsx(Text,{children:record.description})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:4},children:[/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u6280\\u672F\\u5458: \",record.technician]}),/*#__PURE__*/_jsx(Tag,{color:record.status==='已完成'?'green':'processing',style:{marginLeft:8},children:record.status})]})]},record.id))})},\"maintenance\"),/*#__PURE__*/_jsxs(TabPane,{tab:\"\\u670D\\u52A1BOM\",children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u670D\\u52A1BOM\",description:\"\\u663E\\u793A\\u8BE5\\u8BBE\\u5907\\u7684\\u670D\\u52A1BOM\\u4FE1\\u606F\\uFF0C\\u5305\\u62EC\\u5907\\u4EF6\\u6E05\\u5355\\u3001\\u7EF4\\u62A4\\u5DE5\\u5177\\u7B49\",type:\"info\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u670D\\u52A1BOM\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"})]},\"serviceBom\")]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u8BBE\\u5907\\u4E8C\\u7EF4\\u7801\",open:qrModalVisible,onCancel:()=>setQrModalVisible(false),width:400,footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setQrModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),children:\"\\u4E0B\\u8F7D\"},\"download\"),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(PrinterOutlined,{}),children:\"\\u6253\\u5370\"},\"print\")],children:selectedDevice&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(QRCode,{value:\"\".concat(window.location.origin,\"/device/\").concat(selectedDevice.id),size:200}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:16},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:selectedDevice.deviceCode}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:selectedDevice.deviceName})]})]})}),/*#__PURE__*/_jsxs(Modal,{title:\"\\u5BFC\\u51FA\\u8BBE\\u5907\\u6863\\u6848\",open:exportModalVisible,onOk:executeExport,onCancel:()=>setExportModalVisible(false),width:600,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:8},children:\"\\u5BFC\\u51FA\\u683C\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Select,{value:exportFormat,onChange:setExportFormat,style:{width:'100%'},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"excel\",children:\"Excel (.xlsx)\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"csv\",children:\"CSV (.csv)\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:8},children:\"\\u9009\\u62E9\\u5BFC\\u51FA\\u5B57\\u6BB5\\uFF1A\"}),/*#__PURE__*/_jsx(Checkbox.Group,{value:exportFields,onChange:setExportFields,style:{width:'100%'},children:/*#__PURE__*/_jsxs(Row,{gutter:[16,8],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"deviceCode\",children:\"\\u8BBE\\u5907\\u7F16\\u7801\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"deviceName\",children:\"\\u8BBE\\u5907\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"deviceType\",children:\"\\u8BBE\\u5907\\u7C7B\\u578B\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"model\",children:\"\\u8BBE\\u5907\\u578B\\u53F7\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"serialNumber\",children:\"\\u5E8F\\u5217\\u53F7\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"manufacturer\",children:\"\\u5236\\u9020\\u5546\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"installLocation\",children:\"\\u5B89\\u88C5\\u4F4D\\u7F6E\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"installDate\",children:\"\\u5B89\\u88C5\\u65E5\\u671F\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"warrantyEndDate\",children:\"\\u4FDD\\u4FEE\\u5230\\u671F\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"status\",children:\"\\u72B6\\u6001\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"responsiblePerson\",children:\"\\u8D23\\u4EFB\\u4EBA\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"lastMaintenanceDate\",children:\"\\u4E0A\\u6B21\\u7EF4\\u62A4\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"nextMaintenanceDate\",children:\"\\u4E0B\\u6B21\\u7EF4\\u62A4\"})})]})})]})]})]});};export default DeviceArchivePage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Row", "Col", "Tag", "Modal", "Form", "DatePicker", "Upload", "Image", "Descriptions", "Tabs", "Timeline", "<PERSON><PERSON>", "QRCode", "<PERSON><PERSON><PERSON>", "Divider", "Checkbox", "XLSX", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "QrcodeOutlined", "DownloadOutlined", "PrinterOutlined", "ToolOutlined", "HistoryOutlined", "CameraOutlined", "dayjs", "useAppDispatch", "useAppSelector", "fetchDeviceArchives", "formatDate", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Title", "Text", "Search", "TabPane", "DeviceArchivePage", "_selectedDevice$maint", "dispatch", "deviceArchives", "loading", "state", "service", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "deviceModalVisible", "setDeviceModalVisible", "qrModalVisible", "setQrModalVisible", "detailModalVisible", "setDetailModalVisible", "editingDevice", "setEditingDevice", "selected<PERSON><PERSON><PERSON>", "setSelectedDevice", "deviceForm", "useForm", "exportModalVisible", "setExportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "loadData", "keyword", "status", "handleAddDevice", "resetFields", "handleEditDevice", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectSpread", "installDate", "warrantyEndDate", "handleViewDevice", "handleShowQR", "handleDeviceModalOk", "values", "validateFields", "success", "title", "content", "error", "handleDeleteDevice", "handleExport", "executeExport", "exportData", "mockDevices", "map", "device", "data", "includes", "deviceCode", "deviceName", "deviceType", "model", "serialNumber", "manufacturer", "installLocation", "<PERSON><PERSON><PERSON>", "lastMaintenanceDate", "nextMaintenanceDate", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "fileName", "concat", "Date", "toISOString", "split", "writeFile", "id", "qrCode", "photos", "specifications", "frequency", "gain", "power", "weight", "maintenanceRecords", "date", "type", "description", "technician", "efficiency", "channels", "capacity", "deviceColumns", "dataIndex", "key", "width", "fixed", "render", "_", "children", "strong", "style", "fontSize", "color", "ellipsis", "isOverdue", "isBefore", "undefined", "size", "icon", "onClick", "danger", "confirm", "onOk", "justify", "align", "marginBottom", "level", "margin", "placeholder", "allowClear", "onSearch", "value", "onChange", "options", "label", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onCancel", "okText", "cancelText", "form", "layout", "gutter", "xs", "md", "<PERSON><PERSON>", "name", "rules", "required", "message", "Option", "initialValue", "listType", "maxCount", "beforeUpload", "marginTop", "footer", "defaultActiveKey", "tab", "bordered", "column", "span", "orientation", "Object", "entries", "_ref", "String", "length", "photo", "index", "height", "src", "fallback", "dot", "marginLeft", "showIcon", "textAlign", "window", "location", "origin", "display", "Group"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/service/DeviceArchivePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Row,\n  Col,\n  Tag,\n  Modal,\n  Form,\n  DatePicker,\n  Upload,\n  Image,\n  Descriptions,\n  Tabs,\n  Timeline,\n  Alert,\n  QRCode,\n  Tooltip,\n  Divider,\n  Checkbox,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  QrcodeOutlined,\n  UploadOutlined,\n  DownloadOutlined,\n  PrinterOutlined,\n  SearchOutlined,\n  ToolOutlined,\n  HistoryOutlined,\n  FileTextOutlined,\n  CameraOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchDeviceArchives } from '../../store/slices/serviceSlice';\nimport { formatDate } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { TabPane } = Tabs;\n\nconst DeviceArchivePage: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { deviceArchives, loading } = useAppSelector(state => state.service);\n\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [deviceModalVisible, setDeviceModalVisible] = useState(false);\n  const [qrModalVisible, setQrModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingDevice, setEditingDevice] = useState<any>(null);\n  const [selectedDevice, setSelectedDevice] = useState<any>(null);\n  const [deviceForm] = Form.useForm();\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['deviceCode', 'deviceName', 'deviceType', 'model', 'serialNumber', 'manufacturer', 'installLocation', 'status']);\n\n  useEffect(() => {\n    loadData();\n  }, [searchKeyword, statusFilter]);\n\n  const loadData = () => {\n    dispatch(fetchDeviceArchives({\n      keyword: searchKeyword,\n      status: statusFilter,\n    }));\n  };\n\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    deviceForm.resetFields();\n    setDeviceModalVisible(true);\n  };\n\n  const handleEditDevice = (record: any) => {\n    setEditingDevice(record);\n    deviceForm.setFieldsValue({\n      ...record,\n      installDate: record.installDate ? dayjs(record.installDate) : null,\n      warrantyEndDate: record.warrantyEndDate ? dayjs(record.warrantyEndDate) : null,\n    });\n    setDeviceModalVisible(true);\n  };\n\n  const handleViewDevice = (record: any) => {\n    setSelectedDevice(record);\n    setDetailModalVisible(true);\n  };\n\n  const handleShowQR = (record: any) => {\n    setSelectedDevice(record);\n    setQrModalVisible(true);\n  };\n\n  const handleDeviceModalOk = async () => {\n    try {\n      const values = await deviceForm.validateFields();\n\n      if (editingDevice) {\n        // TODO: 实现更新API\n        Modal.success({\n          title: '更新成功',\n          content: '设备档案已更新',\n        });\n      } else {\n        // TODO: 实现创建API\n        Modal.success({\n          title: '添加成功',\n          content: '设备档案已添加',\n        });\n      }\n\n      setDeviceModalVisible(false);\n      loadData();\n    } catch (error) {\n      Modal.error({\n        title: '操作失败',\n        content: '保存设备档案时发生错误',\n      });\n    }\n  };\n\n  const handleDeleteDevice = async (record: any) => {\n    try {\n      // TODO: 实现删除API\n      Modal.success({\n        title: '删除成功',\n        content: '设备档案已删除',\n      });\n      loadData();\n    } catch (error) {\n      Modal.error({\n        title: '删除失败',\n        content: '删除设备档案时发生错误',\n      });\n    }\n  };\n\n  // 处理导出\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const executeExport = () => {\n    try {\n      // 准备导出数据\n      const exportData = mockDevices.map(device => {\n        const data: any = {};\n        \n        if (exportFields.includes('deviceCode')) data['设备编码'] = device.deviceCode;\n        if (exportFields.includes('deviceName')) data['设备名称'] = device.deviceName;\n        if (exportFields.includes('deviceType')) data['设备类型'] = device.deviceType;\n        if (exportFields.includes('model')) data['设备型号'] = device.model;\n        if (exportFields.includes('serialNumber')) data['序列号'] = device.serialNumber;\n        if (exportFields.includes('manufacturer')) data['制造商'] = device.manufacturer;\n        if (exportFields.includes('installLocation')) data['安装位置'] = device.installLocation;\n        if (exportFields.includes('installDate')) data['安装日期'] = formatDate(device.installDate);\n        if (exportFields.includes('warrantyEndDate')) data['保修到期'] = formatDate(device.warrantyEndDate);\n        if (exportFields.includes('status')) data['状态'] = device.status;\n        if (exportFields.includes('responsiblePerson')) data['责任人'] = device.responsiblePerson;\n        if (exportFields.includes('lastMaintenanceDate')) data['上次维护'] = formatDate(device.lastMaintenanceDate);\n        if (exportFields.includes('nextMaintenanceDate')) data['下次维护'] = formatDate(device.nextMaintenanceDate);\n        \n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, '设备档案');\n\n      // 下载文件\n      const fileName = `设备档案_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      \n      Modal.success({\n        title: '导出成功',\n        content: '设备档案数据已导出',\n      });\n      setExportModalVisible(false);\n    } catch (error) {\n      Modal.error({\n        title: '导出失败',\n        content: '导出设备档案数据时发生错误',\n      });\n    }\n  };\n\n  // 模拟设备档案数据\n  const mockDevices = [\n    {\n      id: '1',\n      deviceCode: 'DEV-ANT-001',\n      deviceName: '5G基站天线系统',\n      deviceType: '天线设备',\n      model: 'ANT-5G-V2.0',\n      serialNumber: 'SN20240001',\n      manufacturer: '华为技术有限公司',\n      installLocation: '北京市朝阳区CBD基站',\n      installDate: '2024-01-15T00:00:00Z',\n      warrantyEndDate: '2027-01-15T00:00:00Z',\n      status: '运行中',\n      responsiblePerson: 'service_tech',\n      lastMaintenanceDate: '2024-03-01T00:00:00Z',\n      nextMaintenanceDate: '2024-06-01T00:00:00Z',\n      qrCode: 'QR-DEV-ANT-001',\n      photos: ['/api/photos/device1.jpg'],\n      specifications: {\n        frequency: '3.5GHz',\n        gain: '18dBi',\n        power: '200W',\n        weight: '25kg',\n      },\n      maintenanceRecords: [\n        {\n          id: '1',\n          date: '2024-03-01T00:00:00Z',\n          type: '定期保养',\n          description: '清洁天线表面，检查连接器',\n          technician: 'service_tech',\n          status: '已完成',\n        },\n        {\n          id: '2',\n          date: '2024-01-15T00:00:00Z',\n          type: '安装调试',\n          description: '设备安装及初始调试',\n          technician: 'service_tech',\n          status: '已完成',\n        },\n      ],\n    },\n    {\n      id: '2',\n      deviceCode: 'DEV-RF-002',\n      deviceName: 'RF功率放大器',\n      deviceType: 'RF设备',\n      model: 'RF-AMP-100W',\n      serialNumber: 'SN20240002',\n      manufacturer: '中兴通讯股份有限公司',\n      installLocation: '上海市浦东新区陆家嘴基站',\n      installDate: '2024-02-01T00:00:00Z',\n      warrantyEndDate: '2027-02-01T00:00:00Z',\n      status: '维护中',\n      responsiblePerson: 'service_tech',\n      lastMaintenanceDate: '2024-03-15T00:00:00Z',\n      nextMaintenanceDate: '2024-06-15T00:00:00Z',\n      qrCode: 'QR-DEV-RF-002',\n      photos: ['/api/photos/device2.jpg'],\n      specifications: {\n        frequency: '2.6GHz',\n        power: '100W',\n        efficiency: '45%',\n        weight: '15kg',\n      },\n      maintenanceRecords: [\n        {\n          id: '1',\n          date: '2024-03-15T00:00:00Z',\n          type: '故障维修',\n          description: '功率输出异常，更换功率模块',\n          technician: 'service_tech',\n          status: '进行中',\n        },\n      ],\n    },\n    {\n      id: '3',\n      deviceCode: 'DEV-CTL-003',\n      deviceName: '基站控制器',\n      deviceType: '控制设备',\n      model: 'BSC-V3.0',\n      serialNumber: 'SN20240003',\n      manufacturer: '大唐移动通信设备有限公司',\n      installLocation: '广州市天河区珠江新城基站',\n      installDate: '2024-01-20T00:00:00Z',\n      warrantyEndDate: '2027-01-20T00:00:00Z',\n      status: '运行中',\n      responsiblePerson: 'service_tech',\n      lastMaintenanceDate: '2024-02-20T00:00:00Z',\n      nextMaintenanceDate: '2024-05-20T00:00:00Z',\n      qrCode: 'QR-DEV-CTL-003',\n      photos: ['/api/photos/device3.jpg'],\n      specifications: {\n        channels: '64',\n        capacity: '1000用户',\n        power: '500W',\n        weight: '50kg',\n      },\n      maintenanceRecords: [\n        {\n          id: '1',\n          date: '2024-02-20T00:00:00Z',\n          type: '定期保养',\n          description: '系统软件更新，硬件检查',\n          technician: 'service_tech',\n          status: '已完成',\n        },\n      ],\n    },\n  ];\n\n  const deviceColumns = [\n    {\n      title: '设备编码',\n      dataIndex: 'deviceCode',\n      key: 'deviceCode',\n      width: 120,\n      fixed: 'left' as const,\n    },\n    {\n      title: '设备信息',\n      key: 'deviceInfo',\n      width: 200,\n      render: (_: any, record: any) => (\n        <div>\n          <Text strong>{record.deviceName}</Text>\n          <br />\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.model}\n          </Text>\n          <br />\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            SN: {record.serialNumber}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '设备类型',\n      dataIndex: 'deviceType',\n      key: 'deviceType',\n      width: 100,\n      render: (type: string) => (\n        <Tag color={type === '天线设备' ? 'blue' : type === 'RF设备' ? 'green' : 'orange'}>\n          {type}\n        </Tag>\n      ),\n    },\n    {\n      title: '制造商',\n      dataIndex: 'manufacturer',\n      key: 'manufacturer',\n      ellipsis: true,\n    },\n    {\n      title: '安装位置',\n      dataIndex: 'installLocation',\n      key: 'installLocation',\n      ellipsis: true,\n    },\n    {\n      title: '安装日期',\n      dataIndex: 'installDate',\n      key: 'installDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => {\n        const color = status === '运行中' ? 'green' :\n                     status === '维护中' ? 'orange' :\n                     status === '故障' ? 'red' : 'default';\n        return <Tag color={color}>{status}</Tag>;\n      },\n    },\n    {\n      title: '下次维护',\n      dataIndex: 'nextMaintenanceDate',\n      key: 'nextMaintenanceDate',\n      width: 100,\n      render: (date: string) => {\n        const isOverdue = dayjs(date).isBefore(dayjs());\n        return (\n          <Text style={{ color: isOverdue ? '#ff4d4f' : undefined }}>\n            {formatDate(date)}\n          </Text>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right' as const,\n      render: (_: any, record: any) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewDevice(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"二维码\">\n            <Button\n              type=\"text\"\n              icon={<QrcodeOutlined />}\n              onClick={() => handleShowQR(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditDevice(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => {\n                Modal.confirm({\n                  title: '确认删除',\n                  content: '确定要删除这个设备档案吗？',\n                  onOk: () => handleDeleteDevice(record),\n                });\n              }}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              设备档案\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索设备编码、名称\"\n                allowClear\n                style={{ width: 200 }}\n                onSearch={setSearchKeyword}\n              />\n              <Select\n                placeholder=\"设备状态\"\n                allowClear\n                style={{ width: 120 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '运行中', value: '运行中' },\n                  { label: '维护中', value: '维护中' },\n                  { label: '故障', value: '故障' },\n                  { label: '停用', value: '停用' },\n                ]}\n              />\n              <Button icon={<DownloadOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddDevice}>\n                新增设备\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Table\n          columns={deviceColumns}\n          dataSource={mockDevices}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 设备档案模态框 */}\n      <Modal\n        title={editingDevice ? '编辑设备档案' : '新增设备档案'}\n        open={deviceModalVisible}\n        onOk={handleDeviceModalOk}\n        onCancel={() => setDeviceModalVisible(false)}\n        width={800}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form form={deviceForm} layout=\"vertical\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"deviceCode\"\n                label=\"设备编码\"\n                rules={[{ required: true, message: '请输入设备编码' }]}\n              >\n                <Input placeholder=\"请输入设备编码\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"deviceName\"\n                label=\"设备名称\"\n                rules={[{ required: true, message: '请输入设备名称' }]}\n              >\n                <Input placeholder=\"请输入设备名称\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"deviceType\"\n                label=\"设备类型\"\n                rules={[{ required: true, message: '请选择设备类型' }]}\n              >\n                <Select placeholder=\"请选择设备类型\">\n                  <Select.Option value=\"天线设备\">天线设备</Select.Option>\n                  <Select.Option value=\"RF设备\">RF设备</Select.Option>\n                  <Select.Option value=\"控制设备\">控制设备</Select.Option>\n                  <Select.Option value=\"传输设备\">传输设备</Select.Option>\n                  <Select.Option value=\"电源设备\">电源设备</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"model\"\n                label=\"设备型号\"\n                rules={[{ required: true, message: '请输入设备型号' }]}\n              >\n                <Input placeholder=\"请输入设备型号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"serialNumber\"\n                label=\"序列号\"\n                rules={[{ required: true, message: '请输入序列号' }]}\n              >\n                <Input placeholder=\"请输入序列号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"manufacturer\"\n                label=\"制造商\"\n                rules={[{ required: true, message: '请输入制造商' }]}\n              >\n                <Input placeholder=\"请输入制造商\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24}>\n              <Form.Item\n                name=\"installLocation\"\n                label=\"安装位置\"\n                rules={[{ required: true, message: '请输入安装位置' }]}\n              >\n                <Input placeholder=\"请输入安装位置\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"installDate\"\n                label=\"安装日期\"\n                rules={[{ required: true, message: '请选择安装日期' }]}\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"warrantyEndDate\"\n                label=\"保修到期日期\"\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"设备状态\"\n                initialValue=\"运行中\"\n              >\n                <Select>\n                  <Select.Option value=\"运行中\">运行中</Select.Option>\n                  <Select.Option value=\"维护中\">维护中</Select.Option>\n                  <Select.Option value=\"故障\">故障</Select.Option>\n                  <Select.Option value=\"停用\">停用</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"responsiblePerson\"\n                label=\"责任人\"\n                rules={[{ required: true, message: '请输入责任人' }]}\n              >\n                <Input placeholder=\"请输入责任人\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24}>\n              <Form.Item name=\"photos\" label=\"设备照片\">\n                <Upload\n                  listType=\"picture-card\"\n                  maxCount={5}\n                  beforeUpload={() => false}\n                >\n                  <div>\n                    <CameraOutlined />\n                    <div style={{ marginTop: 8 }}>上传照片</div>\n                  </div>\n                </Upload>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n\n      {/* 设备详情模态框 */}\n      <Modal\n        title=\"设备详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        width={1000}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"print\" icon={<PrinterOutlined />}>\n            打印\n          </Button>,\n          <Button key=\"qr\" icon={<QrcodeOutlined />} onClick={() => handleShowQR(selectedDevice)}>\n            二维码\n          </Button>,\n        ]}\n      >\n        {selectedDevice && (\n          <Tabs defaultActiveKey=\"basic\">\n            <TabPane tab=\"基本信息\" key=\"basic\">\n              <Descriptions bordered column={2}>\n                <Descriptions.Item label=\"设备编码\">{selectedDevice.deviceCode}</Descriptions.Item>\n                <Descriptions.Item label=\"设备名称\">{selectedDevice.deviceName}</Descriptions.Item>\n                <Descriptions.Item label=\"设备类型\">{selectedDevice.deviceType}</Descriptions.Item>\n                <Descriptions.Item label=\"设备型号\">{selectedDevice.model}</Descriptions.Item>\n                <Descriptions.Item label=\"序列号\">{selectedDevice.serialNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"制造商\">{selectedDevice.manufacturer}</Descriptions.Item>\n                <Descriptions.Item label=\"安装位置\" span={2}>{selectedDevice.installLocation}</Descriptions.Item>\n                <Descriptions.Item label=\"安装日期\">{formatDate(selectedDevice.installDate)}</Descriptions.Item>\n                <Descriptions.Item label=\"保修到期\">{formatDate(selectedDevice.warrantyEndDate)}</Descriptions.Item>\n                <Descriptions.Item label=\"设备状态\">\n                  <Tag color={selectedDevice.status === '运行中' ? 'green' : 'orange'}>\n                    {selectedDevice.status}\n                  </Tag>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"责任人\">{selectedDevice.responsiblePerson}</Descriptions.Item>\n              </Descriptions>\n\n              <Divider orientation=\"left\">技术规格</Divider>\n              <Descriptions bordered column={2}>\n                {Object.entries(selectedDevice.specifications || {}).map(([key, value]) => (\n                  <Descriptions.Item key={key} label={key}>\n                    {String(value)}\n                  </Descriptions.Item>\n                ))}\n              </Descriptions>\n\n              {selectedDevice.photos && selectedDevice.photos.length > 0 && (\n                <>\n                  <Divider orientation=\"left\">设备照片</Divider>\n                  <Space>\n                    {selectedDevice.photos.map((photo: string, index: number) => (\n                      <Image\n                        key={index}\n                        width={100}\n                        height={100}\n                        src={photo}\n                        fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n                      />\n                    ))}\n                  </Space>\n                </>\n              )}\n            </TabPane>\n            <TabPane tab=\"维护记录\" key=\"maintenance\">\n              <Timeline>\n                {selectedDevice.maintenanceRecords?.map((record: any) => (\n                  <Timeline.Item\n                    key={record.id}\n                    color={record.status === '已完成' ? 'green' : 'blue'}\n                    dot={record.status === '已完成' ? <ToolOutlined /> : <HistoryOutlined />}\n                  >\n                    <div>\n                      <Text strong>{record.type}</Text>\n                      <Text type=\"secondary\" style={{ marginLeft: 8 }}>\n                        {formatDate(record.date)}\n                      </Text>\n                    </div>\n                    <div style={{ marginTop: 4 }}>\n                      <Text>{record.description}</Text>\n                    </div>\n                    <div style={{ marginTop: 4 }}>\n                      <Text type=\"secondary\">技术员: {record.technician}</Text>\n                      <Tag\n                        color={record.status === '已完成' ? 'green' : 'processing'}\n                        style={{ marginLeft: 8 }}\n                      >\n                        {record.status}\n                      </Tag>\n                    </div>\n                  </Timeline.Item>\n                ))}\n              </Timeline>\n            </TabPane>\n            <TabPane tab=\"服务BOM\" key=\"serviceBom\">\n              <Alert\n                message=\"服务BOM\"\n                description=\"显示该设备的服务BOM信息，包括备件清单、维护工具等\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n              <Text type=\"secondary\">服务BOM功能正在开发中...</Text>\n            </TabPane>\n          </Tabs>\n        )}\n      </Modal>\n\n      {/* 二维码模态框 */}\n      <Modal\n        title=\"设备二维码\"\n        open={qrModalVisible}\n        onCancel={() => setQrModalVisible(false)}\n        width={400}\n        footer={[\n          <Button key=\"close\" onClick={() => setQrModalVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"download\" type=\"primary\" icon={<DownloadOutlined />}>\n            下载\n          </Button>,\n          <Button key=\"print\" icon={<PrinterOutlined />}>\n            打印\n          </Button>,\n        ]}\n      >\n        {selectedDevice && (\n          <div style={{ textAlign: 'center' }}>\n            <QRCode\n              value={`${window.location.origin}/device/${selectedDevice.id}`}\n              size={200}\n            />\n            <div style={{ marginTop: 16 }}>\n              <Text strong>{selectedDevice.deviceCode}</Text>\n              <br />\n              <Text type=\"secondary\">{selectedDevice.deviceName}</Text>\n            </div>\n          </div>\n        )}\n      </Modal>\n\n      {/* 导出模态框 */}\n        <Modal\n          title=\"导出设备档案\"\n          open={exportModalVisible}\n          onOk={executeExport}\n          onCancel={() => setExportModalVisible(false)}\n          width={600}\n        >\n          <div style={{ marginBottom: 16 }}>\n            <label style={{ display: 'block', marginBottom: 8 }}>导出格式：</label>\n            <Select\n              value={exportFormat}\n              onChange={setExportFormat}\n              style={{ width: '100%' }}\n            >\n              <Select.Option value=\"excel\">Excel (.xlsx)</Select.Option>\n              <Select.Option value=\"csv\">CSV (.csv)</Select.Option>\n            </Select>\n          </div>\n          \n          <div>\n            <label style={{ display: 'block', marginBottom: 8 }}>选择导出字段：</label>\n            <Checkbox.Group\n              value={exportFields}\n              onChange={setExportFields}\n              style={{ width: '100%' }}\n            >\n              <Row gutter={[16, 8]}>\n                <Col span={12}>\n                  <Checkbox value=\"deviceCode\">设备编码</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"deviceName\">设备名称</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"deviceType\">设备类型</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"model\">设备型号</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"serialNumber\">序列号</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"manufacturer\">制造商</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"installLocation\">安装位置</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"installDate\">安装日期</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"warrantyEndDate\">保修到期</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"status\">状态</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"responsiblePerson\">责任人</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"lastMaintenanceDate\">上次维护</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"nextMaintenanceDate\">下次维护</Checkbox>\n                </Col>\n              </Row>\n            </Checkbox.Group>\n          </div>\n        </Modal>\n    </div>\n  );\n};\n\nexport default DeviceArchivePage;\n"], "mappings": "wHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,GAAG,CACHC,GAAG,CACHC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,YAAY,CACZC,IAAI,CACJC,QAAQ,CACRC,KAAK,CACLC,MAAM,CACNC,OAAO,CACPC,OAAO,CACPC,QAAQ,KACH,MAAM,CACb,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,OACEC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,cAAc,CAEdC,gBAAgB,CAChBC,eAAe,CAEfC,YAAY,CACZC,eAAe,CAEfC,cAAc,KACT,mBAAmB,CAC1B,MAAO,CAAAC,KAAK,KAAM,OAAO,CAEzB,OAASC,cAAc,CAAEC,cAAc,KAAQ,mBAAmB,CAClE,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,UAAU,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEzC,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG7C,UAAU,CAClC,KAAM,CAAE8C,MAAO,CAAC,CAAG1C,KAAK,CACxB,KAAM,CAAE2C,OAAQ,CAAC,CAAGhC,IAAI,CAExB,KAAM,CAAAiC,iBAA2B,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CACxC,KAAM,CAAAC,QAAQ,CAAGhB,cAAc,CAAC,CAAC,CACjC,KAAM,CAAEiB,cAAc,CAAEC,OAAQ,CAAC,CAAGjB,cAAc,CAACkB,KAAK,EAAIA,KAAK,CAACC,OAAO,CAAC,CAE1E,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC4D,YAAY,CAAEC,eAAe,CAAC,CAAG7D,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAAC8D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG/D,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACgE,cAAc,CAAEC,iBAAiB,CAAC,CAAGjE,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACkE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACoE,aAAa,CAAEC,gBAAgB,CAAC,CAAGrE,QAAQ,CAAM,IAAI,CAAC,CAC7D,KAAM,CAACsE,cAAc,CAAEC,iBAAiB,CAAC,CAAGvE,QAAQ,CAAM,IAAI,CAAC,CAC/D,KAAM,CAACwE,UAAU,CAAC,CAAG3D,IAAI,CAAC4D,OAAO,CAAC,CAAC,CACnC,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3E,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC4E,YAAY,CAAEC,eAAe,CAAC,CAAG7E,QAAQ,CAAkB,OAAO,CAAC,CAC1E,KAAM,CAAC8E,YAAY,CAAEC,eAAe,CAAC,CAAG/E,QAAQ,CAAW,CAAC,YAAY,CAAE,YAAY,CAAE,YAAY,CAAE,OAAO,CAAE,cAAc,CAAE,cAAc,CAAE,iBAAiB,CAAE,QAAQ,CAAC,CAAC,CAE5KC,SAAS,CAAC,IAAM,CACd+E,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACtB,aAAa,CAAEE,YAAY,CAAC,CAAC,CAEjC,KAAM,CAAAoB,QAAQ,CAAGA,CAAA,GAAM,CACrB3B,QAAQ,CAACd,mBAAmB,CAAC,CAC3B0C,OAAO,CAAEvB,aAAa,CACtBwB,MAAM,CAAEtB,YACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAuB,eAAe,CAAGA,CAAA,GAAM,CAC5Bd,gBAAgB,CAAC,IAAI,CAAC,CACtBG,UAAU,CAACY,WAAW,CAAC,CAAC,CACxBrB,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAsB,gBAAgB,CAAIC,MAAW,EAAK,CACxCjB,gBAAgB,CAACiB,MAAM,CAAC,CACxBd,UAAU,CAACe,cAAc,CAAAC,aAAA,CAAAA,aAAA,IACpBF,MAAM,MACTG,WAAW,CAAEH,MAAM,CAACG,WAAW,CAAGrD,KAAK,CAACkD,MAAM,CAACG,WAAW,CAAC,CAAG,IAAI,CAClEC,eAAe,CAAEJ,MAAM,CAACI,eAAe,CAAGtD,KAAK,CAACkD,MAAM,CAACI,eAAe,CAAC,CAAG,IAAI,EAC/E,CAAC,CACF3B,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA4B,gBAAgB,CAAIL,MAAW,EAAK,CACxCf,iBAAiB,CAACe,MAAM,CAAC,CACzBnB,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAyB,YAAY,CAAIN,MAAW,EAAK,CACpCf,iBAAiB,CAACe,MAAM,CAAC,CACzBrB,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAA4B,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAtB,UAAU,CAACuB,cAAc,CAAC,CAAC,CAEhD,GAAI3B,aAAa,CAAE,CACjB;AACAxD,KAAK,CAACoF,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,SACX,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACAtF,KAAK,CAACoF,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,SACX,CAAC,CAAC,CACJ,CAEAnC,qBAAqB,CAAC,KAAK,CAAC,CAC5BiB,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOmB,KAAK,CAAE,CACdvF,KAAK,CAACuF,KAAK,CAAC,CACVF,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,aACX,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAG,KAAO,CAAAd,MAAW,EAAK,CAChD,GAAI,CACF;AACA1E,KAAK,CAACoF,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,SACX,CAAC,CAAC,CACFlB,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOmB,KAAK,CAAE,CACdvF,KAAK,CAACuF,KAAK,CAAC,CACVF,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,aACX,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAG,YAAY,CAAGA,CAAA,GAAM,CACzB1B,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA2B,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAGC,WAAW,CAACC,GAAG,CAACC,MAAM,EAAI,CAC3C,KAAM,CAAAC,IAAS,CAAG,CAAC,CAAC,CAEpB,GAAI7B,YAAY,CAAC8B,QAAQ,CAAC,YAAY,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,MAAM,CAACG,UAAU,CACzE,GAAI/B,YAAY,CAAC8B,QAAQ,CAAC,YAAY,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,MAAM,CAACI,UAAU,CACzE,GAAIhC,YAAY,CAAC8B,QAAQ,CAAC,YAAY,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,MAAM,CAACK,UAAU,CACzE,GAAIjC,YAAY,CAAC8B,QAAQ,CAAC,OAAO,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,MAAM,CAACM,KAAK,CAC/D,GAAIlC,YAAY,CAAC8B,QAAQ,CAAC,cAAc,CAAC,CAAED,IAAI,CAAC,KAAK,CAAC,CAAGD,MAAM,CAACO,YAAY,CAC5E,GAAInC,YAAY,CAAC8B,QAAQ,CAAC,cAAc,CAAC,CAAED,IAAI,CAAC,KAAK,CAAC,CAAGD,MAAM,CAACQ,YAAY,CAC5E,GAAIpC,YAAY,CAAC8B,QAAQ,CAAC,iBAAiB,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,MAAM,CAACS,eAAe,CACnF,GAAIrC,YAAY,CAAC8B,QAAQ,CAAC,aAAa,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGnE,UAAU,CAACkE,MAAM,CAACjB,WAAW,CAAC,CACvF,GAAIX,YAAY,CAAC8B,QAAQ,CAAC,iBAAiB,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGnE,UAAU,CAACkE,MAAM,CAAChB,eAAe,CAAC,CAC/F,GAAIZ,YAAY,CAAC8B,QAAQ,CAAC,QAAQ,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,MAAM,CAACxB,MAAM,CAC/D,GAAIJ,YAAY,CAAC8B,QAAQ,CAAC,mBAAmB,CAAC,CAAED,IAAI,CAAC,KAAK,CAAC,CAAGD,MAAM,CAACU,iBAAiB,CACtF,GAAItC,YAAY,CAAC8B,QAAQ,CAAC,qBAAqB,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGnE,UAAU,CAACkE,MAAM,CAACW,mBAAmB,CAAC,CACvG,GAAIvC,YAAY,CAAC8B,QAAQ,CAAC,qBAAqB,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGnE,UAAU,CAACkE,MAAM,CAACY,mBAAmB,CAAC,CAEvG,MAAO,CAAAX,IAAI,CACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAAY,EAAE,CAAG9F,IAAI,CAAC+F,KAAK,CAACC,aAAa,CAAClB,UAAU,CAAC,CAC/C,KAAM,CAAAmB,EAAE,CAAGjG,IAAI,CAAC+F,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChClG,IAAI,CAAC+F,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,MAAM,CAAC,CAE5C;AACA,KAAM,CAAAM,QAAQ,6BAAAC,MAAA,CAAW,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAAH,MAAA,CAAIlD,YAAY,GAAK,OAAO,CAAG,MAAM,CAAG,KAAK,CAAE,CAC9GnD,IAAI,CAACyG,SAAS,CAACR,EAAE,CAAEG,QAAQ,CAAC,CAE5BjH,KAAK,CAACoF,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,WACX,CAAC,CAAC,CACFvB,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,MAAOwB,KAAK,CAAE,CACdvF,KAAK,CAACuF,KAAK,CAAC,CACVF,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,eACX,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAM,WAAW,CAAG,CAClB,CACE2B,EAAE,CAAE,GAAG,CACPtB,UAAU,CAAE,aAAa,CACzBC,UAAU,CAAE,UAAU,CACtBC,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,aAAa,CACpBC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,UAAU,CACxBC,eAAe,CAAE,aAAa,CAC9B1B,WAAW,CAAE,sBAAsB,CACnCC,eAAe,CAAE,sBAAsB,CACvCR,MAAM,CAAE,KAAK,CACbkC,iBAAiB,CAAE,cAAc,CACjCC,mBAAmB,CAAE,sBAAsB,CAC3CC,mBAAmB,CAAE,sBAAsB,CAC3Cc,MAAM,CAAE,gBAAgB,CACxBC,MAAM,CAAE,CAAC,yBAAyB,CAAC,CACnCC,cAAc,CAAE,CACdC,SAAS,CAAE,QAAQ,CACnBC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MACV,CAAC,CACDC,kBAAkB,CAAE,CAClB,CACER,EAAE,CAAE,GAAG,CACPS,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,MAAM,CACZC,WAAW,CAAE,cAAc,CAC3BC,UAAU,CAAE,cAAc,CAC1B7D,MAAM,CAAE,KACV,CAAC,CACD,CACEiD,EAAE,CAAE,GAAG,CACPS,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,MAAM,CACZC,WAAW,CAAE,WAAW,CACxBC,UAAU,CAAE,cAAc,CAC1B7D,MAAM,CAAE,KACV,CAAC,CAEL,CAAC,CACD,CACEiD,EAAE,CAAE,GAAG,CACPtB,UAAU,CAAE,YAAY,CACxBC,UAAU,CAAE,SAAS,CACrBC,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,aAAa,CACpBC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,YAAY,CAC1BC,eAAe,CAAE,cAAc,CAC/B1B,WAAW,CAAE,sBAAsB,CACnCC,eAAe,CAAE,sBAAsB,CACvCR,MAAM,CAAE,KAAK,CACbkC,iBAAiB,CAAE,cAAc,CACjCC,mBAAmB,CAAE,sBAAsB,CAC3CC,mBAAmB,CAAE,sBAAsB,CAC3Cc,MAAM,CAAE,eAAe,CACvBC,MAAM,CAAE,CAAC,yBAAyB,CAAC,CACnCC,cAAc,CAAE,CACdC,SAAS,CAAE,QAAQ,CACnBE,KAAK,CAAE,MAAM,CACbO,UAAU,CAAE,KAAK,CACjBN,MAAM,CAAE,MACV,CAAC,CACDC,kBAAkB,CAAE,CAClB,CACER,EAAE,CAAE,GAAG,CACPS,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,MAAM,CACZC,WAAW,CAAE,eAAe,CAC5BC,UAAU,CAAE,cAAc,CAC1B7D,MAAM,CAAE,KACV,CAAC,CAEL,CAAC,CACD,CACEiD,EAAE,CAAE,GAAG,CACPtB,UAAU,CAAE,aAAa,CACzBC,UAAU,CAAE,OAAO,CACnBC,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,UAAU,CACjBC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,cAAc,CAC5BC,eAAe,CAAE,cAAc,CAC/B1B,WAAW,CAAE,sBAAsB,CACnCC,eAAe,CAAE,sBAAsB,CACvCR,MAAM,CAAE,KAAK,CACbkC,iBAAiB,CAAE,cAAc,CACjCC,mBAAmB,CAAE,sBAAsB,CAC3CC,mBAAmB,CAAE,sBAAsB,CAC3Cc,MAAM,CAAE,gBAAgB,CACxBC,MAAM,CAAE,CAAC,yBAAyB,CAAC,CACnCC,cAAc,CAAE,CACdW,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,QAAQ,CAClBT,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MACV,CAAC,CACDC,kBAAkB,CAAE,CAClB,CACER,EAAE,CAAE,GAAG,CACPS,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,MAAM,CACZC,WAAW,CAAE,aAAa,CAC1BC,UAAU,CAAE,cAAc,CAC1B7D,MAAM,CAAE,KACV,CAAC,CAEL,CAAC,CACF,CAED,KAAM,CAAAiE,aAAa,CAAG,CACpB,CACElD,KAAK,CAAE,MAAM,CACbmD,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,MACT,CAAC,CACD,CACEtD,KAAK,CAAE,MAAM,CACboD,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACC,CAAM,CAAEnE,MAAW,gBAC1B1C,KAAA,QAAA8G,QAAA,eACEhH,IAAA,CAACM,IAAI,EAAC2G,MAAM,MAAAD,QAAA,CAAEpE,MAAM,CAACwB,UAAU,CAAO,CAAC,cACvCpE,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAC6F,IAAI,CAAC,WAAW,CAACe,KAAK,CAAE,CAAEC,QAAQ,CAAE,EAAG,CAAE,CAAAH,QAAA,CAC5CpE,MAAM,CAAC0B,KAAK,CACT,CAAC,cACPtE,IAAA,QAAK,CAAC,cACNE,KAAA,CAACI,IAAI,EAAC6F,IAAI,CAAC,WAAW,CAACe,KAAK,CAAE,CAAEC,QAAQ,CAAE,EAAG,CAAE,CAAAH,QAAA,EAAC,MAC1C,CAACpE,MAAM,CAAC2B,YAAY,EACpB,CAAC,EACJ,CAET,CAAC,CACD,CACEhB,KAAK,CAAE,MAAM,CACbmD,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGX,IAAY,eACnBnG,IAAA,CAAC/B,GAAG,EAACmJ,KAAK,CAAEjB,IAAI,GAAK,MAAM,CAAG,MAAM,CAAGA,IAAI,GAAK,MAAM,CAAG,OAAO,CAAG,QAAS,CAAAa,QAAA,CACzEb,IAAI,CACF,CAET,CAAC,CACD,CACE5C,KAAK,CAAE,KAAK,CACZmD,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBU,QAAQ,CAAE,IACZ,CAAC,CACD,CACE9D,KAAK,CAAE,MAAM,CACbmD,SAAS,CAAE,iBAAiB,CAC5BC,GAAG,CAAE,iBAAiB,CACtBU,QAAQ,CAAE,IACZ,CAAC,CACD,CACE9D,KAAK,CAAE,MAAM,CACbmD,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGZ,IAAY,EAAKpG,UAAU,CAACoG,IAAI,CAC3C,CAAC,CACD,CACE3C,KAAK,CAAE,IAAI,CACXmD,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGtE,MAAc,EAAK,CAC1B,KAAM,CAAA4E,KAAK,CAAG5E,MAAM,GAAK,KAAK,CAAG,OAAO,CAC3BA,MAAM,GAAK,KAAK,CAAG,QAAQ,CAC3BA,MAAM,GAAK,IAAI,CAAG,KAAK,CAAG,SAAS,CAChD,mBAAOxC,IAAA,CAAC/B,GAAG,EAACmJ,KAAK,CAAEA,KAAM,CAAAJ,QAAA,CAAExE,MAAM,CAAM,CAAC,CAC1C,CACF,CAAC,CACD,CACEe,KAAK,CAAE,MAAM,CACbmD,SAAS,CAAE,qBAAqB,CAChCC,GAAG,CAAE,qBAAqB,CAC1BC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGZ,IAAY,EAAK,CACxB,KAAM,CAAAoB,SAAS,CAAG5H,KAAK,CAACwG,IAAI,CAAC,CAACqB,QAAQ,CAAC7H,KAAK,CAAC,CAAC,CAAC,CAC/C,mBACEM,IAAA,CAACM,IAAI,EAAC4G,KAAK,CAAE,CAAEE,KAAK,CAAEE,SAAS,CAAG,SAAS,CAAGE,SAAU,CAAE,CAAAR,QAAA,CACvDlH,UAAU,CAACoG,IAAI,CAAC,CACb,CAAC,CAEX,CACF,CAAC,CACD,CACE3C,KAAK,CAAE,IAAI,CACXoD,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,OAAgB,CACvBC,MAAM,CAAEA,CAACC,CAAM,CAAEnE,MAAW,gBAC1B1C,KAAA,CAACtC,KAAK,EAAC6J,IAAI,CAAC,OAAO,CAAAT,QAAA,eACjBhH,IAAA,CAACpB,OAAO,EAAC2E,KAAK,CAAC,0BAAM,CAAAyD,QAAA,cACnBhH,IAAA,CAACrC,MAAM,EACLwI,IAAI,CAAC,MAAM,CACXuB,IAAI,cAAE1H,IAAA,CAACb,WAAW,GAAE,CAAE,CACtBwI,OAAO,CAAEA,CAAA,GAAM1E,gBAAgB,CAACL,MAAM,CAAE,CACzC,CAAC,CACK,CAAC,cACV5C,IAAA,CAACpB,OAAO,EAAC2E,KAAK,CAAC,oBAAK,CAAAyD,QAAA,cAClBhH,IAAA,CAACrC,MAAM,EACLwI,IAAI,CAAC,MAAM,CACXuB,IAAI,cAAE1H,IAAA,CAACZ,cAAc,GAAE,CAAE,CACzBuI,OAAO,CAAEA,CAAA,GAAMzE,YAAY,CAACN,MAAM,CAAE,CACrC,CAAC,CACK,CAAC,cACV5C,IAAA,CAACpB,OAAO,EAAC2E,KAAK,CAAC,cAAI,CAAAyD,QAAA,cACjBhH,IAAA,CAACrC,MAAM,EACLwI,IAAI,CAAC,MAAM,CACXuB,IAAI,cAAE1H,IAAA,CAACf,YAAY,GAAE,CAAE,CACvB0I,OAAO,CAAEA,CAAA,GAAMhF,gBAAgB,CAACC,MAAM,CAAE,CACzC,CAAC,CACK,CAAC,cACV5C,IAAA,CAACpB,OAAO,EAAC2E,KAAK,CAAC,cAAI,CAAAyD,QAAA,cACjBhH,IAAA,CAACrC,MAAM,EACLwI,IAAI,CAAC,MAAM,CACXyB,MAAM,MACNF,IAAI,cAAE1H,IAAA,CAACd,cAAc,GAAE,CAAE,CACzByI,OAAO,CAAEA,CAAA,GAAM,CACbzJ,KAAK,CAAC2J,OAAO,CAAC,CACZtE,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,eAAe,CACxBsE,IAAI,CAAEA,CAAA,GAAMpE,kBAAkB,CAACd,MAAM,CACvC,CAAC,CAAC,CACJ,CAAE,CACH,CAAC,CACK,CAAC,EACL,CAEX,CAAC,CACF,CAED,mBACE1C,KAAA,QAAA8G,QAAA,eACE9G,KAAA,CAAC1C,IAAI,EAAAwJ,QAAA,eACH9G,KAAA,CAACnC,GAAG,EAACgK,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACd,KAAK,CAAE,CAAEe,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,eACtEhH,IAAA,CAAChC,GAAG,EAAAgJ,QAAA,cACFhH,IAAA,CAACK,KAAK,EAAC6H,KAAK,CAAE,CAAE,CAAChB,KAAK,CAAE,CAAEiB,MAAM,CAAE,CAAE,CAAE,CAAAnB,QAAA,CAAC,0BAEvC,CAAO,CAAC,CACL,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAAgJ,QAAA,cACF9G,KAAA,CAACtC,KAAK,EAAAoJ,QAAA,eACJhH,IAAA,CAACO,MAAM,EACL6H,WAAW,CAAC,wDAAW,CACvBC,UAAU,MACVnB,KAAK,CAAE,CAAEN,KAAK,CAAE,GAAI,CAAE,CACtB0B,QAAQ,CAAErH,gBAAiB,CAC5B,CAAC,cACFjB,IAAA,CAAClC,MAAM,EACLsK,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVnB,KAAK,CAAE,CAAEN,KAAK,CAAE,GAAI,CAAE,CACtB2B,KAAK,CAAErH,YAAa,CACpBsH,QAAQ,CAAErH,eAAgB,CAC1BsH,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,KAAK,CAAEH,KAAK,CAAE,KAAM,CAAC,CAC9B,CAAEG,KAAK,CAAE,KAAK,CAAEH,KAAK,CAAE,KAAM,CAAC,CAC9B,CAAEG,KAAK,CAAE,IAAI,CAAEH,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAEG,KAAK,CAAE,IAAI,CAAEH,KAAK,CAAE,IAAK,CAAC,CAC5B,CACH,CAAC,cACFvI,IAAA,CAACrC,MAAM,EAAC+J,IAAI,cAAE1H,IAAA,CAACX,gBAAgB,GAAE,CAAE,CAACsI,OAAO,CAAEhE,YAAa,CAAAqD,QAAA,CAAC,cAE3D,CAAQ,CAAC,cACThH,IAAA,CAACrC,MAAM,EAACwI,IAAI,CAAC,SAAS,CAACuB,IAAI,cAAE1H,IAAA,CAAChB,YAAY,GAAE,CAAE,CAAC2I,OAAO,CAAElF,eAAgB,CAAAuE,QAAA,CAAC,0BAEzE,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAENhH,IAAA,CAACtC,KAAK,EACJiL,OAAO,CAAElC,aAAc,CACvBmC,UAAU,CAAE9E,WAAY,CACxBjD,OAAO,CAAEA,OAAQ,CACjBgI,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAAjE,MAAA,CACjBiE,KAAK,CAAC,CAAC,CAAC,MAAAjE,MAAA,CAAIiE,KAAK,CAAC,CAAC,CAAC,oBAAAjE,MAAA,CAAQgE,KAAK,WAC1C,CAAE,CACH,CAAC,EACE,CAAC,cAGPpJ,IAAA,CAAC9B,KAAK,EACJqF,KAAK,CAAE7B,aAAa,CAAG,QAAQ,CAAG,QAAS,CAC3C4H,IAAI,CAAElI,kBAAmB,CACzB0G,IAAI,CAAE3E,mBAAoB,CAC1BoG,QAAQ,CAAEA,CAAA,GAAMlI,qBAAqB,CAAC,KAAK,CAAE,CAC7CuF,KAAK,CAAE,GAAI,CACX4C,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAzC,QAAA,cAEfhH,IAAA,CAAC7B,IAAI,EAACuL,IAAI,CAAE5H,UAAW,CAAC6H,MAAM,CAAC,UAAU,CAAA3C,QAAA,cACvC9G,KAAA,CAACnC,GAAG,EAAC6L,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAA5C,QAAA,eACpBhH,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,YAAY,CACjBtB,KAAK,CAAC,0BAAM,CACZuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAnD,QAAA,cAEhDhH,IAAA,CAACnC,KAAK,EAACuK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNpI,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,YAAY,CACjBtB,KAAK,CAAC,0BAAM,CACZuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAnD,QAAA,cAEhDhH,IAAA,CAACnC,KAAK,EAACuK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNpI,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,YAAY,CACjBtB,KAAK,CAAC,0BAAM,CACZuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAnD,QAAA,cAEhD9G,KAAA,CAACpC,MAAM,EAACsK,WAAW,CAAC,4CAAS,CAAApB,QAAA,eAC3BhH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,0BAAM,CAAAvB,QAAA,CAAC,0BAAI,CAAe,CAAC,cAChDhH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,gBAAM,CAAAvB,QAAA,CAAC,gBAAI,CAAe,CAAC,cAChDhH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,0BAAM,CAAAvB,QAAA,CAAC,0BAAI,CAAe,CAAC,cAChDhH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,0BAAM,CAAAvB,QAAA,CAAC,0BAAI,CAAe,CAAC,cAChDhH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,0BAAM,CAAAvB,QAAA,CAAC,0BAAI,CAAe,CAAC,EAC1C,CAAC,CACA,CAAC,CACT,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,OAAO,CACZtB,KAAK,CAAC,0BAAM,CACZuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAnD,QAAA,cAEhDhH,IAAA,CAACnC,KAAK,EAACuK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNpI,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,cAAc,CACnBtB,KAAK,CAAC,oBAAK,CACXuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,QAAS,CAAC,CAAE,CAAAnD,QAAA,cAE/ChH,IAAA,CAACnC,KAAK,EAACuK,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACNpI,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,cAAc,CACnBtB,KAAK,CAAC,oBAAK,CACXuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,QAAS,CAAC,CAAE,CAAAnD,QAAA,cAE/ChH,IAAA,CAACnC,KAAK,EAACuK,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACNpI,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAAA7C,QAAA,cACVhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,iBAAiB,CACtBtB,KAAK,CAAC,0BAAM,CACZuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAnD,QAAA,cAEhDhH,IAAA,CAACnC,KAAK,EAACuK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNpI,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,aAAa,CAClBtB,KAAK,CAAC,0BAAM,CACZuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAnD,QAAA,cAEhDhH,IAAA,CAAC5B,UAAU,EAAC8I,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAC/B,CAAC,CACT,CAAC,cACN5G,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,iBAAiB,CACtBtB,KAAK,CAAC,sCAAQ,CAAA1B,QAAA,cAEdhH,IAAA,CAAC5B,UAAU,EAAC8I,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAC/B,CAAC,CACT,CAAC,cACN5G,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,QAAQ,CACbtB,KAAK,CAAC,0BAAM,CACZ2B,YAAY,CAAC,oBAAK,CAAArD,QAAA,cAElB9G,KAAA,CAACpC,MAAM,EAAAkJ,QAAA,eACLhH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,oBAAK,CAAAvB,QAAA,CAAC,oBAAG,CAAe,CAAC,cAC9ChH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,oBAAK,CAAAvB,QAAA,CAAC,oBAAG,CAAe,CAAC,cAC9ChH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,cAAI,CAAAvB,QAAA,CAAC,cAAE,CAAe,CAAC,cAC5ChH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,cAAI,CAAAvB,QAAA,CAAC,cAAE,CAAe,CAAC,EACtC,CAAC,CACA,CAAC,CACT,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClBhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EACRC,IAAI,CAAC,mBAAmB,CACxBtB,KAAK,CAAC,oBAAK,CACXuB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,QAAS,CAAC,CAAE,CAAAnD,QAAA,cAE/ChH,IAAA,CAACnC,KAAK,EAACuK,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACNpI,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,EAAG,CAAA7C,QAAA,cACVhH,IAAA,CAAC7B,IAAI,CAAC4L,IAAI,EAACC,IAAI,CAAC,QAAQ,CAACtB,KAAK,CAAC,0BAAM,CAAA1B,QAAA,cACnChH,IAAA,CAAC3B,MAAM,EACLiM,QAAQ,CAAC,cAAc,CACvBC,QAAQ,CAAE,CAAE,CACZC,YAAY,CAAEA,CAAA,GAAM,KAAM,CAAAxD,QAAA,cAE1B9G,KAAA,QAAA8G,QAAA,eACEhH,IAAA,CAACP,cAAc,GAAE,CAAC,cAClBO,IAAA,QAAKkH,KAAK,CAAE,CAAEuD,SAAS,CAAE,CAAE,CAAE,CAAAzD,QAAA,CAAC,0BAAI,CAAK,CAAC,EACrC,CAAC,CACA,CAAC,CACA,CAAC,CACT,CAAC,EACH,CAAC,CACF,CAAC,CACF,CAAC,cAGRhH,IAAA,CAAC9B,KAAK,EACJqF,KAAK,CAAC,0BAAM,CACZ+F,IAAI,CAAE9H,kBAAmB,CACzB+H,QAAQ,CAAEA,CAAA,GAAM9H,qBAAqB,CAAC,KAAK,CAAE,CAC7CmF,KAAK,CAAE,IAAK,CACZ8D,MAAM,CAAE,cACN1K,IAAA,CAACrC,MAAM,EAAagK,OAAO,CAAEA,CAAA,GAAMlG,qBAAqB,CAAC,KAAK,CAAE,CAAAuF,QAAA,CAAC,cAEjE,EAFY,OAEJ,CAAC,cACThH,IAAA,CAACrC,MAAM,EAAa+J,IAAI,cAAE1H,IAAA,CAACV,eAAe,GAAE,CAAE,CAAA0H,QAAA,CAAC,cAE/C,EAFY,OAEJ,CAAC,cACThH,IAAA,CAACrC,MAAM,EAAU+J,IAAI,cAAE1H,IAAA,CAACZ,cAAc,GAAE,CAAE,CAACuI,OAAO,CAAEA,CAAA,GAAMzE,YAAY,CAACtB,cAAc,CAAE,CAAAoF,QAAA,CAAC,oBAExF,EAFY,IAEJ,CAAC,CACT,CAAAA,QAAA,CAEDpF,cAAc,eACb1B,KAAA,CAAC1B,IAAI,EAACmM,gBAAgB,CAAC,OAAO,CAAA3D,QAAA,eAC5B9G,KAAA,CAACM,OAAO,EAACoK,GAAG,CAAC,0BAAM,CAAA5D,QAAA,eACjB9G,KAAA,CAAC3B,YAAY,EAACsM,QAAQ,MAACC,MAAM,CAAE,CAAE,CAAA9D,QAAA,eAC/BhH,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,0BAAM,CAAA1B,QAAA,CAAEpF,cAAc,CAACuC,UAAU,CAAoB,CAAC,cAC/EnE,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,0BAAM,CAAA1B,QAAA,CAAEpF,cAAc,CAACwC,UAAU,CAAoB,CAAC,cAC/EpE,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,0BAAM,CAAA1B,QAAA,CAAEpF,cAAc,CAACyC,UAAU,CAAoB,CAAC,cAC/ErE,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,0BAAM,CAAA1B,QAAA,CAAEpF,cAAc,CAAC0C,KAAK,CAAoB,CAAC,cAC1EtE,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,oBAAK,CAAA1B,QAAA,CAAEpF,cAAc,CAAC2C,YAAY,CAAoB,CAAC,cAChFvE,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,oBAAK,CAAA1B,QAAA,CAAEpF,cAAc,CAAC4C,YAAY,CAAoB,CAAC,cAChFxE,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,0BAAM,CAACqC,IAAI,CAAE,CAAE,CAAA/D,QAAA,CAAEpF,cAAc,CAAC6C,eAAe,CAAoB,CAAC,cAC7FzE,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,0BAAM,CAAA1B,QAAA,CAAElH,UAAU,CAAC8B,cAAc,CAACmB,WAAW,CAAC,CAAoB,CAAC,cAC5F/C,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,0BAAM,CAAA1B,QAAA,CAAElH,UAAU,CAAC8B,cAAc,CAACoB,eAAe,CAAC,CAAoB,CAAC,cAChGhD,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,0BAAM,CAAA1B,QAAA,cAC7BhH,IAAA,CAAC/B,GAAG,EAACmJ,KAAK,CAAExF,cAAc,CAACY,MAAM,GAAK,KAAK,CAAG,OAAO,CAAG,QAAS,CAAAwE,QAAA,CAC9DpF,cAAc,CAACY,MAAM,CACnB,CAAC,CACW,CAAC,cACpBxC,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAACrB,KAAK,CAAC,oBAAK,CAAA1B,QAAA,CAAEpF,cAAc,CAAC8C,iBAAiB,CAAoB,CAAC,EACzE,CAAC,cAEf1E,IAAA,CAACnB,OAAO,EAACmM,WAAW,CAAC,MAAM,CAAAhE,QAAA,CAAC,0BAAI,CAAS,CAAC,cAC1ChH,IAAA,CAACzB,YAAY,EAACsM,QAAQ,MAACC,MAAM,CAAE,CAAE,CAAA9D,QAAA,CAC9BiE,MAAM,CAACC,OAAO,CAACtJ,cAAc,CAACgE,cAAc,EAAI,CAAC,CAAC,CAAC,CAAC7B,GAAG,CAACoH,IAAA,MAAC,CAACxE,GAAG,CAAE4B,KAAK,CAAC,CAAA4C,IAAA,oBACpEnL,IAAA,CAACzB,YAAY,CAACwL,IAAI,EAAWrB,KAAK,CAAE/B,GAAI,CAAAK,QAAA,CACrCoE,MAAM,CAAC7C,KAAK,CAAC,EADQ5B,GAEL,CAAC,EACrB,CAAC,CACU,CAAC,CAEd/E,cAAc,CAAC+D,MAAM,EAAI/D,cAAc,CAAC+D,MAAM,CAAC0F,MAAM,CAAG,CAAC,eACxDnL,KAAA,CAAAE,SAAA,EAAA4G,QAAA,eACEhH,IAAA,CAACnB,OAAO,EAACmM,WAAW,CAAC,MAAM,CAAAhE,QAAA,CAAC,0BAAI,CAAS,CAAC,cAC1ChH,IAAA,CAACpC,KAAK,EAAAoJ,QAAA,CACHpF,cAAc,CAAC+D,MAAM,CAAC5B,GAAG,CAAC,CAACuH,KAAa,CAAEC,KAAa,gBACtDvL,IAAA,CAAC1B,KAAK,EAEJsI,KAAK,CAAE,GAAI,CACX4E,MAAM,CAAE,GAAI,CACZC,GAAG,CAAEH,KAAM,CACXI,QAAQ,CAAC,goBAAgoB,EAJpoBH,KAKN,CACF,CAAC,CACG,CAAC,EACR,CACH,GA3CqB,OA4Cf,CAAC,cACVvL,IAAA,CAACQ,OAAO,EAACoK,GAAG,CAAC,0BAAM,CAAA5D,QAAA,cACjBhH,IAAA,CAACvB,QAAQ,EAAAuI,QAAA,EAAAtG,qBAAA,CACNkB,cAAc,CAACqE,kBAAkB,UAAAvF,qBAAA,iBAAjCA,qBAAA,CAAmCqD,GAAG,CAAEnB,MAAW,eAClD1C,KAAA,CAACzB,QAAQ,CAACsL,IAAI,EAEZ3C,KAAK,CAAExE,MAAM,CAACJ,MAAM,GAAK,KAAK,CAAG,OAAO,CAAG,MAAO,CAClDmJ,GAAG,CAAE/I,MAAM,CAACJ,MAAM,GAAK,KAAK,cAAGxC,IAAA,CAACT,YAAY,GAAE,CAAC,cAAGS,IAAA,CAACR,eAAe,GAAE,CAAE,CAAAwH,QAAA,eAEtE9G,KAAA,QAAA8G,QAAA,eACEhH,IAAA,CAACM,IAAI,EAAC2G,MAAM,MAAAD,QAAA,CAAEpE,MAAM,CAACuD,IAAI,CAAO,CAAC,cACjCnG,IAAA,CAACM,IAAI,EAAC6F,IAAI,CAAC,WAAW,CAACe,KAAK,CAAE,CAAE0E,UAAU,CAAE,CAAE,CAAE,CAAA5E,QAAA,CAC7ClH,UAAU,CAAC8C,MAAM,CAACsD,IAAI,CAAC,CACpB,CAAC,EACJ,CAAC,cACNlG,IAAA,QAAKkH,KAAK,CAAE,CAAEuD,SAAS,CAAE,CAAE,CAAE,CAAAzD,QAAA,cAC3BhH,IAAA,CAACM,IAAI,EAAA0G,QAAA,CAAEpE,MAAM,CAACwD,WAAW,CAAO,CAAC,CAC9B,CAAC,cACNlG,KAAA,QAAKgH,KAAK,CAAE,CAAEuD,SAAS,CAAE,CAAE,CAAE,CAAAzD,QAAA,eAC3B9G,KAAA,CAACI,IAAI,EAAC6F,IAAI,CAAC,WAAW,CAAAa,QAAA,EAAC,sBAAK,CAACpE,MAAM,CAACyD,UAAU,EAAO,CAAC,cACtDrG,IAAA,CAAC/B,GAAG,EACFmJ,KAAK,CAAExE,MAAM,CAACJ,MAAM,GAAK,KAAK,CAAG,OAAO,CAAG,YAAa,CACxD0E,KAAK,CAAE,CAAE0E,UAAU,CAAE,CAAE,CAAE,CAAA5E,QAAA,CAExBpE,MAAM,CAACJ,MAAM,CACX,CAAC,EACH,CAAC,GArBDI,MAAM,CAAC6C,EAsBC,CAChB,CAAC,CACM,CAAC,EA5BW,aA6Bf,CAAC,cACVvF,KAAA,CAACM,OAAO,EAACoK,GAAG,CAAC,iBAAO,CAAA5D,QAAA,eAClBhH,IAAA,CAACtB,KAAK,EACJyL,OAAO,CAAC,iBAAO,CACf/D,WAAW,CAAC,+IAA4B,CACxCD,IAAI,CAAC,MAAM,CACX0F,QAAQ,MACR3E,KAAK,CAAE,CAAEe,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cACFjI,IAAA,CAACM,IAAI,EAAC6F,IAAI,CAAC,WAAW,CAAAa,QAAA,CAAC,8DAAe,CAAM,CAAC,GARtB,YAShB,CAAC,EACN,CACP,CACI,CAAC,cAGRhH,IAAA,CAAC9B,KAAK,EACJqF,KAAK,CAAC,gCAAO,CACb+F,IAAI,CAAEhI,cAAe,CACrBiI,QAAQ,CAAEA,CAAA,GAAMhI,iBAAiB,CAAC,KAAK,CAAE,CACzCqF,KAAK,CAAE,GAAI,CACX8D,MAAM,CAAE,cACN1K,IAAA,CAACrC,MAAM,EAAagK,OAAO,CAAEA,CAAA,GAAMpG,iBAAiB,CAAC,KAAK,CAAE,CAAAyF,QAAA,CAAC,cAE7D,EAFY,OAEJ,CAAC,cACThH,IAAA,CAACrC,MAAM,EAAgBwI,IAAI,CAAC,SAAS,CAACuB,IAAI,cAAE1H,IAAA,CAACX,gBAAgB,GAAE,CAAE,CAAA2H,QAAA,CAAC,cAElE,EAFY,UAEJ,CAAC,cACThH,IAAA,CAACrC,MAAM,EAAa+J,IAAI,cAAE1H,IAAA,CAACV,eAAe,GAAE,CAAE,CAAA0H,QAAA,CAAC,cAE/C,EAFY,OAEJ,CAAC,CACT,CAAAA,QAAA,CAEDpF,cAAc,eACb1B,KAAA,QAAKgH,KAAK,CAAE,CAAE4E,SAAS,CAAE,QAAS,CAAE,CAAA9E,QAAA,eAClChH,IAAA,CAACrB,MAAM,EACL4J,KAAK,IAAAnD,MAAA,CAAK2G,MAAM,CAACC,QAAQ,CAACC,MAAM,aAAA7G,MAAA,CAAWxD,cAAc,CAAC6D,EAAE,CAAG,CAC/DgC,IAAI,CAAE,GAAI,CACX,CAAC,cACFvH,KAAA,QAAKgH,KAAK,CAAE,CAAEuD,SAAS,CAAE,EAAG,CAAE,CAAAzD,QAAA,eAC5BhH,IAAA,CAACM,IAAI,EAAC2G,MAAM,MAAAD,QAAA,CAAEpF,cAAc,CAACuC,UAAU,CAAO,CAAC,cAC/CnE,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAC6F,IAAI,CAAC,WAAW,CAAAa,QAAA,CAAEpF,cAAc,CAACwC,UAAU,CAAO,CAAC,EACtD,CAAC,EACH,CACN,CACI,CAAC,cAGNlE,KAAA,CAAChC,KAAK,EACJqF,KAAK,CAAC,sCAAQ,CACd+F,IAAI,CAAEtH,kBAAmB,CACzB8F,IAAI,CAAElE,aAAc,CACpB2F,QAAQ,CAAEA,CAAA,GAAMtH,qBAAqB,CAAC,KAAK,CAAE,CAC7C2E,KAAK,CAAE,GAAI,CAAAI,QAAA,eAEX9G,KAAA,QAAKgH,KAAK,CAAE,CAAEe,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,eAC/BhH,IAAA,UAAOkH,KAAK,CAAE,CAAEgF,OAAO,CAAE,OAAO,CAAEjE,YAAY,CAAE,CAAE,CAAE,CAAAjB,QAAA,CAAC,gCAAK,CAAO,CAAC,cAClE9G,KAAA,CAACpC,MAAM,EACLyK,KAAK,CAAErG,YAAa,CACpBsG,QAAQ,CAAErG,eAAgB,CAC1B+E,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CAAAI,QAAA,eAEzBhH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,OAAO,CAAAvB,QAAA,CAAC,eAAa,CAAe,CAAC,cAC1DhH,IAAA,CAAClC,MAAM,CAACsM,MAAM,EAAC7B,KAAK,CAAC,KAAK,CAAAvB,QAAA,CAAC,YAAU,CAAe,CAAC,EAC/C,CAAC,EACN,CAAC,cAEN9G,KAAA,QAAA8G,QAAA,eACEhH,IAAA,UAAOkH,KAAK,CAAE,CAAEgF,OAAO,CAAE,OAAO,CAAEjE,YAAY,CAAE,CAAE,CAAE,CAAAjB,QAAA,CAAC,4CAAO,CAAO,CAAC,cACpEhH,IAAA,CAAClB,QAAQ,CAACqN,KAAK,EACb5D,KAAK,CAAEnG,YAAa,CACpBoG,QAAQ,CAAEnG,eAAgB,CAC1B6E,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CAAAI,QAAA,cAEzB9G,KAAA,CAACnC,GAAG,EAAC6L,MAAM,CAAE,CAAC,EAAE,CAAE,CAAC,CAAE,CAAA5C,QAAA,eACnBhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,YAAY,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CACzC,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,YAAY,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CACzC,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,YAAY,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CACzC,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,OAAO,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CACpC,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,cAAc,CAAAvB,QAAA,CAAC,oBAAG,CAAU,CAAC,CAC1C,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,cAAc,CAAAvB,QAAA,CAAC,oBAAG,CAAU,CAAC,CAC1C,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,iBAAiB,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC9C,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,aAAa,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC1C,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,iBAAiB,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC9C,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,QAAQ,CAAAvB,QAAA,CAAC,cAAE,CAAU,CAAC,CACnC,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,mBAAmB,CAAAvB,QAAA,CAAC,oBAAG,CAAU,CAAC,CAC/C,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,qBAAqB,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAClD,CAAC,cACNhH,IAAA,CAAChC,GAAG,EAAC+M,IAAI,CAAE,EAAG,CAAA/D,QAAA,cACZhH,IAAA,CAAClB,QAAQ,EAACyJ,KAAK,CAAC,qBAAqB,CAAAvB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAClD,CAAC,EACH,CAAC,CACQ,CAAC,EACd,CAAC,EACD,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}