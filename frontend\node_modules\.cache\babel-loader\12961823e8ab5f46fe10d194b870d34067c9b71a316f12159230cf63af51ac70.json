{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useEffect,useState}from'react';import{useNavigate}from'react-router-dom';import{Table,Button,Space,Input,Select,Card,Tag,Popconfirm,message,Modal,Form,Typography,Row,Col,Tooltip,Dropdown,Switch,Avatar,Checkbox,Descriptions}from'antd';import{PlusOutlined,EditOutlined,DeleteOutlined,EyeOutlined,UserOutlined,MoreOutlined,ExportOutlined,ReloadOutlined,LockOutlined,UnlockOutlined}from'@ant-design/icons';import*as XLSX from'xlsx';import{formatDate}from'../../utils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const{Search}=Input;const{Option}=Select;const UserListPage=()=>{var _viewingUser$name;const navigate=useNavigate();const[users,setUsers]=useState([]);const[loading,setLoading]=useState(false);const[searchKeyword,setSearchKeyword]=useState('');const[roleFilter,setRoleFilter]=useState('');const[statusFilter,setStatusFilter]=useState('');const[userModalVisible,setUserModalVisible]=useState(false);const[editingUser,setEditingUser]=useState(null);const[userForm]=Form.useForm();const[userDetailVisible,setUserDetailVisible]=useState(false);const[viewingUser,setViewingUser]=useState(null);const[exportModalVisible,setExportModalVisible]=useState(false);const[exportFormat,setExportFormat]=useState('xlsx');const[exportFields,setExportFields]=useState(['username','name','email','phone','department','roles','isActive','lastLoginTime','createdAt']);useEffect(()=>{loadData();},[searchKeyword,roleFilter,statusFilter]);const loadData=async()=>{setLoading(true);try{// TODO: 实现API调用\n// 模拟数据\nconst mockUsers=[{id:'1',username:'admin',name:'系统管理员',email:'<EMAIL>',phone:'13800138000',department:'IT部门',roles:[{id:'1',name:'系统管理员',code:'ADMIN',description:'系统管理员角色',permissions:[],isActive:true}],isActive:true,lastLoginTime:'2024-01-15 09:30:00',createdAt:'2024-01-01 00:00:00',updatedAt:'2024-01-15 09:30:00'},{id:'2',username:'bom_manager',name:'BOM管理员',email:'<EMAIL>',phone:'13800138001',department:'工程部',roles:[{id:'2',name:'BOM管理员',code:'BOM_MANAGER',description:'BOM管理员角色',permissions:[],isActive:true}],isActive:true,lastLoginTime:'2024-01-15 08:45:00',createdAt:'2024-01-02 00:00:00',updatedAt:'2024-01-15 08:45:00'},{id:'3',username:'sales_pmc',name:'销售PMC',email:'<EMAIL>',phone:'13800138002',department:'销售部',roles:[{id:'3',name:'销售PMC',code:'SALES_PMC',description:'销售PMC角色',permissions:[],isActive:true}],isActive:false,lastLoginTime:'2024-01-14 17:20:00',createdAt:'2024-01-03 00:00:00',updatedAt:'2024-01-14 17:20:00'}];setUsers(mockUsers);}catch(error){message.error('加载用户列表失败');}finally{setLoading(false);}};const handleSearch=value=>{setSearchKeyword(value);};const handleRoleFilter=value=>{setRoleFilter(value);};const handleStatusFilter=value=>{setStatusFilter(value);};const handleCreate=()=>{setEditingUser(null);userForm.resetFields();setUserModalVisible(true);};const handleEdit=record=>{setEditingUser(record);userForm.setFieldsValue(_objectSpread(_objectSpread({},record),{},{roleIds:record.roles.map(role=>role.id)}));setUserModalVisible(true);};const handleView=record=>{setViewingUser(record);setUserDetailVisible(true);};const handleDelete=async record=>{try{// TODO: 实现删除API\nmessage.success('删除成功');loadData();}catch(error){message.error('删除失败');}};const handleToggleStatus=async record=>{try{// TODO: 实现状态切换API\nmessage.success(\"\".concat(record.isActive?'禁用':'启用',\"\\u6210\\u529F\"));loadData();}catch(error){message.error('操作失败');}};const handleResetPassword=async record=>{try{// TODO: 实现重置密码API\nmessage.success('密码重置成功，新密码已发送到用户邮箱');}catch(error){message.error('密码重置失败');}};const handleModalOk=async()=>{try{const values=await userForm.validateFields();if(editingUser){// TODO: 实现更新API\nmessage.success('更新成功');}else{// TODO: 实现创建API\nmessage.success('创建成功');}setUserModalVisible(false);loadData();}catch(error){message.error('操作失败');}};const handleExport=()=>{setExportModalVisible(true);};const executeExport=()=>{try{// 准备导出数据\nconst exportData=users.map(user=>{const row={};exportFields.forEach(field=>{switch(field){case'username':row['用户名']=user.username;break;case'name':row['姓名']=user.name;break;case'email':row['邮箱']=user.email;break;case'phone':row['手机号']=user.phone;break;case'department':row['部门']=user.department||'-';break;case'roles':row['角色']=user.roles.map(role=>role.name).join(', ');break;case'isActive':row['状态']=user.isActive?'启用':'禁用';break;case'lastLoginTime':row['最后登录时间']=user.lastLoginTime?formatDate(user.lastLoginTime):'-';break;case'createdAt':row['创建时间']=formatDate(user.createdAt);break;case'updatedAt':row['更新时间']=formatDate(user.updatedAt);break;}});return row;});// 创建工作簿\nconst ws=XLSX.utils.json_to_sheet(exportData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'用户列表');// 下载文件\nconst fileName=\"\\u7528\\u6237\\u5217\\u8868_\".concat(new Date().toISOString().slice(0,10),\".\").concat(exportFormat);XLSX.writeFile(wb,fileName);message.success('导出成功');setExportModalVisible(false);}catch(error){message.error('导出失败');}};const getActionMenuItems=record=>[{key:'resetPassword',icon:/*#__PURE__*/_jsx(LockOutlined,{}),label:'重置密码',onClick:()=>handleResetPassword(record)},{key:'toggleStatus',icon:record.isActive?/*#__PURE__*/_jsx(LockOutlined,{}):/*#__PURE__*/_jsx(UnlockOutlined,{}),label:record.isActive?'禁用用户':'启用用户',onClick:()=>handleToggleStatus(record)},{key:'export',icon:/*#__PURE__*/_jsx(ExportOutlined,{}),label:'导出信息',onClick:()=>{message.info('导出功能开发中');}}];const columns=[{title:'头像',dataIndex:'avatar',key:'avatar',width:80,render:(avatar,record)=>{var _record$name;return/*#__PURE__*/_jsx(Avatar,{size:40,src:avatar,icon:/*#__PURE__*/_jsx(UserOutlined,{}),style:{backgroundColor:record.isActive?'#1890ff':'#d9d9d9'},children:!avatar&&((_record$name=record.name)===null||_record$name===void 0?void 0:_record$name.charAt(0))});}},{title:'用户名',dataIndex:'username',key:'username',width:120,render:(text,record)=>/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:()=>handleView(record),children:text})},{title:'姓名',dataIndex:'name',key:'name',width:120},{title:'邮箱',dataIndex:'email',key:'email',width:200,ellipsis:true},{title:'手机号',dataIndex:'phone',key:'phone',width:120},{title:'部门',dataIndex:'department',key:'department',width:120,ellipsis:true},{title:'角色',dataIndex:'roles',key:'roles',width:200,render:roles=>/*#__PURE__*/_jsx(Space,{wrap:true,children:roles.map(role=>/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:role.name},role.id))})},{title:'状态',dataIndex:'isActive',key:'isActive',width:80,render:isActive=>/*#__PURE__*/_jsx(Tag,{color:isActive?'green':'red',children:isActive?'启用':'禁用'})},{title:'最后登录',dataIndex:'lastLoginTime',key:'lastLoginTime',width:150,render:date=>date?formatDate(date):'-'},{title:'创建时间',dataIndex:'createdAt',key:'createdAt',width:150,render:date=>formatDate(date)},{title:'操作',key:'action',width:150,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record)})}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u7528\\u6237\\u5417\\uFF1F\",onConfirm:()=>handleDelete(record),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{})})})}),/*#__PURE__*/_jsx(Dropdown,{menu:{items:getActionMenuItems(record)},trigger:['click'],children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(MoreOutlined,{})})})]})}];return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u7528\\u6237\\u7BA1\\u7406\"})}),/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsxs(Row,{gutter:[8,8],justify:\"end\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u7528\\u6237\\u540D\\u3001\\u59D3\\u540D\\u3001\\u90AE\\u7BB1\",allowClear:true,style:{width:250},onSearch:handleSearch})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u89D2\\u8272\\u7B5B\\u9009\",allowClear:true,style:{width:120},onChange:handleRoleFilter,children:[/*#__PURE__*/_jsx(Option,{value:\"ADMIN\",children:\"\\u7CFB\\u7EDF\\u7BA1\\u7406\\u5458\"}),/*#__PURE__*/_jsx(Option,{value:\"BOM_MANAGER\",children:\"BOM\\u7BA1\\u7406\\u5458\"}),/*#__PURE__*/_jsx(Option,{value:\"SALES_PMC\",children:\"\\u9500\\u552EPMC\"}),/*#__PURE__*/_jsx(Option,{value:\"PURCHASE_MANAGER\",children:\"\\u91C7\\u8D2D\\u7ECF\\u7406\"})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u72B6\\u6001\\u7B5B\\u9009\",allowClear:true,style:{width:100},onChange:handleStatusFilter,children:[/*#__PURE__*/_jsx(Option,{value:\"active\",children:\"\\u542F\\u7528\"}),/*#__PURE__*/_jsx(Option,{value:\"inactive\",children:\"\\u7981\\u7528\"})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:loadData,children:\"\\u5237\\u65B0\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleCreate,children:\"\\u65B0\\u589E\\u7528\\u6237\"})})]})})]})}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:users,loading:loading,rowKey:\"id\",scroll:{x:1400},pagination:{total:users.length,pageSize:20,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsx(Modal,{title:editingUser?'编辑用户':'新增用户',open:userModalVisible,onOk:handleModalOk,onCancel:()=>setUserModalVisible(false),width:600,destroyOnClose:true,children:/*#__PURE__*/_jsxs(Form,{form:userForm,layout:\"vertical\",initialValues:{isActive:true},children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"username\",label:\"\\u7528\\u6237\\u540D\",rules:[{required:true,message:'请输入用户名'},{min:3,max:20,message:'用户名长度为3-20个字符'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"\\u59D3\\u540D\",rules:[{required:true,message:'请输入姓名'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u59D3\\u540D\"})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"email\",label:\"\\u90AE\\u7BB1\",rules:[{required:true,message:'请输入邮箱'},{type:'email',message:'请输入有效的邮箱地址'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"phone\",label:\"\\u624B\\u673A\\u53F7\",rules:[{required:true,message:'请输入手机号'},{pattern:/^1[3-9]\\d{9}$/,message:'请输入有效的手机号'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u624B\\u673A\\u53F7\"})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"department\",label:\"\\u90E8\\u95E8\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u90E8\\u95E8\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"roleIds\",label:\"\\u89D2\\u8272\",rules:[{required:true,message:'请选择角色'}],children:/*#__PURE__*/_jsxs(Select,{mode:\"multiple\",placeholder:\"\\u8BF7\\u9009\\u62E9\\u89D2\\u8272\",allowClear:true,children:[/*#__PURE__*/_jsx(Option,{value:\"1\",children:\"\\u7CFB\\u7EDF\\u7BA1\\u7406\\u5458\"}),/*#__PURE__*/_jsx(Option,{value:\"2\",children:\"BOM\\u7BA1\\u7406\\u5458\"}),/*#__PURE__*/_jsx(Option,{value:\"3\",children:\"\\u9500\\u552EPMC\"}),/*#__PURE__*/_jsx(Option,{value:\"4\",children:\"\\u91C7\\u8D2D\\u7ECF\\u7406\"}),/*#__PURE__*/_jsx(Option,{value:\"5\",children:\"\\u751F\\u4EA7\\u8BA1\\u5212\\u5458\"}),/*#__PURE__*/_jsx(Option,{value:\"6\",children:\"\\u4ED3\\u5E93\\u7BA1\\u7406\\u5458\"})]})})})]}),!editingUser&&/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"password\",label:\"\\u5BC6\\u7801\",rules:[{required:true,message:'请输入密码'},{min:6,message:'密码至少6位'}],children:/*#__PURE__*/_jsx(Input.Password,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"confirmPassword\",label:\"\\u786E\\u8BA4\\u5BC6\\u7801\",dependencies:['password'],rules:[{required:true,message:'请确认密码'},_ref=>{let{getFieldValue}=_ref;return{validator(_,value){if(!value||getFieldValue('password')===value){return Promise.resolve();}return Promise.reject(new Error('两次输入的密码不一致'));}};}],children:/*#__PURE__*/_jsx(Input.Password,{placeholder:\"\\u8BF7\\u786E\\u8BA4\\u5BC6\\u7801\"})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"isActive\",label:\"\\u72B6\\u6001\",valuePropName:\"checked\",children:/*#__PURE__*/_jsx(Switch,{checkedChildren:\"\\u542F\\u7528\",unCheckedChildren:\"\\u7981\\u7528\"})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u7528\\u6237\\u8BE6\\u60C5\",open:userDetailVisible,onCancel:()=>setUserDetailVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setUserDetailVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:viewingUser&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{span:4,children:/*#__PURE__*/_jsx(Avatar,{size:80,src:viewingUser.avatar,icon:/*#__PURE__*/_jsx(UserOutlined,{}),style:{backgroundColor:viewingUser.isActive?'#1890ff':'#d9d9d9'},children:!viewingUser.avatar&&((_viewingUser$name=viewingUser.name)===null||_viewingUser$name===void 0?void 0:_viewingUser$name.charAt(0))})}),/*#__PURE__*/_jsxs(Col,{span:20,children:[/*#__PURE__*/_jsxs(Title,{level:4,style:{margin:0},children:[viewingUser.name,/*#__PURE__*/_jsx(Tag,{color:viewingUser.isActive?'green':'red',style:{marginLeft:8},children:viewingUser.isActive?'启用':'禁用'})]}),/*#__PURE__*/_jsxs(\"p\",{style:{color:'#666',margin:'8px 0 0 0'},children:[\"@\",viewingUser.username]})]})]}),/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:2,children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7528\\u6237\\u540D\",children:viewingUser.username}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u59D3\\u540D\",children:viewingUser.name}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u90AE\\u7BB1\",children:viewingUser.email}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u624B\\u673A\\u53F7\",children:viewingUser.phone}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u90E8\\u95E8\",children:viewingUser.department||'-'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Tag,{color:viewingUser.isActive?'green':'red',children:viewingUser.isActive?'启用':'禁用'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u89D2\\u8272\",span:2,children:/*#__PURE__*/_jsx(Space,{wrap:true,children:viewingUser.roles.map(role=>/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:role.name},role.id))})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6700\\u540E\\u767B\\u5F55\\u65F6\\u95F4\",children:viewingUser.lastLoginTime?formatDate(viewingUser.lastLoginTime):'-'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u521B\\u5EFA\\u65F6\\u95F4\",children:formatDate(viewingUser.createdAt)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u66F4\\u65B0\\u65F6\\u95F4\",span:2,children:formatDate(viewingUser.updatedAt)})]})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u7528\\u6237\\u5BFC\\u51FA\",open:exportModalVisible,onOk:executeExport,onCancel:()=>setExportModalVisible(false),okText:\"\\u5BFC\\u51FA\",cancelText:\"\\u53D6\\u6D88\",width:600,children:/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Form,{layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BFC\\u51FA\\u683C\\u5F0F\",children:/*#__PURE__*/_jsxs(Select,{value:exportFormat,onChange:setExportFormat,style:{width:'100%'},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"xlsx\",children:\"Excel (.xlsx)\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"csv\",children:\"CSV (.csv)\"})]})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BFC\\u51FA\\u5B57\\u6BB5\",children:/*#__PURE__*/_jsx(Checkbox.Group,{value:exportFields,onChange:setExportFields,style:{width:'100%'},children:/*#__PURE__*/_jsxs(Row,{gutter:[8,8],children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"username\",children:\"\\u7528\\u6237\\u540D\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"name\",children:\"\\u59D3\\u540D\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"email\",children:\"\\u90AE\\u7BB1\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"phone\",children:\"\\u624B\\u673A\\u53F7\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"department\",children:\"\\u90E8\\u95E8\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"roles\",children:\"\\u89D2\\u8272\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"isActive\",children:\"\\u72B6\\u6001\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"lastLoginTime\",children:\"\\u6700\\u540E\\u767B\\u5F55\\u65F6\\u95F4\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"createdAt\",children:\"\\u521B\\u5EFA\\u65F6\\u95F4\"})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Checkbox,{value:\"updatedAt\",children:\"\\u66F4\\u65B0\\u65F6\\u95F4\"})})]})})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(\"div\",{style:{padding:12,backgroundColor:'#f0f2f5',borderRadius:6},children:/*#__PURE__*/_jsxs(\"p\",{style:{margin:0,color:'#666'},children:[\"\\u5C06\\u5BFC\\u51FA\\u5168\\u90E8 \",users.length,\" \\u6761\\u7528\\u6237\\u8BB0\\u5F55\"]})})})]})})})]});};export default UserListPage;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "Popconfirm", "message", "Modal", "Form", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Switch", "Avatar", "Checkbox", "Descriptions", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "UserOutlined", "MoreOutlined", "ExportOutlined", "ReloadOutlined", "LockOutlined", "UnlockOutlined", "XLSX", "formatDate", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Search", "Option", "UserListPage", "_viewingUser$name", "navigate", "users", "setUsers", "loading", "setLoading", "searchKeyword", "setSearchKeyword", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "statusFilter", "setStatus<PERSON>ilter", "userModalVisible", "setUserModalVisible", "editingUser", "setEditingUser", "userForm", "useForm", "userDetailVisible", "setUserDetailVisible", "viewingUser", "setViewingUser", "exportModalVisible", "setExportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "loadData", "mockUsers", "id", "username", "name", "email", "phone", "department", "roles", "code", "description", "permissions", "isActive", "lastLoginTime", "createdAt", "updatedAt", "error", "handleSearch", "value", "handleRoleFilter", "handleStatusFilter", "handleCreate", "resetFields", "handleEdit", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectSpread", "roleIds", "map", "role", "handleView", "handleDelete", "success", "handleToggleStatus", "concat", "handleResetPassword", "handleModalOk", "values", "validateFields", "handleExport", "executeExport", "exportData", "user", "row", "for<PERSON>ach", "field", "join", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "fileName", "Date", "toISOString", "slice", "writeFile", "getActionMenuItems", "key", "icon", "label", "onClick", "info", "columns", "title", "dataIndex", "width", "render", "avatar", "_record$name", "size", "src", "style", "backgroundColor", "children", "char<PERSON>t", "text", "type", "ellipsis", "wrap", "color", "date", "fixed", "_", "onConfirm", "okText", "cancelText", "danger", "menu", "items", "trigger", "marginBottom", "gutter", "align", "level", "margin", "flex", "justify", "placeholder", "allowClear", "onSearch", "onChange", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "total", "length", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onOk", "onCancel", "destroyOnClose", "form", "layout", "initialValues", "span", "<PERSON><PERSON>", "rules", "required", "min", "max", "pattern", "mode", "Password", "dependencies", "_ref", "getFieldValue", "validator", "Promise", "resolve", "reject", "Error", "valuePropName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "footer", "marginLeft", "bordered", "column", "Group", "padding", "borderRadius"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/system/UserListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  Popconfirm,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Switch,\n  Avatar,\n  DatePicker,\n  Checkbox,\n  Descriptions,\n  Divider,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  UserOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  LockOutlined,\n  UnlockOutlined,\n} from '@ant-design/icons';\nimport * as XLSX from 'xlsx';\n\nimport { User, Role } from '../../types';\nimport { ROUTES, USER_ROLES } from '../../constants';\nimport { formatDate } from '../../utils';\n\nconst { Title } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\n\nconst UserListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [roleFilter, setRoleFilter] = useState<string>('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [userModalVisible, setUserModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [userForm] = Form.useForm();\n  const [userDetailVisible, setUserDetailVisible] = useState(false);\n  const [viewingUser, setViewingUser] = useState<User | null>(null);\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState<'xlsx' | 'csv'>('xlsx');\n  const [exportFields, setExportFields] = useState<string[]>([\n    'username', 'name', 'email', 'phone', 'department', 'roles', 'isActive', 'lastLoginTime', 'createdAt'\n  ]);\n\n  useEffect(() => {\n    loadData();\n  }, [searchKeyword, roleFilter, statusFilter]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // TODO: 实现API调用\n      // 模拟数据\n      const mockUsers: User[] = [\n        {\n          id: '1',\n          username: 'admin',\n          name: '系统管理员',\n          email: '<EMAIL>',\n          phone: '13800138000',\n          department: 'IT部门',\n          roles: [{ id: '1', name: '系统管理员', code: 'ADMIN', description: '系统管理员角色', permissions: [], isActive: true }],\n          isActive: true,\n          lastLoginTime: '2024-01-15 09:30:00',\n          createdAt: '2024-01-01 00:00:00',\n          updatedAt: '2024-01-15 09:30:00',\n        },\n        {\n          id: '2',\n          username: 'bom_manager',\n          name: 'BOM管理员',\n          email: '<EMAIL>',\n          phone: '13800138001',\n          department: '工程部',\n          roles: [{ id: '2', name: 'BOM管理员', code: 'BOM_MANAGER', description: 'BOM管理员角色', permissions: [], isActive: true }],\n          isActive: true,\n          lastLoginTime: '2024-01-15 08:45:00',\n          createdAt: '2024-01-02 00:00:00',\n          updatedAt: '2024-01-15 08:45:00',\n        },\n        {\n          id: '3',\n          username: 'sales_pmc',\n          name: '销售PMC',\n          email: '<EMAIL>',\n          phone: '13800138002',\n          department: '销售部',\n          roles: [{ id: '3', name: '销售PMC', code: 'SALES_PMC', description: '销售PMC角色', permissions: [], isActive: true }],\n          isActive: false,\n          lastLoginTime: '2024-01-14 17:20:00',\n          createdAt: '2024-01-03 00:00:00',\n          updatedAt: '2024-01-14 17:20:00',\n        },\n      ];\n      setUsers(mockUsers);\n    } catch (error) {\n      message.error('加载用户列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchKeyword(value);\n  };\n\n  const handleRoleFilter = (value: string) => {\n    setRoleFilter(value);\n  };\n\n  const handleStatusFilter = (value: string) => {\n    setStatusFilter(value);\n  };\n\n  const handleCreate = () => {\n    setEditingUser(null);\n    userForm.resetFields();\n    setUserModalVisible(true);\n  };\n\n  const handleEdit = (record: User) => {\n    setEditingUser(record);\n    userForm.setFieldsValue({\n      ...record,\n      roleIds: record.roles.map(role => role.id),\n    });\n    setUserModalVisible(true);\n  };\n\n  const handleView = (record: User) => {\n    setViewingUser(record);\n    setUserDetailVisible(true);\n  };\n\n  const handleDelete = async (record: User) => {\n    try {\n      // TODO: 实现删除API\n      message.success('删除成功');\n      loadData();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleToggleStatus = async (record: User) => {\n    try {\n      // TODO: 实现状态切换API\n      message.success(`${record.isActive ? '禁用' : '启用'}成功`);\n      loadData();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const handleResetPassword = async (record: User) => {\n    try {\n      // TODO: 实现重置密码API\n      message.success('密码重置成功，新密码已发送到用户邮箱');\n    } catch (error) {\n      message.error('密码重置失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await userForm.validateFields();\n\n      if (editingUser) {\n        // TODO: 实现更新API\n        message.success('更新成功');\n      } else {\n        // TODO: 实现创建API\n        message.success('创建成功');\n      }\n\n      setUserModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const executeExport = () => {\n    try {\n      // 准备导出数据\n      const exportData = users.map(user => {\n        const row: any = {};\n        \n        exportFields.forEach(field => {\n          switch (field) {\n            case 'username':\n              row['用户名'] = user.username;\n              break;\n            case 'name':\n              row['姓名'] = user.name;\n              break;\n            case 'email':\n              row['邮箱'] = user.email;\n              break;\n            case 'phone':\n              row['手机号'] = user.phone;\n              break;\n            case 'department':\n              row['部门'] = user.department || '-';\n              break;\n            case 'roles':\n              row['角色'] = user.roles.map(role => role.name).join(', ');\n              break;\n            case 'isActive':\n              row['状态'] = user.isActive ? '启用' : '禁用';\n              break;\n            case 'lastLoginTime':\n              row['最后登录时间'] = user.lastLoginTime ? formatDate(user.lastLoginTime) : '-';\n              break;\n            case 'createdAt':\n              row['创建时间'] = formatDate(user.createdAt);\n              break;\n            case 'updatedAt':\n              row['更新时间'] = formatDate(user.updatedAt);\n              break;\n          }\n        });\n        \n        return row;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, '用户列表');\n\n      // 下载文件\n      const fileName = `用户列表_${new Date().toISOString().slice(0, 10)}.${exportFormat}`;\n      XLSX.writeFile(wb, fileName);\n\n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  const getActionMenuItems = (record: User): MenuProps['items'] => [\n    {\n      key: 'resetPassword',\n      icon: <LockOutlined />,\n      label: '重置密码',\n      onClick: () => handleResetPassword(record),\n    },\n    {\n      key: 'toggleStatus',\n      icon: record.isActive ? <LockOutlined /> : <UnlockOutlined />,\n      label: record.isActive ? '禁用用户' : '启用用户',\n      onClick: () => handleToggleStatus(record),\n    },\n    {\n      key: 'export',\n      icon: <ExportOutlined />,\n      label: '导出信息',\n      onClick: () => {\n        message.info('导出功能开发中');\n      },\n    },\n  ];\n\n  const columns = [\n    {\n      title: '头像',\n      dataIndex: 'avatar',\n      key: 'avatar',\n      width: 80,\n      render: (avatar: string, record: User) => (\n        <Avatar\n          size={40}\n          src={avatar}\n          icon={<UserOutlined />}\n          style={{ backgroundColor: record.isActive ? '#1890ff' : '#d9d9d9' }}\n        >\n          {!avatar && record.name?.charAt(0)}\n        </Avatar>\n      ),\n    },\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      width: 120,\n      render: (text: string, record: User) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n      width: 120,\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      width: 200,\n      ellipsis: true,\n    },\n    {\n      title: '手机号',\n      dataIndex: 'phone',\n      key: 'phone',\n      width: 120,\n    },\n    {\n      title: '部门',\n      dataIndex: 'department',\n      key: 'department',\n      width: 120,\n      ellipsis: true,\n    },\n    {\n      title: '角色',\n      dataIndex: 'roles',\n      key: 'roles',\n      width: 200,\n      render: (roles: Role[]) => (\n        <Space wrap>\n          {roles.map(role => (\n            <Tag key={role.id} color=\"blue\">\n              {role.name}\n            </Tag>\n          ))}\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'isActive',\n      width: 80,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '最后登录',\n      dataIndex: 'lastLoginTime',\n      key: 'lastLoginTime',\n      width: 150,\n      render: (date: string) => date ? formatDate(date) : '-',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: User) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定要删除这个用户吗？\"\n            onConfirm={() => handleDelete(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n          <Dropdown\n            menu={{ items: getActionMenuItems(record) }}\n            trigger={['click']}\n          >\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <div style={{ marginBottom: 16 }}>\n          <Row gutter={[16, 16]} align=\"middle\">\n            <Col>\n              <Title level={4} style={{ margin: 0 }}>用户管理</Title>\n            </Col>\n            <Col flex=\"auto\">\n              <Row gutter={[8, 8]} justify=\"end\">\n                <Col>\n                  <Search\n                    placeholder=\"搜索用户名、姓名、邮箱\"\n                    allowClear\n                    style={{ width: 250 }}\n                    onSearch={handleSearch}\n                  />\n                </Col>\n                <Col>\n                  <Select\n                    placeholder=\"角色筛选\"\n                    allowClear\n                    style={{ width: 120 }}\n                    onChange={handleRoleFilter}\n                  >\n                    <Option value=\"ADMIN\">系统管理员</Option>\n                    <Option value=\"BOM_MANAGER\">BOM管理员</Option>\n                    <Option value=\"SALES_PMC\">销售PMC</Option>\n                    <Option value=\"PURCHASE_MANAGER\">采购经理</Option>\n                  </Select>\n                </Col>\n                <Col>\n                  <Select\n                    placeholder=\"状态筛选\"\n                    allowClear\n                    style={{ width: 100 }}\n                    onChange={handleStatusFilter}\n                  >\n                    <Option value=\"active\">启用</Option>\n                    <Option value=\"inactive\">禁用</Option>\n                  </Select>\n                </Col>\n                <Col>\n                  <Button\n                    icon={<ReloadOutlined />}\n                    onClick={loadData}\n                  >\n                    刷新\n                  </Button>\n                </Col>\n                <Col>\n                  <Button\n                    icon={<ExportOutlined />}\n                    onClick={handleExport}\n                  >\n                    导出\n                  </Button>\n                </Col>\n                <Col>\n                  <Button\n                    type=\"primary\"\n                    icon={<PlusOutlined />}\n                    onClick={handleCreate}\n                  >\n                    新增用户\n                  </Button>\n                </Col>\n              </Row>\n            </Col>\n          </Row>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={users}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1400 }}\n          pagination={{\n            total: users.length,\n            pageSize: 20,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingUser ? '编辑用户' : '新增用户'}\n        open={userModalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setUserModalVisible(false)}\n        width={600}\n        destroyOnClose\n      >\n        <Form\n          form={userForm}\n          layout=\"vertical\"\n          initialValues={{\n            isActive: true,\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"username\"\n                label=\"用户名\"\n                rules={[\n                  { required: true, message: '请输入用户名' },\n                  { min: 3, max: 20, message: '用户名长度为3-20个字符' },\n                ]}\n              >\n                <Input placeholder=\"请输入用户名\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"姓名\"\n                rules={[{ required: true, message: '请输入姓名' }]}\n              >\n                <Input placeholder=\"请输入姓名\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"email\"\n                label=\"邮箱\"\n                rules={[\n                  { required: true, message: '请输入邮箱' },\n                  { type: 'email', message: '请输入有效的邮箱地址' },\n                ]}\n              >\n                <Input placeholder=\"请输入邮箱\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"phone\"\n                label=\"手机号\"\n                rules={[\n                  { required: true, message: '请输入手机号' },\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号' },\n                ]}\n              >\n                <Input placeholder=\"请输入手机号\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"department\"\n                label=\"部门\"\n              >\n                <Input placeholder=\"请输入部门\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"roleIds\"\n                label=\"角色\"\n                rules={[{ required: true, message: '请选择角色' }]}\n              >\n                <Select\n                  mode=\"multiple\"\n                  placeholder=\"请选择角色\"\n                  allowClear\n                >\n                  <Option value=\"1\">系统管理员</Option>\n                  <Option value=\"2\">BOM管理员</Option>\n                  <Option value=\"3\">销售PMC</Option>\n                  <Option value=\"4\">采购经理</Option>\n                  <Option value=\"5\">生产计划员</Option>\n                  <Option value=\"6\">仓库管理员</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          {!editingUser && (\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"password\"\n                  label=\"密码\"\n                  rules={[\n                    { required: true, message: '请输入密码' },\n                    { min: 6, message: '密码至少6位' },\n                  ]}\n                >\n                  <Input.Password placeholder=\"请输入密码\" />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"confirmPassword\"\n                  label=\"确认密码\"\n                  dependencies={['password']}\n                  rules={[\n                    { required: true, message: '请确认密码' },\n                    ({ getFieldValue }) => ({\n                      validator(_, value) {\n                        if (!value || getFieldValue('password') === value) {\n                          return Promise.resolve();\n                        }\n                        return Promise.reject(new Error('两次输入的密码不一致'));\n                      },\n                    }),\n                  ]}\n                >\n                  <Input.Password placeholder=\"请确认密码\" />\n                </Form.Item>\n              </Col>\n            </Row>\n          )}\n\n          <Form.Item\n            name=\"isActive\"\n            label=\"状态\"\n            valuePropName=\"checked\"\n          >\n            <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 用户详情模态框 */}\n      <Modal\n        title=\"用户详情\"\n        open={userDetailVisible}\n        onCancel={() => setUserDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setUserDetailVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={800}\n      >\n        {viewingUser && (\n          <div>\n            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n              <Col span={4}>\n                <Avatar\n                  size={80}\n                  src={viewingUser.avatar}\n                  icon={<UserOutlined />}\n                  style={{ backgroundColor: viewingUser.isActive ? '#1890ff' : '#d9d9d9' }}\n                >\n                  {!viewingUser.avatar && viewingUser.name?.charAt(0)}\n                </Avatar>\n              </Col>\n              <Col span={20}>\n                <Title level={4} style={{ margin: 0 }}>\n                  {viewingUser.name}\n                  <Tag\n                    color={viewingUser.isActive ? 'green' : 'red'}\n                    style={{ marginLeft: 8 }}\n                  >\n                    {viewingUser.isActive ? '启用' : '禁用'}\n                  </Tag>\n                </Title>\n                <p style={{ color: '#666', margin: '8px 0 0 0' }}>\n                  @{viewingUser.username}\n                </p>\n              </Col>\n            </Row>\n\n            <Descriptions bordered column={2}>\n              <Descriptions.Item label=\"用户名\">{viewingUser.username}</Descriptions.Item>\n              <Descriptions.Item label=\"姓名\">{viewingUser.name}</Descriptions.Item>\n              <Descriptions.Item label=\"邮箱\">{viewingUser.email}</Descriptions.Item>\n              <Descriptions.Item label=\"手机号\">{viewingUser.phone}</Descriptions.Item>\n              <Descriptions.Item label=\"部门\">{viewingUser.department || '-'}</Descriptions.Item>\n              <Descriptions.Item label=\"状态\">\n                <Tag color={viewingUser.isActive ? 'green' : 'red'}>\n                  {viewingUser.isActive ? '启用' : '禁用'}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"角色\" span={2}>\n                <Space wrap>\n                  {viewingUser.roles.map(role => (\n                    <Tag key={role.id} color=\"blue\">\n                      {role.name}\n                    </Tag>\n                  ))}\n                </Space>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"最后登录时间\">\n                {viewingUser.lastLoginTime ? formatDate(viewingUser.lastLoginTime) : '-'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"创建时间\">\n                {formatDate(viewingUser.createdAt)}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"更新时间\" span={2}>\n                {formatDate(viewingUser.updatedAt)}\n              </Descriptions.Item>\n            </Descriptions>\n          </div>\n        )}\n      </Modal>\n\n      {/* 导出模态框 */}\n      <Modal\n        title=\"用户导出\"\n        open={exportModalVisible}\n        onOk={executeExport}\n        onCancel={() => setExportModalVisible(false)}\n        okText=\"导出\"\n        cancelText=\"取消\"\n        width={600}\n      >\n        <div>\n          <Form layout=\"vertical\">\n            <Form.Item label=\"导出格式\">\n              <Select\n                value={exportFormat}\n                onChange={setExportFormat}\n                style={{ width: '100%' }}\n              >\n                <Select.Option value=\"xlsx\">Excel (.xlsx)</Select.Option>\n                <Select.Option value=\"csv\">CSV (.csv)</Select.Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item label=\"导出字段\">\n              <Checkbox.Group\n                value={exportFields}\n                onChange={setExportFields}\n                style={{ width: '100%' }}\n              >\n                <Row gutter={[8, 8]}>\n                  <Col span={8}>\n                    <Checkbox value=\"username\">用户名</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"name\">姓名</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"email\">邮箱</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"phone\">手机号</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"department\">部门</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"roles\">角色</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"isActive\">状态</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"lastLoginTime\">最后登录时间</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"createdAt\">创建时间</Checkbox>\n                  </Col>\n                  <Col span={8}>\n                    <Checkbox value=\"updatedAt\">更新时间</Checkbox>\n                  </Col>\n                </Row>\n              </Checkbox.Group>\n            </Form.Item>\n\n            <Form.Item>\n              <div style={{ padding: 12, backgroundColor: '#f0f2f5', borderRadius: 6 }}>\n                <p style={{ margin: 0, color: '#666' }}>\n                  将导出全部 {users.length} 条用户记录\n                </p>\n              </div>\n            </Form.Item>\n          </Form>\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default UserListPage;\n"], "mappings": "wHAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,KAAK,CACLC,IAAI,CACJC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,OAAO,CACPC,QAAQ,CAERC,MAAM,CACNC,MAAM,CAENC,QAAQ,CACRC,YAAY,KAEP,MAAM,CACb,OACEC,YAAY,CAEZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,cAAc,CACdC,YAAY,CACZC,cAAc,KACT,mBAAmB,CAC1B,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAI5B,OAASC,UAAU,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAEC,KAAM,CAAC,CAAGzB,UAAU,CAC5B,KAAM,CAAE0B,MAAO,CAAC,CAAGlC,KAAK,CACxB,KAAM,CAAEmC,MAAO,CAAC,CAAGlC,MAAM,CAEzB,KAAM,CAAAmC,YAAsB,CAAGA,CAAA,GAAM,KAAAC,iBAAA,CACnC,KAAM,CAAAC,QAAQ,CAAG1C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC2C,KAAK,CAAEC,QAAQ,CAAC,CAAG7C,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgD,aAAa,CAAEC,gBAAgB,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkD,UAAU,CAAEC,aAAa,CAAC,CAAGnD,QAAQ,CAAS,EAAE,CAAC,CACxD,KAAM,CAACoD,YAAY,CAAEC,eAAe,CAAC,CAAGrD,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACsD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACwD,WAAW,CAAEC,cAAc,CAAC,CAAGzD,QAAQ,CAAc,IAAI,CAAC,CACjE,KAAM,CAAC0D,QAAQ,CAAC,CAAG9C,IAAI,CAAC+C,OAAO,CAAC,CAAC,CACjC,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG7D,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC8D,WAAW,CAAEC,cAAc,CAAC,CAAG/D,QAAQ,CAAc,IAAI,CAAC,CACjE,KAAM,CAACgE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAiB,MAAM,CAAC,CACxE,KAAM,CAACoE,YAAY,CAAEC,eAAe,CAAC,CAAGrE,QAAQ,CAAW,CACzD,UAAU,CAAE,MAAM,CAAE,OAAO,CAAE,OAAO,CAAE,YAAY,CAAE,OAAO,CAAE,UAAU,CAAE,eAAe,CAAE,WAAW,CACtG,CAAC,CAEFD,SAAS,CAAC,IAAM,CACduE,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACtB,aAAa,CAAEE,UAAU,CAAEE,YAAY,CAAC,CAAC,CAE7C,KAAM,CAAAkB,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BvB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA;AACA,KAAM,CAAAwB,SAAiB,CAAG,CACxB,CACEC,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,aAAa,CACpBC,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,CAAC,CAAEN,EAAE,CAAE,GAAG,CAAEE,IAAI,CAAE,OAAO,CAAEK,IAAI,CAAE,OAAO,CAAEC,WAAW,CAAE,SAAS,CAAEC,WAAW,CAAE,EAAE,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAC,CAC3GA,QAAQ,CAAE,IAAI,CACdC,aAAa,CAAE,qBAAqB,CACpCC,SAAS,CAAE,qBAAqB,CAChCC,SAAS,CAAE,qBACb,CAAC,CACD,CACEb,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,aAAa,CACvBC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,iBAAiB,CACxBC,KAAK,CAAE,aAAa,CACpBC,UAAU,CAAE,KAAK,CACjBC,KAAK,CAAE,CAAC,CAAEN,EAAE,CAAE,GAAG,CAAEE,IAAI,CAAE,QAAQ,CAAEK,IAAI,CAAE,aAAa,CAAEC,WAAW,CAAE,UAAU,CAAEC,WAAW,CAAE,EAAE,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAC,CACnHA,QAAQ,CAAE,IAAI,CACdC,aAAa,CAAE,qBAAqB,CACpCC,SAAS,CAAE,qBAAqB,CAChCC,SAAS,CAAE,qBACb,CAAC,CACD,CACEb,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,WAAW,CACrBC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,aAAa,CACpBC,UAAU,CAAE,KAAK,CACjBC,KAAK,CAAE,CAAC,CAAEN,EAAE,CAAE,GAAG,CAAEE,IAAI,CAAE,OAAO,CAAEK,IAAI,CAAE,WAAW,CAAEC,WAAW,CAAE,SAAS,CAAEC,WAAW,CAAE,EAAE,CAAEC,QAAQ,CAAE,IAAK,CAAC,CAAC,CAC/GA,QAAQ,CAAE,KAAK,CACfC,aAAa,CAAE,qBAAqB,CACpCC,SAAS,CAAE,qBAAqB,CAChCC,SAAS,CAAE,qBACb,CAAC,CACF,CACDxC,QAAQ,CAAC0B,SAAS,CAAC,CACrB,CAAE,MAAOe,KAAK,CAAE,CACd5E,OAAO,CAAC4E,KAAK,CAAC,UAAU,CAAC,CAC3B,CAAC,OAAS,CACRvC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAwC,YAAY,CAAIC,KAAa,EAAK,CACtCvC,gBAAgB,CAACuC,KAAK,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAID,KAAa,EAAK,CAC1CrC,aAAa,CAACqC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAIF,KAAa,EAAK,CAC5CnC,eAAe,CAACmC,KAAK,CAAC,CACxB,CAAC,CAED,KAAM,CAAAG,YAAY,CAAGA,CAAA,GAAM,CACzBlC,cAAc,CAAC,IAAI,CAAC,CACpBC,QAAQ,CAACkC,WAAW,CAAC,CAAC,CACtBrC,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAsC,UAAU,CAAIC,MAAY,EAAK,CACnCrC,cAAc,CAACqC,MAAM,CAAC,CACtBpC,QAAQ,CAACqC,cAAc,CAAAC,aAAA,CAAAA,aAAA,IAClBF,MAAM,MACTG,OAAO,CAAEH,MAAM,CAAChB,KAAK,CAACoB,GAAG,CAACC,IAAI,EAAIA,IAAI,CAAC3B,EAAE,CAAC,EAC3C,CAAC,CACFjB,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAA6C,UAAU,CAAIN,MAAY,EAAK,CACnC/B,cAAc,CAAC+B,MAAM,CAAC,CACtBjC,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAwC,YAAY,CAAG,KAAO,CAAAP,MAAY,EAAK,CAC3C,GAAI,CACF;AACApF,OAAO,CAAC4F,OAAO,CAAC,MAAM,CAAC,CACvBhC,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOgB,KAAK,CAAE,CACd5E,OAAO,CAAC4E,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAiB,kBAAkB,CAAG,KAAO,CAAAT,MAAY,EAAK,CACjD,GAAI,CACF;AACApF,OAAO,CAAC4F,OAAO,IAAAE,MAAA,CAAIV,MAAM,CAACZ,QAAQ,CAAG,IAAI,CAAG,IAAI,gBAAI,CAAC,CACrDZ,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOgB,KAAK,CAAE,CACd5E,OAAO,CAAC4E,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAmB,mBAAmB,CAAG,KAAO,CAAAX,MAAY,EAAK,CAClD,GAAI,CACF;AACApF,OAAO,CAAC4F,OAAO,CAAC,oBAAoB,CAAC,CACvC,CAAE,MAAOhB,KAAK,CAAE,CACd5E,OAAO,CAAC4E,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAoB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAjD,QAAQ,CAACkD,cAAc,CAAC,CAAC,CAE9C,GAAIpD,WAAW,CAAE,CACf;AACA9C,OAAO,CAAC4F,OAAO,CAAC,MAAM,CAAC,CACzB,CAAC,IAAM,CACL;AACA5F,OAAO,CAAC4F,OAAO,CAAC,MAAM,CAAC,CACzB,CAEA/C,mBAAmB,CAAC,KAAK,CAAC,CAC1Be,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOgB,KAAK,CAAE,CACd5E,OAAO,CAAC4E,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAuB,YAAY,CAAGA,CAAA,GAAM,CACzB5C,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA6C,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAGnE,KAAK,CAACsD,GAAG,CAACc,IAAI,EAAI,CACnC,KAAM,CAAAC,GAAQ,CAAG,CAAC,CAAC,CAEnB7C,YAAY,CAAC8C,OAAO,CAACC,KAAK,EAAI,CAC5B,OAAQA,KAAK,EACX,IAAK,UAAU,CACbF,GAAG,CAAC,KAAK,CAAC,CAAGD,IAAI,CAACvC,QAAQ,CAC1B,MACF,IAAK,MAAM,CACTwC,GAAG,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACtC,IAAI,CACrB,MACF,IAAK,OAAO,CACVuC,GAAG,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACrC,KAAK,CACtB,MACF,IAAK,OAAO,CACVsC,GAAG,CAAC,KAAK,CAAC,CAAGD,IAAI,CAACpC,KAAK,CACvB,MACF,IAAK,YAAY,CACfqC,GAAG,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACnC,UAAU,EAAI,GAAG,CAClC,MACF,IAAK,OAAO,CACVoC,GAAG,CAAC,IAAI,CAAC,CAAGD,IAAI,CAAClC,KAAK,CAACoB,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACzB,IAAI,CAAC,CAAC0C,IAAI,CAAC,IAAI,CAAC,CACxD,MACF,IAAK,UAAU,CACbH,GAAG,CAAC,IAAI,CAAC,CAAGD,IAAI,CAAC9B,QAAQ,CAAG,IAAI,CAAG,IAAI,CACvC,MACF,IAAK,eAAe,CAClB+B,GAAG,CAAC,QAAQ,CAAC,CAAGD,IAAI,CAAC7B,aAAa,CAAGlD,UAAU,CAAC+E,IAAI,CAAC7B,aAAa,CAAC,CAAG,GAAG,CACzE,MACF,IAAK,WAAW,CACd8B,GAAG,CAAC,MAAM,CAAC,CAAGhF,UAAU,CAAC+E,IAAI,CAAC5B,SAAS,CAAC,CACxC,MACF,IAAK,WAAW,CACd6B,GAAG,CAAC,MAAM,CAAC,CAAGhF,UAAU,CAAC+E,IAAI,CAAC3B,SAAS,CAAC,CACxC,MACJ,CACF,CAAC,CAAC,CAEF,MAAO,CAAA4B,GAAG,CACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAI,EAAE,CAAGrF,IAAI,CAACsF,KAAK,CAACC,aAAa,CAACR,UAAU,CAAC,CAC/C,KAAM,CAAAS,EAAE,CAAGxF,IAAI,CAACsF,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChCzF,IAAI,CAACsF,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,MAAM,CAAC,CAE5C;AACA,KAAM,CAAAM,QAAQ,6BAAAnB,MAAA,CAAW,GAAI,CAAAoB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,MAAAtB,MAAA,CAAItC,YAAY,CAAE,CAChFlC,IAAI,CAAC+F,SAAS,CAACP,EAAE,CAAEG,QAAQ,CAAC,CAE5BjH,OAAO,CAAC4F,OAAO,CAAC,MAAM,CAAC,CACvBrC,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,MAAOqB,KAAK,CAAE,CACd5E,OAAO,CAAC4E,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAA0C,kBAAkB,CAAIlC,MAAY,EAAyB,CAC/D,CACEmC,GAAG,CAAE,eAAe,CACpBC,IAAI,cAAE/F,IAAA,CAACL,YAAY,GAAE,CAAC,CACtBqG,KAAK,CAAE,MAAM,CACbC,OAAO,CAAEA,CAAA,GAAM3B,mBAAmB,CAACX,MAAM,CAC3C,CAAC,CACD,CACEmC,GAAG,CAAE,cAAc,CACnBC,IAAI,CAAEpC,MAAM,CAACZ,QAAQ,cAAG/C,IAAA,CAACL,YAAY,GAAE,CAAC,cAAGK,IAAA,CAACJ,cAAc,GAAE,CAAC,CAC7DoG,KAAK,CAAErC,MAAM,CAACZ,QAAQ,CAAG,MAAM,CAAG,MAAM,CACxCkD,OAAO,CAAEA,CAAA,GAAM7B,kBAAkB,CAACT,MAAM,CAC1C,CAAC,CACD,CACEmC,GAAG,CAAE,QAAQ,CACbC,IAAI,cAAE/F,IAAA,CAACP,cAAc,GAAE,CAAC,CACxBuG,KAAK,CAAE,MAAM,CACbC,OAAO,CAAEA,CAAA,GAAM,CACb1H,OAAO,CAAC2H,IAAI,CAAC,SAAS,CAAC,CACzB,CACF,CAAC,CACF,CAED,KAAM,CAAAC,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBP,GAAG,CAAE,QAAQ,CACbQ,KAAK,CAAE,EAAE,CACTC,MAAM,CAAEA,CAACC,MAAc,CAAE7C,MAAY,QAAA8C,YAAA,oBACnCzG,IAAA,CAAChB,MAAM,EACL0H,IAAI,CAAE,EAAG,CACTC,GAAG,CAAEH,MAAO,CACZT,IAAI,cAAE/F,IAAA,CAACT,YAAY,GAAE,CAAE,CACvBqH,KAAK,CAAE,CAAEC,eAAe,CAAElD,MAAM,CAACZ,QAAQ,CAAG,SAAS,CAAG,SAAU,CAAE,CAAA+D,QAAA,CAEnE,CAACN,MAAM,IAAAC,YAAA,CAAI9C,MAAM,CAACpB,IAAI,UAAAkE,YAAA,iBAAXA,YAAA,CAAaM,MAAM,CAAC,CAAC,CAAC,EAC5B,CAAC,EAEb,CAAC,CACD,CACEX,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBP,GAAG,CAAE,UAAU,CACfQ,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACS,IAAY,CAAErD,MAAY,gBACjC3D,IAAA,CAAChC,MAAM,EAACiJ,IAAI,CAAC,MAAM,CAAChB,OAAO,CAAEA,CAAA,GAAMhC,UAAU,CAACN,MAAM,CAAE,CAAAmD,QAAA,CACnDE,IAAI,CACC,CAEZ,CAAC,CACD,CACEZ,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,MAAM,CACjBP,GAAG,CAAE,MAAM,CACXQ,KAAK,CAAE,GACT,CAAC,CACD,CACEF,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,OAAO,CAClBP,GAAG,CAAE,OAAO,CACZQ,KAAK,CAAE,GAAG,CACVY,QAAQ,CAAE,IACZ,CAAC,CACD,CACEd,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,OAAO,CAClBP,GAAG,CAAE,OAAO,CACZQ,KAAK,CAAE,GACT,CAAC,CACD,CACEF,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,YAAY,CACvBP,GAAG,CAAE,YAAY,CACjBQ,KAAK,CAAE,GAAG,CACVY,QAAQ,CAAE,IACZ,CAAC,CACD,CACEd,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,OAAO,CAClBP,GAAG,CAAE,OAAO,CACZQ,KAAK,CAAE,GAAG,CACVC,MAAM,CAAG5D,KAAa,eACpB3C,IAAA,CAAC/B,KAAK,EAACkJ,IAAI,MAAAL,QAAA,CACRnE,KAAK,CAACoB,GAAG,CAACC,IAAI,eACbhE,IAAA,CAAC3B,GAAG,EAAe+I,KAAK,CAAC,MAAM,CAAAN,QAAA,CAC5B9C,IAAI,CAACzB,IAAI,EADFyB,IAAI,CAAC3B,EAEV,CACN,CAAC,CACG,CAEX,CAAC,CACD,CACE+D,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBP,GAAG,CAAE,UAAU,CACfQ,KAAK,CAAE,EAAE,CACTC,MAAM,CAAGxD,QAAiB,eACxB/C,IAAA,CAAC3B,GAAG,EAAC+I,KAAK,CAAErE,QAAQ,CAAG,OAAO,CAAG,KAAM,CAAA+D,QAAA,CACpC/D,QAAQ,CAAG,IAAI,CAAG,IAAI,CACpB,CAET,CAAC,CACD,CACEqD,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,eAAe,CAC1BP,GAAG,CAAE,eAAe,CACpBQ,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGc,IAAY,EAAKA,IAAI,CAAGvH,UAAU,CAACuH,IAAI,CAAC,CAAG,GACtD,CAAC,CACD,CACEjB,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,WAAW,CACtBP,GAAG,CAAE,WAAW,CAChBQ,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGc,IAAY,EAAKvH,UAAU,CAACuH,IAAI,CAC3C,CAAC,CACD,CACEjB,KAAK,CAAE,IAAI,CACXN,GAAG,CAAE,QAAQ,CACbQ,KAAK,CAAE,GAAG,CACVgB,KAAK,CAAE,OAAgB,CACvBf,MAAM,CAAEA,CAACgB,CAAM,CAAE5D,MAAY,gBAC3BzD,KAAA,CAACjC,KAAK,EAACyI,IAAI,CAAC,OAAO,CAAAI,QAAA,eACjB9G,IAAA,CAACnB,OAAO,EAACuH,KAAK,CAAC,cAAI,CAAAU,QAAA,cACjB9G,IAAA,CAAChC,MAAM,EACLiJ,IAAI,CAAC,MAAM,CACXlB,IAAI,cAAE/F,IAAA,CAACV,WAAW,GAAE,CAAE,CACtB2G,OAAO,CAAEA,CAAA,GAAMhC,UAAU,CAACN,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACV3D,IAAA,CAACnB,OAAO,EAACuH,KAAK,CAAC,cAAI,CAAAU,QAAA,cACjB9G,IAAA,CAAChC,MAAM,EACLiJ,IAAI,CAAC,MAAM,CACXlB,IAAI,cAAE/F,IAAA,CAACZ,YAAY,GAAE,CAAE,CACvB6G,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAACC,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACV3D,IAAA,CAAC1B,UAAU,EACT8H,KAAK,CAAC,oEAAa,CACnBoB,SAAS,CAAEA,CAAA,GAAMtD,YAAY,CAACP,MAAM,CAAE,CACtC8D,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAZ,QAAA,cAEf9G,IAAA,CAACnB,OAAO,EAACuH,KAAK,CAAC,cAAI,CAAAU,QAAA,cACjB9G,IAAA,CAAChC,MAAM,EACLiJ,IAAI,CAAC,MAAM,CACXU,MAAM,MACN5B,IAAI,cAAE/F,IAAA,CAACX,cAAc,GAAE,CAAE,CAC1B,CAAC,CACK,CAAC,CACA,CAAC,cACbW,IAAA,CAAClB,QAAQ,EACP8I,IAAI,CAAE,CAAEC,KAAK,CAAEhC,kBAAkB,CAAClC,MAAM,CAAE,CAAE,CAC5CmE,OAAO,CAAE,CAAC,OAAO,CAAE,CAAAhB,QAAA,cAEnB9G,IAAA,CAAChC,MAAM,EAACiJ,IAAI,CAAC,MAAM,CAAClB,IAAI,cAAE/F,IAAA,CAACR,YAAY,GAAE,CAAE,CAAE,CAAC,CACtC,CAAC,EACN,CAEX,CAAC,CACF,CAED,mBACEU,KAAA,QAAA4G,QAAA,eACE5G,KAAA,CAAC9B,IAAI,EAAA0I,QAAA,eACH9G,IAAA,QAAK4G,KAAK,CAAE,CAAEmB,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,cAC/B5G,KAAA,CAACvB,GAAG,EAACqJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACC,KAAK,CAAC,QAAQ,CAAAnB,QAAA,eACnC9G,IAAA,CAACpB,GAAG,EAAAkI,QAAA,cACF9G,IAAA,CAACG,KAAK,EAAC+H,KAAK,CAAE,CAAE,CAACtB,KAAK,CAAE,CAAEuB,MAAM,CAAE,CAAE,CAAE,CAAArB,QAAA,CAAC,0BAAI,CAAO,CAAC,CAChD,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACwJ,IAAI,CAAC,MAAM,CAAAtB,QAAA,cACd5G,KAAA,CAACvB,GAAG,EAACqJ,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAACK,OAAO,CAAC,KAAK,CAAAvB,QAAA,eAChC9G,IAAA,CAACpB,GAAG,EAAAkI,QAAA,cACF9G,IAAA,CAACI,MAAM,EACLkI,WAAW,CAAC,oEAAa,CACzBC,UAAU,MACV3B,KAAK,CAAE,CAAEN,KAAK,CAAE,GAAI,CAAE,CACtBkC,QAAQ,CAAEpF,YAAa,CACxB,CAAC,CACC,CAAC,cACNpD,IAAA,CAACpB,GAAG,EAAAkI,QAAA,cACF5G,KAAA,CAAC/B,MAAM,EACLmK,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACV3B,KAAK,CAAE,CAAEN,KAAK,CAAE,GAAI,CAAE,CACtBmC,QAAQ,CAAEnF,gBAAiB,CAAAwD,QAAA,eAE3B9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,OAAO,CAAAyD,QAAA,CAAC,gCAAK,CAAQ,CAAC,cACpC9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,aAAa,CAAAyD,QAAA,CAAC,uBAAM,CAAQ,CAAC,cAC3C9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,WAAW,CAAAyD,QAAA,CAAC,iBAAK,CAAQ,CAAC,cACxC9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,kBAAkB,CAAAyD,QAAA,CAAC,0BAAI,CAAQ,CAAC,EACxC,CAAC,CACN,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAAAkI,QAAA,cACF5G,KAAA,CAAC/B,MAAM,EACLmK,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACV3B,KAAK,CAAE,CAAEN,KAAK,CAAE,GAAI,CAAE,CACtBmC,QAAQ,CAAElF,kBAAmB,CAAAuD,QAAA,eAE7B9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,QAAQ,CAAAyD,QAAA,CAAC,cAAE,CAAQ,CAAC,cAClC9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,UAAU,CAAAyD,QAAA,CAAC,cAAE,CAAQ,CAAC,EAC9B,CAAC,CACN,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAAAkI,QAAA,cACF9G,IAAA,CAAChC,MAAM,EACL+H,IAAI,cAAE/F,IAAA,CAACN,cAAc,GAAE,CAAE,CACzBuG,OAAO,CAAE9D,QAAS,CAAA2E,QAAA,CACnB,cAED,CAAQ,CAAC,CACN,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAAAkI,QAAA,cACF9G,IAAA,CAAChC,MAAM,EACL+H,IAAI,cAAE/F,IAAA,CAACP,cAAc,GAAE,CAAE,CACzBwG,OAAO,CAAEvB,YAAa,CAAAoC,QAAA,CACvB,cAED,CAAQ,CAAC,CACN,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAAAkI,QAAA,cACF9G,IAAA,CAAChC,MAAM,EACLiJ,IAAI,CAAC,SAAS,CACdlB,IAAI,cAAE/F,IAAA,CAACb,YAAY,GAAE,CAAE,CACvB8G,OAAO,CAAEzC,YAAa,CAAAsD,QAAA,CACvB,0BAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN9G,IAAA,CAACjC,KAAK,EACJoI,OAAO,CAAEA,OAAQ,CACjBuC,UAAU,CAAEjI,KAAM,CAClBE,OAAO,CAAEA,OAAQ,CACjBgI,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,KAAK,CAAEtI,KAAK,CAACuI,MAAM,CACnBC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACL,KAAK,CAAEM,KAAK,aAAAhF,MAAA,CAAUgF,KAAK,CAAC,CAAC,CAAC,MAAAhF,MAAA,CAAIgF,KAAK,CAAC,CAAC,CAAC,oBAAAhF,MAAA,CAAQ0E,KAAK,WACrE,CAAE,CACH,CAAC,EACE,CAAC,cAEP/I,IAAA,CAACxB,KAAK,EACJ4H,KAAK,CAAE/E,WAAW,CAAG,MAAM,CAAG,MAAO,CACrCiI,IAAI,CAAEnI,gBAAiB,CACvBoI,IAAI,CAAEhF,aAAc,CACpBiF,QAAQ,CAAEA,CAAA,GAAMpI,mBAAmB,CAAC,KAAK,CAAE,CAC3CkF,KAAK,CAAE,GAAI,CACXmD,cAAc,MAAA3C,QAAA,cAEd5G,KAAA,CAACzB,IAAI,EACHiL,IAAI,CAAEnI,QAAS,CACfoI,MAAM,CAAC,UAAU,CACjBC,aAAa,CAAE,CACb7G,QAAQ,CAAE,IACZ,CAAE,CAAA+D,QAAA,eAEF5G,KAAA,CAACvB,GAAG,EAACqJ,MAAM,CAAE,EAAG,CAAAlB,QAAA,eACd9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,cACZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,UAAU,CACfyD,KAAK,CAAC,oBAAK,CACX+D,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,QAAS,CAAC,CACrC,CAAE0L,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,EAAE,CAAE3L,OAAO,CAAE,eAAgB,CAAC,CAC7C,CAAAuI,QAAA,cAEF9G,IAAA,CAAC9B,KAAK,EAACoK,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACNtI,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,cACZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,MAAM,CACXyD,KAAK,CAAC,cAAI,CACV+D,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAAuI,QAAA,cAE9C9G,IAAA,CAAC9B,KAAK,EAACoK,WAAW,CAAC,gCAAO,CAAE,CAAC,CACpB,CAAC,CACT,CAAC,EACH,CAAC,cAENpI,KAAA,CAACvB,GAAG,EAACqJ,MAAM,CAAE,EAAG,CAAAlB,QAAA,eACd9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,cACZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,OAAO,CACZyD,KAAK,CAAC,cAAI,CACV+D,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CACpC,CAAE0I,IAAI,CAAE,OAAO,CAAE1I,OAAO,CAAE,YAAa,CAAC,CACxC,CAAAuI,QAAA,cAEF9G,IAAA,CAAC9B,KAAK,EAACoK,WAAW,CAAC,gCAAO,CAAE,CAAC,CACpB,CAAC,CACT,CAAC,cACNtI,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,cACZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,OAAO,CACZyD,KAAK,CAAC,oBAAK,CACX+D,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,QAAS,CAAC,CACrC,CAAE4L,OAAO,CAAE,eAAe,CAAE5L,OAAO,CAAE,WAAY,CAAC,CAClD,CAAAuI,QAAA,cAEF9G,IAAA,CAAC9B,KAAK,EAACoK,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,EACH,CAAC,cAENpI,KAAA,CAACvB,GAAG,EAACqJ,MAAM,CAAE,EAAG,CAAAlB,QAAA,eACd9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,cACZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,YAAY,CACjByD,KAAK,CAAC,cAAI,CAAAc,QAAA,cAEV9G,IAAA,CAAC9B,KAAK,EAACoK,WAAW,CAAC,gCAAO,CAAE,CAAC,CACpB,CAAC,CACT,CAAC,cACNtI,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,cACZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,SAAS,CACdyD,KAAK,CAAC,cAAI,CACV+D,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAAuI,QAAA,cAE9C5G,KAAA,CAAC/B,MAAM,EACLiM,IAAI,CAAC,UAAU,CACf9B,WAAW,CAAC,gCAAO,CACnBC,UAAU,MAAAzB,QAAA,eAEV9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,GAAG,CAAAyD,QAAA,CAAC,gCAAK,CAAQ,CAAC,cAChC9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,GAAG,CAAAyD,QAAA,CAAC,uBAAM,CAAQ,CAAC,cACjC9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,GAAG,CAAAyD,QAAA,CAAC,iBAAK,CAAQ,CAAC,cAChC9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,GAAG,CAAAyD,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAC/B9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,GAAG,CAAAyD,QAAA,CAAC,gCAAK,CAAQ,CAAC,cAChC9G,IAAA,CAACK,MAAM,EAACgD,KAAK,CAAC,GAAG,CAAAyD,QAAA,CAAC,gCAAK,CAAQ,CAAC,EAC1B,CAAC,CACA,CAAC,CACT,CAAC,EACH,CAAC,CAEL,CAACzF,WAAW,eACXnB,KAAA,CAACvB,GAAG,EAACqJ,MAAM,CAAE,EAAG,CAAAlB,QAAA,eACd9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,cACZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,UAAU,CACfyD,KAAK,CAAC,cAAI,CACV+D,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CACpC,CAAE0L,GAAG,CAAE,CAAC,CAAE1L,OAAO,CAAE,QAAS,CAAC,CAC7B,CAAAuI,QAAA,cAEF9G,IAAA,CAAC9B,KAAK,CAACmM,QAAQ,EAAC/B,WAAW,CAAC,gCAAO,CAAE,CAAC,CAC7B,CAAC,CACT,CAAC,cACNtI,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,cACZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,iBAAiB,CACtByD,KAAK,CAAC,0BAAM,CACZsE,YAAY,CAAE,CAAC,UAAU,CAAE,CAC3BP,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CACpCgM,IAAA,MAAC,CAAEC,aAAc,CAAC,CAAAD,IAAA,OAAM,CACtBE,SAASA,CAAClD,CAAC,CAAElE,KAAK,CAAE,CAClB,GAAI,CAACA,KAAK,EAAImH,aAAa,CAAC,UAAU,CAAC,GAAKnH,KAAK,CAAE,CACjD,MAAO,CAAAqH,OAAO,CAACC,OAAO,CAAC,CAAC,CAC1B,CACA,MAAO,CAAAD,OAAO,CAACE,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAChD,CACF,CAAC,EAAC,CACF,CAAA/D,QAAA,cAEF9G,IAAA,CAAC9B,KAAK,CAACmM,QAAQ,EAAC/B,WAAW,CAAC,gCAAO,CAAE,CAAC,CAC7B,CAAC,CACT,CAAC,EACH,CACN,cAEDtI,IAAA,CAACvB,IAAI,CAACqL,IAAI,EACRvH,IAAI,CAAC,UAAU,CACfyD,KAAK,CAAC,cAAI,CACV8E,aAAa,CAAC,SAAS,CAAAhE,QAAA,cAEvB9G,IAAA,CAACjB,MAAM,EAACgM,eAAe,CAAC,cAAI,CAACC,iBAAiB,CAAC,cAAI,CAAE,CAAC,CAC7C,CAAC,EACR,CAAC,CACF,CAAC,cAGRhL,IAAA,CAACxB,KAAK,EACJ4H,KAAK,CAAC,0BAAM,CACZkD,IAAI,CAAE7H,iBAAkB,CACxB+H,QAAQ,CAAEA,CAAA,GAAM9H,oBAAoB,CAAC,KAAK,CAAE,CAC5CuJ,MAAM,CAAE,cACNjL,IAAA,CAAChC,MAAM,EAAaiI,OAAO,CAAEA,CAAA,GAAMvE,oBAAoB,CAAC,KAAK,CAAE,CAAAoF,QAAA,CAAC,cAEhE,EAFY,OAEJ,CAAC,CACT,CACFR,KAAK,CAAE,GAAI,CAAAQ,QAAA,CAEVnF,WAAW,eACVzB,KAAA,QAAA4G,QAAA,eACE5G,KAAA,CAACvB,GAAG,EAACqJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACpB,KAAK,CAAE,CAAEmB,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,eACjD9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAAChB,MAAM,EACL0H,IAAI,CAAE,EAAG,CACTC,GAAG,CAAEhF,WAAW,CAAC6E,MAAO,CACxBT,IAAI,cAAE/F,IAAA,CAACT,YAAY,GAAE,CAAE,CACvBqH,KAAK,CAAE,CAAEC,eAAe,CAAElF,WAAW,CAACoB,QAAQ,CAAG,SAAS,CAAG,SAAU,CAAE,CAAA+D,QAAA,CAExE,CAACnF,WAAW,CAAC6E,MAAM,IAAAjG,iBAAA,CAAIoB,WAAW,CAACY,IAAI,UAAAhC,iBAAA,iBAAhBA,iBAAA,CAAkBwG,MAAM,CAAC,CAAC,CAAC,EAC7C,CAAC,CACN,CAAC,cACN7G,KAAA,CAACtB,GAAG,EAACiL,IAAI,CAAE,EAAG,CAAA/C,QAAA,eACZ5G,KAAA,CAACC,KAAK,EAAC+H,KAAK,CAAE,CAAE,CAACtB,KAAK,CAAE,CAAEuB,MAAM,CAAE,CAAE,CAAE,CAAArB,QAAA,EACnCnF,WAAW,CAACY,IAAI,cACjBvC,IAAA,CAAC3B,GAAG,EACF+I,KAAK,CAAEzF,WAAW,CAACoB,QAAQ,CAAG,OAAO,CAAG,KAAM,CAC9C6D,KAAK,CAAE,CAAEsE,UAAU,CAAE,CAAE,CAAE,CAAApE,QAAA,CAExBnF,WAAW,CAACoB,QAAQ,CAAG,IAAI,CAAG,IAAI,CAChC,CAAC,EACD,CAAC,cACR7C,KAAA,MAAG0G,KAAK,CAAE,CAAEQ,KAAK,CAAE,MAAM,CAAEe,MAAM,CAAE,WAAY,CAAE,CAAArB,QAAA,EAAC,GAC/C,CAACnF,WAAW,CAACW,QAAQ,EACrB,CAAC,EACD,CAAC,EACH,CAAC,cAENpC,KAAA,CAAChB,YAAY,EAACiM,QAAQ,MAACC,MAAM,CAAE,CAAE,CAAAtE,QAAA,eAC/B9G,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,oBAAK,CAAAc,QAAA,CAAEnF,WAAW,CAACW,QAAQ,CAAoB,CAAC,cACzEtC,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,cAAI,CAAAc,QAAA,CAAEnF,WAAW,CAACY,IAAI,CAAoB,CAAC,cACpEvC,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,cAAI,CAAAc,QAAA,CAAEnF,WAAW,CAACa,KAAK,CAAoB,CAAC,cACrExC,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,oBAAK,CAAAc,QAAA,CAAEnF,WAAW,CAACc,KAAK,CAAoB,CAAC,cACtEzC,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,cAAI,CAAAc,QAAA,CAAEnF,WAAW,CAACe,UAAU,EAAI,GAAG,CAAoB,CAAC,cACjF1C,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,cAAI,CAAAc,QAAA,cAC3B9G,IAAA,CAAC3B,GAAG,EAAC+I,KAAK,CAAEzF,WAAW,CAACoB,QAAQ,CAAG,OAAO,CAAG,KAAM,CAAA+D,QAAA,CAChDnF,WAAW,CAACoB,QAAQ,CAAG,IAAI,CAAG,IAAI,CAChC,CAAC,CACW,CAAC,cACpB/C,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,cAAI,CAAC6D,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACpC9G,IAAA,CAAC/B,KAAK,EAACkJ,IAAI,MAAAL,QAAA,CACRnF,WAAW,CAACgB,KAAK,CAACoB,GAAG,CAACC,IAAI,eACzBhE,IAAA,CAAC3B,GAAG,EAAe+I,KAAK,CAAC,MAAM,CAAAN,QAAA,CAC5B9C,IAAI,CAACzB,IAAI,EADFyB,IAAI,CAAC3B,EAEV,CACN,CAAC,CACG,CAAC,CACS,CAAC,cACpBrC,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,sCAAQ,CAAAc,QAAA,CAC9BnF,WAAW,CAACqB,aAAa,CAAGlD,UAAU,CAAC6B,WAAW,CAACqB,aAAa,CAAC,CAAG,GAAG,CACvD,CAAC,cACpBhD,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,0BAAM,CAAAc,QAAA,CAC5BhH,UAAU,CAAC6B,WAAW,CAACsB,SAAS,CAAC,CACjB,CAAC,cACpBjD,IAAA,CAACd,YAAY,CAAC4K,IAAI,EAAC9D,KAAK,CAAC,0BAAM,CAAC6D,IAAI,CAAE,CAAE,CAAA/C,QAAA,CACrChH,UAAU,CAAC6B,WAAW,CAACuB,SAAS,CAAC,CACjB,CAAC,EACR,CAAC,EACZ,CACN,CACI,CAAC,cAGRlD,IAAA,CAACxB,KAAK,EACJ4H,KAAK,CAAC,0BAAM,CACZkD,IAAI,CAAEzH,kBAAmB,CACzB0H,IAAI,CAAE5E,aAAc,CACpB6E,QAAQ,CAAEA,CAAA,GAAM1H,qBAAqB,CAAC,KAAK,CAAE,CAC7C2F,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CACfpB,KAAK,CAAE,GAAI,CAAAQ,QAAA,cAEX9G,IAAA,QAAA8G,QAAA,cACE5G,KAAA,CAACzB,IAAI,EAACkL,MAAM,CAAC,UAAU,CAAA7C,QAAA,eACrB9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EAAC9D,KAAK,CAAC,0BAAM,CAAAc,QAAA,cACrB5G,KAAA,CAAC/B,MAAM,EACLkF,KAAK,CAAEtB,YAAa,CACpB0G,QAAQ,CAAEzG,eAAgB,CAC1B4E,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CAAAQ,QAAA,eAEzB9G,IAAA,CAAC7B,MAAM,CAACkC,MAAM,EAACgD,KAAK,CAAC,MAAM,CAAAyD,QAAA,CAAC,eAAa,CAAe,CAAC,cACzD9G,IAAA,CAAC7B,MAAM,CAACkC,MAAM,EAACgD,KAAK,CAAC,KAAK,CAAAyD,QAAA,CAAC,YAAU,CAAe,CAAC,EAC/C,CAAC,CACA,CAAC,cAEZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EAAC9D,KAAK,CAAC,0BAAM,CAAAc,QAAA,cACrB9G,IAAA,CAACf,QAAQ,CAACoM,KAAK,EACbhI,KAAK,CAAEpB,YAAa,CACpBwG,QAAQ,CAAEvG,eAAgB,CAC1B0E,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CAAAQ,QAAA,cAEzB5G,KAAA,CAACvB,GAAG,EAACqJ,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAlB,QAAA,eAClB9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,UAAU,CAAAyD,QAAA,CAAC,oBAAG,CAAU,CAAC,CACtC,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,MAAM,CAAAyD,QAAA,CAAC,cAAE,CAAU,CAAC,CACjC,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,OAAO,CAAAyD,QAAA,CAAC,cAAE,CAAU,CAAC,CAClC,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,OAAO,CAAAyD,QAAA,CAAC,oBAAG,CAAU,CAAC,CACnC,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,YAAY,CAAAyD,QAAA,CAAC,cAAE,CAAU,CAAC,CACvC,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,OAAO,CAAAyD,QAAA,CAAC,cAAE,CAAU,CAAC,CAClC,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,UAAU,CAAAyD,QAAA,CAAC,cAAE,CAAU,CAAC,CACrC,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,eAAe,CAAAyD,QAAA,CAAC,sCAAM,CAAU,CAAC,CAC9C,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,WAAW,CAAAyD,QAAA,CAAC,0BAAI,CAAU,CAAC,CACxC,CAAC,cACN9G,IAAA,CAACpB,GAAG,EAACiL,IAAI,CAAE,CAAE,CAAA/C,QAAA,cACX9G,IAAA,CAACf,QAAQ,EAACoE,KAAK,CAAC,WAAW,CAAAyD,QAAA,CAAC,0BAAI,CAAU,CAAC,CACxC,CAAC,EACH,CAAC,CACQ,CAAC,CACR,CAAC,cAEZ9G,IAAA,CAACvB,IAAI,CAACqL,IAAI,EAAAhD,QAAA,cACR9G,IAAA,QAAK4G,KAAK,CAAE,CAAE0E,OAAO,CAAE,EAAE,CAAEzE,eAAe,CAAE,SAAS,CAAE0E,YAAY,CAAE,CAAE,CAAE,CAAAzE,QAAA,cACvE5G,KAAA,MAAG0G,KAAK,CAAE,CAAEuB,MAAM,CAAE,CAAC,CAAEf,KAAK,CAAE,MAAO,CAAE,CAAAN,QAAA,EAAC,iCAChC,CAACrG,KAAK,CAACuI,MAAM,CAAC,iCACtB,EAAG,CAAC,CACD,CAAC,CACG,CAAC,EACR,CAAC,CACJ,CAAC,CACD,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1I,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}