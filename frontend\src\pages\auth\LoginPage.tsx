import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Alert,
  Checkbox,
  Divider,
  Row,
  Col,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  LoginOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { login, clearError } from '../../store/slices/authSlice';
import { ROUTES } from '../../constants';
import { LoadingSpinner } from '../../components';
import { errorHandler, ErrorType } from '../../utils/errorHandler';

const { Title, Text } = Typography;

interface LoginFormData {
  username: string;
  password: string;
  remember: boolean;
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { loading, error, isAuthenticated } = useAppSelector(state => state.auth);

  // 如果已经登录，重定向到目标页面或仪表板
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || ROUTES.DASHBOARD;
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // 清除错误信息
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  // 处理登录
  const handleLogin = async (values: LoginFormData) => {
    try {
      await dispatch(login({
        username: values.username,
        password: values.password,
      })).unwrap();
      
      // 登录成功后会通过useEffect重定向
    } catch (error: any) {
      // 使用新的错误处理器
      errorHandler.handleError({
        type: ErrorType.BUSINESS,
        message: error.message || '登录失败，请检查用户名和密码',
        details: error,
        timestamp: Date.now()
      });
    }
  };

  // 演示账号登录
  const handleDemoLogin = (role: string) => {
    const demoAccounts = {
      admin: { username: 'admin', password: 'admin123' },
      bom_manager: { username: 'bom_manager', password: 'bom123' },
      sales_pmc: { username: 'sales_pmc', password: 'sales123' },
      purchase_manager: { username: 'purchase_manager', password: 'purchase123' },
      warehouse_manager: { username: 'warehouse_manager', password: 'warehouse123' },
      finance_manager: { username: 'finance_manager', password: 'finance123' },
      service_technician: { username: 'service_tech', password: 'service123' },
      operator: { username: 'operator', password: 'operator123' },
    };

    const account = demoAccounts[role as keyof typeof demoAccounts];
    if (account) {
      form.setFieldsValue(account);
      handleLogin({ ...account, remember: false });
    }
  };

  return (
    <>
      {loading && (
        <LoadingSpinner
          fullscreen
          overlay
          text="正在登录..."
          size="large"
        />
      )}
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
      }}>
      <Row gutter={[32, 32]} style={{ width: '100%', maxWidth: 1200 }}>
        {/* 左侧：系统介绍 */}
        <Col xs={24} lg={12}>
          <div style={{ color: 'white', textAlign: 'center' }}>
            <Title level={1} style={{ color: 'white', marginBottom: 24 }}>
              Link-BOM-S
            </Title>
            <Title level={3} style={{ color: 'white', fontWeight: 'normal', marginBottom: 32 }}>
              天线行业BOM管理系统
            </Title>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: 16 }}>
                专为天线行业小微制造企业设计的综合BOM管理系统
              </Text>
              <div style={{ textAlign: 'left', maxWidth: 400, margin: '0 auto' }}>
                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>
                  ✓ 核心BOM（150%）+ 订单快速派生（100%）
                </Text>
                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>
                  ✓ 按单合单采购 + 包装/MOQ取整
                </Text>
                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>
                  ✓ 长度型切割优化 + 余料台账
                </Text>
                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>
                  ✓ 服务BOM + 成本透明度
                </Text>
              </div>
            </Space>
          </div>
        </Col>

        {/* 右侧：登录表单 */}
        <Col xs={24} lg={12}>
          <Card
            style={{
              maxWidth: 400,
              margin: '0 auto',
              borderRadius: 12,
              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: 32 }}>
              <Title level={3} style={{ marginBottom: 8 }}>
                用户登录
              </Title>
              <Text type="secondary">
                请输入您的账号和密码
              </Text>
            </div>

            {error && (
              <Alert
                message={error}
                type="error"
                showIcon
                style={{ marginBottom: 24 }}
                closable
                onClose={() => dispatch(clearError())}
              />
            )}

            <Form
              form={form}
              name="login"
              onFinish={handleLogin}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="用户名"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                />
              </Form.Item>

              <Form.Item name="remember" valuePropName="checked">
                <Checkbox>记住我</Checkbox>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<LoginOutlined />}
                  block
                >
                  登录
                </Button>
              </Form.Item>
            </Form>

            <Divider>演示账号</Divider>

            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Button
                    size="small"
                    block
                    onClick={() => handleDemoLogin('admin')}
                  >
                    系统管理员
                  </Button>
                </Col>
                <Col span={12}>
                  <Button
                    size="small"
                    block
                    onClick={() => handleDemoLogin('bom_manager')}
                  >
                    BOM管理员
                  </Button>
                </Col>
              </Row>
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Button
                    size="small"
                    block
                    onClick={() => handleDemoLogin('sales_pmc')}
                  >
                    销售/PMC
                  </Button>
                </Col>
                <Col span={12}>
                  <Button
                    size="small"
                    block
                    onClick={() => handleDemoLogin('purchase_manager')}
                  >
                    采购经理
                  </Button>
                </Col>
              </Row>
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Button
                    size="small"
                    block
                    onClick={() => handleDemoLogin('warehouse_manager')}
                  >
                    仓库经理
                  </Button>
                </Col>
                <Col span={12}>
                  <Button
                    size="small"
                    block
                    onClick={() => handleDemoLogin('finance_manager')}
                  >
                    财务经理
                  </Button>
                </Col>
              </Row>
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Button
                    size="small"
                    block
                    onClick={() => handleDemoLogin('service_technician')}
                  >
                    服务技术员
                  </Button>
                </Col>
                <Col span={12}>
                  <Button
                    size="small"
                    block
                    onClick={() => handleDemoLogin('operator')}
                  >
                    操作员
                  </Button>
                </Col>
              </Row>
            </Space>

            <div style={{ textAlign: 'center', marginTop: 24 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                © 2024 Link-BOM-S. 天线行业BOM管理系统
              </Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
    </>
  );
};

export default LoginPage;
