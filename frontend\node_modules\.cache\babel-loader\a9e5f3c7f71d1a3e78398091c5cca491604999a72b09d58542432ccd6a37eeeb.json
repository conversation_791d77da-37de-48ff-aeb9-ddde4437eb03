{"ast": null, "code": "// API 基础配置\nexport const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api';\n\n// 路由常量\nexport const ROUTES = {\n  LOGIN: '/login',\n  DASHBOARD: '/dashboard',\n  // BOM管理\n  CORE_BOM: '/bom/core',\n  CORE_BOM_CREATE: '/bom/core/create',\n  CORE_BOM_EDIT: '/bom/core/edit/:id',\n  CORE_BOM_VIEW: '/bom/core/view/:id',\n  ORDER_BOM: '/bom/order',\n  ORDER_BOM_CREATE: '/bom/order/create',\n  ORDER_BOM_DERIVE: '/bom/order/derive/:coreBomId',\n  ORDER_BOM_VIEW: '/bom/order/view/:id',\n  // 物料管理\n  MATERIALS: '/materials',\n  MATERIALS_CREATE: '/materials/create',\n  MATERIALS_EDIT: '/materials/edit/:id',\n  // 库存管理\n  INVENTORY: '/inventory',\n  INVENTORY_RECEIVE: '/inventory/receive',\n  INVENTORY_ISSUE: '/inventory/issue',\n  INVENTORY_ADJUST: '/inventory/adjust',\n  REMNANTS: '/inventory/remnants',\n  CUTTING_PLAN: '/inventory/cutting-plan',\n  // 采购管理\n  PURCHASE: '/purchase',\n  PURCHASE_CREATE: '/purchase/create',\n  PURCHASE_EDIT: '/purchase/edit/:id',\n  PURCHASE_VIEW: '/purchase/view/:id',\n  PURCHASE_REQUISITION: '/purchase/requisition',\n  MRP_CALCULATION: '/purchase/mrp',\n  PURCHASE_OPTIMIZATION: '/purchase/optimization',\n  // 成本管理\n  COST_ANALYSIS: '/cost/analysis',\n  COST_REPORTS: '/cost/reports',\n  WASTE_TRACKING: '/cost/waste',\n  STANDARD_COST: '/cost/standard',\n  // 服务管理\n  SERVICE_BOM: '/service/bom',\n  DEVICE_ARCHIVE: '/service/devices',\n  MAINTENANCE: '/service/maintenance',\n  // ECN管理\n  ECN: '/ecn',\n  ECN_CREATE: '/ecn/create',\n  ECN_REVIEW: '/ecn/review/:id',\n  // 报告和分析\n  REPORTS: '/reports',\n  DASHBOARD_CONFIG: '/reports/dashboard',\n  // 系统管理\n  USERS: '/system/users',\n  ROLES: '/system/roles',\n  PERMISSIONS: '/system/permissions',\n  SYSTEM_CONFIG: '/system/config',\n  // 移动端\n  MOBILE: '/mobile',\n  MOBILE_SCAN: '/mobile/scan',\n  MOBILE_INVENTORY: '/mobile/inventory'\n};\n\n// 用户角色常量\nexport const USER_ROLES = {\n  ADMIN: 'ADMIN',\n  BOM_MANAGER: 'BOM_MANAGER',\n  SALES_PMC: 'SALES_PMC',\n  PURCHASE_MANAGER: 'PURCHASE_MANAGER',\n  PRODUCTION_PLANNER: 'PRODUCTION_PLANNER',\n  WAREHOUSE_MANAGER: 'WAREHOUSE_MANAGER',\n  FINANCE_MANAGER: 'FINANCE_MANAGER',\n  SERVICE_TECHNICIAN: 'SERVICE_TECHNICIAN',\n  QUALITY_MANAGER: 'QUALITY_MANAGER',\n  OPERATOR: 'OPERATOR'\n};\n\n// 权限常量\nexport const PERMISSIONS = {\n  // BOM权限\n  BOM_VIEW: 'BOM_VIEW',\n  BOM_CREATE: 'BOM_CREATE',\n  BOM_EDIT: 'BOM_EDIT',\n  BOM_DELETE: 'BOM_DELETE',\n  BOM_FREEZE: 'BOM_FREEZE',\n  BOM_APPROVE: 'BOM_APPROVE',\n  // 物料权限\n  MATERIAL_VIEW: 'MATERIAL_VIEW',\n  MATERIAL_CREATE: 'MATERIAL_CREATE',\n  MATERIAL_EDIT: 'MATERIAL_EDIT',\n  MATERIAL_DELETE: 'MATERIAL_DELETE',\n  // 库存权限\n  INVENTORY_VIEW: 'INVENTORY_VIEW',\n  INVENTORY_RECEIVE: 'INVENTORY_RECEIVE',\n  INVENTORY_ISSUE: 'INVENTORY_ISSUE',\n  INVENTORY_ADJUST: 'INVENTORY_ADJUST',\n  // 采购权限\n  PURCHASE_VIEW: 'PURCHASE_VIEW',\n  PURCHASE_CREATE: 'PURCHASE_CREATE',\n  PURCHASE_APPROVE: 'PURCHASE_APPROVE',\n  // 成本权限\n  COST_VIEW: 'COST_VIEW',\n  COST_ANALYSIS: 'COST_ANALYSIS',\n  // ECN权限\n  ECN_VIEW: 'ECN_VIEW',\n  ECN_CREATE: 'ECN_CREATE',\n  ECN_APPROVE: 'ECN_APPROVE',\n  // 系统权限\n  SYSTEM_CONFIG: 'SYSTEM_CONFIG',\n  USER_MANAGE: 'USER_MANAGE',\n  ROLE_MANAGE: 'ROLE_MANAGE'\n};\n\n// 状态常量\nexport const BOM_STATUS = {\n  DRAFT: 'DRAFT',\n  ACTIVE: 'ACTIVE',\n  FROZEN: 'FROZEN',\n  OBSOLETE: 'OBSOLETE'\n};\nexport const ORDER_STATUS = {\n  DRAFT: 'DRAFT',\n  CONFIRMED: 'CONFIRMED',\n  FROZEN: 'FROZEN',\n  CANCELLED: 'CANCELLED'\n};\nexport const ECN_STATUS = {\n  DRAFT: 'DRAFT',\n  REVIEW: 'REVIEW',\n  APPROVED: 'APPROVED',\n  REJECTED: 'REJECTED',\n  IMPLEMENTED: 'IMPLEMENTED'\n};\nexport const INVENTORY_STATUS = {\n  AVAILABLE: 'AVAILABLE',\n  RESERVED: 'RESERVED',\n  EXPIRED: 'EXPIRED',\n  DAMAGED: 'DAMAGED'\n};\n\n// 业务常量\nexport const MATERIAL_CATEGORIES = [{\n  value: '天线',\n  label: '天线'\n}, {\n  value: '射频器件',\n  label: '射频器件'\n}, {\n  value: '结构件',\n  label: '结构件'\n}, {\n  value: '电子元器件',\n  label: '电子元器件'\n}, {\n  value: '包装材料',\n  label: '包装材料'\n}, {\n  value: '辅助材料',\n  label: '辅助材料'\n}];\nexport const UNITS = ['PCS',\n// 个\n'SET',\n// 套\n'M',\n// 米\n'KG',\n// 千克\n'L',\n// 升\n'M2',\n// 平方米\n'M3' // 立方米\n];\nexport const MATERIAL_UNITS = [{\n  value: 'PCS',\n  label: '个'\n}, {\n  value: 'SET',\n  label: '套'\n}, {\n  value: 'M',\n  label: '米'\n}, {\n  value: 'KG',\n  label: '千克'\n}, {\n  value: 'L',\n  label: '升'\n}, {\n  value: 'M2',\n  label: '平方米'\n}, {\n  value: 'M3',\n  label: '立方米'\n}];\nexport const WASTE_CATEGORIES = {\n  PACKAGING: 'PACKAGING',\n  MOQ: 'MOQ',\n  CUTTING: 'CUTTING',\n  EXPIRY: 'EXPIRY'\n};\nexport const PRIORITY_LEVELS = {\n  LOW: 'LOW',\n  MEDIUM: 'MEDIUM',\n  HIGH: 'HIGH',\n  URGENT: 'URGENT'\n};\n\n// 表格配置\nexport const PAGE_SIZES = [10, 20, 50, 100];\nexport const DEFAULT_PAGE_SIZE = 20;\n\n// 主题配置\nexport const THEME_CONFIG = {\n  primaryColor: '#1890ff',\n  successColor: '#52c41a',\n  warningColor: '#faad14',\n  errorColor: '#f5222d',\n  borderRadius: 6\n};\n\n// 文件上传配置\nexport const UPLOAD_CONFIG = {\n  MAX_FILE_SIZE: 10 * 1024 * 1024,\n  // 10MB\n  ALLOWED_FILE_TYPES: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv', 'application/pdf']\n};\n\n// 本地存储键名\nexport const STORAGE_KEYS = {\n  TOKEN: 'auth_token',\n  USER_INFO: 'user_info',\n  CURRENT_ROLE: 'current_role',\n  THEME: 'theme',\n  LANGUAGE: 'language',\n  DASHBOARD_CONFIG: 'dashboard_config'\n};\n\n// 消息类型\nexport const MESSAGE_TYPES = {\n  SUCCESS: 'success',\n  ERROR: 'error',\n  WARNING: 'warning',\n  INFO: 'info'\n};\n\n// 日期格式\nexport const DATE_FORMATS = {\n  DATE: 'YYYY-MM-DD',\n  DATETIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME: 'HH:mm:ss',\n  MONTH: 'YYYY-MM',\n  YEAR: 'YYYY'\n};\n\n// 数值格式\nexport const NUMBER_FORMATS = {\n  CURRENCY: '¥0,0.00',\n  PERCENTAGE: '0.00%',\n  INTEGER: '0,0',\n  DECIMAL: '0,0.00'\n};\n\n// 正则表达式\nexport const REGEX_PATTERNS = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PHONE: /^1[3-9]\\d{9}$/,\n  CODE: /^[A-Z0-9-]+$/,\n  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/\n};\n\n// 错误代码\nexport const ERROR_CODES = {\n  UNAUTHORIZED: 401,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  VALIDATION_ERROR: 422,\n  SERVER_ERROR: 500\n};\n\n// 操作类型\nexport const OPERATION_TYPES = {\n  CREATE: 'CREATE',\n  UPDATE: 'UPDATE',\n  DELETE: 'DELETE',\n  APPROVE: 'APPROVE',\n  REJECT: 'REJECT',\n  FREEZE: 'FREEZE',\n  ACTIVATE: 'ACTIVATE'\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "ROUTES", "LOGIN", "DASHBOARD", "CORE_BOM", "CORE_BOM_CREATE", "CORE_BOM_EDIT", "CORE_BOM_VIEW", "ORDER_BOM", "ORDER_BOM_CREATE", "ORDER_BOM_DERIVE", "ORDER_BOM_VIEW", "MATERIALS", "MATERIALS_CREATE", "MATERIALS_EDIT", "INVENTORY", "INVENTORY_RECEIVE", "INVENTORY_ISSUE", "INVENTORY_ADJUST", "REMNANTS", "CUTTING_PLAN", "PURCHASE", "PURCHASE_CREATE", "PURCHASE_EDIT", "PURCHASE_VIEW", "PURCHASE_REQUISITION", "MRP_CALCULATION", "PURCHASE_OPTIMIZATION", "COST_ANALYSIS", "COST_REPORTS", "WASTE_TRACKING", "STANDARD_COST", "SERVICE_BOM", "DEVICE_ARCHIVE", "MAINTENANCE", "ECN", "ECN_CREATE", "ECN_REVIEW", "REPORTS", "DASHBOARD_CONFIG", "USERS", "ROLES", "PERMISSIONS", "SYSTEM_CONFIG", "MOBILE", "MOBILE_SCAN", "MOBILE_INVENTORY", "USER_ROLES", "ADMIN", "BOM_MANAGER", "SALES_PMC", "PURCHASE_MANAGER", "PRODUCTION_PLANNER", "WAREHOUSE_MANAGER", "FINANCE_MANAGER", "SERVICE_TECHNICIAN", "QUALITY_MANAGER", "OPERATOR", "BOM_VIEW", "BOM_CREATE", "BOM_EDIT", "BOM_DELETE", "BOM_FREEZE", "BOM_APPROVE", "MATERIAL_VIEW", "MATERIAL_CREATE", "MATERIAL_EDIT", "MATERIAL_DELETE", "INVENTORY_VIEW", "PURCHASE_APPROVE", "COST_VIEW", "ECN_VIEW", "ECN_APPROVE", "USER_MANAGE", "ROLE_MANAGE", "BOM_STATUS", "DRAFT", "ACTIVE", "FROZEN", "OBSOLETE", "ORDER_STATUS", "CONFIRMED", "CANCELLED", "ECN_STATUS", "REVIEW", "APPROVED", "REJECTED", "IMPLEMENTED", "INVENTORY_STATUS", "AVAILABLE", "RESERVED", "EXPIRED", "DAMAGED", "MATERIAL_CATEGORIES", "value", "label", "UNITS", "MATERIAL_UNITS", "WASTE_CATEGORIES", "PACKAGING", "MOQ", "CUTTING", "EXPIRY", "PRIORITY_LEVELS", "LOW", "MEDIUM", "HIGH", "URGENT", "PAGE_SIZES", "DEFAULT_PAGE_SIZE", "THEME_CONFIG", "primaryColor", "successColor", "warningColor", "errorColor", "borderRadius", "UPLOAD_CONFIG", "MAX_FILE_SIZE", "ALLOWED_FILE_TYPES", "STORAGE_KEYS", "TOKEN", "USER_INFO", "CURRENT_ROLE", "THEME", "LANGUAGE", "MESSAGE_TYPES", "SUCCESS", "ERROR", "WARNING", "INFO", "DATE_FORMATS", "DATE", "DATETIME", "TIME", "MONTH", "YEAR", "NUMBER_FORMATS", "CURRENCY", "PERCENTAGE", "INTEGER", "DECIMAL", "REGEX_PATTERNS", "EMAIL", "PHONE", "CODE", "PASSWORD", "ERROR_CODES", "UNAUTHORIZED", "FORBIDDEN", "NOT_FOUND", "VALIDATION_ERROR", "SERVER_ERROR", "OPERATION_TYPES", "CREATE", "UPDATE", "DELETE", "APPROVE", "REJECT", "FREEZE", "ACTIVATE"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/constants/index.ts"], "sourcesContent": ["// API 基础配置\nexport const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api';\n\n// 路由常量\nexport const ROUTES = {\n  LOGIN: '/login',\n  DASHBOARD: '/dashboard',\n  \n  // BOM管理\n  CORE_BOM: '/bom/core',\n  CORE_BOM_CREATE: '/bom/core/create',\n  CORE_BOM_EDIT: '/bom/core/edit/:id',\n  CORE_BOM_VIEW: '/bom/core/view/:id',\n  \n  ORDER_BOM: '/bom/order',\n  ORDER_BOM_CREATE: '/bom/order/create',\n  ORDER_BOM_DERIVE: '/bom/order/derive/:coreBomId',\n  ORDER_BOM_VIEW: '/bom/order/view/:id',\n  \n  // 物料管理\n  MATERIALS: '/materials',\n  MATERIALS_CREATE: '/materials/create',\n  MATERIALS_EDIT: '/materials/edit/:id',\n  \n  // 库存管理\n  INVENTORY: '/inventory',\n  INVENTORY_RECEIVE: '/inventory/receive',\n  INVENTORY_ISSUE: '/inventory/issue',\n  INVENTORY_ADJUST: '/inventory/adjust',\n  \n  REMNANTS: '/inventory/remnants',\n  CUTTING_PLAN: '/inventory/cutting-plan',\n  \n  // 采购管理\n  PURCHASE: '/purchase',\n  PURCHASE_CREATE: '/purchase/create',\n  PURCHASE_EDIT: '/purchase/edit/:id',\n  PURCHASE_VIEW: '/purchase/view/:id',\n  PURCHASE_REQUISITION: '/purchase/requisition',\n  MRP_CALCULATION: '/purchase/mrp',\n  PURCHASE_OPTIMIZATION: '/purchase/optimization',\n  \n  // 成本管理\n  COST_ANALYSIS: '/cost/analysis',\n  COST_REPORTS: '/cost/reports',\n  WASTE_TRACKING: '/cost/waste',\n  STANDARD_COST: '/cost/standard',\n  \n  // 服务管理\n  SERVICE_BOM: '/service/bom',\n  DEVICE_ARCHIVE: '/service/devices',\n  MAINTENANCE: '/service/maintenance',\n  \n  // ECN管理\n  ECN: '/ecn',\n  ECN_CREATE: '/ecn/create',\n  ECN_REVIEW: '/ecn/review/:id',\n  \n  // 报告和分析\n  REPORTS: '/reports',\n  DASHBOARD_CONFIG: '/reports/dashboard',\n  \n  // 系统管理\n  USERS: '/system/users',\n  ROLES: '/system/roles',\n  PERMISSIONS: '/system/permissions',\n  SYSTEM_CONFIG: '/system/config',\n  \n  // 移动端\n  MOBILE: '/mobile',\n  MOBILE_SCAN: '/mobile/scan',\n  MOBILE_INVENTORY: '/mobile/inventory',\n} as const;\n\n// 用户角色常量\nexport const USER_ROLES = {\n  ADMIN: 'ADMIN',\n  BOM_MANAGER: 'BOM_MANAGER',\n  SALES_PMC: 'SALES_PMC',\n  PURCHASE_MANAGER: 'PURCHASE_MANAGER',\n  PRODUCTION_PLANNER: 'PRODUCTION_PLANNER',\n  WAREHOUSE_MANAGER: 'WAREHOUSE_MANAGER',\n  FINANCE_MANAGER: 'FINANCE_MANAGER',\n  SERVICE_TECHNICIAN: 'SERVICE_TECHNICIAN',\n  QUALITY_MANAGER: 'QUALITY_MANAGER',\n  OPERATOR: 'OPERATOR',\n} as const;\n\n// 权限常量\nexport const PERMISSIONS = {\n  // BOM权限\n  BOM_VIEW: 'BOM_VIEW',\n  BOM_CREATE: 'BOM_CREATE',\n  BOM_EDIT: 'BOM_EDIT',\n  BOM_DELETE: 'BOM_DELETE',\n  BOM_FREEZE: 'BOM_FREEZE',\n  BOM_APPROVE: 'BOM_APPROVE',\n  \n  // 物料权限\n  MATERIAL_VIEW: 'MATERIAL_VIEW',\n  MATERIAL_CREATE: 'MATERIAL_CREATE',\n  MATERIAL_EDIT: 'MATERIAL_EDIT',\n  MATERIAL_DELETE: 'MATERIAL_DELETE',\n  \n  // 库存权限\n  INVENTORY_VIEW: 'INVENTORY_VIEW',\n  INVENTORY_RECEIVE: 'INVENTORY_RECEIVE',\n  INVENTORY_ISSUE: 'INVENTORY_ISSUE',\n  INVENTORY_ADJUST: 'INVENTORY_ADJUST',\n  \n  // 采购权限\n  PURCHASE_VIEW: 'PURCHASE_VIEW',\n  PURCHASE_CREATE: 'PURCHASE_CREATE',\n  PURCHASE_APPROVE: 'PURCHASE_APPROVE',\n  \n  // 成本权限\n  COST_VIEW: 'COST_VIEW',\n  COST_ANALYSIS: 'COST_ANALYSIS',\n  \n  // ECN权限\n  ECN_VIEW: 'ECN_VIEW',\n  ECN_CREATE: 'ECN_CREATE',\n  ECN_APPROVE: 'ECN_APPROVE',\n  \n  // 系统权限\n  SYSTEM_CONFIG: 'SYSTEM_CONFIG',\n  USER_MANAGE: 'USER_MANAGE',\n  ROLE_MANAGE: 'ROLE_MANAGE',\n} as const;\n\n// 状态常量\nexport const BOM_STATUS = {\n  DRAFT: 'DRAFT',\n  ACTIVE: 'ACTIVE',\n  FROZEN: 'FROZEN',\n  OBSOLETE: 'OBSOLETE',\n} as const;\n\nexport const ORDER_STATUS = {\n  DRAFT: 'DRAFT',\n  CONFIRMED: 'CONFIRMED',\n  FROZEN: 'FROZEN',\n  CANCELLED: 'CANCELLED',\n} as const;\n\nexport const ECN_STATUS = {\n  DRAFT: 'DRAFT',\n  REVIEW: 'REVIEW',\n  APPROVED: 'APPROVED',\n  REJECTED: 'REJECTED',\n  IMPLEMENTED: 'IMPLEMENTED',\n} as const;\n\nexport const INVENTORY_STATUS = {\n  AVAILABLE: 'AVAILABLE',\n  RESERVED: 'RESERVED',\n  EXPIRED: 'EXPIRED',\n  DAMAGED: 'DAMAGED',\n} as const;\n\n// 业务常量\nexport const MATERIAL_CATEGORIES = [\n  { value: '天线', label: '天线' },\n  { value: '射频器件', label: '射频器件' },\n  { value: '结构件', label: '结构件' },\n  { value: '电子元器件', label: '电子元器件' },\n  { value: '包装材料', label: '包装材料' },\n  { value: '辅助材料', label: '辅助材料' },\n] as const;\n\nexport const UNITS = [\n  'PCS', // 个\n  'SET', // 套\n  'M',   // 米\n  'KG',  // 千克\n  'L',   // 升\n  'M2',  // 平方米\n  'M3',  // 立方米\n] as const;\n\nexport const MATERIAL_UNITS = [\n  { value: 'PCS', label: '个' },\n  { value: 'SET', label: '套' },\n  { value: 'M', label: '米' },\n  { value: 'KG', label: '千克' },\n  { value: 'L', label: '升' },\n  { value: 'M2', label: '平方米' },\n  { value: 'M3', label: '立方米' },\n] as const;\n\nexport const WASTE_CATEGORIES = {\n  PACKAGING: 'PACKAGING',\n  MOQ: 'MOQ',\n  CUTTING: 'CUTTING',\n  EXPIRY: 'EXPIRY',\n} as const;\n\nexport const PRIORITY_LEVELS = {\n  LOW: 'LOW',\n  MEDIUM: 'MEDIUM',\n  HIGH: 'HIGH',\n  URGENT: 'URGENT',\n} as const;\n\n// 表格配置\nexport const PAGE_SIZES = [10, 20, 50, 100] as const;\nexport const DEFAULT_PAGE_SIZE = 20;\n\n// 主题配置\nexport const THEME_CONFIG = {\n  primaryColor: '#1890ff',\n  successColor: '#52c41a',\n  warningColor: '#faad14',\n  errorColor: '#f5222d',\n  borderRadius: 6,\n} as const;\n\n// 文件上传配置\nexport const UPLOAD_CONFIG = {\n  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB\n  ALLOWED_FILE_TYPES: [\n    'application/vnd.ms-excel',\n    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    'text/csv',\n    'application/pdf',\n  ],\n} as const;\n\n// 本地存储键名\nexport const STORAGE_KEYS = {\n  TOKEN: 'auth_token',\n  USER_INFO: 'user_info',\n  CURRENT_ROLE: 'current_role',\n  THEME: 'theme',\n  LANGUAGE: 'language',\n  DASHBOARD_CONFIG: 'dashboard_config',\n} as const;\n\n// 消息类型\nexport const MESSAGE_TYPES = {\n  SUCCESS: 'success',\n  ERROR: 'error',\n  WARNING: 'warning',\n  INFO: 'info',\n} as const;\n\n// 日期格式\nexport const DATE_FORMATS = {\n  DATE: 'YYYY-MM-DD',\n  DATETIME: 'YYYY-MM-DD HH:mm:ss',\n  TIME: 'HH:mm:ss',\n  MONTH: 'YYYY-MM',\n  YEAR: 'YYYY',\n} as const;\n\n// 数值格式\nexport const NUMBER_FORMATS = {\n  CURRENCY: '¥0,0.00',\n  PERCENTAGE: '0.00%',\n  INTEGER: '0,0',\n  DECIMAL: '0,0.00',\n} as const;\n\n// 正则表达式\nexport const REGEX_PATTERNS = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PHONE: /^1[3-9]\\d{9}$/,\n  CODE: /^[A-Z0-9-]+$/,\n  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/,\n} as const;\n\n// 错误代码\nexport const ERROR_CODES = {\n  UNAUTHORIZED: 401,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  VALIDATION_ERROR: 422,\n  SERVER_ERROR: 500,\n} as const;\n\n// 操作类型\nexport const OPERATION_TYPES = {\n  CREATE: 'CREATE',\n  UPDATE: 'UPDATE',\n  DELETE: 'DELETE',\n  APPROVE: 'APPROVE',\n  REJECT: 'REJECT',\n  FREEZE: 'FREEZE',\n  ACTIVATE: 'ACTIVATE',\n} as const;\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,2BAA2B;;AAE7F;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,YAAY;EAEvB;EACAC,QAAQ,EAAE,WAAW;EACrBC,eAAe,EAAE,kBAAkB;EACnCC,aAAa,EAAE,oBAAoB;EACnCC,aAAa,EAAE,oBAAoB;EAEnCC,SAAS,EAAE,YAAY;EACvBC,gBAAgB,EAAE,mBAAmB;EACrCC,gBAAgB,EAAE,8BAA8B;EAChDC,cAAc,EAAE,qBAAqB;EAErC;EACAC,SAAS,EAAE,YAAY;EACvBC,gBAAgB,EAAE,mBAAmB;EACrCC,cAAc,EAAE,qBAAqB;EAErC;EACAC,SAAS,EAAE,YAAY;EACvBC,iBAAiB,EAAE,oBAAoB;EACvCC,eAAe,EAAE,kBAAkB;EACnCC,gBAAgB,EAAE,mBAAmB;EAErCC,QAAQ,EAAE,qBAAqB;EAC/BC,YAAY,EAAE,yBAAyB;EAEvC;EACAC,QAAQ,EAAE,WAAW;EACrBC,eAAe,EAAE,kBAAkB;EACnCC,aAAa,EAAE,oBAAoB;EACnCC,aAAa,EAAE,oBAAoB;EACnCC,oBAAoB,EAAE,uBAAuB;EAC7CC,eAAe,EAAE,eAAe;EAChCC,qBAAqB,EAAE,wBAAwB;EAE/C;EACAC,aAAa,EAAE,gBAAgB;EAC/BC,YAAY,EAAE,eAAe;EAC7BC,cAAc,EAAE,aAAa;EAC7BC,aAAa,EAAE,gBAAgB;EAE/B;EACAC,WAAW,EAAE,cAAc;EAC3BC,cAAc,EAAE,kBAAkB;EAClCC,WAAW,EAAE,sBAAsB;EAEnC;EACAC,GAAG,EAAE,MAAM;EACXC,UAAU,EAAE,aAAa;EACzBC,UAAU,EAAE,iBAAiB;EAE7B;EACAC,OAAO,EAAE,UAAU;EACnBC,gBAAgB,EAAE,oBAAoB;EAEtC;EACAC,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,eAAe;EACtBC,WAAW,EAAE,qBAAqB;EAClCC,aAAa,EAAE,gBAAgB;EAE/B;EACAC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,cAAc;EAC3BC,gBAAgB,EAAE;AACpB,CAAU;;AAEV;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,KAAK,EAAE,OAAO;EACdC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,gBAAgB,EAAE,kBAAkB;EACpCC,kBAAkB,EAAE,oBAAoB;EACxCC,iBAAiB,EAAE,mBAAmB;EACtCC,eAAe,EAAE,iBAAiB;EAClCC,kBAAkB,EAAE,oBAAoB;EACxCC,eAAe,EAAE,iBAAiB;EAClCC,QAAQ,EAAE;AACZ,CAAU;;AAEV;AACA,OAAO,MAAMf,WAAW,GAAG;EACzB;EACAgB,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,YAAY;EACxBC,WAAW,EAAE,aAAa;EAE1B;EACAC,aAAa,EAAE,eAAe;EAC9BC,eAAe,EAAE,iBAAiB;EAClCC,aAAa,EAAE,eAAe;EAC9BC,eAAe,EAAE,iBAAiB;EAElC;EACAC,cAAc,EAAE,gBAAgB;EAChCpD,iBAAiB,EAAE,mBAAmB;EACtCC,eAAe,EAAE,iBAAiB;EAClCC,gBAAgB,EAAE,kBAAkB;EAEpC;EACAM,aAAa,EAAE,eAAe;EAC9BF,eAAe,EAAE,iBAAiB;EAClC+C,gBAAgB,EAAE,kBAAkB;EAEpC;EACAC,SAAS,EAAE,WAAW;EACtB1C,aAAa,EAAE,eAAe;EAE9B;EACA2C,QAAQ,EAAE,UAAU;EACpBnC,UAAU,EAAE,YAAY;EACxBoC,WAAW,EAAE,aAAa;EAE1B;EACA7B,aAAa,EAAE,eAAe;EAC9B8B,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAU;;AAEV;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE;AACZ,CAAU;AAEV,OAAO,MAAMC,YAAY,GAAG;EAC1BJ,KAAK,EAAE,OAAO;EACdK,SAAS,EAAE,WAAW;EACtBH,MAAM,EAAE,QAAQ;EAChBI,SAAS,EAAE;AACb,CAAU;AAEV,OAAO,MAAMC,UAAU,GAAG;EACxBP,KAAK,EAAE,OAAO;EACdQ,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE;AACf,CAAU;AAEV,OAAO,MAAMC,gBAAgB,GAAG;EAC9BC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE;AACX,CAAU;;AAEV;AACA,OAAO,MAAMC,mBAAmB,GAAG,CACjC;EAAEC,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAK,CAAC,EAC5B;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAM,CAAC,EAC9B;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAClC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,CACxB;AAEV,OAAO,MAAMC,KAAK,GAAG,CACnB,KAAK;AAAE;AACP,KAAK;AAAE;AACP,GAAG;AAAI;AACP,IAAI;AAAG;AACP,GAAG;AAAI;AACP,IAAI;AAAG;AACP,IAAI,CAAG;AAAA,CACC;AAEV,OAAO,MAAMC,cAAc,GAAG,CAC5B;EAAEH,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAI,CAAC,EAC5B;EAAED,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAI,CAAC,EAC5B;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAI,CAAC,EAC1B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAK,CAAC,EAC5B;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAI,CAAC,EAC1B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAM,CAAC,EAC7B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAM,CAAC,CACrB;AAEV,OAAO,MAAMG,gBAAgB,GAAG;EAC9BC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAE,KAAK;EACVC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE;AACV,CAAU;AAEV,OAAO,MAAMC,eAAe,GAAG;EAC7BC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE;AACV,CAAU;;AAEV;AACA,OAAO,MAAMC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAU;AACpD,OAAO,MAAMC,iBAAiB,GAAG,EAAE;;AAEnC;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,YAAY,EAAE,SAAS;EACvBC,YAAY,EAAE,SAAS;EACvBC,YAAY,EAAE,SAAS;EACvBC,UAAU,EAAE,SAAS;EACrBC,YAAY,EAAE;AAChB,CAAU;;AAEV;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,aAAa,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;EAAE;EACjCC,kBAAkB,EAAE,CAClB,0BAA0B,EAC1B,mEAAmE,EACnE,UAAU,EACV,iBAAiB;AAErB,CAAU;;AAEV;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,KAAK,EAAE,YAAY;EACnBC,SAAS,EAAE,WAAW;EACtBC,YAAY,EAAE,cAAc;EAC5BC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBrF,gBAAgB,EAAE;AACpB,CAAU;;AAEV;AACA,OAAO,MAAMsF,aAAa,GAAG;EAC3BC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE;AACR,CAAU;;AAEV;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,qBAAqB;EAC/BC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE;AACR,CAAU;;AAEV;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,QAAQ,EAAE,SAAS;EACnBC,UAAU,EAAE,OAAO;EACnBC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;AACX,CAAU;;AAEV;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,KAAK,EAAE,4BAA4B;EACnCC,KAAK,EAAE,eAAe;EACtBC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE;AACZ,CAAU;;AAEV;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,YAAY,EAAE,GAAG;EACjBC,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE,GAAG;EACdC,gBAAgB,EAAE,GAAG;EACrBC,YAAY,EAAE;AAChB,CAAU;;AAEV;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7BC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE;AACZ,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}