{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\bom\\\\CoreBOMListPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Table, Button, Space, Input, Select, Card, Tag, message, Modal, Form, Typography, Row, Col, Tooltip, Dropdown, Upload, Progress, Alert, Divider, Timeline, Descriptions, Checkbox, Radio } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, CopyOutlined, LockOutlined, UnlockOutlined, MoreOutlined, ExportOutlined, ImportOutlined, HistoryOutlined, DownloadOutlined, FileExcelOutlined, CheckCircleOutlined, ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCoreBOMs, deleteCoreBOM, freezeCoreBOM } from '../../store/slices/bomSlice';\nimport { ROUTES, BOM_STATUS } from '../../constants';\nimport { formatDate } from '../../utils';\nimport { ConfirmDialog } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Search\n} = Input;\nconst CoreBOMListPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const {\n    coreBOMs,\n    loading,\n    pagination\n  } = useAppSelector(state => state.bom);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [copyModalVisible, setCopyModalVisible] = useState(false);\n  const [copyingBOM, setCopyingBOM] = useState(null);\n  const [copyForm] = Form.useForm();\n\n  // 版本历史相关状态\n  const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);\n  const [currentBOMForHistory, setCurrentBOMForHistory] = useState(null);\n\n  // 导出导入相关状态\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [importModalVisible, setImportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState('excel');\n  const [exportFields, setExportFields] = useState(['code', 'name', 'version', 'status', 'description']);\n  const [importProgress, setImportProgress] = useState(0);\n  const [importStatus, setImportStatus] = useState(null);\n  const [importResult, setImportResult] = useState(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [exportForm] = Form.useForm();\n  const [importForm] = Form.useForm();\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter]);\n  const loadData = useCallback(() => {\n    dispatch(fetchCoreBOMs({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      keyword: searchKeyword\n    }));\n  }, [dispatch, pagination.current, pagination.pageSize, searchKeyword]);\n  const handleSearch = useCallback(value => {\n    setSearchKeyword(value);\n  }, []);\n  const handleStatusFilter = useCallback(value => {\n    setStatusFilter(value);\n  }, []);\n  const handleCreate = useCallback(() => {\n    navigate(ROUTES.CORE_BOM_CREATE);\n  }, [navigate]);\n  const handleEdit = useCallback(record => {\n    navigate(ROUTES.CORE_BOM_EDIT.replace(':id', record.id));\n  }, [navigate]);\n  const handleView = useCallback(record => {\n    navigate(ROUTES.CORE_BOM_VIEW.replace(':id', record.id));\n  }, [navigate]);\n  const handleDelete = useCallback(async record => {\n    ConfirmDialog.confirm({\n      title: '确认删除',\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u4EE5\\u4E0BBOM\\u5417\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"BOM\\u7F16\\u7801\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 14\n          }, this), record.code]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"BOM\\u540D\\u79F0\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 14\n          }, this), record.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#ff4d4f',\n            marginTop: 12\n          },\n          children: \"\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this),\n      type: 'warning',\n      onConfirm: async () => {\n        try {\n          await dispatch(deleteCoreBOM(record.id)).unwrap();\n          message.success('删除成功');\n          loadData();\n        } catch (error) {\n          errorHandler.handleError({\n            type: ErrorType.BUSINESS,\n            message: error.message || '删除失败',\n            details: error\n          });\n        }\n      }\n    });\n  }, [dispatch, loadData]);\n  const handleFreeze = useCallback(async record => {\n    const isActive = record.status === BOM_STATUS.ACTIVE;\n    const action = isActive ? '冻结' : '解冻';\n    ConfirmDialog.confirm({\n      title: `确认${action}`,\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u786E\\u5B9A\\u8981\", action, \"\\u4EE5\\u4E0BBOM\\u5417\\uFF1F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"BOM\\u7F16\\u7801\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 14\n          }, this), record.code]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"BOM\\u540D\\u79F0\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 14\n          }, this), record.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), isActive && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#faad14',\n            marginTop: 12\n          },\n          children: \"\\u51BB\\u7ED3\\u540E\\u8BE5BOM\\u5C06\\u65E0\\u6CD5\\u88AB\\u4F7F\\u7528\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this),\n      type: isActive ? 'warning' : 'info',\n      onConfirm: async () => {\n        try {\n          await dispatch(freezeCoreBOM(record.id)).unwrap();\n          message.success(`${action}成功`);\n          loadData();\n        } catch (error) {\n          errorHandler.handleError({\n            type: ErrorType.BUSINESS,\n            message: error.message || `${action}失败`,\n            details: error\n          });\n        }\n      }\n    });\n  }, [dispatch, loadData]);\n  const handleCopy = useCallback(record => {\n    setCopyingBOM(record);\n    copyForm.setFieldsValue({\n      name: `${record.name}_副本`,\n      code: `${record.code}_COPY`\n    });\n    setCopyModalVisible(true);\n  }, [copyForm]);\n  const handleCopyConfirm = async () => {\n    try {\n      const values = await copyForm.validateFields();\n      // TODO: 实现复制BOM的API调用\n      message.success('复制成功');\n      setCopyModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('复制失败');\n    }\n  };\n  const handleVersionHistory = record => {\n    setCurrentBOMForHistory(record);\n    setVersionHistoryVisible(true);\n  };\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n  const handleImport = () => {\n    setImportModalVisible(true);\n  };\n\n  // 下载导入模板\n  const handleDownloadTemplate = () => {\n    // TODO: 实现下载模板功能\n    const templateData = [{\n      'BOM编码': 'BOM001',\n      'BOM名称': '示例BOM',\n      '版本': 'V1.0',\n      '状态': 'draft',\n      '描述': '这是一个示例BOM'\n    }];\n\n    // 创建CSV内容\n    const headers = Object.keys(templateData[0]);\n    const csvContent = [headers.join(','), ...templateData.map(row => Object.values(row).join(','))].join('\\n');\n\n    // 下载文件\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    link.href = URL.createObjectURL(blob);\n    link.download = 'core_bom_template.csv';\n    link.click();\n    message.success('模板下载成功');\n  };\n\n  // 执行导出\n  const handleExportConfirm = async () => {\n    try {\n      const values = await exportForm.validateFields();\n\n      // 获取要导出的数据\n      let exportData = mockData;\n      if (selectedRowKeys.length > 0) {\n        exportData = mockData.filter(item => selectedRowKeys.includes(item.id));\n      }\n\n      // 根据选择的字段过滤数据\n      const filteredData = exportData.map(item => {\n        const filteredItem = {};\n        exportFields.forEach(field => {\n          switch (field) {\n            case 'code':\n              filteredItem['BOM编码'] = item.code;\n              break;\n            case 'name':\n              filteredItem['BOM名称'] = item.name;\n              break;\n            case 'version':\n              filteredItem['版本'] = item.version;\n              break;\n            case 'status':\n              filteredItem['状态'] = getStatusText(item.status);\n              break;\n            case 'description':\n              filteredItem['描述'] = item.description;\n              break;\n            case 'createdBy':\n              filteredItem['创建人'] = item.createdBy;\n              break;\n            case 'createdAt':\n              filteredItem['创建时间'] = formatDate(item.createdAt);\n              break;\n            case 'updatedAt':\n              filteredItem['更新时间'] = formatDate(item.updatedAt);\n              break;\n          }\n        });\n        return filteredItem;\n      });\n\n      // 创建并下载文件\n      if (exportFormat === 'excel') {\n        // TODO: 使用xlsx库导出Excel\n        message.info('Excel导出功能需要集成xlsx库');\n      } else {\n        // CSV导出\n        const headers = Object.keys(filteredData[0] || {});\n        const csvContent = [headers.join(','), ...filteredData.map(row => Object.values(row).join(','))].join('\\n');\n        const blob = new Blob([csvContent], {\n          type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        link.href = URL.createObjectURL(blob);\n        link.download = `core_bom_export_${new Date().getTime()}.csv`;\n        link.click();\n      }\n      setExportModalVisible(false);\n      message.success('导出成功');\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  // 处理文件上传\n  const handleFileUpload = file => {\n    setImportStatus('uploading');\n    setImportProgress(0);\n\n    // 模拟上传进度\n    const interval = setInterval(() => {\n      setImportProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(interval);\n          setImportStatus('processing');\n\n          // 模拟处理过程\n          setTimeout(() => {\n            processImportFile(file);\n          }, 1000);\n          return 100;\n        }\n        return prev + 10;\n      });\n    }, 200);\n    return false; // 阻止默认上传\n  };\n\n  // 处理导入文件\n  const processImportFile = file => {\n    const reader = new FileReader();\n    reader.onload = e => {\n      try {\n        var _e$target;\n        const content = (_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result;\n        const lines = content.split('\\n');\n        const headers = lines[0].split(',');\n        const importedData = [];\n        const errors = [];\n        for (let i = 1; i < lines.length; i++) {\n          if (lines[i].trim()) {\n            const values = lines[i].split(',');\n            const row = {};\n            headers.forEach((header, index) => {\n              var _values$index;\n              row[header.trim()] = (_values$index = values[index]) === null || _values$index === void 0 ? void 0 : _values$index.trim();\n            });\n\n            // 验证数据\n            if (!row['BOM编码'] || !row['BOM名称']) {\n              errors.push(`第${i + 1}行：BOM编码和名称不能为空`);\n            } else {\n              importedData.push({\n                code: row['BOM编码'],\n                name: row['BOM名称'],\n                version: row['版本'] || 'V1.0',\n                status: row['状态'] || 'draft',\n                description: row['描述'] || ''\n              });\n            }\n          }\n        }\n        setImportResult({\n          total: lines.length - 1,\n          success: importedData.length,\n          errors: errors.length,\n          data: importedData,\n          errorMessages: errors\n        });\n        setImportStatus(errors.length > 0 ? 'error' : 'success');\n        if (errors.length === 0) {\n          message.success(`成功导入${importedData.length}条数据`);\n          // TODO: 调用API保存数据\n        } else {\n          message.warning(`导入完成，${errors.length}条数据有错误`);\n        }\n      } catch (error) {\n        setImportStatus('error');\n        setImportResult({\n          total: 0,\n          success: 0,\n          errors: 1,\n          errorMessages: ['文件格式错误，请检查文件内容']\n        });\n        message.error('文件解析失败');\n      }\n    };\n    reader.readAsText(file);\n  };\n\n  // 表格行选择\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: newSelectedRowKeys => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    onSelectAll: (selected, selectedRows, changeRows) => {\n      console.log('Select all:', selected, selectedRows, changeRows);\n    }\n  };\n\n  // 清空选择\n  const handleClearSelection = () => {\n    setSelectedRowKeys([]);\n  };\n\n  // 导出选中项\n  const handleExportSelected = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请先选择要导出的数据');\n      return;\n    }\n    setExportModalVisible(true);\n  };\n\n  // 模拟版本历史数据\n  const getVersionHistory = bom => {\n    return [{\n      version: 'V1.2',\n      status: 'active',\n      description: '优化物料清单，更新供应商信息',\n      createdBy: '李工程师',\n      createdAt: '2024-03-25 14:30:00',\n      changes: ['更新供应商信息', '调整物料数量', '优化成本结构']\n    }, {\n      version: 'V1.1',\n      status: 'frozen',\n      description: '修复物料规格错误',\n      createdBy: '王工程师',\n      createdAt: '2024-03-20 10:15:00',\n      changes: ['修正物料规格', '更新技术参数']\n    }, {\n      version: 'V1.0',\n      status: 'obsolete',\n      description: '初始版本',\n      createdBy: '张工程师',\n      createdAt: '2024-03-15 09:00:00',\n      changes: ['创建初始BOM结构']\n    }];\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case BOM_STATUS.DRAFT:\n        return 'default';\n      case BOM_STATUS.ACTIVE:\n        return 'success';\n      case BOM_STATUS.FROZEN:\n        return 'blue';\n      case BOM_STATUS.OBSOLETE:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case BOM_STATUS.DRAFT:\n        return '草稿';\n      case BOM_STATUS.ACTIVE:\n        return '激活';\n      case BOM_STATUS.FROZEN:\n        return '冻结';\n      case BOM_STATUS.OBSOLETE:\n        return '废弃';\n      default:\n        return status;\n    }\n  };\n\n  // 可导出字段选项\n  const exportFieldOptions = [{\n    label: 'BOM编码',\n    value: 'code'\n  }, {\n    label: 'BOM名称',\n    value: 'name'\n  }, {\n    label: '版本',\n    value: 'version'\n  }, {\n    label: '状态',\n    value: 'status'\n  }, {\n    label: '描述',\n    value: 'description'\n  }, {\n    label: '创建人',\n    value: 'createdBy'\n  }, {\n    label: '创建时间',\n    value: 'createdAt'\n  }, {\n    label: '更新时间',\n    value: 'updatedAt'\n  }];\n  const getActionMenuItems = record => [{\n    key: 'copy',\n    icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 13\n    }, this),\n    label: '复制',\n    onClick: () => handleCopy(record)\n  }, {\n    key: 'history',\n    icon: /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 13\n    }, this),\n    label: '版本历史',\n    onClick: () => handleVersionHistory(record)\n  }, {\n    key: 'freeze',\n    icon: record.status === BOM_STATUS.FROZEN ? /*#__PURE__*/_jsxDEV(UnlockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 51\n    }, this) : /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 72\n    }, this),\n    label: record.status === BOM_STATUS.FROZEN ? '解冻' : '冻结',\n    onClick: () => handleFreeze(record),\n    disabled: record.status === BOM_STATUS.DRAFT\n  }];\n  const columns = [{\n    title: 'BOM编码',\n    dataIndex: 'code',\n    key: 'code',\n    width: 120,\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      onClick: () => handleView(record),\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'BOM名称',\n    dataIndex: 'name',\n    key: 'name',\n    ellipsis: true\n  }, {\n    title: '版本',\n    dataIndex: 'version',\n    key: 'version',\n    width: 80\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    ellipsis: true\n  }, {\n    title: '创建人',\n    dataIndex: 'createdBy',\n    key: 'createdBy',\n    width: 100\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 120,\n    render: date => formatDate(date)\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    width: 120,\n    render: date => formatDate(date)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record),\n          disabled: record.status === BOM_STATUS.FROZEN\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleDelete(record),\n          disabled: record.status !== BOM_STATUS.DRAFT\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n        menu: {\n          items: getActionMenuItems(record)\n        },\n        trigger: ['click'],\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(MoreOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 580,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 模拟数据\n  const mockData = [{\n    id: '1',\n    name: '5G基站天线BOM',\n    code: 'ANT-5G-001',\n    version: 'V1.0',\n    description: '5G基站用高增益天线物料清单',\n    status: 'ACTIVE',\n    items: [],\n    configRules: [],\n    createdBy: 'admin',\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-15T00:00:00Z'\n  }, {\n    id: '2',\n    name: '4G室内天线BOM',\n    code: 'ANT-4G-002',\n    version: 'V2.1',\n    description: '4G室内覆盖天线物料清单',\n    status: 'FROZEN',\n    items: [],\n    configRules: [],\n    createdBy: 'bom_manager',\n    createdAt: '2024-01-02T00:00:00Z',\n    updatedAt: '2024-01-16T00:00:00Z',\n    frozenAt: '2024-01-16T00:00:00Z',\n    frozenBy: 'admin'\n  }, {\n    id: '3',\n    name: 'WiFi天线BOM',\n    code: 'ANT-WIFI-003',\n    version: 'V1.5',\n    description: 'WiFi6天线物料清单',\n    status: 'DRAFT',\n    items: [],\n    configRules: [],\n    createdBy: 'bom_manager',\n    createdAt: '2024-01-03T00:00:00Z',\n    updatedAt: '2024-01-17T00:00:00Z'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u6838\\u5FC3BOM\\u7BA1\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ImportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 29\n              }, this),\n              onClick: handleImport,\n              children: \"\\u5BFC\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 29\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 44\n              }, this),\n              onClick: handleCreate,\n              children: \"\\u65B0\\u5EFABOM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Search, {\n            placeholder: \"\\u641C\\u7D22BOM\\u7F16\\u7801\\u6216\\u540D\\u79F0\",\n            allowClear: true,\n            onSearch: handleSearch,\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u72B6\\u6001\\u7B5B\\u9009\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            onChange: handleStatusFilter,\n            options: [{\n              label: '草稿',\n              value: BOM_STATUS.DRAFT\n            }, {\n              label: '激活',\n              value: BOM_STATUS.ACTIVE\n            }, {\n              label: '冻结',\n              value: BOM_STATUS.FROZEN\n            }, {\n              label: '废弃',\n              value: BOM_STATUS.OBSOLETE\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this), selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16,\n          padding: '8px 16px',\n          backgroundColor: '#f0f2f5',\n          borderRadius: 6\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginRight: 16\n          },\n          children: [\"\\u5DF2\\u9009\\u62E9 \", selectedRowKeys.length, \" \\u9879\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          onClick: handleClearSelection,\n          style: {\n            marginRight: 8\n          },\n          children: \"\\u6E05\\u7A7A\\u9009\\u62E9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 55\n          }, this),\n          onClick: handleExportSelected,\n          children: \"\\u5BFC\\u51FA\\u9009\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 711,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: mockData,\n        loading: loading,\n        rowKey: \"id\",\n        rowSelection: rowSelection,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          current: pagination.current,\n          pageSize: pagination.pageSize,\n          total: pagination.total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u590D\\u5236BOM\",\n      open: copyModalVisible,\n      onOk: handleCopyConfirm,\n      onCancel: () => setCopyModalVisible(false),\n      okText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: copyForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"BOM\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入BOM名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 751,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"code\",\n          label: \"BOM\\u7F16\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入BOM编码'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u7F16\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `版本历史 - ${currentBOMForHistory === null || currentBOMForHistory === void 0 ? void 0 : currentBOMForHistory.name}`,\n      open: versionHistoryVisible,\n      onCancel: () => setVersionHistoryVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setVersionHistoryVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 13\n      }, this)],\n      width: 800,\n      children: currentBOMForHistory && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n          title: \"\\u5F53\\u524DBOM\\u4FE1\\u606F\",\n          bordered: true,\n          size: \"small\",\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"BOM\\u7F16\\u7801\",\n            children: currentBOMForHistory.code\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"BOM\\u540D\\u79F0\",\n            children: currentBOMForHistory.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5F53\\u524D\\u7248\\u672C\",\n            children: currentBOMForHistory.version\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u72B6\\u6001\",\n            span: 3,\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: getStatusColor(currentBOMForHistory.status),\n              children: getStatusText(currentBOMForHistory.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          children: \"\\u7248\\u672C\\u5386\\u53F2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Timeline, {\n          children: getVersionHistory(currentBOMForHistory).map((version, index) => /*#__PURE__*/_jsxDEV(Timeline.Item, {\n            dot: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n              style: {\n                fontSize: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 26\n            }, this),\n            color: version.status === 'active' ? 'green' : version.status === 'frozen' ? 'blue' : 'gray',\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: 16,\n                    fontWeight: 'bold'\n                  },\n                  children: version.version\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: getStatusColor(version.status),\n                  children: getStatusText(version.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#666',\n                  marginBottom: 8\n                },\n                children: [version.createdBy, \" \\xB7 \", version.createdAt]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: version.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 23\n              }, this), version.changes && version.changes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold',\n                    marginBottom: 4\n                  },\n                  children: \"\\u4E3B\\u8981\\u53D8\\u66F4\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  style: {\n                    margin: 0,\n                    paddingLeft: 20\n                  },\n                  children: version.changes.map((change, changeIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: change\n                  }, changeIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 21\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 769,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5BFC\\u51FABOM\\u6570\\u636E\",\n      open: exportModalVisible,\n      onOk: handleExportConfirm,\n      onCancel: () => setExportModalVisible(false),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: exportForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5BFC\\u51FA\\u8BF4\\u660E\",\n          description: selectedRowKeys.length > 0 ? `将导出已选择的 ${selectedRowKeys.length} 条BOM数据` : \"将导出所有BOM数据\",\n          type: \"info\",\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5BFC\\u51FA\\u683C\\u5F0F\",\n          name: \"format\",\n          initialValue: exportFormat,\n          children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n            onChange: e => setExportFormat(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"excel\",\n              children: \"Excel\\u683C\\u5F0F (.xlsx)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"csv\",\n              children: \"CSV\\u683C\\u5F0F (.csv)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5BFC\\u51FA\\u5B57\\u6BB5\",\n          name: \"fields\",\n          initialValue: exportFields,\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            options: exportFieldOptions,\n            value: exportFields,\n            onChange: setExportFields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 859,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5BFC\\u5165BOM\\u6570\\u636E\",\n      open: importModalVisible,\n      onCancel: () => {\n        setImportModalVisible(false);\n        setImportStatus(null);\n        setImportProgress(0);\n        setImportResult(null);\n        importForm.resetFields();\n      },\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 881,\n          columnNumber: 42\n        }, this),\n        onClick: handleDownloadTemplate,\n        children: \"\\u4E0B\\u8F7D\\u6A21\\u677F\"\n      }, \"template\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          setImportModalVisible(false);\n          setImportStatus(null);\n          setImportProgress(0);\n          setImportResult(null);\n          importForm.resetFields();\n        },\n        children: \"\\u53D6\\u6D88\"\n      }, \"cancel\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 13\n      }, this)],\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: importForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5BFC\\u5165\\u8BF4\\u660E\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"1. \\u8BF7\\u4E0B\\u8F7D\\u5BFC\\u5165\\u6A21\\u677F\\uFF0C\\u6309\\u7167\\u6A21\\u677F\\u683C\\u5F0F\\u586B\\u5199\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"2. \\u652F\\u6301CSV\\u683C\\u5F0F\\u6587\\u4EF6\\u5BFC\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"3. BOM\\u7F16\\u7801\\u548C\\u540D\\u79F0\\u4E3A\\u5FC5\\u586B\\u5B57\\u6BB5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"4. \\u5BFC\\u5165\\u524D\\u8BF7\\u786E\\u4FDD\\u6570\\u636E\\u683C\\u5F0F\\u6B63\\u786E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 897,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u9009\\u62E9\\u6587\\u4EF6\",\n          children: /*#__PURE__*/_jsxDEV(Upload.Dragger, {\n            accept: \".csv,.xlsx,.xls\",\n            beforeUpload: handleFileUpload,\n            showUploadList: false,\n            disabled: importStatus === 'uploading' || importStatus === 'processing',\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(FileExcelOutlined, {\n                style: {\n                  fontSize: 48,\n                  color: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u3001Excel\\u683C\\u5F0F\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 13\n        }, this), importStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16\n          },\n          children: [importStatus === 'uploading' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"\\u6B63\\u5728\\u4E0A\\u4F20\\u6587\\u4EF6...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 930,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: importProgress,\n              status: \"active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 19\n          }, this), importStatus === 'processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u6570\\u636E...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: 100,\n              status: \"active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 19\n          }, this), importStatus === 'success' && importResult && /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u5BFC\\u5165\\u6210\\u529F\",\n            description: `成功导入 ${importResult.success} 条数据`,\n            type: \"success\",\n            icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 27\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 943,\n            columnNumber: 19\n          }, this), importStatus === 'error' && importResult && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u5BFC\\u5165\\u5B8C\\u6210\\uFF0C\\u90E8\\u5206\\u6570\\u636E\\u6709\\u9519\\u8BEF\",\n              description: `成功：${importResult.success} 条，错误：${importResult.errors} 条`,\n              type: \"warning\",\n              icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 29\n              }, this),\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 21\n            }, this), importResult.errorMessages && importResult.errorMessages.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold',\n                  marginBottom: 8\n                },\n                children: \"\\u9519\\u8BEF\\u8BE6\\u60C5\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: 200,\n                  overflow: 'auto',\n                  backgroundColor: '#f5f5f5',\n                  padding: 8,\n                  borderRadius: 4\n                },\n                children: importResult.errorMessages.map((error, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#ff4d4f',\n                    marginBottom: 4\n                  },\n                  children: error\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 966,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 896,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 870,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 662,\n    columnNumber: 5\n  }, this);\n};\n_s(CoreBOMListPage, \"kI0tGHtDVeLiV8sCWSlfbZ/vL4I=\", false, function () {\n  return [useNavigate, useAppDispatch, useAppSelector, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = CoreBOMListPage;\nexport default _c2 = /*#__PURE__*/React.memo(CoreBOMListPage);\nvar _c, _c2;\n$RefreshReg$(_c, \"CoreBOMListPage\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "message", "Modal", "Form", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Upload", "Progress", "<PERSON><PERSON>", "Divider", "Timeline", "Descriptions", "Checkbox", "Radio", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "LockOutlined", "UnlockOutlined", "MoreOutlined", "ExportOutlined", "ImportOutlined", "HistoryOutlined", "DownloadOutlined", "FileExcelOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "ClockCircleOutlined", "useAppDispatch", "useAppSelector", "fetchCoreBOMs", "deleteCoreBOM", "freezeCoreBOM", "ROUTES", "BOM_STATUS", "formatDate", "ConfirmDialog", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "jsxDEV", "_jsxDEV", "Title", "Search", "CoreBOMListPage", "_s", "navigate", "dispatch", "coreBOMs", "loading", "pagination", "state", "bom", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "copyModalVisible", "setCopyModalVisible", "copyingBOM", "setCopyingBOM", "copyForm", "useForm", "versionHistoryVisible", "setVersionHistoryVisible", "currentBOMForHistory", "setCurrentBOMForHistory", "exportModalVisible", "setExportModalVisible", "importModalVisible", "setImportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "importProgress", "setImportProgress", "importStatus", "setImportStatus", "importResult", "setImportResult", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "exportForm", "importForm", "loadData", "current", "pageSize", "page", "keyword", "handleSearch", "value", "handleStatusFilter", "handleCreate", "CORE_BOM_CREATE", "handleEdit", "record", "CORE_BOM_EDIT", "replace", "id", "handleView", "CORE_BOM_VIEW", "handleDelete", "confirm", "title", "content", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "code", "name", "style", "color", "marginTop", "type", "onConfirm", "unwrap", "success", "error", "handleError", "BUSINESS", "details", "handleFreeze", "isActive", "status", "ACTIVE", "action", "handleCopy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleCopyConfirm", "values", "validateFields", "handleVersionHistory", "handleExport", "handleImport", "handleDownloadTemplate", "templateData", "headers", "Object", "keys", "csv<PERSON><PERSON>nt", "join", "map", "row", "blob", "Blob", "link", "document", "createElement", "href", "URL", "createObjectURL", "download", "click", "handleExportConfirm", "exportData", "mockData", "length", "filter", "item", "includes", "filteredData", "filteredItem", "for<PERSON>ach", "field", "version", "getStatusText", "description", "created<PERSON>y", "createdAt", "updatedAt", "info", "Date", "getTime", "handleFileUpload", "file", "interval", "setInterval", "prev", "clearInterval", "setTimeout", "processImportFile", "reader", "FileReader", "onload", "e", "_e$target", "target", "result", "lines", "split", "importedData", "errors", "i", "trim", "header", "index", "_values$index", "push", "total", "data", "errorMessages", "warning", "readAsText", "rowSelection", "onChange", "newSelectedRowKeys", "onSelectAll", "selected", "selectedRows", "changeRows", "console", "log", "handleClearSelection", "handleExportSelected", "getVersionHistory", "changes", "getStatusColor", "DRAFT", "FROZEN", "OBSOLETE", "exportFieldOptions", "label", "getActionMenuItems", "key", "icon", "onClick", "disabled", "columns", "dataIndex", "width", "render", "text", "ellipsis", "date", "fixed", "_", "size", "danger", "menu", "items", "trigger", "configRules", "frozenAt", "frozenBy", "justify", "align", "marginBottom", "level", "margin", "gutter", "xs", "sm", "md", "placeholder", "allowClear", "onSearch", "options", "padding", "backgroundColor", "borderRadius", "marginRight", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onOk", "onCancel", "okText", "cancelText", "form", "layout", "<PERSON><PERSON>", "rules", "required", "footer", "bordered", "span", "dot", "fontSize", "display", "justifyContent", "alignItems", "fontWeight", "paddingLeft", "change", "changeIndex", "initialValue", "Group", "resetFields", "<PERSON><PERSON>", "accept", "beforeUpload", "showUploadList", "className", "percent", "maxHeight", "overflow", "_c", "_c2", "memo", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/CoreBOMListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState, useMemo, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Upload,\n  Progress,\n  Alert,\n  Divider,\n  Timeline,\n  Descriptions,\n  Checkbox,\n  Radio\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  LockOutlined,\n  UnlockOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  HistoryOutlined,\n  DownloadOutlined,\n  UploadOutlined,\n  FileExcelOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCoreBOMs, deleteCoreBOM, freezeCoreBOM } from '../../store/slices/bomSlice';\nimport { CoreBOM } from '../../types';\nimport { ROUTES, BOM_STATUS } from '../../constants';\nimport { formatDate } from '../../utils';\nimport { ConfirmDialog } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title } = Typography;\nconst { Search } = Input;\n\nconst CoreBOMListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const { coreBOMs, loading, pagination } = useAppSelector(state => state.bom);\n  \n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [copyModalVisible, setCopyModalVisible] = useState(false);\n  const [copyingBOM, setCopyingBOM] = useState<CoreBOM | null>(null);\n  const [copyForm] = Form.useForm();\n  \n  // 版本历史相关状态\n  const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);\n  const [currentBOMForHistory, setCurrentBOMForHistory] = useState<CoreBOM | null>(null);\n  \n  // 导出导入相关状态\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [importModalVisible, setImportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['code', 'name', 'version', 'status', 'description']);\n  const [importProgress, setImportProgress] = useState(0);\n  const [importStatus, setImportStatus] = useState<'uploading' | 'processing' | 'success' | 'error' | null>(null);\n  const [importResult, setImportResult] = useState<any>(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [exportForm] = Form.useForm();\n  const [importForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter]);\n\n  const loadData = useCallback(() => {\n    dispatch(fetchCoreBOMs({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      keyword: searchKeyword,\n    }));\n  }, [dispatch, pagination.current, pagination.pageSize, searchKeyword]);\n\n  const handleSearch = useCallback((value: string) => {\n    setSearchKeyword(value);\n  }, []);\n\n  const handleStatusFilter = useCallback((value: string) => {\n    setStatusFilter(value);\n  }, []);\n\n  const handleCreate = useCallback(() => {\n    navigate(ROUTES.CORE_BOM_CREATE);\n  }, [navigate]);\n\n  const handleEdit = useCallback((record: CoreBOM) => {\n    navigate(ROUTES.CORE_BOM_EDIT.replace(':id', record.id));\n  }, [navigate]);\n\n  const handleView = useCallback((record: CoreBOM) => {\n    navigate(ROUTES.CORE_BOM_VIEW.replace(':id', record.id));\n  }, [navigate]);\n\n  const handleDelete = useCallback(async (record: CoreBOM) => {\n    ConfirmDialog.confirm({\n      title: '确认删除',\n      content: (\n        <div>\n          <p>确定要删除以下BOM吗？</p>\n          <p><strong>BOM编码：</strong>{record.code}</p>\n          <p><strong>BOM名称：</strong>{record.name}</p>\n          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复，请谨慎操作！</p>\n        </div>\n      ),\n      type: 'warning',\n      onConfirm: async () => {\n        try {\n          await dispatch(deleteCoreBOM(record.id)).unwrap();\n          message.success('删除成功');\n          loadData();\n        } catch (error: any) {\n          errorHandler.handleError({\n            type: ErrorType.BUSINESS,\n            message: error.message || '删除失败',\n            details: error\n          });\n        }\n      }\n    });\n  }, [dispatch, loadData]);\n\n  const handleFreeze = useCallback(async (record: CoreBOM) => {\n    const isActive = record.status === BOM_STATUS.ACTIVE;\n    const action = isActive ? '冻结' : '解冻';\n    \n    ConfirmDialog.confirm({\n      title: `确认${action}`,\n      content: (\n        <div>\n          <p>确定要{action}以下BOM吗？</p>\n          <p><strong>BOM编码：</strong>{record.code}</p>\n          <p><strong>BOM名称：</strong>{record.name}</p>\n          {isActive && (\n            <p style={{ color: '#faad14', marginTop: 12 }}>冻结后该BOM将无法被使用！</p>\n          )}\n        </div>\n      ),\n      type: isActive ? 'warning' : 'info',\n      onConfirm: async () => {\n        try {\n          await dispatch(freezeCoreBOM(record.id)).unwrap();\n          message.success(`${action}成功`);\n          loadData();\n        } catch (error: any) {\n          errorHandler.handleError({\n            type: ErrorType.BUSINESS,\n            message: error.message || `${action}失败`,\n            details: error\n          });\n        }\n      }\n    });\n  }, [dispatch, loadData]);\n\n  const handleCopy = useCallback((record: CoreBOM) => {\n    setCopyingBOM(record);\n    copyForm.setFieldsValue({\n      name: `${record.name}_副本`,\n      code: `${record.code}_COPY`,\n    });\n    setCopyModalVisible(true);\n  }, [copyForm]);\n\n  const handleCopyConfirm = async () => {\n    try {\n      const values = await copyForm.validateFields();\n      // TODO: 实现复制BOM的API调用\n      message.success('复制成功');\n      setCopyModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('复制失败');\n    }\n  };\n\n  const handleVersionHistory = (record: CoreBOM) => {\n    setCurrentBOMForHistory(record);\n    setVersionHistoryVisible(true);\n  };\n\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const handleImport = () => {\n    setImportModalVisible(true);\n  };\n  \n  // 下载导入模板\n  const handleDownloadTemplate = () => {\n    // TODO: 实现下载模板功能\n    const templateData = [\n      {\n        'BOM编码': 'BOM001',\n        'BOM名称': '示例BOM',\n        '版本': 'V1.0',\n        '状态': 'draft',\n        '描述': '这是一个示例BOM'\n      }\n    ];\n    \n    // 创建CSV内容\n    const headers = Object.keys(templateData[0]);\n    const csvContent = [headers.join(','), ...templateData.map(row => Object.values(row).join(','))].join('\\n');\n    \n    // 下载文件\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    link.href = URL.createObjectURL(blob);\n    link.download = 'core_bom_template.csv';\n    link.click();\n    \n    message.success('模板下载成功');\n  };\n  \n  // 执行导出\n  const handleExportConfirm = async () => {\n    try {\n      const values = await exportForm.validateFields();\n      \n      // 获取要导出的数据\n      let exportData = mockData;\n      if (selectedRowKeys.length > 0) {\n        exportData = mockData.filter(item => selectedRowKeys.includes(item.id));\n      }\n      \n      // 根据选择的字段过滤数据\n      const filteredData = exportData.map(item => {\n        const filteredItem: any = {};\n        exportFields.forEach(field => {\n          switch (field) {\n            case 'code':\n              filteredItem['BOM编码'] = item.code;\n              break;\n            case 'name':\n              filteredItem['BOM名称'] = item.name;\n              break;\n            case 'version':\n              filteredItem['版本'] = item.version;\n              break;\n            case 'status':\n              filteredItem['状态'] = getStatusText(item.status);\n              break;\n            case 'description':\n              filteredItem['描述'] = item.description;\n              break;\n            case 'createdBy':\n              filteredItem['创建人'] = item.createdBy;\n              break;\n            case 'createdAt':\n              filteredItem['创建时间'] = formatDate(item.createdAt);\n              break;\n            case 'updatedAt':\n              filteredItem['更新时间'] = formatDate(item.updatedAt);\n              break;\n          }\n        });\n        return filteredItem;\n      });\n      \n      // 创建并下载文件\n      if (exportFormat === 'excel') {\n        // TODO: 使用xlsx库导出Excel\n        message.info('Excel导出功能需要集成xlsx库');\n      } else {\n        // CSV导出\n        const headers = Object.keys(filteredData[0] || {});\n        const csvContent = [headers.join(','), ...filteredData.map(row => Object.values(row).join(','))].join('\\n');\n        \n        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n        const link = document.createElement('a');\n        link.href = URL.createObjectURL(blob);\n        link.download = `core_bom_export_${new Date().getTime()}.csv`;\n        link.click();\n      }\n      \n      setExportModalVisible(false);\n      message.success('导出成功');\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n  \n  // 处理文件上传\n  const handleFileUpload = (file: File) => {\n    setImportStatus('uploading');\n    setImportProgress(0);\n    \n    // 模拟上传进度\n    const interval = setInterval(() => {\n      setImportProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(interval);\n          setImportStatus('processing');\n          \n          // 模拟处理过程\n          setTimeout(() => {\n            processImportFile(file);\n          }, 1000);\n          \n          return 100;\n        }\n        return prev + 10;\n      });\n    }, 200);\n    \n    return false; // 阻止默认上传\n  };\n  \n  // 处理导入文件\n  const processImportFile = (file: File) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const content = e.target?.result as string;\n        const lines = content.split('\\n');\n        const headers = lines[0].split(',');\n        \n        const importedData = [];\n        const errors = [];\n        \n        for (let i = 1; i < lines.length; i++) {\n          if (lines[i].trim()) {\n            const values = lines[i].split(',');\n            const row: any = {};\n            \n            headers.forEach((header, index) => {\n              row[header.trim()] = values[index]?.trim();\n            });\n            \n            // 验证数据\n            if (!row['BOM编码'] || !row['BOM名称']) {\n              errors.push(`第${i + 1}行：BOM编码和名称不能为空`);\n            } else {\n              importedData.push({\n                code: row['BOM编码'],\n                name: row['BOM名称'],\n                version: row['版本'] || 'V1.0',\n                status: row['状态'] || 'draft',\n                description: row['描述'] || ''\n              });\n            }\n          }\n        }\n        \n        setImportResult({\n          total: lines.length - 1,\n          success: importedData.length,\n          errors: errors.length,\n          data: importedData,\n          errorMessages: errors\n        });\n        \n        setImportStatus(errors.length > 0 ? 'error' : 'success');\n        \n        if (errors.length === 0) {\n          message.success(`成功导入${importedData.length}条数据`);\n          // TODO: 调用API保存数据\n        } else {\n          message.warning(`导入完成，${errors.length}条数据有错误`);\n        }\n        \n      } catch (error) {\n        setImportStatus('error');\n        setImportResult({\n          total: 0,\n          success: 0,\n          errors: 1,\n          errorMessages: ['文件格式错误，请检查文件内容']\n        });\n        message.error('文件解析失败');\n      }\n    };\n    \n    reader.readAsText(file);\n  };\n  \n  // 表格行选择\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    onSelectAll: (selected: boolean, selectedRows: CoreBOM[], changeRows: CoreBOM[]) => {\n      console.log('Select all:', selected, selectedRows, changeRows);\n    },\n  };\n  \n  // 清空选择\n  const handleClearSelection = () => {\n    setSelectedRowKeys([]);\n  };\n  \n  // 导出选中项\n  const handleExportSelected = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请先选择要导出的数据');\n      return;\n    }\n    setExportModalVisible(true);\n  };\n  \n  // 模拟版本历史数据\n  const getVersionHistory = (bom: CoreBOM) => {\n    return [\n      {\n        version: 'V1.2',\n        status: 'active',\n        description: '优化物料清单，更新供应商信息',\n        createdBy: '李工程师',\n        createdAt: '2024-03-25 14:30:00',\n        changes: ['更新供应商信息', '调整物料数量', '优化成本结构']\n      },\n      {\n        version: 'V1.1',\n        status: 'frozen',\n        description: '修复物料规格错误',\n        createdBy: '王工程师',\n        createdAt: '2024-03-20 10:15:00',\n        changes: ['修正物料规格', '更新技术参数']\n      },\n      {\n        version: 'V1.0',\n        status: 'obsolete',\n        description: '初始版本',\n        createdBy: '张工程师',\n        createdAt: '2024-03-15 09:00:00',\n        changes: ['创建初始BOM结构']\n      }\n    ];\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case BOM_STATUS.DRAFT: return 'default';\n      case BOM_STATUS.ACTIVE: return 'success';\n      case BOM_STATUS.FROZEN: return 'blue';\n      case BOM_STATUS.OBSOLETE: return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case BOM_STATUS.DRAFT: return '草稿';\n      case BOM_STATUS.ACTIVE: return '激活';\n      case BOM_STATUS.FROZEN: return '冻结';\n      case BOM_STATUS.OBSOLETE: return '废弃';\n      default: return status;\n    }\n  };\n\n  // 可导出字段选项\n  const exportFieldOptions = [\n    { label: 'BOM编码', value: 'code' },\n    { label: 'BOM名称', value: 'name' },\n    { label: '版本', value: 'version' },\n    { label: '状态', value: 'status' },\n    { label: '描述', value: 'description' },\n    { label: '创建人', value: 'createdBy' },\n    { label: '创建时间', value: 'createdAt' },\n    { label: '更新时间', value: 'updatedAt' }\n  ];\n\n  const getActionMenuItems = (record: CoreBOM): MenuProps['items'] => [\n    {\n      key: 'copy',\n      icon: <CopyOutlined />,\n      label: '复制',\n      onClick: () => handleCopy(record),\n    },\n    {\n      key: 'history',\n      icon: <HistoryOutlined />,\n      label: '版本历史',\n      onClick: () => handleVersionHistory(record),\n    },\n    {\n      key: 'freeze',\n      icon: record.status === BOM_STATUS.FROZEN ? <UnlockOutlined /> : <LockOutlined />,\n      label: record.status === BOM_STATUS.FROZEN ? '解冻' : '冻结',\n      onClick: () => handleFreeze(record),\n      disabled: record.status === BOM_STATUS.DRAFT,\n    },\n  ];\n\n  const columns = [\n    {\n      title: 'BOM编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 120,\n      render: (text: string, record: CoreBOM) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: 'BOM名称',\n      dataIndex: 'name',\n      key: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '版本',\n      dataIndex: 'version',\n      key: 'version',\n      width: 80,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true,\n    },\n    {\n      title: '创建人',\n      dataIndex: 'createdBy',\n      key: 'createdBy',\n      width: 100,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: CoreBOM) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n              disabled={record.status === BOM_STATUS.FROZEN}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => handleDelete(record)}\n              disabled={record.status !== BOM_STATUS.DRAFT}\n            />\n          </Tooltip>\n          <Dropdown\n            menu={{ items: getActionMenuItems(record) }}\n            trigger={['click']}\n          >\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  // 模拟数据\n  const mockData: CoreBOM[] = [\n    {\n      id: '1',\n      name: '5G基站天线BOM',\n      code: 'ANT-5G-001',\n      version: 'V1.0',\n      description: '5G基站用高增益天线物料清单',\n      status: 'ACTIVE',\n      items: [],\n      configRules: [],\n      createdBy: 'admin',\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-15T00:00:00Z',\n    },\n    {\n      id: '2',\n      name: '4G室内天线BOM',\n      code: 'ANT-4G-002',\n      version: 'V2.1',\n      description: '4G室内覆盖天线物料清单',\n      status: 'FROZEN',\n      items: [],\n      configRules: [],\n      createdBy: 'bom_manager',\n      createdAt: '2024-01-02T00:00:00Z',\n      updatedAt: '2024-01-16T00:00:00Z',\n      frozenAt: '2024-01-16T00:00:00Z',\n      frozenBy: 'admin',\n    },\n    {\n      id: '3',\n      name: 'WiFi天线BOM',\n      code: 'ANT-WIFI-003',\n      version: 'V1.5',\n      description: 'WiFi6天线物料清单',\n      status: 'DRAFT',\n      items: [],\n      configRules: [],\n      createdBy: 'bom_manager',\n      createdAt: '2024-01-03T00:00:00Z',\n      updatedAt: '2024-01-17T00:00:00Z',\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              核心BOM管理\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<ImportOutlined />} onClick={handleImport}>\n                导入\n              </Button>\n              <Button icon={<ExportOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleCreate}>\n                新建BOM\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n          <Col xs={24} sm={12} md={8}>\n            <Search\n              placeholder=\"搜索BOM编码或名称\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Select\n              placeholder=\"状态筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              onChange={handleStatusFilter}\n              options={[\n                { label: '草稿', value: BOM_STATUS.DRAFT },\n                { label: '激活', value: BOM_STATUS.ACTIVE },\n                { label: '冻结', value: BOM_STATUS.FROZEN },\n                { label: '废弃', value: BOM_STATUS.OBSOLETE },\n              ]}\n            />\n          </Col>\n        </Row>\n\n        {selectedRowKeys.length > 0 && (\n          <div style={{ marginBottom: 16, padding: '8px 16px', backgroundColor: '#f0f2f5', borderRadius: 6 }}>\n            <span style={{ marginRight: 16 }}>已选择 {selectedRowKeys.length} 项</span>\n            <Button size=\"small\" onClick={handleClearSelection} style={{ marginRight: 8 }}>\n              清空选择\n            </Button>\n            <Button size=\"small\" type=\"primary\" icon={<DownloadOutlined />} onClick={handleExportSelected}>\n              导出选中\n            </Button>\n          </div>\n        )}\n\n        <Table\n          columns={columns}\n          dataSource={mockData}\n          loading={loading}\n          rowKey=\"id\"\n          rowSelection={rowSelection}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 复制BOM模态框 */}\n      <Modal\n        title=\"复制BOM\"\n        open={copyModalVisible}\n        onOk={handleCopyConfirm}\n        onCancel={() => setCopyModalVisible(false)}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form form={copyForm} layout=\"vertical\">\n          <Form.Item\n            name=\"name\"\n            label=\"BOM名称\"\n            rules={[{ required: true, message: '请输入BOM名称' }]}\n          >\n            <Input placeholder=\"请输入BOM名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"code\"\n            label=\"BOM编码\"\n            rules={[{ required: true, message: '请输入BOM编码' }]}\n          >\n            <Input placeholder=\"请输入BOM编码\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n      \n      {/* 版本历史模态框 */}\n      <Modal\n          title={`版本历史 - ${currentBOMForHistory?.name}`}\n          open={versionHistoryVisible}\n          onCancel={() => setVersionHistoryVisible(false)}\n          footer={[\n            <Button key=\"close\" onClick={() => setVersionHistoryVisible(false)}>\n              关闭\n            </Button>\n          ]}\n          width={800}\n        >\n          {currentBOMForHistory && (\n            <div>\n              <Descriptions\n                title=\"当前BOM信息\"\n                bordered\n                size=\"small\"\n                style={{ marginBottom: 24 }}\n              >\n                <Descriptions.Item label=\"BOM编码\">{currentBOMForHistory.code}</Descriptions.Item>\n                <Descriptions.Item label=\"BOM名称\">{currentBOMForHistory.name}</Descriptions.Item>\n                <Descriptions.Item label=\"当前版本\">{currentBOMForHistory.version}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\" span={3}>\n                  <Tag color={getStatusColor(currentBOMForHistory.status)}>\n                    {getStatusText(currentBOMForHistory.status)}\n                  </Tag>\n                </Descriptions.Item>\n              </Descriptions>\n              \n              <Divider>版本历史</Divider>\n              \n              <Timeline>\n                {getVersionHistory(currentBOMForHistory).map((version, index) => (\n                  <Timeline.Item\n                    key={index}\n                    dot={<ClockCircleOutlined style={{ fontSize: '16px' }} />}\n                    color={version.status === 'active' ? 'green' : version.status === 'frozen' ? 'blue' : 'gray'}\n                  >\n                    <div style={{ marginBottom: 16 }}>\n                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>\n                        <span style={{ fontSize: 16, fontWeight: 'bold' }}>{version.version}</span>\n                        <Tag color={getStatusColor(version.status)}>\n                          {getStatusText(version.status)}\n                        </Tag>\n                      </div>\n                      <div style={{ color: '#666', marginBottom: 8 }}>\n                        {version.createdBy} · {version.createdAt}\n                      </div>\n                      <div style={{ marginBottom: 8 }}>{version.description}</div>\n                      {version.changes && version.changes.length > 0 && (\n                        <div>\n                          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>主要变更：</div>\n                          <ul style={{ margin: 0, paddingLeft: 20 }}>\n                            {version.changes.map((change, changeIndex) => (\n                              <li key={changeIndex}>{change}</li>\n                            ))}\n                          </ul>\n                        </div>\n                      )}\n                    </div>\n                  </Timeline.Item>\n                ))}\n              </Timeline>\n            </div>\n          )}\n      </Modal>\n      \n      {/* 导出模态框 */}\n      <Modal\n          title=\"导出BOM数据\"\n          open={exportModalVisible}\n          onOk={handleExportConfirm}\n          onCancel={() => setExportModalVisible(false)}\n          width={600}\n        >\n          <Form form={exportForm} layout=\"vertical\">\n            <Alert\n              message=\"导出说明\"\n              description={selectedRowKeys.length > 0 ? `将导出已选择的 ${selectedRowKeys.length} 条BOM数据` : \"将导出所有BOM数据\"}\n              type=\"info\"\n              style={{ marginBottom: 16 }}\n            />\n            \n            <Form.Item label=\"导出格式\" name=\"format\" initialValue={exportFormat}>\n              <Radio.Group onChange={(e) => setExportFormat(e.target.value)}>\n                <Radio value=\"excel\">Excel格式 (.xlsx)</Radio>\n                <Radio value=\"csv\">CSV格式 (.csv)</Radio>\n              </Radio.Group>\n            </Form.Item>\n            \n            <Form.Item label=\"导出字段\" name=\"fields\" initialValue={exportFields}>\n              <Checkbox.Group\n                options={exportFieldOptions}\n                value={exportFields}\n                onChange={setExportFields}\n              />\n            </Form.Item>\n          </Form>\n      </Modal>\n      \n      {/* 导入模态框 */}\n      <Modal\n          title=\"导入BOM数据\"\n          open={importModalVisible}\n          onCancel={() => {\n            setImportModalVisible(false);\n            setImportStatus(null);\n            setImportProgress(0);\n            setImportResult(null);\n            importForm.resetFields();\n          }}\n          footer={[\n            <Button key=\"template\" icon={<DownloadOutlined />} onClick={handleDownloadTemplate}>\n              下载模板\n            </Button>,\n            <Button key=\"cancel\" onClick={() => {\n              setImportModalVisible(false);\n              setImportStatus(null);\n              setImportProgress(0);\n              setImportResult(null);\n              importForm.resetFields();\n            }}>\n              取消\n            </Button>\n          ]}\n          width={600}\n        >\n          <Form form={importForm} layout=\"vertical\">\n            <Alert\n              message=\"导入说明\"\n              description={\n                <div>\n                  <p>1. 请下载导入模板，按照模板格式填写数据</p>\n                  <p>2. 支持CSV格式文件导入</p>\n                  <p>3. BOM编码和名称为必填字段</p>\n                  <p>4. 导入前请确保数据格式正确</p>\n                </div>\n              }\n              type=\"info\"\n              style={{ marginBottom: 16 }}\n            />\n            \n            <Form.Item label=\"选择文件\">\n              <Upload.Dragger\n                accept=\".csv,.xlsx,.xls\"\n                beforeUpload={handleFileUpload}\n                showUploadList={false}\n                disabled={importStatus === 'uploading' || importStatus === 'processing'}\n              >\n                <p className=\"ant-upload-drag-icon\">\n                  <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">支持CSV、Excel格式文件</p>\n              </Upload.Dragger>\n            </Form.Item>\n            \n            {importStatus && (\n              <div style={{ marginTop: 16 }}>\n                {importStatus === 'uploading' && (\n                  <div>\n                    <div style={{ marginBottom: 8 }}>正在上传文件...</div>\n                    <Progress percent={importProgress} status=\"active\" />\n                  </div>\n                )}\n                \n                {importStatus === 'processing' && (\n                  <div>\n                    <div style={{ marginBottom: 8 }}>正在处理数据...</div>\n                    <Progress percent={100} status=\"active\" />\n                  </div>\n                )}\n                \n                {importStatus === 'success' && importResult && (\n                  <Alert\n                    message=\"导入成功\"\n                    description={`成功导入 ${importResult.success} 条数据`}\n                    type=\"success\"\n                    icon={<CheckCircleOutlined />}\n                  />\n                )}\n                \n                {importStatus === 'error' && importResult && (\n                  <div>\n                    <Alert\n                      message=\"导入完成，部分数据有错误\"\n                      description={`成功：${importResult.success} 条，错误：${importResult.errors} 条`}\n                      type=\"warning\"\n                      icon={<ExclamationCircleOutlined />}\n                      style={{ marginBottom: 16 }}\n                    />\n                    \n                    {importResult.errorMessages && importResult.errorMessages.length > 0 && (\n                      <div>\n                        <div style={{ fontWeight: 'bold', marginBottom: 8 }}>错误详情：</div>\n                        <div style={{ maxHeight: 200, overflow: 'auto', backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>\n                          {importResult.errorMessages.map((error: string, index: number) => (\n                            <div key={index} style={{ color: '#ff4d4f', marginBottom: 4 }}>\n                              {error}\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            )}\n          </Form>\n        </Modal>\n    </div>\n  );\n};\n\nexport default React.memo(CoreBOMListPage);\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAWC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,QAAQ,EAERC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,QAAQ,EACRC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EAEZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,gBAAgB,EAEhBC,iBAAiB,EACjBC,mBAAmB,EACnBC,yBAAyB,EACzBC,mBAAmB,QACd,mBAAmB;AAE1B,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,aAAa,EAAEC,aAAa,EAAEC,aAAa,QAAQ,6BAA6B;AAEzF,SAASC,MAAM,EAAEC,UAAU,QAAQ,iBAAiB;AACpD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,YAAY,EAAEC,SAAS,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAM;EAAEC;AAAM,CAAC,GAAG1C,UAAU;AAC5B,MAAM;EAAE2C;AAAO,CAAC,GAAGlD,KAAK;AAExB,MAAMmD,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGzD,WAAW,CAAC,CAAC;EAC9B,MAAM0D,QAAQ,GAAGlB,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEmB,QAAQ;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGpB,cAAc,CAACqB,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EAE5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAiB,IAAI,CAAC;EAClE,MAAM,CAAC0E,QAAQ,CAAC,GAAG9D,IAAI,CAAC+D,OAAO,CAAC,CAAC;;EAEjC;EACA,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAiB,IAAI,CAAC;;EAEtF;EACA,MAAM,CAACgF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,OAAO,CAAC;EACzD,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAW,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;EAChH,MAAM,CAACwF,cAAc,EAAEC,iBAAiB,CAAC,GAAGzF,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAA0D,IAAI,CAAC;EAC/G,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAAC8F,eAAe,EAAEC,kBAAkB,CAAC,GAAG/F,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAACgG,UAAU,CAAC,GAAGpF,IAAI,CAAC+D,OAAO,CAAC,CAAC;EACnC,MAAM,CAACsB,UAAU,CAAC,GAAGrF,IAAI,CAAC+D,OAAO,CAAC,CAAC;EAEnC5E,SAAS,CAAC,MAAM;IACdmG,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACnC,UAAU,CAACoC,OAAO,EAAEpC,UAAU,CAACqC,QAAQ,EAAElC,aAAa,EAAEE,YAAY,CAAC,CAAC;EAE1E,MAAM8B,QAAQ,GAAGjG,WAAW,CAAC,MAAM;IACjC2D,QAAQ,CAAChB,aAAa,CAAC;MACrByD,IAAI,EAAEtC,UAAU,CAACoC,OAAO;MACxBC,QAAQ,EAAErC,UAAU,CAACqC,QAAQ;MAC7BE,OAAO,EAAEpC;IACX,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACN,QAAQ,EAAEG,UAAU,CAACoC,OAAO,EAAEpC,UAAU,CAACqC,QAAQ,EAAElC,aAAa,CAAC,CAAC;EAEtE,MAAMqC,YAAY,GAAGtG,WAAW,CAAEuG,KAAa,IAAK;IAClDrC,gBAAgB,CAACqC,KAAK,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,kBAAkB,GAAGxG,WAAW,CAAEuG,KAAa,IAAK;IACxDnC,eAAe,CAACmC,KAAK,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,YAAY,GAAGzG,WAAW,CAAC,MAAM;IACrC0D,QAAQ,CAACZ,MAAM,CAAC4D,eAAe,CAAC;EAClC,CAAC,EAAE,CAAChD,QAAQ,CAAC,CAAC;EAEd,MAAMiD,UAAU,GAAG3G,WAAW,CAAE4G,MAAe,IAAK;IAClDlD,QAAQ,CAACZ,MAAM,CAAC+D,aAAa,CAACC,OAAO,CAAC,KAAK,EAAEF,MAAM,CAACG,EAAE,CAAC,CAAC;EAC1D,CAAC,EAAE,CAACrD,QAAQ,CAAC,CAAC;EAEd,MAAMsD,UAAU,GAAGhH,WAAW,CAAE4G,MAAe,IAAK;IAClDlD,QAAQ,CAACZ,MAAM,CAACmE,aAAa,CAACH,OAAO,CAAC,KAAK,EAAEF,MAAM,CAACG,EAAE,CAAC,CAAC;EAC1D,CAAC,EAAE,CAACrD,QAAQ,CAAC,CAAC;EAEd,MAAMwD,YAAY,GAAGlH,WAAW,CAAC,MAAO4G,MAAe,IAAK;IAC1D3D,aAAa,CAACkE,OAAO,CAAC;MACpBC,KAAK,EAAE,MAAM;MACbC,OAAO,eACLhE,OAAA;QAAAiE,QAAA,gBACEjE,OAAA;UAAAiE,QAAA,EAAG;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnBrE,OAAA;UAAAiE,QAAA,gBAAGjE,OAAA;YAAAiE,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAACd,MAAM,CAACe,IAAI;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CrE,OAAA;UAAAiE,QAAA,gBAAGjE,OAAA;YAAAiE,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAACd,MAAM,CAACgB,IAAI;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CrE,OAAA;UAAGwE,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,SAAS,EAAE;UAAG,CAAE;UAAAT,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACN;MACDM,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAI;UACF,MAAMtE,QAAQ,CAACf,aAAa,CAACgE,MAAM,CAACG,EAAE,CAAC,CAAC,CAACmB,MAAM,CAAC,CAAC;UACjDzH,OAAO,CAAC0H,OAAO,CAAC,MAAM,CAAC;UACvBlC,QAAQ,CAAC,CAAC;QACZ,CAAC,CAAC,OAAOmC,KAAU,EAAE;UACnBlF,YAAY,CAACmF,WAAW,CAAC;YACvBL,IAAI,EAAE7E,SAAS,CAACmF,QAAQ;YACxB7H,OAAO,EAAE2H,KAAK,CAAC3H,OAAO,IAAI,MAAM;YAChC8H,OAAO,EAAEH;UACX,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzE,QAAQ,EAAEsC,QAAQ,CAAC,CAAC;EAExB,MAAMuC,YAAY,GAAGxI,WAAW,CAAC,MAAO4G,MAAe,IAAK;IAC1D,MAAM6B,QAAQ,GAAG7B,MAAM,CAAC8B,MAAM,KAAK3F,UAAU,CAAC4F,MAAM;IACpD,MAAMC,MAAM,GAAGH,QAAQ,GAAG,IAAI,GAAG,IAAI;IAErCxF,aAAa,CAACkE,OAAO,CAAC;MACpBC,KAAK,EAAE,KAAKwB,MAAM,EAAE;MACpBvB,OAAO,eACLhE,OAAA;QAAAiE,QAAA,gBACEjE,OAAA;UAAAiE,QAAA,GAAG,oBAAG,EAACsB,MAAM,EAAC,6BAAO;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzBrE,OAAA;UAAAiE,QAAA,gBAAGjE,OAAA;YAAAiE,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAACd,MAAM,CAACe,IAAI;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CrE,OAAA;UAAAiE,QAAA,gBAAGjE,OAAA;YAAAiE,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAACd,MAAM,CAACgB,IAAI;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC1Ce,QAAQ,iBACPpF,OAAA;UAAGwE,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,SAAS,EAAE;UAAG,CAAE;UAAAT,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACjE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;MACDM,IAAI,EAAES,QAAQ,GAAG,SAAS,GAAG,MAAM;MACnCR,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAI;UACF,MAAMtE,QAAQ,CAACd,aAAa,CAAC+D,MAAM,CAACG,EAAE,CAAC,CAAC,CAACmB,MAAM,CAAC,CAAC;UACjDzH,OAAO,CAAC0H,OAAO,CAAC,GAAGS,MAAM,IAAI,CAAC;UAC9B3C,QAAQ,CAAC,CAAC;QACZ,CAAC,CAAC,OAAOmC,KAAU,EAAE;UACnBlF,YAAY,CAACmF,WAAW,CAAC;YACvBL,IAAI,EAAE7E,SAAS,CAACmF,QAAQ;YACxB7H,OAAO,EAAE2H,KAAK,CAAC3H,OAAO,IAAI,GAAGmI,MAAM,IAAI;YACvCL,OAAO,EAAEH;UACX,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzE,QAAQ,EAAEsC,QAAQ,CAAC,CAAC;EAExB,MAAM4C,UAAU,GAAG7I,WAAW,CAAE4G,MAAe,IAAK;IAClDpC,aAAa,CAACoC,MAAM,CAAC;IACrBnC,QAAQ,CAACqE,cAAc,CAAC;MACtBlB,IAAI,EAAE,GAAGhB,MAAM,CAACgB,IAAI,KAAK;MACzBD,IAAI,EAAE,GAAGf,MAAM,CAACe,IAAI;IACtB,CAAC,CAAC;IACFrD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC,EAAE,CAACG,QAAQ,CAAC,CAAC;EAEd,MAAMsE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMvE,QAAQ,CAACwE,cAAc,CAAC,CAAC;MAC9C;MACAxI,OAAO,CAAC0H,OAAO,CAAC,MAAM,CAAC;MACvB7D,mBAAmB,CAAC,KAAK,CAAC;MAC1B2B,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACd3H,OAAO,CAAC2H,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMc,oBAAoB,GAAItC,MAAe,IAAK;IAChD9B,uBAAuB,CAAC8B,MAAM,CAAC;IAC/BhC,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMuE,YAAY,GAAGA,CAAA,KAAM;IACzBnE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMoE,YAAY,GAAGA,CAAA,KAAM;IACzBlE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMmE,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACA,MAAMC,YAAY,GAAG,CACnB;MACE,OAAO,EAAE,QAAQ;MACjB,OAAO,EAAE,OAAO;MAChB,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,OAAO;MACb,IAAI,EAAE;IACR,CAAC,CACF;;IAED;IACA,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAMI,UAAU,GAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGL,YAAY,CAACM,GAAG,CAACC,GAAG,IAAIL,MAAM,CAACR,MAAM,CAACa,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;;IAE3G;IACA,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;MAAE1B,IAAI,EAAE;IAA0B,CAAC,CAAC;IACxE,MAAMgC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;IACrCE,IAAI,CAACM,QAAQ,GAAG,uBAAuB;IACvCN,IAAI,CAACO,KAAK,CAAC,CAAC;IAEZ9J,OAAO,CAAC0H,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMqC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMxB,MAAM,GAAG,MAAMjD,UAAU,CAACkD,cAAc,CAAC,CAAC;;MAEhD;MACA,IAAIwB,UAAU,GAAGC,QAAQ;MACzB,IAAI7E,eAAe,CAAC8E,MAAM,GAAG,CAAC,EAAE;QAC9BF,UAAU,GAAGC,QAAQ,CAACE,MAAM,CAACC,IAAI,IAAIhF,eAAe,CAACiF,QAAQ,CAACD,IAAI,CAAC9D,EAAE,CAAC,CAAC;MACzE;;MAEA;MACA,MAAMgE,YAAY,GAAGN,UAAU,CAACb,GAAG,CAACiB,IAAI,IAAI;QAC1C,MAAMG,YAAiB,GAAG,CAAC,CAAC;QAC5B3F,YAAY,CAAC4F,OAAO,CAACC,KAAK,IAAI;UAC5B,QAAQA,KAAK;YACX,KAAK,MAAM;cACTF,YAAY,CAAC,OAAO,CAAC,GAAGH,IAAI,CAAClD,IAAI;cACjC;YACF,KAAK,MAAM;cACTqD,YAAY,CAAC,OAAO,CAAC,GAAGH,IAAI,CAACjD,IAAI;cACjC;YACF,KAAK,SAAS;cACZoD,YAAY,CAAC,IAAI,CAAC,GAAGH,IAAI,CAACM,OAAO;cACjC;YACF,KAAK,QAAQ;cACXH,YAAY,CAAC,IAAI,CAAC,GAAGI,aAAa,CAACP,IAAI,CAACnC,MAAM,CAAC;cAC/C;YACF,KAAK,aAAa;cAChBsC,YAAY,CAAC,IAAI,CAAC,GAAGH,IAAI,CAACQ,WAAW;cACrC;YACF,KAAK,WAAW;cACdL,YAAY,CAAC,KAAK,CAAC,GAAGH,IAAI,CAACS,SAAS;cACpC;YACF,KAAK,WAAW;cACdN,YAAY,CAAC,MAAM,CAAC,GAAGhI,UAAU,CAAC6H,IAAI,CAACU,SAAS,CAAC;cACjD;YACF,KAAK,WAAW;cACdP,YAAY,CAAC,MAAM,CAAC,GAAGhI,UAAU,CAAC6H,IAAI,CAACW,SAAS,CAAC;cACjD;UACJ;QACF,CAAC,CAAC;QACF,OAAOR,YAAY;MACrB,CAAC,CAAC;;MAEF;MACA,IAAI7F,YAAY,KAAK,OAAO,EAAE;QAC5B;QACA1E,OAAO,CAACgL,IAAI,CAAC,oBAAoB,CAAC;MACpC,CAAC,MAAM;QACL;QACA,MAAMlC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACsB,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAClD,MAAMrB,UAAU,GAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGoB,YAAY,CAACnB,GAAG,CAACC,GAAG,IAAIL,MAAM,CAACR,MAAM,CAACa,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;QAE3G,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;UAAE1B,IAAI,EAAE;QAA0B,CAAC,CAAC;QACxE,MAAMgC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;QACrCE,IAAI,CAACM,QAAQ,GAAG,mBAAmB,IAAIoB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,MAAM;QAC7D3B,IAAI,CAACO,KAAK,CAAC,CAAC;MACd;MAEAvF,qBAAqB,CAAC,KAAK,CAAC;MAC5BvE,OAAO,CAAC0H,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3H,OAAO,CAAC2H,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwD,gBAAgB,GAAIC,IAAU,IAAK;IACvCnG,eAAe,CAAC,WAAW,CAAC;IAC5BF,iBAAiB,CAAC,CAAC,CAAC;;IAEpB;IACA,MAAMsG,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCvG,iBAAiB,CAACwG,IAAI,IAAI;QACxB,IAAIA,IAAI,IAAI,GAAG,EAAE;UACfC,aAAa,CAACH,QAAQ,CAAC;UACvBpG,eAAe,CAAC,YAAY,CAAC;;UAE7B;UACAwG,UAAU,CAAC,MAAM;YACfC,iBAAiB,CAACN,IAAI,CAAC;UACzB,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,GAAG;QACZ;QACA,OAAOG,IAAI,GAAG,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIN,IAAU,IAAK;IACxC,MAAMO,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MACrB,IAAI;QAAA,IAAAC,SAAA;QACF,MAAMnF,OAAO,IAAAmF,SAAA,GAAGD,CAAC,CAACE,MAAM,cAAAD,SAAA,uBAARA,SAAA,CAAUE,MAAgB;QAC1C,MAAMC,KAAK,GAAGtF,OAAO,CAACuF,KAAK,CAAC,IAAI,CAAC;QACjC,MAAMrD,OAAO,GAAGoD,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;QAEnC,MAAMC,YAAY,GAAG,EAAE;QACvB,MAAMC,MAAM,GAAG,EAAE;QAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAAChC,MAAM,EAAEoC,CAAC,EAAE,EAAE;UACrC,IAAIJ,KAAK,CAACI,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,EAAE;YACnB,MAAMhE,MAAM,GAAG2D,KAAK,CAACI,CAAC,CAAC,CAACH,KAAK,CAAC,GAAG,CAAC;YAClC,MAAM/C,GAAQ,GAAG,CAAC,CAAC;YAEnBN,OAAO,CAAC0B,OAAO,CAAC,CAACgC,MAAM,EAAEC,KAAK,KAAK;cAAA,IAAAC,aAAA;cACjCtD,GAAG,CAACoD,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,IAAAG,aAAA,GAAGnE,MAAM,CAACkE,KAAK,CAAC,cAAAC,aAAA,uBAAbA,aAAA,CAAeH,IAAI,CAAC,CAAC;YAC5C,CAAC,CAAC;;YAEF;YACA,IAAI,CAACnD,GAAG,CAAC,OAAO,CAAC,IAAI,CAACA,GAAG,CAAC,OAAO,CAAC,EAAE;cAClCiD,MAAM,CAACM,IAAI,CAAC,IAAIL,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACxC,CAAC,MAAM;cACLF,YAAY,CAACO,IAAI,CAAC;gBAChBzF,IAAI,EAAEkC,GAAG,CAAC,OAAO,CAAC;gBAClBjC,IAAI,EAAEiC,GAAG,CAAC,OAAO,CAAC;gBAClBsB,OAAO,EAAEtB,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM;gBAC5BnB,MAAM,EAAEmB,GAAG,CAAC,IAAI,CAAC,IAAI,OAAO;gBAC5BwB,WAAW,EAAExB,GAAG,CAAC,IAAI,CAAC,IAAI;cAC5B,CAAC,CAAC;YACJ;UACF;QACF;QAEAjE,eAAe,CAAC;UACdyH,KAAK,EAAEV,KAAK,CAAChC,MAAM,GAAG,CAAC;UACvBxC,OAAO,EAAE0E,YAAY,CAAClC,MAAM;UAC5BmC,MAAM,EAAEA,MAAM,CAACnC,MAAM;UACrB2C,IAAI,EAAET,YAAY;UAClBU,aAAa,EAAET;QACjB,CAAC,CAAC;QAEFpH,eAAe,CAACoH,MAAM,CAACnC,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;QAExD,IAAImC,MAAM,CAACnC,MAAM,KAAK,CAAC,EAAE;UACvBlK,OAAO,CAAC0H,OAAO,CAAC,OAAO0E,YAAY,CAAClC,MAAM,KAAK,CAAC;UAChD;QACF,CAAC,MAAM;UACLlK,OAAO,CAAC+M,OAAO,CAAC,QAAQV,MAAM,CAACnC,MAAM,QAAQ,CAAC;QAChD;MAEF,CAAC,CAAC,OAAOvC,KAAK,EAAE;QACd1C,eAAe,CAAC,OAAO,CAAC;QACxBE,eAAe,CAAC;UACdyH,KAAK,EAAE,CAAC;UACRlF,OAAO,EAAE,CAAC;UACV2E,MAAM,EAAE,CAAC;UACTS,aAAa,EAAE,CAAC,gBAAgB;QAClC,CAAC,CAAC;QACF9M,OAAO,CAAC2H,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC;IAEDgE,MAAM,CAACqB,UAAU,CAAC5B,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM6B,YAAY,GAAG;IACnB7H,eAAe;IACf8H,QAAQ,EAAGC,kBAA+B,IAAK;MAC7C9H,kBAAkB,CAAC8H,kBAAkB,CAAC;IACxC,CAAC;IACDC,WAAW,EAAEA,CAACC,QAAiB,EAAEC,YAAuB,EAAEC,UAAqB,KAAK;MAClFC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEJ,QAAQ,EAAEC,YAAY,EAAEC,UAAU,CAAC;IAChE;EACF,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjCrI,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMsI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIvI,eAAe,CAAC8E,MAAM,KAAK,CAAC,EAAE;MAChClK,OAAO,CAAC+M,OAAO,CAAC,YAAY,CAAC;MAC7B;IACF;IACAxI,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMqJ,iBAAiB,GAAIrK,GAAY,IAAK;IAC1C,OAAO,CACL;MACEmH,OAAO,EAAE,MAAM;MACfzC,MAAM,EAAE,QAAQ;MAChB2C,WAAW,EAAE,gBAAgB;MAC7BC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,qBAAqB;MAChC+C,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ;IACzC,CAAC,EACD;MACEnD,OAAO,EAAE,MAAM;MACfzC,MAAM,EAAE,QAAQ;MAChB2C,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,qBAAqB;MAChC+C,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ;IAC9B,CAAC,EACD;MACEnD,OAAO,EAAE,MAAM;MACfzC,MAAM,EAAE,UAAU;MAClB2C,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,qBAAqB;MAChC+C,OAAO,EAAE,CAAC,WAAW;IACvB,CAAC,CACF;EACH,CAAC;EAED,MAAMC,cAAc,GAAI7F,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK3F,UAAU,CAACyL,KAAK;QAAE,OAAO,SAAS;MACvC,KAAKzL,UAAU,CAAC4F,MAAM;QAAE,OAAO,SAAS;MACxC,KAAK5F,UAAU,CAAC0L,MAAM;QAAE,OAAO,MAAM;MACrC,KAAK1L,UAAU,CAAC2L,QAAQ;QAAE,OAAO,OAAO;MACxC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMtD,aAAa,GAAI1C,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK3F,UAAU,CAACyL,KAAK;QAAE,OAAO,IAAI;MAClC,KAAKzL,UAAU,CAAC4F,MAAM;QAAE,OAAO,IAAI;MACnC,KAAK5F,UAAU,CAAC0L,MAAM;QAAE,OAAO,IAAI;MACnC,KAAK1L,UAAU,CAAC2L,QAAQ;QAAE,OAAO,IAAI;MACrC;QAAS,OAAOhG,MAAM;IACxB;EACF,CAAC;;EAED;EACA,MAAMiG,kBAAkB,GAAG,CACzB;IAAEC,KAAK,EAAE,OAAO;IAAErI,KAAK,EAAE;EAAO,CAAC,EACjC;IAAEqI,KAAK,EAAE,OAAO;IAAErI,KAAK,EAAE;EAAO,CAAC,EACjC;IAAEqI,KAAK,EAAE,IAAI;IAAErI,KAAK,EAAE;EAAU,CAAC,EACjC;IAAEqI,KAAK,EAAE,IAAI;IAAErI,KAAK,EAAE;EAAS,CAAC,EAChC;IAAEqI,KAAK,EAAE,IAAI;IAAErI,KAAK,EAAE;EAAc,CAAC,EACrC;IAAEqI,KAAK,EAAE,KAAK;IAAErI,KAAK,EAAE;EAAY,CAAC,EACpC;IAAEqI,KAAK,EAAE,MAAM;IAAErI,KAAK,EAAE;EAAY,CAAC,EACrC;IAAEqI,KAAK,EAAE,MAAM;IAAErI,KAAK,EAAE;EAAY,CAAC,CACtC;EAED,MAAMsI,kBAAkB,GAAIjI,MAAe,IAAyB,CAClE;IACEkI,GAAG,EAAE,MAAM;IACXC,IAAI,eAAE1L,OAAA,CAACxB,YAAY;MAAA0F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBkH,KAAK,EAAE,IAAI;IACXI,OAAO,EAAEA,CAAA,KAAMnG,UAAU,CAACjC,MAAM;EAClC,CAAC,EACD;IACEkI,GAAG,EAAE,SAAS;IACdC,IAAI,eAAE1L,OAAA,CAAClB,eAAe;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBkH,KAAK,EAAE,MAAM;IACbI,OAAO,EAAEA,CAAA,KAAM9F,oBAAoB,CAACtC,MAAM;EAC5C,CAAC,EACD;IACEkI,GAAG,EAAE,QAAQ;IACbC,IAAI,EAAEnI,MAAM,CAAC8B,MAAM,KAAK3F,UAAU,CAAC0L,MAAM,gBAAGpL,OAAA,CAACtB,cAAc;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACvB,YAAY;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjFkH,KAAK,EAAEhI,MAAM,CAAC8B,MAAM,KAAK3F,UAAU,CAAC0L,MAAM,GAAG,IAAI,GAAG,IAAI;IACxDO,OAAO,EAAEA,CAAA,KAAMxG,YAAY,CAAC5B,MAAM,CAAC;IACnCqI,QAAQ,EAAErI,MAAM,CAAC8B,MAAM,KAAK3F,UAAU,CAACyL;EACzC,CAAC,CACF;EAED,MAAMU,OAAO,GAAG,CACd;IACE9H,KAAK,EAAE,OAAO;IACd+H,SAAS,EAAE,MAAM;IACjBL,GAAG,EAAE,MAAM;IACXM,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,IAAY,EAAE1I,MAAe,kBACpCvD,OAAA,CAAClD,MAAM;MAAC6H,IAAI,EAAC,MAAM;MAACgH,OAAO,EAAEA,CAAA,KAAMhI,UAAU,CAACJ,MAAM,CAAE;MAAAU,QAAA,EACnDgI;IAAI;MAAA/H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAEZ,CAAC,EACD;IACEN,KAAK,EAAE,OAAO;IACd+H,SAAS,EAAE,MAAM;IACjBL,GAAG,EAAE,MAAM;IACXS,QAAQ,EAAE;EACZ,CAAC,EACD;IACEnI,KAAK,EAAE,IAAI;IACX+H,SAAS,EAAE,SAAS;IACpBL,GAAG,EAAE,SAAS;IACdM,KAAK,EAAE;EACT,CAAC,EACD;IACEhI,KAAK,EAAE,IAAI;IACX+H,SAAS,EAAE,QAAQ;IACnBL,GAAG,EAAE,QAAQ;IACbM,KAAK,EAAE,EAAE;IACTC,MAAM,EAAG3G,MAAc,iBACrBrF,OAAA,CAAC7C,GAAG;MAACsH,KAAK,EAAEyG,cAAc,CAAC7F,MAAM,CAAE;MAAApB,QAAA,EAChC8D,aAAa,CAAC1C,MAAM;IAAC;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEN,KAAK,EAAE,IAAI;IACX+H,SAAS,EAAE,aAAa;IACxBL,GAAG,EAAE,aAAa;IAClBS,QAAQ,EAAE;EACZ,CAAC,EACD;IACEnI,KAAK,EAAE,KAAK;IACZ+H,SAAS,EAAE,WAAW;IACtBL,GAAG,EAAE,WAAW;IAChBM,KAAK,EAAE;EACT,CAAC,EACD;IACEhI,KAAK,EAAE,MAAM;IACb+H,SAAS,EAAE,WAAW;IACtBL,GAAG,EAAE,WAAW;IAChBM,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGG,IAAY,IAAKxM,UAAU,CAACwM,IAAI;EAC3C,CAAC,EACD;IACEpI,KAAK,EAAE,MAAM;IACb+H,SAAS,EAAE,WAAW;IACtBL,GAAG,EAAE,WAAW;IAChBM,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGG,IAAY,IAAKxM,UAAU,CAACwM,IAAI;EAC3C,CAAC,EACD;IACEpI,KAAK,EAAE,IAAI;IACX0H,GAAG,EAAE,QAAQ;IACbM,KAAK,EAAE,GAAG;IACVK,KAAK,EAAE,OAAgB;IACvBJ,MAAM,EAAEA,CAACK,CAAM,EAAE9I,MAAe,kBAC9BvD,OAAA,CAACjD,KAAK;MAACuP,IAAI,EAAC,OAAO;MAAArI,QAAA,gBACjBjE,OAAA,CAACtC,OAAO;QAACqG,KAAK,EAAC,cAAI;QAAAE,QAAA,eACjBjE,OAAA,CAAClD,MAAM;UACL6H,IAAI,EAAC,MAAM;UACX+G,IAAI,eAAE1L,OAAA,CAACzB,WAAW;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBsH,OAAO,EAAEA,CAAA,KAAMhI,UAAU,CAACJ,MAAM;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVrE,OAAA,CAACtC,OAAO;QAACqG,KAAK,EAAC,cAAI;QAAAE,QAAA,eACjBjE,OAAA,CAAClD,MAAM;UACL6H,IAAI,EAAC,MAAM;UACX+G,IAAI,eAAE1L,OAAA,CAAC3B,YAAY;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBsH,OAAO,EAAEA,CAAA,KAAMrI,UAAU,CAACC,MAAM,CAAE;UAClCqI,QAAQ,EAAErI,MAAM,CAAC8B,MAAM,KAAK3F,UAAU,CAAC0L;QAAO;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVrE,OAAA,CAACtC,OAAO;QAACqG,KAAK,EAAC,cAAI;QAAAE,QAAA,eACjBjE,OAAA,CAAClD,MAAM;UACL6H,IAAI,EAAC,MAAM;UACX4H,MAAM;UACNb,IAAI,eAAE1L,OAAA,CAAC1B,cAAc;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBsH,OAAO,EAAEA,CAAA,KAAM9H,YAAY,CAACN,MAAM,CAAE;UACpCqI,QAAQ,EAAErI,MAAM,CAAC8B,MAAM,KAAK3F,UAAU,CAACyL;QAAM;UAAAjH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVrE,OAAA,CAACrC,QAAQ;QACP6O,IAAI,EAAE;UAAEC,KAAK,EAAEjB,kBAAkB,CAACjI,MAAM;QAAE,CAAE;QAC5CmJ,OAAO,EAAE,CAAC,OAAO,CAAE;QAAAzI,QAAA,eAEnBjE,OAAA,CAAClD,MAAM;UAAC6H,IAAI,EAAC,MAAM;UAAC+G,IAAI,eAAE1L,OAAA,CAACrB,YAAY;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAEX,CAAC,CACF;;EAED;EACA,MAAMgD,QAAmB,GAAG,CAC1B;IACE3D,EAAE,EAAE,GAAG;IACPa,IAAI,EAAE,WAAW;IACjBD,IAAI,EAAE,YAAY;IAClBwD,OAAO,EAAE,MAAM;IACfE,WAAW,EAAE,gBAAgB;IAC7B3C,MAAM,EAAE,QAAQ;IAChBoH,KAAK,EAAE,EAAE;IACTE,WAAW,EAAE,EAAE;IACf1E,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEzE,EAAE,EAAE,GAAG;IACPa,IAAI,EAAE,WAAW;IACjBD,IAAI,EAAE,YAAY;IAClBwD,OAAO,EAAE,MAAM;IACfE,WAAW,EAAE,cAAc;IAC3B3C,MAAM,EAAE,QAAQ;IAChBoH,KAAK,EAAE,EAAE;IACTE,WAAW,EAAE,EAAE;IACf1E,SAAS,EAAE,aAAa;IACxBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE,sBAAsB;IACjCyE,QAAQ,EAAE,sBAAsB;IAChCC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEnJ,EAAE,EAAE,GAAG;IACPa,IAAI,EAAE,WAAW;IACjBD,IAAI,EAAE,cAAc;IACpBwD,OAAO,EAAE,MAAM;IACfE,WAAW,EAAE,aAAa;IAC1B3C,MAAM,EAAE,OAAO;IACfoH,KAAK,EAAE,EAAE;IACTE,WAAW,EAAE,EAAE;IACf1E,SAAS,EAAE,aAAa;IACxBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF;EAED,oBACEnI,OAAA;IAAAiE,QAAA,gBACEjE,OAAA,CAAC9C,IAAI;MAAA+G,QAAA,gBACHjE,OAAA,CAACxC,GAAG;QAACsP,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACvI,KAAK,EAAE;UAAEwI,YAAY,EAAE;QAAG,CAAE;QAAA/I,QAAA,gBACtEjE,OAAA,CAACvC,GAAG;UAAAwG,QAAA,eACFjE,OAAA,CAACC,KAAK;YAACgN,KAAK,EAAE,CAAE;YAACzI,KAAK,EAAE;cAAE0I,MAAM,EAAE;YAAE,CAAE;YAAAjJ,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNrE,OAAA,CAACvC,GAAG;UAAAwG,QAAA,eACFjE,OAAA,CAACjD,KAAK;YAAAkH,QAAA,gBACJjE,OAAA,CAAClD,MAAM;cAAC4O,IAAI,eAAE1L,OAAA,CAACnB,cAAc;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACsH,OAAO,EAAE5F,YAAa;cAAA9B,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrE,OAAA,CAAClD,MAAM;cAAC4O,IAAI,eAAE1L,OAAA,CAACpB,cAAc;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACsH,OAAO,EAAE7F,YAAa;cAAA7B,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrE,OAAA,CAAClD,MAAM;cAAC6H,IAAI,EAAC,SAAS;cAAC+G,IAAI,eAAE1L,OAAA,CAAC5B,YAAY;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACsH,OAAO,EAAEvI,YAAa;cAAAa,QAAA,EAAC;YAEtE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrE,OAAA,CAACxC,GAAG;QAAC2P,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAC3I,KAAK,EAAE;UAAEwI,YAAY,EAAE;QAAG,CAAE;QAAA/I,QAAA,gBACjDjE,OAAA,CAACvC,GAAG;UAAC2P,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArJ,QAAA,eACzBjE,OAAA,CAACE,MAAM;YACLqN,WAAW,EAAC,+CAAY;YACxBC,UAAU;YACVC,QAAQ,EAAExK,YAAa;YACvBuB,KAAK,EAAE;cAAEuH,KAAK,EAAE;YAAO;UAAE;YAAA7H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrE,OAAA,CAACvC,GAAG;UAAC2P,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArJ,QAAA,eACzBjE,OAAA,CAAC/C,MAAM;YACLsQ,WAAW,EAAC,0BAAM;YAClBC,UAAU;YACVhJ,KAAK,EAAE;cAAEuH,KAAK,EAAE;YAAO,CAAE;YACzBzB,QAAQ,EAAEnH,kBAAmB;YAC7BuK,OAAO,EAAE,CACP;cAAEnC,KAAK,EAAE,IAAI;cAAErI,KAAK,EAAExD,UAAU,CAACyL;YAAM,CAAC,EACxC;cAAEI,KAAK,EAAE,IAAI;cAAErI,KAAK,EAAExD,UAAU,CAAC4F;YAAO,CAAC,EACzC;cAAEiG,KAAK,EAAE,IAAI;cAAErI,KAAK,EAAExD,UAAU,CAAC0L;YAAO,CAAC,EACzC;cAAEG,KAAK,EAAE,IAAI;cAAErI,KAAK,EAAExD,UAAU,CAAC2L;YAAS,CAAC;UAC3C;YAAAnH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL7B,eAAe,CAAC8E,MAAM,GAAG,CAAC,iBACzBtH,OAAA;QAAKwE,KAAK,EAAE;UAAEwI,YAAY,EAAE,EAAE;UAAEW,OAAO,EAAE,UAAU;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAA5J,QAAA,gBACjGjE,OAAA;UAAMwE,KAAK,EAAE;YAAEsJ,WAAW,EAAE;UAAG,CAAE;UAAA7J,QAAA,GAAC,qBAAI,EAACzB,eAAe,CAAC8E,MAAM,EAAC,SAAE;QAAA;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvErE,OAAA,CAAClD,MAAM;UAACwP,IAAI,EAAC,OAAO;UAACX,OAAO,EAAEb,oBAAqB;UAACtG,KAAK,EAAE;YAAEsJ,WAAW,EAAE;UAAE,CAAE;UAAA7J,QAAA,EAAC;QAE/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA,CAAClD,MAAM;UAACwP,IAAI,EAAC,OAAO;UAAC3H,IAAI,EAAC,SAAS;UAAC+G,IAAI,eAAE1L,OAAA,CAACjB,gBAAgB;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACsH,OAAO,EAAEZ,oBAAqB;UAAA9G,QAAA,EAAC;QAE/F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eAEDrE,OAAA,CAACnD,KAAK;QACJgP,OAAO,EAAEA,OAAQ;QACjBkC,UAAU,EAAE1G,QAAS;QACrB7G,OAAO,EAAEA,OAAQ;QACjBwN,MAAM,EAAC,IAAI;QACX3D,YAAY,EAAEA,YAAa;QAC3B4D,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBzN,UAAU,EAAE;UACVoC,OAAO,EAAEpC,UAAU,CAACoC,OAAO;UAC3BC,QAAQ,EAAErC,UAAU,CAACqC,QAAQ;UAC7BkH,KAAK,EAAEvJ,UAAU,CAACuJ,KAAK;UACvBmE,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACrE,KAAK,EAAEsE,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQtE,KAAK;QAC1C;MAAE;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPrE,OAAA,CAAC3C,KAAK;MACJ0G,KAAK,EAAC,iBAAO;MACbwK,IAAI,EAAEvN,gBAAiB;MACvBwN,IAAI,EAAE9I,iBAAkB;MACxB+I,QAAQ,EAAEA,CAAA,KAAMxN,mBAAmB,CAAC,KAAK,CAAE;MAC3CyN,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAA1K,QAAA,eAEfjE,OAAA,CAAC1C,IAAI;QAACsR,IAAI,EAAExN,QAAS;QAACyN,MAAM,EAAC,UAAU;QAAA5K,QAAA,gBACrCjE,OAAA,CAAC1C,IAAI,CAACwR,IAAI;UACRvK,IAAI,EAAC,MAAM;UACXgH,KAAK,EAAC,iBAAO;UACbwD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5R,OAAO,EAAE;UAAW,CAAC,CAAE;UAAA6G,QAAA,eAEjDjE,OAAA,CAAChD,KAAK;YAACuQ,WAAW,EAAC;UAAU;YAAArJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACZrE,OAAA,CAAC1C,IAAI,CAACwR,IAAI;UACRvK,IAAI,EAAC,MAAM;UACXgH,KAAK,EAAC,iBAAO;UACbwD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5R,OAAO,EAAE;UAAW,CAAC,CAAE;UAAA6G,QAAA,eAEjDjE,OAAA,CAAChD,KAAK;YAACuQ,WAAW,EAAC;UAAU;YAAArJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRrE,OAAA,CAAC3C,KAAK;MACF0G,KAAK,EAAE,UAAUvC,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAE+C,IAAI,EAAG;MAC9CgK,IAAI,EAAEjN,qBAAsB;MAC5BmN,QAAQ,EAAEA,CAAA,KAAMlN,wBAAwB,CAAC,KAAK,CAAE;MAChD0N,MAAM,EAAE,cACNjP,OAAA,CAAClD,MAAM;QAAa6O,OAAO,EAAEA,CAAA,KAAMpK,wBAAwB,CAAC,KAAK,CAAE;QAAA0C,QAAA,EAAC;MAEpE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACF0H,KAAK,EAAE,GAAI;MAAA9H,QAAA,EAEVzC,oBAAoB,iBACnBxB,OAAA;QAAAiE,QAAA,gBACEjE,OAAA,CAAC/B,YAAY;UACX8F,KAAK,EAAC,6BAAS;UACfmL,QAAQ;UACR5C,IAAI,EAAC,OAAO;UACZ9H,KAAK,EAAE;YAAEwI,YAAY,EAAE;UAAG,CAAE;UAAA/I,QAAA,gBAE5BjE,OAAA,CAAC/B,YAAY,CAAC6Q,IAAI;YAACvD,KAAK,EAAC,iBAAO;YAAAtH,QAAA,EAAEzC,oBAAoB,CAAC8C;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAChFrE,OAAA,CAAC/B,YAAY,CAAC6Q,IAAI;YAACvD,KAAK,EAAC,iBAAO;YAAAtH,QAAA,EAAEzC,oBAAoB,CAAC+C;UAAI;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAChFrE,OAAA,CAAC/B,YAAY,CAAC6Q,IAAI;YAACvD,KAAK,EAAC,0BAAM;YAAAtH,QAAA,EAAEzC,oBAAoB,CAACsG;UAAO;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAClFrE,OAAA,CAAC/B,YAAY,CAAC6Q,IAAI;YAACvD,KAAK,EAAC,cAAI;YAAC4D,IAAI,EAAE,CAAE;YAAAlL,QAAA,eACpCjE,OAAA,CAAC7C,GAAG;cAACsH,KAAK,EAAEyG,cAAc,CAAC1J,oBAAoB,CAAC6D,MAAM,CAAE;cAAApB,QAAA,EACrD8D,aAAa,CAACvG,oBAAoB,CAAC6D,MAAM;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEfrE,OAAA,CAACjC,OAAO;UAAAkG,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAEvBrE,OAAA,CAAChC,QAAQ;UAAAiG,QAAA,EACN+G,iBAAiB,CAACxJ,oBAAoB,CAAC,CAAC+E,GAAG,CAAC,CAACuB,OAAO,EAAE+B,KAAK,kBAC1D7J,OAAA,CAAChC,QAAQ,CAAC8Q,IAAI;YAEZM,GAAG,eAAEpP,OAAA,CAACb,mBAAmB;cAACqF,KAAK,EAAE;gBAAE6K,QAAQ,EAAE;cAAO;YAAE;cAAAnL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1DI,KAAK,EAAEqD,OAAO,CAACzC,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAGyC,OAAO,CAACzC,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAO;YAAApB,QAAA,eAE7FjE,OAAA;cAAKwE,KAAK,EAAE;gBAAEwI,YAAY,EAAE;cAAG,CAAE;cAAA/I,QAAA,gBAC/BjE,OAAA;gBAAKwE,KAAK,EAAE;kBAAE8K,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,QAAQ;kBAAExC,YAAY,EAAE;gBAAE,CAAE;gBAAA/I,QAAA,gBACtGjE,OAAA;kBAAMwE,KAAK,EAAE;oBAAE6K,QAAQ,EAAE,EAAE;oBAAEI,UAAU,EAAE;kBAAO,CAAE;kBAAAxL,QAAA,EAAE6D,OAAO,CAACA;gBAAO;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3ErE,OAAA,CAAC7C,GAAG;kBAACsH,KAAK,EAAEyG,cAAc,CAACpD,OAAO,CAACzC,MAAM,CAAE;kBAAApB,QAAA,EACxC8D,aAAa,CAACD,OAAO,CAACzC,MAAM;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrE,OAAA;gBAAKwE,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEuI,YAAY,EAAE;gBAAE,CAAE;gBAAA/I,QAAA,GAC5C6D,OAAO,CAACG,SAAS,EAAC,QAAG,EAACH,OAAO,CAACI,SAAS;cAAA;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACNrE,OAAA;gBAAKwE,KAAK,EAAE;kBAAEwI,YAAY,EAAE;gBAAE,CAAE;gBAAA/I,QAAA,EAAE6D,OAAO,CAACE;cAAW;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC3DyD,OAAO,CAACmD,OAAO,IAAInD,OAAO,CAACmD,OAAO,CAAC3D,MAAM,GAAG,CAAC,iBAC5CtH,OAAA;gBAAAiE,QAAA,gBACEjE,OAAA;kBAAKwE,KAAK,EAAE;oBAAEiL,UAAU,EAAE,MAAM;oBAAEzC,YAAY,EAAE;kBAAE,CAAE;kBAAA/I,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChErE,OAAA;kBAAIwE,KAAK,EAAE;oBAAE0I,MAAM,EAAE,CAAC;oBAAEwC,WAAW,EAAE;kBAAG,CAAE;kBAAAzL,QAAA,EACvC6D,OAAO,CAACmD,OAAO,CAAC1E,GAAG,CAAC,CAACoJ,MAAM,EAAEC,WAAW,kBACvC5P,OAAA;oBAAAiE,QAAA,EAAuB0L;kBAAM,GAApBC,WAAW;oBAAA1L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAc,CACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GAzBDwF,KAAK;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BG,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGRrE,OAAA,CAAC3C,KAAK;MACF0G,KAAK,EAAC,6BAAS;MACfwK,IAAI,EAAE7M,kBAAmB;MACzB8M,IAAI,EAAErH,mBAAoB;MAC1BsH,QAAQ,EAAEA,CAAA,KAAM9M,qBAAqB,CAAC,KAAK,CAAE;MAC7CoK,KAAK,EAAE,GAAI;MAAA9H,QAAA,eAEXjE,OAAA,CAAC1C,IAAI;QAACsR,IAAI,EAAElM,UAAW;QAACmM,MAAM,EAAC,UAAU;QAAA5K,QAAA,gBACvCjE,OAAA,CAAClC,KAAK;UACJV,OAAO,EAAC,0BAAM;UACd4K,WAAW,EAAExF,eAAe,CAAC8E,MAAM,GAAG,CAAC,GAAG,WAAW9E,eAAe,CAAC8E,MAAM,SAAS,GAAG,YAAa;UACpG3C,IAAI,EAAC,MAAM;UACXH,KAAK,EAAE;YAAEwI,YAAY,EAAE;UAAG;QAAE;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFrE,OAAA,CAAC1C,IAAI,CAACwR,IAAI;UAACvD,KAAK,EAAC,0BAAM;UAAChH,IAAI,EAAC,QAAQ;UAACsL,YAAY,EAAE/N,YAAa;UAAAmC,QAAA,eAC/DjE,OAAA,CAAC7B,KAAK,CAAC2R,KAAK;YAACxF,QAAQ,EAAGpB,CAAC,IAAKnH,eAAe,CAACmH,CAAC,CAACE,MAAM,CAAClG,KAAK,CAAE;YAAAe,QAAA,gBAC5DjE,OAAA,CAAC7B,KAAK;cAAC+E,KAAK,EAAC,OAAO;cAAAe,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CrE,OAAA,CAAC7B,KAAK;cAAC+E,KAAK,EAAC,KAAK;cAAAe,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEZrE,OAAA,CAAC1C,IAAI,CAACwR,IAAI;UAACvD,KAAK,EAAC,0BAAM;UAAChH,IAAI,EAAC,QAAQ;UAACsL,YAAY,EAAE7N,YAAa;UAAAiC,QAAA,eAC/DjE,OAAA,CAAC9B,QAAQ,CAAC4R,KAAK;YACbpC,OAAO,EAAEpC,kBAAmB;YAC5BpI,KAAK,EAAElB,YAAa;YACpBsI,QAAQ,EAAErI;UAAgB;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGRrE,OAAA,CAAC3C,KAAK;MACF0G,KAAK,EAAC,6BAAS;MACfwK,IAAI,EAAE3M,kBAAmB;MACzB6M,QAAQ,EAAEA,CAAA,KAAM;QACd5M,qBAAqB,CAAC,KAAK,CAAC;QAC5BQ,eAAe,CAAC,IAAI,CAAC;QACrBF,iBAAiB,CAAC,CAAC,CAAC;QACpBI,eAAe,CAAC,IAAI,CAAC;QACrBI,UAAU,CAACoN,WAAW,CAAC,CAAC;MAC1B,CAAE;MACFd,MAAM,EAAE,cACNjP,OAAA,CAAClD,MAAM;QAAgB4O,IAAI,eAAE1L,OAAA,CAACjB,gBAAgB;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACsH,OAAO,EAAE3F,sBAAuB;QAAA/B,QAAA,EAAC;MAEpF,GAFY,UAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CAAC,eACTrE,OAAA,CAAClD,MAAM;QAAc6O,OAAO,EAAEA,CAAA,KAAM;UAClC9J,qBAAqB,CAAC,KAAK,CAAC;UAC5BQ,eAAe,CAAC,IAAI,CAAC;UACrBF,iBAAiB,CAAC,CAAC,CAAC;UACpBI,eAAe,CAAC,IAAI,CAAC;UACrBI,UAAU,CAACoN,WAAW,CAAC,CAAC;QAC1B,CAAE;QAAA9L,QAAA,EAAC;MAEH,GARY,QAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQZ,CAAC,CACT;MACF0H,KAAK,EAAE,GAAI;MAAA9H,QAAA,eAEXjE,OAAA,CAAC1C,IAAI;QAACsR,IAAI,EAAEjM,UAAW;QAACkM,MAAM,EAAC,UAAU;QAAA5K,QAAA,gBACvCjE,OAAA,CAAClC,KAAK;UACJV,OAAO,EAAC,0BAAM;UACd4K,WAAW,eACThI,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAAiE,QAAA,EAAG;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5BrE,OAAA;cAAAiE,QAAA,EAAG;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrBrE,OAAA;cAAAiE,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvBrE,OAAA;cAAAiE,QAAA,EAAG;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CACN;UACDM,IAAI,EAAC,MAAM;UACXH,KAAK,EAAE;YAAEwI,YAAY,EAAE;UAAG;QAAE;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFrE,OAAA,CAAC1C,IAAI,CAACwR,IAAI;UAACvD,KAAK,EAAC,0BAAM;UAAAtH,QAAA,eACrBjE,OAAA,CAACpC,MAAM,CAACoS,OAAO;YACbC,MAAM,EAAC,iBAAiB;YACxBC,YAAY,EAAE3H,gBAAiB;YAC/B4H,cAAc,EAAE,KAAM;YACtBvE,QAAQ,EAAExJ,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,YAAa;YAAA6B,QAAA,gBAExEjE,OAAA;cAAGoQ,SAAS,EAAC,sBAAsB;cAAAnM,QAAA,eACjCjE,OAAA,CAAChB,iBAAiB;gBAACwF,KAAK,EAAE;kBAAE6K,QAAQ,EAAE,EAAE;kBAAE5K,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACJrE,OAAA;cAAGoQ,SAAS,EAAC,iBAAiB;cAAAnM,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChDrE,OAAA;cAAGoQ,SAAS,EAAC,iBAAiB;cAAAnM,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEXjC,YAAY,iBACXpC,OAAA;UAAKwE,KAAK,EAAE;YAAEE,SAAS,EAAE;UAAG,CAAE;UAAAT,QAAA,GAC3B7B,YAAY,KAAK,WAAW,iBAC3BpC,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAKwE,KAAK,EAAE;gBAAEwI,YAAY,EAAE;cAAE,CAAE;cAAA/I,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChDrE,OAAA,CAACnC,QAAQ;cAACwS,OAAO,EAAEnO,cAAe;cAACmD,MAAM,EAAC;YAAQ;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACN,EAEAjC,YAAY,KAAK,YAAY,iBAC5BpC,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAKwE,KAAK,EAAE;gBAAEwI,YAAY,EAAE;cAAE,CAAE;cAAA/I,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChDrE,OAAA,CAACnC,QAAQ;cAACwS,OAAO,EAAE,GAAI;cAAChL,MAAM,EAAC;YAAQ;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN,EAEAjC,YAAY,KAAK,SAAS,IAAIE,YAAY,iBACzCtC,OAAA,CAAClC,KAAK;YACJV,OAAO,EAAC,0BAAM;YACd4K,WAAW,EAAE,QAAQ1F,YAAY,CAACwC,OAAO,MAAO;YAChDH,IAAI,EAAC,SAAS;YACd+G,IAAI,eAAE1L,OAAA,CAACf,mBAAmB;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CACF,EAEAjC,YAAY,KAAK,OAAO,IAAIE,YAAY,iBACvCtC,OAAA;YAAAiE,QAAA,gBACEjE,OAAA,CAAClC,KAAK;cACJV,OAAO,EAAC,0EAAc;cACtB4K,WAAW,EAAE,MAAM1F,YAAY,CAACwC,OAAO,SAASxC,YAAY,CAACmH,MAAM,IAAK;cACxE9E,IAAI,EAAC,SAAS;cACd+G,IAAI,eAAE1L,OAAA,CAACd,yBAAyB;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACpCG,KAAK,EAAE;gBAAEwI,YAAY,EAAE;cAAG;YAAE;cAAA9I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,EAED/B,YAAY,CAAC4H,aAAa,IAAI5H,YAAY,CAAC4H,aAAa,CAAC5C,MAAM,GAAG,CAAC,iBAClEtH,OAAA;cAAAiE,QAAA,gBACEjE,OAAA;gBAAKwE,KAAK,EAAE;kBAAEiL,UAAU,EAAE,MAAM;kBAAEzC,YAAY,EAAE;gBAAE,CAAE;gBAAA/I,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChErE,OAAA;gBAAKwE,KAAK,EAAE;kBAAE8L,SAAS,EAAE,GAAG;kBAAEC,QAAQ,EAAE,MAAM;kBAAE3C,eAAe,EAAE,SAAS;kBAAED,OAAO,EAAE,CAAC;kBAAEE,YAAY,EAAE;gBAAE,CAAE;gBAAA5J,QAAA,EACvG3B,YAAY,CAAC4H,aAAa,CAAC3D,GAAG,CAAC,CAACxB,KAAa,EAAE8E,KAAa,kBAC3D7J,OAAA;kBAAiBwE,KAAK,EAAE;oBAAEC,KAAK,EAAE,SAAS;oBAAEuI,YAAY,EAAE;kBAAE,CAAE;kBAAA/I,QAAA,EAC3Dc;gBAAK,GADE8E,KAAK;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjE,EAAA,CAx5BID,eAAyB;EAAA,QACZvD,WAAW,EACXwC,cAAc,EACWC,cAAc,EAMrC/B,IAAI,CAAC+D,OAAO,EAeV/D,IAAI,CAAC+D,OAAO,EACZ/D,IAAI,CAAC+D,OAAO;AAAA;AAAAmP,EAAA,GAzB7BrQ,eAAyB;AA05B/B,eAAAsQ,GAAA,gBAAejU,KAAK,CAACkU,IAAI,CAACvQ,eAAe,CAAC;AAAC,IAAAqQ,EAAA,EAAAC,GAAA;AAAAE,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}