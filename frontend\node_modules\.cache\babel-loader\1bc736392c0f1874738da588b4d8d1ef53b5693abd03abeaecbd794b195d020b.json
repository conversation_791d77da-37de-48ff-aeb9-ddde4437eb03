{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\purchase\\\\PurchaseOptimizationPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Button, Table, Space, Tag, Modal, Form, InputNumber, Select, DatePicker, Checkbox, Divider, Typography, Statistic, Progress, Tabs, Alert, message } from 'antd';\nimport { SettingOutlined, ShoppingCartOutlined, ExportOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { formatCurrency } from '../../utils/format';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  RangePicker\n} = DatePicker;\nconst PurchaseOptimizationPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [optimizationModalVisible, setOptimizationModalVisible] = useState(false);\n  const [optimizationForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('overview');\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = () => {\n    // TODO: 加载优化数据\n  };\n  const handleOptimize = async () => {\n    try {\n      const values = await optimizationForm.validateFields();\n      setLoading(true);\n\n      // TODO: 调用优化API\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      setOptimizationModalVisible(false);\n      message.success('采购优化完成');\n    } catch (error) {\n      message.error('采购优化失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 模拟优化结果数据\n  const mockOptimizationData = {\n    overview: {\n      totalItems: 45,\n      optimizedItems: 38,\n      totalSavings: 125000,\n      wasteReduction: 18.5,\n      mergedOrders: 12,\n      optimizationRate: 84.4\n    },\n    results: [{\n      id: '1',\n      materialCode: 'ANT-MAIN-001',\n      materialName: '5G主天线单元',\n      originalQuantity: 75,\n      optimizedQuantity: 80,\n      packageSize: 10,\n      moq: 50,\n      unitPrice: 1500,\n      originalCost: 112500,\n      optimizedCost: 120000,\n      wasteAmount: 7500,\n      wasteType: '包装浪费',\n      supplier: '华为技术',\n      leadTime: 14,\n      urgency: 'HIGH'\n    }, {\n      id: '2',\n      materialCode: 'RF-AMP-001',\n      materialName: 'RF功率放大器',\n      originalQuantity: 35,\n      optimizedQuantity: 50,\n      packageSize: 5,\n      moq: 50,\n      unitPrice: 2500,\n      originalCost: 87500,\n      optimizedCost: 125000,\n      wasteAmount: 37500,\n      wasteType: 'MOQ浪费',\n      supplier: '中兴通讯',\n      leadTime: 21,\n      urgency: 'HIGH'\n    }, {\n      id: '3',\n      materialCode: 'CABLE-001',\n      materialName: '同轴电缆',\n      originalQuantity: 280,\n      optimizedQuantity: 300,\n      packageSize: 100,\n      moq: 200,\n      unitPrice: 25,\n      originalCost: 7000,\n      optimizedCost: 7500,\n      wasteAmount: 500,\n      wasteType: '包装浪费',\n      supplier: '电缆供应商',\n      leadTime: 7,\n      urgency: 'MEDIUM'\n    }],\n    suggestions: [{\n      id: '1',\n      supplier: '华为技术',\n      totalValue: 245000,\n      itemCount: 8,\n      wasteAmount: 12500,\n      wastePercentage: 5.1,\n      urgency: 'HIGH',\n      leadTime: 14,\n      items: []\n    }, {\n      id: '2',\n      supplier: '中兴通讯',\n      totalValue: 187500,\n      itemCount: 5,\n      wasteAmount: 37500,\n      wastePercentage: 20.0,\n      urgency: 'HIGH',\n      leadTime: 21,\n      items: []\n    }, {\n      id: '3',\n      supplier: '电缆供应商',\n      totalValue: 45000,\n      itemCount: 12,\n      wasteAmount: 2500,\n      wastePercentage: 5.6,\n      urgency: 'MEDIUM',\n      leadTime: 7,\n      items: []\n    }]\n  };\n  const optimizationColumns = [{\n    title: '物料编码',\n    dataIndex: 'materialCode',\n    key: 'materialCode',\n    width: 120,\n    fixed: 'left'\n  }, {\n    title: '物料名称',\n    dataIndex: 'materialName',\n    key: 'materialName',\n    width: 200\n  }, {\n    title: '原始需求',\n    dataIndex: 'originalQuantity',\n    key: 'originalQuantity',\n    width: 100,\n    render: value => value.toLocaleString()\n  }, {\n    title: '优化数量',\n    dataIndex: 'optimizedQuantity',\n    key: 'optimizedQuantity',\n    width: 100,\n    render: (value, record) => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: value > record.originalQuantity ? '#faad14' : '#52c41a'\n      },\n      children: value.toLocaleString()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '包装规格',\n    dataIndex: 'packageSize',\n    key: 'packageSize',\n    width: 100,\n    render: value => `${value}/包`\n  }, {\n    title: 'MOQ',\n    dataIndex: 'moq',\n    key: 'moq',\n    width: 80,\n    render: value => value.toLocaleString()\n  }, {\n    title: '单价',\n    dataIndex: 'unitPrice',\n    key: 'unitPrice',\n    width: 100,\n    render: value => formatCurrency(value)\n  }, {\n    title: '原始成本',\n    dataIndex: 'originalCost',\n    key: 'originalCost',\n    width: 120,\n    render: value => formatCurrency(value)\n  }, {\n    title: '优化成本',\n    dataIndex: 'optimizedCost',\n    key: 'optimizedCost',\n    width: 120,\n    render: (value, record) => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: value > record.originalCost ? '#ff4d4f' : '#52c41a'\n      },\n      children: formatCurrency(value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '浪费金额',\n    dataIndex: 'wasteAmount',\n    key: 'wasteAmount',\n    width: 120,\n    render: value => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: '#ff4d4f'\n      },\n      children: formatCurrency(value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '浪费类型',\n    dataIndex: 'wasteType',\n    key: 'wasteType',\n    width: 100,\n    render: type => {\n      const color = type === '包装浪费' ? 'orange' : type === 'MOQ浪费' ? 'red' : 'blue';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: type\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '供应商',\n    dataIndex: 'supplier',\n    key: 'supplier',\n    width: 120\n  }, {\n    title: '交期',\n    dataIndex: 'leadTime',\n    key: 'leadTime',\n    width: 80,\n    render: days => `${days}天`\n  }, {\n    title: '紧急度',\n    dataIndex: 'urgency',\n    key: 'urgency',\n    width: 80,\n    render: urgency => {\n      const color = urgency === 'HIGH' ? 'red' : urgency === 'MEDIUM' ? 'orange' : 'green';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: urgency\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 16\n      }, this);\n    }\n  }];\n  const suggestionColumns = [{\n    title: '供应商',\n    dataIndex: 'supplier',\n    key: 'supplier',\n    width: 150\n  }, {\n    title: '采购金额',\n    dataIndex: 'totalValue',\n    key: 'totalValue',\n    width: 120,\n    render: value => formatCurrency(value)\n  }, {\n    title: '物料数量',\n    dataIndex: 'itemCount',\n    key: 'itemCount',\n    width: 80,\n    render: count => `${count}项`\n  }, {\n    title: '浪费金额',\n    dataIndex: 'wasteAmount',\n    key: 'wasteAmount',\n    width: 120,\n    render: value => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: '#ff4d4f'\n      },\n      children: formatCurrency(value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '浪费率',\n    dataIndex: 'wastePercentage',\n    key: 'wastePercentage',\n    width: 100,\n    render: percentage => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: percentage > 10 ? '#ff4d4f' : '#faad14'\n      },\n      children: [percentage.toFixed(1), \"%\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '紧急度',\n    dataIndex: 'urgency',\n    key: 'urgency',\n    width: 80,\n    render: urgency => {\n      const color = urgency === 'HIGH' ? 'red' : urgency === 'MEDIUM' ? 'orange' : 'green';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: urgency\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '交期',\n    dataIndex: 'leadTime',\n    key: 'leadTime',\n    width: 80,\n    render: days => `${days}天`\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 120,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(ShoppingCartOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 19\n        }, this),\n        onClick: () => {\n          Modal.confirm({\n            title: '生成采购订单',\n            content: `确定为供应商 ${record.supplier} 生成采购订单吗？`,\n            onOk: () => {\n              message.success('采购订单已生成');\n            }\n          });\n        },\n        children: \"\\u751F\\u6210\\u8BA2\\u5355\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 9\n    }, this)\n  }];\n  const renderOverviewTab = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u7269\\u6599\\u9879\",\n            value: mockOptimizationData.overview.totalItems,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u4F18\\u5316\\u9879\",\n            value: mockOptimizationData.overview.optimizedItems,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8282\\u7701\\u91D1\\u989D\",\n            value: mockOptimizationData.overview.totalSavings,\n            prefix: \"\\xA5\",\n            valueStyle: {\n              color: '#52c41a'\n            },\n            formatter: value => formatCurrency(Number(value))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D6A\\u8D39\\u51CF\\u5C11\",\n            value: mockOptimizationData.overview.wasteReduction,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4F18\\u5316\\u8FDB\\u5EA6\",\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4F18\\u5316\\u5B8C\\u6210\\u7387\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: mockOptimizationData.overview.optimizationRate,\n              status: \"active\",\n              strokeColor: \"#52c41a\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8BA2\\u5355\\u5408\\u5E76\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: 24,\n                fontWeight: 'bold',\n                color: '#1890ff'\n              },\n              children: mockOptimizationData.overview.mergedOrders\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4F18\\u5316\\u5EFA\\u8BAE\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u4F18\\u5316\\u5EFA\\u8BAE\",\n        description: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5EFA\\u8BAE\\u5408\\u5E76\\u534E\\u4E3A\\u6280\\u672F\\u548C\\u4E2D\\u5174\\u901A\\u8BAF\\u7684\\u8BA2\\u5355\\uFF0C\\u53EF\\u51CF\\u5C1115%\\u7684\\u91C7\\u8D2D\\u6210\\u672C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"CABLE-001\\u7684\\u5305\\u88C5\\u89C4\\u683C\\u53EF\\u4EE5\\u8C03\\u6574\\uFF0C\\u51CF\\u5C11\\u5305\\u88C5\\u6D6A\\u8D39\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"RF-AMP-001\\u7684MOQ\\u8F83\\u9AD8\\uFF0C\\u5EFA\\u8BAE\\u5BFB\\u627E\\u66FF\\u4EE3\\u4F9B\\u5E94\\u5546\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u5EFA\\u8BAE\\u8BBE\\u7F6E\\u7D27\\u6025\\u8BA2\\u5355\\u767D\\u540D\\u5355\\uFF0C\\u4F18\\u5148\\u4FDD\\u8BC1\\u5173\\u952E\\u4EA4\\u4ED8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this),\n        type: \"info\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 407,\n    columnNumber: 5\n  }, this);\n  const renderOptimizationTab = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4F18\\u5316\\u7ED3\\u679C\\u8BE6\\u60C5\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: optimizationColumns,\n        dataSource: mockOptimizationData.results,\n        rowKey: \"id\",\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 495,\n    columnNumber: 5\n  }, this);\n  const renderSuggestionTab = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u91C7\\u8D2D\\u5EFA\\u8BAE\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: suggestionColumns,\n        dataSource: mockOptimizationData.suggestions,\n        rowKey: \"id\",\n        scroll: {\n          x: 1000\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 514,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u91C7\\u8D2D\\u4F18\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u5305\\u88C5\\u4F18\\u5316\\u3001MOQ\\u5904\\u7406\\u548C\\u91C7\\u8D2D\\u5EFA\\u8BAE\\u751F\\u6210\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 29\n              }, this),\n              onClick: loadData,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 23\n              }, this),\n              onClick: () => setOptimizationModalVisible(true),\n              children: \"\\u5F00\\u59CB\\u4F18\\u5316\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u4F18\\u5316\\u6982\\u89C8\",\n          children: renderOverviewTab()\n        }, \"overview\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u4F18\\u5316\\u7ED3\\u679C\",\n          children: renderOptimizationTab()\n        }, \"optimization\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u91C7\\u8D2D\\u5EFA\\u8BAE\",\n          children: renderSuggestionTab()\n        }, \"suggestion\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u91C7\\u8D2D\\u4F18\\u5316\\u8BBE\\u7F6E\",\n      open: optimizationModalVisible,\n      onOk: handleOptimize,\n      onCancel: () => setOptimizationModalVisible(false),\n      width: 600,\n      okText: \"\\u5F00\\u59CB\\u4F18\\u5316\",\n      cancelText: \"\\u53D6\\u6D88\",\n      confirmLoading: loading,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: optimizationForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"dateRange\",\n          label: \"\\u4F18\\u5316\\u5468\\u671F\",\n          rules: [{\n            required: true,\n            message: '请选择优化周期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            style: {\n              width: '100%'\n            },\n            placeholder: ['开始日期', '结束日期']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u4F18\\u5316\\u7B56\\u7565\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"enablePackageOptimization\",\n          valuePropName: \"checked\",\n          initialValue: true,\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            children: \"\\u542F\\u7528\\u5305\\u88C5\\u4F18\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"enableMOQOptimization\",\n          valuePropName: \"checked\",\n          initialValue: true,\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            children: \"\\u542F\\u7528MOQ\\u4F18\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"enableOrderMerging\",\n          valuePropName: \"checked\",\n          initialValue: true,\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            children: \"\\u542F\\u7528\\u8BA2\\u5355\\u5408\\u5E76\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"enableWasteTracking\",\n          valuePropName: \"checked\",\n          initialValue: true,\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            children: \"\\u542F\\u7528\\u6D6A\\u8D39\\u5F52\\u56E0\\u8DDF\\u8E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u4F18\\u5316\\u53C2\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"wasteThreshold\",\n              label: \"\\u6D6A\\u8D39\\u9608\\u503C(%)\",\n              initialValue: 10,\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                max: 50,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u6D6A\\u8D39\\u9608\\u503C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"mergingWindow\",\n              label: \"\\u5408\\u5E76\\u65F6\\u95F4\\u7A97\\u53E3(\\u5929)\",\n              initialValue: 30,\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 90,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u5408\\u5E76\\u65F6\\u95F4\\u7A97\\u53E3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"priorityLevel\",\n              label: \"\\u4F18\\u5148\\u7EA7\",\n              initialValue: \"MEDIUM\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u9009\\u62E9\\u4F18\\u5148\\u7EA7\",\n                children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"HIGH\",\n                  children: \"\\u9AD8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"MEDIUM\",\n                  children: \"\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"LOW\",\n                  children: \"\\u4F4E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"urgentBypass\",\n              valuePropName: \"checked\",\n              initialValue: false,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                children: \"\\u7D27\\u6025\\u8BA2\\u5355\\u7ED5\\u8FC7\\u4F18\\u5316\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 533,\n    columnNumber: 5\n  }, this);\n};\n_s(PurchaseOptimizationPage, \"EyxG9PZaOYAGhVs1NlFHMTuushU=\", false, function () {\n  return [Form.useForm];\n});\n_c = PurchaseOptimizationPage;\nexport default PurchaseOptimizationPage;\nvar _c;\n$RefreshReg$(_c, \"PurchaseOptimizationPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "InputNumber", "Select", "DatePicker", "Checkbox", "Divider", "Typography", "Statistic", "Progress", "Tabs", "<PERSON><PERSON>", "message", "SettingOutlined", "ShoppingCartOutlined", "ExportOutlined", "ReloadOutlined", "formatCurrency", "jsxDEV", "_jsxDEV", "Title", "Text", "TabPane", "RangePicker", "PurchaseOptimizationPage", "_s", "loading", "setLoading", "optimizationModalVisible", "setOptimizationModalVisible", "optimizationForm", "useForm", "activeTab", "setActiveTab", "loadData", "handleOptimize", "values", "validateFields", "Promise", "resolve", "setTimeout", "success", "error", "mockOptimizationData", "overview", "totalItems", "optimizedItems", "totalSavings", "wasteReduction", "mergedOrders", "optimizationRate", "results", "id", "materialCode", "materialName", "originalQuantity", "optimizedQuantity", "packageSize", "moq", "unitPrice", "originalCost", "optimizedCost", "wasteAmount", "wasteType", "supplier", "leadTime", "urgency", "suggestions", "totalValue", "itemCount", "wastePercentage", "items", "optimizationColumns", "title", "dataIndex", "key", "width", "fixed", "render", "value", "toLocaleString", "record", "style", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "days", "suggestionColumns", "count", "percentage", "toFixed", "_", "size", "icon", "onClick", "confirm", "content", "onOk", "renderOverviewTab", "gutter", "marginBottom", "xs", "sm", "valueStyle", "prefix", "formatter", "Number", "suffix", "md", "strong", "percent", "status", "strokeColor", "fontSize", "fontWeight", "description", "showIcon", "renderOptimizationTab", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "renderSuggestionTab", "justify", "align", "level", "margin", "active<PERSON><PERSON>", "onChange", "tab", "open", "onCancel", "okText", "cancelText", "confirmLoading", "form", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "orientation", "valuePropName", "initialValue", "span", "min", "max", "Option", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/purchase/PurchaseOptimizationPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  InputNumber,\n  Select,\n  DatePicker,\n  Checkbox,\n  Divider,\n  Typography,\n  Statistic,\n  Progress,\n  Tabs,\n  Alert,\n  message,\n} from 'antd';\nimport {\n  SettingOutlined,\n  CalculatorOutlined,\n  ShoppingCartOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport { formatCurrency, formatDate } from '../../utils/format';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { RangePicker } = DatePicker;\n\ninterface OptimizationResult {\n  id: string;\n  materialCode: string;\n  materialName: string;\n  originalQuantity: number;\n  optimizedQuantity: number;\n  packageSize: number;\n  moq: number;\n  unitPrice: number;\n  originalCost: number;\n  optimizedCost: number;\n  wasteAmount: number;\n  wasteType: string;\n  supplier: string;\n  leadTime: number;\n  urgency: string;\n}\n\ninterface PurchaseSuggestion {\n  id: string;\n  supplier: string;\n  totalValue: number;\n  itemCount: number;\n  wasteAmount: number;\n  wastePercentage: number;\n  urgency: string;\n  leadTime: number;\n  items: OptimizationResult[];\n}\n\nconst PurchaseOptimizationPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [optimizationModalVisible, setOptimizationModalVisible] = useState(false);\n  const [optimizationForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('overview');\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = () => {\n    // TODO: 加载优化数据\n  };\n\n  const handleOptimize = async () => {\n    try {\n      const values = await optimizationForm.validateFields();\n      setLoading(true);\n      \n      // TODO: 调用优化API\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      setOptimizationModalVisible(false);\n      message.success('采购优化完成');\n    } catch (error) {\n      message.error('采购优化失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 模拟优化结果数据\n  const mockOptimizationData = {\n    overview: {\n      totalItems: 45,\n      optimizedItems: 38,\n      totalSavings: 125000,\n      wasteReduction: 18.5,\n      mergedOrders: 12,\n      optimizationRate: 84.4,\n    },\n    results: [\n      {\n        id: '1',\n        materialCode: 'ANT-MAIN-001',\n        materialName: '5G主天线单元',\n        originalQuantity: 75,\n        optimizedQuantity: 80,\n        packageSize: 10,\n        moq: 50,\n        unitPrice: 1500,\n        originalCost: 112500,\n        optimizedCost: 120000,\n        wasteAmount: 7500,\n        wasteType: '包装浪费',\n        supplier: '华为技术',\n        leadTime: 14,\n        urgency: 'HIGH',\n      },\n      {\n        id: '2',\n        materialCode: 'RF-AMP-001',\n        materialName: 'RF功率放大器',\n        originalQuantity: 35,\n        optimizedQuantity: 50,\n        packageSize: 5,\n        moq: 50,\n        unitPrice: 2500,\n        originalCost: 87500,\n        optimizedCost: 125000,\n        wasteAmount: 37500,\n        wasteType: 'MOQ浪费',\n        supplier: '中兴通讯',\n        leadTime: 21,\n        urgency: 'HIGH',\n      },\n      {\n        id: '3',\n        materialCode: 'CABLE-001',\n        materialName: '同轴电缆',\n        originalQuantity: 280,\n        optimizedQuantity: 300,\n        packageSize: 100,\n        moq: 200,\n        unitPrice: 25,\n        originalCost: 7000,\n        optimizedCost: 7500,\n        wasteAmount: 500,\n        wasteType: '包装浪费',\n        supplier: '电缆供应商',\n        leadTime: 7,\n        urgency: 'MEDIUM',\n      },\n    ],\n    suggestions: [\n      {\n        id: '1',\n        supplier: '华为技术',\n        totalValue: 245000,\n        itemCount: 8,\n        wasteAmount: 12500,\n        wastePercentage: 5.1,\n        urgency: 'HIGH',\n        leadTime: 14,\n        items: [],\n      },\n      {\n        id: '2',\n        supplier: '中兴通讯',\n        totalValue: 187500,\n        itemCount: 5,\n        wasteAmount: 37500,\n        wastePercentage: 20.0,\n        urgency: 'HIGH',\n        leadTime: 21,\n        items: [],\n      },\n      {\n        id: '3',\n        supplier: '电缆供应商',\n        totalValue: 45000,\n        itemCount: 12,\n        wasteAmount: 2500,\n        wastePercentage: 5.6,\n        urgency: 'MEDIUM',\n        leadTime: 7,\n        items: [],\n      },\n    ],\n  };\n\n  const optimizationColumns = [\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n      fixed: 'left' as const,\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      width: 200,\n    },\n    {\n      title: '原始需求',\n      dataIndex: 'originalQuantity',\n      key: 'originalQuantity',\n      width: 100,\n      render: (value: number) => value.toLocaleString(),\n    },\n    {\n      title: '优化数量',\n      dataIndex: 'optimizedQuantity',\n      key: 'optimizedQuantity',\n      width: 100,\n      render: (value: number, record: OptimizationResult) => (\n        <span style={{ color: value > record.originalQuantity ? '#faad14' : '#52c41a' }}>\n          {value.toLocaleString()}\n        </span>\n      ),\n    },\n    {\n      title: '包装规格',\n      dataIndex: 'packageSize',\n      key: 'packageSize',\n      width: 100,\n      render: (value: number) => `${value}/包`,\n    },\n    {\n      title: 'MOQ',\n      dataIndex: 'moq',\n      key: 'moq',\n      width: 80,\n      render: (value: number) => value.toLocaleString(),\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      width: 100,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '原始成本',\n      dataIndex: 'originalCost',\n      key: 'originalCost',\n      width: 120,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '优化成本',\n      dataIndex: 'optimizedCost',\n      key: 'optimizedCost',\n      width: 120,\n      render: (value: number, record: OptimizationResult) => (\n        <span style={{ color: value > record.originalCost ? '#ff4d4f' : '#52c41a' }}>\n          {formatCurrency(value)}\n        </span>\n      ),\n    },\n    {\n      title: '浪费金额',\n      dataIndex: 'wasteAmount',\n      key: 'wasteAmount',\n      width: 120,\n      render: (value: number) => (\n        <span style={{ color: '#ff4d4f' }}>\n          {formatCurrency(value)}\n        </span>\n      ),\n    },\n    {\n      title: '浪费类型',\n      dataIndex: 'wasteType',\n      key: 'wasteType',\n      width: 100,\n      render: (type: string) => {\n        const color = type === '包装浪费' ? 'orange' : type === 'MOQ浪费' ? 'red' : 'blue';\n        return <Tag color={color}>{type}</Tag>;\n      },\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 120,\n    },\n    {\n      title: '交期',\n      dataIndex: 'leadTime',\n      key: 'leadTime',\n      width: 80,\n      render: (days: number) => `${days}天`,\n    },\n    {\n      title: '紧急度',\n      dataIndex: 'urgency',\n      key: 'urgency',\n      width: 80,\n      render: (urgency: string) => {\n        const color = urgency === 'HIGH' ? 'red' : urgency === 'MEDIUM' ? 'orange' : 'green';\n        return <Tag color={color}>{urgency}</Tag>;\n      },\n    },\n  ];\n\n  const suggestionColumns = [\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 150,\n    },\n    {\n      title: '采购金额',\n      dataIndex: 'totalValue',\n      key: 'totalValue',\n      width: 120,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '物料数量',\n      dataIndex: 'itemCount',\n      key: 'itemCount',\n      width: 80,\n      render: (count: number) => `${count}项`,\n    },\n    {\n      title: '浪费金额',\n      dataIndex: 'wasteAmount',\n      key: 'wasteAmount',\n      width: 120,\n      render: (value: number) => (\n        <span style={{ color: '#ff4d4f' }}>\n          {formatCurrency(value)}\n        </span>\n      ),\n    },\n    {\n      title: '浪费率',\n      dataIndex: 'wastePercentage',\n      key: 'wastePercentage',\n      width: 100,\n      render: (percentage: number) => (\n        <span style={{ color: percentage > 10 ? '#ff4d4f' : '#faad14' }}>\n          {percentage.toFixed(1)}%\n        </span>\n      ),\n    },\n    {\n      title: '紧急度',\n      dataIndex: 'urgency',\n      key: 'urgency',\n      width: 80,\n      render: (urgency: string) => {\n        const color = urgency === 'HIGH' ? 'red' : urgency === 'MEDIUM' ? 'orange' : 'green';\n        return <Tag color={color}>{urgency}</Tag>;\n      },\n    },\n    {\n      title: '交期',\n      dataIndex: 'leadTime',\n      key: 'leadTime',\n      width: 80,\n      render: (days: number) => `${days}天`,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      fixed: 'right' as const,\n      render: (_: any, record: PurchaseSuggestion) => (\n        <Space size=\"small\">\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<ShoppingCartOutlined />}\n            onClick={() => {\n              Modal.confirm({\n                title: '生成采购订单',\n                content: `确定为供应商 ${record.supplier} 生成采购订单吗？`,\n                onOk: () => {\n                  message.success('采购订单已生成');\n                },\n              });\n            }}\n          >\n            生成订单\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  const renderOverviewTab = () => (\n    <div>\n      {/* 优化概览 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总物料项\"\n              value={mockOptimizationData.overview.totalItems}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"已优化项\"\n              value={mockOptimizationData.overview.optimizedItems}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"节省金额\"\n              value={mockOptimizationData.overview.totalSavings}\n              prefix=\"¥\"\n              valueStyle={{ color: '#52c41a' }}\n              formatter={(value) => formatCurrency(Number(value))}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"浪费减少\"\n              value={mockOptimizationData.overview.wasteReduction}\n              suffix=\"%\"\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 优化进度 */}\n      <Card title=\"优化进度\" style={{ marginBottom: 24 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} md={12}>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>优化完成率</Text>\n              <Progress\n                percent={mockOptimizationData.overview.optimizationRate}\n                status=\"active\"\n                strokeColor=\"#52c41a\"\n              />\n            </div>\n          </Col>\n          <Col xs={24} md={12}>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>订单合并数</Text>\n              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>\n                {mockOptimizationData.overview.mergedOrders}\n              </div>\n            </div>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 优化建议 */}\n      <Card title=\"优化建议\">\n        <Alert\n          message=\"优化建议\"\n          description={\n            <ul>\n              <li>建议合并华为技术和中兴通讯的订单，可减少15%的采购成本</li>\n              <li>CABLE-001的包装规格可以调整，减少包装浪费</li>\n              <li>RF-AMP-001的MOQ较高，建议寻找替代供应商</li>\n              <li>建议设置紧急订单白名单，优先保证关键交付</li>\n            </ul>\n          }\n          type=\"info\"\n          showIcon\n        />\n      </Card>\n    </div>\n  );\n\n  const renderOptimizationTab = () => (\n    <div>\n      <Card title=\"优化结果详情\">\n        <Table\n          columns={optimizationColumns}\n          dataSource={mockOptimizationData.results}\n          rowKey=\"id\"\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  const renderSuggestionTab = () => (\n    <div>\n      <Card title=\"采购建议\">\n        <Table\n          columns={suggestionColumns}\n          dataSource={mockOptimizationData.suggestions}\n          rowKey=\"id\"\n          scroll={{ x: 1000 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              采购优化\n            </Title>\n            <Text type=\"secondary\">\n              包装优化、MOQ处理和采购建议生成\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<ReloadOutlined />} onClick={loadData}>\n                刷新\n              </Button>\n              <Button icon={<ExportOutlined />}>\n                导出\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<SettingOutlined />}\n                onClick={() => setOptimizationModalVisible(true)}\n              >\n                开始优化\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab=\"优化概览\" key=\"overview\">\n            {renderOverviewTab()}\n          </TabPane>\n          <TabPane tab=\"优化结果\" key=\"optimization\">\n            {renderOptimizationTab()}\n          </TabPane>\n          <TabPane tab=\"采购建议\" key=\"suggestion\">\n            {renderSuggestionTab()}\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 优化设置模态框 */}\n      <Modal\n        title=\"采购优化设置\"\n        open={optimizationModalVisible}\n        onOk={handleOptimize}\n        onCancel={() => setOptimizationModalVisible(false)}\n        width={600}\n        okText=\"开始优化\"\n        cancelText=\"取消\"\n        confirmLoading={loading}\n      >\n        <Form form={optimizationForm} layout=\"vertical\">\n          <Form.Item\n            name=\"dateRange\"\n            label=\"优化周期\"\n            rules={[{ required: true, message: '请选择优化周期' }]}\n          >\n            <RangePicker\n              style={{ width: '100%' }}\n              placeholder={['开始日期', '结束日期']}\n            />\n          </Form.Item>\n\n          <Divider orientation=\"left\">优化策略</Divider>\n\n          <Form.Item\n            name=\"enablePackageOptimization\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Checkbox>启用包装优化</Checkbox>\n          </Form.Item>\n\n          <Form.Item\n            name=\"enableMOQOptimization\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Checkbox>启用MOQ优化</Checkbox>\n          </Form.Item>\n\n          <Form.Item\n            name=\"enableOrderMerging\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Checkbox>启用订单合并</Checkbox>\n          </Form.Item>\n\n          <Form.Item\n            name=\"enableWasteTracking\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Checkbox>启用浪费归因跟踪</Checkbox>\n          </Form.Item>\n\n          <Divider orientation=\"left\">优化参数</Divider>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"wasteThreshold\"\n                label=\"浪费阈值(%)\"\n                initialValue={10}\n              >\n                <InputNumber\n                  min={0}\n                  max={50}\n                  style={{ width: '100%' }}\n                  placeholder=\"浪费阈值\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"mergingWindow\"\n                label=\"合并时间窗口(天)\"\n                initialValue={30}\n              >\n                <InputNumber\n                  min={1}\n                  max={90}\n                  style={{ width: '100%' }}\n                  placeholder=\"合并时间窗口\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"priorityLevel\"\n                label=\"优先级\"\n                initialValue=\"MEDIUM\"\n              >\n                <Select placeholder=\"选择优先级\">\n                  <Select.Option value=\"HIGH\">高</Select.Option>\n                  <Select.Option value=\"MEDIUM\">中</Select.Option>\n                  <Select.Option value=\"LOW\">低</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"urgentBypass\"\n                valuePropName=\"checked\"\n                initialValue={false}\n              >\n                <Checkbox>紧急订单绕过优化</Checkbox>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default PurchaseOptimizationPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EAEJC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,eAAe,EAEfC,oBAAoB,EAGpBC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAC1B,SAASC,cAAc,QAAoB,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGd,UAAU;AAClC,MAAM;EAAEe;AAAQ,CAAC,GAAGZ,IAAI;AACxB,MAAM;EAAEa;AAAY,CAAC,GAAGnB,UAAU;AAgClC,MAAMoB,wBAAkC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACuC,gBAAgB,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;EACzC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,UAAU,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd0C,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAGA,CAAA,KAAM;IACrB;EAAA,CACD;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMN,gBAAgB,CAACO,cAAc,CAAC,CAAC;MACtDV,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,IAAIW,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDV,2BAA2B,CAAC,KAAK,CAAC;MAClCjB,OAAO,CAAC6B,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd9B,OAAO,CAAC8B,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgB,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,MAAM;MACpBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,OAAO,EAAE,CACP;MACEC,EAAE,EAAE,GAAG;MACPC,YAAY,EAAE,cAAc;MAC5BC,YAAY,EAAE,SAAS;MACvBC,gBAAgB,EAAE,EAAE;MACpBC,iBAAiB,EAAE,EAAE;MACrBC,WAAW,EAAE,EAAE;MACfC,GAAG,EAAE,EAAE;MACPC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,MAAM;MACpBC,aAAa,EAAE,MAAM;MACrBC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;IACX,CAAC,EACD;MACEd,EAAE,EAAE,GAAG;MACPC,YAAY,EAAE,YAAY;MAC1BC,YAAY,EAAE,SAAS;MACvBC,gBAAgB,EAAE,EAAE;MACpBC,iBAAiB,EAAE,EAAE;MACrBC,WAAW,EAAE,CAAC;MACdC,GAAG,EAAE,EAAE;MACPC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,MAAM;MACrBC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;IACX,CAAC,EACD;MACEd,EAAE,EAAE,GAAG;MACPC,YAAY,EAAE,WAAW;MACzBC,YAAY,EAAE,MAAM;MACpBC,gBAAgB,EAAE,GAAG;MACrBC,iBAAiB,EAAE,GAAG;MACtBC,WAAW,EAAE,GAAG;MAChBC,GAAG,EAAE,GAAG;MACRC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC,CACF;IACDC,WAAW,EAAE,CACX;MACEf,EAAE,EAAE,GAAG;MACPY,QAAQ,EAAE,MAAM;MAChBI,UAAU,EAAE,MAAM;MAClBC,SAAS,EAAE,CAAC;MACZP,WAAW,EAAE,KAAK;MAClBQ,eAAe,EAAE,GAAG;MACpBJ,OAAO,EAAE,MAAM;MACfD,QAAQ,EAAE,EAAE;MACZM,KAAK,EAAE;IACT,CAAC,EACD;MACEnB,EAAE,EAAE,GAAG;MACPY,QAAQ,EAAE,MAAM;MAChBI,UAAU,EAAE,MAAM;MAClBC,SAAS,EAAE,CAAC;MACZP,WAAW,EAAE,KAAK;MAClBQ,eAAe,EAAE,IAAI;MACrBJ,OAAO,EAAE,MAAM;MACfD,QAAQ,EAAE,EAAE;MACZM,KAAK,EAAE;IACT,CAAC,EACD;MACEnB,EAAE,EAAE,GAAG;MACPY,QAAQ,EAAE,OAAO;MACjBI,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,EAAE;MACbP,WAAW,EAAE,IAAI;MACjBQ,eAAe,EAAE,GAAG;MACpBJ,OAAO,EAAE,QAAQ;MACjBD,QAAQ,EAAE,CAAC;MACXM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;EAED,MAAMC,mBAAmB,GAAG,CAC1B;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,kBAAkB;IAC7BC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,KAAa,IAAKA,KAAK,CAACC,cAAc,CAAC;EAClD,CAAC,EACD;IACEP,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,mBAAmB;IAC9BC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACC,KAAa,EAAEE,MAA0B,kBAChD9D,OAAA;MAAM+D,KAAK,EAAE;QAAEC,KAAK,EAAEJ,KAAK,GAAGE,MAAM,CAAC1B,gBAAgB,GAAG,SAAS,GAAG;MAAU,CAAE;MAAA6B,QAAA,EAC7EL,KAAK,CAACC,cAAc,CAAC;IAAC;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAEV,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,KAAa,IAAK,GAAGA,KAAK;EACrC,CAAC,EACD;IACEN,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,KAAK;IAChBC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGC,KAAa,IAAKA,KAAK,CAACC,cAAc,CAAC;EAClD,CAAC,EACD;IACEP,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,KAAa,IAAK9D,cAAc,CAAC8D,KAAK;EACjD,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,KAAa,IAAK9D,cAAc,CAAC8D,KAAK;EACjD,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACC,KAAa,EAAEE,MAA0B,kBAChD9D,OAAA;MAAM+D,KAAK,EAAE;QAAEC,KAAK,EAAEJ,KAAK,GAAGE,MAAM,CAACrB,YAAY,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAwB,QAAA,EACzEnE,cAAc,CAAC8D,KAAK;IAAC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAEV,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,KAAa,iBACpB5D,OAAA;MAAM+D,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAC,QAAA,EAC/BnE,cAAc,CAAC8D,KAAK;IAAC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAEV,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGW,IAAY,IAAK;MACxB,MAAMN,KAAK,GAAGM,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAGA,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,MAAM;MAC5E,oBAAOtE,OAAA,CAACpB,GAAG;QAACoF,KAAK,EAAEA,KAAM;QAAAC,QAAA,EAAEK;MAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACxC;EACF,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGY,IAAY,IAAK,GAAGA,IAAI;EACnC,CAAC,EACD;IACEjB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGZ,OAAe,IAAK;MAC3B,MAAMiB,KAAK,GAAGjB,OAAO,KAAK,MAAM,GAAG,KAAK,GAAGA,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;MACpF,oBAAO/C,OAAA,CAACpB,GAAG;QAACoF,KAAK,EAAEA,KAAM;QAAAC,QAAA,EAAElB;MAAO;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC3C;EACF,CAAC,CACF;EAED,MAAMG,iBAAiB,GAAG,CACxB;IACElB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,KAAa,IAAK9D,cAAc,CAAC8D,KAAK;EACjD,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGc,KAAa,IAAK,GAAGA,KAAK;EACrC,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,KAAa,iBACpB5D,OAAA;MAAM+D,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAC,QAAA,EAC/BnE,cAAc,CAAC8D,KAAK;IAAC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAEV,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGe,UAAkB,iBACzB1E,OAAA;MAAM+D,KAAK,EAAE;QAAEC,KAAK,EAAEU,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAT,QAAA,GAC7DS,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GACzB;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEV,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGZ,OAAe,IAAK;MAC3B,MAAMiB,KAAK,GAAGjB,OAAO,KAAK,MAAM,GAAG,KAAK,GAAGA,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;MACpF,oBAAO/C,OAAA,CAACpB,GAAG;QAACoF,KAAK,EAAEA,KAAM;QAAAC,QAAA,EAAElB;MAAO;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC3C;EACF,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGY,IAAY,IAAK,GAAGA,IAAI;EACnC,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAgB;IACvBC,MAAM,EAAEA,CAACiB,CAAM,EAAEd,MAA0B,kBACzC9D,OAAA,CAACrB,KAAK;MAACkG,IAAI,EAAC,OAAO;MAAAZ,QAAA,eACjBjE,OAAA,CAACvB,MAAM;QACL6F,IAAI,EAAC,SAAS;QACdO,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE9E,OAAA,CAACL,oBAAoB;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/BU,OAAO,EAAEA,CAAA,KAAM;UACblG,KAAK,CAACmG,OAAO,CAAC;YACZ1B,KAAK,EAAE,QAAQ;YACf2B,OAAO,EAAE,UAAUnB,MAAM,CAACjB,QAAQ,WAAW;YAC7CqC,IAAI,EAAEA,CAAA,KAAM;cACVzF,OAAO,CAAC6B,OAAO,CAAC,SAAS,CAAC;YAC5B;UACF,CAAC,CAAC;QACJ,CAAE;QAAA2C,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,MAAMc,iBAAiB,GAAGA,CAAA,kBACxBnF,OAAA;IAAAiE,QAAA,gBAEEjE,OAAA,CAACzB,GAAG;MAAC6G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrB,KAAK,EAAE;QAAEsB,YAAY,EAAE;MAAG,CAAE;MAAApB,QAAA,gBACjDjE,OAAA,CAACxB,GAAG;QAAC8G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACjBjE,OAAA,CAAC1B,IAAI;UAAA2F,QAAA,eACHjE,OAAA,CAACX,SAAS;YACRiE,KAAK,EAAC,0BAAM;YACZM,KAAK,EAAEpC,oBAAoB,CAACC,QAAQ,CAACC,UAAW;YAChD8D,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrE,OAAA,CAACxB,GAAG;QAAC8G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACjBjE,OAAA,CAAC1B,IAAI;UAAA2F,QAAA,eACHjE,OAAA,CAACX,SAAS;YACRiE,KAAK,EAAC,0BAAM;YACZM,KAAK,EAAEpC,oBAAoB,CAACC,QAAQ,CAACE,cAAe;YACpD6D,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrE,OAAA,CAACxB,GAAG;QAAC8G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACjBjE,OAAA,CAAC1B,IAAI;UAAA2F,QAAA,eACHjE,OAAA,CAACX,SAAS;YACRiE,KAAK,EAAC,0BAAM;YACZM,KAAK,EAAEpC,oBAAoB,CAACC,QAAQ,CAACG,YAAa;YAClD6D,MAAM,EAAC,MAAG;YACVD,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU,CAAE;YACjC0B,SAAS,EAAG9B,KAAK,IAAK9D,cAAc,CAAC6F,MAAM,CAAC/B,KAAK,CAAC;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrE,OAAA,CAACxB,GAAG;QAAC8G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACjBjE,OAAA,CAAC1B,IAAI;UAAA2F,QAAA,eACHjE,OAAA,CAACX,SAAS;YACRiE,KAAK,EAAC,0BAAM;YACZM,KAAK,EAAEpC,oBAAoB,CAACC,QAAQ,CAACI,cAAe;YACpD+D,MAAM,EAAC,GAAG;YACVJ,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrE,OAAA,CAAC1B,IAAI;MAACgF,KAAK,EAAC,0BAAM;MAACS,KAAK,EAAE;QAAEsB,YAAY,EAAE;MAAG,CAAE;MAAApB,QAAA,eAC7CjE,OAAA,CAACzB,GAAG;QAAC6G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAnB,QAAA,gBACpBjE,OAAA,CAACxB,GAAG;UAAC8G,EAAE,EAAE,EAAG;UAACO,EAAE,EAAE,EAAG;UAAA5B,QAAA,eAClBjE,OAAA;YAAK+D,KAAK,EAAE;cAAEsB,YAAY,EAAE;YAAG,CAAE;YAAApB,QAAA,gBAC/BjE,OAAA,CAACE,IAAI;cAAC4F,MAAM;cAAA7B,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBrE,OAAA,CAACV,QAAQ;cACPyG,OAAO,EAAEvE,oBAAoB,CAACC,QAAQ,CAACM,gBAAiB;cACxDiE,MAAM,EAAC,QAAQ;cACfC,WAAW,EAAC;YAAS;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrE,OAAA,CAACxB,GAAG;UAAC8G,EAAE,EAAE,EAAG;UAACO,EAAE,EAAE,EAAG;UAAA5B,QAAA,eAClBjE,OAAA;YAAK+D,KAAK,EAAE;cAAEsB,YAAY,EAAE;YAAG,CAAE;YAAApB,QAAA,gBAC/BjE,OAAA,CAACE,IAAI;cAAC4F,MAAM;cAAA7B,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBrE,OAAA;cAAK+D,KAAK,EAAE;gBAAEmC,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE,MAAM;gBAAEnC,KAAK,EAAE;cAAU,CAAE;cAAAC,QAAA,EAChEzC,oBAAoB,CAACC,QAAQ,CAACK;YAAY;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPrE,OAAA,CAAC1B,IAAI;MAACgF,KAAK,EAAC,0BAAM;MAAAW,QAAA,eAChBjE,OAAA,CAACR,KAAK;QACJC,OAAO,EAAC,0BAAM;QACd2G,WAAW,eACTpG,OAAA;UAAAiE,QAAA,gBACEjE,OAAA;YAAAiE,QAAA,EAAI;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCrE,OAAA;YAAAiE,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClCrE,OAAA;YAAAiE,QAAA,EAAI;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnCrE,OAAA;YAAAiE,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CACL;QACDC,IAAI,EAAC,MAAM;QACX+B,QAAQ;MAAA;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,MAAMiC,qBAAqB,GAAGA,CAAA,kBAC5BtG,OAAA;IAAAiE,QAAA,eACEjE,OAAA,CAAC1B,IAAI;MAACgF,KAAK,EAAC,sCAAQ;MAAAW,QAAA,eAClBjE,OAAA,CAACtB,KAAK;QACJ6H,OAAO,EAAElD,mBAAoB;QAC7BmD,UAAU,EAAEhF,oBAAoB,CAACQ,OAAQ;QACzCyE,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,MAAM6C,mBAAmB,GAAGA,CAAA,kBAC1BlH,OAAA;IAAAiE,QAAA,eACEjE,OAAA,CAAC1B,IAAI;MAACgF,KAAK,EAAC,0BAAM;MAAAW,QAAA,eAChBjE,OAAA,CAACtB,KAAK;QACJ6H,OAAO,EAAE/B,iBAAkB;QAC3BgC,UAAU,EAAEhF,oBAAoB,CAACwB,WAAY;QAC7CyD,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,oBACErE,OAAA;IAAAiE,QAAA,gBACEjE,OAAA,CAAC1B,IAAI;MAAA2F,QAAA,gBACHjE,OAAA,CAACzB,GAAG;QAAC4I,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACrD,KAAK,EAAE;UAAEsB,YAAY,EAAE;QAAG,CAAE;QAAApB,QAAA,gBACtEjE,OAAA,CAACxB,GAAG;UAAAyF,QAAA,gBACFjE,OAAA,CAACC,KAAK;YAACoH,KAAK,EAAE,CAAE;YAACtD,KAAK,EAAE;cAAEuD,MAAM,EAAE;YAAE,CAAE;YAAArD,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrE,OAAA,CAACE,IAAI;YAACoE,IAAI,EAAC,WAAW;YAAAL,QAAA,EAAC;UAEvB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNrE,OAAA,CAACxB,GAAG;UAAAyF,QAAA,eACFjE,OAAA,CAACrB,KAAK;YAAAsF,QAAA,gBACJjE,OAAA,CAACvB,MAAM;cAACqG,IAAI,eAAE9E,OAAA,CAACH,cAAc;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACU,OAAO,EAAEhE,QAAS;cAAAkD,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrE,OAAA,CAACvB,MAAM;cAACqG,IAAI,eAAE9E,OAAA,CAACJ,cAAc;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrE,OAAA,CAACvB,MAAM;cACL6F,IAAI,EAAC,SAAS;cACdQ,IAAI,eAAE9E,OAAA,CAACN,eAAe;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BU,OAAO,EAAEA,CAAA,KAAMrE,2BAA2B,CAAC,IAAI,CAAE;cAAAuD,QAAA,EAClD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrE,OAAA,CAACT,IAAI;QAACgI,SAAS,EAAE1G,SAAU;QAAC2G,QAAQ,EAAE1G,YAAa;QAAAmD,QAAA,gBACjDjE,OAAA,CAACG,OAAO;UAACsH,GAAG,EAAC,0BAAM;UAAAxD,QAAA,EAChBkB,iBAAiB,CAAC;QAAC,GADE,UAAU;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eACVrE,OAAA,CAACG,OAAO;UAACsH,GAAG,EAAC,0BAAM;UAAAxD,QAAA,EAChBqC,qBAAqB,CAAC;QAAC,GADF,cAAc;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE7B,CAAC,eACVrE,OAAA,CAACG,OAAO;UAACsH,GAAG,EAAC,0BAAM;UAAAxD,QAAA,EAChBiD,mBAAmB,CAAC;QAAC,GADA,YAAY;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPrE,OAAA,CAACnB,KAAK;MACJyE,KAAK,EAAC,sCAAQ;MACdoE,IAAI,EAAEjH,wBAAyB;MAC/ByE,IAAI,EAAElE,cAAe;MACrB2G,QAAQ,EAAEA,CAAA,KAAMjH,2BAA2B,CAAC,KAAK,CAAE;MACnD+C,KAAK,EAAE,GAAI;MACXmE,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfC,cAAc,EAAEvH,OAAQ;MAAA0D,QAAA,eAExBjE,OAAA,CAAClB,IAAI;QAACiJ,IAAI,EAAEpH,gBAAiB;QAACqH,MAAM,EAAC,UAAU;QAAA/D,QAAA,gBAC7CjE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;UACRC,IAAI,EAAC,WAAW;UAChBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5I,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwE,QAAA,eAEhDjE,OAAA,CAACI,WAAW;YACV2D,KAAK,EAAE;cAAEN,KAAK,EAAE;YAAO,CAAE;YACzB6E,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM;UAAE;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZrE,OAAA,CAACb,OAAO;UAACoJ,WAAW,EAAC,MAAM;UAAAtE,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAE1CrE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;UACRC,IAAI,EAAC,2BAA2B;UAChCM,aAAa,EAAC,SAAS;UACvBC,YAAY,EAAE,IAAK;UAAAxE,QAAA,eAEnBjE,OAAA,CAACd,QAAQ;YAAA+E,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAEZrE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;UACRC,IAAI,EAAC,uBAAuB;UAC5BM,aAAa,EAAC,SAAS;UACvBC,YAAY,EAAE,IAAK;UAAAxE,QAAA,eAEnBjE,OAAA,CAACd,QAAQ;YAAA+E,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAEZrE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;UACRC,IAAI,EAAC,oBAAoB;UACzBM,aAAa,EAAC,SAAS;UACvBC,YAAY,EAAE,IAAK;UAAAxE,QAAA,eAEnBjE,OAAA,CAACd,QAAQ;YAAA+E,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAEZrE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;UACRC,IAAI,EAAC,qBAAqB;UAC1BM,aAAa,EAAC,SAAS;UACvBC,YAAY,EAAE,IAAK;UAAAxE,QAAA,eAEnBjE,OAAA,CAACd,QAAQ;YAAA+E,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZrE,OAAA,CAACb,OAAO;UAACoJ,WAAW,EAAC,MAAM;UAAAtE,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAE1CrE,OAAA,CAACzB,GAAG;UAAC6G,MAAM,EAAE,EAAG;UAAAnB,QAAA,gBACdjE,OAAA,CAACxB,GAAG;YAACkK,IAAI,EAAE,EAAG;YAAAzE,QAAA,eACZjE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAC,6BAAS;cACfM,YAAY,EAAE,EAAG;cAAAxE,QAAA,eAEjBjE,OAAA,CAACjB,WAAW;gBACV4J,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,EAAG;gBACR7E,KAAK,EAAE;kBAAEN,KAAK,EAAE;gBAAO,CAAE;gBACzB6E,WAAW,EAAC;cAAM;gBAAApE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrE,OAAA,CAACxB,GAAG;YAACkK,IAAI,EAAE,EAAG;YAAAzE,QAAA,eACZjE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAC,8CAAW;cACjBM,YAAY,EAAE,EAAG;cAAAxE,QAAA,eAEjBjE,OAAA,CAACjB,WAAW;gBACV4J,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,EAAG;gBACR7E,KAAK,EAAE;kBAAEN,KAAK,EAAE;gBAAO,CAAE;gBACzB6E,WAAW,EAAC;cAAQ;gBAAApE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA,CAACzB,GAAG;UAAC6G,MAAM,EAAE,EAAG;UAAAnB,QAAA,gBACdjE,OAAA,CAACxB,GAAG;YAACkK,IAAI,EAAE,EAAG;YAAAzE,QAAA,eACZjE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAC,oBAAK;cACXM,YAAY,EAAC,QAAQ;cAAAxE,QAAA,eAErBjE,OAAA,CAAChB,MAAM;gBAACsJ,WAAW,EAAC,gCAAO;gBAAArE,QAAA,gBACzBjE,OAAA,CAAChB,MAAM,CAAC6J,MAAM;kBAACjF,KAAK,EAAC,MAAM;kBAAAK,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC7CrE,OAAA,CAAChB,MAAM,CAAC6J,MAAM;kBAACjF,KAAK,EAAC,QAAQ;kBAAAK,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC/CrE,OAAA,CAAChB,MAAM,CAAC6J,MAAM;kBAACjF,KAAK,EAAC,KAAK;kBAAAK,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrE,OAAA,CAACxB,GAAG;YAACkK,IAAI,EAAE,EAAG;YAAAzE,QAAA,eACZjE,OAAA,CAAClB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBM,aAAa,EAAC,SAAS;cACvBC,YAAY,EAAE,KAAM;cAAAxE,QAAA,eAEpBjE,OAAA,CAACd,QAAQ;gBAAA+E,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/D,EAAA,CAhnBID,wBAAkC;EAAA,QAGXvB,IAAI,CAAC8B,OAAO;AAAA;AAAAkI,EAAA,GAHnCzI,wBAAkC;AAknBxC,eAAeA,wBAAwB;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}