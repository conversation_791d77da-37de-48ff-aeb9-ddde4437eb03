{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams,useNavigate}from'react-router-dom';import{Card,Typography,Form,Input,Select,Button,Space,Row,Col,Table,Modal,InputNumber,message,Breadcrumb,Tabs,Tag,Tooltip}from'antd';import{SaveOutlined,ArrowLeftOutlined,PlusOutlined,EditOutlined,DeleteOutlined,CopyOutlined,FileTextOutlined,HistoryOutlined}from'@ant-design/icons';import{useAppDispatch,useAppSelector}from'../../hooks/redux';import{fetchCoreBOM,updateCoreBOM}from'../../store/slices/bomSlice';import{ConfirmDialog}from'../../components';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{TextArea}=Input;const{TabPane}=Tabs;const CoreBOMEditPage=()=>{const{id}=useParams();const navigate=useNavigate();const dispatch=useAppDispatch();const{currentBOM,loading}=useAppSelector(state=>state.bom);const[form]=Form.useForm();const[materialModalVisible,setMaterialModalVisible]=useState(false);const[editingMaterial,setEditingMaterial]=useState(null);const[materialForm]=Form.useForm();const[activeTab,setActiveTab]=useState('basic');useEffect(()=>{if(id){dispatch(fetchCoreBOM(id));}},[id,dispatch]);useEffect(()=>{if(currentBOM){form.setFieldsValue({bomCode:currentBOM.code,bomName:currentBOM.name,version:currentBOM.version,productLine:'productLine'in currentBOM?currentBOM.productLine:'5G基站',description:currentBOM.description,status:currentBOM.status});}},[currentBOM,form]);const handleSave=async()=>{try{const values=await form.validateFields();await dispatch(updateCoreBOM({id:id,data:values}));message.success('BOM保存成功');}catch(error){message.error('保存失败，请重试');}};const handleAddMaterial=()=>{setEditingMaterial(null);materialForm.resetFields();setMaterialModalVisible(true);};const handleEditMaterial=record=>{setEditingMaterial(record);materialForm.setFieldsValue(record);setMaterialModalVisible(true);};const handleMaterialModalOk=async()=>{try{const values=await materialForm.validateFields();if(editingMaterial){message.success('物料更新成功');}else{message.success('物料添加成功');}setMaterialModalVisible(false);// TODO: 更新物料列表\n}catch(error){message.error('操作失败');}};// 模拟BOM物料数据\nconst mockMaterials=[{id:'1',materialCode:'ANT-MAIN-001',materialName:'5G主天线单元',specification:'ANT-5G-001',quantity:1,unit:'PCS',unitPrice:1500,totalPrice:1500,supplier:'华为技术',leadTime:14,level:1,parentId:null},{id:'2',materialCode:'RF-AMP-001',materialName:'RF功率放大器',specification:'RF-PA-5G-001',quantity:2,unit:'PCS',unitPrice:2500,totalPrice:5000,supplier:'中兴通讯',leadTime:21,level:2,parentId:'1'}];const materialColumns=[{title:'层级',dataIndex:'level',key:'level',width:60,render:level=>/*#__PURE__*/_jsxs(Tag,{color:\"blue\",children:[\"L\",level]})},{title:'物料编码',dataIndex:'materialCode',key:'materialCode',width:120},{title:'物料名称',dataIndex:'materialName',key:'materialName',ellipsis:true},{title:'规格型号',dataIndex:'specification',key:'specification',width:120},{title:'数量',dataIndex:'quantity',key:'quantity',width:80,render:(quantity,record)=>\"\".concat(quantity,\" \").concat(record.unit)},{title:'单价',dataIndex:'unitPrice',key:'unitPrice',width:100,render:price=>\"\\xA5\".concat(price.toLocaleString())},{title:'总价',dataIndex:'totalPrice',key:'totalPrice',width:100,render:price=>\"\\xA5\".concat(price.toLocaleString())},{title:'供应商',dataIndex:'supplier',key:'supplier',width:120},{title:'交期',dataIndex:'leadTime',key:'leadTime',width:80,render:days=>\"\".concat(days,\"\\u5929\")},{title:'操作',key:'action',width:150,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEditMaterial(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u590D\\u5236\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(CopyOutlined,{}),onClick:()=>{message.success('物料已复制');}})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>{ConfirmDialog.confirm({title:'确认删除',content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u7269\\u6599\\u5417\\uFF1F\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#ff4d4f',marginTop:12},children:\"\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\uFF01\"})]}),type:'warning',onConfirm:()=>{message.success('物料已删除');}});}})})]})}];if(loading){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'50px 0'},children:/*#__PURE__*/_jsx(Title,{level:4,children:\"\\u52A0\\u8F7D\\u4E2D...\"})});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Breadcrumb,{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Breadcrumb.Item,{children:\"BOM\\u7BA1\\u7406\"}),/*#__PURE__*/_jsx(Breadcrumb.Item,{children:\"\\u6838\\u5FC3BOM\"}),/*#__PURE__*/_jsx(Breadcrumb.Item,{children:\"\\u7F16\\u8F91BOM\"})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ArrowLeftOutlined,{}),onClick:()=>navigate(-1),children:\"\\u8FD4\\u56DE\"}),/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u7F16\\u8F91\\u6838\\u5FC3BOM\"}),currentBOM&&/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:currentBOM.code})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(HistoryOutlined,{}),children:\"\\u7248\\u672C\\u5386\\u53F2\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(SaveOutlined,{}),onClick:handleSave,children:\"\\u4FDD\\u5B58\"})]})})]}),/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:[/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(FileTextOutlined,{}),\"\\u57FA\\u672C\\u4FE1\\u606F\"]}),children:/*#__PURE__*/_jsx(Form,{form:form,layout:\"vertical\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"bomCode\",label:\"BOM\\u7F16\\u7801\",rules:[{required:true,message:'请输入BOM编码'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u7F16\\u7801\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"bomName\",label:\"BOM\\u540D\\u79F0\",rules:[{required:true,message:'请输入BOM名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"version\",label:\"\\u7248\\u672C\",rules:[{required:true,message:'请输入版本'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7248\\u672C\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"productLine\",label:\"\\u4EA7\\u54C1\\u7EBF\",rules:[{required:true,message:'请选择产品线'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u7EBF\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"5G\\u57FA\\u7AD9\",children:\"5G\\u57FA\\u7AD9\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"4G\\u57FA\\u7AD9\",children:\"4G\\u57FA\\u7AD9\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"\\u4F20\\u8F93\\u8BBE\\u5907\",children:\"\\u4F20\\u8F93\\u8BBE\\u5907\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"status\",label:\"\\u72B6\\u6001\",rules:[{required:true,message:'请选择状态'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"draft\",children:\"\\u8349\\u7A3F\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"active\",children:\"\\u751F\\u6548\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"obsolete\",children:\"\\u5E9F\\u5F03\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,children:/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u63CF\\u8FF0\",children:/*#__PURE__*/_jsx(TextArea,{rows:4,placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u63CF\\u8FF0\"})})})]})})},\"basic\"),/*#__PURE__*/_jsxs(TabPane,{tab:\"\\u7269\\u6599\\u6E05\\u5355\",children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u7269\\u6599\\u6E05\\u5355\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAddMaterial,children:\"\\u6DFB\\u52A0\\u7269\\u6599\"})})]}),/*#__PURE__*/_jsx(Table,{columns:materialColumns,dataSource:mockMaterials,rowKey:\"id\",scroll:{x:1000},pagination:false})]},\"materials\")]})]}),/*#__PURE__*/_jsx(Modal,{title:editingMaterial?'编辑物料':'添加物料',open:materialModalVisible,onOk:handleMaterialModalOk,onCancel:()=>setMaterialModalVisible(false),width:600,children:/*#__PURE__*/_jsx(Form,{form:materialForm,layout:\"vertical\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"materialCode\",label:\"\\u7269\\u6599\\u7F16\\u7801\",rules:[{required:true,message:'请输入物料编码'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u7F16\\u7801\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"materialName\",label:\"\\u7269\\u6599\\u540D\\u79F0\",rules:[{required:true,message:'请输入物料名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"specification\",label:\"\\u89C4\\u683C\\u578B\\u53F7\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u89C4\\u683C\\u578B\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"quantity\",label:\"\\u6570\\u91CF\",rules:[{required:true,message:'请输入数量'}],children:/*#__PURE__*/_jsx(InputNumber,{min:1,style:{width:'100%'},placeholder:\"\\u8BF7\\u8F93\\u5165\\u6570\\u91CF\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"unit\",label:\"\\u5355\\u4F4D\",rules:[{required:true,message:'请选择单位'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u5355\\u4F4D\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"PCS\",children:\"PCS\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"KG\",children:\"KG\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"M\",children:\"M\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"SET\",children:\"SET\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"unitPrice\",label:\"\\u5355\\u4EF7\",children:/*#__PURE__*/_jsx(InputNumber,{min:0,precision:2,style:{width:'100%'},placeholder:\"\\u8BF7\\u8F93\\u5165\\u5355\\u4EF7\"})})})]})})})]});};export default CoreBOMEditPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Card", "Typography", "Form", "Input", "Select", "<PERSON><PERSON>", "Space", "Row", "Col", "Table", "Modal", "InputNumber", "message", "Breadcrumb", "Tabs", "Tag", "<PERSON><PERSON><PERSON>", "SaveOutlined", "ArrowLeftOutlined", "PlusOutlined", "EditOutlined", "DeleteOutlined", "CopyOutlined", "FileTextOutlined", "HistoryOutlined", "useAppDispatch", "useAppSelector", "fetchCoreBOM", "updateCoreBOM", "ConfirmDialog", "jsxs", "_jsxs", "jsx", "_jsx", "Title", "Text", "TextArea", "TabPane", "CoreBOMEditPage", "id", "navigate", "dispatch", "currentBOM", "loading", "state", "bom", "form", "useForm", "materialModalVisible", "setMaterialModalVisible", "editingMaterial", "setEditingMaterial", "materialForm", "activeTab", "setActiveTab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomCode", "code", "bom<PERSON>ame", "name", "version", "productLine", "description", "status", "handleSave", "values", "validateFields", "data", "success", "error", "handleAddMaterial", "resetFields", "handleEditMaterial", "record", "handleMaterialModalOk", "mockMaterials", "materialCode", "materialName", "specification", "quantity", "unit", "unitPrice", "totalPrice", "supplier", "leadTime", "level", "parentId", "materialColumns", "title", "dataIndex", "key", "width", "render", "color", "children", "ellipsis", "concat", "price", "toLocaleString", "days", "fixed", "_", "size", "type", "icon", "onClick", "danger", "confirm", "content", "style", "marginTop", "onConfirm", "textAlign", "padding", "marginBottom", "<PERSON><PERSON>", "justify", "align", "margin", "active<PERSON><PERSON>", "onChange", "tab", "layout", "gutter", "xs", "md", "label", "rules", "required", "placeholder", "Option", "value", "rows", "strong", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "open", "onOk", "onCancel", "min", "precision"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/CoreBOMEditPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Typography,\n  Form,\n  Input,\n  Select,\n  Button,\n  Space,\n  Row,\n  Col,\n  Table,\n  Modal,\n  InputNumber,\n  message,\n  Breadcrumb,\n  Tabs,\n  Tag,\n  Tooltip,\n} from 'antd';\nimport {\n  SaveOutlined,\n  ArrowLeftOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  CopyOutlined,\n  FileTextOutlined,\n  HistoryOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCoreBOM, updateCoreBOM } from '../../store/slices/bomSlice';\nimport { formatDate } from '../../utils';\nimport { ConfirmDialog } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { TabPane } = Tabs;\n\nconst CoreBOMEditPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const { currentBOM, loading } = useAppSelector(state => state.bom);\n\n  const [form] = Form.useForm();\n  const [materialModalVisible, setMaterialModalVisible] = useState(false);\n  const [editingMaterial, setEditingMaterial] = useState<any>(null);\n  const [materialForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('basic');\n\n  useEffect(() => {\n    if (id) {\n      dispatch(fetchCoreBOM(id));\n    }\n  }, [id, dispatch]);\n\n  useEffect(() => {\n    if (currentBOM) {\n      form.setFieldsValue({\n        bomCode: currentBOM.code,\n        bomName: currentBOM.name,\n        version: currentBOM.version,\n        productLine: 'productLine' in currentBOM ? currentBOM.productLine : '5G基站',\n        description: currentBOM.description,\n        status: currentBOM.status,\n      });\n    }\n  }, [currentBOM, form]);\n\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      await dispatch(updateCoreBOM({ id: id!, data: values }));\n      message.success('BOM保存成功');\n    } catch (error) {\n      message.error('保存失败，请重试');\n    }\n  };\n\n  const handleAddMaterial = () => {\n    setEditingMaterial(null);\n    materialForm.resetFields();\n    setMaterialModalVisible(true);\n  };\n\n  const handleEditMaterial = (record: any) => {\n    setEditingMaterial(record);\n    materialForm.setFieldsValue(record);\n    setMaterialModalVisible(true);\n  };\n\n  const handleMaterialModalOk = async () => {\n    try {\n      const values = await materialForm.validateFields();\n\n      if (editingMaterial) {\n        message.success('物料更新成功');\n      } else {\n        message.success('物料添加成功');\n      }\n\n      setMaterialModalVisible(false);\n      // TODO: 更新物料列表\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  // 模拟BOM物料数据\n  const mockMaterials = [\n    {\n      id: '1',\n      materialCode: 'ANT-MAIN-001',\n      materialName: '5G主天线单元',\n      specification: 'ANT-5G-001',\n      quantity: 1,\n      unit: 'PCS',\n      unitPrice: 1500,\n      totalPrice: 1500,\n      supplier: '华为技术',\n      leadTime: 14,\n      level: 1,\n      parentId: null,\n    },\n    {\n      id: '2',\n      materialCode: 'RF-AMP-001',\n      materialName: 'RF功率放大器',\n      specification: 'RF-PA-5G-001',\n      quantity: 2,\n      unit: 'PCS',\n      unitPrice: 2500,\n      totalPrice: 5000,\n      supplier: '中兴通讯',\n      leadTime: 21,\n      level: 2,\n      parentId: '1',\n    },\n  ];\n\n  const materialColumns = [\n    {\n      title: '层级',\n      dataIndex: 'level',\n      key: 'level',\n      width: 60,\n      render: (level: number) => <Tag color=\"blue\">L{level}</Tag>,\n    },\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      ellipsis: true,\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 120,\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n      render: (quantity: number, record: any) => `${quantity} ${record.unit}`,\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      width: 100,\n      render: (price: number) => `¥${price.toLocaleString()}`,\n    },\n    {\n      title: '总价',\n      dataIndex: 'totalPrice',\n      key: 'totalPrice',\n      width: 100,\n      render: (price: number) => `¥${price.toLocaleString()}`,\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 120,\n    },\n    {\n      title: '交期',\n      dataIndex: 'leadTime',\n      key: 'leadTime',\n      width: 80,\n      render: (days: number) => `${days}天`,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: any) => (\n        <Space size=\"small\">\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditMaterial(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"复制\">\n            <Button\n              type=\"text\"\n              icon={<CopyOutlined />}\n              onClick={() => {\n                message.success('物料已复制');\n              }}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => {\n                ConfirmDialog.confirm({\n                  title: '确认删除',\n                  content: (\n                    <div>\n                      <p>确定要删除这个物料吗？</p>\n                      <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复！</p>\n                    </div>\n                  ),\n                  type: 'warning',\n                  onConfirm: () => {\n                    message.success('物料已删除');\n                  }\n                });\n              }}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Title level={4}>加载中...</Title>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Breadcrumb style={{ marginBottom: 16 }}>\n        <Breadcrumb.Item>BOM管理</Breadcrumb.Item>\n        <Breadcrumb.Item>核心BOM</Breadcrumb.Item>\n        <Breadcrumb.Item>编辑BOM</Breadcrumb.Item>\n      </Breadcrumb>\n\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n          <Col>\n            <Space>\n              <Button\n                icon={<ArrowLeftOutlined />}\n                onClick={() => navigate(-1)}\n              >\n                返回\n              </Button>\n              <Title level={4} style={{ margin: 0 }}>\n                编辑核心BOM\n              </Title>\n              {currentBOM && (\n                <Tag color=\"blue\">{currentBOM.code}</Tag>\n              )}\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<HistoryOutlined />}>\n                版本历史\n              </Button>\n              <Button type=\"primary\" icon={<SaveOutlined />} onClick={handleSave}>\n                保存\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab={<span><FileTextOutlined />基本信息</span>} key=\"basic\">\n            <Form form={form} layout=\"vertical\">\n              <Row gutter={[16, 16]}>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name=\"bomCode\"\n                    label=\"BOM编码\"\n                    rules={[{ required: true, message: '请输入BOM编码' }]}\n                  >\n                    <Input placeholder=\"请输入BOM编码\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name=\"bomName\"\n                    label=\"BOM名称\"\n                    rules={[{ required: true, message: '请输入BOM名称' }]}\n                  >\n                    <Input placeholder=\"请输入BOM名称\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name=\"version\"\n                    label=\"版本\"\n                    rules={[{ required: true, message: '请输入版本' }]}\n                  >\n                    <Input placeholder=\"请输入版本\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"productLine\"\n                    label=\"产品线\"\n                    rules={[{ required: true, message: '请选择产品线' }]}\n                  >\n                    <Select placeholder=\"请选择产品线\">\n                      <Select.Option value=\"5G基站\">5G基站</Select.Option>\n                      <Select.Option value=\"4G基站\">4G基站</Select.Option>\n                      <Select.Option value=\"传输设备\">传输设备</Select.Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"status\"\n                    label=\"状态\"\n                    rules={[{ required: true, message: '请选择状态' }]}\n                  >\n                    <Select placeholder=\"请选择状态\">\n                      <Select.Option value=\"draft\">草稿</Select.Option>\n                      <Select.Option value=\"active\">生效</Select.Option>\n                      <Select.Option value=\"obsolete\">废弃</Select.Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n                <Col xs={24}>\n                  <Form.Item name=\"description\" label=\"描述\">\n                    <TextArea rows={4} placeholder=\"请输入BOM描述\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n            </Form>\n          </TabPane>\n\n          <TabPane tab=\"物料清单\" key=\"materials\">\n            <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n              <Col>\n                <Text strong>物料清单</Text>\n              </Col>\n              <Col>\n                <Button\n                  type=\"primary\"\n                  icon={<PlusOutlined />}\n                  onClick={handleAddMaterial}\n                >\n                  添加物料\n                </Button>\n              </Col>\n            </Row>\n\n            <Table\n              columns={materialColumns}\n              dataSource={mockMaterials}\n              rowKey=\"id\"\n              scroll={{ x: 1000 }}\n              pagination={false}\n            />\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 物料编辑模态框 */}\n      <Modal\n        title={editingMaterial ? '编辑物料' : '添加物料'}\n        open={materialModalVisible}\n        onOk={handleMaterialModalOk}\n        onCancel={() => setMaterialModalVisible(false)}\n        width={600}\n      >\n        <Form form={materialForm} layout=\"vertical\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"materialCode\"\n                label=\"物料编码\"\n                rules={[{ required: true, message: '请输入物料编码' }]}\n              >\n                <Input placeholder=\"请输入物料编码\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"materialName\"\n                label=\"物料名称\"\n                rules={[{ required: true, message: '请输入物料名称' }]}\n              >\n                <Input placeholder=\"请输入物料名称\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"specification\"\n                label=\"规格型号\"\n              >\n                <Input placeholder=\"请输入规格型号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"quantity\"\n                label=\"数量\"\n                rules={[{ required: true, message: '请输入数量' }]}\n              >\n                <InputNumber min={1} style={{ width: '100%' }} placeholder=\"请输入数量\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"unit\"\n                label=\"单位\"\n                rules={[{ required: true, message: '请选择单位' }]}\n              >\n                <Select placeholder=\"请选择单位\">\n                  <Select.Option value=\"PCS\">PCS</Select.Option>\n                  <Select.Option value=\"KG\">KG</Select.Option>\n                  <Select.Option value=\"M\">M</Select.Option>\n                  <Select.Option value=\"SET\">SET</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"unitPrice\"\n                label=\"单价\"\n              >\n                <InputNumber\n                  min={0}\n                  precision={2}\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入单价\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CoreBOMEditPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OACEC,IAAI,CACJC,UAAU,CACVC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,KAAK,CACLC,GAAG,CACHC,GAAG,CACHC,KAAK,CACLC,KAAK,CACLC,WAAW,CACXC,OAAO,CACPC,UAAU,CACVC,IAAI,CACJC,GAAG,CACHC,OAAO,KACF,MAAM,CACb,OACEC,YAAY,CACZC,iBAAiB,CACjBC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,YAAY,CACZC,gBAAgB,CAChBC,eAAe,KACV,mBAAmB,CAE1B,OAASC,cAAc,CAAEC,cAAc,KAAQ,mBAAmB,CAClE,OAASC,YAAY,CAAEC,aAAa,KAAQ,6BAA6B,CAEzE,OAASC,aAAa,KAAQ,kBAAkB,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAGjD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGlC,UAAU,CAClC,KAAM,CAAEmC,QAAS,CAAC,CAAGjC,KAAK,CAC1B,KAAM,CAAEkC,OAAQ,CAAC,CAAGvB,IAAI,CAExB,KAAM,CAAAwB,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAEC,EAAG,CAAC,CAAGzC,SAAS,CAAiB,CAAC,CAC1C,KAAM,CAAA0C,QAAQ,CAAGzC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA0C,QAAQ,CAAGhB,cAAc,CAAC,CAAC,CACjC,KAAM,CAAEiB,UAAU,CAAEC,OAAQ,CAAC,CAAGjB,cAAc,CAACkB,KAAK,EAAIA,KAAK,CAACC,GAAG,CAAC,CAElE,KAAM,CAACC,IAAI,CAAC,CAAG5C,IAAI,CAAC6C,OAAO,CAAC,CAAC,CAC7B,KAAM,CAACC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAACsD,eAAe,CAAEC,kBAAkB,CAAC,CAAGvD,QAAQ,CAAM,IAAI,CAAC,CACjE,KAAM,CAACwD,YAAY,CAAC,CAAGlD,IAAI,CAAC6C,OAAO,CAAC,CAAC,CACrC,KAAM,CAACM,SAAS,CAAEC,YAAY,CAAC,CAAG1D,QAAQ,CAAC,OAAO,CAAC,CAEnDC,SAAS,CAAC,IAAM,CACd,GAAI0C,EAAE,CAAE,CACNE,QAAQ,CAACd,YAAY,CAACY,EAAE,CAAC,CAAC,CAC5B,CACF,CAAC,CAAE,CAACA,EAAE,CAAEE,QAAQ,CAAC,CAAC,CAElB5C,SAAS,CAAC,IAAM,CACd,GAAI6C,UAAU,CAAE,CACdI,IAAI,CAACS,cAAc,CAAC,CAClBC,OAAO,CAAEd,UAAU,CAACe,IAAI,CACxBC,OAAO,CAAEhB,UAAU,CAACiB,IAAI,CACxBC,OAAO,CAAElB,UAAU,CAACkB,OAAO,CAC3BC,WAAW,CAAE,aAAa,EAAI,CAAAnB,UAAU,CAAGA,UAAU,CAACmB,WAAW,CAAG,MAAM,CAC1EC,WAAW,CAAEpB,UAAU,CAACoB,WAAW,CACnCC,MAAM,CAAErB,UAAU,CAACqB,MACrB,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,CAACrB,UAAU,CAAEI,IAAI,CAAC,CAAC,CAEtB,KAAM,CAAAkB,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAnB,IAAI,CAACoB,cAAc,CAAC,CAAC,CAC1C,KAAM,CAAAzB,QAAQ,CAACb,aAAa,CAAC,CAAEW,EAAE,CAAEA,EAAG,CAAE4B,IAAI,CAAEF,MAAO,CAAC,CAAC,CAAC,CACxDrD,OAAO,CAACwD,OAAO,CAAC,SAAS,CAAC,CAC5B,CAAE,MAAOC,KAAK,CAAE,CACdzD,OAAO,CAACyD,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9BnB,kBAAkB,CAAC,IAAI,CAAC,CACxBC,YAAY,CAACmB,WAAW,CAAC,CAAC,CAC1BtB,uBAAuB,CAAC,IAAI,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAuB,kBAAkB,CAAIC,MAAW,EAAK,CAC1CtB,kBAAkB,CAACsB,MAAM,CAAC,CAC1BrB,YAAY,CAACG,cAAc,CAACkB,MAAM,CAAC,CACnCxB,uBAAuB,CAAC,IAAI,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAyB,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACF,KAAM,CAAAT,MAAM,CAAG,KAAM,CAAAb,YAAY,CAACc,cAAc,CAAC,CAAC,CAElD,GAAIhB,eAAe,CAAE,CACnBtC,OAAO,CAACwD,OAAO,CAAC,QAAQ,CAAC,CAC3B,CAAC,IAAM,CACLxD,OAAO,CAACwD,OAAO,CAAC,QAAQ,CAAC,CAC3B,CAEAnB,uBAAuB,CAAC,KAAK,CAAC,CAC9B;AACF,CAAE,MAAOoB,KAAK,CAAE,CACdzD,OAAO,CAACyD,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAAM,aAAa,CAAG,CACpB,CACEpC,EAAE,CAAE,GAAG,CACPqC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,SAAS,CACvBC,aAAa,CAAE,YAAY,CAC3BC,QAAQ,CAAE,CAAC,CACXC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CAAC,CACRC,QAAQ,CAAE,IACZ,CAAC,CACD,CACE/C,EAAE,CAAE,GAAG,CACPqC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,SAAS,CACvBC,aAAa,CAAE,cAAc,CAC7BC,QAAQ,CAAE,CAAC,CACXC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CAAC,CACRC,QAAQ,CAAE,GACZ,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtB,CACEC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,OAAO,CAClBC,GAAG,CAAE,OAAO,CACZC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAGP,KAAa,eAAKtD,KAAA,CAAChB,GAAG,EAAC8E,KAAK,CAAC,MAAM,CAAAC,QAAA,EAAC,GAAC,CAACT,KAAK,EAAM,CAC5D,CAAC,CACD,CACEG,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBK,QAAQ,CAAE,IACZ,CAAC,CACD,CACEP,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAEA,CAACb,QAAgB,CAAEN,MAAW,MAAAuB,MAAA,CAAQjB,QAAQ,MAAAiB,MAAA,CAAIvB,MAAM,CAACO,IAAI,CACvE,CAAC,CACD,CACEQ,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGK,KAAa,SAAAD,MAAA,CAASC,KAAK,CAACC,cAAc,CAAC,CAAC,CACvD,CAAC,CACD,CACEV,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGK,KAAa,SAAAD,MAAA,CAASC,KAAK,CAACC,cAAc,CAAC,CAAC,CACvD,CAAC,CACD,CACEV,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAGO,IAAY,KAAAH,MAAA,CAAQG,IAAI,UACnC,CAAC,CACD,CACEX,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVS,KAAK,CAAE,OAAgB,CACvBR,MAAM,CAAEA,CAACS,CAAM,CAAE5B,MAAW,gBAC1B1C,KAAA,CAACzB,KAAK,EAACgG,IAAI,CAAC,OAAO,CAAAR,QAAA,eACjB7D,IAAA,CAACjB,OAAO,EAACwE,KAAK,CAAC,cAAI,CAAAM,QAAA,cACjB7D,IAAA,CAAC5B,MAAM,EACLkG,IAAI,CAAC,MAAM,CACXC,IAAI,cAAEvE,IAAA,CAACb,YAAY,GAAE,CAAE,CACvBqF,OAAO,CAAEA,CAAA,GAAMjC,kBAAkB,CAACC,MAAM,CAAE,CAC3C,CAAC,CACK,CAAC,cACVxC,IAAA,CAACjB,OAAO,EAACwE,KAAK,CAAC,cAAI,CAAAM,QAAA,cACjB7D,IAAA,CAAC5B,MAAM,EACLkG,IAAI,CAAC,MAAM,CACXC,IAAI,cAAEvE,IAAA,CAACX,YAAY,GAAE,CAAE,CACvBmF,OAAO,CAAEA,CAAA,GAAM,CACb7F,OAAO,CAACwD,OAAO,CAAC,OAAO,CAAC,CAC1B,CAAE,CACH,CAAC,CACK,CAAC,cACVnC,IAAA,CAACjB,OAAO,EAACwE,KAAK,CAAC,cAAI,CAAAM,QAAA,cACjB7D,IAAA,CAAC5B,MAAM,EACLkG,IAAI,CAAC,MAAM,CACXG,MAAM,MACNF,IAAI,cAAEvE,IAAA,CAACZ,cAAc,GAAE,CAAE,CACzBoF,OAAO,CAAEA,CAAA,GAAM,CACb5E,aAAa,CAAC8E,OAAO,CAAC,CACpBnB,KAAK,CAAE,MAAM,CACboB,OAAO,cACL7E,KAAA,QAAA+D,QAAA,eACE7D,IAAA,MAAA6D,QAAA,CAAG,oEAAW,CAAG,CAAC,cAClB7D,IAAA,MAAG4E,KAAK,CAAE,CAAEhB,KAAK,CAAE,SAAS,CAAEiB,SAAS,CAAE,EAAG,CAAE,CAAAhB,QAAA,CAAC,kDAAQ,CAAG,CAAC,EACxD,CACN,CACDS,IAAI,CAAE,SAAS,CACfQ,SAAS,CAAEA,CAAA,GAAM,CACfnG,OAAO,CAACwD,OAAO,CAAC,OAAO,CAAC,CAC1B,CACF,CAAC,CAAC,CACJ,CAAE,CACH,CAAC,CACK,CAAC,EACL,CAEX,CAAC,CACF,CAED,GAAIzB,OAAO,CAAE,CACX,mBACEV,IAAA,QAAK4E,KAAK,CAAE,CAAEG,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,QAAS,CAAE,CAAAnB,QAAA,cACrD7D,IAAA,CAACC,KAAK,EAACmD,KAAK,CAAE,CAAE,CAAAS,QAAA,CAAC,uBAAM,CAAO,CAAC,CAC5B,CAAC,CAEV,CAEA,mBACE/D,KAAA,QAAA+D,QAAA,eACE/D,KAAA,CAAClB,UAAU,EAACgG,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,eACtC7D,IAAA,CAACpB,UAAU,CAACsG,IAAI,EAAArB,QAAA,CAAC,iBAAK,CAAiB,CAAC,cACxC7D,IAAA,CAACpB,UAAU,CAACsG,IAAI,EAAArB,QAAA,CAAC,iBAAK,CAAiB,CAAC,cACxC7D,IAAA,CAACpB,UAAU,CAACsG,IAAI,EAAArB,QAAA,CAAC,iBAAK,CAAiB,CAAC,EAC9B,CAAC,cAEb/D,KAAA,CAAC/B,IAAI,EAAA8F,QAAA,eACH/D,KAAA,CAACxB,GAAG,EAAC6G,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACR,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,eACtE7D,IAAA,CAACzB,GAAG,EAAAsF,QAAA,cACF/D,KAAA,CAACzB,KAAK,EAAAwF,QAAA,eACJ7D,IAAA,CAAC5B,MAAM,EACLmG,IAAI,cAAEvE,IAAA,CAACf,iBAAiB,GAAE,CAAE,CAC5BuF,OAAO,CAAEA,CAAA,GAAMjE,QAAQ,CAAC,CAAC,CAAC,CAAE,CAAAsD,QAAA,CAC7B,cAED,CAAQ,CAAC,cACT7D,IAAA,CAACC,KAAK,EAACmD,KAAK,CAAE,CAAE,CAACwB,KAAK,CAAE,CAAES,MAAM,CAAE,CAAE,CAAE,CAAAxB,QAAA,CAAC,6BAEvC,CAAO,CAAC,CACPpD,UAAU,eACTT,IAAA,CAAClB,GAAG,EAAC8E,KAAK,CAAC,MAAM,CAAAC,QAAA,CAAEpD,UAAU,CAACe,IAAI,CAAM,CACzC,EACI,CAAC,CACL,CAAC,cACNxB,IAAA,CAACzB,GAAG,EAAAsF,QAAA,cACF/D,KAAA,CAACzB,KAAK,EAAAwF,QAAA,eACJ7D,IAAA,CAAC5B,MAAM,EAACmG,IAAI,cAAEvE,IAAA,CAACT,eAAe,GAAE,CAAE,CAAAsE,QAAA,CAAC,0BAEnC,CAAQ,CAAC,cACT7D,IAAA,CAAC5B,MAAM,EAACkG,IAAI,CAAC,SAAS,CAACC,IAAI,cAAEvE,IAAA,CAAChB,YAAY,GAAE,CAAE,CAACwF,OAAO,CAAEzC,UAAW,CAAA8B,QAAA,CAAC,cAEpE,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAEN/D,KAAA,CAACjB,IAAI,EAACyG,SAAS,CAAElE,SAAU,CAACmE,QAAQ,CAAElE,YAAa,CAAAwC,QAAA,eACjD7D,IAAA,CAACI,OAAO,EAACoF,GAAG,cAAE1F,KAAA,SAAA+D,QAAA,eAAM7D,IAAA,CAACV,gBAAgB,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAAuE,QAAA,cAClD7D,IAAA,CAAC/B,IAAI,EAAC4C,IAAI,CAAEA,IAAK,CAAC4E,MAAM,CAAC,UAAU,CAAA5B,QAAA,cACjC/D,KAAA,CAACxB,GAAG,EAACoH,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAA7B,QAAA,eACpB7D,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA/B,QAAA,cACjB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,SAAS,CACdmE,KAAK,CAAC,iBAAO,CACbC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,UAAW,CAAC,CAAE,CAAAkF,QAAA,cAEjD7D,IAAA,CAAC9B,KAAK,EAAC8H,WAAW,CAAC,mCAAU,CAAE,CAAC,CACvB,CAAC,CACT,CAAC,cACNhG,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA/B,QAAA,cACjB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,SAAS,CACdmE,KAAK,CAAC,iBAAO,CACbC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,UAAW,CAAC,CAAE,CAAAkF,QAAA,cAEjD7D,IAAA,CAAC9B,KAAK,EAAC8H,WAAW,CAAC,mCAAU,CAAE,CAAC,CACvB,CAAC,CACT,CAAC,cACNhG,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA/B,QAAA,cACjB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,SAAS,CACdmE,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAAkF,QAAA,cAE9C7D,IAAA,CAAC9B,KAAK,EAAC8H,WAAW,CAAC,gCAAO,CAAE,CAAC,CACpB,CAAC,CACT,CAAC,cACNhG,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA/B,QAAA,cAClB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,aAAa,CAClBmE,KAAK,CAAC,oBAAK,CACXC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,QAAS,CAAC,CAAE,CAAAkF,QAAA,cAE/C/D,KAAA,CAAC3B,MAAM,EAAC6H,WAAW,CAAC,sCAAQ,CAAAnC,QAAA,eAC1B7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,gBAAM,CAAArC,QAAA,CAAC,gBAAI,CAAe,CAAC,cAChD7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,gBAAM,CAAArC,QAAA,CAAC,gBAAI,CAAe,CAAC,cAChD7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,0BAAM,CAAArC,QAAA,CAAC,0BAAI,CAAe,CAAC,EAC1C,CAAC,CACA,CAAC,CACT,CAAC,cACN7D,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA/B,QAAA,cAClB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,QAAQ,CACbmE,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAAkF,QAAA,cAE9C/D,KAAA,CAAC3B,MAAM,EAAC6H,WAAW,CAAC,gCAAO,CAAAnC,QAAA,eACzB7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,OAAO,CAAArC,QAAA,CAAC,cAAE,CAAe,CAAC,cAC/C7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,QAAQ,CAAArC,QAAA,CAAC,cAAE,CAAe,CAAC,cAChD7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,UAAU,CAAArC,QAAA,CAAC,cAAE,CAAe,CAAC,EAC5C,CAAC,CACA,CAAC,CACT,CAAC,cACN7D,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAAA9B,QAAA,cACV7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EAACxD,IAAI,CAAC,aAAa,CAACmE,KAAK,CAAC,cAAI,CAAAhC,QAAA,cACtC7D,IAAA,CAACG,QAAQ,EAACgG,IAAI,CAAE,CAAE,CAACH,WAAW,CAAC,mCAAU,CAAE,CAAC,CACnC,CAAC,CACT,CAAC,EACH,CAAC,CACF,CAAC,EA9DgD,OA+DhD,CAAC,cAEVlG,KAAA,CAACM,OAAO,EAACoF,GAAG,CAAC,0BAAM,CAAA3B,QAAA,eACjB/D,KAAA,CAACxB,GAAG,EAAC6G,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACR,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,eACtE7D,IAAA,CAACzB,GAAG,EAAAsF,QAAA,cACF7D,IAAA,CAACE,IAAI,EAACkG,MAAM,MAAAvC,QAAA,CAAC,0BAAI,CAAM,CAAC,CACrB,CAAC,cACN7D,IAAA,CAACzB,GAAG,EAAAsF,QAAA,cACF7D,IAAA,CAAC5B,MAAM,EACLkG,IAAI,CAAC,SAAS,CACdC,IAAI,cAAEvE,IAAA,CAACd,YAAY,GAAE,CAAE,CACvBsF,OAAO,CAAEnC,iBAAkB,CAAAwB,QAAA,CAC5B,0BAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cAEN7D,IAAA,CAACxB,KAAK,EACJ6H,OAAO,CAAE/C,eAAgB,CACzBgD,UAAU,CAAE5D,aAAc,CAC1B6D,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,KAAM,CACnB,CAAC,GAtBoB,WAuBf,CAAC,EACN,CAAC,EACH,CAAC,cAGP1G,IAAA,CAACvB,KAAK,EACJ8E,KAAK,CAAEtC,eAAe,CAAG,MAAM,CAAG,MAAO,CACzC0F,IAAI,CAAE5F,oBAAqB,CAC3B6F,IAAI,CAAEnE,qBAAsB,CAC5BoE,QAAQ,CAAEA,CAAA,GAAM7F,uBAAuB,CAAC,KAAK,CAAE,CAC/C0C,KAAK,CAAE,GAAI,CAAAG,QAAA,cAEX7D,IAAA,CAAC/B,IAAI,EAAC4C,IAAI,CAAEM,YAAa,CAACsE,MAAM,CAAC,UAAU,CAAA5B,QAAA,cACzC/D,KAAA,CAACxB,GAAG,EAACoH,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAA7B,QAAA,eACpB7D,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA/B,QAAA,cAClB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAkF,QAAA,cAEhD7D,IAAA,CAAC9B,KAAK,EAAC8H,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNhG,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA/B,QAAA,cAClB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAkF,QAAA,cAEhD7D,IAAA,CAAC9B,KAAK,EAAC8H,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNhG,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA/B,QAAA,cAClB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,eAAe,CACpBmE,KAAK,CAAC,0BAAM,CAAAhC,QAAA,cAEZ7D,IAAA,CAAC9B,KAAK,EAAC8H,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNhG,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA/B,QAAA,cAClB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,UAAU,CACfmE,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAAkF,QAAA,cAE9C7D,IAAA,CAACtB,WAAW,EAACoI,GAAG,CAAE,CAAE,CAAClC,KAAK,CAAE,CAAElB,KAAK,CAAE,MAAO,CAAE,CAACsC,WAAW,CAAC,gCAAO,CAAE,CAAC,CAC5D,CAAC,CACT,CAAC,cACNhG,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA/B,QAAA,cAClB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,MAAM,CACXmE,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpH,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAAkF,QAAA,cAE9C/D,KAAA,CAAC3B,MAAM,EAAC6H,WAAW,CAAC,gCAAO,CAAAnC,QAAA,eACzB7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,KAAK,CAAArC,QAAA,CAAC,KAAG,CAAe,CAAC,cAC9C7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,IAAI,CAAArC,QAAA,CAAC,IAAE,CAAe,CAAC,cAC5C7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,GAAG,CAAArC,QAAA,CAAC,GAAC,CAAe,CAAC,cAC1C7D,IAAA,CAAC7B,MAAM,CAAC8H,MAAM,EAACC,KAAK,CAAC,KAAK,CAAArC,QAAA,CAAC,KAAG,CAAe,CAAC,EACxC,CAAC,CACA,CAAC,CACT,CAAC,cACN7D,IAAA,CAACzB,GAAG,EAACoH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA/B,QAAA,cAClB7D,IAAA,CAAC/B,IAAI,CAACiH,IAAI,EACRxD,IAAI,CAAC,WAAW,CAChBmE,KAAK,CAAC,cAAI,CAAAhC,QAAA,cAEV7D,IAAA,CAACtB,WAAW,EACVoI,GAAG,CAAE,CAAE,CACPC,SAAS,CAAE,CAAE,CACbnC,KAAK,CAAE,CAAElB,KAAK,CAAE,MAAO,CAAE,CACzBsC,WAAW,CAAC,gCAAO,CACpB,CAAC,CACO,CAAC,CACT,CAAC,EACH,CAAC,CACF,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3F,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}