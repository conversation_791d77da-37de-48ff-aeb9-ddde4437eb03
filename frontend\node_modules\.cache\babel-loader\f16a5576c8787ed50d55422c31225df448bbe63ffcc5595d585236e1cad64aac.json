{"ast": null, "code": "// 错误处理组件\nexport{default as ErrorBoundary}from'./ErrorBoundary';// 加载组件\nexport{default as LoadingSpinner}from'./LoadingSpinner';// 对话框组件\nexport{default as ConfirmDialog}from'./ConfirmDialog';", "map": {"version": 3, "names": ["default", "Error<PERSON>ou<PERSON><PERSON>", "LoadingSpinner", "ConfirmDialog"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/index.ts"], "sourcesContent": ["// 错误处理组件\nexport { default as ErrorBoundary } from './ErrorBoundary';\n\n// 加载组件\nexport { default as LoadingSpinner } from './LoadingSpinner';\n\n// 对话框组件\nexport { default as ConfirmDialog } from './ConfirmDialog';"], "mappings": "AAAA;AACA,OAASA,OAAO,GAAI,CAAAC,aAAa,KAAQ,iBAAiB,CAE1D;AACA,OAASD,OAAO,GAAI,CAAAE,cAAc,KAAQ,kBAAkB,CAE5D;AACA,OAASF,OAAO,GAAI,CAAAG,aAAa,KAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}