import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Table,
  Space,
  Descriptions,
  Tag,
  Divider,
  Row,
  Col,
  Statistic,
  message,
  Spin,
  Tooltip,
  Modal,
  Progress,
  Select,
  Checkbox
} from 'antd';
import * as XLSX from 'xlsx';
import {
  ArrowLeftOutlined,
  EditOutlined,
  PrinterOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  StopOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface BOMItem {
  key: string;
  materialCode: string;
  materialName: string;
  specification: string;
  unit: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  supplier: string;
  deliveryDays: number;
  remark?: string;
  level: number;
  parentKey?: string;
  status: 'available' | 'shortage' | 'pending';
}

interface OrderBOMData {
  id: string;
  bomCode: string;
  bomName: string;
  version: string;
  productCode: string;
  productName: string;
  customerCode: string;
  customerName: string;
  orderNumber: string;
  orderQuantity: number;
  deliveryDate: string;
  priority: 'high' | 'medium' | 'low';
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'in_production' | 'completed';
  description?: string;
  items: BOMItem[];
  totalAmount: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  approvedBy?: string;
  approvedAt?: string;
}

const OrderBOMViewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [bomData, setBomData] = useState<OrderBOMData | null>(null);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');
  const [exportFields, setExportFields] = useState<string[]>(['materialCode', 'materialName', 'specification', 'unit', 'quantity', 'unitPrice', 'totalPrice', 'supplier']);

  // 模拟数据加载
  useEffect(() => {
    const loadBOMData = async () => {
      setLoading(true);
      try {
        // TODO: 从API获取订单BOM数据
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟数据
        const mockData: OrderBOMData = {
          id: id || '1',
          bomCode: 'BOM202401001',
          bomName: '智能控制器订单BOM',
          version: '1.0',
          productCode: 'P001',
          productName: '智能控制器 V1.0',
          customerCode: 'C001',
          customerName: '华为技术有限公司',
          orderNumber: 'ORD202401001',
          orderQuantity: 100,
          deliveryDate: '2024-02-15',
          priority: 'high',
          status: 'approved',
          description: '华为智能控制器项目专用BOM，包含主控芯片、传感器模块等核心组件',
          totalAmount: 4580.00,
          createdBy: '张三',
          createdAt: '2024-01-15 10:30:00',
          updatedAt: '2024-01-16 14:20:00',
          approvedBy: '李四',
          approvedAt: '2024-01-16 16:45:00',
          items: [
            {
              key: '1',
              materialCode: 'M003',
              materialName: '集成电路 STM32',
              specification: 'STM32F407VGT6',
              unit: '片',
              quantity: 1,
              unitPrice: 25.00,
              totalPrice: 25.00,
              supplier: '意法半导体',
              deliveryDays: 7,
              remark: '主控芯片',
              level: 1,
              status: 'available'
            },
            {
              key: '2',
              materialCode: 'M004',
              materialName: 'PCB板 主板',
              specification: '4层板 100x80mm',
              unit: '块',
              quantity: 1,
              unitPrice: 15.80,
              totalPrice: 15.80,
              supplier: '深圳PCB厂',
              deliveryDays: 5,
              remark: '主控板',
              level: 1,
              status: 'available'
            },
            {
              key: '3',
              materialCode: 'M001',
              materialName: '电阻器 10KΩ',
              specification: '0603 1% 1/10W',
              unit: '个',
              quantity: 10,
              unitPrice: 0.50,
              totalPrice: 5.00,
              supplier: '国巨电子',
              deliveryDays: 3,
              remark: '上拉电阻',
              level: 2,
              parentKey: '2',
              status: 'available'
            },
            {
              key: '4',
              materialCode: 'M002',
              materialName: '电容器 100μF',
              specification: '25V 电解电容',
              unit: '个',
              quantity: 5,
              unitPrice: 1.20,
              totalPrice: 6.00,
              supplier: '松下电器',
              deliveryDays: 4,
              remark: '滤波电容',
              level: 2,
              parentKey: '2',
              status: 'shortage'
            },
            {
              key: '5',
              materialCode: 'M005',
              materialName: '外壳 塑料',
              specification: 'ABS 黑色',
              unit: '个',
              quantity: 1,
              unitPrice: 8.50,
              totalPrice: 8.50,
              supplier: '塑料制品厂',
              deliveryDays: 10,
              remark: '产品外壳',
              level: 1,
              status: 'pending'
            }
          ]
        };
        
        setBomData(mockData);
      } catch (error) {
        message.error('加载BOM数据失败');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadBOMData();
    }
  }, [id]);

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      draft: { color: 'default', text: '草稿', icon: <EditOutlined /> },
      pending: { color: 'processing', text: '待审核', icon: <ClockCircleOutlined /> },
      approved: { color: 'success', text: '已审核', icon: <CheckCircleOutlined /> },
      rejected: { color: 'error', text: '已拒绝', icon: <StopOutlined /> },
      in_production: { color: 'warning', text: '生产中', icon: <ExclamationCircleOutlined /> },
      completed: { color: 'success', text: '已完成', icon: <CheckCircleOutlined /> }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 优先级标签渲染
  const renderPriorityTag = (priority: string) => {
    const priorityConfig = {
      high: { color: 'red', text: '高' },
      medium: { color: 'orange', text: '中' },
      low: { color: 'green', text: '低' }
    };
    
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 物料状态标签渲染
  const renderMaterialStatusTag = (status: string) => {
    const statusConfig = {
      available: { color: 'success', text: '可用' },
      shortage: { color: 'error', text: '缺料' },
      pending: { color: 'warning', text: '待采购' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.available;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns: ColumnsType<BOMItem> = [
    {
      title: '层级',
      dataIndex: 'level',
      width: 60,
      render: (level: number) => (
        <Tag color={level === 1 ? 'blue' : level === 2 ? 'green' : 'orange'}>
          L{level}
        </Tag>
      )
    },
    {
      title: '物料编码',
      dataIndex: 'materialCode',
      width: 120,
      render: (text: string, record: BOMItem) => (
        <div style={{ paddingLeft: (record.level - 1) * 20 }}>
          <Text strong>{text}</Text>
        </div>
      )
    },
    {
      title: '物料名称',
      dataIndex: 'materialName',
      width: 200
    },
    {
      title: '规格型号',
      dataIndex: 'specification',
      width: 150
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 80
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      width: 100,
      render: (value: number) => value.toFixed(2)
    },
    {
      title: '单价(元)',
      dataIndex: 'unitPrice',
      width: 100,
      render: (value: number) => `¥${value.toFixed(2)}`
    },
    {
      title: '总价(元)',
      dataIndex: 'totalPrice',
      width: 120,
      render: (value: number) => (
        <Text strong style={{ color: '#1890ff' }}>
          ¥{value.toFixed(2)}
        </Text>
      )
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      width: 120
    },
    {
      title: '交期(天)',
      dataIndex: 'deliveryDays',
      width: 100
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status: string) => renderMaterialStatusTag(status)
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
      ellipsis: true
    }
  ];

  // 处理编辑
  const handleEdit = () => {
    navigate(`/bom/order-bom-edit/${id}`);
  };

  // 处理复制
  const handleCopy = () => {
    Modal.confirm({
      title: '确认复制',
      content: '确定要复制这个订单BOM吗？',
      onOk: () => {
        // TODO: 实现复制功能
        message.success('BOM复制成功');
        navigate('/bom/order-bom-create');
      }
    });
  };

  // 处理打印
  const handlePrint = () => {
    window.print();
  };

  // 处理导出
  const handleExport = () => {
    setExportModalVisible(true);
  };

  const executeExport = () => {
    if (!bomData) return;
    
    try {
      // 准备导出数据
      const exportData = bomData.items.map(item => {
        const data: any = {};
        
        if (exportFields.includes('materialCode')) data['物料编码'] = item.materialCode;
        if (exportFields.includes('materialName')) data['物料名称'] = item.materialName;
        if (exportFields.includes('specification')) data['规格型号'] = item.specification;
        if (exportFields.includes('unit')) data['单位'] = item.unit;
        if (exportFields.includes('quantity')) data['数量'] = item.quantity;
        if (exportFields.includes('unitPrice')) data['单价'] = item.unitPrice;
        if (exportFields.includes('totalPrice')) data['总价'] = item.totalPrice;
        if (exportFields.includes('supplier')) data['供应商'] = item.supplier;
        if (exportFields.includes('deliveryDays')) data['交期(天)'] = item.deliveryDays;
        if (exportFields.includes('status')) data['状态'] = item.status === 'available' ? '可用' : item.status === 'shortage' ? '缺料' : '待采购';
        if (exportFields.includes('remark')) data['备注'] = item.remark || '';
        
        return data;
      });

      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'BOM明细');

      // 下载文件
      const fileName = `${bomData.bomCode}_BOM明细_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;
      XLSX.writeFile(wb, fileName);
      
      message.success('导出成功');
      setExportModalVisible(false);
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 处理分享
  const handleShare = () => {
    // TODO: 实现分享功能
    message.info('分享功能开发中...');
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载中...</p>
      </div>
    );
  }

  if (!bomData) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Text type="secondary">未找到订单BOM数据</Text>
      </div>
    );
  }

  // 计算统计数据
  const totalItems = bomData.items.length;
  const availableItems = bomData.items.filter(item => item.status === 'available').length;
  const shortageItems = bomData.items.filter(item => item.status === 'shortage').length;
  const pendingItems = bomData.items.filter(item => item.status === 'pending').length;

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/bom/order-bom-list')}
              style={{ marginRight: 16 }}
            >
              返回列表
            </Button>
            <Title level={4} style={{ margin: 0 }}>查看订单BOM</Title>
          </div>
          <Space>
            <Tooltip title="编辑">
              <Button
                icon={<EditOutlined />}
                onClick={handleEdit}
                disabled={bomData.status === 'completed'}
              >
                编辑
              </Button>
            </Tooltip>
            <Tooltip title="复制">
              <Button
                icon={<CopyOutlined />}
                onClick={handleCopy}
              >
                复制
              </Button>
            </Tooltip>
            <Tooltip title="打印">
              <Button
                icon={<PrinterOutlined />}
                onClick={handlePrint}
              >
                打印
              </Button>
            </Tooltip>
            <Tooltip title="导出">
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
            </Tooltip>
            <Tooltip title="分享">
              <Button
                icon={<ShareAltOutlined />}
                onClick={handleShare}
              >
                分享
              </Button>
            </Tooltip>
          </Space>
        </div>

        {/* 基本信息 */}
        <Descriptions
          title="基本信息"
          bordered
          column={3}
          size="small"
          style={{ marginBottom: 24 }}
        >
          <Descriptions.Item label="BOM编码">
            <Text strong>{bomData.bomCode}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="BOM名称">
            {bomData.bomName}
          </Descriptions.Item>
          <Descriptions.Item label="版本号">
            <Tag color="blue">{bomData.version}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="产品编码">
            {bomData.productCode}
          </Descriptions.Item>
          <Descriptions.Item label="产品名称">
            {bomData.productName}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            {renderStatusTag(bomData.status)}
          </Descriptions.Item>
          <Descriptions.Item label="客户编码">
            {bomData.customerCode}
          </Descriptions.Item>
          <Descriptions.Item label="客户名称">
            {bomData.customerName}
          </Descriptions.Item>
          <Descriptions.Item label="优先级">
            {renderPriorityTag(bomData.priority)}
          </Descriptions.Item>
          <Descriptions.Item label="订单号">
            <Text strong>{bomData.orderNumber}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="订单数量">
            <Text strong>{bomData.orderQuantity}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="交付日期">
            <Text strong style={{ color: '#1890ff' }}>
              {dayjs(bomData.deliveryDate).format('YYYY-MM-DD')}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="创建人">
            {bomData.createdBy}
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {bomData.createdAt}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {bomData.updatedAt}
          </Descriptions.Item>
          {bomData.approvedBy && (
            <>
              <Descriptions.Item label="审核人">
                {bomData.approvedBy}
              </Descriptions.Item>
              <Descriptions.Item label="审核时间">
                {bomData.approvedAt}
              </Descriptions.Item>
            </>
          )}
          <Descriptions.Item label="描述" span={bomData.approvedBy ? 1 : 3}>
            {bomData.description || '-'}
          </Descriptions.Item>
        </Descriptions>

        {/* 统计信息 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic
              title="总金额"
              value={bomData.totalAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="物料总数"
              value={totalItems}
              suffix="项"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="可用物料"
              value={availableItems}
              suffix="项"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="缺料数量"
              value={shortageItems}
              suffix="项"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Col>
        </Row>

        {/* 物料可用性进度 */}
        <div style={{ marginBottom: 24 }}>
          <Text strong>物料可用性: </Text>
          <Progress
            percent={Math.round((availableItems / totalItems) * 100)}
            status={shortageItems > 0 ? 'exception' : 'success'}
            format={(percent) => `${availableItems}/${totalItems} (${percent}%)`}
            style={{ width: 300, marginLeft: 16 }}
          />
        </div>

        <Divider>BOM明细</Divider>

        <Table
          columns={columns}
          dataSource={bomData.items}
          pagination={false}
          scroll={{ x: 1400 }}
          size="small"
          bordered
          expandable={{
            expandedRowKeys,
            onExpandedRowsChange: (keys) => setExpandedRowKeys(keys as React.Key[]),
            childrenColumnName: 'children',
            indentSize: 20
          }}
        />

        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Text strong style={{ fontSize: 16, color: '#1890ff' }}>
            总金额: ¥{bomData.totalAmount.toFixed(2)}
          </Text>
        </div>
      </Card>

      {/* 导出模态框 */}
      <Modal
        title="导出BOM明细"
        open={exportModalVisible}
        onOk={executeExport}
        onCancel={() => setExportModalVisible(false)}
        okText="导出"
        cancelText="取消"
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ display: 'block', marginBottom: 8 }}>导出格式：</label>
          <Select
            value={exportFormat}
            onChange={setExportFormat}
            style={{ width: '100%' }}
          >
            <Select.Option value="excel">Excel (.xlsx)</Select.Option>
            <Select.Option value="csv">CSV (.csv)</Select.Option>
          </Select>
        </div>
        
        <div>
          <label style={{ display: 'block', marginBottom: 8 }}>导出字段：</label>
          <Checkbox.Group
            value={exportFields}
            onChange={setExportFields}
            style={{ width: '100%' }}
          >
            <Row>
              <Col span={12}>
                <Checkbox value="materialCode">物料编码</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="materialName">物料名称</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="specification">规格型号</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="unit">单位</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="quantity">数量</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="unitPrice">单价</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="totalPrice">总价</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="supplier">供应商</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="deliveryDays">交期</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="status">状态</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="remark">备注</Checkbox>
              </Col>
            </Row>
          </Checkbox.Group>
        </div>
      </Modal>
    </div>
  );
};

export default OrderBOMViewPage;
