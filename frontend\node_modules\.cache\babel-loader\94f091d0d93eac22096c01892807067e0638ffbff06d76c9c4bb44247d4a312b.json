{"ast": null, "code": "import React,{useEffect}from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{Provider}from'react-redux';import{ConfigProvider,App as AntdApp}from'antd';import zhCN from'antd/locale/zh_CN';import dayjs from'dayjs';import'dayjs/locale/zh-cn';import{store}from'./store';import{useAppDispatch,useAppSelector}from'./hooks/redux';import{initializeAuth}from'./store/slices/authSlice';import{ROUTES,THEME_CONFIG}from'./constants';// 页面组件\nimport LoginPage from'./pages/auth/LoginPage';import DashboardPage from'./pages/dashboard/DashboardPage';import MainLayout from'./components/layout/MainLayout';import ProtectedRoute from'./components/auth/ProtectedRoute';import ErrorBoundary from'./components/ErrorBoundary';// BOM管理页面\nimport CoreBOMListPage from'./pages/bom/CoreBOMListPage';import CoreBOMCreatePage from'./pages/bom/CoreBOMCreatePage';import CoreBOMEditPage from'./pages/bom/CoreBOMEditPage';import CoreBOMViewPage from'./pages/bom/CoreBOMViewPage';import OrderBOMListPage from'./pages/bom/OrderBOMListPage';import OrderBOMCreatePage from'./pages/bom/OrderBOMCreatePage';import OrderBOMDerivePage from'./pages/bom/OrderBOMDerivePage';import OrderBOMViewPage from'./pages/bom/OrderBOMViewPage';// 物料管理页面\nimport MaterialListPage from'./pages/material/MaterialListPage';import MaterialCreatePage from'./pages/material/MaterialCreatePage';import MaterialEditPage from'./pages/material/MaterialEditPage';// 库存管理页面\nimport InventoryListPage from'./pages/inventory/InventoryListPage';import InventoryReceivePage from'./pages/inventory/InventoryReceivePage';import InventoryIssuePage from'./pages/inventory/InventoryIssuePage';import InventoryAdjustPage from'./pages/inventory/InventoryAdjustPage';import RemnantListPage from'./pages/inventory/RemnantListPage';import CuttingPlanPage from'./pages/inventory/CuttingPlanPage';import BatchTrackingPage from'./pages/inventory/BatchTrackingPage';// 采购管理页面\nimport PurchaseListPage from'./pages/purchase/PurchaseListPage';import PurchaseRequisitionPage from'./pages/purchase/PurchaseRequisitionPage';import MRPCalculationPage from'./pages/purchase/MRPCalculationPage';import PurchaseOptimizationPage from'./pages/purchase/PurchaseOptimizationPage';// 成本管理页面\nimport CostAnalysisPage from'./pages/cost/CostAnalysisPage';import CostReportsPage from'./pages/cost/CostReportsPage';import WasteTrackingPage from'./pages/cost/WasteTrackingPage';import StandardCostPage from'./pages/cost/StandardCostPage';// 服务管理页面\nimport ServiceBOMListPage from'./pages/service/ServiceBOMListPage';import DeviceArchivePage from'./pages/service/DeviceArchivePage';import MaintenancePage from'./pages/service/MaintenancePage';import AsBuiltBOMPage from'./pages/service/AsBuiltBOMPage';import SparePartsPage from'./pages/service/SparePartsPage';// ECN管理页面\nimport ECNListPage from'./pages/ecn/ECNListPage';import ECNCreatePage from'./pages/ecn/ECNCreatePage';import ECNReviewPage from'./pages/ecn/ECNReviewPage';// 报告页面\nimport ReportsPage from'./pages/reports/ReportsPage';import DashboardConfigPage from'./pages/reports/DashboardConfigPage';// 系统管理页面\nimport UserListPage from'./pages/system/UserListPage';import RoleListPage from'./pages/system/RoleListPage';import PermissionListPage from'./pages/system/PermissionListPage';import SystemConfigPage from'./pages/system/SystemConfigPage';import AuditLogPage from'./pages/system/AuditLogPage';// 移动端页面\nimport MobilePage from'./pages/mobile/MobilePage';import MobileScanPage from'./pages/mobile/MobileScanPage';import MobileInventoryPage from'./pages/mobile/MobileInventoryPage';// 设置dayjs中文\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";dayjs.locale('zh-cn');const AppContent=()=>{const dispatch=useAppDispatch();const{isAuthenticated}=useAppSelector(state=>state.auth);useEffect(()=>{dispatch(initializeAuth());},[dispatch]);return/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:ROUTES.LOGIN,element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsxs(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(MainLayout,{})}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:ROUTES.DASHBOARD,replace:true})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.DASHBOARD,element:/*#__PURE__*/_jsx(DashboardPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.CORE_BOM,element:/*#__PURE__*/_jsx(CoreBOMListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.CORE_BOM_CREATE,element:/*#__PURE__*/_jsx(CoreBOMCreatePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.CORE_BOM_EDIT,element:/*#__PURE__*/_jsx(CoreBOMEditPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.CORE_BOM_VIEW,element:/*#__PURE__*/_jsx(CoreBOMViewPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.ORDER_BOM,element:/*#__PURE__*/_jsx(OrderBOMListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.ORDER_BOM_CREATE,element:/*#__PURE__*/_jsx(OrderBOMCreatePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.ORDER_BOM_DERIVE,element:/*#__PURE__*/_jsx(OrderBOMDerivePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.ORDER_BOM_VIEW,element:/*#__PURE__*/_jsx(OrderBOMViewPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.MATERIALS,element:/*#__PURE__*/_jsx(MaterialListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.MATERIALS_CREATE,element:/*#__PURE__*/_jsx(MaterialCreatePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.MATERIALS_EDIT,element:/*#__PURE__*/_jsx(MaterialEditPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.INVENTORY,element:/*#__PURE__*/_jsx(InventoryListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.INVENTORY_RECEIVE,element:/*#__PURE__*/_jsx(InventoryReceivePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.INVENTORY_ISSUE,element:/*#__PURE__*/_jsx(InventoryIssuePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.INVENTORY_ADJUST,element:/*#__PURE__*/_jsx(InventoryAdjustPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.REMNANTS,element:/*#__PURE__*/_jsx(RemnantListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.CUTTING_PLAN,element:/*#__PURE__*/_jsx(CuttingPlanPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.BATCH_TRACKING,element:/*#__PURE__*/_jsx(BatchTrackingPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.PURCHASE,element:/*#__PURE__*/_jsx(PurchaseListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.PURCHASE_REQUISITION,element:/*#__PURE__*/_jsx(PurchaseRequisitionPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.MRP_CALCULATION,element:/*#__PURE__*/_jsx(MRPCalculationPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.PURCHASE_OPTIMIZATION,element:/*#__PURE__*/_jsx(PurchaseOptimizationPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.COST_ANALYSIS,element:/*#__PURE__*/_jsx(CostAnalysisPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.COST_REPORTS,element:/*#__PURE__*/_jsx(CostReportsPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.WASTE_TRACKING,element:/*#__PURE__*/_jsx(WasteTrackingPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.STANDARD_COST,element:/*#__PURE__*/_jsx(StandardCostPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.SERVICE_BOM,element:/*#__PURE__*/_jsx(ServiceBOMListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.DEVICE_ARCHIVE,element:/*#__PURE__*/_jsx(DeviceArchivePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.MAINTENANCE,element:/*#__PURE__*/_jsx(MaintenancePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.AS_BUILT_BOM,element:/*#__PURE__*/_jsx(AsBuiltBOMPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.SPARE_PARTS,element:/*#__PURE__*/_jsx(SparePartsPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.ECN,element:/*#__PURE__*/_jsx(ECNListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.ECN_CREATE,element:/*#__PURE__*/_jsx(ECNCreatePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.ECN_REVIEW,element:/*#__PURE__*/_jsx(ECNReviewPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.REPORTS,element:/*#__PURE__*/_jsx(ReportsPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.DASHBOARD_CONFIG,element:/*#__PURE__*/_jsx(DashboardConfigPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.USERS,element:/*#__PURE__*/_jsx(UserListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.ROLES,element:/*#__PURE__*/_jsx(RoleListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.PERMISSIONS,element:/*#__PURE__*/_jsx(PermissionListPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.SYSTEM_CONFIG,element:/*#__PURE__*/_jsx(SystemConfigPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.AUDIT_LOG,element:/*#__PURE__*/_jsx(AuditLogPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.MOBILE,element:/*#__PURE__*/_jsx(MobilePage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.MOBILE_SCAN,element:/*#__PURE__*/_jsx(MobileScanPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.MOBILE_INVENTORY,element:/*#__PURE__*/_jsx(MobileInventoryPage,{})})]}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:ROUTES.DASHBOARD,replace:true})})]});};const App=()=>{return/*#__PURE__*/_jsx(Provider,{store:store,children:/*#__PURE__*/_jsx(ConfigProvider,{locale:zhCN,theme:{token:{colorPrimary:THEME_CONFIG.primaryColor,borderRadius:THEME_CONFIG.borderRadius}},children:/*#__PURE__*/_jsx(AntdApp,{children:/*#__PURE__*/_jsx(ErrorBoundary,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(AppContent,{})})})})})});};export default App;", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "App", "AntdApp", "zhCN", "dayjs", "store", "useAppDispatch", "useAppSelector", "initializeAuth", "ROUTES", "THEME_CONFIG", "LoginPage", "DashboardPage", "MainLayout", "ProtectedRoute", "Error<PERSON>ou<PERSON><PERSON>", "CoreBOMListPage", "CoreBOMCreatePage", "CoreBOMEditPage", "CoreBOMViewPage", "OrderBOMListPage", "OrderBOMCreatePage", "OrderBOMDerivePage", "OrderBOMViewPage", "MaterialListPage", "MaterialCreatePage", "MaterialEditPage", "InventoryListPage", "InventoryReceivePage", "InventoryIssuePage", "InventoryAdjustPage", "RemnantListPage", "CuttingPlanPage", "BatchTrackingPage", "PurchaseListPage", "PurchaseRequisitionPage", "MRPCalculationPage", "PurchaseOptimizationPage", "CostAnalysisPage", "CostReportsPage", "WasteTrackingPage", "StandardCostPage", "ServiceBOMListPage", "DeviceArchivePage", "MaintenancePage", "AsBuiltBOMPage", "SparePartsPage", "ECNListPage", "ECNCreatePage", "ECNReviewPage", "ReportsPage", "DashboardConfigPage", "UserListPage", "RoleListPage", "PermissionListPage", "SystemConfigPage", "AuditLogPage", "MobilePage", "MobileScanPage", "MobileInventoryPage", "jsx", "_jsx", "jsxs", "_jsxs", "locale", "A<PERSON><PERSON><PERSON>nt", "dispatch", "isAuthenticated", "state", "auth", "children", "path", "LOGIN", "element", "index", "to", "DASHBOARD", "replace", "CORE_BOM", "CORE_BOM_CREATE", "CORE_BOM_EDIT", "CORE_BOM_VIEW", "ORDER_BOM", "ORDER_BOM_CREATE", "ORDER_BOM_DERIVE", "ORDER_BOM_VIEW", "MATERIALS", "MATERIALS_CREATE", "MATERIALS_EDIT", "INVENTORY", "INVENTORY_RECEIVE", "INVENTORY_ISSUE", "INVENTORY_ADJUST", "REMNANTS", "CUTTING_PLAN", "BATCH_TRACKING", "PURCHASE", "PURCHASE_REQUISITION", "MRP_CALCULATION", "PURCHASE_OPTIMIZATION", "COST_ANALYSIS", "COST_REPORTS", "WASTE_TRACKING", "STANDARD_COST", "SERVICE_BOM", "DEVICE_ARCHIVE", "MAINTENANCE", "AS_BUILT_BOM", "SPARE_PARTS", "ECN", "ECN_CREATE", "ECN_REVIEW", "REPORTS", "DASHBOARD_CONFIG", "USERS", "ROLES", "PERMISSIONS", "SYSTEM_CONFIG", "AUDIT_LOG", "MOBILE", "MOBILE_SCAN", "MOBILE_INVENTORY", "theme", "token", "colorPrimary", "primaryColor", "borderRadius"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ConfigProvider, App as AntdApp } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport dayjs from 'dayjs';\nimport 'dayjs/locale/zh-cn';\n\nimport { store } from './store';\nimport { useAppDispatch, useAppSelector } from './hooks/redux';\nimport { initializeAuth } from './store/slices/authSlice';\nimport { ROUTES, THEME_CONFIG } from './constants';\n\n// 页面组件\nimport LoginPage from './pages/auth/LoginPage';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport MainLayout from './components/layout/MainLayout';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport ErrorBoundary from './components/ErrorBoundary';\n\n// BOM管理页面\nimport CoreBOMListPage from './pages/bom/CoreBOMListPage';\nimport CoreBOMCreatePage from './pages/bom/CoreBOMCreatePage';\nimport CoreBOMEditPage from './pages/bom/CoreBOMEditPage';\nimport CoreBOMViewPage from './pages/bom/CoreBOMViewPage';\nimport OrderBOMListPage from './pages/bom/OrderBOMListPage';\nimport OrderBOMCreatePage from './pages/bom/OrderBOMCreatePage';\nimport OrderBOMDerivePage from './pages/bom/OrderBOMDerivePage';\nimport OrderBOMViewPage from './pages/bom/OrderBOMViewPage';\n\n// 物料管理页面\nimport MaterialListPage from './pages/material/MaterialListPage';\nimport MaterialCreatePage from './pages/material/MaterialCreatePage';\nimport MaterialEditPage from './pages/material/MaterialEditPage';\n\n// 库存管理页面\nimport InventoryListPage from './pages/inventory/InventoryListPage';\nimport InventoryReceivePage from './pages/inventory/InventoryReceivePage';\nimport InventoryIssuePage from './pages/inventory/InventoryIssuePage';\nimport InventoryAdjustPage from './pages/inventory/InventoryAdjustPage';\nimport RemnantListPage from './pages/inventory/RemnantListPage';\nimport CuttingPlanPage from './pages/inventory/CuttingPlanPage';\nimport BatchTrackingPage from './pages/inventory/BatchTrackingPage';\n\n// 采购管理页面\nimport PurchaseListPage from './pages/purchase/PurchaseListPage';\nimport PurchaseRequisitionPage from './pages/purchase/PurchaseRequisitionPage';\nimport MRPCalculationPage from './pages/purchase/MRPCalculationPage';\nimport PurchaseOptimizationPage from './pages/purchase/PurchaseOptimizationPage';\n\n// 成本管理页面\nimport CostAnalysisPage from './pages/cost/CostAnalysisPage';\nimport CostReportsPage from './pages/cost/CostReportsPage';\nimport WasteTrackingPage from './pages/cost/WasteTrackingPage';\nimport StandardCostPage from './pages/cost/StandardCostPage';\n\n// 服务管理页面\nimport ServiceBOMListPage from './pages/service/ServiceBOMListPage';\nimport DeviceArchivePage from './pages/service/DeviceArchivePage';\nimport MaintenancePage from './pages/service/MaintenancePage';\nimport AsBuiltBOMPage from './pages/service/AsBuiltBOMPage';\nimport SparePartsPage from './pages/service/SparePartsPage';\n\n// ECN管理页面\nimport ECNListPage from './pages/ecn/ECNListPage';\nimport ECNCreatePage from './pages/ecn/ECNCreatePage';\nimport ECNReviewPage from './pages/ecn/ECNReviewPage';\n\n// 报告页面\nimport ReportsPage from './pages/reports/ReportsPage';\nimport DashboardConfigPage from './pages/reports/DashboardConfigPage';\n\n// 系统管理页面\nimport UserListPage from './pages/system/UserListPage';\nimport RoleListPage from './pages/system/RoleListPage';\nimport PermissionListPage from './pages/system/PermissionListPage';\nimport SystemConfigPage from './pages/system/SystemConfigPage';\nimport AuditLogPage from './pages/system/AuditLogPage';\n\n// 移动端页面\nimport MobilePage from './pages/mobile/MobilePage';\nimport MobileScanPage from './pages/mobile/MobileScanPage';\nimport MobileInventoryPage from './pages/mobile/MobileInventoryPage';\n\n// 设置dayjs中文\ndayjs.locale('zh-cn');\n\nconst AppContent: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { isAuthenticated } = useAppSelector(state => state.auth);\n\n  useEffect(() => {\n    dispatch(initializeAuth());\n  }, [dispatch]);\n\n  return (\n    <Routes>\n      {/* 登录页面 */}\n      <Route path={ROUTES.LOGIN} element={<LoginPage />} />\n      \n      {/* 受保护的路由 */}\n      <Route path=\"/\" element={<ProtectedRoute><MainLayout /></ProtectedRoute>}>\n        {/* 仪表板 */}\n        <Route index element={<Navigate to={ROUTES.DASHBOARD} replace />} />\n        <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />\n        \n        {/* BOM管理 */}\n        <Route path={ROUTES.CORE_BOM} element={<CoreBOMListPage />} />\n        <Route path={ROUTES.CORE_BOM_CREATE} element={<CoreBOMCreatePage />} />\n        <Route path={ROUTES.CORE_BOM_EDIT} element={<CoreBOMEditPage />} />\n        <Route path={ROUTES.CORE_BOM_VIEW} element={<CoreBOMViewPage />} />\n        \n        <Route path={ROUTES.ORDER_BOM} element={<OrderBOMListPage />} />\n        <Route path={ROUTES.ORDER_BOM_CREATE} element={<OrderBOMCreatePage />} />\n        <Route path={ROUTES.ORDER_BOM_DERIVE} element={<OrderBOMDerivePage />} />\n        <Route path={ROUTES.ORDER_BOM_VIEW} element={<OrderBOMViewPage />} />\n        \n        {/* 物料管理 */}\n        <Route path={ROUTES.MATERIALS} element={<MaterialListPage />} />\n        <Route path={ROUTES.MATERIALS_CREATE} element={<MaterialCreatePage />} />\n        <Route path={ROUTES.MATERIALS_EDIT} element={<MaterialEditPage />} />\n        \n        {/* 库存管理 */}\n        <Route path={ROUTES.INVENTORY} element={<InventoryListPage />} />\n        <Route path={ROUTES.INVENTORY_RECEIVE} element={<InventoryReceivePage />} />\n        <Route path={ROUTES.INVENTORY_ISSUE} element={<InventoryIssuePage />} />\n        <Route path={ROUTES.INVENTORY_ADJUST} element={<InventoryAdjustPage />} />\n        <Route path={ROUTES.REMNANTS} element={<RemnantListPage />} />\n        <Route path={ROUTES.CUTTING_PLAN} element={<CuttingPlanPage />} />\n        <Route path={ROUTES.BATCH_TRACKING} element={<BatchTrackingPage />} />\n        \n        {/* 采购管理 */}\n        <Route path={ROUTES.PURCHASE} element={<PurchaseListPage />} />\n        <Route path={ROUTES.PURCHASE_REQUISITION} element={<PurchaseRequisitionPage />} />\n        <Route path={ROUTES.MRP_CALCULATION} element={<MRPCalculationPage />} />\n        <Route path={ROUTES.PURCHASE_OPTIMIZATION} element={<PurchaseOptimizationPage />} />\n        \n        {/* 成本管理 */}\n        <Route path={ROUTES.COST_ANALYSIS} element={<CostAnalysisPage />} />\n        <Route path={ROUTES.COST_REPORTS} element={<CostReportsPage />} />\n        <Route path={ROUTES.WASTE_TRACKING} element={<WasteTrackingPage />} />\n        <Route path={ROUTES.STANDARD_COST} element={<StandardCostPage />} />\n        \n        {/* 服务管理 */}\n        <Route path={ROUTES.SERVICE_BOM} element={<ServiceBOMListPage />} />\n        <Route path={ROUTES.DEVICE_ARCHIVE} element={<DeviceArchivePage />} />\n        <Route path={ROUTES.MAINTENANCE} element={<MaintenancePage />} />\n        <Route path={ROUTES.AS_BUILT_BOM} element={<AsBuiltBOMPage />} />\n            <Route path={ROUTES.SPARE_PARTS} element={<SparePartsPage />} />\n        \n        {/* ECN管理 */}\n        <Route path={ROUTES.ECN} element={<ECNListPage />} />\n        <Route path={ROUTES.ECN_CREATE} element={<ECNCreatePage />} />\n        <Route path={ROUTES.ECN_REVIEW} element={<ECNReviewPage />} />\n        \n        {/* 报告 */}\n        <Route path={ROUTES.REPORTS} element={<ReportsPage />} />\n        <Route path={ROUTES.DASHBOARD_CONFIG} element={<DashboardConfigPage />} />\n        \n        {/* 系统管理 */}\n        <Route path={ROUTES.USERS} element={<UserListPage />} />\n            <Route path={ROUTES.ROLES} element={<RoleListPage />} />\n            <Route path={ROUTES.PERMISSIONS} element={<PermissionListPage />} />\n            <Route path={ROUTES.SYSTEM_CONFIG} element={<SystemConfigPage />} />\n            <Route path={ROUTES.AUDIT_LOG} element={<AuditLogPage />} />\n        \n        {/* 移动端 */}\n        <Route path={ROUTES.MOBILE} element={<MobilePage />} />\n        <Route path={ROUTES.MOBILE_SCAN} element={<MobileScanPage />} />\n        <Route path={ROUTES.MOBILE_INVENTORY} element={<MobileInventoryPage />} />\n      </Route>\n      \n      {/* 404页面 */}\n      <Route path=\"*\" element={<Navigate to={ROUTES.DASHBOARD} replace />} />\n    </Routes>\n  );\n};\n\nconst App: React.FC = () => {\n  return (\n    <Provider store={store}>\n      <ConfigProvider \n        locale={zhCN}\n        theme={{\n          token: {\n            colorPrimary: THEME_CONFIG.primaryColor,\n            borderRadius: THEME_CONFIG.borderRadius,\n          },\n        }}\n      >\n        <AntdApp>\n          <ErrorBoundary>\n            <Router>\n              <AppContent />\n            </Router>\n          </ErrorBoundary>\n        </AntdApp>\n      </ConfigProvider>\n    </Provider>\n  );\n};\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,QAAQ,KAAQ,aAAa,CACtC,OAASC,cAAc,CAAEC,GAAG,GAAI,CAAAC,OAAO,KAAQ,MAAM,CACrD,MAAO,CAAAC,IAAI,KAAM,mBAAmB,CACpC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,oBAAoB,CAE3B,OAASC,KAAK,KAAQ,SAAS,CAC/B,OAASC,cAAc,CAAEC,cAAc,KAAQ,eAAe,CAC9D,OAASC,cAAc,KAAQ,0BAA0B,CACzD,OAASC,MAAM,CAAEC,YAAY,KAAQ,aAAa,CAElD;AACA,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,aAAa,KAAM,iCAAiC,CAC3D,MAAO,CAAAC,UAAU,KAAM,gCAAgC,CACvD,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CAEtD;AACA,MAAO,CAAAC,eAAe,KAAM,6BAA6B,CACzD,MAAO,CAAAC,iBAAiB,KAAM,+BAA+B,CAC7D,MAAO,CAAAC,eAAe,KAAM,6BAA6B,CACzD,MAAO,CAAAC,eAAe,KAAM,6BAA6B,CACzD,MAAO,CAAAC,gBAAgB,KAAM,8BAA8B,CAC3D,MAAO,CAAAC,kBAAkB,KAAM,gCAAgC,CAC/D,MAAO,CAAAC,kBAAkB,KAAM,gCAAgC,CAC/D,MAAO,CAAAC,gBAAgB,KAAM,8BAA8B,CAE3D;AACA,MAAO,CAAAC,gBAAgB,KAAM,mCAAmC,CAChE,MAAO,CAAAC,kBAAkB,KAAM,qCAAqC,CACpE,MAAO,CAAAC,gBAAgB,KAAM,mCAAmC,CAEhE;AACA,MAAO,CAAAC,iBAAiB,KAAM,qCAAqC,CACnE,MAAO,CAAAC,oBAAoB,KAAM,wCAAwC,CACzE,MAAO,CAAAC,kBAAkB,KAAM,sCAAsC,CACrE,MAAO,CAAAC,mBAAmB,KAAM,uCAAuC,CACvE,MAAO,CAAAC,eAAe,KAAM,mCAAmC,CAC/D,MAAO,CAAAC,eAAe,KAAM,mCAAmC,CAC/D,MAAO,CAAAC,iBAAiB,KAAM,qCAAqC,CAEnE;AACA,MAAO,CAAAC,gBAAgB,KAAM,mCAAmC,CAChE,MAAO,CAAAC,uBAAuB,KAAM,0CAA0C,CAC9E,MAAO,CAAAC,kBAAkB,KAAM,qCAAqC,CACpE,MAAO,CAAAC,wBAAwB,KAAM,2CAA2C,CAEhF;AACA,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAE5D;AACA,MAAO,CAAAC,kBAAkB,KAAM,oCAAoC,CACnE,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAE3D;AACA,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,MAAO,CAAAC,aAAa,KAAM,2BAA2B,CACrD,MAAO,CAAAC,aAAa,KAAM,2BAA2B,CAErD;AACA,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,mBAAmB,KAAM,qCAAqC,CAErE;AACA,MAAO,CAAAC,YAAY,KAAM,6BAA6B,CACtD,MAAO,CAAAC,YAAY,KAAM,6BAA6B,CACtD,MAAO,CAAAC,kBAAkB,KAAM,mCAAmC,CAClE,MAAO,CAAAC,gBAAgB,KAAM,iCAAiC,CAC9D,MAAO,CAAAC,YAAY,KAAM,6BAA6B,CAEtD;AACA,MAAO,CAAAC,UAAU,KAAM,2BAA2B,CAClD,MAAO,CAAAC,cAAc,KAAM,+BAA+B,CAC1D,MAAO,CAAAC,mBAAmB,KAAM,oCAAoC,CAEpE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA3D,KAAK,CAAC4D,MAAM,CAAC,OAAO,CAAC,CAErB,KAAM,CAAAC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,QAAQ,CAAG5D,cAAc,CAAC,CAAC,CACjC,KAAM,CAAE6D,eAAgB,CAAC,CAAG5D,cAAc,CAAC6D,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CAE/D5E,SAAS,CAAC,IAAM,CACdyE,QAAQ,CAAC1D,cAAc,CAAC,CAAC,CAAC,CAC5B,CAAC,CAAE,CAAC0D,QAAQ,CAAC,CAAC,CAEd,mBACEH,KAAA,CAACnE,MAAM,EAAA0E,QAAA,eAELT,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC+D,KAAM,CAACC,OAAO,cAAEZ,IAAA,CAAClD,SAAS,GAAE,CAAE,CAAE,CAAC,cAGrDoD,KAAA,CAAClE,KAAK,EAAC0E,IAAI,CAAC,GAAG,CAACE,OAAO,cAAEZ,IAAA,CAAC/C,cAAc,EAAAwD,QAAA,cAACT,IAAA,CAAChD,UAAU,GAAE,CAAC,CAAgB,CAAE,CAAAyD,QAAA,eAEvET,IAAA,CAAChE,KAAK,EAAC6E,KAAK,MAACD,OAAO,cAAEZ,IAAA,CAAC/D,QAAQ,EAAC6E,EAAE,CAAElE,MAAM,CAACmE,SAAU,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACpEhB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACmE,SAAU,CAACH,OAAO,cAAEZ,IAAA,CAACjD,aAAa,GAAE,CAAE,CAAE,CAAC,cAG7DiD,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACqE,QAAS,CAACL,OAAO,cAAEZ,IAAA,CAAC7C,eAAe,GAAE,CAAE,CAAE,CAAC,cAC9D6C,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACsE,eAAgB,CAACN,OAAO,cAAEZ,IAAA,CAAC5C,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACvE4C,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACuE,aAAc,CAACP,OAAO,cAAEZ,IAAA,CAAC3C,eAAe,GAAE,CAAE,CAAE,CAAC,cACnE2C,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACwE,aAAc,CAACR,OAAO,cAAEZ,IAAA,CAAC1C,eAAe,GAAE,CAAE,CAAE,CAAC,cAEnE0C,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACyE,SAAU,CAACT,OAAO,cAAEZ,IAAA,CAACzC,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAChEyC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC0E,gBAAiB,CAACV,OAAO,cAAEZ,IAAA,CAACxC,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACzEwC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC2E,gBAAiB,CAACX,OAAO,cAAEZ,IAAA,CAACvC,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACzEuC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC4E,cAAe,CAACZ,OAAO,cAAEZ,IAAA,CAACtC,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAGrEsC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC6E,SAAU,CAACb,OAAO,cAAEZ,IAAA,CAACrC,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAChEqC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC8E,gBAAiB,CAACd,OAAO,cAAEZ,IAAA,CAACpC,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACzEoC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC+E,cAAe,CAACf,OAAO,cAAEZ,IAAA,CAACnC,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAGrEmC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACgF,SAAU,CAAChB,OAAO,cAAEZ,IAAA,CAAClC,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACjEkC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACiF,iBAAkB,CAACjB,OAAO,cAAEZ,IAAA,CAACjC,oBAAoB,GAAE,CAAE,CAAE,CAAC,cAC5EiC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACkF,eAAgB,CAAClB,OAAO,cAAEZ,IAAA,CAAChC,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACxEgC,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACmF,gBAAiB,CAACnB,OAAO,cAAEZ,IAAA,CAAC/B,mBAAmB,GAAE,CAAE,CAAE,CAAC,cAC1E+B,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACoF,QAAS,CAACpB,OAAO,cAAEZ,IAAA,CAAC9B,eAAe,GAAE,CAAE,CAAE,CAAC,cAC9D8B,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACqF,YAAa,CAACrB,OAAO,cAAEZ,IAAA,CAAC7B,eAAe,GAAE,CAAE,CAAE,CAAC,cAClE6B,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACsF,cAAe,CAACtB,OAAO,cAAEZ,IAAA,CAAC5B,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAGtE4B,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACuF,QAAS,CAACvB,OAAO,cAAEZ,IAAA,CAAC3B,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAC/D2B,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACwF,oBAAqB,CAACxB,OAAO,cAAEZ,IAAA,CAAC1B,uBAAuB,GAAE,CAAE,CAAE,CAAC,cAClF0B,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACyF,eAAgB,CAACzB,OAAO,cAAEZ,IAAA,CAACzB,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACxEyB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC0F,qBAAsB,CAAC1B,OAAO,cAAEZ,IAAA,CAACxB,wBAAwB,GAAE,CAAE,CAAE,CAAC,cAGpFwB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC2F,aAAc,CAAC3B,OAAO,cAAEZ,IAAA,CAACvB,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACpEuB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC4F,YAAa,CAAC5B,OAAO,cAAEZ,IAAA,CAACtB,eAAe,GAAE,CAAE,CAAE,CAAC,cAClEsB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC6F,cAAe,CAAC7B,OAAO,cAAEZ,IAAA,CAACrB,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACtEqB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC8F,aAAc,CAAC9B,OAAO,cAAEZ,IAAA,CAACpB,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAGpEoB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC+F,WAAY,CAAC/B,OAAO,cAAEZ,IAAA,CAACnB,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACpEmB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACgG,cAAe,CAAChC,OAAO,cAAEZ,IAAA,CAAClB,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACtEkB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACiG,WAAY,CAACjC,OAAO,cAAEZ,IAAA,CAACjB,eAAe,GAAE,CAAE,CAAE,CAAC,cACjEiB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACkG,YAAa,CAAClC,OAAO,cAAEZ,IAAA,CAAChB,cAAc,GAAE,CAAE,CAAE,CAAC,cAC7DgB,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACmG,WAAY,CAACnC,OAAO,cAAEZ,IAAA,CAACf,cAAc,GAAE,CAAE,CAAE,CAAC,cAGpEe,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACoG,GAAI,CAACpC,OAAO,cAAEZ,IAAA,CAACd,WAAW,GAAE,CAAE,CAAE,CAAC,cACrDc,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACqG,UAAW,CAACrC,OAAO,cAAEZ,IAAA,CAACb,aAAa,GAAE,CAAE,CAAE,CAAC,cAC9Da,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACsG,UAAW,CAACtC,OAAO,cAAEZ,IAAA,CAACZ,aAAa,GAAE,CAAE,CAAE,CAAC,cAG9DY,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACuG,OAAQ,CAACvC,OAAO,cAAEZ,IAAA,CAACX,WAAW,GAAE,CAAE,CAAE,CAAC,cACzDW,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACwG,gBAAiB,CAACxC,OAAO,cAAEZ,IAAA,CAACV,mBAAmB,GAAE,CAAE,CAAE,CAAC,cAG1EU,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACyG,KAAM,CAACzC,OAAO,cAAEZ,IAAA,CAACT,YAAY,GAAE,CAAE,CAAE,CAAC,cACpDS,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC0G,KAAM,CAAC1C,OAAO,cAAEZ,IAAA,CAACR,YAAY,GAAE,CAAE,CAAE,CAAC,cACxDQ,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC2G,WAAY,CAAC3C,OAAO,cAAEZ,IAAA,CAACP,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACpEO,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC4G,aAAc,CAAC5C,OAAO,cAAEZ,IAAA,CAACN,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACpEM,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC6G,SAAU,CAAC7C,OAAO,cAAEZ,IAAA,CAACL,YAAY,GAAE,CAAE,CAAE,CAAC,cAGhEK,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC8G,MAAO,CAAC9C,OAAO,cAAEZ,IAAA,CAACJ,UAAU,GAAE,CAAE,CAAE,CAAC,cACvDI,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAAC+G,WAAY,CAAC/C,OAAO,cAAEZ,IAAA,CAACH,cAAc,GAAE,CAAE,CAAE,CAAC,cAChEG,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAE9D,MAAM,CAACgH,gBAAiB,CAAChD,OAAO,cAAEZ,IAAA,CAACF,mBAAmB,GAAE,CAAE,CAAE,CAAC,EACrE,CAAC,cAGRE,IAAA,CAAChE,KAAK,EAAC0E,IAAI,CAAC,GAAG,CAACE,OAAO,cAAEZ,IAAA,CAAC/D,QAAQ,EAAC6E,EAAE,CAAElE,MAAM,CAACmE,SAAU,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EACjE,CAAC,CAEb,CAAC,CAED,KAAM,CAAA5E,GAAa,CAAGA,CAAA,GAAM,CAC1B,mBACE4D,IAAA,CAAC9D,QAAQ,EAACM,KAAK,CAAEA,KAAM,CAAAiE,QAAA,cACrBT,IAAA,CAAC7D,cAAc,EACbgE,MAAM,CAAE7D,IAAK,CACbuH,KAAK,CAAE,CACLC,KAAK,CAAE,CACLC,YAAY,CAAElH,YAAY,CAACmH,YAAY,CACvCC,YAAY,CAAEpH,YAAY,CAACoH,YAC7B,CACF,CAAE,CAAAxD,QAAA,cAEFT,IAAA,CAAC3D,OAAO,EAAAoE,QAAA,cACNT,IAAA,CAAC9C,aAAa,EAAAuD,QAAA,cACZT,IAAA,CAAClE,MAAM,EAAA2E,QAAA,cACLT,IAAA,CAACI,UAAU,GAAE,CAAC,CACR,CAAC,CACI,CAAC,CACT,CAAC,CACI,CAAC,CACT,CAAC,CAEf,CAAC,CAED,cAAe,CAAAhE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}