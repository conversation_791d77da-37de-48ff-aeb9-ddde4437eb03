{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\bom\\\\CoreBOMEditPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Typography, Form, Input, Select, Button, Space, Row, Col, Table, Modal, InputNumber, message, Breadcrumb, Tabs, Tag, Tooltip } from 'antd';\nimport { SaveOutlined, ArrowLeftOutlined, PlusOutlined, EditOutlined, DeleteOutlined, CopyOutlined, FileTextOutlined, HistoryOutlined } from '@ant-design/icons';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCoreBOM, updateCoreBOM } from '../../store/slices/bomSlice';\nimport { ConfirmDialog } from '../../components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  TabPane\n} = Tabs;\nconst CoreBOMEditPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const {\n    currentBOM,\n    loading\n  } = useAppSelector(state => state.bom);\n  const [form] = Form.useForm();\n  const [materialModalVisible, setMaterialModalVisible] = useState(false);\n  const [editingMaterial, setEditingMaterial] = useState(null);\n  const [materialForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('basic');\n  useEffect(() => {\n    if (id) {\n      dispatch(fetchCoreBOM(id));\n    }\n  }, [id, dispatch]);\n  useEffect(() => {\n    if (currentBOM) {\n      form.setFieldsValue({\n        bomCode: currentBOM.code,\n        bomName: currentBOM.name,\n        version: currentBOM.version,\n        productLine: 'productLine' in currentBOM ? currentBOM.productLine : '5G基站',\n        description: currentBOM.description,\n        status: currentBOM.status\n      });\n    }\n  }, [currentBOM, form]);\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      await dispatch(updateCoreBOM({\n        id: id,\n        data: values\n      }));\n      message.success('BOM保存成功');\n    } catch (error) {\n      message.error('保存失败，请重试');\n    }\n  };\n  const handleAddMaterial = () => {\n    setEditingMaterial(null);\n    materialForm.resetFields();\n    setMaterialModalVisible(true);\n  };\n  const handleEditMaterial = record => {\n    setEditingMaterial(record);\n    materialForm.setFieldsValue(record);\n    setMaterialModalVisible(true);\n  };\n  const handleMaterialModalOk = async () => {\n    try {\n      const values = await materialForm.validateFields();\n      if (editingMaterial) {\n        message.success('物料更新成功');\n      } else {\n        message.success('物料添加成功');\n      }\n      setMaterialModalVisible(false);\n      // TODO: 更新物料列表\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  // 模拟BOM物料数据\n  const mockMaterials = [{\n    id: '1',\n    materialCode: 'ANT-MAIN-001',\n    materialName: '5G主天线单元',\n    specification: 'ANT-5G-001',\n    quantity: 1,\n    unit: 'PCS',\n    unitPrice: 1500,\n    totalPrice: 1500,\n    supplier: '华为技术',\n    leadTime: 14,\n    level: 1,\n    parentId: null\n  }, {\n    id: '2',\n    materialCode: 'RF-AMP-001',\n    materialName: 'RF功率放大器',\n    specification: 'RF-PA-5G-001',\n    quantity: 2,\n    unit: 'PCS',\n    unitPrice: 2500,\n    totalPrice: 5000,\n    supplier: '中兴通讯',\n    leadTime: 21,\n    level: 2,\n    parentId: '1'\n  }];\n  const materialColumns = [{\n    title: '层级',\n    dataIndex: 'level',\n    key: 'level',\n    width: 60,\n    render: level => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: [\"L\", level]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 34\n    }, this)\n  }, {\n    title: '物料编码',\n    dataIndex: 'materialCode',\n    key: 'materialCode',\n    width: 120\n  }, {\n    title: '物料名称',\n    dataIndex: 'materialName',\n    key: 'materialName',\n    ellipsis: true\n  }, {\n    title: '规格型号',\n    dataIndex: 'specification',\n    key: 'specification',\n    width: 120\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    key: 'quantity',\n    width: 80,\n    render: (quantity, record) => `${quantity} ${record.unit}`\n  }, {\n    title: '单价',\n    dataIndex: 'unitPrice',\n    key: 'unitPrice',\n    width: 100,\n    render: price => `¥${price.toLocaleString()}`\n  }, {\n    title: '总价',\n    dataIndex: 'totalPrice',\n    key: 'totalPrice',\n    width: 100,\n    render: price => `¥${price.toLocaleString()}`\n  }, {\n    title: '供应商',\n    dataIndex: 'supplier',\n    key: 'supplier',\n    width: 120\n  }, {\n    title: '交期',\n    dataIndex: 'leadTime',\n    key: 'leadTime',\n    width: 80,\n    render: days => `${days}天`\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEditMaterial(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u590D\\u5236\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 21\n          }, this),\n          onClick: () => {\n            message.success('物料已复制');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 21\n          }, this),\n          onClick: () => {\n            ConfirmDialog.confirm({\n              title: '确认删除',\n              content: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u7269\\u6599\\u5417\\uFF1F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: '#ff4d4f',\n                    marginTop: 12\n                  },\n                  children: \"\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\uFF01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this),\n              type: 'warning',\n              onConfirm: () => {\n                message.success('物料已删除');\n              }\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this)\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px 0'\n      },\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n        children: \"BOM\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n        children: \"\\u6838\\u5FC3BOM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n        children: \"\\u7F16\\u8F91BOM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 23\n              }, this),\n              onClick: () => navigate(-1),\n              children: \"\\u8FD4\\u56DE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              style: {\n                margin: 0\n              },\n              children: \"\\u7F16\\u8F91\\u6838\\u5FC3BOM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), currentBOM && /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: currentBOM.code\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 29\n              }, this),\n              children: \"\\u7248\\u672C\\u5386\\u53F2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 44\n              }, this),\n              onClick: handleSave,\n              children: \"\\u4FDD\\u5B58\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 31\n            }, this), \"\\u57FA\\u672C\\u4FE1\\u606F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 25\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              gutter: [16, 16],\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"bomCode\",\n                  label: \"BOM\\u7F16\\u7801\",\n                  rules: [{\n                    required: true,\n                    message: '请输入BOM编码'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u7F16\\u7801\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"bomName\",\n                  label: \"BOM\\u540D\\u79F0\",\n                  rules: [{\n                    required: true,\n                    message: '请输入BOM名称'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u540D\\u79F0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"version\",\n                  label: \"\\u7248\\u672C\",\n                  rules: [{\n                    required: true,\n                    message: '请输入版本'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u7248\\u672C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"productLine\",\n                  label: \"\\u4EA7\\u54C1\\u7EBF\",\n                  rules: [{\n                    required: true,\n                    message: '请选择产品线'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EA7\\u54C1\\u7EBF\",\n                    children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                      value: \"5G\\u57FA\\u7AD9\",\n                      children: \"5G\\u57FA\\u7AD9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                      value: \"4G\\u57FA\\u7AD9\",\n                      children: \"4G\\u57FA\\u7AD9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                      value: \"\\u4F20\\u8F93\\u8BBE\\u5907\",\n                      children: \"\\u4F20\\u8F93\\u8BBE\\u5907\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"status\",\n                  label: \"\\u72B6\\u6001\",\n                  rules: [{\n                    required: true,\n                    message: '请选择状态'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n                    children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                      value: \"draft\",\n                      children: \"\\u8349\\u7A3F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                      value: \"active\",\n                      children: \"\\u751F\\u6548\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                      value: \"obsolete\",\n                      children: \"\\u5E9F\\u5F03\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"description\",\n                  label: \"\\u63CF\\u8FF0\",\n                  children: /*#__PURE__*/_jsxDEV(TextArea, {\n                    rows: 4,\n                    placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u63CF\\u8FF0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, \"basic\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u7269\\u6599\\u6E05\\u5355\",\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            justify: \"space-between\",\n            align: \"middle\",\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u7269\\u6599\\u6E05\\u5355\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 25\n                }, this),\n                onClick: handleAddMaterial,\n                children: \"\\u6DFB\\u52A0\\u7269\\u6599\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            columns: materialColumns,\n            dataSource: mockMaterials,\n            rowKey: \"id\",\n            scroll: {\n              x: 1000\n            },\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, \"materials\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingMaterial ? '编辑物料' : '添加物料',\n      open: materialModalVisible,\n      onOk: handleMaterialModalOk,\n      onCancel: () => setMaterialModalVisible(false),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: materialForm,\n        layout: \"vertical\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"materialCode\",\n              label: \"\\u7269\\u6599\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入物料编码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u7F16\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"materialName\",\n              label: \"\\u7269\\u6599\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入物料名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"specification\",\n              label: \"\\u89C4\\u683C\\u578B\\u53F7\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89C4\\u683C\\u578B\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"quantity\",\n              label: \"\\u6570\\u91CF\",\n              rules: [{\n                required: true,\n                message: '请输入数量'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u6570\\u91CF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"unit\",\n              label: \"\\u5355\\u4F4D\",\n              rules: [{\n                required: true,\n                message: '请选择单位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5355\\u4F4D\",\n                children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"PCS\",\n                  children: \"PCS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"KG\",\n                  children: \"KG\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"M\",\n                  children: \"M\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"SET\",\n                  children: \"SET\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"unitPrice\",\n              label: \"\\u5355\\u4EF7\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                precision: 2,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5355\\u4EF7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(CoreBOMEditPage, \"w6rcIl/3i1lDn5pTQ+WTHxbTT5Q=\", false, function () {\n  return [useParams, useNavigate, useAppDispatch, useAppSelector, Form.useForm, Form.useForm];\n});\n_c = CoreBOMEditPage;\nexport default CoreBOMEditPage;\nvar _c;\n$RefreshReg$(_c, \"CoreBOMEditPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Card", "Typography", "Form", "Input", "Select", "<PERSON><PERSON>", "Space", "Row", "Col", "Table", "Modal", "InputNumber", "message", "Breadcrumb", "Tabs", "Tag", "<PERSON><PERSON><PERSON>", "SaveOutlined", "ArrowLeftOutlined", "PlusOutlined", "EditOutlined", "DeleteOutlined", "CopyOutlined", "FileTextOutlined", "HistoryOutlined", "useAppDispatch", "useAppSelector", "fetchCoreBOM", "updateCoreBOM", "ConfirmDialog", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "TabPane", "CoreBOMEditPage", "_s", "id", "navigate", "dispatch", "currentBOM", "loading", "state", "bom", "form", "useForm", "materialModalVisible", "setMaterialModalVisible", "editingMaterial", "setEditingMaterial", "materialForm", "activeTab", "setActiveTab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomCode", "code", "bom<PERSON>ame", "name", "version", "productLine", "description", "status", "handleSave", "values", "validateFields", "data", "success", "error", "handleAddMaterial", "resetFields", "handleEditMaterial", "record", "handleMaterialModalOk", "mockMaterials", "materialCode", "materialName", "specification", "quantity", "unit", "unitPrice", "totalPrice", "supplier", "leadTime", "level", "parentId", "materialColumns", "title", "dataIndex", "key", "width", "render", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ellipsis", "price", "toLocaleString", "days", "fixed", "_", "size", "type", "icon", "onClick", "danger", "confirm", "content", "style", "marginTop", "onConfirm", "textAlign", "padding", "marginBottom", "<PERSON><PERSON>", "justify", "align", "margin", "active<PERSON><PERSON>", "onChange", "tab", "layout", "gutter", "xs", "md", "label", "rules", "required", "placeholder", "Option", "value", "rows", "strong", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "open", "onOk", "onCancel", "min", "precision", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/CoreBOMEditPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Typography,\n  Form,\n  Input,\n  Select,\n  Button,\n  Space,\n  Row,\n  Col,\n  Table,\n  Modal,\n  InputNumber,\n  message,\n  Breadcrumb,\n  Tabs,\n  Tag,\n  Tooltip,\n\n} from 'antd';\nimport {\n  SaveOutlined,\n  ArrowLeftOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  CopyOutlined,\n  FileTextOutlined,\n  HistoryOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCoreBOM, updateCoreBOM } from '../../store/slices/bomSlice';\nimport { formatDate } from '../../utils';\nimport { ConfirmDialog } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { TabPane } = Tabs;\n\nconst CoreBOMEditPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const { currentBOM, loading } = useAppSelector(state => state.bom);\n\n  const [form] = Form.useForm();\n  const [materialModalVisible, setMaterialModalVisible] = useState(false);\n  const [editingMaterial, setEditingMaterial] = useState<any>(null);\n  const [materialForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('basic');\n\n  useEffect(() => {\n    if (id) {\n      dispatch(fetchCoreBOM(id));\n    }\n  }, [id, dispatch]);\n\n  useEffect(() => {\n    if (currentBOM) {\n      form.setFieldsValue({\n        bomCode: currentBOM.code,\n        bomName: currentBOM.name,\n        version: currentBOM.version,\n        productLine: 'productLine' in currentBOM ? currentBOM.productLine : '5G基站',\n        description: currentBOM.description,\n        status: currentBOM.status,\n      });\n    }\n  }, [currentBOM, form]);\n\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      await dispatch(updateCoreBOM({ id: id!, data: values }));\n      message.success('BOM保存成功');\n    } catch (error) {\n      message.error('保存失败，请重试');\n    }\n  };\n\n  const handleAddMaterial = () => {\n    setEditingMaterial(null);\n    materialForm.resetFields();\n    setMaterialModalVisible(true);\n  };\n\n  const handleEditMaterial = (record: any) => {\n    setEditingMaterial(record);\n    materialForm.setFieldsValue(record);\n    setMaterialModalVisible(true);\n  };\n\n  const handleMaterialModalOk = async () => {\n    try {\n      const values = await materialForm.validateFields();\n\n      if (editingMaterial) {\n        message.success('物料更新成功');\n      } else {\n        message.success('物料添加成功');\n      }\n\n      setMaterialModalVisible(false);\n      // TODO: 更新物料列表\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  // 模拟BOM物料数据\n  const mockMaterials = [\n    {\n      id: '1',\n      materialCode: 'ANT-MAIN-001',\n      materialName: '5G主天线单元',\n      specification: 'ANT-5G-001',\n      quantity: 1,\n      unit: 'PCS',\n      unitPrice: 1500,\n      totalPrice: 1500,\n      supplier: '华为技术',\n      leadTime: 14,\n      level: 1,\n      parentId: null,\n    },\n    {\n      id: '2',\n      materialCode: 'RF-AMP-001',\n      materialName: 'RF功率放大器',\n      specification: 'RF-PA-5G-001',\n      quantity: 2,\n      unit: 'PCS',\n      unitPrice: 2500,\n      totalPrice: 5000,\n      supplier: '中兴通讯',\n      leadTime: 21,\n      level: 2,\n      parentId: '1',\n    },\n  ];\n\n  const materialColumns = [\n    {\n      title: '层级',\n      dataIndex: 'level',\n      key: 'level',\n      width: 60,\n      render: (level: number) => <Tag color=\"blue\">L{level}</Tag>,\n    },\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      ellipsis: true,\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 120,\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n      render: (quantity: number, record: any) => `${quantity} ${record.unit}`,\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      width: 100,\n      render: (price: number) => `¥${price.toLocaleString()}`,\n    },\n    {\n      title: '总价',\n      dataIndex: 'totalPrice',\n      key: 'totalPrice',\n      width: 100,\n      render: (price: number) => `¥${price.toLocaleString()}`,\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 120,\n    },\n    {\n      title: '交期',\n      dataIndex: 'leadTime',\n      key: 'leadTime',\n      width: 80,\n      render: (days: number) => `${days}天`,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: any) => (\n        <Space size=\"small\">\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditMaterial(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"复制\">\n            <Button\n              type=\"text\"\n              icon={<CopyOutlined />}\n              onClick={() => {\n                message.success('物料已复制');\n              }}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => {\n                ConfirmDialog.confirm({\n                  title: '确认删除',\n                  content: (\n                    <div>\n                      <p>确定要删除这个物料吗？</p>\n                      <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复！</p>\n                    </div>\n                  ),\n                  type: 'warning',\n                  onConfirm: () => {\n                    message.success('物料已删除');\n                  }\n                });\n              }}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Title level={4}>加载中...</Title>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Breadcrumb style={{ marginBottom: 16 }}>\n        <Breadcrumb.Item>BOM管理</Breadcrumb.Item>\n        <Breadcrumb.Item>核心BOM</Breadcrumb.Item>\n        <Breadcrumb.Item>编辑BOM</Breadcrumb.Item>\n      </Breadcrumb>\n\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n          <Col>\n            <Space>\n              <Button\n                icon={<ArrowLeftOutlined />}\n                onClick={() => navigate(-1)}\n              >\n                返回\n              </Button>\n              <Title level={4} style={{ margin: 0 }}>\n                编辑核心BOM\n              </Title>\n              {currentBOM && (\n                <Tag color=\"blue\">{currentBOM.code}</Tag>\n              )}\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<HistoryOutlined />}>\n                版本历史\n              </Button>\n              <Button type=\"primary\" icon={<SaveOutlined />} onClick={handleSave}>\n                保存\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab={<span><FileTextOutlined />基本信息</span>} key=\"basic\">\n            <Form form={form} layout=\"vertical\">\n              <Row gutter={[16, 16]}>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name=\"bomCode\"\n                    label=\"BOM编码\"\n                    rules={[{ required: true, message: '请输入BOM编码' }]}\n                  >\n                    <Input placeholder=\"请输入BOM编码\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name=\"bomName\"\n                    label=\"BOM名称\"\n                    rules={[{ required: true, message: '请输入BOM名称' }]}\n                  >\n                    <Input placeholder=\"请输入BOM名称\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={8}>\n                  <Form.Item\n                    name=\"version\"\n                    label=\"版本\"\n                    rules={[{ required: true, message: '请输入版本' }]}\n                  >\n                    <Input placeholder=\"请输入版本\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"productLine\"\n                    label=\"产品线\"\n                    rules={[{ required: true, message: '请选择产品线' }]}\n                  >\n                    <Select placeholder=\"请选择产品线\">\n                      <Select.Option value=\"5G基站\">5G基站</Select.Option>\n                      <Select.Option value=\"4G基站\">4G基站</Select.Option>\n                      <Select.Option value=\"传输设备\">传输设备</Select.Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"status\"\n                    label=\"状态\"\n                    rules={[{ required: true, message: '请选择状态' }]}\n                  >\n                    <Select placeholder=\"请选择状态\">\n                      <Select.Option value=\"draft\">草稿</Select.Option>\n                      <Select.Option value=\"active\">生效</Select.Option>\n                      <Select.Option value=\"obsolete\">废弃</Select.Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n                <Col xs={24}>\n                  <Form.Item name=\"description\" label=\"描述\">\n                    <TextArea rows={4} placeholder=\"请输入BOM描述\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n            </Form>\n          </TabPane>\n\n          <TabPane tab=\"物料清单\" key=\"materials\">\n            <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n              <Col>\n                <Text strong>物料清单</Text>\n              </Col>\n              <Col>\n                <Button\n                  type=\"primary\"\n                  icon={<PlusOutlined />}\n                  onClick={handleAddMaterial}\n                >\n                  添加物料\n                </Button>\n              </Col>\n            </Row>\n\n            <Table\n              columns={materialColumns}\n              dataSource={mockMaterials}\n              rowKey=\"id\"\n              scroll={{ x: 1000 }}\n              pagination={false}\n            />\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 物料编辑模态框 */}\n      <Modal\n        title={editingMaterial ? '编辑物料' : '添加物料'}\n        open={materialModalVisible}\n        onOk={handleMaterialModalOk}\n        onCancel={() => setMaterialModalVisible(false)}\n        width={600}\n      >\n        <Form form={materialForm} layout=\"vertical\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"materialCode\"\n                label=\"物料编码\"\n                rules={[{ required: true, message: '请输入物料编码' }]}\n              >\n                <Input placeholder=\"请输入物料编码\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"materialName\"\n                label=\"物料名称\"\n                rules={[{ required: true, message: '请输入物料名称' }]}\n              >\n                <Input placeholder=\"请输入物料名称\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"specification\"\n                label=\"规格型号\"\n              >\n                <Input placeholder=\"请输入规格型号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"quantity\"\n                label=\"数量\"\n                rules={[{ required: true, message: '请输入数量' }]}\n              >\n                <InputNumber min={1} style={{ width: '100%' }} placeholder=\"请输入数量\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"unit\"\n                label=\"单位\"\n                rules={[{ required: true, message: '请选择单位' }]}\n              >\n                <Select placeholder=\"请选择单位\">\n                  <Select.Option value=\"PCS\">PCS</Select.Option>\n                  <Select.Option value=\"KG\">KG</Select.Option>\n                  <Select.Option value=\"M\">M</Select.Option>\n                  <Select.Option value=\"SET\">SET</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"unitPrice\"\n                label=\"单价\"\n              >\n                <InputNumber\n                  min={0}\n                  precision={2}\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入单价\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CoreBOMEditPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,OAAO,QAEF,MAAM;AACb,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,eAAe,QACV,mBAAmB;AAE1B,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;AAEzE,SAASC,aAAa,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGjD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGhC,UAAU;AAClC,MAAM;EAAEiC;AAAS,CAAC,GAAG/B,KAAK;AAC1B,MAAM;EAAEgC;AAAQ,CAAC,GAAGrB,IAAI;AAExB,MAAMsB,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAG,CAAC,GAAGxC,SAAS,CAAiB,CAAC;EAC1C,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,QAAQ,GAAGf,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEgB,UAAU;IAAEC;EAAQ,CAAC,GAAGhB,cAAc,CAACiB,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EAElE,MAAM,CAACC,IAAI,CAAC,GAAG3C,IAAI,CAAC4C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAM,IAAI,CAAC;EACjE,MAAM,CAACuD,YAAY,CAAC,GAAGjD,IAAI,CAAC4C,OAAO,CAAC,CAAC;EACrC,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,OAAO,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,IAAIyC,EAAE,EAAE;MACNE,QAAQ,CAACb,YAAY,CAACW,EAAE,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACA,EAAE,EAAEE,QAAQ,CAAC,CAAC;EAElB3C,SAAS,CAAC,MAAM;IACd,IAAI4C,UAAU,EAAE;MACdI,IAAI,CAACS,cAAc,CAAC;QAClBC,OAAO,EAAEd,UAAU,CAACe,IAAI;QACxBC,OAAO,EAAEhB,UAAU,CAACiB,IAAI;QACxBC,OAAO,EAAElB,UAAU,CAACkB,OAAO;QAC3BC,WAAW,EAAE,aAAa,IAAInB,UAAU,GAAGA,UAAU,CAACmB,WAAW,GAAG,MAAM;QAC1EC,WAAW,EAAEpB,UAAU,CAACoB,WAAW;QACnCC,MAAM,EAAErB,UAAU,CAACqB;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrB,UAAU,EAAEI,IAAI,CAAC,CAAC;EAEtB,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMnB,IAAI,CAACoB,cAAc,CAAC,CAAC;MAC1C,MAAMzB,QAAQ,CAACZ,aAAa,CAAC;QAAEU,EAAE,EAAEA,EAAG;QAAE4B,IAAI,EAAEF;MAAO,CAAC,CAAC,CAAC;MACxDpD,OAAO,CAACuD,OAAO,CAAC,SAAS,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdxD,OAAO,CAACwD,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnB,kBAAkB,CAAC,IAAI,CAAC;IACxBC,YAAY,CAACmB,WAAW,CAAC,CAAC;IAC1BtB,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAMuB,kBAAkB,GAAIC,MAAW,IAAK;IAC1CtB,kBAAkB,CAACsB,MAAM,CAAC;IAC1BrB,YAAY,CAACG,cAAc,CAACkB,MAAM,CAAC;IACnCxB,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAMyB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMT,MAAM,GAAG,MAAMb,YAAY,CAACc,cAAc,CAAC,CAAC;MAElD,IAAIhB,eAAe,EAAE;QACnBrC,OAAO,CAACuD,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACLvD,OAAO,CAACuD,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEAnB,uBAAuB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdxD,OAAO,CAACwD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMM,aAAa,GAAG,CACpB;IACEpC,EAAE,EAAE,GAAG;IACPqC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,YAAY;IAC3BC,QAAQ,EAAE,CAAC;IACXC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE/C,EAAE,EAAE,GAAG;IACPqC,YAAY,EAAE,YAAY;IAC1BC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,cAAc;IAC7BC,QAAQ,EAAE,CAAC;IACXC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGP,KAAa,iBAAKrD,OAAA,CAAChB,GAAG;MAAC6E,KAAK,EAAC,MAAM;MAAAC,QAAA,GAAC,GAAC,EAACT,KAAK;IAAA;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAC5D,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBS,QAAQ,EAAE;EACZ,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAEA,CAACb,QAAgB,EAAEN,MAAW,KAAK,GAAGM,QAAQ,IAAIN,MAAM,CAACO,IAAI;EACvE,CAAC,EACD;IACEQ,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGQ,KAAa,IAAK,IAAIA,KAAK,CAACC,cAAc,CAAC,CAAC;EACvD,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGQ,KAAa,IAAK,IAAIA,KAAK,CAACC,cAAc,CAAC,CAAC;EACvD,CAAC,EACD;IACEb,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGU,IAAY,IAAK,GAAGA,IAAI;EACnC,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVY,KAAK,EAAE,OAAgB;IACvBX,MAAM,EAAEA,CAACY,CAAM,EAAE/B,MAAW,kBAC1BzC,OAAA,CAACzB,KAAK;MAACkG,IAAI,EAAC,OAAO;MAAAX,QAAA,gBACjB9D,OAAA,CAACf,OAAO;QAACuE,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB9D,OAAA,CAAC1B,MAAM;UACLoG,IAAI,EAAC,MAAM;UACXC,IAAI,eAAE3E,OAAA,CAACX,YAAY;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBU,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAACC,MAAM;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlE,OAAA,CAACf,OAAO;QAACuE,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB9D,OAAA,CAAC1B,MAAM;UACLoG,IAAI,EAAC,MAAM;UACXC,IAAI,eAAE3E,OAAA,CAACT,YAAY;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBU,OAAO,EAAEA,CAAA,KAAM;YACb/F,OAAO,CAACuD,OAAO,CAAC,OAAO,CAAC;UAC1B;QAAE;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlE,OAAA,CAACf,OAAO;QAACuE,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB9D,OAAA,CAAC1B,MAAM;UACLoG,IAAI,EAAC,MAAM;UACXG,MAAM;UACNF,IAAI,eAAE3E,OAAA,CAACV,cAAc;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBU,OAAO,EAAEA,CAAA,KAAM;YACb9E,aAAa,CAACgF,OAAO,CAAC;cACpBtB,KAAK,EAAE,MAAM;cACbuB,OAAO,eACL/E,OAAA;gBAAA8D,QAAA,gBACE9D,OAAA;kBAAA8D,QAAA,EAAG;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClBlE,OAAA;kBAAGgF,KAAK,EAAE;oBAAEnB,KAAK,EAAE,SAAS;oBAAEoB,SAAS,EAAE;kBAAG,CAAE;kBAAAnB,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CACN;cACDQ,IAAI,EAAE,SAAS;cACfQ,SAAS,EAAEA,CAAA,KAAM;gBACfrG,OAAO,CAACuD,OAAO,CAAC,OAAO,CAAC;cAC1B;YACF,CAAC,CAAC;UACJ;QAAE;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;EAED,IAAIvD,OAAO,EAAE;IACX,oBACEX,OAAA;MAAKgF,KAAK,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAS,CAAE;MAAAtB,QAAA,eACrD9D,OAAA,CAACC,KAAK;QAACoD,KAAK,EAAE,CAAE;QAAAS,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAEV;EAEA,oBACElE,OAAA;IAAA8D,QAAA,gBACE9D,OAAA,CAAClB,UAAU;MAACkG,KAAK,EAAE;QAAEK,YAAY,EAAE;MAAG,CAAE;MAAAvB,QAAA,gBACtC9D,OAAA,CAAClB,UAAU,CAACwG,IAAI;QAAAxB,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eACxClE,OAAA,CAAClB,UAAU,CAACwG,IAAI;QAAAxB,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eACxClE,OAAA,CAAClB,UAAU,CAACwG,IAAI;QAAAxB,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAEblE,OAAA,CAAC/B,IAAI;MAAA6F,QAAA,gBACH9D,OAAA,CAACxB,GAAG;QAAC+G,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACR,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAG,CAAE;QAAAvB,QAAA,gBACtE9D,OAAA,CAACvB,GAAG;UAAAqF,QAAA,eACF9D,OAAA,CAACzB,KAAK;YAAAuF,QAAA,gBACJ9D,OAAA,CAAC1B,MAAM;cACLqG,IAAI,eAAE3E,OAAA,CAACb,iBAAiB;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5BU,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,CAAC,CAAC,CAAE;cAAAsD,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlE,OAAA,CAACC,KAAK;cAACoD,KAAK,EAAE,CAAE;cAAC2B,KAAK,EAAE;gBAAES,MAAM,EAAE;cAAE,CAAE;cAAA3B,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACPxD,UAAU,iBACTV,OAAA,CAAChB,GAAG;cAAC6E,KAAK,EAAC,MAAM;cAAAC,QAAA,EAAEpD,UAAU,CAACe;YAAI;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlE,OAAA,CAACvB,GAAG;UAAAqF,QAAA,eACF9D,OAAA,CAACzB,KAAK;YAAAuF,QAAA,gBACJ9D,OAAA,CAAC1B,MAAM;cAACqG,IAAI,eAAE3E,OAAA,CAACP,eAAe;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAAC;YAEnC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlE,OAAA,CAAC1B,MAAM;cAACoG,IAAI,EAAC,SAAS;cAACC,IAAI,eAAE3E,OAAA,CAACd,YAAY;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACU,OAAO,EAAE5C,UAAW;cAAA8B,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlE,OAAA,CAACjB,IAAI;QAAC2G,SAAS,EAAErE,SAAU;QAACsE,QAAQ,EAAErE,YAAa;QAAAwC,QAAA,gBACjD9D,OAAA,CAACI,OAAO;UAACwF,GAAG,eAAE5F,OAAA;YAAA8D,QAAA,gBAAM9D,OAAA,CAACR,gBAAgB;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAJ,QAAA,eAClD9D,OAAA,CAAC7B,IAAI;YAAC2C,IAAI,EAAEA,IAAK;YAAC+E,MAAM,EAAC,UAAU;YAAA/B,QAAA,eACjC9D,OAAA,CAACxB,GAAG;cAACsH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;cAAAhC,QAAA,gBACpB9D,OAAA,CAACvB,GAAG;gBAACsH,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAlC,QAAA,eACjB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;kBACR3D,IAAI,EAAC,SAAS;kBACdsE,KAAK,EAAC,iBAAO;kBACbC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtH,OAAO,EAAE;kBAAW,CAAC,CAAE;kBAAAiF,QAAA,eAEjD9D,OAAA,CAAC5B,KAAK;oBAACgI,WAAW,EAAC;kBAAU;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;gBAACsH,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAlC,QAAA,eACjB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;kBACR3D,IAAI,EAAC,SAAS;kBACdsE,KAAK,EAAC,iBAAO;kBACbC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtH,OAAO,EAAE;kBAAW,CAAC,CAAE;kBAAAiF,QAAA,eAEjD9D,OAAA,CAAC5B,KAAK;oBAACgI,WAAW,EAAC;kBAAU;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;gBAACsH,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAlC,QAAA,eACjB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;kBACR3D,IAAI,EAAC,SAAS;kBACdsE,KAAK,EAAC,cAAI;kBACVC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtH,OAAO,EAAE;kBAAQ,CAAC,CAAE;kBAAAiF,QAAA,eAE9C9D,OAAA,CAAC5B,KAAK;oBAACgI,WAAW,EAAC;kBAAO;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;gBAACsH,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAlC,QAAA,eAClB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;kBACR3D,IAAI,EAAC,aAAa;kBAClBsE,KAAK,EAAC,oBAAK;kBACXC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtH,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAAiF,QAAA,eAE/C9D,OAAA,CAAC3B,MAAM;oBAAC+H,WAAW,EAAC,sCAAQ;oBAAAtC,QAAA,gBAC1B9D,OAAA,CAAC3B,MAAM,CAACgI,MAAM;sBAACC,KAAK,EAAC,gBAAM;sBAAAxC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAe,CAAC,eAChDlE,OAAA,CAAC3B,MAAM,CAACgI,MAAM;sBAACC,KAAK,EAAC,gBAAM;sBAAAxC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAe,CAAC,eAChDlE,OAAA,CAAC3B,MAAM,CAACgI,MAAM;sBAACC,KAAK,EAAC,0BAAM;sBAAAxC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAe,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;gBAACsH,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAlC,QAAA,eAClB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;kBACR3D,IAAI,EAAC,QAAQ;kBACbsE,KAAK,EAAC,cAAI;kBACVC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtH,OAAO,EAAE;kBAAQ,CAAC,CAAE;kBAAAiF,QAAA,eAE9C9D,OAAA,CAAC3B,MAAM;oBAAC+H,WAAW,EAAC,gCAAO;oBAAAtC,QAAA,gBACzB9D,OAAA,CAAC3B,MAAM,CAACgI,MAAM;sBAACC,KAAK,EAAC,OAAO;sBAAAxC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAe,CAAC,eAC/ClE,OAAA,CAAC3B,MAAM,CAACgI,MAAM;sBAACC,KAAK,EAAC,QAAQ;sBAAAxC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAe,CAAC,eAChDlE,OAAA,CAAC3B,MAAM,CAACgI,MAAM;sBAACC,KAAK,EAAC,UAAU;sBAAAxC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAe,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;gBAACsH,EAAE,EAAE,EAAG;gBAAAjC,QAAA,eACV9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;kBAAC3D,IAAI,EAAC,aAAa;kBAACsE,KAAK,EAAC,cAAI;kBAAAnC,QAAA,eACtC9D,OAAA,CAACG,QAAQ;oBAACoG,IAAI,EAAE,CAAE;oBAACH,WAAW,EAAC;kBAAU;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GA9DgD,OAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+DvD,CAAC,eAEVlE,OAAA,CAACI,OAAO;UAACwF,GAAG,EAAC,0BAAM;UAAA9B,QAAA,gBACjB9D,OAAA,CAACxB,GAAG;YAAC+G,OAAO,EAAC,eAAe;YAACC,KAAK,EAAC,QAAQ;YAACR,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAG,CAAE;YAAAvB,QAAA,gBACtE9D,OAAA,CAACvB,GAAG;cAAAqF,QAAA,eACF9D,OAAA,CAACE,IAAI;gBAACsG,MAAM;gBAAA1C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNlE,OAAA,CAACvB,GAAG;cAAAqF,QAAA,eACF9D,OAAA,CAAC1B,MAAM;gBACLoG,IAAI,EAAC,SAAS;gBACdC,IAAI,eAAE3E,OAAA,CAACZ,YAAY;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBU,OAAO,EAAEtC,iBAAkB;gBAAAwB,QAAA,EAC5B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlE,OAAA,CAACtB,KAAK;YACJ+H,OAAO,EAAElD,eAAgB;YACzBmD,UAAU,EAAE/D,aAAc;YAC1BgE,MAAM,EAAC,IAAI;YACXC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAK,CAAE;YACpBC,UAAU,EAAE;UAAM;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA,GAtBoB,WAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuB1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPlE,OAAA,CAACrB,KAAK;MACJ6E,KAAK,EAAEtC,eAAe,GAAG,MAAM,GAAG,MAAO;MACzC6F,IAAI,EAAE/F,oBAAqB;MAC3BgG,IAAI,EAAEtE,qBAAsB;MAC5BuE,QAAQ,EAAEA,CAAA,KAAMhG,uBAAuB,CAAC,KAAK,CAAE;MAC/C0C,KAAK,EAAE,GAAI;MAAAG,QAAA,eAEX9D,OAAA,CAAC7B,IAAI;QAAC2C,IAAI,EAAEM,YAAa;QAACyE,MAAM,EAAC,UAAU;QAAA/B,QAAA,eACzC9D,OAAA,CAACxB,GAAG;UAACsH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAhC,QAAA,gBACpB9D,OAAA,CAACvB,GAAG;YAACsH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlC,QAAA,eAClB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;cACR3D,IAAI,EAAC,cAAc;cACnBsE,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtH,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiF,QAAA,eAEhD9D,OAAA,CAAC5B,KAAK;gBAACgI,WAAW,EAAC;cAAS;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;YAACsH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlC,QAAA,eAClB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;cACR3D,IAAI,EAAC,cAAc;cACnBsE,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtH,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiF,QAAA,eAEhD9D,OAAA,CAAC5B,KAAK;gBAACgI,WAAW,EAAC;cAAS;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;YAACsH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlC,QAAA,eAClB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;cACR3D,IAAI,EAAC,eAAe;cACpBsE,KAAK,EAAC,0BAAM;cAAAnC,QAAA,eAEZ9D,OAAA,CAAC5B,KAAK;gBAACgI,WAAW,EAAC;cAAS;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;YAACsH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlC,QAAA,eAClB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;cACR3D,IAAI,EAAC,UAAU;cACfsE,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtH,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiF,QAAA,eAE9C9D,OAAA,CAACpB,WAAW;gBAACsI,GAAG,EAAE,CAAE;gBAAClC,KAAK,EAAE;kBAAErB,KAAK,EAAE;gBAAO,CAAE;gBAACyC,WAAW,EAAC;cAAO;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;YAACsH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlC,QAAA,eAClB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;cACR3D,IAAI,EAAC,MAAM;cACXsE,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtH,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiF,QAAA,eAE9C9D,OAAA,CAAC3B,MAAM;gBAAC+H,WAAW,EAAC,gCAAO;gBAAAtC,QAAA,gBACzB9D,OAAA,CAAC3B,MAAM,CAACgI,MAAM;kBAACC,KAAK,EAAC,KAAK;kBAAAxC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC9ClE,OAAA,CAAC3B,MAAM,CAACgI,MAAM;kBAACC,KAAK,EAAC,IAAI;kBAAAxC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC5ClE,OAAA,CAAC3B,MAAM,CAACgI,MAAM;kBAACC,KAAK,EAAC,GAAG;kBAAAxC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC1ClE,OAAA,CAAC3B,MAAM,CAACgI,MAAM;kBAACC,KAAK,EAAC,KAAK;kBAAAxC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACvB,GAAG;YAACsH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlC,QAAA,eAClB9D,OAAA,CAAC7B,IAAI,CAACmH,IAAI;cACR3D,IAAI,EAAC,WAAW;cAChBsE,KAAK,EAAC,cAAI;cAAAnC,QAAA,eAEV9D,OAAA,CAACpB,WAAW;gBACVsI,GAAG,EAAE,CAAE;gBACPC,SAAS,EAAE,CAAE;gBACbnC,KAAK,EAAE;kBAAErB,KAAK,EAAE;gBAAO,CAAE;gBACzByC,WAAW,EAAC;cAAO;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5D,EAAA,CA5aID,eAAyB;EAAA,QACdtC,SAAS,EACPC,WAAW,EACX0B,cAAc,EACCC,cAAc,EAE/BxB,IAAI,CAAC4C,OAAO,EAGJ5C,IAAI,CAAC4C,OAAO;AAAA;AAAAqG,EAAA,GAT/B/G,eAAyB;AA8a/B,eAAeA,eAAe;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}