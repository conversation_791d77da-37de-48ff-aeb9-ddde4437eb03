{"ast": null, "code": "import axios from 'axios';\nimport { API_BASE_URL, STORAGE_KEYS, ERROR_CODES } from '../constants';\nimport { errorHandler, ErrorType } from '../utils/errorHandler';\nclass ApiService {\n  constructor() {\n    this.instance = void 0;\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // 请求拦截器\n    this.instance.interceptors.request.use(config => {\n      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // 响应拦截器\n    this.instance.interceptors.response.use(response => {\n      return response;\n    }, error => {\n      if (error.response) {\n        const {\n          status,\n          data\n        } = error.response;\n        switch (status) {\n          case ERROR_CODES.UNAUTHORIZED:\n            // 清除本地存储的认证信息\n            localStorage.removeItem(STORAGE_KEYS.TOKEN);\n            localStorage.removeItem(STORAGE_KEYS.USER_INFO);\n            localStorage.removeItem(STORAGE_KEYS.CURRENT_ROLE);\n            // 使用错误处理器处理权限错误\n            errorHandler.handleError({\n              type: ErrorType.PERMISSION,\n              message: (data === null || data === void 0 ? void 0 : data.message) || '登录已过期，请重新登录',\n              code: status,\n              details: data\n            });\n            // 重定向到登录页\n            window.location.href = '/login';\n            break;\n          case ERROR_CODES.FORBIDDEN:\n            errorHandler.handleError({\n              type: ErrorType.PERMISSION,\n              message: (data === null || data === void 0 ? void 0 : data.message) || '权限不足，无法访问该资源',\n              code: status,\n              details: data\n            });\n            break;\n          case ERROR_CODES.NOT_FOUND:\n            errorHandler.handleError({\n              type: ErrorType.HTTP,\n              message: (data === null || data === void 0 ? void 0 : data.message) || '请求的资源不存在',\n              code: status,\n              details: data\n            });\n            break;\n          case ERROR_CODES.VALIDATION_ERROR:\n            errorHandler.handleError({\n              type: ErrorType.VALIDATION,\n              message: (data === null || data === void 0 ? void 0 : data.message) || '数据验证失败',\n              code: status,\n              details: data\n            });\n            break;\n          case ERROR_CODES.SERVER_ERROR:\n            errorHandler.handleError({\n              type: ErrorType.HTTP,\n              message: (data === null || data === void 0 ? void 0 : data.message) || '服务器内部错误，请稍后重试',\n              code: status,\n              details: data\n            });\n            break;\n          default:\n            errorHandler.handleError({\n              type: ErrorType.HTTP,\n              message: (data === null || data === void 0 ? void 0 : data.message) || error.message || '请求失败',\n              code: status,\n              details: data,\n              timestamp: Date.now()\n            });\n        }\n      } else if (error.request) {\n        errorHandler.handleError({\n          type: ErrorType.NETWORK,\n          message: '网络连接失败，请检查网络设置',\n          details: error.request,\n          timestamp: Date.now()\n        });\n      } else {\n        errorHandler.handleError({\n          type: ErrorType.UNKNOWN,\n          message: `请求配置错误: ${error.message}`,\n          details: error,\n          timestamp: Date.now()\n        });\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // GET 请求\n  async get(url, config) {\n    try {\n      const response = await this.instance.get(url, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // POST 请求\n  async post(url, data, config) {\n    try {\n      const response = await this.instance.post(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // PUT 请求\n  async put(url, data, config) {\n    try {\n      const response = await this.instance.put(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // DELETE 请求\n  async delete(url, config) {\n    try {\n      const response = await this.instance.delete(url, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // PATCH 请求\n  async patch(url, data, config) {\n    try {\n      const response = await this.instance.patch(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 文件上传\n  async upload(url, file, onProgress) {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        onUploadProgress: progressEvent => {\n          if (onProgress && progressEvent.total) {\n            const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n            onProgress(progress);\n          }\n        }\n      };\n      const response = await this.instance.post(url, formData, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 文件下载\n  async download(url, filename) {\n    try {\n      const response = await this.instance.get(url, {\n        responseType: 'blob'\n      });\n      const blob = new Blob([response.data]);\n      const downloadUrl = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = filename || 'download';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(downloadUrl);\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 获取原始axios实例（用于特殊需求）\n  getInstance() {\n    return this.instance;\n  }\n}\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "STORAGE_KEYS", "ERROR_CODES", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "ApiService", "constructor", "instance", "create", "baseURL", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "TOKEN", "Authorization", "error", "Promise", "reject", "response", "status", "data", "UNAUTHORIZED", "removeItem", "USER_INFO", "CURRENT_ROLE", "handleError", "type", "PERMISSION", "message", "code", "details", "window", "location", "href", "FORBIDDEN", "NOT_FOUND", "HTTP", "VALIDATION_ERROR", "VALIDATION", "SERVER_ERROR", "timestamp", "Date", "now", "NETWORK", "UNKNOWN", "get", "url", "post", "put", "delete", "patch", "upload", "file", "onProgress", "formData", "FormData", "append", "onUploadProgress", "progressEvent", "total", "progress", "Math", "round", "loaded", "download", "filename", "responseType", "blob", "Blob", "downloadUrl", "URL", "createObjectURL", "link", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "getInstance", "apiService"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { API_BASE_URL, STORAGE_KEYS, ERROR_CODES } from '../constants';\nimport { ApiResponse } from '../types';\nimport { errorHandler, ErrorType } from '../utils/errorHandler';\n\nclass ApiService {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // 请求拦截器\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // 响应拦截器\n    this.instance.interceptors.response.use(\n      (response: AxiosResponse<ApiResponse<any>>) => {\n        return response;\n      },\n      (error) => {\n        if (error.response) {\n          const { status, data } = error.response;\n          \n          switch (status) {\n            case ERROR_CODES.UNAUTHORIZED:\n              // 清除本地存储的认证信息\n              localStorage.removeItem(STORAGE_KEYS.TOKEN);\n              localStorage.removeItem(STORAGE_KEYS.USER_INFO);\n              localStorage.removeItem(STORAGE_KEYS.CURRENT_ROLE);\n              // 使用错误处理器处理权限错误\n              errorHandler.handleError({\n                type: ErrorType.PERMISSION,\n                message: data?.message || '登录已过期，请重新登录',\n                code: status,\n                details: data\n              });\n              // 重定向到登录页\n              window.location.href = '/login';\n              break;\n            case ERROR_CODES.FORBIDDEN:\n              errorHandler.handleError({\n                type: ErrorType.PERMISSION,\n                message: data?.message || '权限不足，无法访问该资源',\n                code: status,\n                details: data\n              });\n              break;\n            case ERROR_CODES.NOT_FOUND:\n              errorHandler.handleError({\n                type: ErrorType.HTTP,\n                message: data?.message || '请求的资源不存在',\n                code: status,\n                details: data\n              });\n              break;\n            case ERROR_CODES.VALIDATION_ERROR:\n              errorHandler.handleError({\n                type: ErrorType.VALIDATION,\n                message: data?.message || '数据验证失败',\n                code: status,\n                details: data\n              });\n              break;\n            case ERROR_CODES.SERVER_ERROR:\n              errorHandler.handleError({\n                type: ErrorType.HTTP,\n                message: data?.message || '服务器内部错误，请稍后重试',\n                code: status,\n                details: data\n              });\n              break;\n            default:\n              errorHandler.handleError({\n                type: ErrorType.HTTP,\n                message: data?.message || error.message || '请求失败',\n                code: status,\n                details: data,\n                timestamp: Date.now()\n              });\n          }\n        } else if (error.request) {\n          errorHandler.handleError({\n            type: ErrorType.NETWORK,\n            message: '网络连接失败，请检查网络设置',\n            details: error.request,\n            timestamp: Date.now()\n          });\n        } else {\n          errorHandler.handleError({\n            type: ErrorType.UNKNOWN,\n            message: `请求配置错误: ${error.message}`,\n            details: error,\n            timestamp: Date.now()\n          });\n        }\n        \n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // GET 请求\n  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.get<ApiResponse<T>>(url, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // POST 请求\n  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.post<ApiResponse<T>>(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // PUT 请求\n  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.put<ApiResponse<T>>(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // DELETE 请求\n  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.delete<ApiResponse<T>>(url, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // PATCH 请求\n  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.patch<ApiResponse<T>>(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 文件上传\n  async upload<T>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const config: AxiosRequestConfig = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        onUploadProgress: (progressEvent) => {\n          if (onProgress && progressEvent.total) {\n            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    };\n\n      const response = await this.instance.post<ApiResponse<T>>(url, formData, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 文件下载\n  async download(url: string, filename?: string): Promise<void> {\n    try {\n      const response = await this.instance.get(url, {\n        responseType: 'blob',\n      });\n\n      const blob = new Blob([response.data]);\n      const downloadUrl = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = filename || 'download';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(downloadUrl);\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 获取原始axios实例（用于特殊需求）\n  getInstance(): AxiosInstance {\n    return this.instance;\n  }\n}\n\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAA4D,OAAO;AAC/E,SAASC,YAAY,EAAEC,YAAY,EAAEC,WAAW,QAAQ,cAAc;AAEtE,SAASC,YAAY,EAAEC,SAAS,QAAQ,uBAAuB;AAE/D,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAAA,EAAG;IAAA,KAFNC,QAAQ;IAGd,IAAI,CAACA,QAAQ,GAAGR,KAAK,CAACS,MAAM,CAAC;MAC3BC,OAAO,EAAET,YAAY;MACrBU,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAG;IAC1B;IACA,IAAI,CAACL,QAAQ,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,CAAC;MACtD,IAAIH,KAAK,EAAE;QACTD,MAAM,CAACL,OAAO,CAACU,aAAa,GAAG,UAAUJ,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAM,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACf,QAAQ,CAACM,YAAY,CAACY,QAAQ,CAACV,GAAG,CACpCU,QAAyC,IAAK;MAC7C,OAAOA,QAAQ;IACjB,CAAC,EACAH,KAAK,IAAK;MACT,IAAIA,KAAK,CAACG,QAAQ,EAAE;QAClB,MAAM;UAAEC,MAAM;UAAEC;QAAK,CAAC,GAAGL,KAAK,CAACG,QAAQ;QAEvC,QAAQC,MAAM;UACZ,KAAKxB,WAAW,CAAC0B,YAAY;YAC3B;YACAV,YAAY,CAACW,UAAU,CAAC5B,YAAY,CAACmB,KAAK,CAAC;YAC3CF,YAAY,CAACW,UAAU,CAAC5B,YAAY,CAAC6B,SAAS,CAAC;YAC/CZ,YAAY,CAACW,UAAU,CAAC5B,YAAY,CAAC8B,YAAY,CAAC;YAClD;YACA5B,YAAY,CAAC6B,WAAW,CAAC;cACvBC,IAAI,EAAE7B,SAAS,CAAC8B,UAAU;cAC1BC,OAAO,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,OAAO,KAAI,aAAa;cACvCC,IAAI,EAAEV,MAAM;cACZW,OAAO,EAAEV;YACX,CAAC,CAAC;YACF;YACAW,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;YAC/B;UACF,KAAKtC,WAAW,CAACuC,SAAS;YACxBtC,YAAY,CAAC6B,WAAW,CAAC;cACvBC,IAAI,EAAE7B,SAAS,CAAC8B,UAAU;cAC1BC,OAAO,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,OAAO,KAAI,cAAc;cACxCC,IAAI,EAAEV,MAAM;cACZW,OAAO,EAAEV;YACX,CAAC,CAAC;YACF;UACF,KAAKzB,WAAW,CAACwC,SAAS;YACxBvC,YAAY,CAAC6B,WAAW,CAAC;cACvBC,IAAI,EAAE7B,SAAS,CAACuC,IAAI;cACpBR,OAAO,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,OAAO,KAAI,UAAU;cACpCC,IAAI,EAAEV,MAAM;cACZW,OAAO,EAAEV;YACX,CAAC,CAAC;YACF;UACF,KAAKzB,WAAW,CAAC0C,gBAAgB;YAC/BzC,YAAY,CAAC6B,WAAW,CAAC;cACvBC,IAAI,EAAE7B,SAAS,CAACyC,UAAU;cAC1BV,OAAO,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,OAAO,KAAI,QAAQ;cAClCC,IAAI,EAAEV,MAAM;cACZW,OAAO,EAAEV;YACX,CAAC,CAAC;YACF;UACF,KAAKzB,WAAW,CAAC4C,YAAY;YAC3B3C,YAAY,CAAC6B,WAAW,CAAC;cACvBC,IAAI,EAAE7B,SAAS,CAACuC,IAAI;cACpBR,OAAO,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,OAAO,KAAI,eAAe;cACzCC,IAAI,EAAEV,MAAM;cACZW,OAAO,EAAEV;YACX,CAAC,CAAC;YACF;UACF;YACExB,YAAY,CAAC6B,WAAW,CAAC;cACvBC,IAAI,EAAE7B,SAAS,CAACuC,IAAI;cACpBR,OAAO,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,OAAO,KAAIb,KAAK,CAACa,OAAO,IAAI,MAAM;cACjDC,IAAI,EAAEV,MAAM;cACZW,OAAO,EAAEV,IAAI;cACboB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;YACtB,CAAC,CAAC;QACN;MACF,CAAC,MAAM,IAAI3B,KAAK,CAACR,OAAO,EAAE;QACxBX,YAAY,CAAC6B,WAAW,CAAC;UACvBC,IAAI,EAAE7B,SAAS,CAAC8C,OAAO;UACvBf,OAAO,EAAE,gBAAgB;UACzBE,OAAO,EAAEf,KAAK,CAACR,OAAO;UACtBiC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL9C,YAAY,CAAC6B,WAAW,CAAC;UACvBC,IAAI,EAAE7B,SAAS,CAAC+C,OAAO;UACvBhB,OAAO,EAAE,WAAWb,KAAK,CAACa,OAAO,EAAE;UACnCE,OAAO,EAAEf,KAAK;UACdyB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;QACtB,CAAC,CAAC;MACJ;MAEA,OAAO1B,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAM8B,GAAGA,CAAIC,GAAW,EAAErC,MAA2B,EAAc;IACjE,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAAC6C,GAAG,CAAiBC,GAAG,EAAErC,MAAM,CAAC;MACrE,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,MAAMA,KAAK,CAAC,CAAC;IACf;EACF;;EAEA;EACA,MAAMgC,IAAIA,CAAID,GAAW,EAAE1B,IAAU,EAAEX,MAA2B,EAAc;IAC9E,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAAC+C,IAAI,CAAiBD,GAAG,EAAE1B,IAAI,EAAEX,MAAM,CAAC;MAC5E,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,MAAMA,KAAK,CAAC,CAAC;IACf;EACF;;EAEA;EACA,MAAMiC,GAAGA,CAAIF,GAAW,EAAE1B,IAAU,EAAEX,MAA2B,EAAc;IAC7E,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAACgD,GAAG,CAAiBF,GAAG,EAAE1B,IAAI,EAAEX,MAAM,CAAC;MAC3E,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,MAAMA,KAAK,CAAC,CAAC;IACf;EACF;;EAEA;EACA,MAAMkC,MAAMA,CAAIH,GAAW,EAAErC,MAA2B,EAAc;IACpE,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAACiD,MAAM,CAAiBH,GAAG,EAAErC,MAAM,CAAC;MACxE,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,MAAMA,KAAK,CAAC,CAAC;IACf;EACF;;EAEA;EACA,MAAMmC,KAAKA,CAAIJ,GAAW,EAAE1B,IAAU,EAAEX,MAA2B,EAAc;IAC/E,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAACkD,KAAK,CAAiBJ,GAAG,EAAE1B,IAAI,EAAEX,MAAM,CAAC;MAC7E,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,MAAMA,KAAK,CAAC,CAAC;IACf;EACF;;EAEA;EACA,MAAMoC,MAAMA,CAAIL,GAAW,EAAEM,IAAU,EAAEC,UAAuC,EAAc;IAC5F,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEJ,IAAI,CAAC;MAE7B,MAAM3C,MAA0B,GAAG;QACjCL,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDqD,gBAAgB,EAAGC,aAAa,IAAK;UACnC,IAAIL,UAAU,IAAIK,aAAa,CAACC,KAAK,EAAE;YACrC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAEJ,aAAa,CAACK,MAAM,GAAG,GAAG,GAAIL,aAAa,CAACC,KAAK,CAAC;YACjFN,UAAU,CAACO,QAAQ,CAAC;UACtB;QACF;MACF,CAAC;MAEC,MAAM1C,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAAC+C,IAAI,CAAiBD,GAAG,EAAEQ,QAAQ,EAAE7C,MAAM,CAAC;MAChF,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,MAAMA,KAAK,CAAC,CAAC;IACf;EACF;;EAEA;EACA,MAAMiD,QAAQA,CAAClB,GAAW,EAAEmB,QAAiB,EAAiB;IAC5D,IAAI;MACF,MAAM/C,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAAC6C,GAAG,CAACC,GAAG,EAAE;QAC5CoB,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAClD,QAAQ,CAACE,IAAI,CAAC,CAAC;MACtC,MAAMiD,WAAW,GAAGtC,MAAM,CAACuC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACpD,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACvC,IAAI,GAAGoC,WAAW;MACvBG,IAAI,CAACR,QAAQ,GAAGC,QAAQ,IAAI,UAAU;MACtCQ,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;MAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC;MACZJ,QAAQ,CAACE,IAAI,CAACG,WAAW,CAACN,IAAI,CAAC;MAC/BzC,MAAM,CAACuC,GAAG,CAACS,eAAe,CAACV,WAAW,CAAC;IACzC,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACd,MAAMA,KAAK,CAAC,CAAC;IACf;EACF;;EAEA;EACAiE,WAAWA,CAAA,EAAkB;IAC3B,OAAO,IAAI,CAAChF,QAAQ;EACtB;AACF;AAEA,OAAO,MAAMiF,UAAU,GAAG,IAAInF,UAAU,CAAC,CAAC;AAC1C,eAAemF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}