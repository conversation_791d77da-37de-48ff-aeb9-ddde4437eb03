{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\bom\\\\CoreBOMListPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Table, Button, Space, Input, Select, Card, Tag, Popconfirm, message, Modal, Form, Typography, Row, Col, Tooltip, Dropdown, Upload, Progress, Alert, Divider, Timeline, Descriptions, Checkbox, Radio } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, CopyOutlined, LockOutlined, UnlockOutlined, MoreOutlined, ExportOutlined, ImportOutlined, HistoryOutlined, DownloadOutlined, FileExcelOutlined, CheckCircleOutlined, ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCoreBOMs, deleteCoreBOM, freezeCoreBOM } from '../../store/slices/bomSlice';\nimport { ROUTES, BOM_STATUS } from '../../constants';\nimport { formatDate } from '../../utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Search\n} = Input;\nconst CoreBOMListPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const {\n    coreBOMs,\n    loading,\n    pagination\n  } = useAppSelector(state => state.bom);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [copyModalVisible, setCopyModalVisible] = useState(false);\n  const [copyingBOM, setCopyingBOM] = useState(null);\n  const [copyForm] = Form.useForm();\n\n  // 版本历史相关状态\n  const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);\n  const [currentBOMForHistory, setCurrentBOMForHistory] = useState(null);\n\n  // 导出导入相关状态\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [importModalVisible, setImportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState('excel');\n  const [exportFields, setExportFields] = useState(['code', 'name', 'version', 'status', 'description']);\n  const [importProgress, setImportProgress] = useState(0);\n  const [importStatus, setImportStatus] = useState(null);\n  const [importResult, setImportResult] = useState(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [exportForm] = Form.useForm();\n  const [importForm] = Form.useForm();\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter]);\n  const loadData = useCallback(() => {\n    dispatch(fetchCoreBOMs({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      keyword: searchKeyword\n    }));\n  }, [dispatch, pagination.current, pagination.pageSize, searchKeyword]);\n  const handleSearch = useCallback(value => {\n    setSearchKeyword(value);\n  }, []);\n  const handleStatusFilter = useCallback(value => {\n    setStatusFilter(value);\n  }, []);\n  const handleCreate = useCallback(() => {\n    navigate(ROUTES.CORE_BOM_CREATE);\n  }, [navigate]);\n  const handleEdit = useCallback(record => {\n    navigate(ROUTES.CORE_BOM_EDIT.replace(':id', record.id));\n  }, [navigate]);\n  const handleView = useCallback(record => {\n    navigate(ROUTES.CORE_BOM_VIEW.replace(':id', record.id));\n  }, [navigate]);\n  const handleDelete = useCallback(async record => {\n    try {\n      await dispatch(deleteCoreBOM(record.id)).unwrap();\n      message.success('删除成功');\n      loadData();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  }, [dispatch, loadData]);\n  const handleFreeze = useCallback(async record => {\n    try {\n      await dispatch(freezeCoreBOM(record.id)).unwrap();\n      message.success('冻结成功');\n      loadData();\n    } catch (error) {\n      message.error('冻结失败');\n    }\n  }, [dispatch, loadData]);\n  const handleCopy = useCallback(record => {\n    setCopyingBOM(record);\n    copyForm.setFieldsValue({\n      name: `${record.name}_副本`,\n      code: `${record.code}_COPY`\n    });\n    setCopyModalVisible(true);\n  }, [copyForm]);\n  const handleCopyConfirm = async () => {\n    try {\n      const values = await copyForm.validateFields();\n      // TODO: 实现复制BOM的API调用\n      message.success('复制成功');\n      setCopyModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('复制失败');\n    }\n  };\n  const handleVersionHistory = record => {\n    setCurrentBOMForHistory(record);\n    setVersionHistoryVisible(true);\n  };\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n  const handleImport = () => {\n    setImportModalVisible(true);\n  };\n\n  // 下载导入模板\n  const handleDownloadTemplate = () => {\n    // TODO: 实现下载模板功能\n    const templateData = [{\n      'BOM编码': 'BOM001',\n      'BOM名称': '示例BOM',\n      '版本': 'V1.0',\n      '状态': 'draft',\n      '描述': '这是一个示例BOM'\n    }];\n\n    // 创建CSV内容\n    const headers = Object.keys(templateData[0]);\n    const csvContent = [headers.join(','), ...templateData.map(row => Object.values(row).join(','))].join('\\n');\n\n    // 下载文件\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    link.href = URL.createObjectURL(blob);\n    link.download = 'core_bom_template.csv';\n    link.click();\n    message.success('模板下载成功');\n  };\n\n  // 执行导出\n  const handleExportConfirm = async () => {\n    try {\n      const values = await exportForm.validateFields();\n\n      // 获取要导出的数据\n      let exportData = mockData;\n      if (selectedRowKeys.length > 0) {\n        exportData = mockData.filter(item => selectedRowKeys.includes(item.id));\n      }\n\n      // 根据选择的字段过滤数据\n      const filteredData = exportData.map(item => {\n        const filteredItem = {};\n        exportFields.forEach(field => {\n          switch (field) {\n            case 'code':\n              filteredItem['BOM编码'] = item.code;\n              break;\n            case 'name':\n              filteredItem['BOM名称'] = item.name;\n              break;\n            case 'version':\n              filteredItem['版本'] = item.version;\n              break;\n            case 'status':\n              filteredItem['状态'] = getStatusText(item.status);\n              break;\n            case 'description':\n              filteredItem['描述'] = item.description;\n              break;\n            case 'createdBy':\n              filteredItem['创建人'] = item.createdBy;\n              break;\n            case 'createdAt':\n              filteredItem['创建时间'] = formatDate(item.createdAt);\n              break;\n            case 'updatedAt':\n              filteredItem['更新时间'] = formatDate(item.updatedAt);\n              break;\n          }\n        });\n        return filteredItem;\n      });\n\n      // 创建并下载文件\n      if (exportFormat === 'excel') {\n        // TODO: 使用xlsx库导出Excel\n        message.info('Excel导出功能需要集成xlsx库');\n      } else {\n        // CSV导出\n        const headers = Object.keys(filteredData[0] || {});\n        const csvContent = [headers.join(','), ...filteredData.map(row => Object.values(row).join(','))].join('\\n');\n        const blob = new Blob([csvContent], {\n          type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        link.href = URL.createObjectURL(blob);\n        link.download = `core_bom_export_${new Date().getTime()}.csv`;\n        link.click();\n      }\n      setExportModalVisible(false);\n      message.success('导出成功');\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  // 处理文件上传\n  const handleFileUpload = file => {\n    setImportStatus('uploading');\n    setImportProgress(0);\n\n    // 模拟上传进度\n    const interval = setInterval(() => {\n      setImportProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(interval);\n          setImportStatus('processing');\n\n          // 模拟处理过程\n          setTimeout(() => {\n            processImportFile(file);\n          }, 1000);\n          return 100;\n        }\n        return prev + 10;\n      });\n    }, 200);\n    return false; // 阻止默认上传\n  };\n\n  // 处理导入文件\n  const processImportFile = file => {\n    const reader = new FileReader();\n    reader.onload = e => {\n      try {\n        var _e$target;\n        const content = (_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result;\n        const lines = content.split('\\n');\n        const headers = lines[0].split(',');\n        const importedData = [];\n        const errors = [];\n        for (let i = 1; i < lines.length; i++) {\n          if (lines[i].trim()) {\n            const values = lines[i].split(',');\n            const row = {};\n            headers.forEach((header, index) => {\n              var _values$index;\n              row[header.trim()] = (_values$index = values[index]) === null || _values$index === void 0 ? void 0 : _values$index.trim();\n            });\n\n            // 验证数据\n            if (!row['BOM编码'] || !row['BOM名称']) {\n              errors.push(`第${i + 1}行：BOM编码和名称不能为空`);\n            } else {\n              importedData.push({\n                code: row['BOM编码'],\n                name: row['BOM名称'],\n                version: row['版本'] || 'V1.0',\n                status: row['状态'] || 'draft',\n                description: row['描述'] || ''\n              });\n            }\n          }\n        }\n        setImportResult({\n          total: lines.length - 1,\n          success: importedData.length,\n          errors: errors.length,\n          data: importedData,\n          errorMessages: errors\n        });\n        setImportStatus(errors.length > 0 ? 'error' : 'success');\n        if (errors.length === 0) {\n          message.success(`成功导入${importedData.length}条数据`);\n          // TODO: 调用API保存数据\n        } else {\n          message.warning(`导入完成，${errors.length}条数据有错误`);\n        }\n      } catch (error) {\n        setImportStatus('error');\n        setImportResult({\n          total: 0,\n          success: 0,\n          errors: 1,\n          errorMessages: ['文件格式错误，请检查文件内容']\n        });\n        message.error('文件解析失败');\n      }\n    };\n    reader.readAsText(file);\n  };\n\n  // 表格行选择\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: newSelectedRowKeys => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    onSelectAll: (selected, selectedRows, changeRows) => {\n      console.log('Select all:', selected, selectedRows, changeRows);\n    }\n  };\n\n  // 清空选择\n  const handleClearSelection = () => {\n    setSelectedRowKeys([]);\n  };\n\n  // 导出选中项\n  const handleExportSelected = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请先选择要导出的数据');\n      return;\n    }\n    setExportModalVisible(true);\n  };\n\n  // 模拟版本历史数据\n  const getVersionHistory = bom => {\n    return [{\n      version: 'V1.2',\n      status: 'active',\n      description: '优化物料清单，更新供应商信息',\n      createdBy: '李工程师',\n      createdAt: '2024-03-25 14:30:00',\n      changes: ['更新供应商信息', '调整物料数量', '优化成本结构']\n    }, {\n      version: 'V1.1',\n      status: 'frozen',\n      description: '修复物料规格错误',\n      createdBy: '王工程师',\n      createdAt: '2024-03-20 10:15:00',\n      changes: ['修正物料规格', '更新技术参数']\n    }, {\n      version: 'V1.0',\n      status: 'obsolete',\n      description: '初始版本',\n      createdBy: '张工程师',\n      createdAt: '2024-03-15 09:00:00',\n      changes: ['创建初始BOM结构']\n    }];\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case BOM_STATUS.DRAFT:\n        return 'default';\n      case BOM_STATUS.ACTIVE:\n        return 'success';\n      case BOM_STATUS.FROZEN:\n        return 'blue';\n      case BOM_STATUS.OBSOLETE:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case BOM_STATUS.DRAFT:\n        return '草稿';\n      case BOM_STATUS.ACTIVE:\n        return '激活';\n      case BOM_STATUS.FROZEN:\n        return '冻结';\n      case BOM_STATUS.OBSOLETE:\n        return '废弃';\n      default:\n        return status;\n    }\n  };\n\n  // 可导出字段选项\n  const exportFieldOptions = [{\n    label: 'BOM编码',\n    value: 'code'\n  }, {\n    label: 'BOM名称',\n    value: 'name'\n  }, {\n    label: '版本',\n    value: 'version'\n  }, {\n    label: '状态',\n    value: 'status'\n  }, {\n    label: '描述',\n    value: 'description'\n  }, {\n    label: '创建人',\n    value: 'createdBy'\n  }, {\n    label: '创建时间',\n    value: 'createdAt'\n  }, {\n    label: '更新时间',\n    value: 'updatedAt'\n  }];\n  const getActionMenuItems = record => [{\n    key: 'copy',\n    icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 13\n    }, this),\n    label: '复制',\n    onClick: () => handleCopy(record)\n  }, {\n    key: 'history',\n    icon: /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 13\n    }, this),\n    label: '版本历史',\n    onClick: () => handleVersionHistory(record)\n  }, {\n    key: 'freeze',\n    icon: record.status === BOM_STATUS.FROZEN ? /*#__PURE__*/_jsxDEV(UnlockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 51\n    }, this) : /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 72\n    }, this),\n    label: record.status === BOM_STATUS.FROZEN ? '解冻' : '冻结',\n    onClick: () => handleFreeze(record),\n    disabled: record.status === BOM_STATUS.DRAFT\n  }];\n  const columns = [{\n    title: 'BOM编码',\n    dataIndex: 'code',\n    key: 'code',\n    width: 120,\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      onClick: () => handleView(record),\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'BOM名称',\n    dataIndex: 'name',\n    key: 'name',\n    ellipsis: true\n  }, {\n    title: '版本',\n    dataIndex: 'version',\n    key: 'version',\n    width: 80\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    ellipsis: true\n  }, {\n    title: '创建人',\n    dataIndex: 'createdBy',\n    key: 'createdBy',\n    width: 100\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 120,\n    render: date => formatDate(date)\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    width: 120,\n    render: date => formatDate(date)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record),\n          disabled: record.status === BOM_STATUS.FROZEN\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2ABOM\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 23\n            }, this),\n            disabled: record.status !== BOM_STATUS.DRAFT\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n        menu: {\n          items: getActionMenuItems(record)\n        },\n        trigger: ['click'],\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(MoreOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 模拟数据\n  const mockData = [{\n    id: '1',\n    name: '5G基站天线BOM',\n    code: 'ANT-5G-001',\n    version: 'V1.0',\n    description: '5G基站用高增益天线物料清单',\n    status: 'ACTIVE',\n    items: [],\n    configRules: [],\n    createdBy: 'admin',\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-15T00:00:00Z'\n  }, {\n    id: '2',\n    name: '4G室内天线BOM',\n    code: 'ANT-4G-002',\n    version: 'V2.1',\n    description: '4G室内覆盖天线物料清单',\n    status: 'FROZEN',\n    items: [],\n    configRules: [],\n    createdBy: 'bom_manager',\n    createdAt: '2024-01-02T00:00:00Z',\n    updatedAt: '2024-01-16T00:00:00Z',\n    frozenAt: '2024-01-16T00:00:00Z',\n    frozenBy: 'admin'\n  }, {\n    id: '3',\n    name: 'WiFi天线BOM',\n    code: 'ANT-WIFI-003',\n    version: 'V1.5',\n    description: 'WiFi6天线物料清单',\n    status: 'DRAFT',\n    items: [],\n    configRules: [],\n    createdBy: 'bom_manager',\n    createdAt: '2024-01-03T00:00:00Z',\n    updatedAt: '2024-01-17T00:00:00Z'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u6838\\u5FC3BOM\\u7BA1\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ImportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 29\n              }, this),\n              onClick: handleImport,\n              children: \"\\u5BFC\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 29\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 44\n              }, this),\n              onClick: handleCreate,\n              children: \"\\u65B0\\u5EFABOM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Search, {\n            placeholder: \"\\u641C\\u7D22BOM\\u7F16\\u7801\\u6216\\u540D\\u79F0\",\n            allowClear: true,\n            onSearch: handleSearch,\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u72B6\\u6001\\u7B5B\\u9009\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            onChange: handleStatusFilter,\n            options: [{\n              label: '草稿',\n              value: BOM_STATUS.DRAFT\n            }, {\n              label: '激活',\n              value: BOM_STATUS.ACTIVE\n            }, {\n              label: '冻结',\n              value: BOM_STATUS.FROZEN\n            }, {\n              label: '废弃',\n              value: BOM_STATUS.OBSOLETE\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this), selectedRowKeys.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16,\n          padding: '8px 16px',\n          backgroundColor: '#f0f2f5',\n          borderRadius: 6\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginRight: 16\n          },\n          children: [\"\\u5DF2\\u9009\\u62E9 \", selectedRowKeys.length, \" \\u9879\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          onClick: handleClearSelection,\n          style: {\n            marginRight: 8\n          },\n          children: \"\\u6E05\\u7A7A\\u9009\\u62E9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 55\n          }, this),\n          onClick: handleExportSelected,\n          children: \"\\u5BFC\\u51FA\\u9009\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: mockData,\n        loading: loading,\n        rowKey: \"id\",\n        rowSelection: rowSelection,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          current: pagination.current,\n          pageSize: pagination.pageSize,\n          total: pagination.total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 627,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u590D\\u5236BOM\",\n      open: copyModalVisible,\n      onOk: handleCopyConfirm,\n      onCancel: () => setCopyModalVisible(false),\n      okText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: copyForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"BOM\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入BOM名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"code\",\n          label: \"BOM\\u7F16\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入BOM编码'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165BOM\\u7F16\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `版本历史 - ${currentBOMForHistory === null || currentBOMForHistory === void 0 ? void 0 : currentBOMForHistory.name}`,\n      open: versionHistoryVisible,\n      onCancel: () => setVersionHistoryVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setVersionHistoryVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 13\n      }, this)],\n      width: 800,\n      children: currentBOMForHistory && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n          title: \"\\u5F53\\u524DBOM\\u4FE1\\u606F\",\n          bordered: true,\n          size: \"small\",\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"BOM\\u7F16\\u7801\",\n            children: currentBOMForHistory.code\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"BOM\\u540D\\u79F0\",\n            children: currentBOMForHistory.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5F53\\u524D\\u7248\\u672C\",\n            children: currentBOMForHistory.version\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u72B6\\u6001\",\n            span: 3,\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: getStatusColor(currentBOMForHistory.status),\n              children: getStatusText(currentBOMForHistory.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          children: \"\\u7248\\u672C\\u5386\\u53F2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Timeline, {\n          children: getVersionHistory(currentBOMForHistory).map((version, index) => /*#__PURE__*/_jsxDEV(Timeline.Item, {\n            dot: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n              style: {\n                fontSize: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 26\n            }, this),\n            color: version.status === 'active' ? 'green' : version.status === 'frozen' ? 'blue' : 'gray',\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  marginBottom: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: 16,\n                    fontWeight: 'bold'\n                  },\n                  children: version.version\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: getStatusColor(version.status),\n                  children: getStatusText(version.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#666',\n                  marginBottom: 8\n                },\n                children: [version.createdBy, \" \\xB7 \", version.createdAt]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 8\n                },\n                children: version.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 23\n              }, this), version.changes && version.changes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold',\n                    marginBottom: 4\n                  },\n                  children: \"\\u4E3B\\u8981\\u53D8\\u66F4\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  style: {\n                    margin: 0,\n                    paddingLeft: 20\n                  },\n                  children: version.changes.map((change, changeIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: change\n                  }, changeIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 21\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 733,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5BFC\\u51FABOM\\u6570\\u636E\",\n      open: exportModalVisible,\n      onOk: handleExportConfirm,\n      onCancel: () => setExportModalVisible(false),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: exportForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5BFC\\u51FA\\u8BF4\\u660E\",\n          description: selectedRowKeys.length > 0 ? `将导出已选择的 ${selectedRowKeys.length} 条BOM数据` : \"将导出所有BOM数据\",\n          type: \"info\",\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5BFC\\u51FA\\u683C\\u5F0F\",\n          name: \"format\",\n          initialValue: exportFormat,\n          children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n            onChange: e => setExportFormat(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"excel\",\n              children: \"Excel\\u683C\\u5F0F (.xlsx)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"csv\",\n              children: \"CSV\\u683C\\u5F0F (.csv)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5BFC\\u51FA\\u5B57\\u6BB5\",\n          name: \"fields\",\n          initialValue: exportFields,\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            options: exportFieldOptions,\n            value: exportFields,\n            onChange: setExportFields\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5BFC\\u5165BOM\\u6570\\u636E\",\n      open: importModalVisible,\n      onCancel: () => {\n        setImportModalVisible(false);\n        setImportStatus(null);\n        setImportProgress(0);\n        setImportResult(null);\n        importForm.resetFields();\n      },\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 42\n        }, this),\n        onClick: handleDownloadTemplate,\n        children: \"\\u4E0B\\u8F7D\\u6A21\\u677F\"\n      }, \"template\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          setImportModalVisible(false);\n          setImportStatus(null);\n          setImportProgress(0);\n          setImportResult(null);\n          importForm.resetFields();\n        },\n        children: \"\\u53D6\\u6D88\"\n      }, \"cancel\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 848,\n        columnNumber: 13\n      }, this)],\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: importForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5BFC\\u5165\\u8BF4\\u660E\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"1. \\u8BF7\\u4E0B\\u8F7D\\u5BFC\\u5165\\u6A21\\u677F\\uFF0C\\u6309\\u7167\\u6A21\\u677F\\u683C\\u5F0F\\u586B\\u5199\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"2. \\u652F\\u6301CSV\\u683C\\u5F0F\\u6587\\u4EF6\\u5BFC\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"3. BOM\\u7F16\\u7801\\u548C\\u540D\\u79F0\\u4E3A\\u5FC5\\u586B\\u5B57\\u6BB5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"4. \\u5BFC\\u5165\\u524D\\u8BF7\\u786E\\u4FDD\\u6570\\u636E\\u683C\\u5F0F\\u6B63\\u786E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u9009\\u62E9\\u6587\\u4EF6\",\n          children: /*#__PURE__*/_jsxDEV(Upload.Dragger, {\n            accept: \".csv,.xlsx,.xls\",\n            beforeUpload: handleFileUpload,\n            showUploadList: false,\n            disabled: importStatus === 'uploading' || importStatus === 'processing',\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(FileExcelOutlined, {\n                style: {\n                  fontSize: 48,\n                  color: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u3001Excel\\u683C\\u5F0F\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 13\n        }, this), importStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16\n          },\n          children: [importStatus === 'uploading' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"\\u6B63\\u5728\\u4E0A\\u4F20\\u6587\\u4EF6...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: importProgress,\n              status: \"active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 19\n          }, this), importStatus === 'processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"\\u6B63\\u5728\\u5904\\u7406\\u6570\\u636E...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: 100,\n              status: \"active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 19\n          }, this), importStatus === 'success' && importResult && /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u5BFC\\u5165\\u6210\\u529F\",\n            description: `成功导入 ${importResult.success} 条数据`,\n            type: \"success\",\n            icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 27\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 19\n          }, this), importStatus === 'error' && importResult && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u5BFC\\u5165\\u5B8C\\u6210\\uFF0C\\u90E8\\u5206\\u6570\\u636E\\u6709\\u9519\\u8BEF\",\n              description: `成功：${importResult.success} 条，错误：${importResult.errors} 条`,\n              type: \"warning\",\n              icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 29\n              }, this),\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 21\n            }, this), importResult.errorMessages && importResult.errorMessages.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold',\n                  marginBottom: 8\n                },\n                children: \"\\u9519\\u8BEF\\u8BE6\\u60C5\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: 200,\n                  overflow: 'auto',\n                  backgroundColor: '#f5f5f5',\n                  padding: 8,\n                  borderRadius: 4\n                },\n                children: importResult.errorMessages.map((error, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#ff4d4f',\n                    marginBottom: 4\n                  },\n                  children: error\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 891,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 860,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 834,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 626,\n    columnNumber: 5\n  }, this);\n};\n_s(CoreBOMListPage, \"kI0tGHtDVeLiV8sCWSlfbZ/vL4I=\", false, function () {\n  return [useNavigate, useAppDispatch, useAppSelector, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = CoreBOMListPage;\nexport default _c2 = /*#__PURE__*/React.memo(CoreBOMListPage);\nvar _c, _c2;\n$RefreshReg$(_c, \"CoreBOMListPage\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "Popconfirm", "message", "Modal", "Form", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Upload", "Progress", "<PERSON><PERSON>", "Divider", "Timeline", "Descriptions", "Checkbox", "Radio", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "LockOutlined", "UnlockOutlined", "MoreOutlined", "ExportOutlined", "ImportOutlined", "HistoryOutlined", "DownloadOutlined", "FileExcelOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "ClockCircleOutlined", "useAppDispatch", "useAppSelector", "fetchCoreBOMs", "deleteCoreBOM", "freezeCoreBOM", "ROUTES", "BOM_STATUS", "formatDate", "jsxDEV", "_jsxDEV", "Title", "Search", "CoreBOMListPage", "_s", "navigate", "dispatch", "coreBOMs", "loading", "pagination", "state", "bom", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "copyModalVisible", "setCopyModalVisible", "copyingBOM", "setCopyingBOM", "copyForm", "useForm", "versionHistoryVisible", "setVersionHistoryVisible", "currentBOMForHistory", "setCurrentBOMForHistory", "exportModalVisible", "setExportModalVisible", "importModalVisible", "setImportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "importProgress", "setImportProgress", "importStatus", "setImportStatus", "importResult", "setImportResult", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "exportForm", "importForm", "loadData", "current", "pageSize", "page", "keyword", "handleSearch", "value", "handleStatusFilter", "handleCreate", "CORE_BOM_CREATE", "handleEdit", "record", "CORE_BOM_EDIT", "replace", "id", "handleView", "CORE_BOM_VIEW", "handleDelete", "unwrap", "success", "error", "handleFreeze", "handleCopy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "code", "handleCopyConfirm", "values", "validateFields", "handleVersionHistory", "handleExport", "handleImport", "handleDownloadTemplate", "templateData", "headers", "Object", "keys", "csv<PERSON><PERSON>nt", "join", "map", "row", "blob", "Blob", "type", "link", "document", "createElement", "href", "URL", "createObjectURL", "download", "click", "handleExportConfirm", "exportData", "mockData", "length", "filter", "item", "includes", "filteredData", "filteredItem", "for<PERSON>ach", "field", "version", "getStatusText", "status", "description", "created<PERSON>y", "createdAt", "updatedAt", "info", "Date", "getTime", "handleFileUpload", "file", "interval", "setInterval", "prev", "clearInterval", "setTimeout", "processImportFile", "reader", "FileReader", "onload", "e", "_e$target", "content", "target", "result", "lines", "split", "importedData", "errors", "i", "trim", "header", "index", "_values$index", "push", "total", "data", "errorMessages", "warning", "readAsText", "rowSelection", "onChange", "newSelectedRowKeys", "onSelectAll", "selected", "selectedRows", "changeRows", "console", "log", "handleClearSelection", "handleExportSelected", "getVersionHistory", "changes", "getStatusColor", "DRAFT", "ACTIVE", "FROZEN", "OBSOLETE", "exportFieldOptions", "label", "getActionMenuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "columns", "title", "dataIndex", "width", "render", "text", "children", "ellipsis", "color", "date", "fixed", "_", "size", "onConfirm", "okText", "cancelText", "danger", "menu", "items", "trigger", "configRules", "frozenAt", "frozenBy", "justify", "align", "style", "marginBottom", "level", "margin", "gutter", "xs", "sm", "md", "placeholder", "allowClear", "onSearch", "options", "padding", "backgroundColor", "borderRadius", "marginRight", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onOk", "onCancel", "form", "layout", "<PERSON><PERSON>", "rules", "required", "footer", "bordered", "span", "dot", "fontSize", "display", "justifyContent", "alignItems", "fontWeight", "paddingLeft", "change", "changeIndex", "initialValue", "Group", "resetFields", "<PERSON><PERSON>", "accept", "beforeUpload", "showUploadList", "className", "marginTop", "percent", "maxHeight", "overflow", "_c", "_c2", "memo", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/CoreBOMListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState, useMemo, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  Popconfirm,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Upload,\n  Progress,\n  Alert,\n  Divider,\n  Timeline,\n  Descriptions,\n  Checkbox,\n  Radio\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  LockOutlined,\n  UnlockOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  HistoryOutlined,\n  DownloadOutlined,\n  UploadOutlined,\n  FileExcelOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCoreBOMs, deleteCoreBOM, freezeCoreBOM } from '../../store/slices/bomSlice';\nimport { CoreBOM } from '../../types';\nimport { ROUTES, BOM_STATUS } from '../../constants';\nimport { formatDate } from '../../utils';\n\nconst { Title } = Typography;\nconst { Search } = Input;\n\nconst CoreBOMListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const { coreBOMs, loading, pagination } = useAppSelector(state => state.bom);\n  \n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [copyModalVisible, setCopyModalVisible] = useState(false);\n  const [copyingBOM, setCopyingBOM] = useState<CoreBOM | null>(null);\n  const [copyForm] = Form.useForm();\n  \n  // 版本历史相关状态\n  const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);\n  const [currentBOMForHistory, setCurrentBOMForHistory] = useState<CoreBOM | null>(null);\n  \n  // 导出导入相关状态\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [importModalVisible, setImportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['code', 'name', 'version', 'status', 'description']);\n  const [importProgress, setImportProgress] = useState(0);\n  const [importStatus, setImportStatus] = useState<'uploading' | 'processing' | 'success' | 'error' | null>(null);\n  const [importResult, setImportResult] = useState<any>(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [exportForm] = Form.useForm();\n  const [importForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter]);\n\n  const loadData = useCallback(() => {\n    dispatch(fetchCoreBOMs({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      keyword: searchKeyword,\n    }));\n  }, [dispatch, pagination.current, pagination.pageSize, searchKeyword]);\n\n  const handleSearch = useCallback((value: string) => {\n    setSearchKeyword(value);\n  }, []);\n\n  const handleStatusFilter = useCallback((value: string) => {\n    setStatusFilter(value);\n  }, []);\n\n  const handleCreate = useCallback(() => {\n    navigate(ROUTES.CORE_BOM_CREATE);\n  }, [navigate]);\n\n  const handleEdit = useCallback((record: CoreBOM) => {\n    navigate(ROUTES.CORE_BOM_EDIT.replace(':id', record.id));\n  }, [navigate]);\n\n  const handleView = useCallback((record: CoreBOM) => {\n    navigate(ROUTES.CORE_BOM_VIEW.replace(':id', record.id));\n  }, [navigate]);\n\n  const handleDelete = useCallback(async (record: CoreBOM) => {\n    try {\n      await dispatch(deleteCoreBOM(record.id)).unwrap();\n      message.success('删除成功');\n      loadData();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  }, [dispatch, loadData]);\n\n  const handleFreeze = useCallback(async (record: CoreBOM) => {\n    try {\n      await dispatch(freezeCoreBOM(record.id)).unwrap();\n      message.success('冻结成功');\n      loadData();\n    } catch (error) {\n      message.error('冻结失败');\n    }\n  }, [dispatch, loadData]);\n\n  const handleCopy = useCallback((record: CoreBOM) => {\n    setCopyingBOM(record);\n    copyForm.setFieldsValue({\n      name: `${record.name}_副本`,\n      code: `${record.code}_COPY`,\n    });\n    setCopyModalVisible(true);\n  }, [copyForm]);\n\n  const handleCopyConfirm = async () => {\n    try {\n      const values = await copyForm.validateFields();\n      // TODO: 实现复制BOM的API调用\n      message.success('复制成功');\n      setCopyModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('复制失败');\n    }\n  };\n\n  const handleVersionHistory = (record: CoreBOM) => {\n    setCurrentBOMForHistory(record);\n    setVersionHistoryVisible(true);\n  };\n\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const handleImport = () => {\n    setImportModalVisible(true);\n  };\n  \n  // 下载导入模板\n  const handleDownloadTemplate = () => {\n    // TODO: 实现下载模板功能\n    const templateData = [\n      {\n        'BOM编码': 'BOM001',\n        'BOM名称': '示例BOM',\n        '版本': 'V1.0',\n        '状态': 'draft',\n        '描述': '这是一个示例BOM'\n      }\n    ];\n    \n    // 创建CSV内容\n    const headers = Object.keys(templateData[0]);\n    const csvContent = [headers.join(','), ...templateData.map(row => Object.values(row).join(','))].join('\\n');\n    \n    // 下载文件\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    link.href = URL.createObjectURL(blob);\n    link.download = 'core_bom_template.csv';\n    link.click();\n    \n    message.success('模板下载成功');\n  };\n  \n  // 执行导出\n  const handleExportConfirm = async () => {\n    try {\n      const values = await exportForm.validateFields();\n      \n      // 获取要导出的数据\n      let exportData = mockData;\n      if (selectedRowKeys.length > 0) {\n        exportData = mockData.filter(item => selectedRowKeys.includes(item.id));\n      }\n      \n      // 根据选择的字段过滤数据\n      const filteredData = exportData.map(item => {\n        const filteredItem: any = {};\n        exportFields.forEach(field => {\n          switch (field) {\n            case 'code':\n              filteredItem['BOM编码'] = item.code;\n              break;\n            case 'name':\n              filteredItem['BOM名称'] = item.name;\n              break;\n            case 'version':\n              filteredItem['版本'] = item.version;\n              break;\n            case 'status':\n              filteredItem['状态'] = getStatusText(item.status);\n              break;\n            case 'description':\n              filteredItem['描述'] = item.description;\n              break;\n            case 'createdBy':\n              filteredItem['创建人'] = item.createdBy;\n              break;\n            case 'createdAt':\n              filteredItem['创建时间'] = formatDate(item.createdAt);\n              break;\n            case 'updatedAt':\n              filteredItem['更新时间'] = formatDate(item.updatedAt);\n              break;\n          }\n        });\n        return filteredItem;\n      });\n      \n      // 创建并下载文件\n      if (exportFormat === 'excel') {\n        // TODO: 使用xlsx库导出Excel\n        message.info('Excel导出功能需要集成xlsx库');\n      } else {\n        // CSV导出\n        const headers = Object.keys(filteredData[0] || {});\n        const csvContent = [headers.join(','), ...filteredData.map(row => Object.values(row).join(','))].join('\\n');\n        \n        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n        const link = document.createElement('a');\n        link.href = URL.createObjectURL(blob);\n        link.download = `core_bom_export_${new Date().getTime()}.csv`;\n        link.click();\n      }\n      \n      setExportModalVisible(false);\n      message.success('导出成功');\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n  \n  // 处理文件上传\n  const handleFileUpload = (file: File) => {\n    setImportStatus('uploading');\n    setImportProgress(0);\n    \n    // 模拟上传进度\n    const interval = setInterval(() => {\n      setImportProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(interval);\n          setImportStatus('processing');\n          \n          // 模拟处理过程\n          setTimeout(() => {\n            processImportFile(file);\n          }, 1000);\n          \n          return 100;\n        }\n        return prev + 10;\n      });\n    }, 200);\n    \n    return false; // 阻止默认上传\n  };\n  \n  // 处理导入文件\n  const processImportFile = (file: File) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const content = e.target?.result as string;\n        const lines = content.split('\\n');\n        const headers = lines[0].split(',');\n        \n        const importedData = [];\n        const errors = [];\n        \n        for (let i = 1; i < lines.length; i++) {\n          if (lines[i].trim()) {\n            const values = lines[i].split(',');\n            const row: any = {};\n            \n            headers.forEach((header, index) => {\n              row[header.trim()] = values[index]?.trim();\n            });\n            \n            // 验证数据\n            if (!row['BOM编码'] || !row['BOM名称']) {\n              errors.push(`第${i + 1}行：BOM编码和名称不能为空`);\n            } else {\n              importedData.push({\n                code: row['BOM编码'],\n                name: row['BOM名称'],\n                version: row['版本'] || 'V1.0',\n                status: row['状态'] || 'draft',\n                description: row['描述'] || ''\n              });\n            }\n          }\n        }\n        \n        setImportResult({\n          total: lines.length - 1,\n          success: importedData.length,\n          errors: errors.length,\n          data: importedData,\n          errorMessages: errors\n        });\n        \n        setImportStatus(errors.length > 0 ? 'error' : 'success');\n        \n        if (errors.length === 0) {\n          message.success(`成功导入${importedData.length}条数据`);\n          // TODO: 调用API保存数据\n        } else {\n          message.warning(`导入完成，${errors.length}条数据有错误`);\n        }\n        \n      } catch (error) {\n        setImportStatus('error');\n        setImportResult({\n          total: 0,\n          success: 0,\n          errors: 1,\n          errorMessages: ['文件格式错误，请检查文件内容']\n        });\n        message.error('文件解析失败');\n      }\n    };\n    \n    reader.readAsText(file);\n  };\n  \n  // 表格行选择\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    onSelectAll: (selected: boolean, selectedRows: CoreBOM[], changeRows: CoreBOM[]) => {\n      console.log('Select all:', selected, selectedRows, changeRows);\n    },\n  };\n  \n  // 清空选择\n  const handleClearSelection = () => {\n    setSelectedRowKeys([]);\n  };\n  \n  // 导出选中项\n  const handleExportSelected = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请先选择要导出的数据');\n      return;\n    }\n    setExportModalVisible(true);\n  };\n  \n  // 模拟版本历史数据\n  const getVersionHistory = (bom: CoreBOM) => {\n    return [\n      {\n        version: 'V1.2',\n        status: 'active',\n        description: '优化物料清单，更新供应商信息',\n        createdBy: '李工程师',\n        createdAt: '2024-03-25 14:30:00',\n        changes: ['更新供应商信息', '调整物料数量', '优化成本结构']\n      },\n      {\n        version: 'V1.1',\n        status: 'frozen',\n        description: '修复物料规格错误',\n        createdBy: '王工程师',\n        createdAt: '2024-03-20 10:15:00',\n        changes: ['修正物料规格', '更新技术参数']\n      },\n      {\n        version: 'V1.0',\n        status: 'obsolete',\n        description: '初始版本',\n        createdBy: '张工程师',\n        createdAt: '2024-03-15 09:00:00',\n        changes: ['创建初始BOM结构']\n      }\n    ];\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case BOM_STATUS.DRAFT: return 'default';\n      case BOM_STATUS.ACTIVE: return 'success';\n      case BOM_STATUS.FROZEN: return 'blue';\n      case BOM_STATUS.OBSOLETE: return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case BOM_STATUS.DRAFT: return '草稿';\n      case BOM_STATUS.ACTIVE: return '激活';\n      case BOM_STATUS.FROZEN: return '冻结';\n      case BOM_STATUS.OBSOLETE: return '废弃';\n      default: return status;\n    }\n  };\n\n  // 可导出字段选项\n  const exportFieldOptions = [\n    { label: 'BOM编码', value: 'code' },\n    { label: 'BOM名称', value: 'name' },\n    { label: '版本', value: 'version' },\n    { label: '状态', value: 'status' },\n    { label: '描述', value: 'description' },\n    { label: '创建人', value: 'createdBy' },\n    { label: '创建时间', value: 'createdAt' },\n    { label: '更新时间', value: 'updatedAt' }\n  ];\n\n  const getActionMenuItems = (record: CoreBOM): MenuProps['items'] => [\n    {\n      key: 'copy',\n      icon: <CopyOutlined />,\n      label: '复制',\n      onClick: () => handleCopy(record),\n    },\n    {\n      key: 'history',\n      icon: <HistoryOutlined />,\n      label: '版本历史',\n      onClick: () => handleVersionHistory(record),\n    },\n    {\n      key: 'freeze',\n      icon: record.status === BOM_STATUS.FROZEN ? <UnlockOutlined /> : <LockOutlined />,\n      label: record.status === BOM_STATUS.FROZEN ? '解冻' : '冻结',\n      onClick: () => handleFreeze(record),\n      disabled: record.status === BOM_STATUS.DRAFT,\n    },\n  ];\n\n  const columns = [\n    {\n      title: 'BOM编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 120,\n      render: (text: string, record: CoreBOM) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: 'BOM名称',\n      dataIndex: 'name',\n      key: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '版本',\n      dataIndex: 'version',\n      key: 'version',\n      width: 80,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true,\n    },\n    {\n      title: '创建人',\n      dataIndex: 'createdBy',\n      key: 'createdBy',\n      width: 100,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: CoreBOM) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n              disabled={record.status === BOM_STATUS.FROZEN}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定要删除这个BOM吗？\"\n            onConfirm={() => handleDelete(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n                disabled={record.status !== BOM_STATUS.DRAFT}\n              />\n            </Tooltip>\n          </Popconfirm>\n          <Dropdown\n            menu={{ items: getActionMenuItems(record) }}\n            trigger={['click']}\n          >\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  // 模拟数据\n  const mockData: CoreBOM[] = [\n    {\n      id: '1',\n      name: '5G基站天线BOM',\n      code: 'ANT-5G-001',\n      version: 'V1.0',\n      description: '5G基站用高增益天线物料清单',\n      status: 'ACTIVE',\n      items: [],\n      configRules: [],\n      createdBy: 'admin',\n      createdAt: '2024-01-01T00:00:00Z',\n      updatedAt: '2024-01-15T00:00:00Z',\n    },\n    {\n      id: '2',\n      name: '4G室内天线BOM',\n      code: 'ANT-4G-002',\n      version: 'V2.1',\n      description: '4G室内覆盖天线物料清单',\n      status: 'FROZEN',\n      items: [],\n      configRules: [],\n      createdBy: 'bom_manager',\n      createdAt: '2024-01-02T00:00:00Z',\n      updatedAt: '2024-01-16T00:00:00Z',\n      frozenAt: '2024-01-16T00:00:00Z',\n      frozenBy: 'admin',\n    },\n    {\n      id: '3',\n      name: 'WiFi天线BOM',\n      code: 'ANT-WIFI-003',\n      version: 'V1.5',\n      description: 'WiFi6天线物料清单',\n      status: 'DRAFT',\n      items: [],\n      configRules: [],\n      createdBy: 'bom_manager',\n      createdAt: '2024-01-03T00:00:00Z',\n      updatedAt: '2024-01-17T00:00:00Z',\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              核心BOM管理\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<ImportOutlined />} onClick={handleImport}>\n                导入\n              </Button>\n              <Button icon={<ExportOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleCreate}>\n                新建BOM\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n          <Col xs={24} sm={12} md={8}>\n            <Search\n              placeholder=\"搜索BOM编码或名称\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Select\n              placeholder=\"状态筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              onChange={handleStatusFilter}\n              options={[\n                { label: '草稿', value: BOM_STATUS.DRAFT },\n                { label: '激活', value: BOM_STATUS.ACTIVE },\n                { label: '冻结', value: BOM_STATUS.FROZEN },\n                { label: '废弃', value: BOM_STATUS.OBSOLETE },\n              ]}\n            />\n          </Col>\n        </Row>\n\n        {selectedRowKeys.length > 0 && (\n          <div style={{ marginBottom: 16, padding: '8px 16px', backgroundColor: '#f0f2f5', borderRadius: 6 }}>\n            <span style={{ marginRight: 16 }}>已选择 {selectedRowKeys.length} 项</span>\n            <Button size=\"small\" onClick={handleClearSelection} style={{ marginRight: 8 }}>\n              清空选择\n            </Button>\n            <Button size=\"small\" type=\"primary\" icon={<DownloadOutlined />} onClick={handleExportSelected}>\n              导出选中\n            </Button>\n          </div>\n        )}\n\n        <Table\n          columns={columns}\n          dataSource={mockData}\n          loading={loading}\n          rowKey=\"id\"\n          rowSelection={rowSelection}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 复制BOM模态框 */}\n      <Modal\n        title=\"复制BOM\"\n        open={copyModalVisible}\n        onOk={handleCopyConfirm}\n        onCancel={() => setCopyModalVisible(false)}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form form={copyForm} layout=\"vertical\">\n          <Form.Item\n            name=\"name\"\n            label=\"BOM名称\"\n            rules={[{ required: true, message: '请输入BOM名称' }]}\n          >\n            <Input placeholder=\"请输入BOM名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"code\"\n            label=\"BOM编码\"\n            rules={[{ required: true, message: '请输入BOM编码' }]}\n          >\n            <Input placeholder=\"请输入BOM编码\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n      \n      {/* 版本历史模态框 */}\n      <Modal\n          title={`版本历史 - ${currentBOMForHistory?.name}`}\n          open={versionHistoryVisible}\n          onCancel={() => setVersionHistoryVisible(false)}\n          footer={[\n            <Button key=\"close\" onClick={() => setVersionHistoryVisible(false)}>\n              关闭\n            </Button>\n          ]}\n          width={800}\n        >\n          {currentBOMForHistory && (\n            <div>\n              <Descriptions\n                title=\"当前BOM信息\"\n                bordered\n                size=\"small\"\n                style={{ marginBottom: 24 }}\n              >\n                <Descriptions.Item label=\"BOM编码\">{currentBOMForHistory.code}</Descriptions.Item>\n                <Descriptions.Item label=\"BOM名称\">{currentBOMForHistory.name}</Descriptions.Item>\n                <Descriptions.Item label=\"当前版本\">{currentBOMForHistory.version}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\" span={3}>\n                  <Tag color={getStatusColor(currentBOMForHistory.status)}>\n                    {getStatusText(currentBOMForHistory.status)}\n                  </Tag>\n                </Descriptions.Item>\n              </Descriptions>\n              \n              <Divider>版本历史</Divider>\n              \n              <Timeline>\n                {getVersionHistory(currentBOMForHistory).map((version, index) => (\n                  <Timeline.Item\n                    key={index}\n                    dot={<ClockCircleOutlined style={{ fontSize: '16px' }} />}\n                    color={version.status === 'active' ? 'green' : version.status === 'frozen' ? 'blue' : 'gray'}\n                  >\n                    <div style={{ marginBottom: 16 }}>\n                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>\n                        <span style={{ fontSize: 16, fontWeight: 'bold' }}>{version.version}</span>\n                        <Tag color={getStatusColor(version.status)}>\n                          {getStatusText(version.status)}\n                        </Tag>\n                      </div>\n                      <div style={{ color: '#666', marginBottom: 8 }}>\n                        {version.createdBy} · {version.createdAt}\n                      </div>\n                      <div style={{ marginBottom: 8 }}>{version.description}</div>\n                      {version.changes && version.changes.length > 0 && (\n                        <div>\n                          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>主要变更：</div>\n                          <ul style={{ margin: 0, paddingLeft: 20 }}>\n                            {version.changes.map((change, changeIndex) => (\n                              <li key={changeIndex}>{change}</li>\n                            ))}\n                          </ul>\n                        </div>\n                      )}\n                    </div>\n                  </Timeline.Item>\n                ))}\n              </Timeline>\n            </div>\n          )}\n      </Modal>\n      \n      {/* 导出模态框 */}\n      <Modal\n          title=\"导出BOM数据\"\n          open={exportModalVisible}\n          onOk={handleExportConfirm}\n          onCancel={() => setExportModalVisible(false)}\n          width={600}\n        >\n          <Form form={exportForm} layout=\"vertical\">\n            <Alert\n              message=\"导出说明\"\n              description={selectedRowKeys.length > 0 ? `将导出已选择的 ${selectedRowKeys.length} 条BOM数据` : \"将导出所有BOM数据\"}\n              type=\"info\"\n              style={{ marginBottom: 16 }}\n            />\n            \n            <Form.Item label=\"导出格式\" name=\"format\" initialValue={exportFormat}>\n              <Radio.Group onChange={(e) => setExportFormat(e.target.value)}>\n                <Radio value=\"excel\">Excel格式 (.xlsx)</Radio>\n                <Radio value=\"csv\">CSV格式 (.csv)</Radio>\n              </Radio.Group>\n            </Form.Item>\n            \n            <Form.Item label=\"导出字段\" name=\"fields\" initialValue={exportFields}>\n              <Checkbox.Group\n                options={exportFieldOptions}\n                value={exportFields}\n                onChange={setExportFields}\n              />\n            </Form.Item>\n          </Form>\n      </Modal>\n      \n      {/* 导入模态框 */}\n      <Modal\n          title=\"导入BOM数据\"\n          open={importModalVisible}\n          onCancel={() => {\n            setImportModalVisible(false);\n            setImportStatus(null);\n            setImportProgress(0);\n            setImportResult(null);\n            importForm.resetFields();\n          }}\n          footer={[\n            <Button key=\"template\" icon={<DownloadOutlined />} onClick={handleDownloadTemplate}>\n              下载模板\n            </Button>,\n            <Button key=\"cancel\" onClick={() => {\n              setImportModalVisible(false);\n              setImportStatus(null);\n              setImportProgress(0);\n              setImportResult(null);\n              importForm.resetFields();\n            }}>\n              取消\n            </Button>\n          ]}\n          width={600}\n        >\n          <Form form={importForm} layout=\"vertical\">\n            <Alert\n              message=\"导入说明\"\n              description={\n                <div>\n                  <p>1. 请下载导入模板，按照模板格式填写数据</p>\n                  <p>2. 支持CSV格式文件导入</p>\n                  <p>3. BOM编码和名称为必填字段</p>\n                  <p>4. 导入前请确保数据格式正确</p>\n                </div>\n              }\n              type=\"info\"\n              style={{ marginBottom: 16 }}\n            />\n            \n            <Form.Item label=\"选择文件\">\n              <Upload.Dragger\n                accept=\".csv,.xlsx,.xls\"\n                beforeUpload={handleFileUpload}\n                showUploadList={false}\n                disabled={importStatus === 'uploading' || importStatus === 'processing'}\n              >\n                <p className=\"ant-upload-drag-icon\">\n                  <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">支持CSV、Excel格式文件</p>\n              </Upload.Dragger>\n            </Form.Item>\n            \n            {importStatus && (\n              <div style={{ marginTop: 16 }}>\n                {importStatus === 'uploading' && (\n                  <div>\n                    <div style={{ marginBottom: 8 }}>正在上传文件...</div>\n                    <Progress percent={importProgress} status=\"active\" />\n                  </div>\n                )}\n                \n                {importStatus === 'processing' && (\n                  <div>\n                    <div style={{ marginBottom: 8 }}>正在处理数据...</div>\n                    <Progress percent={100} status=\"active\" />\n                  </div>\n                )}\n                \n                {importStatus === 'success' && importResult && (\n                  <Alert\n                    message=\"导入成功\"\n                    description={`成功导入 ${importResult.success} 条数据`}\n                    type=\"success\"\n                    icon={<CheckCircleOutlined />}\n                  />\n                )}\n                \n                {importStatus === 'error' && importResult && (\n                  <div>\n                    <Alert\n                      message=\"导入完成，部分数据有错误\"\n                      description={`成功：${importResult.success} 条，错误：${importResult.errors} 条`}\n                      type=\"warning\"\n                      icon={<ExclamationCircleOutlined />}\n                      style={{ marginBottom: 16 }}\n                    />\n                    \n                    {importResult.errorMessages && importResult.errorMessages.length > 0 && (\n                      <div>\n                        <div style={{ fontWeight: 'bold', marginBottom: 8 }}>错误详情：</div>\n                        <div style={{ maxHeight: 200, overflow: 'auto', backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>\n                          {importResult.errorMessages.map((error: string, index: number) => (\n                            <div key={index} style={{ color: '#ff4d4f', marginBottom: 4 }}>\n                              {error}\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            )}\n          </Form>\n        </Modal>\n    </div>\n  );\n};\n\nexport default React.memo(CoreBOMListPage);\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAWC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,QAAQ,EAERC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,QAAQ,EACRC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EAEZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,gBAAgB,EAEhBC,iBAAiB,EACjBC,mBAAmB,EACnBC,yBAAyB,EACzBC,mBAAmB,QACd,mBAAmB;AAE1B,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,aAAa,EAAEC,aAAa,EAAEC,aAAa,QAAQ,6BAA6B;AAEzF,SAASC,MAAM,EAAEC,UAAU,QAAQ,iBAAiB;AACpD,SAASC,UAAU,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAM;EAAEC;AAAM,CAAC,GAAGvC,UAAU;AAC5B,MAAM;EAAEwC;AAAO,CAAC,GAAGhD,KAAK;AAExB,MAAMiD,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGvD,WAAW,CAAC,CAAC;EAC9B,MAAMwD,QAAQ,GAAGf,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEgB,QAAQ;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGjB,cAAc,CAACkB,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EAE5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACoE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAiB,IAAI,CAAC;EAClE,MAAM,CAACwE,QAAQ,CAAC,GAAG3D,IAAI,CAAC4D,OAAO,CAAC,CAAC;;EAEjC;EACA,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAiB,IAAI,CAAC;;EAEtF;EACA,MAAM,CAAC8E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAC,OAAO,CAAC;EACzD,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAW,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;EAChH,MAAM,CAACsF,cAAc,EAAEC,iBAAiB,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACwF,YAAY,EAAEC,eAAe,CAAC,GAAGzF,QAAQ,CAA0D,IAAI,CAAC;EAC/G,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAAC4F,eAAe,EAAEC,kBAAkB,CAAC,GAAG7F,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAAC8F,UAAU,CAAC,GAAGjF,IAAI,CAAC4D,OAAO,CAAC,CAAC;EACnC,MAAM,CAACsB,UAAU,CAAC,GAAGlF,IAAI,CAAC4D,OAAO,CAAC,CAAC;EAEnC1E,SAAS,CAAC,MAAM;IACdiG,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACnC,UAAU,CAACoC,OAAO,EAAEpC,UAAU,CAACqC,QAAQ,EAAElC,aAAa,EAAEE,YAAY,CAAC,CAAC;EAE1E,MAAM8B,QAAQ,GAAG/F,WAAW,CAAC,MAAM;IACjCyD,QAAQ,CAACb,aAAa,CAAC;MACrBsD,IAAI,EAAEtC,UAAU,CAACoC,OAAO;MACxBC,QAAQ,EAAErC,UAAU,CAACqC,QAAQ;MAC7BE,OAAO,EAAEpC;IACX,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACN,QAAQ,EAAEG,UAAU,CAACoC,OAAO,EAAEpC,UAAU,CAACqC,QAAQ,EAAElC,aAAa,CAAC,CAAC;EAEtE,MAAMqC,YAAY,GAAGpG,WAAW,CAAEqG,KAAa,IAAK;IAClDrC,gBAAgB,CAACqC,KAAK,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,kBAAkB,GAAGtG,WAAW,CAAEqG,KAAa,IAAK;IACxDnC,eAAe,CAACmC,KAAK,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,YAAY,GAAGvG,WAAW,CAAC,MAAM;IACrCwD,QAAQ,CAACT,MAAM,CAACyD,eAAe,CAAC;EAClC,CAAC,EAAE,CAAChD,QAAQ,CAAC,CAAC;EAEd,MAAMiD,UAAU,GAAGzG,WAAW,CAAE0G,MAAe,IAAK;IAClDlD,QAAQ,CAACT,MAAM,CAAC4D,aAAa,CAACC,OAAO,CAAC,KAAK,EAAEF,MAAM,CAACG,EAAE,CAAC,CAAC;EAC1D,CAAC,EAAE,CAACrD,QAAQ,CAAC,CAAC;EAEd,MAAMsD,UAAU,GAAG9G,WAAW,CAAE0G,MAAe,IAAK;IAClDlD,QAAQ,CAACT,MAAM,CAACgE,aAAa,CAACH,OAAO,CAAC,KAAK,EAAEF,MAAM,CAACG,EAAE,CAAC,CAAC;EAC1D,CAAC,EAAE,CAACrD,QAAQ,CAAC,CAAC;EAEd,MAAMwD,YAAY,GAAGhH,WAAW,CAAC,MAAO0G,MAAe,IAAK;IAC1D,IAAI;MACF,MAAMjD,QAAQ,CAACZ,aAAa,CAAC6D,MAAM,CAACG,EAAE,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC;MACjDvG,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;MACvBnB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdzG,OAAO,CAACyG,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC,EAAE,CAAC1D,QAAQ,EAAEsC,QAAQ,CAAC,CAAC;EAExB,MAAMqB,YAAY,GAAGpH,WAAW,CAAC,MAAO0G,MAAe,IAAK;IAC1D,IAAI;MACF,MAAMjD,QAAQ,CAACX,aAAa,CAAC4D,MAAM,CAACG,EAAE,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC;MACjDvG,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;MACvBnB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdzG,OAAO,CAACyG,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC,EAAE,CAAC1D,QAAQ,EAAEsC,QAAQ,CAAC,CAAC;EAExB,MAAMsB,UAAU,GAAGrH,WAAW,CAAE0G,MAAe,IAAK;IAClDpC,aAAa,CAACoC,MAAM,CAAC;IACrBnC,QAAQ,CAAC+C,cAAc,CAAC;MACtBC,IAAI,EAAE,GAAGb,MAAM,CAACa,IAAI,KAAK;MACzBC,IAAI,EAAE,GAAGd,MAAM,CAACc,IAAI;IACtB,CAAC,CAAC;IACFpD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC,EAAE,CAACG,QAAQ,CAAC,CAAC;EAEd,MAAMkD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMnD,QAAQ,CAACoD,cAAc,CAAC,CAAC;MAC9C;MACAjH,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;MACvB9C,mBAAmB,CAAC,KAAK,CAAC;MAC1B2B,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdzG,OAAO,CAACyG,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMS,oBAAoB,GAAIlB,MAAe,IAAK;IAChD9B,uBAAuB,CAAC8B,MAAM,CAAC;IAC/BhC,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMmD,YAAY,GAAGA,CAAA,KAAM;IACzB/C,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMgD,YAAY,GAAGA,CAAA,KAAM;IACzB9C,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM+C,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACA,MAAMC,YAAY,GAAG,CACnB;MACE,OAAO,EAAE,QAAQ;MACjB,OAAO,EAAE,OAAO;MAChB,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,OAAO;MACb,IAAI,EAAE;IACR,CAAC,CACF;;IAED;IACA,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAMI,UAAU,GAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGL,YAAY,CAACM,GAAG,CAACC,GAAG,IAAIL,MAAM,CAACR,MAAM,CAACa,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;;IAE3G;IACA,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;MAAEM,IAAI,EAAE;IAA0B,CAAC,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,QAAQ,GAAG,uBAAuB;IACvCN,IAAI,CAACO,KAAK,CAAC,CAAC;IAEZxI,OAAO,CAACwG,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMiC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMzB,MAAM,GAAG,MAAM7B,UAAU,CAAC8B,cAAc,CAAC,CAAC;;MAEhD;MACA,IAAIyB,UAAU,GAAGC,QAAQ;MACzB,IAAI1D,eAAe,CAAC2D,MAAM,GAAG,CAAC,EAAE;QAC9BF,UAAU,GAAGC,QAAQ,CAACE,MAAM,CAACC,IAAI,IAAI7D,eAAe,CAAC8D,QAAQ,CAACD,IAAI,CAAC3C,EAAE,CAAC,CAAC;MACzE;;MAEA;MACA,MAAM6C,YAAY,GAAGN,UAAU,CAACd,GAAG,CAACkB,IAAI,IAAI;QAC1C,MAAMG,YAAiB,GAAG,CAAC,CAAC;QAC5BxE,YAAY,CAACyE,OAAO,CAACC,KAAK,IAAI;UAC5B,QAAQA,KAAK;YACX,KAAK,MAAM;cACTF,YAAY,CAAC,OAAO,CAAC,GAAGH,IAAI,CAAChC,IAAI;cACjC;YACF,KAAK,MAAM;cACTmC,YAAY,CAAC,OAAO,CAAC,GAAGH,IAAI,CAACjC,IAAI;cACjC;YACF,KAAK,SAAS;cACZoC,YAAY,CAAC,IAAI,CAAC,GAAGH,IAAI,CAACM,OAAO;cACjC;YACF,KAAK,QAAQ;cACXH,YAAY,CAAC,IAAI,CAAC,GAAGI,aAAa,CAACP,IAAI,CAACQ,MAAM,CAAC;cAC/C;YACF,KAAK,aAAa;cAChBL,YAAY,CAAC,IAAI,CAAC,GAAGH,IAAI,CAACS,WAAW;cACrC;YACF,KAAK,WAAW;cACdN,YAAY,CAAC,KAAK,CAAC,GAAGH,IAAI,CAACU,SAAS;cACpC;YACF,KAAK,WAAW;cACdP,YAAY,CAAC,MAAM,CAAC,GAAG1G,UAAU,CAACuG,IAAI,CAACW,SAAS,CAAC;cACjD;YACF,KAAK,WAAW;cACdR,YAAY,CAAC,MAAM,CAAC,GAAG1G,UAAU,CAACuG,IAAI,CAACY,SAAS,CAAC;cACjD;UACJ;QACF,CAAC,CAAC;QACF,OAAOT,YAAY;MACrB,CAAC,CAAC;;MAEF;MACA,IAAI1E,YAAY,KAAK,OAAO,EAAE;QAC5B;QACAvE,OAAO,CAAC2J,IAAI,CAAC,oBAAoB,CAAC;MACpC,CAAC,MAAM;QACL;QACA,MAAMpC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACuB,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAClD,MAAMtB,UAAU,GAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGqB,YAAY,CAACpB,GAAG,CAACC,GAAG,IAAIL,MAAM,CAACR,MAAM,CAACa,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;QAE3G,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;UAAEM,IAAI,EAAE;QAA0B,CAAC,CAAC;QACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;QACrCG,IAAI,CAACM,QAAQ,GAAG,mBAAmB,IAAIqB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,MAAM;QAC7D5B,IAAI,CAACO,KAAK,CAAC,CAAC;MACd;MAEApE,qBAAqB,CAAC,KAAK,CAAC;MAC5BpE,OAAO,CAACwG,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdzG,OAAO,CAACyG,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMqD,gBAAgB,GAAIC,IAAU,IAAK;IACvCjF,eAAe,CAAC,WAAW,CAAC;IAC5BF,iBAAiB,CAAC,CAAC,CAAC;;IAEpB;IACA,MAAMoF,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCrF,iBAAiB,CAACsF,IAAI,IAAI;QACxB,IAAIA,IAAI,IAAI,GAAG,EAAE;UACfC,aAAa,CAACH,QAAQ,CAAC;UACvBlF,eAAe,CAAC,YAAY,CAAC;;UAE7B;UACAsF,UAAU,CAAC,MAAM;YACfC,iBAAiB,CAACN,IAAI,CAAC;UACzB,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,GAAG;QACZ;QACA,OAAOG,IAAI,GAAG,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIN,IAAU,IAAK;IACxC,MAAMO,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MACrB,IAAI;QAAA,IAAAC,SAAA;QACF,MAAMC,OAAO,IAAAD,SAAA,GAAGD,CAAC,CAACG,MAAM,cAAAF,SAAA,uBAARA,SAAA,CAAUG,MAAgB;QAC1C,MAAMC,KAAK,GAAGH,OAAO,CAACI,KAAK,CAAC,IAAI,CAAC;QACjC,MAAMxD,OAAO,GAAGuD,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;QAEnC,MAAMC,YAAY,GAAG,EAAE;QACvB,MAAMC,MAAM,GAAG,EAAE;QAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAAClC,MAAM,EAAEsC,CAAC,EAAE,EAAE;UACrC,IAAIJ,KAAK,CAACI,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,EAAE;YACnB,MAAMnE,MAAM,GAAG8D,KAAK,CAACI,CAAC,CAAC,CAACH,KAAK,CAAC,GAAG,CAAC;YAClC,MAAMlD,GAAQ,GAAG,CAAC,CAAC;YAEnBN,OAAO,CAAC2B,OAAO,CAAC,CAACkC,MAAM,EAAEC,KAAK,KAAK;cAAA,IAAAC,aAAA;cACjCzD,GAAG,CAACuD,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,IAAAG,aAAA,GAAGtE,MAAM,CAACqE,KAAK,CAAC,cAAAC,aAAA,uBAAbA,aAAA,CAAeH,IAAI,CAAC,CAAC;YAC5C,CAAC,CAAC;;YAEF;YACA,IAAI,CAACtD,GAAG,CAAC,OAAO,CAAC,IAAI,CAACA,GAAG,CAAC,OAAO,CAAC,EAAE;cAClCoD,MAAM,CAACM,IAAI,CAAC,IAAIL,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACxC,CAAC,MAAM;cACLF,YAAY,CAACO,IAAI,CAAC;gBAChBzE,IAAI,EAAEe,GAAG,CAAC,OAAO,CAAC;gBAClBhB,IAAI,EAAEgB,GAAG,CAAC,OAAO,CAAC;gBAClBuB,OAAO,EAAEvB,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM;gBAC5ByB,MAAM,EAAEzB,GAAG,CAAC,IAAI,CAAC,IAAI,OAAO;gBAC5B0B,WAAW,EAAE1B,GAAG,CAAC,IAAI,CAAC,IAAI;cAC5B,CAAC,CAAC;YACJ;UACF;QACF;QAEA7C,eAAe,CAAC;UACdwG,KAAK,EAAEV,KAAK,CAAClC,MAAM,GAAG,CAAC;UACvBpC,OAAO,EAAEwE,YAAY,CAACpC,MAAM;UAC5BqC,MAAM,EAAEA,MAAM,CAACrC,MAAM;UACrB6C,IAAI,EAAET,YAAY;UAClBU,aAAa,EAAET;QACjB,CAAC,CAAC;QAEFnG,eAAe,CAACmG,MAAM,CAACrC,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;QAExD,IAAIqC,MAAM,CAACrC,MAAM,KAAK,CAAC,EAAE;UACvB5I,OAAO,CAACwG,OAAO,CAAC,OAAOwE,YAAY,CAACpC,MAAM,KAAK,CAAC;UAChD;QACF,CAAC,MAAM;UACL5I,OAAO,CAAC2L,OAAO,CAAC,QAAQV,MAAM,CAACrC,MAAM,QAAQ,CAAC;QAChD;MAEF,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACd3B,eAAe,CAAC,OAAO,CAAC;QACxBE,eAAe,CAAC;UACdwG,KAAK,EAAE,CAAC;UACRhF,OAAO,EAAE,CAAC;UACVyE,MAAM,EAAE,CAAC;UACTS,aAAa,EAAE,CAAC,gBAAgB;QAClC,CAAC,CAAC;QACF1L,OAAO,CAACyG,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC;IAED6D,MAAM,CAACsB,UAAU,CAAC7B,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM8B,YAAY,GAAG;IACnB5G,eAAe;IACf6G,QAAQ,EAAGC,kBAA+B,IAAK;MAC7C7G,kBAAkB,CAAC6G,kBAAkB,CAAC;IACxC,CAAC;IACDC,WAAW,EAAEA,CAACC,QAAiB,EAAEC,YAAuB,EAAEC,UAAqB,KAAK;MAClFC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEJ,QAAQ,EAAEC,YAAY,EAAEC,UAAU,CAAC;IAChE;EACF,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjCpH,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMqH,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAItH,eAAe,CAAC2D,MAAM,KAAK,CAAC,EAAE;MAChC5I,OAAO,CAAC2L,OAAO,CAAC,YAAY,CAAC;MAC7B;IACF;IACAvH,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMoI,iBAAiB,GAAIpJ,GAAY,IAAK;IAC1C,OAAO,CACL;MACEgG,OAAO,EAAE,MAAM;MACfE,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE,gBAAgB;MAC7BC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,qBAAqB;MAChCgD,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ;IACzC,CAAC,EACD;MACErD,OAAO,EAAE,MAAM;MACfE,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,qBAAqB;MAChCgD,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ;IAC9B,CAAC,EACD;MACErD,OAAO,EAAE,MAAM;MACfE,MAAM,EAAE,UAAU;MAClBC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,qBAAqB;MAChCgD,OAAO,EAAE,CAAC,WAAW;IACvB,CAAC,CACF;EACH,CAAC;EAED,MAAMC,cAAc,GAAIpD,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAKhH,UAAU,CAACqK,KAAK;QAAE,OAAO,SAAS;MACvC,KAAKrK,UAAU,CAACsK,MAAM;QAAE,OAAO,SAAS;MACxC,KAAKtK,UAAU,CAACuK,MAAM;QAAE,OAAO,MAAM;MACrC,KAAKvK,UAAU,CAACwK,QAAQ;QAAE,OAAO,OAAO;MACxC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMzD,aAAa,GAAIC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAKhH,UAAU,CAACqK,KAAK;QAAE,OAAO,IAAI;MAClC,KAAKrK,UAAU,CAACsK,MAAM;QAAE,OAAO,IAAI;MACnC,KAAKtK,UAAU,CAACuK,MAAM;QAAE,OAAO,IAAI;MACnC,KAAKvK,UAAU,CAACwK,QAAQ;QAAE,OAAO,IAAI;MACrC;QAAS,OAAOxD,MAAM;IACxB;EACF,CAAC;;EAED;EACA,MAAMyD,kBAAkB,GAAG,CACzB;IAAEC,KAAK,EAAE,OAAO;IAAErH,KAAK,EAAE;EAAO,CAAC,EACjC;IAAEqH,KAAK,EAAE,OAAO;IAAErH,KAAK,EAAE;EAAO,CAAC,EACjC;IAAEqH,KAAK,EAAE,IAAI;IAAErH,KAAK,EAAE;EAAU,CAAC,EACjC;IAAEqH,KAAK,EAAE,IAAI;IAAErH,KAAK,EAAE;EAAS,CAAC,EAChC;IAAEqH,KAAK,EAAE,IAAI;IAAErH,KAAK,EAAE;EAAc,CAAC,EACrC;IAAEqH,KAAK,EAAE,KAAK;IAAErH,KAAK,EAAE;EAAY,CAAC,EACpC;IAAEqH,KAAK,EAAE,MAAM;IAAErH,KAAK,EAAE;EAAY,CAAC,EACrC;IAAEqH,KAAK,EAAE,MAAM;IAAErH,KAAK,EAAE;EAAY,CAAC,CACtC;EAED,MAAMsH,kBAAkB,GAAIjH,MAAe,IAAyB,CAClE;IACEkH,GAAG,EAAE,MAAM;IACXC,IAAI,eAAE1K,OAAA,CAACrB,YAAY;MAAAgM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBP,KAAK,EAAE,IAAI;IACXQ,OAAO,EAAEA,CAAA,KAAM7G,UAAU,CAACX,MAAM;EAClC,CAAC,EACD;IACEkH,GAAG,EAAE,SAAS;IACdC,IAAI,eAAE1K,OAAA,CAACf,eAAe;MAAA0L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBP,KAAK,EAAE,MAAM;IACbQ,OAAO,EAAEA,CAAA,KAAMtG,oBAAoB,CAAClB,MAAM;EAC5C,CAAC,EACD;IACEkH,GAAG,EAAE,QAAQ;IACbC,IAAI,EAAEnH,MAAM,CAACsD,MAAM,KAAKhH,UAAU,CAACuK,MAAM,gBAAGpK,OAAA,CAACnB,cAAc;MAAA8L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAG9K,OAAA,CAACpB,YAAY;MAAA+L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjFP,KAAK,EAAEhH,MAAM,CAACsD,MAAM,KAAKhH,UAAU,CAACuK,MAAM,GAAG,IAAI,GAAG,IAAI;IACxDW,OAAO,EAAEA,CAAA,KAAM9G,YAAY,CAACV,MAAM,CAAC;IACnCyH,QAAQ,EAAEzH,MAAM,CAACsD,MAAM,KAAKhH,UAAU,CAACqK;EACzC,CAAC,CACF;EAED,MAAMe,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,MAAM;IACjBV,GAAG,EAAE,MAAM;IACXW,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,IAAY,EAAE/H,MAAe,kBACpCvD,OAAA,CAAChD,MAAM;MAACuI,IAAI,EAAC,MAAM;MAACwF,OAAO,EAAEA,CAAA,KAAMpH,UAAU,CAACJ,MAAM,CAAE;MAAAgI,QAAA,EACnDD;IAAI;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAEZ,CAAC,EACD;IACEI,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,MAAM;IACjBV,GAAG,EAAE,MAAM;IACXe,QAAQ,EAAE;EACZ,CAAC,EACD;IACEN,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,SAAS;IACpBV,GAAG,EAAE,SAAS;IACdW,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBV,GAAG,EAAE,QAAQ;IACbW,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGxE,MAAc,iBACrB7G,OAAA,CAAC3C,GAAG;MAACoO,KAAK,EAAExB,cAAc,CAACpD,MAAM,CAAE;MAAA0E,QAAA,EAChC3E,aAAa,CAACC,MAAM;IAAC;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEI,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBV,GAAG,EAAE,aAAa;IAClBe,QAAQ,EAAE;EACZ,CAAC,EACD;IACEN,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBV,GAAG,EAAE,WAAW;IAChBW,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBV,GAAG,EAAE,WAAW;IAChBW,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGK,IAAY,IAAK5L,UAAU,CAAC4L,IAAI;EAC3C,CAAC,EACD;IACER,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBV,GAAG,EAAE,WAAW;IAChBW,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGK,IAAY,IAAK5L,UAAU,CAAC4L,IAAI;EAC3C,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACXT,GAAG,EAAE,QAAQ;IACbW,KAAK,EAAE,GAAG;IACVO,KAAK,EAAE,OAAgB;IACvBN,MAAM,EAAEA,CAACO,CAAM,EAAErI,MAAe,kBAC9BvD,OAAA,CAAC/C,KAAK;MAAC4O,IAAI,EAAC,OAAO;MAAAN,QAAA,gBACjBvL,OAAA,CAACnC,OAAO;QAACqN,KAAK,EAAC,cAAI;QAAAK,QAAA,eACjBvL,OAAA,CAAChD,MAAM;UACLuI,IAAI,EAAC,MAAM;UACXmF,IAAI,eAAE1K,OAAA,CAACtB,WAAW;YAAAiM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBC,OAAO,EAAEA,CAAA,KAAMpH,UAAU,CAACJ,MAAM;QAAE;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV9K,OAAA,CAACnC,OAAO;QAACqN,KAAK,EAAC,cAAI;QAAAK,QAAA,eACjBvL,OAAA,CAAChD,MAAM;UACLuI,IAAI,EAAC,MAAM;UACXmF,IAAI,eAAE1K,OAAA,CAACxB,YAAY;YAAAmM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEA,CAAA,KAAMzH,UAAU,CAACC,MAAM,CAAE;UAClCyH,QAAQ,EAAEzH,MAAM,CAACsD,MAAM,KAAKhH,UAAU,CAACuK;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV9K,OAAA,CAAC1C,UAAU;QACT4N,KAAK,EAAC,2DAAc;QACpBY,SAAS,EAAEA,CAAA,KAAMjI,YAAY,CAACN,MAAM,CAAE;QACtCwI,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAT,QAAA,eAEfvL,OAAA,CAACnC,OAAO;UAACqN,KAAK,EAAC,cAAI;UAAAK,QAAA,eACjBvL,OAAA,CAAChD,MAAM;YACLuI,IAAI,EAAC,MAAM;YACX0G,MAAM;YACNvB,IAAI,eAAE1K,OAAA,CAACvB,cAAc;cAAAkM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBE,QAAQ,EAAEzH,MAAM,CAACsD,MAAM,KAAKhH,UAAU,CAACqK;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACb9K,OAAA,CAAClC,QAAQ;QACPoO,IAAI,EAAE;UAAEC,KAAK,EAAE3B,kBAAkB,CAACjH,MAAM;QAAE,CAAE;QAC5C6I,OAAO,EAAE,CAAC,OAAO,CAAE;QAAAb,QAAA,eAEnBvL,OAAA,CAAChD,MAAM;UAACuI,IAAI,EAAC,MAAM;UAACmF,IAAI,eAAE1K,OAAA,CAAClB,YAAY;YAAA6L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAEX,CAAC,CACF;;EAED;EACA,MAAM5E,QAAmB,GAAG,CAC1B;IACExC,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,YAAY;IAClBsC,OAAO,EAAE,MAAM;IACfG,WAAW,EAAE,gBAAgB;IAC7BD,MAAM,EAAE,QAAQ;IAChBsF,KAAK,EAAE,EAAE;IACTE,WAAW,EAAE,EAAE;IACftF,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEvD,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,YAAY;IAClBsC,OAAO,EAAE,MAAM;IACfG,WAAW,EAAE,cAAc;IAC3BD,MAAM,EAAE,QAAQ;IAChBsF,KAAK,EAAE,EAAE;IACTE,WAAW,EAAE,EAAE;IACftF,SAAS,EAAE,aAAa;IACxBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE,sBAAsB;IACjCqF,QAAQ,EAAE,sBAAsB;IAChCC,QAAQ,EAAE;EACZ,CAAC,EACD;IACE7I,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,cAAc;IACpBsC,OAAO,EAAE,MAAM;IACfG,WAAW,EAAE,aAAa;IAC1BD,MAAM,EAAE,OAAO;IACfsF,KAAK,EAAE,EAAE;IACTE,WAAW,EAAE,EAAE;IACftF,SAAS,EAAE,aAAa;IACxBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF;EAED,oBACEjH,OAAA;IAAAuL,QAAA,gBACEvL,OAAA,CAAC5C,IAAI;MAAAmO,QAAA,gBACHvL,OAAA,CAACrC,GAAG;QAAC6O,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAApB,QAAA,gBACtEvL,OAAA,CAACpC,GAAG;UAAA2N,QAAA,eACFvL,OAAA,CAACC,KAAK;YAAC2M,KAAK,EAAE,CAAE;YAACF,KAAK,EAAE;cAAEG,MAAM,EAAE;YAAE,CAAE;YAAAtB,QAAA,EAAC;UAEvC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9K,OAAA,CAACpC,GAAG;UAAA2N,QAAA,eACFvL,OAAA,CAAC/C,KAAK;YAAAsO,QAAA,gBACJvL,OAAA,CAAChD,MAAM;cAAC0N,IAAI,eAAE1K,OAAA,CAAChB,cAAc;gBAAA2L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACC,OAAO,EAAEpG,YAAa;cAAA4G,QAAA,EAAC;YAEzD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9K,OAAA,CAAChD,MAAM;cAAC0N,IAAI,eAAE1K,OAAA,CAACjB,cAAc;gBAAA4L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACC,OAAO,EAAErG,YAAa;cAAA6G,QAAA,EAAC;YAEzD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9K,OAAA,CAAChD,MAAM;cAACuI,IAAI,EAAC,SAAS;cAACmF,IAAI,eAAE1K,OAAA,CAACzB,YAAY;gBAAAoM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACC,OAAO,EAAE3H,YAAa;cAAAmI,QAAA,EAAC;YAEtE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9K,OAAA,CAACrC,GAAG;QAACmP,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACJ,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAApB,QAAA,gBACjDvL,OAAA,CAACpC,GAAG;UAACmP,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA1B,QAAA,eACzBvL,OAAA,CAACE,MAAM;YACLgN,WAAW,EAAC,+CAAY;YACxBC,UAAU;YACVC,QAAQ,EAAEnK,YAAa;YACvByJ,KAAK,EAAE;cAAEtB,KAAK,EAAE;YAAO;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9K,OAAA,CAACpC,GAAG;UAACmP,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA1B,QAAA,eACzBvL,OAAA,CAAC7C,MAAM;YACL+P,WAAW,EAAC,0BAAM;YAClBC,UAAU;YACVT,KAAK,EAAE;cAAEtB,KAAK,EAAE;YAAO,CAAE;YACzB/B,QAAQ,EAAElG,kBAAmB;YAC7BkK,OAAO,EAAE,CACP;cAAE9C,KAAK,EAAE,IAAI;cAAErH,KAAK,EAAErD,UAAU,CAACqK;YAAM,CAAC,EACxC;cAAEK,KAAK,EAAE,IAAI;cAAErH,KAAK,EAAErD,UAAU,CAACsK;YAAO,CAAC,EACzC;cAAEI,KAAK,EAAE,IAAI;cAAErH,KAAK,EAAErD,UAAU,CAACuK;YAAO,CAAC,EACzC;cAAEG,KAAK,EAAE,IAAI;cAAErH,KAAK,EAAErD,UAAU,CAACwK;YAAS,CAAC;UAC3C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELtI,eAAe,CAAC2D,MAAM,GAAG,CAAC,iBACzBnG,OAAA;QAAK0M,KAAK,EAAE;UAAEC,YAAY,EAAE,EAAE;UAAEW,OAAO,EAAE,UAAU;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAE,CAAE;QAAAjC,QAAA,gBACjGvL,OAAA;UAAM0M,KAAK,EAAE;YAAEe,WAAW,EAAE;UAAG,CAAE;UAAAlC,QAAA,GAAC,qBAAI,EAAC/I,eAAe,CAAC2D,MAAM,EAAC,SAAE;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvE9K,OAAA,CAAChD,MAAM;UAAC6O,IAAI,EAAC,OAAO;UAACd,OAAO,EAAElB,oBAAqB;UAAC6C,KAAK,EAAE;YAAEe,WAAW,EAAE;UAAE,CAAE;UAAAlC,QAAA,EAAC;QAE/E;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9K,OAAA,CAAChD,MAAM;UAAC6O,IAAI,EAAC,OAAO;UAACtG,IAAI,EAAC,SAAS;UAACmF,IAAI,eAAE1K,OAAA,CAACd,gBAAgB;YAAAyL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACC,OAAO,EAAEjB,oBAAqB;UAAAyB,QAAA,EAAC;QAE/F;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eAED9K,OAAA,CAACjD,KAAK;QACJkO,OAAO,EAAEA,OAAQ;QACjByC,UAAU,EAAExH,QAAS;QACrB1F,OAAO,EAAEA,OAAQ;QACjBmN,MAAM,EAAC,IAAI;QACXvE,YAAY,EAAEA,YAAa;QAC3BwE,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBpN,UAAU,EAAE;UACVoC,OAAO,EAAEpC,UAAU,CAACoC,OAAO;UAC3BC,QAAQ,EAAErC,UAAU,CAACqC,QAAQ;UAC7BiG,KAAK,EAAEtI,UAAU,CAACsI,KAAK;UACvB+E,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACjF,KAAK,EAAEkF,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQlF,KAAK;QAC1C;MAAE;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP9K,OAAA,CAACxC,KAAK;MACJ0N,KAAK,EAAC,iBAAO;MACbgD,IAAI,EAAElN,gBAAiB;MACvBmN,IAAI,EAAE7J,iBAAkB;MACxB8J,QAAQ,EAAEA,CAAA,KAAMnN,mBAAmB,CAAC,KAAK,CAAE;MAC3C8K,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAAT,QAAA,eAEfvL,OAAA,CAACvC,IAAI;QAAC4Q,IAAI,EAAEjN,QAAS;QAACkN,MAAM,EAAC,UAAU;QAAA/C,QAAA,gBACrCvL,OAAA,CAACvC,IAAI,CAAC8Q,IAAI;UACRnK,IAAI,EAAC,MAAM;UACXmG,KAAK,EAAC,iBAAO;UACbiE,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElR,OAAO,EAAE;UAAW,CAAC,CAAE;UAAAgO,QAAA,eAEjDvL,OAAA,CAAC9C,KAAK;YAACgQ,WAAW,EAAC;UAAU;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACZ9K,OAAA,CAACvC,IAAI,CAAC8Q,IAAI;UACRnK,IAAI,EAAC,MAAM;UACXmG,KAAK,EAAC,iBAAO;UACbiE,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElR,OAAO,EAAE;UAAW,CAAC,CAAE;UAAAgO,QAAA,eAEjDvL,OAAA,CAAC9C,KAAK;YAACgQ,WAAW,EAAC;UAAU;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR9K,OAAA,CAACxC,KAAK;MACF0N,KAAK,EAAE,UAAU1J,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAE4C,IAAI,EAAG;MAC9C8J,IAAI,EAAE5M,qBAAsB;MAC5B8M,QAAQ,EAAEA,CAAA,KAAM7M,wBAAwB,CAAC,KAAK,CAAE;MAChDmN,MAAM,EAAE,cACN1O,OAAA,CAAChD,MAAM;QAAa+N,OAAO,EAAEA,CAAA,KAAMxJ,wBAAwB,CAAC,KAAK,CAAE;QAAAgK,QAAA,EAAC;MAEpE,GAFY,OAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFM,KAAK,EAAE,GAAI;MAAAG,QAAA,EAEV/J,oBAAoB,iBACnBxB,OAAA;QAAAuL,QAAA,gBACEvL,OAAA,CAAC5B,YAAY;UACX8M,KAAK,EAAC,6BAAS;UACfyD,QAAQ;UACR9C,IAAI,EAAC,OAAO;UACZa,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAApB,QAAA,gBAE5BvL,OAAA,CAAC5B,YAAY,CAACmQ,IAAI;YAAChE,KAAK,EAAC,iBAAO;YAAAgB,QAAA,EAAE/J,oBAAoB,CAAC6C;UAAI;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAChF9K,OAAA,CAAC5B,YAAY,CAACmQ,IAAI;YAAChE,KAAK,EAAC,iBAAO;YAAAgB,QAAA,EAAE/J,oBAAoB,CAAC4C;UAAI;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAChF9K,OAAA,CAAC5B,YAAY,CAACmQ,IAAI;YAAChE,KAAK,EAAC,0BAAM;YAAAgB,QAAA,EAAE/J,oBAAoB,CAACmF;UAAO;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAClF9K,OAAA,CAAC5B,YAAY,CAACmQ,IAAI;YAAChE,KAAK,EAAC,cAAI;YAACqE,IAAI,EAAE,CAAE;YAAArD,QAAA,eACpCvL,OAAA,CAAC3C,GAAG;cAACoO,KAAK,EAAExB,cAAc,CAACzI,oBAAoB,CAACqF,MAAM,CAAE;cAAA0E,QAAA,EACrD3E,aAAa,CAACpF,oBAAoB,CAACqF,MAAM;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEf9K,OAAA,CAAC9B,OAAO;UAAAqN,QAAA,EAAC;QAAI;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAEvB9K,OAAA,CAAC7B,QAAQ;UAAAoN,QAAA,EACNxB,iBAAiB,CAACvI,oBAAoB,CAAC,CAAC2D,GAAG,CAAC,CAACwB,OAAO,EAAEiC,KAAK,kBAC1D5I,OAAA,CAAC7B,QAAQ,CAACoQ,IAAI;YAEZM,GAAG,eAAE7O,OAAA,CAACV,mBAAmB;cAACoN,KAAK,EAAE;gBAAEoC,QAAQ,EAAE;cAAO;YAAE;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1DW,KAAK,EAAE9E,OAAO,CAACE,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAGF,OAAO,CAACE,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAO;YAAA0E,QAAA,eAE7FvL,OAAA;cAAK0M,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAApB,QAAA,gBAC/BvL,OAAA;gBAAK0M,KAAK,EAAE;kBAAEqC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,QAAQ;kBAAEtC,YAAY,EAAE;gBAAE,CAAE;gBAAApB,QAAA,gBACtGvL,OAAA;kBAAM0M,KAAK,EAAE;oBAAEoC,QAAQ,EAAE,EAAE;oBAAEI,UAAU,EAAE;kBAAO,CAAE;kBAAA3D,QAAA,EAAE5E,OAAO,CAACA;gBAAO;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3E9K,OAAA,CAAC3C,GAAG;kBAACoO,KAAK,EAAExB,cAAc,CAACtD,OAAO,CAACE,MAAM,CAAE;kBAAA0E,QAAA,EACxC3E,aAAa,CAACD,OAAO,CAACE,MAAM;gBAAC;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9K,OAAA;gBAAK0M,KAAK,EAAE;kBAAEjB,KAAK,EAAE,MAAM;kBAAEkB,YAAY,EAAE;gBAAE,CAAE;gBAAApB,QAAA,GAC5C5E,OAAO,CAACI,SAAS,EAAC,QAAG,EAACJ,OAAO,CAACK,SAAS;cAAA;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACN9K,OAAA;gBAAK0M,KAAK,EAAE;kBAAEC,YAAY,EAAE;gBAAE,CAAE;gBAAApB,QAAA,EAAE5E,OAAO,CAACG;cAAW;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC3DnE,OAAO,CAACqD,OAAO,IAAIrD,OAAO,CAACqD,OAAO,CAAC7D,MAAM,GAAG,CAAC,iBAC5CnG,OAAA;gBAAAuL,QAAA,gBACEvL,OAAA;kBAAK0M,KAAK,EAAE;oBAAEwC,UAAU,EAAE,MAAM;oBAAEvC,YAAY,EAAE;kBAAE,CAAE;kBAAApB,QAAA,EAAC;gBAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChE9K,OAAA;kBAAI0M,KAAK,EAAE;oBAAEG,MAAM,EAAE,CAAC;oBAAEsC,WAAW,EAAE;kBAAG,CAAE;kBAAA5D,QAAA,EACvC5E,OAAO,CAACqD,OAAO,CAAC7E,GAAG,CAAC,CAACiK,MAAM,EAAEC,WAAW,kBACvCrP,OAAA;oBAAAuL,QAAA,EAAuB6D;kBAAM,GAApBC,WAAW;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAc,CACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GAzBDlC,KAAK;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BG,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGR9K,OAAA,CAACxC,KAAK;MACF0N,KAAK,EAAC,6BAAS;MACfgD,IAAI,EAAExM,kBAAmB;MACzByM,IAAI,EAAEnI,mBAAoB;MAC1BoI,QAAQ,EAAEA,CAAA,KAAMzM,qBAAqB,CAAC,KAAK,CAAE;MAC7CyJ,KAAK,EAAE,GAAI;MAAAG,QAAA,eAEXvL,OAAA,CAACvC,IAAI;QAAC4Q,IAAI,EAAE3L,UAAW;QAAC4L,MAAM,EAAC,UAAU;QAAA/C,QAAA,gBACvCvL,OAAA,CAAC/B,KAAK;UACJV,OAAO,EAAC,0BAAM;UACduJ,WAAW,EAAEtE,eAAe,CAAC2D,MAAM,GAAG,CAAC,GAAG,WAAW3D,eAAe,CAAC2D,MAAM,SAAS,GAAG,YAAa;UACpGZ,IAAI,EAAC,MAAM;UACXmH,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG;QAAE;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEF9K,OAAA,CAACvC,IAAI,CAAC8Q,IAAI;UAAChE,KAAK,EAAC,0BAAM;UAACnG,IAAI,EAAC,QAAQ;UAACkL,YAAY,EAAExN,YAAa;UAAAyJ,QAAA,eAC/DvL,OAAA,CAAC1B,KAAK,CAACiR,KAAK;YAAClG,QAAQ,EAAGrB,CAAC,IAAKjG,eAAe,CAACiG,CAAC,CAACG,MAAM,CAACjF,KAAK,CAAE;YAAAqI,QAAA,gBAC5DvL,OAAA,CAAC1B,KAAK;cAAC4E,KAAK,EAAC,OAAO;cAAAqI,QAAA,EAAC;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C9K,OAAA,CAAC1B,KAAK;cAAC4E,KAAK,EAAC,KAAK;cAAAqI,QAAA,EAAC;YAAY;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEZ9K,OAAA,CAACvC,IAAI,CAAC8Q,IAAI;UAAChE,KAAK,EAAC,0BAAM;UAACnG,IAAI,EAAC,QAAQ;UAACkL,YAAY,EAAEtN,YAAa;UAAAuJ,QAAA,eAC/DvL,OAAA,CAAC3B,QAAQ,CAACkR,KAAK;YACblC,OAAO,EAAE/C,kBAAmB;YAC5BpH,KAAK,EAAElB,YAAa;YACpBqH,QAAQ,EAAEpH;UAAgB;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGR9K,OAAA,CAACxC,KAAK;MACF0N,KAAK,EAAC,6BAAS;MACfgD,IAAI,EAAEtM,kBAAmB;MACzBwM,QAAQ,EAAEA,CAAA,KAAM;QACdvM,qBAAqB,CAAC,KAAK,CAAC;QAC5BQ,eAAe,CAAC,IAAI,CAAC;QACrBF,iBAAiB,CAAC,CAAC,CAAC;QACpBI,eAAe,CAAC,IAAI,CAAC;QACrBI,UAAU,CAAC6M,WAAW,CAAC,CAAC;MAC1B,CAAE;MACFd,MAAM,EAAE,cACN1O,OAAA,CAAChD,MAAM;QAAgB0N,IAAI,eAAE1K,OAAA,CAACd,gBAAgB;UAAAyL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACC,OAAO,EAAEnG,sBAAuB;QAAA2G,QAAA,EAAC;MAEpF,GAFY,UAAU;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CAAC,eACT9K,OAAA,CAAChD,MAAM;QAAc+N,OAAO,EAAEA,CAAA,KAAM;UAClClJ,qBAAqB,CAAC,KAAK,CAAC;UAC5BQ,eAAe,CAAC,IAAI,CAAC;UACrBF,iBAAiB,CAAC,CAAC,CAAC;UACpBI,eAAe,CAAC,IAAI,CAAC;UACrBI,UAAU,CAAC6M,WAAW,CAAC,CAAC;QAC1B,CAAE;QAAAjE,QAAA,EAAC;MAEH,GARY,QAAQ;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQZ,CAAC,CACT;MACFM,KAAK,EAAE,GAAI;MAAAG,QAAA,eAEXvL,OAAA,CAACvC,IAAI;QAAC4Q,IAAI,EAAE1L,UAAW;QAAC2L,MAAM,EAAC,UAAU;QAAA/C,QAAA,gBACvCvL,OAAA,CAAC/B,KAAK;UACJV,OAAO,EAAC,0BAAM;UACduJ,WAAW,eACT9G,OAAA;YAAAuL,QAAA,gBACEvL,OAAA;cAAAuL,QAAA,EAAG;YAAqB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5B9K,OAAA;cAAAuL,QAAA,EAAG;YAAc;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrB9K,OAAA;cAAAuL,QAAA,EAAG;YAAgB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvB9K,OAAA;cAAAuL,QAAA,EAAG;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CACN;UACDvF,IAAI,EAAC,MAAM;UACXmH,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG;QAAE;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEF9K,OAAA,CAACvC,IAAI,CAAC8Q,IAAI;UAAChE,KAAK,EAAC,0BAAM;UAAAgB,QAAA,eACrBvL,OAAA,CAACjC,MAAM,CAAC0R,OAAO;YACbC,MAAM,EAAC,iBAAiB;YACxBC,YAAY,EAAEtI,gBAAiB;YAC/BuI,cAAc,EAAE,KAAM;YACtB5E,QAAQ,EAAE5I,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,YAAa;YAAAmJ,QAAA,gBAExEvL,OAAA;cAAG6P,SAAS,EAAC,sBAAsB;cAAAtE,QAAA,eACjCvL,OAAA,CAACb,iBAAiB;gBAACuN,KAAK,EAAE;kBAAEoC,QAAQ,EAAE,EAAE;kBAAErD,KAAK,EAAE;gBAAU;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACJ9K,OAAA;cAAG6P,SAAS,EAAC,iBAAiB;cAAAtE,QAAA,EAAC;YAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChD9K,OAAA;cAAG6P,SAAS,EAAC,iBAAiB;cAAAtE,QAAA,EAAC;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEX1I,YAAY,iBACXpC,OAAA;UAAK0M,KAAK,EAAE;YAAEoD,SAAS,EAAE;UAAG,CAAE;UAAAvE,QAAA,GAC3BnJ,YAAY,KAAK,WAAW,iBAC3BpC,OAAA;YAAAuL,QAAA,gBACEvL,OAAA;cAAK0M,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAApB,QAAA,EAAC;YAAS;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChD9K,OAAA,CAAChC,QAAQ;cAAC+R,OAAO,EAAE7N,cAAe;cAAC2E,MAAM,EAAC;YAAQ;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACN,EAEA1I,YAAY,KAAK,YAAY,iBAC5BpC,OAAA;YAAAuL,QAAA,gBACEvL,OAAA;cAAK0M,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAApB,QAAA,EAAC;YAAS;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChD9K,OAAA,CAAChC,QAAQ;cAAC+R,OAAO,EAAE,GAAI;cAAClJ,MAAM,EAAC;YAAQ;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN,EAEA1I,YAAY,KAAK,SAAS,IAAIE,YAAY,iBACzCtC,OAAA,CAAC/B,KAAK;YACJV,OAAO,EAAC,0BAAM;YACduJ,WAAW,EAAE,QAAQxE,YAAY,CAACyB,OAAO,MAAO;YAChDwB,IAAI,EAAC,SAAS;YACdmF,IAAI,eAAE1K,OAAA,CAACZ,mBAAmB;cAAAuL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CACF,EAEA1I,YAAY,KAAK,OAAO,IAAIE,YAAY,iBACvCtC,OAAA;YAAAuL,QAAA,gBACEvL,OAAA,CAAC/B,KAAK;cACJV,OAAO,EAAC,0EAAc;cACtBuJ,WAAW,EAAE,MAAMxE,YAAY,CAACyB,OAAO,SAASzB,YAAY,CAACkG,MAAM,IAAK;cACxEjD,IAAI,EAAC,SAAS;cACdmF,IAAI,eAAE1K,OAAA,CAACX,yBAAyB;gBAAAsL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACpC4B,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAG;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,EAEDxI,YAAY,CAAC2G,aAAa,IAAI3G,YAAY,CAAC2G,aAAa,CAAC9C,MAAM,GAAG,CAAC,iBAClEnG,OAAA;cAAAuL,QAAA,gBACEvL,OAAA;gBAAK0M,KAAK,EAAE;kBAAEwC,UAAU,EAAE,MAAM;kBAAEvC,YAAY,EAAE;gBAAE,CAAE;gBAAApB,QAAA,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChE9K,OAAA;gBAAK0M,KAAK,EAAE;kBAAEsD,SAAS,EAAE,GAAG;kBAAEC,QAAQ,EAAE,MAAM;kBAAE1C,eAAe,EAAE,SAAS;kBAAED,OAAO,EAAE,CAAC;kBAAEE,YAAY,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,EACvGjJ,YAAY,CAAC2G,aAAa,CAAC9D,GAAG,CAAC,CAACnB,KAAa,EAAE4E,KAAa,kBAC3D5I,OAAA;kBAAiB0M,KAAK,EAAE;oBAAEjB,KAAK,EAAE,SAAS;oBAAEkB,YAAY,EAAE;kBAAE,CAAE;kBAAApB,QAAA,EAC3DvH;gBAAK,GADE4E,KAAK;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC1K,EAAA,CAr3BID,eAAyB;EAAA,QACZrD,WAAW,EACXyC,cAAc,EACWC,cAAc,EAMrC/B,IAAI,CAAC4D,OAAO,EAeV5D,IAAI,CAAC4D,OAAO,EACZ5D,IAAI,CAAC4D,OAAO;AAAA;AAAA6O,EAAA,GAzB7B/P,eAAyB;AAu3B/B,eAAAgQ,GAAA,gBAAezT,KAAK,CAAC0T,IAAI,CAACjQ,eAAe,CAAC;AAAC,IAAA+P,EAAA,EAAAC,GAAA;AAAAE,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}