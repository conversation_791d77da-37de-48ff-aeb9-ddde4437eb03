{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\";\nimport React, { Component } from 'react';\nimport { Result, Button } from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n/**\n * 错误边界组件\n * 用于捕获子组件中的JavaScript错误，记录错误并显示降级UI\n */\nclass ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.handleReload = () => {\n      // 重置错误状态\n      this.setState({\n        hasError: false,\n        error: undefined,\n        errorInfo: undefined\n      });\n      // 刷新页面\n      window.location.reload();\n    };\n    this.handleReset = () => {\n      // 仅重置错误状态，不刷新页面\n      this.setState({\n        hasError: false,\n        error: undefined,\n        errorInfo: undefined\n      });\n    };\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // 更新state使下一次渲染能够显示降级后的UI\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // 记录错误信息\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n\n    // 可以在这里添加错误上报逻辑\n    // reportError(error, errorInfo);\n\n    this.setState({\n      error,\n      errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      // 如果有自定义的fallback UI，使用它\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // 默认的错误UI\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '50px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Result, {\n          status: \"error\",\n          title: \"\\u9875\\u9762\\u51FA\\u73B0\\u9519\\u8BEF\",\n          subTitle: \"\\u62B1\\u6B49\\uFF0C\\u9875\\u9762\\u9047\\u5230\\u4E86\\u4E00\\u4E9B\\u95EE\\u9898\\u3002\\u60A8\\u53EF\\u4EE5\\u5C1D\\u8BD5\\u5237\\u65B0\\u9875\\u9762\\u6216\\u8054\\u7CFB\\u6280\\u672F\\u652F\\u6301\\u3002\",\n          extra: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 44\n            }, this),\n            onClick: this.handleReload,\n            children: \"\\u5237\\u65B0\\u9875\\u9762\"\n          }, \"reload\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: this.handleReset,\n            children: \"\\u91CD\\u8BD5\"\n          }, \"reset\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)],\n          children: process.env.NODE_ENV === 'development' && this.state.error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'left',\n              marginTop: 20\n            },\n            children: /*#__PURE__*/_jsxDEV(\"details\", {\n              style: {\n                whiteSpace: 'pre-wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                children: \"\\u9519\\u8BEF\\u8BE6\\u60C5\\uFF08\\u5F00\\u53D1\\u6A21\\u5F0F\\uFF09\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u9519\\u8BEF\\u4FE1\\u606F\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 22\n                }, this), this.state.error.message]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u9519\\u8BEF\\u5806\\u6808\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: this.state.error.stack\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 19\n              }, this), this.state.errorInfo && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u7EC4\\u4EF6\\u5806\\u6808\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 26\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                  children: this.state.errorInfo.componentStack\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "Result", "<PERSON><PERSON>", "ReloadOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "handleReload", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "undefined", "errorInfo", "window", "location", "reload", "handleReset", "state", "getDerivedStateFromError", "componentDidCatch", "console", "render", "fallback", "style", "padding", "textAlign", "children", "status", "title", "subTitle", "extra", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "process", "env", "NODE_ENV", "marginTop", "whiteSpace", "message", "stack", "componentStack"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { Result, Button } from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\n/**\n * 错误边界组件\n * 用于捕获子组件中的JavaScript错误，记录错误并显示降级UI\n */\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    // 更新state使下一次渲染能够显示降级后的UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // 记录错误信息\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    // 可以在这里添加错误上报逻辑\n    // reportError(error, errorInfo);\n    \n    this.setState({\n      error,\n      errorInfo\n    });\n  }\n\n  handleReload = () => {\n    // 重置错误状态\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n    // 刷新页面\n    window.location.reload();\n  };\n\n  handleReset = () => {\n    // 仅重置错误状态，不刷新页面\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // 如果有自定义的fallback UI，使用它\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // 默认的错误UI\n      return (\n        <div style={{ padding: '50px', textAlign: 'center' }}>\n          <Result\n            status=\"error\"\n            title=\"页面出现错误\"\n            subTitle=\"抱歉，页面遇到了一些问题。您可以尝试刷新页面或联系技术支持。\"\n            extra={[\n              <Button type=\"primary\" icon={<ReloadOutlined />} onClick={this.handleReload} key=\"reload\">\n                刷新页面\n              </Button>,\n              <Button onClick={this.handleReset} key=\"reset\">\n                重试\n              </Button>\n            ]}\n          >\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <div style={{ textAlign: 'left', marginTop: 20 }}>\n                <details style={{ whiteSpace: 'pre-wrap' }}>\n                  <summary>错误详情（开发模式）</summary>\n                  <p><strong>错误信息：</strong>{this.state.error.message}</p>\n                  <p><strong>错误堆栈：</strong></p>\n                  <pre>{this.state.error.stack}</pre>\n                  {this.state.errorInfo && (\n                    <>\n                      <p><strong>组件堆栈：</strong></p>\n                      <pre>{this.state.errorInfo.componentStack}</pre>\n                    </>\n                  )}\n                </details>\n              </div>\n            )}\n          </Result>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAC9D,SAASC,MAAM,EAAEC,MAAM,QAAQ,MAAM;AACrC,SAASC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAanD;AACA;AACA;AACA;AACA,MAAMC,aAAa,SAASR,SAAS,CAAe;EAClDS,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAAC,KAsBfC,YAAY,GAAG,MAAM;MACnB;MACA,IAAI,CAACC,QAAQ,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEC,KAAK,EAAEC,SAAS;QAAEC,SAAS,EAAED;MAAU,CAAC,CAAC;MAC1E;MACAE,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAAA,KAEDC,WAAW,GAAG,MAAM;MAClB;MACA,IAAI,CAACR,QAAQ,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEC,KAAK,EAAEC,SAAS;QAAEC,SAAS,EAAED;MAAU,CAAC,CAAC;IAC5E,CAAC;IA/BC,IAAI,CAACM,KAAK,GAAG;MAAER,QAAQ,EAAE;IAAM,CAAC;EAClC;EAEA,OAAOS,wBAAwBA,CAACR,KAAY,EAAS;IACnD;IACA,OAAO;MAAED,QAAQ,EAAE,IAAI;MAAEC;IAAM,CAAC;EAClC;EAEAS,iBAAiBA,CAACT,KAAY,EAAEE,SAAoB,EAAE;IACpD;IACAQ,OAAO,CAACV,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEE,SAAS,CAAC;;IAEjE;IACA;;IAEA,IAAI,CAACJ,QAAQ,CAAC;MACZE,KAAK;MACLE;IACF,CAAC,CAAC;EACJ;EAcAS,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACJ,KAAK,CAACR,QAAQ,EAAE;MACvB;MACA,IAAI,IAAI,CAACH,KAAK,CAACgB,QAAQ,EAAE;QACvB,OAAO,IAAI,CAAChB,KAAK,CAACgB,QAAQ;MAC5B;;MAEA;MACA,oBACErB,OAAA;QAAKsB,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,eACnDzB,OAAA,CAACJ,MAAM;UACL8B,MAAM,EAAC,OAAO;UACdC,KAAK,EAAC,sCAAQ;UACdC,QAAQ,EAAC,sLAAgC;UACzCC,KAAK,EAAE,cACL7B,OAAA,CAACH,MAAM;YAACiC,IAAI,EAAC,SAAS;YAACC,IAAI,eAAE/B,OAAA,CAACF,cAAc;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACC,OAAO,EAAE,IAAI,CAAC9B,YAAa;YAAAmB,QAAA,EAAc;UAE1F,GAFiF,QAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEjF,CAAC,eACTnC,OAAA,CAACH,MAAM;YAACuC,OAAO,EAAE,IAAI,CAACrB,WAAY;YAAAU,QAAA,EAAa;UAE/C,GAFuC,OAAO;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEtC,CAAC,CACT;UAAAV,QAAA,EAEDY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAACvB,KAAK,CAACP,KAAK,iBACzDT,OAAA;YAAKsB,KAAK,EAAE;cAAEE,SAAS,EAAE,MAAM;cAAEgB,SAAS,EAAE;YAAG,CAAE;YAAAf,QAAA,eAC/CzB,OAAA;cAASsB,KAAK,EAAE;gBAAEmB,UAAU,EAAE;cAAW,CAAE;cAAAhB,QAAA,gBACzCzB,OAAA;gBAAAyB,QAAA,EAAS;cAAU;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC7BnC,OAAA;gBAAAyB,QAAA,gBAAGzB,OAAA;kBAAAyB,QAAA,EAAQ;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAAC,IAAI,CAACnB,KAAK,CAACP,KAAK,CAACiC,OAAO;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDnC,OAAA;gBAAAyB,QAAA,eAAGzB,OAAA;kBAAAyB,QAAA,EAAQ;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7BnC,OAAA;gBAAAyB,QAAA,EAAM,IAAI,CAACT,KAAK,CAACP,KAAK,CAACkC;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAClC,IAAI,CAACnB,KAAK,CAACL,SAAS,iBACnBX,OAAA,CAAAE,SAAA;gBAAAuB,QAAA,gBACEzB,OAAA;kBAAAyB,QAAA,eAAGzB,OAAA;oBAAAyB,QAAA,EAAQ;kBAAK;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7BnC,OAAA;kBAAAyB,QAAA,EAAM,IAAI,CAACT,KAAK,CAACL,SAAS,CAACiC;gBAAc;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eAChD,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAEV;IAEA,OAAO,IAAI,CAAC9B,KAAK,CAACoB,QAAQ;EAC5B;AACF;AAEA,eAAetB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}