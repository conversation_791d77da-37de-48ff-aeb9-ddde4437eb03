{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Typography,Input,Button,Space,List,Tag,Avatar,Statistic,Row,Col,Modal,Form,InputNumber,Select,message,Tabs,Empty,Divider,Progress,FloatButton,Drawer,Affix}from'antd';import{ScanOutlined,PlusOutlined,MinusOutlined,FilterOutlined,ReloadOutlined,WarningOutlined,CheckCircleOutlined,ExclamationCircleOutlined,BarcodeOutlined,InboxOutlined,TruckOutlined,EditOutlined,EnvironmentOutlined,UserOutlined,CalendarOutlined}from'@ant-design/icons';import{formatDate}from'../../utils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Search}=Input;const{Option}=Select;const{TabPane}=Tabs;const MobileInventoryPage=()=>{const[loading,setLoading]=useState(false);const[inventoryList,setInventoryList]=useState([]);const[operationHistory,setOperationHistory]=useState([]);const[searchKeyword,setSearchKeyword]=useState('');const[filterStatus,setFilterStatus]=useState('');const[filterCategory,setFilterCategory]=useState('');const[activeTab,setActiveTab]=useState('inventory');const[scanModalVisible,setScanModalVisible]=useState(false);const[operationModalVisible,setOperationModalVisible]=useState(false);const[filterDrawerVisible,setFilterDrawerVisible]=useState(false);const[selectedItem,setSelectedItem]=useState(null);const[operationType,setOperationType]=useState('IN');const[form]=Form.useForm();useEffect(()=>{loadInventoryData();loadOperationHistory();},[searchKeyword,filterStatus,filterCategory]);const loadInventoryData=async()=>{try{setLoading(true);// TODO: 调用API获取库存数据\n// 模拟数据\nconst mockData=[{id:'1',materialCode:'M001',materialName:'电阻 10KΩ',specification:'1/4W ±5%',unit:'个',currentStock:1500,safetyStock:1000,maxStock:5000,location:'A-01-01',warehouse:'主仓库',lastUpdateTime:'2024-12-01 14:30:00',lastOperator:'张三',status:'NORMAL',category:'电子元件',supplier:'华强电子',unitPrice:0.05,totalValue:75.0},{id:'2',materialCode:'M002',materialName:'电容 100uF',specification:'25V 电解电容',unit:'个',currentStock:50,safetyStock:200,maxStock:1000,location:'A-01-02',warehouse:'主仓库',lastUpdateTime:'2024-12-01 10:15:00',lastOperator:'李四',status:'LOW_STOCK',category:'电子元件',supplier:'华强电子',unitPrice:0.15,totalValue:7.5},{id:'3',materialCode:'M003',materialName:'PCB板',specification:'主控板 v2.1',unit:'片',currentStock:0,safetyStock:10,maxStock:100,location:'B-02-01',warehouse:'主仓库',lastUpdateTime:'2024-11-30 16:45:00',lastOperator:'王五',status:'OUT_OF_STOCK',category:'PCB',supplier:'深圳PCB厂',unitPrice:25.0,totalValue:0},{id:'4',materialCode:'M004',materialName:'螺丝',specification:'M3×8 十字螺丝',unit:'个',currentStock:8000,safetyStock:1000,maxStock:5000,location:'C-03-01',warehouse:'主仓库',lastUpdateTime:'2024-12-01 09:20:00',lastOperator:'赵六',status:'EXCESS',category:'五金件',supplier:'五金批发',unitPrice:0.02,totalValue:160.0}];setInventoryList(mockData);}catch(error){message.error('加载库存数据失败');}finally{setLoading(false);}};const loadOperationHistory=async()=>{try{// TODO: 调用API获取操作历史\nconst mockHistory=[{id:'1',type:'IN',materialCode:'M001',materialName:'电阻 10KΩ',quantity:500,operator:'张三',operationTime:'2024-12-01 14:30:00',reason:'采购入库',toLocation:'A-01-01'},{id:'2',type:'OUT',materialCode:'M002',materialName:'电容 100uF',quantity:150,operator:'李四',operationTime:'2024-12-01 10:15:00',reason:'生产领料',fromLocation:'A-01-02'},{id:'3',type:'ADJUST',materialCode:'M004',materialName:'螺丝',quantity:3000,operator:'赵六',operationTime:'2024-12-01 09:20:00',reason:'盘点调整'}];setOperationHistory(mockHistory);}catch(error){message.error('加载操作历史失败');}};const getStatusConfig=status=>{const statusMap={'NORMAL':{text:'正常',color:'success',icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{})},'LOW_STOCK':{text:'库存不足',color:'warning',icon:/*#__PURE__*/_jsx(WarningOutlined,{})},'OUT_OF_STOCK':{text:'缺货',color:'error',icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{})},'EXCESS':{text:'库存过多',color:'processing',icon:/*#__PURE__*/_jsx(InboxOutlined,{})}};return statusMap[status]||{text:status,color:'default',icon:null};};const getOperationTypeConfig=type=>{const typeMap={'IN':{text:'入库',color:'green',icon:/*#__PURE__*/_jsx(PlusOutlined,{})},'OUT':{text:'出库',color:'red',icon:/*#__PURE__*/_jsx(MinusOutlined,{})},'TRANSFER':{text:'调拨',color:'blue',icon:/*#__PURE__*/_jsx(TruckOutlined,{})},'ADJUST':{text:'调整',color:'orange',icon:/*#__PURE__*/_jsx(EditOutlined,{})}};return typeMap[type]||{text:type,color:'default',icon:null};};const getStockLevel=item=>{const{currentStock,safetyStock,maxStock}=item;if(currentStock===0)return{level:'danger',percent:0};if(currentStock<safetyStock)return{level:'warning',percent:currentStock/safetyStock*50};if(currentStock>maxStock)return{level:'exception',percent:100};return{level:'success',percent:currentStock/maxStock*100};};const handleScan=()=>{setScanModalVisible(true);// TODO: 集成扫码功能\nmessage.info('扫码功能开发中');};const handleQuickOperation=(item,type)=>{setSelectedItem(item);setOperationType(type);setOperationModalVisible(true);form.setFieldsValue({materialCode:item.materialCode,materialName:item.materialName,currentStock:item.currentStock,location:item.location});};const handleOperation=async values=>{try{// TODO: 调用API执行库存操作\nmessage.success('操作成功');setOperationModalVisible(false);form.resetFields();loadInventoryData();loadOperationHistory();}catch(error){message.error('操作失败');}};const filteredInventoryList=inventoryList.filter(item=>{const matchKeyword=!searchKeyword||item.materialCode.toLowerCase().includes(searchKeyword.toLowerCase())||item.materialName.toLowerCase().includes(searchKeyword.toLowerCase());const matchStatus=!filterStatus||item.status===filterStatus;const matchCategory=!filterCategory||item.category===filterCategory;return matchKeyword&&matchStatus&&matchCategory;});// 统计数据\nconst statistics={total:inventoryList.length,normal:inventoryList.filter(item=>item.status==='NORMAL').length,lowStock:inventoryList.filter(item=>item.status==='LOW_STOCK').length,outOfStock:inventoryList.filter(item=>item.status==='OUT_OF_STOCK').length,excess:inventoryList.filter(item=>item.status==='EXCESS').length,totalValue:inventoryList.reduce((sum,item)=>sum+item.totalValue,0)};return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',background:'#f5f5f5',minHeight:'100vh'},children:[/*#__PURE__*/_jsx(Affix,{offsetTop:0,children:/*#__PURE__*/_jsx(\"div\",{style:{background:'#fff',padding:'12px',marginBottom:'8px',borderRadius:'8px'},children:/*#__PURE__*/_jsxs(Row,{gutter:8,align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{flex:1,children:/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u7269\\u6599\\u7F16\\u7801\\u6216\\u540D\\u79F0\",allowClear:true,value:searchKeyword,onChange:e=>setSearchKeyword(e.target.value),style:{borderRadius:'20px'}})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(ScanOutlined,{}),shape:\"circle\",size:\"large\",onClick:handleScan})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(FilterOutlined,{}),shape:\"circle\",size:\"large\",onClick:()=>setFilterDrawerVisible(true)})})]})})}),/*#__PURE__*/_jsxs(Row,{gutter:8,style:{marginBottom:'12px'},children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Card,{size:\"small\",style:{textAlign:'center'},children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u5E93\\u5B58\",value:statistics.total,valueStyle:{fontSize:'18px',color:'#1890ff'},prefix:/*#__PURE__*/_jsx(InboxOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Card,{size:\"small\",style:{textAlign:'center'},children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7F3A\\u8D27\",value:statistics.outOfStock,valueStyle:{fontSize:'18px',color:'#ff4d4f'},prefix:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Card,{size:\"small\",style:{textAlign:'center'},children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E93\\u5B58\\u4E0D\\u8DB3\",value:statistics.lowStock,valueStyle:{fontSize:'18px',color:'#faad14'},prefix:/*#__PURE__*/_jsx(WarningOutlined,{})})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:8,style:{marginBottom:'12px'},children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Card,{size:\"small\",style:{textAlign:'center'},children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u4EF7\\u503C\",value:statistics.totalValue,precision:2,valueStyle:{fontSize:'16px',color:'#52c41a'},prefix:\"\\xA5\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Card,{size:\"small\",style:{textAlign:'center'},children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6B63\\u5E38\\u5E93\\u5B58\",value:statistics.normal,valueStyle:{fontSize:'16px',color:'#52c41a'},prefix:/*#__PURE__*/_jsx(CheckCircleOutlined,{})})})})]}),/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,style:{background:'#fff',borderRadius:'8px'},children:[/*#__PURE__*/_jsx(TabPane,{tab:\"\\u5E93\\u5B58\\u5217\\u8868\",children:/*#__PURE__*/_jsx(List,{loading:loading,dataSource:filteredInventoryList,renderItem:item=>{const statusConfig=getStatusConfig(item.status);const stockLevel=getStockLevel(item);return/*#__PURE__*/_jsxs(List.Item,{style:{background:'#fff',marginBottom:'8px',borderRadius:'8px',border:'1px solid #f0f0f0',padding:'12px'},children:[/*#__PURE__*/_jsx(List.Item.Meta,{avatar:/*#__PURE__*/_jsx(Avatar,{style:{backgroundColor:statusConfig.color==='success'?'#52c41a':statusConfig.color==='warning'?'#faad14':statusConfig.color==='error'?'#ff4d4f':'#1890ff'},children:statusConfig.icon}),title:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:item.materialName}),/*#__PURE__*/_jsx(Tag,{color:statusConfig.color,style:{marginLeft:'8px',fontSize:'10px'},children:statusConfig.text})]}),description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:'12px'},children:[item.materialCode,\" | \",item.specification]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Space,{size:4,style:{marginTop:'4px'},children:[/*#__PURE__*/_jsx(EnvironmentOutlined,{style:{fontSize:'10px'}}),/*#__PURE__*/_jsx(Text,{style:{fontSize:'10px'},children:item.location}),/*#__PURE__*/_jsx(Divider,{type:\"vertical\",style:{margin:'0 4px'}}),/*#__PURE__*/_jsxs(Text,{style:{fontSize:'10px'},children:[\"\\u5F53\\u524D: \",item.currentStock,\" \",item.unit]})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'6px'},children:/*#__PURE__*/_jsx(Progress,{percent:stockLevel.percent,status:stockLevel.level,size:\"small\",showInfo:false,strokeWidth:4})})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',alignItems:'flex-end'},children:[/*#__PURE__*/_jsx(Text,{strong:true,style:{fontSize:'16px',color:'#1890ff'},children:item.currentStock}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'10px'},children:item.unit}),/*#__PURE__*/_jsxs(Space,{size:4,style:{marginTop:'8px'},children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"small\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:()=>handleQuickOperation(item,'IN'),style:{fontSize:'10px',height:'24px',width:'24px'}}),/*#__PURE__*/_jsx(Button,{size:\"small\",icon:/*#__PURE__*/_jsx(MinusOutlined,{}),onClick:()=>handleQuickOperation(item,'OUT'),style:{fontSize:'10px',height:'24px',width:'24px'}}),/*#__PURE__*/_jsx(Button,{size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleQuickOperation(item,'ADJUST'),style:{fontSize:'10px',height:'24px',width:'24px'}})]})]})]});},locale:{emptyText:/*#__PURE__*/_jsx(Empty,{description:\"\\u6682\\u65E0\\u5E93\\u5B58\\u6570\\u636E\"})}})},\"inventory\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u64CD\\u4F5C\\u5386\\u53F2\",children:/*#__PURE__*/_jsx(List,{dataSource:operationHistory,renderItem:operation=>{const typeConfig=getOperationTypeConfig(operation.type);return/*#__PURE__*/_jsxs(List.Item,{style:{background:'#fff',marginBottom:'8px',borderRadius:'8px',border:'1px solid #f0f0f0',padding:'12px'},children:[/*#__PURE__*/_jsx(List.Item.Meta,{avatar:/*#__PURE__*/_jsx(Avatar,{style:{backgroundColor:typeConfig.color},children:typeConfig.icon}),title:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:operation.materialName}),/*#__PURE__*/_jsx(Tag,{color:typeConfig.color,style:{marginLeft:'8px',fontSize:'10px'},children:typeConfig.text})]}),description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:'12px'},children:[operation.materialCode,\" | \\u6570\\u91CF: \",operation.quantity]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Space,{size:4,style:{marginTop:'4px'},children:[/*#__PURE__*/_jsx(UserOutlined,{style:{fontSize:'10px'}}),/*#__PURE__*/_jsx(Text,{style:{fontSize:'10px'},children:operation.operator}),/*#__PURE__*/_jsx(Divider,{type:\"vertical\",style:{margin:'0 4px'}}),/*#__PURE__*/_jsx(CalendarOutlined,{style:{fontSize:'10px'}}),/*#__PURE__*/_jsx(Text,{style:{fontSize:'10px'},children:formatDate(operation.operationTime)})]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{style:{fontSize:'10px'},type:\"secondary\",children:[\"\\u539F\\u56E0: \",operation.reason]})]})}),/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'right'},children:/*#__PURE__*/_jsxs(Text,{strong:true,style:{fontSize:'16px',color:operation.type==='IN'?'#52c41a':operation.type==='OUT'?'#ff4d4f':'#1890ff'},children:[operation.type==='IN'?'+':operation.type==='OUT'?'-':'±',operation.quantity]})})]});},locale:{emptyText:/*#__PURE__*/_jsx(Empty,{description:\"\\u6682\\u65E0\\u64CD\\u4F5C\\u5386\\u53F2\"})}})},\"history\")]}),/*#__PURE__*/_jsxs(FloatButton.Group,{trigger:\"click\",type:\"primary\",style:{right:24},icon:/*#__PURE__*/_jsx(PlusOutlined,{}),children:[/*#__PURE__*/_jsx(FloatButton,{icon:/*#__PURE__*/_jsx(ScanOutlined,{}),tooltip:\"\\u626B\\u7801\",onClick:handleScan}),/*#__PURE__*/_jsx(FloatButton,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),tooltip:\"\\u5237\\u65B0\",onClick:loadInventoryData})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u626B\\u7801\\u67E5\\u8BE2\",open:scanModalVisible,onCancel:()=>setScanModalVisible(false),footer:null,centered:true,children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'40px 0'},children:[/*#__PURE__*/_jsx(BarcodeOutlined,{style:{fontSize:'64px',color:'#1890ff'}}),/*#__PURE__*/_jsx(\"p\",{style:{marginTop:'16px'},children:\"\\u626B\\u7801\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>setScanModalVisible(false),children:\"\\u5173\\u95ED\"})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5E93\\u5B58\".concat(operationType==='IN'?'入库':operationType==='OUT'?'出库':'调整'),open:operationModalVisible,onCancel:()=>{setOperationModalVisible(false);form.resetFields();},onOk:()=>form.submit(),okText:\"\\u786E\\u8BA4\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsxs(Form,{form:form,layout:\"vertical\",onFinish:handleOperation,children:[/*#__PURE__*/_jsx(Form.Item,{label:\"\\u7269\\u6599\\u7F16\\u7801\",name:\"materialCode\",children:/*#__PURE__*/_jsx(Input,{disabled:true})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u7269\\u6599\\u540D\\u79F0\",name:\"materialName\",children:/*#__PURE__*/_jsx(Input,{disabled:true})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5F53\\u524D\\u5E93\\u5B58\",name:\"currentStock\",children:/*#__PURE__*/_jsx(InputNumber,{disabled:true,style:{width:'100%'}})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5E93\\u4F4D\",name:\"location\",children:/*#__PURE__*/_jsx(Input,{disabled:true})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\".concat(operationType==='IN'?'入库':operationType==='OUT'?'出库':'调整',\"\\u6570\\u91CF\"),name:\"quantity\",rules:[{required:true,message:'请输入数量'}],children:/*#__PURE__*/_jsx(InputNumber,{min:operationType==='OUT'?-((selectedItem===null||selectedItem===void 0?void 0:selectedItem.currentStock)||0):undefined,style:{width:'100%'},placeholder:\"\\u8BF7\\u8F93\\u5165\\u6570\\u91CF\"})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u64CD\\u4F5C\\u539F\\u56E0\",name:\"reason\",rules:[{required:true,message:'请输入操作原因'}],children:/*#__PURE__*/_jsx(Input.TextArea,{rows:3,placeholder:\"\\u8BF7\\u8F93\\u5165\\u64CD\\u4F5C\\u539F\\u56E0\"})})]})}),/*#__PURE__*/_jsx(Drawer,{title:\"\\u7B5B\\u9009\\u6761\\u4EF6\",placement:\"bottom\",height:300,open:filterDrawerVisible,onClose:()=>setFilterDrawerVisible(false),children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5E93\\u5B58\\u72B6\\u6001\"}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u9009\\u62E9\\u72B6\\u6001\",allowClear:true,style:{width:'100%',marginTop:'8px'},value:filterStatus,onChange:setFilterStatus,children:[/*#__PURE__*/_jsx(Option,{value:\"NORMAL\",children:\"\\u6B63\\u5E38\"}),/*#__PURE__*/_jsx(Option,{value:\"LOW_STOCK\",children:\"\\u5E93\\u5B58\\u4E0D\\u8DB3\"}),/*#__PURE__*/_jsx(Option,{value:\"OUT_OF_STOCK\",children:\"\\u7F3A\\u8D27\"}),/*#__PURE__*/_jsx(Option,{value:\"EXCESS\",children:\"\\u5E93\\u5B58\\u8FC7\\u591A\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u7269\\u6599\\u5206\\u7C7B\"}),/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u9009\\u62E9\\u5206\\u7C7B\",allowClear:true,style:{width:'100%',marginTop:'8px'},value:filterCategory,onChange:setFilterCategory,children:[/*#__PURE__*/_jsx(Option,{value:\"\\u7535\\u5B50\\u5143\\u4EF6\",children:\"\\u7535\\u5B50\\u5143\\u4EF6\"}),/*#__PURE__*/_jsx(Option,{value:\"PCB\",children:\"PCB\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u4E94\\u91D1\\u4EF6\",children:\"\\u4E94\\u91D1\\u4EF6\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u5305\\u88C5\\u6750\\u6599\",children:\"\\u5305\\u88C5\\u6750\\u6599\"})]})]}),/*#__PURE__*/_jsx(Button,{type:\"primary\",block:true,onClick:()=>setFilterDrawerVisible(false),style:{marginTop:'16px'},children:\"\\u5E94\\u7528\\u7B5B\\u9009\"})]})})]});};export default MobileInventoryPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Input", "<PERSON><PERSON>", "Space", "List", "Tag", "Avatar", "Statistic", "Row", "Col", "Modal", "Form", "InputNumber", "Select", "message", "Tabs", "Empty", "Divider", "Progress", "FloatButton", "Drawer", "Affix", "ScanOutlined", "PlusOutlined", "MinusOutlined", "FilterOutlined", "ReloadOutlined", "WarningOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "BarcodeOutlined", "InboxOutlined", "TruckOutlined", "EditOutlined", "EnvironmentOutlined", "UserOutlined", "CalendarOutlined", "formatDate", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Search", "Option", "TabPane", "MobileInventoryPage", "loading", "setLoading", "inventoryList", "setInventoryList", "operationHistory", "setOperationHistory", "searchKeyword", "setSearchKeyword", "filterStatus", "setFilterStatus", "filterCategory", "setFilterCategory", "activeTab", "setActiveTab", "scanModalVisible", "setScanModalVisible", "operationModalVisible", "setOperationModalVisible", "filterDrawerVisible", "setFilterDrawerVisible", "selectedItem", "setSelectedItem", "operationType", "setOperationType", "form", "useForm", "loadInventoryData", "loadOperationHistory", "mockData", "id", "materialCode", "materialName", "specification", "unit", "currentStock", "safetyStock", "maxStock", "location", "warehouse", "lastUpdateTime", "lastOperator", "status", "category", "supplier", "unitPrice", "totalValue", "error", "mockHistory", "type", "quantity", "operator", "operationTime", "reason", "toLocation", "fromLocation", "getStatusConfig", "statusMap", "text", "color", "icon", "getOperationTypeConfig", "typeMap", "getStockLevel", "item", "level", "percent", "handleScan", "info", "handleQuickOperation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleOperation", "values", "success", "resetFields", "filteredInventoryList", "filter", "matchKeyword", "toLowerCase", "includes", "matchStatus", "matchCategory", "statistics", "total", "length", "normal", "lowStock", "outOfStock", "excess", "reduce", "sum", "style", "padding", "background", "minHeight", "children", "offsetTop", "marginBottom", "borderRadius", "gutter", "align", "flex", "placeholder", "allowClear", "value", "onChange", "e", "target", "shape", "size", "onClick", "span", "textAlign", "title", "valueStyle", "fontSize", "prefix", "precision", "active<PERSON><PERSON>", "tab", "dataSource", "renderItem", "statusConfig", "stockLevel", "<PERSON><PERSON>", "border", "Meta", "avatar", "backgroundColor", "strong", "marginLeft", "description", "marginTop", "margin", "showInfo", "strokeWidth", "display", "flexDirection", "alignItems", "height", "width", "locale", "emptyText", "operation", "typeConfig", "Group", "trigger", "right", "tooltip", "open", "onCancel", "footer", "centered", "concat", "onOk", "submit", "okText", "cancelText", "layout", "onFinish", "label", "name", "disabled", "rules", "required", "min", "undefined", "TextArea", "rows", "placement", "onClose", "direction", "block"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/mobile/MobileInventoryPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Input,\n  Button,\n  Space,\n  List,\n  Tag,\n  Badge,\n  Avatar,\n  Statistic,\n  Row,\n  Col,\n  Modal,\n  Form,\n  InputNumber,\n  Select,\n  message,\n  Tabs,\n  Empty,\n  Divider,\n  Progress,\n  Alert,\n  FloatButton,\n  Drawer,\n  Switch,\n  Radio,\n  Tooltip,\n  Affix,\n} from 'antd';\nimport {\n  SearchOutlined,\n  ScanOutlined,\n  PlusOutlined,\n  MinusOutlined,\n  HistoryOutlined,\n  FilterOutlined,\n  ReloadOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  BarcodeOutlined,\n  ShoppingCartOutlined,\n  InboxOutlined,\n  TruckOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  EditOutlined,\n  EnvironmentOutlined,\n  UserOutlined,\n  CalendarOutlined,\n} from '@ant-design/icons';\nimport { formatDate } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\n\ninterface InventoryItem {\n  id: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  unit: string;\n  currentStock: number;\n  safetyStock: number;\n  maxStock: number;\n  location: string;\n  warehouse: string;\n  lastUpdateTime: string;\n  lastOperator: string;\n  status: 'NORMAL' | 'LOW_STOCK' | 'OUT_OF_STOCK' | 'EXCESS';\n  category: string;\n  supplier: string;\n  unitPrice: number;\n  totalValue: number;\n}\n\ninterface StockOperation {\n  id: string;\n  type: 'IN' | 'OUT' | 'TRANSFER' | 'ADJUST';\n  materialCode: string;\n  materialName: string;\n  quantity: number;\n  operator: string;\n  operationTime: string;\n  reason: string;\n  fromLocation?: string;\n  toLocation?: string;\n}\n\nconst MobileInventoryPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [inventoryList, setInventoryList] = useState<InventoryItem[]>([]);\n  const [operationHistory, setOperationHistory] = useState<StockOperation[]>([]);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [filterStatus, setFilterStatus] = useState<string>('');\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [activeTab, setActiveTab] = useState('inventory');\n  const [scanModalVisible, setScanModalVisible] = useState(false);\n  const [operationModalVisible, setOperationModalVisible] = useState(false);\n  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);\n  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);\n  const [operationType, setOperationType] = useState<'IN' | 'OUT' | 'ADJUST'>('IN');\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    loadInventoryData();\n    loadOperationHistory();\n  }, [searchKeyword, filterStatus, filterCategory]);\n\n  const loadInventoryData = async () => {\n    try {\n      setLoading(true);\n      // TODO: 调用API获取库存数据\n      // 模拟数据\n      const mockData: InventoryItem[] = [\n        {\n          id: '1',\n          materialCode: 'M001',\n          materialName: '电阻 10KΩ',\n          specification: '1/4W ±5%',\n          unit: '个',\n          currentStock: 1500,\n          safetyStock: 1000,\n          maxStock: 5000,\n          location: 'A-01-01',\n          warehouse: '主仓库',\n          lastUpdateTime: '2024-12-01 14:30:00',\n          lastOperator: '张三',\n          status: 'NORMAL',\n          category: '电子元件',\n          supplier: '华强电子',\n          unitPrice: 0.05,\n          totalValue: 75.0,\n        },\n        {\n          id: '2',\n          materialCode: 'M002',\n          materialName: '电容 100uF',\n          specification: '25V 电解电容',\n          unit: '个',\n          currentStock: 50,\n          safetyStock: 200,\n          maxStock: 1000,\n          location: 'A-01-02',\n          warehouse: '主仓库',\n          lastUpdateTime: '2024-12-01 10:15:00',\n          lastOperator: '李四',\n          status: 'LOW_STOCK',\n          category: '电子元件',\n          supplier: '华强电子',\n          unitPrice: 0.15,\n          totalValue: 7.5,\n        },\n        {\n          id: '3',\n          materialCode: 'M003',\n          materialName: 'PCB板',\n          specification: '主控板 v2.1',\n          unit: '片',\n          currentStock: 0,\n          safetyStock: 10,\n          maxStock: 100,\n          location: 'B-02-01',\n          warehouse: '主仓库',\n          lastUpdateTime: '2024-11-30 16:45:00',\n          lastOperator: '王五',\n          status: 'OUT_OF_STOCK',\n          category: 'PCB',\n          supplier: '深圳PCB厂',\n          unitPrice: 25.0,\n          totalValue: 0,\n        },\n        {\n          id: '4',\n          materialCode: 'M004',\n          materialName: '螺丝',\n          specification: 'M3×8 十字螺丝',\n          unit: '个',\n          currentStock: 8000,\n          safetyStock: 1000,\n          maxStock: 5000,\n          location: 'C-03-01',\n          warehouse: '主仓库',\n          lastUpdateTime: '2024-12-01 09:20:00',\n          lastOperator: '赵六',\n          status: 'EXCESS',\n          category: '五金件',\n          supplier: '五金批发',\n          unitPrice: 0.02,\n          totalValue: 160.0,\n        },\n      ];\n      \n      setInventoryList(mockData);\n    } catch (error) {\n      message.error('加载库存数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadOperationHistory = async () => {\n    try {\n      // TODO: 调用API获取操作历史\n      const mockHistory: StockOperation[] = [\n        {\n          id: '1',\n          type: 'IN',\n          materialCode: 'M001',\n          materialName: '电阻 10KΩ',\n          quantity: 500,\n          operator: '张三',\n          operationTime: '2024-12-01 14:30:00',\n          reason: '采购入库',\n          toLocation: 'A-01-01',\n        },\n        {\n          id: '2',\n          type: 'OUT',\n          materialCode: 'M002',\n          materialName: '电容 100uF',\n          quantity: 150,\n          operator: '李四',\n          operationTime: '2024-12-01 10:15:00',\n          reason: '生产领料',\n          fromLocation: 'A-01-02',\n        },\n        {\n          id: '3',\n          type: 'ADJUST',\n          materialCode: 'M004',\n          materialName: '螺丝',\n          quantity: 3000,\n          operator: '赵六',\n          operationTime: '2024-12-01 09:20:00',\n          reason: '盘点调整',\n        },\n      ];\n      \n      setOperationHistory(mockHistory);\n    } catch (error) {\n      message.error('加载操作历史失败');\n    }\n  };\n\n  const getStatusConfig = (status: string) => {\n    const statusMap: Record<string, { text: string; color: string; icon: React.ReactNode }> = {\n      'NORMAL': { text: '正常', color: 'success', icon: <CheckCircleOutlined /> },\n      'LOW_STOCK': { text: '库存不足', color: 'warning', icon: <WarningOutlined /> },\n      'OUT_OF_STOCK': { text: '缺货', color: 'error', icon: <ExclamationCircleOutlined /> },\n      'EXCESS': { text: '库存过多', color: 'processing', icon: <InboxOutlined /> },\n    };\n    return statusMap[status] || { text: status, color: 'default', icon: null };\n  };\n\n  const getOperationTypeConfig = (type: string) => {\n    const typeMap: Record<string, { text: string; color: string; icon: React.ReactNode }> = {\n      'IN': { text: '入库', color: 'green', icon: <PlusOutlined /> },\n      'OUT': { text: '出库', color: 'red', icon: <MinusOutlined /> },\n      'TRANSFER': { text: '调拨', color: 'blue', icon: <TruckOutlined /> },\n      'ADJUST': { text: '调整', color: 'orange', icon: <EditOutlined /> },\n    };\n    return typeMap[type] || { text: type, color: 'default', icon: null };\n  };\n\n  const getStockLevel = (item: InventoryItem) => {\n    const { currentStock, safetyStock, maxStock } = item;\n    if (currentStock === 0) return { level: 'danger', percent: 0 };\n    if (currentStock < safetyStock) return { level: 'warning', percent: (currentStock / safetyStock) * 50 };\n    if (currentStock > maxStock) return { level: 'exception', percent: 100 };\n    return { level: 'success', percent: (currentStock / maxStock) * 100 };\n  };\n\n  const handleScan = () => {\n    setScanModalVisible(true);\n    // TODO: 集成扫码功能\n    message.info('扫码功能开发中');\n  };\n\n  const handleQuickOperation = (item: InventoryItem, type: 'IN' | 'OUT' | 'ADJUST') => {\n    setSelectedItem(item);\n    setOperationType(type);\n    setOperationModalVisible(true);\n    form.setFieldsValue({\n      materialCode: item.materialCode,\n      materialName: item.materialName,\n      currentStock: item.currentStock,\n      location: item.location,\n    });\n  };\n\n  const handleOperation = async (values: any) => {\n    try {\n      // TODO: 调用API执行库存操作\n      message.success('操作成功');\n      setOperationModalVisible(false);\n      form.resetFields();\n      loadInventoryData();\n      loadOperationHistory();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const filteredInventoryList = inventoryList.filter(item => {\n    const matchKeyword = !searchKeyword || \n      item.materialCode.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n      item.materialName.toLowerCase().includes(searchKeyword.toLowerCase());\n    const matchStatus = !filterStatus || item.status === filterStatus;\n    const matchCategory = !filterCategory || item.category === filterCategory;\n    return matchKeyword && matchStatus && matchCategory;\n  });\n\n  // 统计数据\n  const statistics = {\n    total: inventoryList.length,\n    normal: inventoryList.filter(item => item.status === 'NORMAL').length,\n    lowStock: inventoryList.filter(item => item.status === 'LOW_STOCK').length,\n    outOfStock: inventoryList.filter(item => item.status === 'OUT_OF_STOCK').length,\n    excess: inventoryList.filter(item => item.status === 'EXCESS').length,\n    totalValue: inventoryList.reduce((sum, item) => sum + item.totalValue, 0),\n  };\n\n  return (\n    <div style={{ padding: '8px', background: '#f5f5f5', minHeight: '100vh' }}>\n      {/* 顶部搜索栏 */}\n      <Affix offsetTop={0}>\n        <div style={{ background: '#fff', padding: '12px', marginBottom: '8px', borderRadius: '8px' }}>\n          <Row gutter={8} align=\"middle\">\n            <Col flex={1}>\n              <Search\n                placeholder=\"搜索物料编码或名称\"\n                allowClear\n                value={searchKeyword}\n                onChange={(e) => setSearchKeyword(e.target.value)}\n                style={{ borderRadius: '20px' }}\n              />\n            </Col>\n            <Col>\n              <Button\n                type=\"primary\"\n                icon={<ScanOutlined />}\n                shape=\"circle\"\n                size=\"large\"\n                onClick={handleScan}\n              />\n            </Col>\n            <Col>\n              <Button\n                icon={<FilterOutlined />}\n                shape=\"circle\"\n                size=\"large\"\n                onClick={() => setFilterDrawerVisible(true)}\n              />\n            </Col>\n          </Row>\n        </div>\n      </Affix>\n\n      {/* 统计卡片 */}\n      <Row gutter={8} style={{ marginBottom: '12px' }}>\n        <Col span={8}>\n          <Card size=\"small\" style={{ textAlign: 'center' }}>\n            <Statistic\n              title=\"总库存\"\n              value={statistics.total}\n              valueStyle={{ fontSize: '18px', color: '#1890ff' }}\n              prefix={<InboxOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card size=\"small\" style={{ textAlign: 'center' }}>\n            <Statistic\n              title=\"缺货\"\n              value={statistics.outOfStock}\n              valueStyle={{ fontSize: '18px', color: '#ff4d4f' }}\n              prefix={<ExclamationCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card size=\"small\" style={{ textAlign: 'center' }}>\n            <Statistic\n              title=\"库存不足\"\n              value={statistics.lowStock}\n              valueStyle={{ fontSize: '18px', color: '#faad14' }}\n              prefix={<WarningOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={8} style={{ marginBottom: '12px' }}>\n        <Col span={12}>\n          <Card size=\"small\" style={{ textAlign: 'center' }}>\n            <Statistic\n              title=\"总价值\"\n              value={statistics.totalValue}\n              precision={2}\n              valueStyle={{ fontSize: '16px', color: '#52c41a' }}\n              prefix=\"¥\"\n            />\n          </Card>\n        </Col>\n        <Col span={12}>\n          <Card size=\"small\" style={{ textAlign: 'center' }}>\n            <Statistic\n              title=\"正常库存\"\n              value={statistics.normal}\n              valueStyle={{ fontSize: '16px', color: '#52c41a' }}\n              prefix={<CheckCircleOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 主要内容 */}\n      <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ background: '#fff', borderRadius: '8px' }}>\n        <TabPane tab=\"库存列表\" key=\"inventory\">\n          <List\n            loading={loading}\n            dataSource={filteredInventoryList}\n            renderItem={(item) => {\n              const statusConfig = getStatusConfig(item.status);\n              const stockLevel = getStockLevel(item);\n              \n              return (\n                <List.Item\n                  style={{ \n                    background: '#fff', \n                    marginBottom: '8px', \n                    borderRadius: '8px',\n                    border: '1px solid #f0f0f0',\n                    padding: '12px'\n                  }}\n                >\n                  <List.Item.Meta\n                    avatar={\n                      <Avatar \n                        style={{ \n                          backgroundColor: statusConfig.color === 'success' ? '#52c41a' : \n                                           statusConfig.color === 'warning' ? '#faad14' :\n                                           statusConfig.color === 'error' ? '#ff4d4f' : '#1890ff'\n                        }}\n                      >\n                        {statusConfig.icon}\n                      </Avatar>\n                    }\n                    title={\n                      <div>\n                        <Text strong>{item.materialName}</Text>\n                        <Tag color={statusConfig.color} style={{ marginLeft: '8px', fontSize: '10px' }}>\n                          {statusConfig.text}\n                        </Tag>\n                      </div>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {item.materialCode} | {item.specification}\n                        </Text>\n                        <br />\n                        <Space size={4} style={{ marginTop: '4px' }}>\n                          <EnvironmentOutlined style={{ fontSize: '10px' }} />\n                          <Text style={{ fontSize: '10px' }}>{item.location}</Text>\n                          <Divider type=\"vertical\" style={{ margin: '0 4px' }} />\n                          <Text style={{ fontSize: '10px' }}>当前: {item.currentStock} {item.unit}</Text>\n                        </Space>\n                        <div style={{ marginTop: '6px' }}>\n                          <Progress\n                            percent={stockLevel.percent}\n                            status={stockLevel.level as any}\n                            size=\"small\"\n                            showInfo={false}\n                            strokeWidth={4}\n                          />\n                        </div>\n                      </div>\n                    }\n                  />\n                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>\n                    <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>\n                      {item.currentStock}\n                    </Text>\n                    <Text type=\"secondary\" style={{ fontSize: '10px' }}>\n                      {item.unit}\n                    </Text>\n                    <Space size={4} style={{ marginTop: '8px' }}>\n                      <Button\n                        type=\"primary\"\n                        size=\"small\"\n                        icon={<PlusOutlined />}\n                        onClick={() => handleQuickOperation(item, 'IN')}\n                        style={{ fontSize: '10px', height: '24px', width: '24px' }}\n                      />\n                      <Button\n                        size=\"small\"\n                        icon={<MinusOutlined />}\n                        onClick={() => handleQuickOperation(item, 'OUT')}\n                        style={{ fontSize: '10px', height: '24px', width: '24px' }}\n                      />\n                      <Button\n                        size=\"small\"\n                        icon={<EditOutlined />}\n                        onClick={() => handleQuickOperation(item, 'ADJUST')}\n                        style={{ fontSize: '10px', height: '24px', width: '24px' }}\n                      />\n                    </Space>\n                  </div>\n                </List.Item>\n              );\n            }}\n            locale={{ emptyText: <Empty description=\"暂无库存数据\" /> }}\n          />\n        </TabPane>\n        \n        <TabPane tab=\"操作历史\" key=\"history\">\n          <List\n            dataSource={operationHistory}\n            renderItem={(operation) => {\n              const typeConfig = getOperationTypeConfig(operation.type);\n              \n              return (\n                <List.Item\n                  style={{ \n                    background: '#fff', \n                    marginBottom: '8px', \n                    borderRadius: '8px',\n                    border: '1px solid #f0f0f0',\n                    padding: '12px'\n                  }}\n                >\n                  <List.Item.Meta\n                    avatar={\n                      <Avatar style={{ backgroundColor: typeConfig.color }}>\n                        {typeConfig.icon}\n                      </Avatar>\n                    }\n                    title={\n                      <div>\n                        <Text strong>{operation.materialName}</Text>\n                        <Tag color={typeConfig.color} style={{ marginLeft: '8px', fontSize: '10px' }}>\n                          {typeConfig.text}\n                        </Tag>\n                      </div>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {operation.materialCode} | 数量: {operation.quantity}\n                        </Text>\n                        <br />\n                        <Space size={4} style={{ marginTop: '4px' }}>\n                          <UserOutlined style={{ fontSize: '10px' }} />\n                          <Text style={{ fontSize: '10px' }}>{operation.operator}</Text>\n                          <Divider type=\"vertical\" style={{ margin: '0 4px' }} />\n                          <CalendarOutlined style={{ fontSize: '10px' }} />\n                          <Text style={{ fontSize: '10px' }}>{formatDate(operation.operationTime)}</Text>\n                        </Space>\n                        <br />\n                        <Text style={{ fontSize: '10px' }} type=\"secondary\">\n                          原因: {operation.reason}\n                        </Text>\n                      </div>\n                    }\n                  />\n                  <div style={{ textAlign: 'right' }}>\n                    <Text strong style={{ \n                      fontSize: '16px', \n                      color: operation.type === 'IN' ? '#52c41a' : \n                             operation.type === 'OUT' ? '#ff4d4f' : '#1890ff'\n                    }}>\n                      {operation.type === 'IN' ? '+' : operation.type === 'OUT' ? '-' : '±'}{operation.quantity}\n                    </Text>\n                  </div>\n                </List.Item>\n              );\n            }}\n            locale={{ emptyText: <Empty description=\"暂无操作历史\" /> }}\n          />\n        </TabPane>\n      </Tabs>\n\n      {/* 浮动按钮 */}\n      <FloatButton.Group\n        trigger=\"click\"\n        type=\"primary\"\n        style={{ right: 24 }}\n        icon={<PlusOutlined />}\n      >\n        <FloatButton icon={<ScanOutlined />} tooltip=\"扫码\" onClick={handleScan} />\n        <FloatButton icon={<ReloadOutlined />} tooltip=\"刷新\" onClick={loadInventoryData} />\n      </FloatButton.Group>\n\n      {/* 扫码模态框 */}\n      <Modal\n        title=\"扫码查询\"\n        open={scanModalVisible}\n        onCancel={() => setScanModalVisible(false)}\n        footer={null}\n        centered\n      >\n        <div style={{ textAlign: 'center', padding: '40px 0' }}>\n          <BarcodeOutlined style={{ fontSize: '64px', color: '#1890ff' }} />\n          <p style={{ marginTop: '16px' }}>扫码功能开发中...</p>\n          <Button type=\"primary\" onClick={() => setScanModalVisible(false)}>\n            关闭\n          </Button>\n        </div>\n      </Modal>\n\n      {/* 库存操作模态框 */}\n      <Modal\n        title={`库存${operationType === 'IN' ? '入库' : operationType === 'OUT' ? '出库' : '调整'}`}\n        open={operationModalVisible}\n        onCancel={() => {\n          setOperationModalVisible(false);\n          form.resetFields();\n        }}\n        onOk={() => form.submit()}\n        okText=\"确认\"\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleOperation}\n        >\n          <Form.Item label=\"物料编码\" name=\"materialCode\">\n            <Input disabled />\n          </Form.Item>\n          <Form.Item label=\"物料名称\" name=\"materialName\">\n            <Input disabled />\n          </Form.Item>\n          <Form.Item label=\"当前库存\" name=\"currentStock\">\n            <InputNumber disabled style={{ width: '100%' }} />\n          </Form.Item>\n          <Form.Item label=\"库位\" name=\"location\">\n            <Input disabled />\n          </Form.Item>\n          <Form.Item \n            label={`${operationType === 'IN' ? '入库' : operationType === 'OUT' ? '出库' : '调整'}数量`} \n            name=\"quantity\"\n            rules={[{ required: true, message: '请输入数量' }]}\n          >\n            <InputNumber\n               min={operationType === 'OUT' ? -(selectedItem?.currentStock || 0) : undefined}\n               style={{ width: '100%' }}\n               placeholder=\"请输入数量\"\n             />\n          </Form.Item>\n          <Form.Item \n            label=\"操作原因\" \n            name=\"reason\"\n            rules={[{ required: true, message: '请输入操作原因' }]}\n          >\n            <Input.TextArea rows={3} placeholder=\"请输入操作原因\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 筛选抽屉 */}\n      <Drawer\n        title=\"筛选条件\"\n        placement=\"bottom\"\n        height={300}\n        open={filterDrawerVisible}\n        onClose={() => setFilterDrawerVisible(false)}\n      >\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>库存状态</Text>\n            <Select\n              placeholder=\"选择状态\"\n              allowClear\n              style={{ width: '100%', marginTop: '8px' }}\n              value={filterStatus}\n              onChange={setFilterStatus}\n            >\n              <Option value=\"NORMAL\">正常</Option>\n              <Option value=\"LOW_STOCK\">库存不足</Option>\n              <Option value=\"OUT_OF_STOCK\">缺货</Option>\n              <Option value=\"EXCESS\">库存过多</Option>\n            </Select>\n          </div>\n          <div>\n            <Text strong>物料分类</Text>\n            <Select\n              placeholder=\"选择分类\"\n              allowClear\n              style={{ width: '100%', marginTop: '8px' }}\n              value={filterCategory}\n              onChange={setFilterCategory}\n            >\n              <Option value=\"电子元件\">电子元件</Option>\n              <Option value=\"PCB\">PCB</Option>\n              <Option value=\"五金件\">五金件</Option>\n              <Option value=\"包装材料\">包装材料</Option>\n            </Select>\n          </div>\n          <Button \n            type=\"primary\" \n            block \n            onClick={() => setFilterDrawerVisible(false)}\n            style={{ marginTop: '16px' }}\n          >\n            应用筛选\n          </Button>\n        </Space>\n      </Drawer>\n    </div>\n  );\n};\n\nexport default MobileInventoryPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,GAAG,CAEHC,MAAM,CACNC,SAAS,CACTC,GAAG,CACHC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,WAAW,CACXC,MAAM,CACNC,OAAO,CACPC,IAAI,CACJC,KAAK,CACLC,OAAO,CACPC,QAAQ,CAERC,WAAW,CACXC,MAAM,CAINC,KAAK,KACA,MAAM,CACb,OAEEC,YAAY,CACZC,YAAY,CACZC,aAAa,CAEbC,cAAc,CACdC,cAAc,CACdC,eAAe,CACfC,mBAAmB,CACnBC,yBAAyB,CACzBC,eAAe,CAEfC,aAAa,CACbC,aAAa,CAGbC,YAAY,CACZC,mBAAmB,CACnBC,YAAY,CACZC,gBAAgB,KACX,mBAAmB,CAC1B,OAASC,UAAU,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG3C,UAAU,CAClC,KAAM,CAAE4C,MAAO,CAAC,CAAG3C,KAAK,CACxB,KAAM,CAAE4C,MAAO,CAAC,CAAGhC,MAAM,CACzB,KAAM,CAAEiC,OAAQ,CAAC,CAAG/B,IAAI,CAmCxB,KAAM,CAAAgC,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqD,aAAa,CAAEC,gBAAgB,CAAC,CAAGtD,QAAQ,CAAkB,EAAE,CAAC,CACvE,KAAM,CAACuD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxD,QAAQ,CAAmB,EAAE,CAAC,CAC9E,KAAM,CAACyD,aAAa,CAAEC,gBAAgB,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC2D,YAAY,CAAEC,eAAe,CAAC,CAAG5D,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAAC6D,cAAc,CAAEC,iBAAiB,CAAC,CAAG9D,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAAC+D,SAAS,CAAEC,YAAY,CAAC,CAAGhE,QAAQ,CAAC,WAAW,CAAC,CACvD,KAAM,CAACiE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlE,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACmE,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CACzE,KAAM,CAACqE,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAACuE,YAAY,CAAEC,eAAe,CAAC,CAAGxE,QAAQ,CAAuB,IAAI,CAAC,CAC5E,KAAM,CAACyE,aAAa,CAAEC,gBAAgB,CAAC,CAAG1E,QAAQ,CAA0B,IAAI,CAAC,CACjF,KAAM,CAAC2E,IAAI,CAAC,CAAG7D,IAAI,CAAC8D,OAAO,CAAC,CAAC,CAE7B3E,SAAS,CAAC,IAAM,CACd4E,iBAAiB,CAAC,CAAC,CACnBC,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,CAACrB,aAAa,CAAEE,YAAY,CAAEE,cAAc,CAAC,CAAC,CAEjD,KAAM,CAAAgB,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACFzB,UAAU,CAAC,IAAI,CAAC,CAChB;AACA;AACA,KAAM,CAAA2B,QAAyB,CAAG,CAChC,CACEC,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,SAAS,CACvBC,aAAa,CAAE,UAAU,CACzBC,IAAI,CAAE,GAAG,CACTC,YAAY,CAAE,IAAI,CAClBC,WAAW,CAAE,IAAI,CACjBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,SAAS,CACnBC,SAAS,CAAE,KAAK,CAChBC,cAAc,CAAE,qBAAqB,CACrCC,YAAY,CAAE,IAAI,CAClBC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,IACd,CAAC,CACD,CACEhB,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,UAAU,CACxBC,aAAa,CAAE,UAAU,CACzBC,IAAI,CAAE,GAAG,CACTC,YAAY,CAAE,EAAE,CAChBC,WAAW,CAAE,GAAG,CAChBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,SAAS,CACnBC,SAAS,CAAE,KAAK,CAChBC,cAAc,CAAE,qBAAqB,CACrCC,YAAY,CAAE,IAAI,CAClBC,MAAM,CAAE,WAAW,CACnBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,GACd,CAAC,CACD,CACEhB,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,MAAM,CACpBC,aAAa,CAAE,UAAU,CACzBC,IAAI,CAAE,GAAG,CACTC,YAAY,CAAE,CAAC,CACfC,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,GAAG,CACbC,QAAQ,CAAE,SAAS,CACnBC,SAAS,CAAE,KAAK,CAChBC,cAAc,CAAE,qBAAqB,CACrCC,YAAY,CAAE,IAAI,CAClBC,MAAM,CAAE,cAAc,CACtBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,QAAQ,CAClBC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,CACd,CAAC,CACD,CACEhB,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,WAAW,CAC1BC,IAAI,CAAE,GAAG,CACTC,YAAY,CAAE,IAAI,CAClBC,WAAW,CAAE,IAAI,CACjBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,SAAS,CACnBC,SAAS,CAAE,KAAK,CAChBC,cAAc,CAAE,qBAAqB,CACrCC,YAAY,CAAE,IAAI,CAClBC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,KACd,CAAC,CACF,CAED1C,gBAAgB,CAACyB,QAAQ,CAAC,CAC5B,CAAE,MAAOkB,KAAK,CAAE,CACdhF,OAAO,CAACgF,KAAK,CAAC,UAAU,CAAC,CAC3B,CAAC,OAAS,CACR7C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA0B,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF;AACA,KAAM,CAAAoB,WAA6B,CAAG,CACpC,CACElB,EAAE,CAAE,GAAG,CACPmB,IAAI,CAAE,IAAI,CACVlB,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,SAAS,CACvBkB,QAAQ,CAAE,GAAG,CACbC,QAAQ,CAAE,IAAI,CACdC,aAAa,CAAE,qBAAqB,CACpCC,MAAM,CAAE,MAAM,CACdC,UAAU,CAAE,SACd,CAAC,CACD,CACExB,EAAE,CAAE,GAAG,CACPmB,IAAI,CAAE,KAAK,CACXlB,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,UAAU,CACxBkB,QAAQ,CAAE,GAAG,CACbC,QAAQ,CAAE,IAAI,CACdC,aAAa,CAAE,qBAAqB,CACpCC,MAAM,CAAE,MAAM,CACdE,YAAY,CAAE,SAChB,CAAC,CACD,CACEzB,EAAE,CAAE,GAAG,CACPmB,IAAI,CAAE,QAAQ,CACdlB,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,IAAI,CAClBkB,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,IAAI,CACdC,aAAa,CAAE,qBAAqB,CACpCC,MAAM,CAAE,MACV,CAAC,CACF,CAED/C,mBAAmB,CAAC0C,WAAW,CAAC,CAClC,CAAE,MAAOD,KAAK,CAAE,CACdhF,OAAO,CAACgF,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAC,CAED,KAAM,CAAAS,eAAe,CAAId,MAAc,EAAK,CAC1C,KAAM,CAAAe,SAAiF,CAAG,CACxF,QAAQ,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,cAAEpE,IAAA,CAACX,mBAAmB,GAAE,CAAE,CAAC,CACzE,WAAW,CAAE,CAAE6E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,cAAEpE,IAAA,CAACZ,eAAe,GAAE,CAAE,CAAC,CAC1E,cAAc,CAAE,CAAE8E,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,OAAO,CAAEC,IAAI,cAAEpE,IAAA,CAACV,yBAAyB,GAAE,CAAE,CAAC,CACnF,QAAQ,CAAE,CAAE4E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,YAAY,CAAEC,IAAI,cAAEpE,IAAA,CAACR,aAAa,GAAE,CAAE,CACzE,CAAC,CACD,MAAO,CAAAyE,SAAS,CAACf,MAAM,CAAC,EAAI,CAAEgB,IAAI,CAAEhB,MAAM,CAAEiB,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC5E,CAAC,CAED,KAAM,CAAAC,sBAAsB,CAAIZ,IAAY,EAAK,CAC/C,KAAM,CAAAa,OAA+E,CAAG,CACtF,IAAI,CAAE,CAAEJ,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,OAAO,CAAEC,IAAI,cAAEpE,IAAA,CAAChB,YAAY,GAAE,CAAE,CAAC,CAC5D,KAAK,CAAE,CAAEkF,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,KAAK,CAAEC,IAAI,cAAEpE,IAAA,CAACf,aAAa,GAAE,CAAE,CAAC,CAC5D,UAAU,CAAE,CAAEiF,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,MAAM,CAAEC,IAAI,cAAEpE,IAAA,CAACP,aAAa,GAAE,CAAE,CAAC,CAClE,QAAQ,CAAE,CAAEyE,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,QAAQ,CAAEC,IAAI,cAAEpE,IAAA,CAACN,YAAY,GAAE,CAAE,CAClE,CAAC,CACD,MAAO,CAAA4E,OAAO,CAACb,IAAI,CAAC,EAAI,CAAES,IAAI,CAAET,IAAI,CAAEU,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAK,CAAC,CACtE,CAAC,CAED,KAAM,CAAAG,aAAa,CAAIC,IAAmB,EAAK,CAC7C,KAAM,CAAE7B,YAAY,CAAEC,WAAW,CAAEC,QAAS,CAAC,CAAG2B,IAAI,CACpD,GAAI7B,YAAY,GAAK,CAAC,CAAE,MAAO,CAAE8B,KAAK,CAAE,QAAQ,CAAEC,OAAO,CAAE,CAAE,CAAC,CAC9D,GAAI/B,YAAY,CAAGC,WAAW,CAAE,MAAO,CAAE6B,KAAK,CAAE,SAAS,CAAEC,OAAO,CAAG/B,YAAY,CAAGC,WAAW,CAAI,EAAG,CAAC,CACvG,GAAID,YAAY,CAAGE,QAAQ,CAAE,MAAO,CAAE4B,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAE,GAAI,CAAC,CACxE,MAAO,CAAED,KAAK,CAAE,SAAS,CAAEC,OAAO,CAAG/B,YAAY,CAAGE,QAAQ,CAAI,GAAI,CAAC,CACvE,CAAC,CAED,KAAM,CAAA8B,UAAU,CAAGA,CAAA,GAAM,CACvBnD,mBAAmB,CAAC,IAAI,CAAC,CACzB;AACAjD,OAAO,CAACqG,IAAI,CAAC,SAAS,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAGA,CAACL,IAAmB,CAAEf,IAA6B,GAAK,CACnF3B,eAAe,CAAC0C,IAAI,CAAC,CACrBxC,gBAAgB,CAACyB,IAAI,CAAC,CACtB/B,wBAAwB,CAAC,IAAI,CAAC,CAC9BO,IAAI,CAAC6C,cAAc,CAAC,CAClBvC,YAAY,CAAEiC,IAAI,CAACjC,YAAY,CAC/BC,YAAY,CAAEgC,IAAI,CAAChC,YAAY,CAC/BG,YAAY,CAAE6B,IAAI,CAAC7B,YAAY,CAC/BG,QAAQ,CAAE0B,IAAI,CAAC1B,QACjB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAiC,eAAe,CAAG,KAAO,CAAAC,MAAW,EAAK,CAC7C,GAAI,CACF;AACAzG,OAAO,CAAC0G,OAAO,CAAC,MAAM,CAAC,CACvBvD,wBAAwB,CAAC,KAAK,CAAC,CAC/BO,IAAI,CAACiD,WAAW,CAAC,CAAC,CAClB/C,iBAAiB,CAAC,CAAC,CACnBC,oBAAoB,CAAC,CAAC,CACxB,CAAE,MAAOmB,KAAK,CAAE,CACdhF,OAAO,CAACgF,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAA4B,qBAAqB,CAAGxE,aAAa,CAACyE,MAAM,CAACZ,IAAI,EAAI,CACzD,KAAM,CAAAa,YAAY,CAAG,CAACtE,aAAa,EACjCyD,IAAI,CAACjC,YAAY,CAAC+C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxE,aAAa,CAACuE,WAAW,CAAC,CAAC,CAAC,EACrEd,IAAI,CAAChC,YAAY,CAAC8C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxE,aAAa,CAACuE,WAAW,CAAC,CAAC,CAAC,CACvE,KAAM,CAAAE,WAAW,CAAG,CAACvE,YAAY,EAAIuD,IAAI,CAACtB,MAAM,GAAKjC,YAAY,CACjE,KAAM,CAAAwE,aAAa,CAAG,CAACtE,cAAc,EAAIqD,IAAI,CAACrB,QAAQ,GAAKhC,cAAc,CACzE,MAAO,CAAAkE,YAAY,EAAIG,WAAW,EAAIC,aAAa,CACrD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAG,CACjBC,KAAK,CAAEhF,aAAa,CAACiF,MAAM,CAC3BC,MAAM,CAAElF,aAAa,CAACyE,MAAM,CAACZ,IAAI,EAAIA,IAAI,CAACtB,MAAM,GAAK,QAAQ,CAAC,CAAC0C,MAAM,CACrEE,QAAQ,CAAEnF,aAAa,CAACyE,MAAM,CAACZ,IAAI,EAAIA,IAAI,CAACtB,MAAM,GAAK,WAAW,CAAC,CAAC0C,MAAM,CAC1EG,UAAU,CAAEpF,aAAa,CAACyE,MAAM,CAACZ,IAAI,EAAIA,IAAI,CAACtB,MAAM,GAAK,cAAc,CAAC,CAAC0C,MAAM,CAC/EI,MAAM,CAAErF,aAAa,CAACyE,MAAM,CAACZ,IAAI,EAAIA,IAAI,CAACtB,MAAM,GAAK,QAAQ,CAAC,CAAC0C,MAAM,CACrEtC,UAAU,CAAE3C,aAAa,CAACsF,MAAM,CAAC,CAACC,GAAG,CAAE1B,IAAI,GAAK0B,GAAG,CAAG1B,IAAI,CAAClB,UAAU,CAAE,CAAC,CAC1E,CAAC,CAED,mBACEpD,KAAA,QAAKiG,KAAK,CAAE,CAAEC,OAAO,CAAE,KAAK,CAAEC,UAAU,CAAE,SAAS,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAAC,QAAA,eAExEvG,IAAA,CAAClB,KAAK,EAAC0H,SAAS,CAAE,CAAE,CAAAD,QAAA,cAClBvG,IAAA,QAAKmG,KAAK,CAAE,CAAEE,UAAU,CAAE,MAAM,CAAED,OAAO,CAAE,MAAM,CAAEK,YAAY,CAAE,KAAK,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAH,QAAA,cAC5FrG,KAAA,CAACjC,GAAG,EAAC0I,MAAM,CAAE,CAAE,CAACC,KAAK,CAAC,QAAQ,CAAAL,QAAA,eAC5BvG,IAAA,CAAC9B,GAAG,EAAC2I,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXvG,IAAA,CAACK,MAAM,EACLyG,WAAW,CAAC,wDAAW,CACvBC,UAAU,MACVC,KAAK,CAAEjG,aAAc,CACrBkG,QAAQ,CAAGC,CAAC,EAAKlG,gBAAgB,CAACkG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClDb,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CACjC,CAAC,CACC,CAAC,cACN1G,IAAA,CAAC9B,GAAG,EAAAqI,QAAA,cACFvG,IAAA,CAACrC,MAAM,EACL8F,IAAI,CAAC,SAAS,CACdW,IAAI,cAAEpE,IAAA,CAACjB,YAAY,GAAE,CAAE,CACvBqI,KAAK,CAAC,QAAQ,CACdC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAE3C,UAAW,CACrB,CAAC,CACC,CAAC,cACN3E,IAAA,CAAC9B,GAAG,EAAAqI,QAAA,cACFvG,IAAA,CAACrC,MAAM,EACLyG,IAAI,cAAEpE,IAAA,CAACd,cAAc,GAAE,CAAE,CACzBkI,KAAK,CAAC,QAAQ,CACdC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAM1F,sBAAsB,CAAC,IAAI,CAAE,CAC7C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACD,CAAC,cAGR1B,KAAA,CAACjC,GAAG,EAAC0I,MAAM,CAAE,CAAE,CAACR,KAAK,CAAE,CAAEM,YAAY,CAAE,MAAO,CAAE,CAAAF,QAAA,eAC9CvG,IAAA,CAAC9B,GAAG,EAACqJ,IAAI,CAAE,CAAE,CAAAhB,QAAA,cACXvG,IAAA,CAACxC,IAAI,EAAC6J,IAAI,CAAC,OAAO,CAAClB,KAAK,CAAE,CAAEqB,SAAS,CAAE,QAAS,CAAE,CAAAjB,QAAA,cAChDvG,IAAA,CAAChC,SAAS,EACRyJ,KAAK,CAAC,oBAAK,CACXT,KAAK,CAAEtB,UAAU,CAACC,KAAM,CACxB+B,UAAU,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAExD,KAAK,CAAE,SAAU,CAAE,CACnDyD,MAAM,cAAE5H,IAAA,CAACR,aAAa,GAAE,CAAE,CAC3B,CAAC,CACE,CAAC,CACJ,CAAC,cACNQ,IAAA,CAAC9B,GAAG,EAACqJ,IAAI,CAAE,CAAE,CAAAhB,QAAA,cACXvG,IAAA,CAACxC,IAAI,EAAC6J,IAAI,CAAC,OAAO,CAAClB,KAAK,CAAE,CAAEqB,SAAS,CAAE,QAAS,CAAE,CAAAjB,QAAA,cAChDvG,IAAA,CAAChC,SAAS,EACRyJ,KAAK,CAAC,cAAI,CACVT,KAAK,CAAEtB,UAAU,CAACK,UAAW,CAC7B2B,UAAU,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAExD,KAAK,CAAE,SAAU,CAAE,CACnDyD,MAAM,cAAE5H,IAAA,CAACV,yBAAyB,GAAE,CAAE,CACvC,CAAC,CACE,CAAC,CACJ,CAAC,cACNU,IAAA,CAAC9B,GAAG,EAACqJ,IAAI,CAAE,CAAE,CAAAhB,QAAA,cACXvG,IAAA,CAACxC,IAAI,EAAC6J,IAAI,CAAC,OAAO,CAAClB,KAAK,CAAE,CAAEqB,SAAS,CAAE,QAAS,CAAE,CAAAjB,QAAA,cAChDvG,IAAA,CAAChC,SAAS,EACRyJ,KAAK,CAAC,0BAAM,CACZT,KAAK,CAAEtB,UAAU,CAACI,QAAS,CAC3B4B,UAAU,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAExD,KAAK,CAAE,SAAU,CAAE,CACnDyD,MAAM,cAAE5H,IAAA,CAACZ,eAAe,GAAE,CAAE,CAC7B,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAENc,KAAA,CAACjC,GAAG,EAAC0I,MAAM,CAAE,CAAE,CAACR,KAAK,CAAE,CAAEM,YAAY,CAAE,MAAO,CAAE,CAAAF,QAAA,eAC9CvG,IAAA,CAAC9B,GAAG,EAACqJ,IAAI,CAAE,EAAG,CAAAhB,QAAA,cACZvG,IAAA,CAACxC,IAAI,EAAC6J,IAAI,CAAC,OAAO,CAAClB,KAAK,CAAE,CAAEqB,SAAS,CAAE,QAAS,CAAE,CAAAjB,QAAA,cAChDvG,IAAA,CAAChC,SAAS,EACRyJ,KAAK,CAAC,oBAAK,CACXT,KAAK,CAAEtB,UAAU,CAACpC,UAAW,CAC7BuE,SAAS,CAAE,CAAE,CACbH,UAAU,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAExD,KAAK,CAAE,SAAU,CAAE,CACnDyD,MAAM,CAAC,MAAG,CACX,CAAC,CACE,CAAC,CACJ,CAAC,cACN5H,IAAA,CAAC9B,GAAG,EAACqJ,IAAI,CAAE,EAAG,CAAAhB,QAAA,cACZvG,IAAA,CAACxC,IAAI,EAAC6J,IAAI,CAAC,OAAO,CAAClB,KAAK,CAAE,CAAEqB,SAAS,CAAE,QAAS,CAAE,CAAAjB,QAAA,cAChDvG,IAAA,CAAChC,SAAS,EACRyJ,KAAK,CAAC,0BAAM,CACZT,KAAK,CAAEtB,UAAU,CAACG,MAAO,CACzB6B,UAAU,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAExD,KAAK,CAAE,SAAU,CAAE,CACnDyD,MAAM,cAAE5H,IAAA,CAACX,mBAAmB,GAAE,CAAE,CACjC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGNa,KAAA,CAAC1B,IAAI,EAACsJ,SAAS,CAAEzG,SAAU,CAAC4F,QAAQ,CAAE3F,YAAa,CAAC6E,KAAK,CAAE,CAAEE,UAAU,CAAE,MAAM,CAAEK,YAAY,CAAE,KAAM,CAAE,CAAAH,QAAA,eACrGvG,IAAA,CAACO,OAAO,EAACwH,GAAG,CAAC,0BAAM,CAAAxB,QAAA,cACjBvG,IAAA,CAACnC,IAAI,EACH4C,OAAO,CAAEA,OAAQ,CACjBuH,UAAU,CAAE7C,qBAAsB,CAClC8C,UAAU,CAAGzD,IAAI,EAAK,CACpB,KAAM,CAAA0D,YAAY,CAAGlE,eAAe,CAACQ,IAAI,CAACtB,MAAM,CAAC,CACjD,KAAM,CAAAiF,UAAU,CAAG5D,aAAa,CAACC,IAAI,CAAC,CAEtC,mBACEtE,KAAA,CAACrC,IAAI,CAACuK,IAAI,EACRjC,KAAK,CAAE,CACLE,UAAU,CAAE,MAAM,CAClBI,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,KAAK,CACnB2B,MAAM,CAAE,mBAAmB,CAC3BjC,OAAO,CAAE,MACX,CAAE,CAAAG,QAAA,eAEFvG,IAAA,CAACnC,IAAI,CAACuK,IAAI,CAACE,IAAI,EACbC,MAAM,cACJvI,IAAA,CAACjC,MAAM,EACLoI,KAAK,CAAE,CACLqC,eAAe,CAAEN,YAAY,CAAC/D,KAAK,GAAK,SAAS,CAAG,SAAS,CAC5C+D,YAAY,CAAC/D,KAAK,GAAK,SAAS,CAAG,SAAS,CAC5C+D,YAAY,CAAC/D,KAAK,GAAK,OAAO,CAAG,SAAS,CAAG,SAChE,CAAE,CAAAoC,QAAA,CAED2B,YAAY,CAAC9D,IAAI,CACZ,CACT,CACDqD,KAAK,cACHvH,KAAA,QAAAqG,QAAA,eACEvG,IAAA,CAACI,IAAI,EAACqI,MAAM,MAAAlC,QAAA,CAAE/B,IAAI,CAAChC,YAAY,CAAO,CAAC,cACvCxC,IAAA,CAAClC,GAAG,EAACqG,KAAK,CAAE+D,YAAY,CAAC/D,KAAM,CAACgC,KAAK,CAAE,CAAEuC,UAAU,CAAE,KAAK,CAAEf,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,CAC5E2B,YAAY,CAAChE,IAAI,CACf,CAAC,EACH,CACN,CACDyE,WAAW,cACTzI,KAAA,QAAAqG,QAAA,eACErG,KAAA,CAACE,IAAI,EAACqD,IAAI,CAAC,WAAW,CAAC0C,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,EAChD/B,IAAI,CAACjC,YAAY,CAAC,KAAG,CAACiC,IAAI,CAAC/B,aAAa,EACrC,CAAC,cACPzC,IAAA,QAAK,CAAC,cACNE,KAAA,CAACtC,KAAK,EAACyJ,IAAI,CAAE,CAAE,CAAClB,KAAK,CAAE,CAAEyC,SAAS,CAAE,KAAM,CAAE,CAAArC,QAAA,eAC1CvG,IAAA,CAACL,mBAAmB,EAACwG,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAC,cACpD3H,IAAA,CAACI,IAAI,EAAC+F,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,CAAE/B,IAAI,CAAC1B,QAAQ,CAAO,CAAC,cACzD9C,IAAA,CAACtB,OAAO,EAAC+E,IAAI,CAAC,UAAU,CAAC0C,KAAK,CAAE,CAAE0C,MAAM,CAAE,OAAQ,CAAE,CAAE,CAAC,cACvD3I,KAAA,CAACE,IAAI,EAAC+F,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,EAAC,gBAAI,CAAC/B,IAAI,CAAC7B,YAAY,CAAC,GAAC,CAAC6B,IAAI,CAAC9B,IAAI,EAAO,CAAC,EACxE,CAAC,cACR1C,IAAA,QAAKmG,KAAK,CAAE,CAAEyC,SAAS,CAAE,KAAM,CAAE,CAAArC,QAAA,cAC/BvG,IAAA,CAACrB,QAAQ,EACP+F,OAAO,CAAEyD,UAAU,CAACzD,OAAQ,CAC5BxB,MAAM,CAAEiF,UAAU,CAAC1D,KAAa,CAChC4C,IAAI,CAAC,OAAO,CACZyB,QAAQ,CAAE,KAAM,CAChBC,WAAW,CAAE,CAAE,CAChB,CAAC,CACC,CAAC,EACH,CACN,CACF,CAAC,cACF7I,KAAA,QAAKiG,KAAK,CAAE,CAAE6C,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,UAAU,CAAE,UAAW,CAAE,CAAA3C,QAAA,eAC/EvG,IAAA,CAACI,IAAI,EAACqI,MAAM,MAACtC,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAM,CAAExD,KAAK,CAAE,SAAU,CAAE,CAAAoC,QAAA,CACxD/B,IAAI,CAAC7B,YAAY,CACd,CAAC,cACP3C,IAAA,CAACI,IAAI,EAACqD,IAAI,CAAC,WAAW,CAAC0C,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,CAChD/B,IAAI,CAAC9B,IAAI,CACN,CAAC,cACPxC,KAAA,CAACtC,KAAK,EAACyJ,IAAI,CAAE,CAAE,CAAClB,KAAK,CAAE,CAAEyC,SAAS,CAAE,KAAM,CAAE,CAAArC,QAAA,eAC1CvG,IAAA,CAACrC,MAAM,EACL8F,IAAI,CAAC,SAAS,CACd4D,IAAI,CAAC,OAAO,CACZjD,IAAI,cAAEpE,IAAA,CAAChB,YAAY,GAAE,CAAE,CACvBsI,OAAO,CAAEA,CAAA,GAAMzC,oBAAoB,CAACL,IAAI,CAAE,IAAI,CAAE,CAChD2B,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAM,CAAEwB,MAAM,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAC5D,CAAC,cACFpJ,IAAA,CAACrC,MAAM,EACL0J,IAAI,CAAC,OAAO,CACZjD,IAAI,cAAEpE,IAAA,CAACf,aAAa,GAAE,CAAE,CACxBqI,OAAO,CAAEA,CAAA,GAAMzC,oBAAoB,CAACL,IAAI,CAAE,KAAK,CAAE,CACjD2B,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAM,CAAEwB,MAAM,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAC5D,CAAC,cACFpJ,IAAA,CAACrC,MAAM,EACL0J,IAAI,CAAC,OAAO,CACZjD,IAAI,cAAEpE,IAAA,CAACN,YAAY,GAAE,CAAE,CACvB4H,OAAO,CAAEA,CAAA,GAAMzC,oBAAoB,CAACL,IAAI,CAAE,QAAQ,CAAE,CACpD2B,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAM,CAAEwB,MAAM,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAC5D,CAAC,EACG,CAAC,EACL,CAAC,EACG,CAAC,CAEhB,CAAE,CACFC,MAAM,CAAE,CAAEC,SAAS,cAAEtJ,IAAA,CAACvB,KAAK,EAACkK,WAAW,CAAC,sCAAQ,CAAE,CAAE,CAAE,CACvD,CAAC,EA/FoB,WAgGf,CAAC,cAEV3I,IAAA,CAACO,OAAO,EAACwH,GAAG,CAAC,0BAAM,CAAAxB,QAAA,cACjBvG,IAAA,CAACnC,IAAI,EACHmK,UAAU,CAAEnH,gBAAiB,CAC7BoH,UAAU,CAAGsB,SAAS,EAAK,CACzB,KAAM,CAAAC,UAAU,CAAGnF,sBAAsB,CAACkF,SAAS,CAAC9F,IAAI,CAAC,CAEzD,mBACEvD,KAAA,CAACrC,IAAI,CAACuK,IAAI,EACRjC,KAAK,CAAE,CACLE,UAAU,CAAE,MAAM,CAClBI,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,KAAK,CACnB2B,MAAM,CAAE,mBAAmB,CAC3BjC,OAAO,CAAE,MACX,CAAE,CAAAG,QAAA,eAEFvG,IAAA,CAACnC,IAAI,CAACuK,IAAI,CAACE,IAAI,EACbC,MAAM,cACJvI,IAAA,CAACjC,MAAM,EAACoI,KAAK,CAAE,CAAEqC,eAAe,CAAEgB,UAAU,CAACrF,KAAM,CAAE,CAAAoC,QAAA,CAClDiD,UAAU,CAACpF,IAAI,CACV,CACT,CACDqD,KAAK,cACHvH,KAAA,QAAAqG,QAAA,eACEvG,IAAA,CAACI,IAAI,EAACqI,MAAM,MAAAlC,QAAA,CAAEgD,SAAS,CAAC/G,YAAY,CAAO,CAAC,cAC5CxC,IAAA,CAAClC,GAAG,EAACqG,KAAK,CAAEqF,UAAU,CAACrF,KAAM,CAACgC,KAAK,CAAE,CAAEuC,UAAU,CAAE,KAAK,CAAEf,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,CAC1EiD,UAAU,CAACtF,IAAI,CACb,CAAC,EACH,CACN,CACDyE,WAAW,cACTzI,KAAA,QAAAqG,QAAA,eACErG,KAAA,CAACE,IAAI,EAACqD,IAAI,CAAC,WAAW,CAAC0C,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,EAChDgD,SAAS,CAAChH,YAAY,CAAC,mBAAO,CAACgH,SAAS,CAAC7F,QAAQ,EAC9C,CAAC,cACP1D,IAAA,QAAK,CAAC,cACNE,KAAA,CAACtC,KAAK,EAACyJ,IAAI,CAAE,CAAE,CAAClB,KAAK,CAAE,CAAEyC,SAAS,CAAE,KAAM,CAAE,CAAArC,QAAA,eAC1CvG,IAAA,CAACJ,YAAY,EAACuG,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAC,cAC7C3H,IAAA,CAACI,IAAI,EAAC+F,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,CAAEgD,SAAS,CAAC5F,QAAQ,CAAO,CAAC,cAC9D3D,IAAA,CAACtB,OAAO,EAAC+E,IAAI,CAAC,UAAU,CAAC0C,KAAK,CAAE,CAAE0C,MAAM,CAAE,OAAQ,CAAE,CAAE,CAAC,cACvD7I,IAAA,CAACH,gBAAgB,EAACsG,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAC,cACjD3H,IAAA,CAACI,IAAI,EAAC+F,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,CAAEzG,UAAU,CAACyJ,SAAS,CAAC3F,aAAa,CAAC,CAAO,CAAC,EAC1E,CAAC,cACR5D,IAAA,QAAK,CAAC,cACNE,KAAA,CAACE,IAAI,EAAC+F,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAO,CAAE,CAAClE,IAAI,CAAC,WAAW,CAAA8C,QAAA,EAAC,gBAC9C,CAACgD,SAAS,CAAC1F,MAAM,EACjB,CAAC,EACJ,CACN,CACF,CAAC,cACF7D,IAAA,QAAKmG,KAAK,CAAE,CAAEqB,SAAS,CAAE,OAAQ,CAAE,CAAAjB,QAAA,cACjCrG,KAAA,CAACE,IAAI,EAACqI,MAAM,MAACtC,KAAK,CAAE,CAClBwB,QAAQ,CAAE,MAAM,CAChBxD,KAAK,CAAEoF,SAAS,CAAC9F,IAAI,GAAK,IAAI,CAAG,SAAS,CACnC8F,SAAS,CAAC9F,IAAI,GAAK,KAAK,CAAG,SAAS,CAAG,SAChD,CAAE,CAAA8C,QAAA,EACCgD,SAAS,CAAC9F,IAAI,GAAK,IAAI,CAAG,GAAG,CAAG8F,SAAS,CAAC9F,IAAI,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAAE8F,SAAS,CAAC7F,QAAQ,EACrF,CAAC,CACJ,CAAC,EACG,CAAC,CAEhB,CAAE,CACF2F,MAAM,CAAE,CAAEC,SAAS,cAAEtJ,IAAA,CAACvB,KAAK,EAACkK,WAAW,CAAC,sCAAQ,CAAE,CAAE,CAAE,CACvD,CAAC,EA/DoB,SAgEf,CAAC,EACN,CAAC,cAGPzI,KAAA,CAACtB,WAAW,CAAC6K,KAAK,EAChBC,OAAO,CAAC,OAAO,CACfjG,IAAI,CAAC,SAAS,CACd0C,KAAK,CAAE,CAAEwD,KAAK,CAAE,EAAG,CAAE,CACrBvF,IAAI,cAAEpE,IAAA,CAAChB,YAAY,GAAE,CAAE,CAAAuH,QAAA,eAEvBvG,IAAA,CAACpB,WAAW,EAACwF,IAAI,cAAEpE,IAAA,CAACjB,YAAY,GAAE,CAAE,CAAC6K,OAAO,CAAC,cAAI,CAACtC,OAAO,CAAE3C,UAAW,CAAE,CAAC,cACzE3E,IAAA,CAACpB,WAAW,EAACwF,IAAI,cAAEpE,IAAA,CAACb,cAAc,GAAE,CAAE,CAACyK,OAAO,CAAC,cAAI,CAACtC,OAAO,CAAEnF,iBAAkB,CAAE,CAAC,EACjE,CAAC,cAGpBnC,IAAA,CAAC7B,KAAK,EACJsJ,KAAK,CAAC,0BAAM,CACZoC,IAAI,CAAEtI,gBAAiB,CACvBuI,QAAQ,CAAEA,CAAA,GAAMtI,mBAAmB,CAAC,KAAK,CAAE,CAC3CuI,MAAM,CAAE,IAAK,CACbC,QAAQ,MAAAzD,QAAA,cAERrG,KAAA,QAAKiG,KAAK,CAAE,CAAEqB,SAAS,CAAE,QAAQ,CAAEpB,OAAO,CAAE,QAAS,CAAE,CAAAG,QAAA,eACrDvG,IAAA,CAACT,eAAe,EAAC4G,KAAK,CAAE,CAAEwB,QAAQ,CAAE,MAAM,CAAExD,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,cAClEnE,IAAA,MAAGmG,KAAK,CAAE,CAAEyC,SAAS,CAAE,MAAO,CAAE,CAAArC,QAAA,CAAC,+CAAU,CAAG,CAAC,cAC/CvG,IAAA,CAACrC,MAAM,EAAC8F,IAAI,CAAC,SAAS,CAAC6D,OAAO,CAAEA,CAAA,GAAM9F,mBAAmB,CAAC,KAAK,CAAE,CAAA+E,QAAA,CAAC,cAElE,CAAQ,CAAC,EACN,CAAC,CACD,CAAC,cAGRvG,IAAA,CAAC7B,KAAK,EACJsJ,KAAK,gBAAAwC,MAAA,CAAOlI,aAAa,GAAK,IAAI,CAAG,IAAI,CAAGA,aAAa,GAAK,KAAK,CAAG,IAAI,CAAG,IAAI,CAAG,CACpF8H,IAAI,CAAEpI,qBAAsB,CAC5BqI,QAAQ,CAAEA,CAAA,GAAM,CACdpI,wBAAwB,CAAC,KAAK,CAAC,CAC/BO,IAAI,CAACiD,WAAW,CAAC,CAAC,CACpB,CAAE,CACFgF,IAAI,CAAEA,CAAA,GAAMjI,IAAI,CAACkI,MAAM,CAAC,CAAE,CAC1BC,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAA9D,QAAA,cAEfrG,KAAA,CAAC9B,IAAI,EACH6D,IAAI,CAAEA,IAAK,CACXqI,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAExF,eAAgB,CAAAwB,QAAA,eAE1BvG,IAAA,CAAC5B,IAAI,CAACgK,IAAI,EAACoC,KAAK,CAAC,0BAAM,CAACC,IAAI,CAAC,cAAc,CAAAlE,QAAA,cACzCvG,IAAA,CAACtC,KAAK,EAACgN,QAAQ,MAAE,CAAC,CACT,CAAC,cACZ1K,IAAA,CAAC5B,IAAI,CAACgK,IAAI,EAACoC,KAAK,CAAC,0BAAM,CAACC,IAAI,CAAC,cAAc,CAAAlE,QAAA,cACzCvG,IAAA,CAACtC,KAAK,EAACgN,QAAQ,MAAE,CAAC,CACT,CAAC,cACZ1K,IAAA,CAAC5B,IAAI,CAACgK,IAAI,EAACoC,KAAK,CAAC,0BAAM,CAACC,IAAI,CAAC,cAAc,CAAAlE,QAAA,cACzCvG,IAAA,CAAC3B,WAAW,EAACqM,QAAQ,MAACvE,KAAK,CAAE,CAAEiD,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACzC,CAAC,cACZpJ,IAAA,CAAC5B,IAAI,CAACgK,IAAI,EAACoC,KAAK,CAAC,cAAI,CAACC,IAAI,CAAC,UAAU,CAAAlE,QAAA,cACnCvG,IAAA,CAACtC,KAAK,EAACgN,QAAQ,MAAE,CAAC,CACT,CAAC,cACZ1K,IAAA,CAAC5B,IAAI,CAACgK,IAAI,EACRoC,KAAK,IAAAP,MAAA,CAAKlI,aAAa,GAAK,IAAI,CAAG,IAAI,CAAGA,aAAa,GAAK,KAAK,CAAG,IAAI,CAAG,IAAI,gBAAK,CACpF0I,IAAI,CAAC,UAAU,CACfE,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErM,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAAgI,QAAA,cAE9CvG,IAAA,CAAC3B,WAAW,EACTwM,GAAG,CAAE9I,aAAa,GAAK,KAAK,CAAG,EAAE,CAAAF,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEc,YAAY,GAAI,CAAC,CAAC,CAAGmI,SAAU,CAC9E3E,KAAK,CAAE,CAAEiD,KAAK,CAAE,MAAO,CAAE,CACzBtC,WAAW,CAAC,gCAAO,CACpB,CAAC,CACM,CAAC,cACZ9G,IAAA,CAAC5B,IAAI,CAACgK,IAAI,EACRoC,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,QAAQ,CACbE,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErM,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAgI,QAAA,cAEhDvG,IAAA,CAACtC,KAAK,CAACqN,QAAQ,EAACC,IAAI,CAAE,CAAE,CAAClE,WAAW,CAAC,4CAAS,CAAE,CAAC,CACxC,CAAC,EACR,CAAC,CACF,CAAC,cAGR9G,IAAA,CAACnB,MAAM,EACL4I,KAAK,CAAC,0BAAM,CACZwD,SAAS,CAAC,QAAQ,CAClB9B,MAAM,CAAE,GAAI,CACZU,IAAI,CAAElI,mBAAoB,CAC1BuJ,OAAO,CAAEA,CAAA,GAAMtJ,sBAAsB,CAAC,KAAK,CAAE,CAAA2E,QAAA,cAE7CrG,KAAA,CAACtC,KAAK,EAACuN,SAAS,CAAC,UAAU,CAAChF,KAAK,CAAE,CAAEiD,KAAK,CAAE,MAAO,CAAE,CAAA7C,QAAA,eACnDrG,KAAA,QAAAqG,QAAA,eACEvG,IAAA,CAACI,IAAI,EAACqI,MAAM,MAAAlC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBrG,KAAA,CAAC5B,MAAM,EACLwI,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVZ,KAAK,CAAE,CAAEiD,KAAK,CAAE,MAAM,CAAER,SAAS,CAAE,KAAM,CAAE,CAC3C5B,KAAK,CAAE/F,YAAa,CACpBgG,QAAQ,CAAE/F,eAAgB,CAAAqF,QAAA,eAE1BvG,IAAA,CAACM,MAAM,EAAC0G,KAAK,CAAC,QAAQ,CAAAT,QAAA,CAAC,cAAE,CAAQ,CAAC,cAClCvG,IAAA,CAACM,MAAM,EAAC0G,KAAK,CAAC,WAAW,CAAAT,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACvCvG,IAAA,CAACM,MAAM,EAAC0G,KAAK,CAAC,cAAc,CAAAT,QAAA,CAAC,cAAE,CAAQ,CAAC,cACxCvG,IAAA,CAACM,MAAM,EAAC0G,KAAK,CAAC,QAAQ,CAAAT,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC9B,CAAC,EACN,CAAC,cACNrG,KAAA,QAAAqG,QAAA,eACEvG,IAAA,CAACI,IAAI,EAACqI,MAAM,MAAAlC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBrG,KAAA,CAAC5B,MAAM,EACLwI,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVZ,KAAK,CAAE,CAAEiD,KAAK,CAAE,MAAM,CAAER,SAAS,CAAE,KAAM,CAAE,CAC3C5B,KAAK,CAAE7F,cAAe,CACtB8F,QAAQ,CAAE7F,iBAAkB,CAAAmF,QAAA,eAE5BvG,IAAA,CAACM,MAAM,EAAC0G,KAAK,CAAC,0BAAM,CAAAT,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAClCvG,IAAA,CAACM,MAAM,EAAC0G,KAAK,CAAC,KAAK,CAAAT,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChCvG,IAAA,CAACM,MAAM,EAAC0G,KAAK,CAAC,oBAAK,CAAAT,QAAA,CAAC,oBAAG,CAAQ,CAAC,cAChCvG,IAAA,CAACM,MAAM,EAAC0G,KAAK,CAAC,0BAAM,CAAAT,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC5B,CAAC,EACN,CAAC,cACNvG,IAAA,CAACrC,MAAM,EACL8F,IAAI,CAAC,SAAS,CACd2H,KAAK,MACL9D,OAAO,CAAEA,CAAA,GAAM1F,sBAAsB,CAAC,KAAK,CAAE,CAC7CuE,KAAK,CAAE,CAAEyC,SAAS,CAAE,MAAO,CAAE,CAAArC,QAAA,CAC9B,0BAED,CAAQ,CAAC,EACJ,CAAC,CACF,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAA/F,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}