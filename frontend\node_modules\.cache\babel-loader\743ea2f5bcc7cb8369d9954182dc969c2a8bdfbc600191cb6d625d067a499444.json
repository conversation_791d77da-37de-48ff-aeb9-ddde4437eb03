{"ast": null, "code": "import axios from 'axios';\nimport { API_BASE_URL, STORAGE_KEYS, ERROR_CODES } from '../constants';\nimport { errorHandler, ErrorType } from '../utils/errorHandler';\nclass ApiService {\n  constructor() {\n    this.instance = void 0;\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // 请求拦截器\n    this.instance.interceptors.request.use(config => {\n      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // 响应拦截器\n    this.instance.interceptors.response.use(response => {\n      return response;\n    }, error => {\n      if (error.response) {\n        const {\n          status,\n          data\n        } = error.response;\n        switch (status) {\n          case ERROR_CODES.UNAUTHORIZED:\n            // 清除本地存储的认证信息\n            localStorage.removeItem(STORAGE_KEYS.TOKEN);\n            localStorage.removeItem(STORAGE_KEYS.USER_INFO);\n            localStorage.removeItem(STORAGE_KEYS.CURRENT_ROLE);\n            // 使用错误处理器处理权限错误\n            errorHandler.handleError({\n              type: ErrorType.PERMISSION,\n              message: (data === null || data === void 0 ? void 0 : data.message) || '登录已过期，请重新登录',\n              code: status,\n              details: data\n            });\n            // 重定向到登录页\n            window.location.href = '/login';\n            break;\n          case ERROR_CODES.FORBIDDEN:\n            console.error('权限不足');\n            break;\n          case ERROR_CODES.NOT_FOUND:\n            console.error('资源不存在');\n            break;\n          case ERROR_CODES.VALIDATION_ERROR:\n            console.error('数据验证失败:', data.message);\n            break;\n          case ERROR_CODES.SERVER_ERROR:\n            console.error('服务器内部错误');\n            break;\n          default:\n            console.error('请求失败:', (data === null || data === void 0 ? void 0 : data.message) || error.message);\n        }\n      } else if (error.request) {\n        console.error('网络错误，请检查网络连接');\n      } else {\n        console.error('请求配置错误:', error.message);\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // GET 请求\n  async get(url, config) {\n    const response = await this.instance.get(url, config);\n    return response.data.data;\n  }\n\n  // POST 请求\n  async post(url, data, config) {\n    const response = await this.instance.post(url, data, config);\n    return response.data.data;\n  }\n\n  // PUT 请求\n  async put(url, data, config) {\n    const response = await this.instance.put(url, data, config);\n    return response.data.data;\n  }\n\n  // DELETE 请求\n  async delete(url, config) {\n    const response = await this.instance.delete(url, config);\n    return response.data.data;\n  }\n\n  // PATCH 请求\n  async patch(url, data, config) {\n    const response = await this.instance.patch(url, data, config);\n    return response.data.data;\n  }\n\n  // 文件上传\n  async upload(url, file, onProgress) {\n    const formData = new FormData();\n    formData.append('file', file);\n    const config = {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      onUploadProgress: progressEvent => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          onProgress(progress);\n        }\n      }\n    };\n    const response = await this.instance.post(url, formData, config);\n    return response.data.data;\n  }\n\n  // 文件下载\n  async download(url, filename) {\n    const response = await this.instance.get(url, {\n      responseType: 'blob'\n    });\n    const blob = new Blob([response.data]);\n    const downloadUrl = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = downloadUrl;\n    link.download = filename || 'download';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(downloadUrl);\n  }\n\n  // 获取原始axios实例（用于特殊需求）\n  getInstance() {\n    return this.instance;\n  }\n}\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "STORAGE_KEYS", "ERROR_CODES", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "ApiService", "constructor", "instance", "create", "baseURL", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "TOKEN", "Authorization", "error", "Promise", "reject", "response", "status", "data", "UNAUTHORIZED", "removeItem", "USER_INFO", "CURRENT_ROLE", "handleError", "type", "PERMISSION", "message", "code", "details", "window", "location", "href", "FORBIDDEN", "console", "NOT_FOUND", "VALIDATION_ERROR", "SERVER_ERROR", "get", "url", "post", "put", "delete", "patch", "upload", "file", "onProgress", "formData", "FormData", "append", "onUploadProgress", "progressEvent", "total", "progress", "Math", "round", "loaded", "download", "filename", "responseType", "blob", "Blob", "downloadUrl", "URL", "createObjectURL", "link", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "getInstance", "apiService"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { API_BASE_URL, STORAGE_KEYS, ERROR_CODES } from '../constants';\nimport { ApiResponse } from '../types';\nimport { errorHandler, ErrorType } from '../utils/errorHandler';\n\nclass ApiService {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // 请求拦截器\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // 响应拦截器\n    this.instance.interceptors.response.use(\n      (response: AxiosResponse<ApiResponse<any>>) => {\n        return response;\n      },\n      (error) => {\n        if (error.response) {\n          const { status, data } = error.response;\n          \n          switch (status) {\n            case ERROR_CODES.UNAUTHORIZED:\n              // 清除本地存储的认证信息\n              localStorage.removeItem(STORAGE_KEYS.TOKEN);\n              localStorage.removeItem(STORAGE_KEYS.USER_INFO);\n              localStorage.removeItem(STORAGE_KEYS.CURRENT_ROLE);\n              // 使用错误处理器处理权限错误\n              errorHandler.handleError({\n                type: ErrorType.PERMISSION,\n                message: data?.message || '登录已过期，请重新登录',\n                code: status,\n                details: data\n              });\n              // 重定向到登录页\n              window.location.href = '/login';\n              break;\n            case ERROR_CODES.FORBIDDEN:\n              console.error('权限不足');\n              break;\n            case ERROR_CODES.NOT_FOUND:\n              console.error('资源不存在');\n              break;\n            case ERROR_CODES.VALIDATION_ERROR:\n              console.error('数据验证失败:', data.message);\n              break;\n            case ERROR_CODES.SERVER_ERROR:\n              console.error('服务器内部错误');\n              break;\n            default:\n              console.error('请求失败:', data?.message || error.message);\n          }\n        } else if (error.request) {\n          console.error('网络错误，请检查网络连接');\n        } else {\n          console.error('请求配置错误:', error.message);\n        }\n        \n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // GET 请求\n  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.instance.get<ApiResponse<T>>(url, config);\n    return response.data.data;\n  }\n\n  // POST 请求\n  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.instance.post<ApiResponse<T>>(url, data, config);\n    return response.data.data;\n  }\n\n  // PUT 请求\n  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.instance.put<ApiResponse<T>>(url, data, config);\n    return response.data.data;\n  }\n\n  // DELETE 请求\n  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.instance.delete<ApiResponse<T>>(url, config);\n    return response.data.data;\n  }\n\n  // PATCH 请求\n  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.instance.patch<ApiResponse<T>>(url, data, config);\n    return response.data.data;\n  }\n\n  // 文件上传\n  async upload<T>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    const config: AxiosRequestConfig = {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    };\n\n    const response = await this.instance.post<ApiResponse<T>>(url, formData, config);\n    return response.data.data;\n  }\n\n  // 文件下载\n  async download(url: string, filename?: string): Promise<void> {\n    const response = await this.instance.get(url, {\n      responseType: 'blob',\n    });\n\n    const blob = new Blob([response.data]);\n    const downloadUrl = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = downloadUrl;\n    link.download = filename || 'download';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(downloadUrl);\n  }\n\n  // 获取原始axios实例（用于特殊需求）\n  getInstance(): AxiosInstance {\n    return this.instance;\n  }\n}\n\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAA4D,OAAO;AAC/E,SAASC,YAAY,EAAEC,YAAY,EAAEC,WAAW,QAAQ,cAAc;AAEtE,SAASC,YAAY,EAAEC,SAAS,QAAQ,uBAAuB;AAE/D,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAAA,EAAG;IAAA,KAFNC,QAAQ;IAGd,IAAI,CAACA,QAAQ,GAAGR,KAAK,CAACS,MAAM,CAAC;MAC3BC,OAAO,EAAET,YAAY;MACrBU,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAG;IAC1B;IACA,IAAI,CAACL,QAAQ,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,CAAC;MACtD,IAAIH,KAAK,EAAE;QACTD,MAAM,CAACL,OAAO,CAACU,aAAa,GAAG,UAAUJ,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAM,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACf,QAAQ,CAACM,YAAY,CAACY,QAAQ,CAACV,GAAG,CACpCU,QAAyC,IAAK;MAC7C,OAAOA,QAAQ;IACjB,CAAC,EACAH,KAAK,IAAK;MACT,IAAIA,KAAK,CAACG,QAAQ,EAAE;QAClB,MAAM;UAAEC,MAAM;UAAEC;QAAK,CAAC,GAAGL,KAAK,CAACG,QAAQ;QAEvC,QAAQC,MAAM;UACZ,KAAKxB,WAAW,CAAC0B,YAAY;YAC3B;YACAV,YAAY,CAACW,UAAU,CAAC5B,YAAY,CAACmB,KAAK,CAAC;YAC3CF,YAAY,CAACW,UAAU,CAAC5B,YAAY,CAAC6B,SAAS,CAAC;YAC/CZ,YAAY,CAACW,UAAU,CAAC5B,YAAY,CAAC8B,YAAY,CAAC;YAClD;YACA5B,YAAY,CAAC6B,WAAW,CAAC;cACvBC,IAAI,EAAE7B,SAAS,CAAC8B,UAAU;cAC1BC,OAAO,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,OAAO,KAAI,aAAa;cACvCC,IAAI,EAAEV,MAAM;cACZW,OAAO,EAAEV;YACX,CAAC,CAAC;YACF;YACAW,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;YAC/B;UACF,KAAKtC,WAAW,CAACuC,SAAS;YACxBC,OAAO,CAACpB,KAAK,CAAC,MAAM,CAAC;YACrB;UACF,KAAKpB,WAAW,CAACyC,SAAS;YACxBD,OAAO,CAACpB,KAAK,CAAC,OAAO,CAAC;YACtB;UACF,KAAKpB,WAAW,CAAC0C,gBAAgB;YAC/BF,OAAO,CAACpB,KAAK,CAAC,SAAS,EAAEK,IAAI,CAACQ,OAAO,CAAC;YACtC;UACF,KAAKjC,WAAW,CAAC2C,YAAY;YAC3BH,OAAO,CAACpB,KAAK,CAAC,SAAS,CAAC;YACxB;UACF;YACEoB,OAAO,CAACpB,KAAK,CAAC,OAAO,EAAE,CAAAK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,OAAO,KAAIb,KAAK,CAACa,OAAO,CAAC;QAC1D;MACF,CAAC,MAAM,IAAIb,KAAK,CAACR,OAAO,EAAE;QACxB4B,OAAO,CAACpB,KAAK,CAAC,cAAc,CAAC;MAC/B,CAAC,MAAM;QACLoB,OAAO,CAACpB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACa,OAAO,CAAC;MACzC;MAEA,OAAOZ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAMwB,GAAGA,CAAIC,GAAW,EAAE/B,MAA2B,EAAc;IACjE,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAACuC,GAAG,CAAiBC,GAAG,EAAE/B,MAAM,CAAC;IACrE,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMqB,IAAIA,CAAID,GAAW,EAAEpB,IAAU,EAAEX,MAA2B,EAAc;IAC9E,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAACyC,IAAI,CAAiBD,GAAG,EAAEpB,IAAI,EAAEX,MAAM,CAAC;IAC5E,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMsB,GAAGA,CAAIF,GAAW,EAAEpB,IAAU,EAAEX,MAA2B,EAAc;IAC7E,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAAC0C,GAAG,CAAiBF,GAAG,EAAEpB,IAAI,EAAEX,MAAM,CAAC;IAC3E,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMuB,MAAMA,CAAIH,GAAW,EAAE/B,MAA2B,EAAc;IACpE,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAAC2C,MAAM,CAAiBH,GAAG,EAAE/B,MAAM,CAAC;IACxE,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMwB,KAAKA,CAAIJ,GAAW,EAAEpB,IAAU,EAAEX,MAA2B,EAAc;IAC/E,MAAMS,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAAC4C,KAAK,CAAiBJ,GAAG,EAAEpB,IAAI,EAAEX,MAAM,CAAC;IAC7E,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMyB,MAAMA,CAAIL,GAAW,EAAEM,IAAU,EAAEC,UAAuC,EAAc;IAC5F,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEJ,IAAI,CAAC;IAE7B,MAAMrC,MAA0B,GAAG;MACjCL,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD+C,gBAAgB,EAAGC,aAAa,IAAK;QACnC,IAAIL,UAAU,IAAIK,aAAa,CAACC,KAAK,EAAE;UACrC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAEJ,aAAa,CAACK,MAAM,GAAG,GAAG,GAAIL,aAAa,CAACC,KAAK,CAAC;UAC/EN,UAAU,CAACO,QAAQ,CAAC;QACtB;MACF;IACF,CAAC;IAED,MAAMpC,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAACyC,IAAI,CAAiBD,GAAG,EAAEQ,QAAQ,EAAEvC,MAAM,CAAC;IAChF,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMsC,QAAQA,CAAClB,GAAW,EAAEmB,QAAiB,EAAiB;IAC5D,MAAMzC,QAAQ,GAAG,MAAM,IAAI,CAAClB,QAAQ,CAACuC,GAAG,CAACC,GAAG,EAAE;MAC5CoB,YAAY,EAAE;IAChB,CAAC,CAAC;IAEF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC5C,QAAQ,CAACE,IAAI,CAAC,CAAC;IACtC,MAAM2C,WAAW,GAAGhC,MAAM,CAACiC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACpD,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACjC,IAAI,GAAG8B,WAAW;IACvBG,IAAI,CAACR,QAAQ,GAAGC,QAAQ,IAAI,UAAU;IACtCQ,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;IAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC;IACZJ,QAAQ,CAACE,IAAI,CAACG,WAAW,CAACN,IAAI,CAAC;IAC/BnC,MAAM,CAACiC,GAAG,CAACS,eAAe,CAACV,WAAW,CAAC;EACzC;;EAEA;EACAW,WAAWA,CAAA,EAAkB;IAC3B,OAAO,IAAI,CAAC1E,QAAQ;EACtB;AACF;AAEA,OAAO,MAAM2E,UAAU,GAAG,IAAI7E,UAAU,CAAC,CAAC;AAC1C,eAAe6E,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}