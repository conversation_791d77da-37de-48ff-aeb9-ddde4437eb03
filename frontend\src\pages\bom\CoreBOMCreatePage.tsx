import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Row,
  Col,
  Typography,
  Steps,
  message,
  Divider,
  Select,
  Table,
  Modal,
  InputNumber,
  Checkbox,
  Tag,
  Tooltip,
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  UpOutlined,
  DownOutlined,
} from '@ant-design/icons';

import { useAppDispatch } from '../../hooks/redux';
import { createCoreBOM } from '../../store/slices/bomSlice';
import { BOMItem, ConfigRule } from '../../types';
import { ROUTES, MATERIAL_CATEGORIES, UNITS } from '../../constants';
import { ConfirmDialog } from '../../components';
import { errorHandler, ErrorType } from '../../utils/errorHandler';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Step } = Steps;

interface BOMFormData {
  name: string;
  code: string;
  version: string;
  description: string;
}

const CoreBOMCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm<BOMFormData>();
  const [itemForm] = Form.useForm();
  const [ruleForm] = Form.useForm();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [bomItems, setBomItems] = useState<BOMItem[]>([]);
  const [configRules, setConfigRules] = useState<ConfigRule[]>([]);
  const [itemModalVisible, setItemModalVisible] = useState(false);
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<BOMItem | null>(null);
  const [editingRule, setEditingRule] = useState<ConfigRule | null>(null);

  const steps = [
    {
      title: '基本信息',
      description: '填写BOM基本信息',
    },
    {
      title: 'BOM结构',
      description: '添加物料清单',
    },
    {
      title: '配置规则',
      description: '设置配置规则',
    },
    {
      title: '完成',
      description: '确认并保存',
    },
  ];

  const handleNext = async () => {
    if (currentStep === 0) {
      try {
        await form.validateFields();
        setCurrentStep(1);
      } catch (error) {
        message.error('请完善基本信息');
      }
    } else if (currentStep === 1) {
      if (bomItems.length === 0) {
        message.error('请至少添加一个物料');
        return;
      }
      setCurrentStep(2);
    } else if (currentStep === 2) {
      setCurrentStep(3);
    }
  };

  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      const basicInfo = await form.validateFields();
      
      const bomData = {
        ...basicInfo,
        items: bomItems,
        configRules: configRules,
        status: 'DRAFT' as const,
      };

      await dispatch(createCoreBOM(bomData)).unwrap();
      message.success('BOM创建成功');
      navigate(ROUTES.CORE_BOM);
    } catch (error) {
      message.error('创建失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.CORE_BOM);
  };

  // BOM项目管理
  const handleAddItem = () => {
    setEditingItem(null);
    itemForm.resetFields();
    setItemModalVisible(true);
  };

  const handleEditItem = (item: BOMItem) => {
    setEditingItem(item);
    itemForm.setFieldsValue(item);
    setItemModalVisible(true);
  };

  const handleDeleteItem = (id: string) => {
    const item = bomItems.find(item => item.id === id);
    if (!item) return;
    
    Modal.confirm({
      title: '确认删除',
      content: (
        <div>
          <p>确定要删除以下物料吗？</p>
          <p><strong>物料编码：</strong>{item.materialCode}</p>
          <p><strong>物料名称：</strong>{item.materialName}</p>
          <p><strong>数量：</strong>{item.quantity} {item.unit}</p>
          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复！</p>
        </div>
      ),
      type: 'warning',
      onOk: () => {
        setBomItems(bomItems.filter(item => item.id !== id));
        message.success('删除成功');
      }
    });
  };

  const handleItemModalOk = async () => {
    try {
      const values = await itemForm.validateFields();
      const newItem: BOMItem = {
        id: editingItem?.id || Date.now().toString(),
        parentId: values.parentId,
        materialId: values.materialId,
        materialCode: values.materialCode,
        materialName: values.materialName,
        materialSpec: values.materialSpec,
        quantity: values.quantity,
        unit: values.unit,
        level: values.level || 1,
        sequence: values.sequence || bomItems.length + 1,
        isOptional: values.isOptional || false,
        isMandatory: values.isMandatory || true,
        isAlternative: values.isAlternative || false,
        alternativeGroup: values.alternativeGroup,
        unitPrice: values.unitPrice,
        totalPrice: (values.quantity || 0) * (values.unitPrice || 0),
        supplier: values.supplier,
        leadTime: values.leadTime,
        moq: values.moq,
        packageSize: values.packageSize,
        remarks: values.remarks,
      };

      if (editingItem) {
        setBomItems(bomItems.map(item => item.id === editingItem.id ? newItem : item));
        message.success('修改成功');
      } else {
        setBomItems([...bomItems, newItem]);
        message.success('添加成功');
      }
      
      setItemModalVisible(false);
    } catch (error) {
      message.error('请完善物料信息');
    }
  };

  // 配置规则管理
  const handleAddRule = () => {
    setEditingRule(null);
    ruleForm.resetFields();
    setRuleModalVisible(true);
  };

  const handleEditRule = (rule: ConfigRule) => {
    setEditingRule(rule);
    ruleForm.setFieldsValue(rule);
    setRuleModalVisible(true);
  };

  const handleDeleteRule = (id: string) => {
    const rule = configRules.find(rule => rule.id === id);
    if (!rule) return;
    
    Modal.confirm({
      title: '确认删除',
      content: (
        <div>
          <p>确定要删除以下配置规则吗？</p>
          <p><strong>规则名称：</strong>{rule.name}</p>
          <p><strong>规则描述：</strong>{rule.description || '无描述'}</p>
          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复！</p>
        </div>
      ),
      type: 'warning',
      onOk: () => {
        setConfigRules(configRules.filter(rule => rule.id !== id));
        message.success('删除成功');
      }
    });
  };

  const handleRuleModalOk = async () => {
    try {
      const values = await ruleForm.validateFields();
      const newRule: ConfigRule = {
        id: editingRule?.id || Date.now().toString(),
        name: values.name,
        condition: values.condition,
        action: values.action,
        priority: values.priority || 1,
        isActive: values.isActive !== false,
        description: values.description,
      };

      if (editingRule) {
        setConfigRules(configRules.map(rule => rule.id === editingRule.id ? newRule : rule));
        message.success('修改成功');
      } else {
        setConfigRules([...configRules, newRule]);
        message.success('添加成功');
      }

      setRuleModalVisible(false);
    } catch (error) {
      message.error('请完善规则信息');
    }
  };

  // BOM项目表格列定义
  const itemColumns = [
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      width: 60,
    },
    {
      title: '物料编码',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 120,
    },
    {
      title: '物料名称',
      dataIndex: 'materialName',
      key: 'materialName',
      ellipsis: true,
    },
    {
      title: '规格',
      dataIndex: 'materialSpec',
      key: 'materialSpec',
      ellipsis: true,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 60,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 80,
      render: (value: number) => value ? `¥${value.toFixed(2)}` : '-',
    },
    {
      title: '总价',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 80,
      render: (value: number) => value ? `¥${value.toFixed(2)}` : '-',
    },
    {
      title: '属性',
      key: 'attributes',
      width: 120,
      render: (_: any, record: BOMItem) => (
        <Space size={4}>
          {record.isOptional && <Tag color="blue">可选</Tag>}
          {record.isMandatory && <Tag color="green">必选</Tag>}
          {record.isAlternative && <Tag color="orange">互斥</Tag>}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: BOMItem) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditItem(record)}
          />
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteItem(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 配置规则表格列定义
  const ruleColumns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '条件',
      dataIndex: 'condition',
      key: 'condition',
      ellipsis: true,
    },
    {
      title: '动作',
      dataIndex: 'action',
      key: 'action',
      ellipsis: true,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: ConfigRule) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditRule(record)}
          />
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteRule(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Form form={form} layout="vertical">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="name"
                  label="BOM名称"
                  rules={[{ required: true, message: '请输入BOM名称' }]}
                >
                  <Input placeholder="请输入BOM名称" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="code"
                  label="BOM编码"
                  rules={[{ required: true, message: '请输入BOM编码' }]}
                >
                  <Input placeholder="请输入BOM编码" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="version"
                  label="版本号"
                  rules={[{ required: true, message: '请输入版本号' }]}
                  initialValue="V1.0"
                >
                  <Input placeholder="请输入版本号" />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item
                  name="description"
                  label="描述"
                  rules={[{ required: true, message: '请输入描述' }]}
                >
                  <TextArea rows={4} placeholder="请输入BOM描述" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        );

      case 1:
        return (
          <div>
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Title level={5}>BOM结构</Title>
              </Col>
              <Col>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddItem}>
                  添加物料
                </Button>
              </Col>
            </Row>
            <Table
              columns={itemColumns}
              dataSource={bomItems}
              rowKey="id"
              pagination={false}
              scroll={{ x: 1000 }}
              locale={{ emptyText: '暂无物料，请点击"添加物料"按钮添加' }}
            />
          </div>
        );

      case 2:
        return (
          <div>
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Title level={5}>配置规则</Title>
              </Col>
              <Col>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddRule}>
                  添加规则
                </Button>
              </Col>
            </Row>
            <Table
              columns={ruleColumns}
              dataSource={configRules}
              rowKey="id"
              pagination={false}
              locale={{ emptyText: '暂无配置规则，可选择添加' }}
            />
          </div>
        );

      case 3:
        const basicInfo = form.getFieldsValue();
        return (
          <div>
            <Title level={5}>确认信息</Title>
            <Card style={{ marginBottom: 16 }}>
              <Title level={5}>基本信息</Title>
              <Row gutter={[16, 8]}>
                <Col span={8}>
                  <Text strong>BOM名称：</Text>
                  <Text>{basicInfo.name}</Text>
                </Col>
                <Col span={8}>
                  <Text strong>BOM编码：</Text>
                  <Text>{basicInfo.code}</Text>
                </Col>
                <Col span={8}>
                  <Text strong>版本号：</Text>
                  <Text>{basicInfo.version}</Text>
                </Col>
                <Col span={24}>
                  <Text strong>描述：</Text>
                  <Text>{basicInfo.description}</Text>
                </Col>
              </Row>
            </Card>

            <Card style={{ marginBottom: 16 }}>
              <Title level={5}>BOM结构（{bomItems.length}个物料）</Title>
              <Table
                columns={itemColumns.filter(col => col.key !== 'action')}
                dataSource={bomItems}
                rowKey="id"
                pagination={false}
                size="small"
                scroll={{ x: 800 }}
              />
            </Card>

            <Card>
              <Title level={5}>配置规则（{configRules.length}个规则）</Title>
              <Table
                columns={ruleColumns.filter(col => col.key !== 'action')}
                dataSource={configRules}
                rowKey="id"
                pagination={false}
                size="small"
              />
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <Space>
              <Button icon={<ArrowLeftOutlined />} onClick={handleCancel}>
                返回
              </Button>
              <Title level={4} style={{ margin: 0 }}>
                创建核心BOM
              </Title>
            </Space>
          </Col>
        </Row>

        <Steps current={currentStep} style={{ marginBottom: 32 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} description={item.description} />
          ))}
        </Steps>

        <div style={{ minHeight: 400 }}>
          {renderStepContent()}
        </div>

        <Divider />

        <Row justify="space-between">
          <Col>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
          </Col>
          <Col>
            <Space>
              <Button onClick={handleCancel}>
                取消
              </Button>
              {currentStep < steps.length - 1 ? (
                <Button type="primary" onClick={handleNext}>
                  下一步
                </Button>
              ) : (
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  loading={loading}
                  onClick={handleSave}
                >
                  保存
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 添加/编辑物料模态框 */}
      <Modal
        title={editingItem ? '编辑物料' : '添加物料'}
        open={itemModalVisible}
        onOk={handleItemModalOk}
        onCancel={() => setItemModalVisible(false)}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <Form form={itemForm} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="materialCode"
                label="物料编码"
                rules={[{ required: true, message: '请输入物料编码' }]}
              >
                <Input placeholder="请输入物料编码" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="materialName"
                label="物料名称"
                rules={[{ required: true, message: '请输入物料名称' }]}
              >
                <Input placeholder="请输入物料名称" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="materialSpec"
                label="物料规格"
              >
                <Input placeholder="请输入物料规格" />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="quantity"
                label="数量"
                rules={[{ required: true, message: '请输入数量' }]}
              >
                <InputNumber min={0} precision={2} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  {UNITS.map(unit => (
                    <Select.Option key={unit} value={unit}>
                      {unit}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="level"
                label="层级"
                initialValue={1}
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="unitPrice"
                label="单价"
              >
                <InputNumber min={0} precision={2} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="supplier"
                label="供应商"
              >
                <Input placeholder="请输入供应商" />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="leadTime"
                label="交期(天)"
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Space>
                <Form.Item name="isOptional" valuePropName="checked">
                  <Checkbox>可选</Checkbox>
                </Form.Item>
                <Form.Item name="isMandatory" valuePropName="checked" initialValue={true}>
                  <Checkbox>必选</Checkbox>
                </Form.Item>
                <Form.Item name="isAlternative" valuePropName="checked">
                  <Checkbox>互斥</Checkbox>
                </Form.Item>
              </Space>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="remarks"
                label="备注"
              >
                <TextArea rows={3} placeholder="请输入备注" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 添加/编辑配置规则模态框 */}
      <Modal
        title={editingRule ? '编辑配置规则' : '添加配置规则'}
        open={ruleModalVisible}
        onOk={handleRuleModalOk}
        onCancel={() => setRuleModalVisible(false)}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form form={ruleForm} layout="vertical">
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="请输入规则名称" />
          </Form.Item>
          <Form.Item
            name="condition"
            label="条件表达式"
            rules={[{ required: true, message: '请输入条件表达式' }]}
          >
            <TextArea rows={3} placeholder="例如：frequency == '5G' && power > 100" />
          </Form.Item>
          <Form.Item
            name="action"
            label="执行动作"
            rules={[{ required: true, message: '请输入执行动作' }]}
          >
            <TextArea rows={3} placeholder="例如：include('ANT-5G-001'); exclude('ANT-4G-001')" />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="priority"
                label="优先级"
                initialValue={1}
              >
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item name="isActive" valuePropName="checked" initialValue={true}>
                <Checkbox>启用规则</Checkbox>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="description"
            label="规则描述"
          >
            <TextArea rows={2} placeholder="请输入规则描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CoreBOMCreatePage;
