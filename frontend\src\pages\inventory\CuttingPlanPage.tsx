import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Row,
  Col,
  Statistic,
  Typography,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Alert,
  Tabs,
  Tag,
  Tooltip,
  Progress,
  Divider,
  message,
  Checkbox,
} from 'antd';
import {
  PlusOutlined,
  CalculatorOutlined,
  Sc<PERSON>orOutlined,
  Bar<PERSON><PERSON>Outlined,
  ExportOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { formatCurrency } from '../../utils/format';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface CuttingPlan {
  id: string;
  planName: string;
  materialCode: string;
  materialName: string;
  specification: string;
  standardLength: number;
  unit: string;
  unitPrice: number;
  totalDemand: number;
  planDate: string;
  status: 'draft' | 'calculating' | 'optimized' | 'executing' | 'completed';
  algorithm: string;
  utilizationRate: number;
  wasteRate: number;
  totalCost: number;
  wasteCost: number;
  createdBy: string;
  createdDate: string;
}

interface CuttingScheme {
  id: string;
  planId: string;
  schemeNo: number;
  pattern: string;
  cuts: number[];
  quantity: number;
  utilization: number;
  waste: number;
  wasteLength: number;
}

interface DemandItem {
  id: string;
  length: number;
  quantity: number;
  priority: string;
  orderCode: string;
  customerName: string;
  dueDate: string;
}

const CuttingPlanPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('plans');
  const [planModalVisible, setPlanModalVisible] = useState(false);
  const [optimizeModalVisible, setOptimizeModalVisible] = useState(false);
  const [schemeModalVisible, setSchemeModalVisible] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<CuttingPlan | null>(null);
  const [planForm] = Form.useForm();
  const [optimizeForm] = Form.useForm();

  const loadData = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleCreatePlan = () => {
    setPlanModalVisible(true);
    planForm.resetFields();
  };

  const handleOptimize = (record: CuttingPlan) => {
    setSelectedPlan(record);
    setOptimizeModalVisible(true);
    optimizeForm.resetFields();
  };

  const handleViewScheme = (record: CuttingPlan) => {
    setSelectedPlan(record);
    setSchemeModalVisible(true);
  };

  const handlePlanModalOk = () => {
    planForm.validateFields().then(values => {
      console.log('创建切割计划:', values);
      message.success('切割计划创建成功');
      setPlanModalVisible(false);
      loadData();
    });
  };

  const handleOptimizeModalOk = () => {
    optimizeForm.validateFields().then(values => {
      console.log('优化参数:', values);
      message.success('切割优化计算已开始，请稍候查看结果');
      setOptimizeModalVisible(false);
      loadData();
    });
  };

  // 模拟切割计划数据
  const mockPlans: CuttingPlan[] = [
    {
      id: '1',
      planName: '5G基站电缆切割计划-202403',
      materialCode: 'CABLE-001',
      materialName: '同轴电缆',
      specification: 'RG-58/U',
      standardLength: 100,
      unit: 'M',
      unitPrice: 4.5,
      totalDemand: 1250,
      planDate: '2024-03-25T00:00:00Z',
      status: 'optimized',
      algorithm: '首次适应算法',
      utilizationRate: 92.5,
      wasteRate: 7.5,
      totalCost: 5625,
      wasteCost: 421.88,
      createdBy: '工艺工程师',
      createdDate: '2024-03-20T00:00:00Z',
    },
    {
      id: '2',
      planName: '不锈钢板切割计划-202403',
      materialCode: 'STEEL-PLATE-001',
      materialName: '不锈钢板',
      specification: '304-2mm',
      standardLength: 2000,
      unit: 'MM',
      unitPrice: 0.15,
      totalDemand: 8500,
      planDate: '2024-03-28T00:00:00Z',
      status: 'calculating',
      algorithm: '最佳适应算法',
      utilizationRate: 0,
      wasteRate: 0,
      totalCost: 1275,
      wasteCost: 0,
      createdBy: '生产计划员',
      createdDate: '2024-03-22T00:00:00Z',
    },
    {
      id: '3',
      planName: '铜线切割计划-202403',
      materialCode: 'COPPER-WIRE-001',
      materialName: '铜线',
      specification: '2.5mm²',
      standardLength: 500,
      unit: 'M',
      unitPrice: 8.2,
      totalDemand: 2800,
      planDate: '2024-03-30T00:00:00Z',
      status: 'draft',
      algorithm: '',
      utilizationRate: 0,
      wasteRate: 0,
      totalCost: 22960,
      wasteCost: 0,
      createdBy: '工艺工程师',
      createdDate: '2024-03-24T00:00:00Z',
    },
  ];

  // 模拟切割方案数据
  const mockSchemes: CuttingScheme[] = [
    {
      id: '1',
      planId: '1',
      schemeNo: 1,
      pattern: '25M + 25M + 25M + 25M',
      cuts: [25, 25, 25, 25],
      quantity: 8,
      utilization: 100,
      waste: 0,
      wasteLength: 0,
    },
    {
      id: '2',
      planId: '1',
      schemeNo: 2,
      pattern: '30M + 30M + 30M + 10M',
      cuts: [30, 30, 30, 10],
      quantity: 6,
      utilization: 100,
      waste: 0,
      wasteLength: 0,
    },
    {
      id: '3',
      planId: '1',
      schemeNo: 3,
      pattern: '35M + 35M + 20M + 8M',
      cuts: [35, 35, 20, 8],
      quantity: 4,
      utilization: 98,
      waste: 2,
      wasteLength: 2,
    },
  ];

  // 模拟需求数据
  const mockDemands: DemandItem[] = [
    {
      id: '1',
      length: 25,
      quantity: 32,
      priority: 'HIGH',
      orderCode: 'ORD-5G-001',
      customerName: '中国移动',
      dueDate: '2024-04-05T00:00:00Z',
    },
    {
      id: '2',
      length: 30,
      quantity: 18,
      priority: 'HIGH',
      orderCode: 'ORD-5G-002',
      customerName: '中国联通',
      dueDate: '2024-04-08T00:00:00Z',
    },
    {
      id: '3',
      length: 35,
      quantity: 8,
      priority: 'MEDIUM',
      orderCode: 'ORD-5G-003',
      customerName: '中国电信',
      dueDate: '2024-04-10T00:00:00Z',
    },
    {
      id: '4',
      length: 20,
      quantity: 4,
      priority: 'MEDIUM',
      orderCode: 'ORD-5G-004',
      customerName: '华为技术',
      dueDate: '2024-04-12T00:00:00Z',
    },
  ];

  const planColumns = [
    {
      title: '计划名称',
      dataIndex: 'planName',
      key: 'planName',
      width: 200,
      fixed: 'left' as const,
    },
    {
      title: '物料信息',
      key: 'material',
      width: 200,
      render: (record: CuttingPlan) => (
        <div>
          <div>{record.materialCode}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.materialName}
          </Text>
        </div>
      ),
    },
    {
      title: '规格',
      dataIndex: 'specification',
      key: 'specification',
      width: 120,
    },
    {
      title: '标准长度',
      key: 'standardLength',
      width: 100,
      render: (record: CuttingPlan) => `${record.standardLength}${record.unit}`,
    },
    {
      title: '总需求',
      key: 'totalDemand',
      width: 100,
      render: (record: CuttingPlan) => `${record.totalDemand}${record.unit}`,
    },
    {
      title: '利用率',
      dataIndex: 'utilizationRate',
      key: 'utilizationRate',
      width: 100,
      render: (value: number) => (
        <Progress
          percent={value}
          size="small"
          status={value >= 90 ? 'success' : value >= 80 ? 'normal' : 'exception'}
        />
      ),
    },
    {
      title: '浪费率',
      dataIndex: 'wasteRate',
      key: 'wasteRate',
      width: 100,
      render: (value: number) => (
        <Tag color={value <= 5 ? 'green' : value <= 10 ? 'orange' : 'red'}>
          {value.toFixed(1)}%
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusConfig = {
          draft: { color: 'default', text: '草稿' },
          calculating: { color: 'processing', text: '计算中' },
          optimized: { color: 'success', text: '已优化' },
          executing: { color: 'warning', text: '执行中' },
          completed: { color: 'success', text: '已完成' },
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '计划日期',
      dataIndex: 'planDate',
      key: 'planDate',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (record: CuttingPlan) => (
        <Space size="small">
          <Tooltip title="查看方案">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewScheme(record)}
            />
          </Tooltip>
          <Tooltip title="优化计算">
            <Button
              type="text"
              icon={<CalculatorOutlined />}
              onClick={() => handleOptimize(record)}
              disabled={record.status === 'calculating'}
            />
          </Tooltip>
          {record.status === 'optimized' && (
            <Tooltip title="开始执行">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => {
                  message.success('切割计划已开始执行');
                  loadData();
                }}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              disabled={record.status === 'executing' || record.status === 'completed'}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: '确定要删除这个切割计划吗？',
                  onOk: () => {
                    message.success('删除成功');
                    loadData();
                  },
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const schemeColumns = [
    {
      title: '方案编号',
      dataIndex: 'schemeNo',
      key: 'schemeNo',
      width: 80,
    },
    {
      title: '切割模式',
      dataIndex: 'pattern',
      key: 'pattern',
      width: 200,
    },
    {
      title: '切割长度',
      dataIndex: 'cuts',
      key: 'cuts',
      width: 150,
      render: (cuts: number[]) => cuts.join(' + '),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
    },
    {
      title: '利用率',
      dataIndex: 'utilization',
      key: 'utilization',
      width: 100,
      render: (value: number) => `${value}%`,
    },
    {
      title: '浪费长度',
      dataIndex: 'wasteLength',
      key: 'wasteLength',
      width: 100,
      render: (value: number, record: CuttingScheme) => 
        `${value}${selectedPlan?.unit || 'M'}`,
    },
  ];

  const demandColumns = [
    {
      title: '长度',
      dataIndex: 'length',
      key: 'length',
      width: 80,
      render: (value: number) => `${value}M`,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: string) => (
        <Tag color={priority === 'HIGH' ? 'red' : priority === 'MEDIUM' ? 'orange' : 'green'}>
          {priority === 'HIGH' ? '高' : priority === 'MEDIUM' ? '中' : '低'}
        </Tag>
      ),
    },
    {
      title: '订单编号',
      dataIndex: 'orderCode',
      key: 'orderCode',
      width: 120,
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 120,
    },
    {
      title: '交期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 100,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
  ];

  const renderStatistics = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
      <Col xs={12} sm={6}>
        <Card size="small">
          <Statistic
            title="总计划数"
            value={mockPlans.length}
            prefix={<ScissorOutlined />}
          />
        </Card>
      </Col>
      <Col xs={12} sm={6}>
        <Card size="small">
          <Statistic
            title="平均利用率"
            value={88.3}
            suffix="%"
            valueStyle={{ color: '#3f8600' }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={6}>
        <Card size="small">
          <Statistic
            title="总节约成本"
            value={1250}
            prefix="¥"
            precision={0}
            valueStyle={{ color: '#3f8600' }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={6}>
        <Card size="small">
          <Statistic
            title="浪费金额"
            value={421.88}
            prefix="¥"
            precision={2}
            valueStyle={{ color: '#cf1322' }}
          />
        </Card>
      </Col>
    </Row>
  );

  const renderPlansTab = () => (
    <div>
      {renderStatistics()}
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreatePlan}
              >
                新建计划
              </Button>
              <Button icon={<ExportOutlined />}>
                导出计划
              </Button>
              <Button icon={<ReloadOutlined />} onClick={loadData}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>

        <Alert
          message="切割优化提示"
          description="系统会根据需求自动生成最优切割方案，建议定期检查计划执行情况，及时调整优化参数。"
          type="info"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={planColumns}
          dataSource={mockPlans}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1400 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>
    </div>
  );

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              切割计划管理
            </Title>
            <Text type="secondary">
              管理长度型材料的切割计划，优化材料利用率，减少浪费
            </Text>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<span><ScissorOutlined />切割计划</span>} key="plans">
            {renderPlansTab()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 新建计划模态框 */}
      <Modal
        title="新建切割计划"
        open={planModalVisible}
        onOk={handlePlanModalOk}
        onCancel={() => setPlanModalVisible(false)}
        okText="创建"
        cancelText="取消"
        width={800}
      >
        <Form form={planForm} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="planName"
                label="计划名称"
                rules={[{ required: true, message: '请输入计划名称' }]}
              >
                <Input placeholder="请输入计划名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="materialCode"
                label="物料编码"
                rules={[{ required: true, message: '请选择物料' }]}
              >
                <Select placeholder="请选择物料">
                  <Select.Option value="CABLE-001">CABLE-001 - 同轴电缆</Select.Option>
                  <Select.Option value="STEEL-PLATE-001">STEEL-PLATE-001 - 不锈钢板</Select.Option>
                  <Select.Option value="COPPER-WIRE-001">COPPER-WIRE-001 - 铜线</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="standardLength"
                label="标准长度"
                rules={[{ required: true, message: '请输入标准长度' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="标准长度"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select placeholder="请选择单位">
                  <Select.Option value="M">米(M)</Select.Option>
                  <Select.Option value="MM">毫米(MM)</Select.Option>
                  <Select.Option value="CM">厘米(CM)</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unitPrice"
                label="单价"
                rules={[{ required: true, message: '请输入单价' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="单价"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="planDate"
                label="计划日期"
                rules={[{ required: true, message: '请选择计划日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="algorithm"
                label="优化算法"
                initialValue="first_fit"
              >
                <Select placeholder="请选择优化算法">
                  <Select.Option value="first_fit">首次适应算法</Select.Option>
                  <Select.Option value="best_fit">最佳适应算法</Select.Option>
                  <Select.Option value="worst_fit">最坏适应算法</Select.Option>
                  <Select.Option value="genetic">遗传算法</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="计划描述">
            <TextArea rows={3} placeholder="请输入计划描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 优化计算模态框 */}
      <Modal
        title="切割优化设置"
        open={optimizeModalVisible}
        onOk={handleOptimizeModalOk}
        onCancel={() => setOptimizeModalVisible(false)}
        okText="开始优化"
        cancelText="取消"
        width={600}
      >
        <Form form={optimizeForm} layout="vertical">
          <Alert
            message="优化参数设置"
            description="调整优化参数可以获得不同的切割方案，建议根据实际生产情况选择合适的参数。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            name="algorithm"
            label="优化算法"
            initialValue="best_fit"
          >
            <Select placeholder="请选择优化算法">
              <Select.Option value="first_fit">首次适应算法 - 快速计算</Select.Option>
              <Select.Option value="best_fit">最佳适应算法 - 平衡效果</Select.Option>
              <Select.Option value="worst_fit">最坏适应算法 - 减少余料种类</Select.Option>
              <Select.Option value="genetic">遗传算法 - 最优解</Select.Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="minUtilization"
                label="最小利用率(%)"
                initialValue={85}
              >
                <InputNumber
                  min={50}
                  max={100}
                  style={{ width: '100%' }}
                  placeholder="最小利用率"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxWasteLength"
                label="最大浪费长度"
                initialValue={5}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="最大浪费长度"
                  addonAfter={selectedPlan?.unit || 'M'}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="priorityOrder" label="优先级排序">
            <Checkbox.Group
              options={[
                { label: '优先满足高优先级订单', value: 'priority' },
                { label: '优先满足紧急交期', value: 'dueDate' },
                { label: '优先减少浪费', value: 'waste' },
                { label: '优先减少方案数量', value: 'schemes' },
              ]}
              defaultValue={['priority', 'waste']}
            />
          </Form.Item>

          <Form.Item name="allowRemnant" valuePropName="checked" initialValue={true}>
            <Checkbox>允许生成余料</Checkbox>
          </Form.Item>
        </Form>
      </Modal>

      {/* 切割方案模态框 */}
      <Modal
        title={`切割方案详情 - ${selectedPlan?.planName}`}
        open={schemeModalVisible}
        onCancel={() => setSchemeModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setSchemeModalVisible(false)}>
            关闭
          </Button>,
          <Button key="export" icon={<ExportOutlined />}>
            导出方案
          </Button>,
          <Button key="execute" type="primary" icon={<PlayCircleOutlined />}>
            执行计划
          </Button>,
        ]}
        width={1000}
      >
        {selectedPlan && (
          <div>
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic
                  title="总利用率"
                  value={selectedPlan.utilizationRate}
                  suffix="%"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="浪费率"
                  value={selectedPlan.wasteRate}
                  suffix="%"
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="总成本"
                  value={selectedPlan.totalCost}
                  prefix="¥"
                  precision={2}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="浪费成本"
                  value={selectedPlan.wasteCost}
                  prefix="¥"
                  precision={2}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
            </Row>

            <Divider orientation="left">需求清单</Divider>
            <Table
              columns={demandColumns}
              dataSource={mockDemands}
              rowKey="id"
              size="small"
              pagination={false}
              style={{ marginBottom: 16 }}
            />

            <Divider orientation="left">切割方案</Divider>
            <Table
              columns={schemeColumns}
              dataSource={mockSchemes}
              rowKey="id"
              size="small"
              pagination={false}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CuttingPlanPage;