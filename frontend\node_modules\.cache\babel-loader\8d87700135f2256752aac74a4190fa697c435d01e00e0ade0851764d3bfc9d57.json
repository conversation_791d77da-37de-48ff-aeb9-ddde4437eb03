{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams,useNavigate}from'react-router-dom';import{Card,Typography,Button,Table,Space,Descriptions,Tag,Divider,Row,Col,Statistic,message,Spin,Tooltip,Modal,Progress,Select,Checkbox}from'antd';import*as XLSX from'xlsx';import{ArrowLeftOutlined,EditOutlined,PrinterOutlined,DownloadOutlined,ShareAltOutlined,CopyOutlined,CheckCircleOutlined,ClockCircleOutlined,ExclamationCircleOutlined,StopOutlined}from'@ant-design/icons';import dayjs from'dayjs';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const OrderBOMViewPage=()=>{const{id}=useParams();const navigate=useNavigate();const[loading,setLoading]=useState(true);const[bomData,setBomData]=useState(null);const[expandedRowKeys,setExpandedRowKeys]=useState([]);const[exportModalVisible,setExportModalVisible]=useState(false);const[exportFormat,setExportFormat]=useState('excel');const[exportFields,setExportFields]=useState(['materialCode','materialName','specification','unit','quantity','unitPrice','totalPrice','supplier']);// 模拟数据加载\nuseEffect(()=>{const loadBOMData=async()=>{setLoading(true);try{// TODO: 从API获取订单BOM数据\nawait new Promise(resolve=>setTimeout(resolve,1000));// 模拟数据\nconst mockData={id:id||'1',bomCode:'BOM202401001',bomName:'智能控制器订单BOM',version:'1.0',productCode:'P001',productName:'智能控制器 V1.0',customerCode:'C001',customerName:'华为技术有限公司',orderNumber:'ORD202401001',orderQuantity:100,deliveryDate:'2024-02-15',priority:'high',status:'approved',description:'华为智能控制器项目专用BOM，包含主控芯片、传感器模块等核心组件',totalAmount:4580.00,createdBy:'张三',createdAt:'2024-01-15 10:30:00',updatedAt:'2024-01-16 14:20:00',approvedBy:'李四',approvedAt:'2024-01-16 16:45:00',items:[{key:'1',materialCode:'M003',materialName:'集成电路 STM32',specification:'STM32F407VGT6',unit:'片',quantity:1,unitPrice:25.00,totalPrice:25.00,supplier:'意法半导体',deliveryDays:7,remark:'主控芯片',level:1,status:'available'},{key:'2',materialCode:'M004',materialName:'PCB板 主板',specification:'4层板 100x80mm',unit:'块',quantity:1,unitPrice:15.80,totalPrice:15.80,supplier:'深圳PCB厂',deliveryDays:5,remark:'主控板',level:1,status:'available'},{key:'3',materialCode:'M001',materialName:'电阻器 10KΩ',specification:'0603 1% 1/10W',unit:'个',quantity:10,unitPrice:0.50,totalPrice:5.00,supplier:'国巨电子',deliveryDays:3,remark:'上拉电阻',level:2,parentKey:'2',status:'available'},{key:'4',materialCode:'M002',materialName:'电容器 100μF',specification:'25V 电解电容',unit:'个',quantity:5,unitPrice:1.20,totalPrice:6.00,supplier:'松下电器',deliveryDays:4,remark:'滤波电容',level:2,parentKey:'2',status:'shortage'},{key:'5',materialCode:'M005',materialName:'外壳 塑料',specification:'ABS 黑色',unit:'个',quantity:1,unitPrice:8.50,totalPrice:8.50,supplier:'塑料制品厂',deliveryDays:10,remark:'产品外壳',level:1,status:'pending'}]};setBomData(mockData);}catch(error){message.error('加载BOM数据失败');}finally{setLoading(false);}};if(id){loadBOMData();}},[id]);// 状态标签渲染\nconst renderStatusTag=status=>{const statusConfig={draft:{color:'default',text:'草稿',icon:/*#__PURE__*/_jsx(EditOutlined,{})},pending:{color:'processing',text:'待审核',icon:/*#__PURE__*/_jsx(ClockCircleOutlined,{})},approved:{color:'success',text:'已审核',icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{})},rejected:{color:'error',text:'已拒绝',icon:/*#__PURE__*/_jsx(StopOutlined,{})},in_production:{color:'warning',text:'生产中',icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{})},completed:{color:'success',text:'已完成',icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{})}};const config=statusConfig[status]||statusConfig.draft;return/*#__PURE__*/_jsx(Tag,{color:config.color,icon:config.icon,children:config.text});};// 优先级标签渲染\nconst renderPriorityTag=priority=>{const priorityConfig={high:{color:'red',text:'高'},medium:{color:'orange',text:'中'},low:{color:'green',text:'低'}};const config=priorityConfig[priority]||priorityConfig.medium;return/*#__PURE__*/_jsx(Tag,{color:config.color,children:config.text});};// 物料状态标签渲染\nconst renderMaterialStatusTag=status=>{const statusConfig={available:{color:'success',text:'可用'},shortage:{color:'error',text:'缺料'},pending:{color:'warning',text:'待采购'}};const config=statusConfig[status]||statusConfig.available;return/*#__PURE__*/_jsx(Tag,{color:config.color,children:config.text});};// 表格列定义\nconst columns=[{title:'层级',dataIndex:'level',width:60,render:level=>/*#__PURE__*/_jsxs(Tag,{color:level===1?'blue':level===2?'green':'orange',children:[\"L\",level]})},{title:'物料编码',dataIndex:'materialCode',width:120,render:(text,record)=>/*#__PURE__*/_jsx(\"div\",{style:{paddingLeft:(record.level-1)*20},children:/*#__PURE__*/_jsx(Text,{strong:true,children:text})})},{title:'物料名称',dataIndex:'materialName',width:200},{title:'规格型号',dataIndex:'specification',width:150},{title:'单位',dataIndex:'unit',width:80},{title:'数量',dataIndex:'quantity',width:100,render:value=>value.toFixed(2)},{title:'单价(元)',dataIndex:'unitPrice',width:100,render:value=>\"\\xA5\".concat(value.toFixed(2))},{title:'总价(元)',dataIndex:'totalPrice',width:120,render:value=>/*#__PURE__*/_jsxs(Text,{strong:true,style:{color:'#1890ff'},children:[\"\\xA5\",value.toFixed(2)]})},{title:'供应商',dataIndex:'supplier',width:120},{title:'交期(天)',dataIndex:'deliveryDays',width:100},{title:'状态',dataIndex:'status',width:100,render:status=>renderMaterialStatusTag(status)},{title:'备注',dataIndex:'remark',width:150,ellipsis:true}];// 处理编辑\nconst handleEdit=()=>{navigate(\"/bom/order-bom-edit/\".concat(id));};// 处理复制\nconst handleCopy=()=>{Modal.confirm({title:'确认复制',content:'确定要复制这个订单BOM吗？',onOk:()=>{// TODO: 实现复制功能\nmessage.success('BOM复制成功');navigate('/bom/order-bom-create');}});};// 处理打印\nconst handlePrint=()=>{window.print();};// 处理导出\nconst handleExport=()=>{setExportModalVisible(true);};const executeExport=()=>{if(!bomData)return;try{// 准备导出数据\nconst exportData=bomData.items.map(item=>{const data={};if(exportFields.includes('materialCode'))data['物料编码']=item.materialCode;if(exportFields.includes('materialName'))data['物料名称']=item.materialName;if(exportFields.includes('specification'))data['规格型号']=item.specification;if(exportFields.includes('unit'))data['单位']=item.unit;if(exportFields.includes('quantity'))data['数量']=item.quantity;if(exportFields.includes('unitPrice'))data['单价']=item.unitPrice;if(exportFields.includes('totalPrice'))data['总价']=item.totalPrice;if(exportFields.includes('supplier'))data['供应商']=item.supplier;if(exportFields.includes('deliveryDays'))data['交期(天)']=item.deliveryDays;if(exportFields.includes('status'))data['状态']=item.status==='available'?'可用':item.status==='shortage'?'缺料':'待采购';if(exportFields.includes('remark'))data['备注']=item.remark||'';return data;});// 创建工作簿\nconst ws=XLSX.utils.json_to_sheet(exportData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'BOM明细');// 下载文件\nconst fileName=\"\".concat(bomData.bomCode,\"_BOM\\u660E\\u7EC6_\").concat(new Date().toISOString().split('T')[0],\".\").concat(exportFormat==='excel'?'xlsx':'csv');XLSX.writeFile(wb,fileName);message.success('导出成功');setExportModalVisible(false);}catch(error){message.error('导出失败');}};// 处理分享\nconst handleShare=()=>{// TODO: 实现分享功能\nmessage.info('分享功能开发中...');};if(loading){return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px',textAlign:'center'},children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"p\",{style:{marginTop:16},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]});}if(!bomData){return/*#__PURE__*/_jsx(\"div\",{style:{padding:'24px',textAlign:'center'},children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u672A\\u627E\\u5230\\u8BA2\\u5355BOM\\u6570\\u636E\"})});}// 计算统计数据\nconst totalItems=bomData.items.length;const availableItems=bomData.items.filter(item=>item.status==='available').length;const shortageItems=bomData.items.filter(item=>item.status==='shortage').length;const pendingItems=bomData.items.filter(item=>item.status==='pending').length;return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px'},children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16,display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ArrowLeftOutlined,{}),onClick:()=>navigate('/bom/order-bom-list'),style:{marginRight:16},children:\"\\u8FD4\\u56DE\\u5217\\u8868\"}),/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u67E5\\u770B\\u8BA2\\u5355BOM\"})]}),/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:handleEdit,disabled:bomData.status==='completed',children:\"\\u7F16\\u8F91\"})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u590D\\u5236\",children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(CopyOutlined,{}),onClick:handleCopy,children:\"\\u590D\\u5236\"})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u6253\\u5370\",children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(PrinterOutlined,{}),onClick:handlePrint,children:\"\\u6253\\u5370\"})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5BFC\\u51FA\",children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5206\\u4EAB\",children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ShareAltOutlined,{}),onClick:handleShare,children:\"\\u5206\\u4EAB\"})})]})]}),/*#__PURE__*/_jsxs(Descriptions,{title:\"\\u57FA\\u672C\\u4FE1\\u606F\",bordered:true,column:3,size:\"small\",style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"BOM\\u7F16\\u7801\",children:/*#__PURE__*/_jsx(Text,{strong:true,children:bomData.bomCode})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"BOM\\u540D\\u79F0\",children:bomData.bomName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7248\\u672C\\u53F7\",children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:bomData.version})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u7F16\\u7801\",children:bomData.productCode}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA7\\u54C1\\u540D\\u79F0\",children:bomData.productName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:renderStatusTag(bomData.status)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u7F16\\u7801\",children:bomData.customerCode}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA2\\u6237\\u540D\\u79F0\",children:bomData.customerName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4F18\\u5148\\u7EA7\",children:renderPriorityTag(bomData.priority)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u53F7\",children:/*#__PURE__*/_jsx(Text,{strong:true,children:bomData.orderNumber})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BA2\\u5355\\u6570\\u91CF\",children:/*#__PURE__*/_jsx(Text,{strong:true,children:bomData.orderQuantity})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4EA4\\u4ED8\\u65E5\\u671F\",children:/*#__PURE__*/_jsx(Text,{strong:true,style:{color:'#1890ff'},children:dayjs(bomData.deliveryDate).format('YYYY-MM-DD')})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u521B\\u5EFA\\u4EBA\",children:bomData.createdBy}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u521B\\u5EFA\\u65F6\\u95F4\",children:bomData.createdAt}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u66F4\\u65B0\\u65F6\\u95F4\",children:bomData.updatedAt}),bomData.approvedBy&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA1\\u6838\\u4EBA\",children:bomData.approvedBy}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5BA1\\u6838\\u65F6\\u95F4\",children:bomData.approvedAt})]}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u63CF\\u8FF0\",span:bomData.approvedBy?1:3,children:bomData.description||'-'})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u91D1\\u989D\",value:bomData.totalAmount,precision:2,prefix:\"\\xA5\",valueStyle:{color:'#1890ff'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7269\\u6599\\u603B\\u6570\",value:totalItems,suffix:\"\\u9879\"})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u53EF\\u7528\\u7269\\u6599\",value:availableItems,suffix:\"\\u9879\",valueStyle:{color:'#52c41a'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7F3A\\u6599\\u6570\\u91CF\",value:shortageItems,suffix:\"\\u9879\",valueStyle:{color:'#ff4d4f'}})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u7269\\u6599\\u53EF\\u7528\\u6027: \"}),/*#__PURE__*/_jsx(Progress,{percent:Math.round(availableItems/totalItems*100),status:shortageItems>0?'exception':'success',format:percent=>\"\".concat(availableItems,\"/\").concat(totalItems,\" (\").concat(percent,\"%)\"),style:{width:300,marginLeft:16}})]}),/*#__PURE__*/_jsx(Divider,{children:\"BOM\\u660E\\u7EC6\"}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:bomData.items,pagination:false,scroll:{x:1400},size:\"small\",bordered:true,expandable:{expandedRowKeys,onExpandedRowsChange:keys=>setExpandedRowKeys(keys),childrenColumnName:'children',indentSize:20}}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16,textAlign:'right'},children:/*#__PURE__*/_jsxs(Text,{strong:true,style:{fontSize:16,color:'#1890ff'},children:[\"\\u603B\\u91D1\\u989D: \\xA5\",bomData.totalAmount.toFixed(2)]})})]}),/*#__PURE__*/_jsxs(Modal,{title:\"\\u5BFC\\u51FABOM\\u660E\\u7EC6\",open:exportModalVisible,onOk:executeExport,onCancel:()=>setExportModalVisible(false),okText:\"\\u5BFC\\u51FA\",cancelText:\"\\u53D6\\u6D88\",width:600,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:8},children:\"\\u5BFC\\u51FA\\u683C\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Select,{value:exportFormat,onChange:setExportFormat,style:{width:'100%'},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"excel\",children:\"Excel (.xlsx)\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"csv\",children:\"CSV (.csv)\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:8},children:\"\\u5BFC\\u51FA\\u5B57\\u6BB5\\uFF1A\"}),/*#__PURE__*/_jsx(Checkbox.Group,{value:exportFields,onChange:setExportFields,style:{width:'100%'},children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"materialCode\",children:\"\\u7269\\u6599\\u7F16\\u7801\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"materialName\",children:\"\\u7269\\u6599\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"specification\",children:\"\\u89C4\\u683C\\u578B\\u53F7\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"unit\",children:\"\\u5355\\u4F4D\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"quantity\",children:\"\\u6570\\u91CF\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"unitPrice\",children:\"\\u5355\\u4EF7\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"totalPrice\",children:\"\\u603B\\u4EF7\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"supplier\",children:\"\\u4F9B\\u5E94\\u5546\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"deliveryDays\",children:\"\\u4EA4\\u671F\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"status\",children:\"\\u72B6\\u6001\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"remark\",children:\"\\u5907\\u6CE8\"})})]})})]})]})]});};export default OrderBOMViewPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Descriptions", "Tag", "Divider", "Row", "Col", "Statistic", "message", "Spin", "<PERSON><PERSON><PERSON>", "Modal", "Progress", "Select", "Checkbox", "XLSX", "ArrowLeftOutlined", "EditOutlined", "PrinterOutlined", "DownloadOutlined", "ShareAltOutlined", "CopyOutlined", "CheckCircleOutlined", "ClockCircleOutlined", "ExclamationCircleOutlined", "StopOutlined", "dayjs", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Title", "Text", "OrderBOMViewPage", "id", "navigate", "loading", "setLoading", "bomData", "setBomData", "expandedRowKeys", "setExpandedRowKeys", "exportModalVisible", "setExportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "loadBOMData", "Promise", "resolve", "setTimeout", "mockData", "bomCode", "bom<PERSON>ame", "version", "productCode", "productName", "customerCode", "customerName", "orderNumber", "orderQuantity", "deliveryDate", "priority", "status", "description", "totalAmount", "created<PERSON>y", "createdAt", "updatedAt", "approvedBy", "approvedAt", "items", "key", "materialCode", "materialName", "specification", "unit", "quantity", "unitPrice", "totalPrice", "supplier", "deliveryDays", "remark", "level", "parent<PERSON><PERSON>", "error", "renderStatusTag", "statusConfig", "draft", "color", "text", "icon", "pending", "approved", "rejected", "in_production", "completed", "config", "children", "renderPriorityTag", "priorityConfig", "high", "medium", "low", "renderMaterialStatusTag", "available", "shortage", "columns", "title", "dataIndex", "width", "render", "record", "style", "paddingLeft", "strong", "value", "toFixed", "concat", "ellipsis", "handleEdit", "handleCopy", "confirm", "content", "onOk", "success", "handlePrint", "window", "print", "handleExport", "executeExport", "exportData", "map", "item", "data", "includes", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "fileName", "Date", "toISOString", "split", "writeFile", "handleShare", "info", "padding", "textAlign", "size", "marginTop", "type", "totalItems", "length", "availableItems", "filter", "shortageItems", "pendingItems", "marginBottom", "display", "justifyContent", "alignItems", "onClick", "marginRight", "margin", "disabled", "bordered", "column", "<PERSON><PERSON>", "label", "format", "span", "gutter", "precision", "prefix", "valueStyle", "suffix", "percent", "Math", "round", "marginLeft", "dataSource", "pagination", "scroll", "x", "expandable", "onExpandedRowsChange", "keys", "childrenColumnName", "indentSize", "fontSize", "open", "onCancel", "okText", "cancelText", "onChange", "Option", "Group"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/OrderBOMViewPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Descriptions,\n  Tag,\n  Divider,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Spin,\n  Tooltip,\n  Modal,\n  Progress,\n  Select,\n  Checkbox\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  ArrowLeftOutlined,\n  EditOutlined,\n  PrinterOutlined,\n  DownloadOutlined,\n  ShareAltOutlined,\n  CopyOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  ExclamationCircleOutlined,\n  StopOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\n\ninterface BOMItem {\n  key: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  unit: string;\n  quantity: number;\n  unitPrice: number;\n  totalPrice: number;\n  supplier: string;\n  deliveryDays: number;\n  remark?: string;\n  level: number;\n  parentKey?: string;\n  status: 'available' | 'shortage' | 'pending';\n}\n\ninterface OrderBOMData {\n  id: string;\n  bomCode: string;\n  bomName: string;\n  version: string;\n  productCode: string;\n  productName: string;\n  customerCode: string;\n  customerName: string;\n  orderNumber: string;\n  orderQuantity: number;\n  deliveryDate: string;\n  priority: 'high' | 'medium' | 'low';\n  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'in_production' | 'completed';\n  description?: string;\n  items: BOMItem[];\n  totalAmount: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  approvedBy?: string;\n  approvedAt?: string;\n}\n\nconst OrderBOMViewPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [bomData, setBomData] = useState<OrderBOMData | null>(null);\n  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['materialCode', 'materialName', 'specification', 'unit', 'quantity', 'unitPrice', 'totalPrice', 'supplier']);\n\n  // 模拟数据加载\n  useEffect(() => {\n    const loadBOMData = async () => {\n      setLoading(true);\n      try {\n        // TODO: 从API获取订单BOM数据\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        // 模拟数据\n        const mockData: OrderBOMData = {\n          id: id || '1',\n          bomCode: 'BOM202401001',\n          bomName: '智能控制器订单BOM',\n          version: '1.0',\n          productCode: 'P001',\n          productName: '智能控制器 V1.0',\n          customerCode: 'C001',\n          customerName: '华为技术有限公司',\n          orderNumber: 'ORD202401001',\n          orderQuantity: 100,\n          deliveryDate: '2024-02-15',\n          priority: 'high',\n          status: 'approved',\n          description: '华为智能控制器项目专用BOM，包含主控芯片、传感器模块等核心组件',\n          totalAmount: 4580.00,\n          createdBy: '张三',\n          createdAt: '2024-01-15 10:30:00',\n          updatedAt: '2024-01-16 14:20:00',\n          approvedBy: '李四',\n          approvedAt: '2024-01-16 16:45:00',\n          items: [\n            {\n              key: '1',\n              materialCode: 'M003',\n              materialName: '集成电路 STM32',\n              specification: 'STM32F407VGT6',\n              unit: '片',\n              quantity: 1,\n              unitPrice: 25.00,\n              totalPrice: 25.00,\n              supplier: '意法半导体',\n              deliveryDays: 7,\n              remark: '主控芯片',\n              level: 1,\n              status: 'available'\n            },\n            {\n              key: '2',\n              materialCode: 'M004',\n              materialName: 'PCB板 主板',\n              specification: '4层板 100x80mm',\n              unit: '块',\n              quantity: 1,\n              unitPrice: 15.80,\n              totalPrice: 15.80,\n              supplier: '深圳PCB厂',\n              deliveryDays: 5,\n              remark: '主控板',\n              level: 1,\n              status: 'available'\n            },\n            {\n              key: '3',\n              materialCode: 'M001',\n              materialName: '电阻器 10KΩ',\n              specification: '0603 1% 1/10W',\n              unit: '个',\n              quantity: 10,\n              unitPrice: 0.50,\n              totalPrice: 5.00,\n              supplier: '国巨电子',\n              deliveryDays: 3,\n              remark: '上拉电阻',\n              level: 2,\n              parentKey: '2',\n              status: 'available'\n            },\n            {\n              key: '4',\n              materialCode: 'M002',\n              materialName: '电容器 100μF',\n              specification: '25V 电解电容',\n              unit: '个',\n              quantity: 5,\n              unitPrice: 1.20,\n              totalPrice: 6.00,\n              supplier: '松下电器',\n              deliveryDays: 4,\n              remark: '滤波电容',\n              level: 2,\n              parentKey: '2',\n              status: 'shortage'\n            },\n            {\n              key: '5',\n              materialCode: 'M005',\n              materialName: '外壳 塑料',\n              specification: 'ABS 黑色',\n              unit: '个',\n              quantity: 1,\n              unitPrice: 8.50,\n              totalPrice: 8.50,\n              supplier: '塑料制品厂',\n              deliveryDays: 10,\n              remark: '产品外壳',\n              level: 1,\n              status: 'pending'\n            }\n          ]\n        };\n        \n        setBomData(mockData);\n      } catch (error) {\n        message.error('加载BOM数据失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id) {\n      loadBOMData();\n    }\n  }, [id]);\n\n  // 状态标签渲染\n  const renderStatusTag = (status: string) => {\n    const statusConfig = {\n      draft: { color: 'default', text: '草稿', icon: <EditOutlined /> },\n      pending: { color: 'processing', text: '待审核', icon: <ClockCircleOutlined /> },\n      approved: { color: 'success', text: '已审核', icon: <CheckCircleOutlined /> },\n      rejected: { color: 'error', text: '已拒绝', icon: <StopOutlined /> },\n      in_production: { color: 'warning', text: '生产中', icon: <ExclamationCircleOutlined /> },\n      completed: { color: 'success', text: '已完成', icon: <CheckCircleOutlined /> }\n    };\n    \n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;\n    return (\n      <Tag color={config.color} icon={config.icon}>\n        {config.text}\n      </Tag>\n    );\n  };\n\n  // 优先级标签渲染\n  const renderPriorityTag = (priority: string) => {\n    const priorityConfig = {\n      high: { color: 'red', text: '高' },\n      medium: { color: 'orange', text: '中' },\n      low: { color: 'green', text: '低' }\n    };\n    \n    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  // 物料状态标签渲染\n  const renderMaterialStatusTag = (status: string) => {\n    const statusConfig = {\n      available: { color: 'success', text: '可用' },\n      shortage: { color: 'error', text: '缺料' },\n      pending: { color: 'warning', text: '待采购' }\n    };\n    \n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.available;\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<BOMItem> = [\n    {\n      title: '层级',\n      dataIndex: 'level',\n      width: 60,\n      render: (level: number) => (\n        <Tag color={level === 1 ? 'blue' : level === 2 ? 'green' : 'orange'}>\n          L{level}\n        </Tag>\n      )\n    },\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      width: 120,\n      render: (text: string, record: BOMItem) => (\n        <div style={{ paddingLeft: (record.level - 1) * 20 }}>\n          <Text strong>{text}</Text>\n        </div>\n      )\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      width: 200\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      width: 150\n    },\n    {\n      title: '单位',\n      dataIndex: 'unit',\n      width: 80\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      width: 100,\n      render: (value: number) => value.toFixed(2)\n    },\n    {\n      title: '单价(元)',\n      dataIndex: 'unitPrice',\n      width: 100,\n      render: (value: number) => `¥${value.toFixed(2)}`\n    },\n    {\n      title: '总价(元)',\n      dataIndex: 'totalPrice',\n      width: 120,\n      render: (value: number) => (\n        <Text strong style={{ color: '#1890ff' }}>\n          ¥{value.toFixed(2)}\n        </Text>\n      )\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      width: 120\n    },\n    {\n      title: '交期(天)',\n      dataIndex: 'deliveryDays',\n      width: 100\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      width: 100,\n      render: (status: string) => renderMaterialStatusTag(status)\n    },\n    {\n      title: '备注',\n      dataIndex: 'remark',\n      width: 150,\n      ellipsis: true\n    }\n  ];\n\n  // 处理编辑\n  const handleEdit = () => {\n    navigate(`/bom/order-bom-edit/${id}`);\n  };\n\n  // 处理复制\n  const handleCopy = () => {\n    Modal.confirm({\n      title: '确认复制',\n      content: '确定要复制这个订单BOM吗？',\n      onOk: () => {\n        // TODO: 实现复制功能\n        message.success('BOM复制成功');\n        navigate('/bom/order-bom-create');\n      }\n    });\n  };\n\n  // 处理打印\n  const handlePrint = () => {\n    window.print();\n  };\n\n  // 处理导出\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const executeExport = () => {\n    if (!bomData) return;\n    \n    try {\n      // 准备导出数据\n      const exportData = bomData.items.map(item => {\n        const data: any = {};\n        \n        if (exportFields.includes('materialCode')) data['物料编码'] = item.materialCode;\n        if (exportFields.includes('materialName')) data['物料名称'] = item.materialName;\n        if (exportFields.includes('specification')) data['规格型号'] = item.specification;\n        if (exportFields.includes('unit')) data['单位'] = item.unit;\n        if (exportFields.includes('quantity')) data['数量'] = item.quantity;\n        if (exportFields.includes('unitPrice')) data['单价'] = item.unitPrice;\n        if (exportFields.includes('totalPrice')) data['总价'] = item.totalPrice;\n        if (exportFields.includes('supplier')) data['供应商'] = item.supplier;\n        if (exportFields.includes('deliveryDays')) data['交期(天)'] = item.deliveryDays;\n        if (exportFields.includes('status')) data['状态'] = item.status === 'available' ? '可用' : item.status === 'shortage' ? '缺料' : '待采购';\n        if (exportFields.includes('remark')) data['备注'] = item.remark || '';\n        \n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, 'BOM明细');\n\n      // 下载文件\n      const fileName = `${bomData.bomCode}_BOM明细_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      \n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  // 处理分享\n  const handleShare = () => {\n    // TODO: 实现分享功能\n    message.info('分享功能开发中...');\n  };\n\n  if (loading) {\n    return (\n      <div style={{ padding: '24px', textAlign: 'center' }}>\n        <Spin size=\"large\" />\n        <p style={{ marginTop: 16 }}>加载中...</p>\n      </div>\n    );\n  }\n\n  if (!bomData) {\n    return (\n      <div style={{ padding: '24px', textAlign: 'center' }}>\n        <Text type=\"secondary\">未找到订单BOM数据</Text>\n      </div>\n    );\n  }\n\n  // 计算统计数据\n  const totalItems = bomData.items.length;\n  const availableItems = bomData.items.filter(item => item.status === 'available').length;\n  const shortageItems = bomData.items.filter(item => item.status === 'shortage').length;\n  const pendingItems = bomData.items.filter(item => item.status === 'pending').length;\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Card>\n        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <Button\n              icon={<ArrowLeftOutlined />}\n              onClick={() => navigate('/bom/order-bom-list')}\n              style={{ marginRight: 16 }}\n            >\n              返回列表\n            </Button>\n            <Title level={4} style={{ margin: 0 }}>查看订单BOM</Title>\n          </div>\n          <Space>\n            <Tooltip title=\"编辑\">\n              <Button\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n                disabled={bomData.status === 'completed'}\n              >\n                编辑\n              </Button>\n            </Tooltip>\n            <Tooltip title=\"复制\">\n              <Button\n                icon={<CopyOutlined />}\n                onClick={handleCopy}\n              >\n                复制\n              </Button>\n            </Tooltip>\n            <Tooltip title=\"打印\">\n              <Button\n                icon={<PrinterOutlined />}\n                onClick={handlePrint}\n              >\n                打印\n              </Button>\n            </Tooltip>\n            <Tooltip title=\"导出\">\n              <Button\n                icon={<DownloadOutlined />}\n                onClick={handleExport}\n              >\n                导出\n              </Button>\n            </Tooltip>\n            <Tooltip title=\"分享\">\n              <Button\n                icon={<ShareAltOutlined />}\n                onClick={handleShare}\n              >\n                分享\n              </Button>\n            </Tooltip>\n          </Space>\n        </div>\n\n        {/* 基本信息 */}\n        <Descriptions\n          title=\"基本信息\"\n          bordered\n          column={3}\n          size=\"small\"\n          style={{ marginBottom: 24 }}\n        >\n          <Descriptions.Item label=\"BOM编码\">\n            <Text strong>{bomData.bomCode}</Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"BOM名称\">\n            {bomData.bomName}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"版本号\">\n            <Tag color=\"blue\">{bomData.version}</Tag>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"产品编码\">\n            {bomData.productCode}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"产品名称\">\n            {bomData.productName}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"状态\">\n            {renderStatusTag(bomData.status)}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"客户编码\">\n            {bomData.customerCode}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"客户名称\">\n            {bomData.customerName}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"优先级\">\n            {renderPriorityTag(bomData.priority)}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"订单号\">\n            <Text strong>{bomData.orderNumber}</Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"订单数量\">\n            <Text strong>{bomData.orderQuantity}</Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"交付日期\">\n            <Text strong style={{ color: '#1890ff' }}>\n              {dayjs(bomData.deliveryDate).format('YYYY-MM-DD')}\n            </Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建人\">\n            {bomData.createdBy}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {bomData.createdAt}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"更新时间\">\n            {bomData.updatedAt}\n          </Descriptions.Item>\n          {bomData.approvedBy && (\n            <>\n              <Descriptions.Item label=\"审核人\">\n                {bomData.approvedBy}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"审核时间\">\n                {bomData.approvedAt}\n              </Descriptions.Item>\n            </>\n          )}\n          <Descriptions.Item label=\"描述\" span={bomData.approvedBy ? 1 : 3}>\n            {bomData.description || '-'}\n          </Descriptions.Item>\n        </Descriptions>\n\n        {/* 统计信息 */}\n        <Row gutter={16} style={{ marginBottom: 24 }}>\n          <Col span={6}>\n            <Statistic\n              title=\"总金额\"\n              value={bomData.totalAmount}\n              precision={2}\n              prefix=\"¥\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"物料总数\"\n              value={totalItems}\n              suffix=\"项\"\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"可用物料\"\n              value={availableItems}\n              suffix=\"项\"\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"缺料数量\"\n              value={shortageItems}\n              suffix=\"项\"\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n        </Row>\n\n        {/* 物料可用性进度 */}\n        <div style={{ marginBottom: 24 }}>\n          <Text strong>物料可用性: </Text>\n          <Progress\n            percent={Math.round((availableItems / totalItems) * 100)}\n            status={shortageItems > 0 ? 'exception' : 'success'}\n            format={(percent) => `${availableItems}/${totalItems} (${percent}%)`}\n            style={{ width: 300, marginLeft: 16 }}\n          />\n        </div>\n\n        <Divider>BOM明细</Divider>\n\n        <Table\n          columns={columns}\n          dataSource={bomData.items}\n          pagination={false}\n          scroll={{ x: 1400 }}\n          size=\"small\"\n          bordered\n          expandable={{\n            expandedRowKeys,\n            onExpandedRowsChange: (keys) => setExpandedRowKeys(keys as React.Key[]),\n            childrenColumnName: 'children',\n            indentSize: 20\n          }}\n        />\n\n        <div style={{ marginTop: 16, textAlign: 'right' }}>\n          <Text strong style={{ fontSize: 16, color: '#1890ff' }}>\n            总金额: ¥{bomData.totalAmount.toFixed(2)}\n          </Text>\n        </div>\n      </Card>\n\n      {/* 导出模态框 */}\n      <Modal\n        title=\"导出BOM明细\"\n        open={exportModalVisible}\n        onOk={executeExport}\n        onCancel={() => setExportModalVisible(false)}\n        okText=\"导出\"\n        cancelText=\"取消\"\n        width={600}\n      >\n        <div style={{ marginBottom: 16 }}>\n          <label style={{ display: 'block', marginBottom: 8 }}>导出格式：</label>\n          <Select\n            value={exportFormat}\n            onChange={setExportFormat}\n            style={{ width: '100%' }}\n          >\n            <Select.Option value=\"excel\">Excel (.xlsx)</Select.Option>\n            <Select.Option value=\"csv\">CSV (.csv)</Select.Option>\n          </Select>\n        </div>\n        \n        <div>\n          <label style={{ display: 'block', marginBottom: 8 }}>导出字段：</label>\n          <Checkbox.Group\n            value={exportFields}\n            onChange={setExportFields}\n            style={{ width: '100%' }}\n          >\n            <Row>\n              <Col span={12}>\n                <Checkbox value=\"materialCode\">物料编码</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"materialName\">物料名称</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"specification\">规格型号</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"unit\">单位</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"quantity\">数量</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"unitPrice\">单价</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"totalPrice\">总价</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"supplier\">供应商</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"deliveryDays\">交期</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"status\">状态</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"remark\">备注</Checkbox>\n              </Col>\n            </Row>\n          </Checkbox.Group>\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderBOMViewPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OACEC,IAAI,CACJC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,YAAY,CACZC,GAAG,CACHC,OAAO,CACPC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,OAAO,CACPC,IAAI,CACJC,OAAO,CACPC,KAAK,CACLC,QAAQ,CACRC,MAAM,CACNC,QAAQ,KACH,MAAM,CACb,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,OACEC,iBAAiB,CACjBC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,gBAAgB,CAChBC,YAAY,CACZC,mBAAmB,CACnBC,mBAAmB,CACnBC,yBAAyB,CACzBC,YAAY,KACP,mBAAmB,CAE1B,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1B,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGpC,UAAU,CA2ClC,KAAM,CAAAqC,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAAEC,EAAG,CAAC,CAAGzC,SAAS,CAAiB,CAAC,CAC1C,KAAM,CAAA0C,QAAQ,CAAGzC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC0C,OAAO,CAAEC,UAAU,CAAC,CAAG9C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC+C,OAAO,CAAEC,UAAU,CAAC,CAAGhD,QAAQ,CAAsB,IAAI,CAAC,CACjE,KAAM,CAACiD,eAAe,CAAEC,kBAAkB,CAAC,CAAGlD,QAAQ,CAAc,EAAE,CAAC,CACvE,KAAM,CAACmD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACqD,YAAY,CAAEC,eAAe,CAAC,CAAGtD,QAAQ,CAAkB,OAAO,CAAC,CAC1E,KAAM,CAACuD,YAAY,CAAEC,eAAe,CAAC,CAAGxD,QAAQ,CAAW,CAAC,cAAc,CAAE,cAAc,CAAE,eAAe,CAAE,MAAM,CAAE,UAAU,CAAE,WAAW,CAAE,YAAY,CAAE,UAAU,CAAC,CAAC,CAExK;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwD,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9BX,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,IAAI,CAAAY,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAE,QAAsB,CAAG,CAC7BlB,EAAE,CAAEA,EAAE,EAAI,GAAG,CACbmB,OAAO,CAAE,cAAc,CACvBC,OAAO,CAAE,YAAY,CACrBC,OAAO,CAAE,KAAK,CACdC,WAAW,CAAE,MAAM,CACnBC,WAAW,CAAE,YAAY,CACzBC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,UAAU,CACxBC,WAAW,CAAE,cAAc,CAC3BC,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,MAAM,CAChBC,MAAM,CAAE,UAAU,CAClBC,WAAW,CAAE,kCAAkC,CAC/CC,WAAW,CAAE,OAAO,CACpBC,SAAS,CAAE,IAAI,CACfC,SAAS,CAAE,qBAAqB,CAChCC,SAAS,CAAE,qBAAqB,CAChCC,UAAU,CAAE,IAAI,CAChBC,UAAU,CAAE,qBAAqB,CACjCC,KAAK,CAAE,CACL,CACEC,GAAG,CAAE,GAAG,CACRC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,YAAY,CAC1BC,aAAa,CAAE,eAAe,CAC9BC,IAAI,CAAE,GAAG,CACTC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,KAAK,CAChBC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAE,OAAO,CACjBC,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,CAAC,CACRpB,MAAM,CAAE,WACV,CAAC,CACD,CACES,GAAG,CAAE,GAAG,CACRC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,SAAS,CACvBC,aAAa,CAAE,cAAc,CAC7BC,IAAI,CAAE,GAAG,CACTC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,KAAK,CAChBC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,KAAK,CACbC,KAAK,CAAE,CAAC,CACRpB,MAAM,CAAE,WACV,CAAC,CACD,CACES,GAAG,CAAE,GAAG,CACRC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,UAAU,CACxBC,aAAa,CAAE,eAAe,CAC9BC,IAAI,CAAE,GAAG,CACTC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,MAAM,CAChBC,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,GAAG,CACdrB,MAAM,CAAE,WACV,CAAC,CACD,CACES,GAAG,CAAE,GAAG,CACRC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,WAAW,CACzBC,aAAa,CAAE,UAAU,CACzBC,IAAI,CAAE,GAAG,CACTC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,MAAM,CAChBC,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,GAAG,CACdrB,MAAM,CAAE,UACV,CAAC,CACD,CACES,GAAG,CAAE,GAAG,CACRC,YAAY,CAAE,MAAM,CACpBC,YAAY,CAAE,OAAO,CACrBC,aAAa,CAAE,QAAQ,CACvBC,IAAI,CAAE,GAAG,CACTC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,OAAO,CACjBC,YAAY,CAAE,EAAE,CAChBC,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,CAAC,CACRpB,MAAM,CAAE,SACV,CAAC,CAEL,CAAC,CAEDzB,UAAU,CAACa,QAAQ,CAAC,CACtB,CAAE,MAAOkC,KAAK,CAAE,CACdhF,OAAO,CAACgF,KAAK,CAAC,WAAW,CAAC,CAC5B,CAAC,OAAS,CACRjD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIH,EAAE,CAAE,CACNc,WAAW,CAAC,CAAC,CACf,CACF,CAAC,CAAE,CAACd,EAAE,CAAC,CAAC,CAER;AACA,KAAM,CAAAqD,eAAe,CAAIvB,MAAc,EAAK,CAC1C,KAAM,CAAAwB,YAAY,CAAG,CACnBC,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAI,CAAEC,IAAI,cAAElE,IAAA,CAACX,YAAY,GAAE,CAAE,CAAC,CAC/D8E,OAAO,CAAE,CAAEH,KAAK,CAAE,YAAY,CAAEC,IAAI,CAAE,KAAK,CAAEC,IAAI,cAAElE,IAAA,CAACL,mBAAmB,GAAE,CAAE,CAAC,CAC5EyE,QAAQ,CAAE,CAAEJ,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,KAAK,CAAEC,IAAI,cAAElE,IAAA,CAACN,mBAAmB,GAAE,CAAE,CAAC,CAC1E2E,QAAQ,CAAE,CAAEL,KAAK,CAAE,OAAO,CAAEC,IAAI,CAAE,KAAK,CAAEC,IAAI,cAAElE,IAAA,CAACH,YAAY,GAAE,CAAE,CAAC,CACjEyE,aAAa,CAAE,CAAEN,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,KAAK,CAAEC,IAAI,cAAElE,IAAA,CAACJ,yBAAyB,GAAE,CAAE,CAAC,CACrF2E,SAAS,CAAE,CAAEP,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,KAAK,CAAEC,IAAI,cAAElE,IAAA,CAACN,mBAAmB,GAAE,CAAE,CAC5E,CAAC,CAED,KAAM,CAAA8E,MAAM,CAAGV,YAAY,CAACxB,MAAM,CAA8B,EAAIwB,YAAY,CAACC,KAAK,CACtF,mBACE/D,IAAA,CAACzB,GAAG,EAACyF,KAAK,CAAEQ,MAAM,CAACR,KAAM,CAACE,IAAI,CAAEM,MAAM,CAACN,IAAK,CAAAO,QAAA,CACzCD,MAAM,CAACP,IAAI,CACT,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAS,iBAAiB,CAAIrC,QAAgB,EAAK,CAC9C,KAAM,CAAAsC,cAAc,CAAG,CACrBC,IAAI,CAAE,CAAEZ,KAAK,CAAE,KAAK,CAAEC,IAAI,CAAE,GAAI,CAAC,CACjCY,MAAM,CAAE,CAAEb,KAAK,CAAE,QAAQ,CAAEC,IAAI,CAAE,GAAI,CAAC,CACtCa,GAAG,CAAE,CAAEd,KAAK,CAAE,OAAO,CAAEC,IAAI,CAAE,GAAI,CACnC,CAAC,CAED,KAAM,CAAAO,MAAM,CAAGG,cAAc,CAACtC,QAAQ,CAAgC,EAAIsC,cAAc,CAACE,MAAM,CAC/F,mBAAO7E,IAAA,CAACzB,GAAG,EAACyF,KAAK,CAAEQ,MAAM,CAACR,KAAM,CAAAS,QAAA,CAAED,MAAM,CAACP,IAAI,CAAM,CAAC,CACtD,CAAC,CAED;AACA,KAAM,CAAAc,uBAAuB,CAAIzC,MAAc,EAAK,CAClD,KAAM,CAAAwB,YAAY,CAAG,CACnBkB,SAAS,CAAE,CAAEhB,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC3CgB,QAAQ,CAAE,CAAEjB,KAAK,CAAE,OAAO,CAAEC,IAAI,CAAE,IAAK,CAAC,CACxCE,OAAO,CAAE,CAAEH,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,KAAM,CAC3C,CAAC,CAED,KAAM,CAAAO,MAAM,CAAGV,YAAY,CAACxB,MAAM,CAA8B,EAAIwB,YAAY,CAACkB,SAAS,CAC1F,mBAAOhF,IAAA,CAACzB,GAAG,EAACyF,KAAK,CAAEQ,MAAM,CAACR,KAAM,CAAAS,QAAA,CAAED,MAAM,CAACP,IAAI,CAAM,CAAC,CACtD,CAAC,CAED;AACA,KAAM,CAAAiB,OAA6B,CAAG,CACpC,CACEC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,OAAO,CAClBC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAG5B,KAAa,eACpBxD,KAAA,CAAC3B,GAAG,EAACyF,KAAK,CAAEN,KAAK,GAAK,CAAC,CAAG,MAAM,CAAGA,KAAK,GAAK,CAAC,CAAG,OAAO,CAAG,QAAS,CAAAe,QAAA,EAAC,GAClE,CAACf,KAAK,EACJ,CAET,CAAC,CACD,CACEyB,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACrB,IAAY,CAAEsB,MAAe,gBACpCvF,IAAA,QAAKwF,KAAK,CAAE,CAAEC,WAAW,CAAE,CAACF,MAAM,CAAC7B,KAAK,CAAG,CAAC,EAAI,EAAG,CAAE,CAAAe,QAAA,cACnDzE,IAAA,CAACM,IAAI,EAACoF,MAAM,MAAAjB,QAAA,CAAER,IAAI,CAAO,CAAC,CACvB,CAET,CAAC,CACD,CACEkB,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,KAAK,CAAE,GACT,CAAC,CACD,CACEF,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,eAAe,CAC1BC,KAAK,CAAE,GACT,CAAC,CACD,CACEF,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,MAAM,CACjBC,KAAK,CAAE,EACT,CAAC,CACD,CACEF,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGK,KAAa,EAAKA,KAAK,CAACC,OAAO,CAAC,CAAC,CAC5C,CAAC,CACD,CACET,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,WAAW,CACtBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGK,KAAa,SAAAE,MAAA,CAASF,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CACjD,CAAC,CACD,CACET,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,YAAY,CACvBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGK,KAAa,eACpBzF,KAAA,CAACI,IAAI,EAACoF,MAAM,MAACF,KAAK,CAAE,CAAExB,KAAK,CAAE,SAAU,CAAE,CAAAS,QAAA,EAAC,MACvC,CAACkB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,EACd,CAEV,CAAC,CACD,CACET,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBC,KAAK,CAAE,GACT,CAAC,CACD,CACEF,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,cAAc,CACzBC,KAAK,CAAE,GACT,CAAC,CACD,CACEF,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGhD,MAAc,EAAKyC,uBAAuB,CAACzC,MAAM,CAC5D,CAAC,CACD,CACE6C,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBC,KAAK,CAAE,GAAG,CACVS,QAAQ,CAAE,IACZ,CAAC,CACF,CAED;AACA,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvBtF,QAAQ,wBAAAoF,MAAA,CAAwBrF,EAAE,CAAE,CAAC,CACvC,CAAC,CAED;AACA,KAAM,CAAAwF,UAAU,CAAGA,CAAA,GAAM,CACvBjH,KAAK,CAACkH,OAAO,CAAC,CACZd,KAAK,CAAE,MAAM,CACbe,OAAO,CAAE,gBAAgB,CACzBC,IAAI,CAAEA,CAAA,GAAM,CACV;AACAvH,OAAO,CAACwH,OAAO,CAAC,SAAS,CAAC,CAC1B3F,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA4F,WAAW,CAAGA,CAAA,GAAM,CACxBC,MAAM,CAACC,KAAK,CAAC,CAAC,CAChB,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBvF,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAwF,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CAAC7F,OAAO,CAAE,OAEd,GAAI,CACF;AACA,KAAM,CAAA8F,UAAU,CAAG9F,OAAO,CAACkC,KAAK,CAAC6D,GAAG,CAACC,IAAI,EAAI,CAC3C,KAAM,CAAAC,IAAS,CAAG,CAAC,CAAC,CAEpB,GAAIzF,YAAY,CAAC0F,QAAQ,CAAC,cAAc,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,IAAI,CAAC5D,YAAY,CAC3E,GAAI5B,YAAY,CAAC0F,QAAQ,CAAC,cAAc,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,IAAI,CAAC3D,YAAY,CAC3E,GAAI7B,YAAY,CAAC0F,QAAQ,CAAC,eAAe,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,IAAI,CAAC1D,aAAa,CAC7E,GAAI9B,YAAY,CAAC0F,QAAQ,CAAC,MAAM,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACzD,IAAI,CACzD,GAAI/B,YAAY,CAAC0F,QAAQ,CAAC,UAAU,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACxD,QAAQ,CACjE,GAAIhC,YAAY,CAAC0F,QAAQ,CAAC,WAAW,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACvD,SAAS,CACnE,GAAIjC,YAAY,CAAC0F,QAAQ,CAAC,YAAY,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACtD,UAAU,CACrE,GAAIlC,YAAY,CAAC0F,QAAQ,CAAC,UAAU,CAAC,CAAED,IAAI,CAAC,KAAK,CAAC,CAAGD,IAAI,CAACrD,QAAQ,CAClE,GAAInC,YAAY,CAAC0F,QAAQ,CAAC,cAAc,CAAC,CAAED,IAAI,CAAC,OAAO,CAAC,CAAGD,IAAI,CAACpD,YAAY,CAC5E,GAAIpC,YAAY,CAAC0F,QAAQ,CAAC,QAAQ,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACtE,MAAM,GAAK,WAAW,CAAG,IAAI,CAAGsE,IAAI,CAACtE,MAAM,GAAK,UAAU,CAAG,IAAI,CAAG,KAAK,CAChI,GAAIlB,YAAY,CAAC0F,QAAQ,CAAC,QAAQ,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACnD,MAAM,EAAI,EAAE,CAEnE,MAAO,CAAAoD,IAAI,CACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,EAAE,CAAG5H,IAAI,CAAC6H,KAAK,CAACC,aAAa,CAACP,UAAU,CAAC,CAC/C,KAAM,CAAAQ,EAAE,CAAG/H,IAAI,CAAC6H,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChChI,IAAI,CAAC6H,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,OAAO,CAAC,CAE7C;AACA,KAAM,CAAAM,QAAQ,IAAAxB,MAAA,CAAMjF,OAAO,CAACe,OAAO,sBAAAkE,MAAA,CAAU,GAAI,CAAAyB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAA3B,MAAA,CAAI3E,YAAY,GAAK,OAAO,CAAG,MAAM,CAAG,KAAK,CAAE,CAClI/B,IAAI,CAACsI,SAAS,CAACP,EAAE,CAAEG,QAAQ,CAAC,CAE5BzI,OAAO,CAACwH,OAAO,CAAC,MAAM,CAAC,CACvBnF,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,MAAO2C,KAAK,CAAE,CACdhF,OAAO,CAACgF,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAA8D,WAAW,CAAGA,CAAA,GAAM,CACxB;AACA9I,OAAO,CAAC+I,IAAI,CAAC,YAAY,CAAC,CAC5B,CAAC,CAED,GAAIjH,OAAO,CAAE,CACX,mBACER,KAAA,QAAKsF,KAAK,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAApD,QAAA,eACnDzE,IAAA,CAACnB,IAAI,EAACiJ,IAAI,CAAC,OAAO,CAAE,CAAC,cACrB9H,IAAA,MAAGwF,KAAK,CAAE,CAAEuC,SAAS,CAAE,EAAG,CAAE,CAAAtD,QAAA,CAAC,uBAAM,CAAG,CAAC,EACpC,CAAC,CAEV,CAEA,GAAI,CAAC7D,OAAO,CAAE,CACZ,mBACEZ,IAAA,QAAKwF,KAAK,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAApD,QAAA,cACnDzE,IAAA,CAACM,IAAI,EAAC0H,IAAI,CAAC,WAAW,CAAAvD,QAAA,CAAC,+CAAU,CAAM,CAAC,CACrC,CAAC,CAEV,CAEA;AACA,KAAM,CAAAwD,UAAU,CAAGrH,OAAO,CAACkC,KAAK,CAACoF,MAAM,CACvC,KAAM,CAAAC,cAAc,CAAGvH,OAAO,CAACkC,KAAK,CAACsF,MAAM,CAACxB,IAAI,EAAIA,IAAI,CAACtE,MAAM,GAAK,WAAW,CAAC,CAAC4F,MAAM,CACvF,KAAM,CAAAG,aAAa,CAAGzH,OAAO,CAACkC,KAAK,CAACsF,MAAM,CAACxB,IAAI,EAAIA,IAAI,CAACtE,MAAM,GAAK,UAAU,CAAC,CAAC4F,MAAM,CACrF,KAAM,CAAAI,YAAY,CAAG1H,OAAO,CAACkC,KAAK,CAACsF,MAAM,CAACxB,IAAI,EAAIA,IAAI,CAACtE,MAAM,GAAK,SAAS,CAAC,CAAC4F,MAAM,CAEnF,mBACEhI,KAAA,QAAKsF,KAAK,CAAE,CAAEoC,OAAO,CAAE,MAAO,CAAE,CAAAnD,QAAA,eAC9BvE,KAAA,CAACjC,IAAI,EAAAwG,QAAA,eACHvE,KAAA,QAAKsF,KAAK,CAAE,CAAE+C,YAAY,CAAE,EAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAjE,QAAA,eACvGvE,KAAA,QAAKsF,KAAK,CAAE,CAAEgD,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAjE,QAAA,eACpDzE,IAAA,CAAC7B,MAAM,EACL+F,IAAI,cAAElE,IAAA,CAACZ,iBAAiB,GAAE,CAAE,CAC5BuJ,OAAO,CAAEA,CAAA,GAAMlI,QAAQ,CAAC,qBAAqB,CAAE,CAC/C+E,KAAK,CAAE,CAAEoD,WAAW,CAAE,EAAG,CAAE,CAAAnE,QAAA,CAC5B,0BAED,CAAQ,CAAC,cACTzE,IAAA,CAACK,KAAK,EAACqD,KAAK,CAAE,CAAE,CAAC8B,KAAK,CAAE,CAAEqD,MAAM,CAAE,CAAE,CAAE,CAAApE,QAAA,CAAC,6BAAO,CAAO,CAAC,EACnD,CAAC,cACNvE,KAAA,CAAC7B,KAAK,EAAAoG,QAAA,eACJzE,IAAA,CAAClB,OAAO,EAACqG,KAAK,CAAC,cAAI,CAAAV,QAAA,cACjBzE,IAAA,CAAC7B,MAAM,EACL+F,IAAI,cAAElE,IAAA,CAACX,YAAY,GAAE,CAAE,CACvBsJ,OAAO,CAAE5C,UAAW,CACpB+C,QAAQ,CAAElI,OAAO,CAAC0B,MAAM,GAAK,WAAY,CAAAmC,QAAA,CAC1C,cAED,CAAQ,CAAC,CACF,CAAC,cACVzE,IAAA,CAAClB,OAAO,EAACqG,KAAK,CAAC,cAAI,CAAAV,QAAA,cACjBzE,IAAA,CAAC7B,MAAM,EACL+F,IAAI,cAAElE,IAAA,CAACP,YAAY,GAAE,CAAE,CACvBkJ,OAAO,CAAE3C,UAAW,CAAAvB,QAAA,CACrB,cAED,CAAQ,CAAC,CACF,CAAC,cACVzE,IAAA,CAAClB,OAAO,EAACqG,KAAK,CAAC,cAAI,CAAAV,QAAA,cACjBzE,IAAA,CAAC7B,MAAM,EACL+F,IAAI,cAAElE,IAAA,CAACV,eAAe,GAAE,CAAE,CAC1BqJ,OAAO,CAAEtC,WAAY,CAAA5B,QAAA,CACtB,cAED,CAAQ,CAAC,CACF,CAAC,cACVzE,IAAA,CAAClB,OAAO,EAACqG,KAAK,CAAC,cAAI,CAAAV,QAAA,cACjBzE,IAAA,CAAC7B,MAAM,EACL+F,IAAI,cAAElE,IAAA,CAACT,gBAAgB,GAAE,CAAE,CAC3BoJ,OAAO,CAAEnC,YAAa,CAAA/B,QAAA,CACvB,cAED,CAAQ,CAAC,CACF,CAAC,cACVzE,IAAA,CAAClB,OAAO,EAACqG,KAAK,CAAC,cAAI,CAAAV,QAAA,cACjBzE,IAAA,CAAC7B,MAAM,EACL+F,IAAI,cAAElE,IAAA,CAACR,gBAAgB,GAAE,CAAE,CAC3BmJ,OAAO,CAAEjB,WAAY,CAAAjD,QAAA,CACtB,cAED,CAAQ,CAAC,CACF,CAAC,EACL,CAAC,EACL,CAAC,cAGNvE,KAAA,CAAC5B,YAAY,EACX6G,KAAK,CAAC,0BAAM,CACZ4D,QAAQ,MACRC,MAAM,CAAE,CAAE,CACVlB,IAAI,CAAC,OAAO,CACZtC,KAAK,CAAE,CAAE+C,YAAY,CAAE,EAAG,CAAE,CAAA9D,QAAA,eAE5BzE,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,iBAAO,CAAAzE,QAAA,cAC9BzE,IAAA,CAACM,IAAI,EAACoF,MAAM,MAAAjB,QAAA,CAAE7D,OAAO,CAACe,OAAO,CAAO,CAAC,CACpB,CAAC,cACpB3B,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,iBAAO,CAAAzE,QAAA,CAC7B7D,OAAO,CAACgB,OAAO,CACC,CAAC,cACpB5B,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAzE,QAAA,cAC5BzE,IAAA,CAACzB,GAAG,EAACyF,KAAK,CAAC,MAAM,CAAAS,QAAA,CAAE7D,OAAO,CAACiB,OAAO,CAAM,CAAC,CACxB,CAAC,cACpB7B,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,CAC5B7D,OAAO,CAACkB,WAAW,CACH,CAAC,cACpB9B,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,CAC5B7D,OAAO,CAACmB,WAAW,CACH,CAAC,cACpB/B,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,cAAI,CAAAzE,QAAA,CAC1BZ,eAAe,CAACjD,OAAO,CAAC0B,MAAM,CAAC,CACf,CAAC,cACpBtC,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,CAC5B7D,OAAO,CAACoB,YAAY,CACJ,CAAC,cACpBhC,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,CAC5B7D,OAAO,CAACqB,YAAY,CACJ,CAAC,cACpBjC,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAzE,QAAA,CAC3BC,iBAAiB,CAAC9D,OAAO,CAACyB,QAAQ,CAAC,CACnB,CAAC,cACpBrC,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAzE,QAAA,cAC5BzE,IAAA,CAACM,IAAI,EAACoF,MAAM,MAAAjB,QAAA,CAAE7D,OAAO,CAACsB,WAAW,CAAO,CAAC,CACxB,CAAC,cACpBlC,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,cAC7BzE,IAAA,CAACM,IAAI,EAACoF,MAAM,MAAAjB,QAAA,CAAE7D,OAAO,CAACuB,aAAa,CAAO,CAAC,CAC1B,CAAC,cACpBnC,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,cAC7BzE,IAAA,CAACM,IAAI,EAACoF,MAAM,MAACF,KAAK,CAAE,CAAExB,KAAK,CAAE,SAAU,CAAE,CAAAS,QAAA,CACtC3E,KAAK,CAACc,OAAO,CAACwB,YAAY,CAAC,CAAC+G,MAAM,CAAC,YAAY,CAAC,CAC7C,CAAC,CACU,CAAC,cACpBnJ,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAzE,QAAA,CAC3B7D,OAAO,CAAC6B,SAAS,CACD,CAAC,cACpBzC,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,CAC5B7D,OAAO,CAAC8B,SAAS,CACD,CAAC,cACpB1C,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,CAC5B7D,OAAO,CAAC+B,SAAS,CACD,CAAC,CACnB/B,OAAO,CAACgC,UAAU,eACjB1C,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEzE,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAzE,QAAA,CAC3B7D,OAAO,CAACgC,UAAU,CACF,CAAC,cACpB5C,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAzE,QAAA,CAC5B7D,OAAO,CAACiC,UAAU,CACF,CAAC,EACpB,CACH,cACD7C,IAAA,CAAC1B,YAAY,CAAC2K,IAAI,EAACC,KAAK,CAAC,cAAI,CAACE,IAAI,CAAExI,OAAO,CAACgC,UAAU,CAAG,CAAC,CAAG,CAAE,CAAA6B,QAAA,CAC5D7D,OAAO,CAAC2B,WAAW,EAAI,GAAG,CACV,CAAC,EACR,CAAC,cAGfrC,KAAA,CAACzB,GAAG,EAAC4K,MAAM,CAAE,EAAG,CAAC7D,KAAK,CAAE,CAAE+C,YAAY,CAAE,EAAG,CAAE,CAAA9D,QAAA,eAC3CzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,CAAE,CAAA3E,QAAA,cACXzE,IAAA,CAACrB,SAAS,EACRwG,KAAK,CAAC,oBAAK,CACXQ,KAAK,CAAE/E,OAAO,CAAC4B,WAAY,CAC3B8G,SAAS,CAAE,CAAE,CACbC,MAAM,CAAC,MAAG,CACVC,UAAU,CAAE,CAAExF,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNhE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,CAAE,CAAA3E,QAAA,cACXzE,IAAA,CAACrB,SAAS,EACRwG,KAAK,CAAC,0BAAM,CACZQ,KAAK,CAAEsC,UAAW,CAClBwB,MAAM,CAAC,QAAG,CACX,CAAC,CACC,CAAC,cACNzJ,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,CAAE,CAAA3E,QAAA,cACXzE,IAAA,CAACrB,SAAS,EACRwG,KAAK,CAAC,0BAAM,CACZQ,KAAK,CAAEwC,cAAe,CACtBsB,MAAM,CAAC,QAAG,CACVD,UAAU,CAAE,CAAExF,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNhE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,CAAE,CAAA3E,QAAA,cACXzE,IAAA,CAACrB,SAAS,EACRwG,KAAK,CAAC,0BAAM,CACZQ,KAAK,CAAE0C,aAAc,CACrBoB,MAAM,CAAC,QAAG,CACVD,UAAU,CAAE,CAAExF,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,EACH,CAAC,cAGN9D,KAAA,QAAKsF,KAAK,CAAE,CAAE+C,YAAY,CAAE,EAAG,CAAE,CAAA9D,QAAA,eAC/BzE,IAAA,CAACM,IAAI,EAACoF,MAAM,MAAAjB,QAAA,CAAC,kCAAO,CAAM,CAAC,cAC3BzE,IAAA,CAAChB,QAAQ,EACP0K,OAAO,CAAEC,IAAI,CAACC,KAAK,CAAEzB,cAAc,CAAGF,UAAU,CAAI,GAAG,CAAE,CACzD3F,MAAM,CAAE+F,aAAa,CAAG,CAAC,CAAG,WAAW,CAAG,SAAU,CACpDc,MAAM,CAAGO,OAAO,KAAA7D,MAAA,CAAQsC,cAAc,MAAAtC,MAAA,CAAIoC,UAAU,OAAApC,MAAA,CAAK6D,OAAO,MAAK,CACrElE,KAAK,CAAE,CAAEH,KAAK,CAAE,GAAG,CAAEwE,UAAU,CAAE,EAAG,CAAE,CACvC,CAAC,EACC,CAAC,cAEN7J,IAAA,CAACxB,OAAO,EAAAiG,QAAA,CAAC,iBAAK,CAAS,CAAC,cAExBzE,IAAA,CAAC5B,KAAK,EACJ8G,OAAO,CAAEA,OAAQ,CACjB4E,UAAU,CAAElJ,OAAO,CAACkC,KAAM,CAC1BiH,UAAU,CAAE,KAAM,CAClBC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBnC,IAAI,CAAC,OAAO,CACZiB,QAAQ,MACRmB,UAAU,CAAE,CACVpJ,eAAe,CACfqJ,oBAAoB,CAAGC,IAAI,EAAKrJ,kBAAkB,CAACqJ,IAAmB,CAAC,CACvEC,kBAAkB,CAAE,UAAU,CAC9BC,UAAU,CAAE,EACd,CAAE,CACH,CAAC,cAEFtK,IAAA,QAAKwF,KAAK,CAAE,CAAEuC,SAAS,CAAE,EAAE,CAAEF,SAAS,CAAE,OAAQ,CAAE,CAAApD,QAAA,cAChDvE,KAAA,CAACI,IAAI,EAACoF,MAAM,MAACF,KAAK,CAAE,CAAE+E,QAAQ,CAAE,EAAE,CAAEvG,KAAK,CAAE,SAAU,CAAE,CAAAS,QAAA,EAAC,0BAChD,CAAC7D,OAAO,CAAC4B,WAAW,CAACoD,OAAO,CAAC,CAAC,CAAC,EACjC,CAAC,CACJ,CAAC,EACF,CAAC,cAGP1F,KAAA,CAACnB,KAAK,EACJoG,KAAK,CAAC,6BAAS,CACfqF,IAAI,CAAExJ,kBAAmB,CACzBmF,IAAI,CAAEM,aAAc,CACpBgE,QAAQ,CAAEA,CAAA,GAAMxJ,qBAAqB,CAAC,KAAK,CAAE,CAC7CyJ,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CACftF,KAAK,CAAE,GAAI,CAAAZ,QAAA,eAEXvE,KAAA,QAAKsF,KAAK,CAAE,CAAE+C,YAAY,CAAE,EAAG,CAAE,CAAA9D,QAAA,eAC/BzE,IAAA,UAAOwF,KAAK,CAAE,CAAEgD,OAAO,CAAE,OAAO,CAAED,YAAY,CAAE,CAAE,CAAE,CAAA9D,QAAA,CAAC,gCAAK,CAAO,CAAC,cAClEvE,KAAA,CAACjB,MAAM,EACL0G,KAAK,CAAEzE,YAAa,CACpB0J,QAAQ,CAAEzJ,eAAgB,CAC1BqE,KAAK,CAAE,CAAEH,KAAK,CAAE,MAAO,CAAE,CAAAZ,QAAA,eAEzBzE,IAAA,CAACf,MAAM,CAAC4L,MAAM,EAAClF,KAAK,CAAC,OAAO,CAAAlB,QAAA,CAAC,eAAa,CAAe,CAAC,cAC1DzE,IAAA,CAACf,MAAM,CAAC4L,MAAM,EAAClF,KAAK,CAAC,KAAK,CAAAlB,QAAA,CAAC,YAAU,CAAe,CAAC,EAC/C,CAAC,EACN,CAAC,cAENvE,KAAA,QAAAuE,QAAA,eACEzE,IAAA,UAAOwF,KAAK,CAAE,CAAEgD,OAAO,CAAE,OAAO,CAAED,YAAY,CAAE,CAAE,CAAE,CAAA9D,QAAA,CAAC,gCAAK,CAAO,CAAC,cAClEzE,IAAA,CAACd,QAAQ,CAAC4L,KAAK,EACbnF,KAAK,CAAEvE,YAAa,CACpBwJ,QAAQ,CAAEvJ,eAAgB,CAC1BmE,KAAK,CAAE,CAAEH,KAAK,CAAE,MAAO,CAAE,CAAAZ,QAAA,cAEzBvE,KAAA,CAACzB,GAAG,EAAAgG,QAAA,eACFzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,cAAc,CAAAlB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC3C,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,cAAc,CAAAlB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC3C,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,eAAe,CAAAlB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC5C,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,MAAM,CAAAlB,QAAA,CAAC,cAAE,CAAU,CAAC,CACjC,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,UAAU,CAAAlB,QAAA,CAAC,cAAE,CAAU,CAAC,CACrC,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,WAAW,CAAAlB,QAAA,CAAC,cAAE,CAAU,CAAC,CACtC,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,YAAY,CAAAlB,QAAA,CAAC,cAAE,CAAU,CAAC,CACvC,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,UAAU,CAAAlB,QAAA,CAAC,oBAAG,CAAU,CAAC,CACtC,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,cAAc,CAAAlB,QAAA,CAAC,cAAE,CAAU,CAAC,CACzC,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,QAAQ,CAAAlB,QAAA,CAAC,cAAE,CAAU,CAAC,CACnC,CAAC,cACNzE,IAAA,CAACtB,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAA3E,QAAA,cACZzE,IAAA,CAACd,QAAQ,EAACyG,KAAK,CAAC,QAAQ,CAAAlB,QAAA,CAAC,cAAE,CAAU,CAAC,CACnC,CAAC,EACH,CAAC,CACQ,CAAC,EACd,CAAC,EACD,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}