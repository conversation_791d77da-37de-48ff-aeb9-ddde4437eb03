{"ast": null, "code": "import React,{Component}from'react';import{Result,Button}from'antd';import{ReloadOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";/**\n * 错误边界组件\n * 用于捕获子组件中的JavaScript错误，记录错误并显示降级UI\n */class ErrorBoundary extends Component{constructor(props){super(props);this.handleReload=()=>{// 重置错误状态\nthis.setState({hasError:false,error:undefined,errorInfo:undefined});// 刷新页面\nwindow.location.reload();};this.handleReset=()=>{// 仅重置错误状态，不刷新页面\nthis.setState({hasError:false,error:undefined,errorInfo:undefined});};this.state={hasError:false};}static getDerivedStateFromError(error){// 更新state使下一次渲染能够显示降级后的UI\nreturn{hasError:true,error};}componentDidCatch(error,errorInfo){// 记录错误信息\nconsole.error('ErrorBoundary caught an error:',error,errorInfo);// 可以在这里添加错误上报逻辑\n// reportError(error, errorInfo);\nthis.setState({error,errorInfo});}render(){if(this.state.hasError){// 如果有自定义的fallback UI，使用它\nif(this.props.fallback){return this.props.fallback;}// 默认的错误UI\nreturn/*#__PURE__*/_jsx(\"div\",{style:{padding:'50px',textAlign:'center'},children:/*#__PURE__*/_jsx(Result,{status:\"error\",title:\"\\u9875\\u9762\\u51FA\\u73B0\\u9519\\u8BEF\",subTitle:\"\\u62B1\\u6B49\\uFF0C\\u9875\\u9762\\u9047\\u5230\\u4E86\\u4E00\\u4E9B\\u95EE\\u9898\\u3002\\u60A8\\u53EF\\u4EE5\\u5C1D\\u8BD5\\u5237\\u65B0\\u9875\\u9762\\u6216\\u8054\\u7CFB\\u6280\\u672F\\u652F\\u6301\\u3002\",extra:[/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:this.handleReload,children:\"\\u5237\\u65B0\\u9875\\u9762\"},\"reload\"),/*#__PURE__*/_jsx(Button,{onClick:this.handleReset,children:\"\\u91CD\\u8BD5\"},\"reset\")],children:process.env.NODE_ENV==='development'&&this.state.error&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'left',marginTop:20},children:/*#__PURE__*/_jsxs(\"details\",{style:{whiteSpace:'pre-wrap'},children:[/*#__PURE__*/_jsx(\"summary\",{children:\"\\u9519\\u8BEF\\u8BE6\\u60C5\\uFF08\\u5F00\\u53D1\\u6A21\\u5F0F\\uFF09\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u9519\\u8BEF\\u4FE1\\u606F\\uFF1A\"}),this.state.error.message]}),/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u9519\\u8BEF\\u5806\\u6808\\uFF1A\"})}),/*#__PURE__*/_jsx(\"pre\",{children:this.state.error.stack}),this.state.errorInfo&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u7EC4\\u4EF6\\u5806\\u6808\\uFF1A\"})}),/*#__PURE__*/_jsx(\"pre\",{children:this.state.errorInfo.componentStack})]})]})})})});}return this.props.children;}}export default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "Result", "<PERSON><PERSON>", "ReloadOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "handleReload", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "undefined", "errorInfo", "window", "location", "reload", "handleReset", "state", "getDerivedStateFromError", "componentDidCatch", "console", "render", "fallback", "style", "padding", "textAlign", "children", "status", "title", "subTitle", "extra", "type", "icon", "onClick", "process", "env", "NODE_ENV", "marginTop", "whiteSpace", "message", "stack", "componentStack"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { Result, Button } from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\n/**\n * 错误边界组件\n * 用于捕获子组件中的JavaScript错误，记录错误并显示降级UI\n */\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    // 更新state使下一次渲染能够显示降级后的UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // 记录错误信息\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    // 可以在这里添加错误上报逻辑\n    // reportError(error, errorInfo);\n    \n    this.setState({\n      error,\n      errorInfo\n    });\n  }\n\n  handleReload = () => {\n    // 重置错误状态\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n    // 刷新页面\n    window.location.reload();\n  };\n\n  handleReset = () => {\n    // 仅重置错误状态，不刷新页面\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // 如果有自定义的fallback UI，使用它\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // 默认的错误UI\n      return (\n        <div style={{ padding: '50px', textAlign: 'center' }}>\n          <Result\n            status=\"error\"\n            title=\"页面出现错误\"\n            subTitle=\"抱歉，页面遇到了一些问题。您可以尝试刷新页面或联系技术支持。\"\n            extra={[\n              <Button type=\"primary\" icon={<ReloadOutlined />} onClick={this.handleReload} key=\"reload\">\n                刷新页面\n              </Button>,\n              <Button onClick={this.handleReset} key=\"reset\">\n                重试\n              </Button>\n            ]}\n          >\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <div style={{ textAlign: 'left', marginTop: 20 }}>\n                <details style={{ whiteSpace: 'pre-wrap' }}>\n                  <summary>错误详情（开发模式）</summary>\n                  <p><strong>错误信息：</strong>{this.state.error.message}</p>\n                  <p><strong>错误堆栈：</strong></p>\n                  <pre>{this.state.error.stack}</pre>\n                  {this.state.errorInfo && (\n                    <>\n                      <p><strong>组件堆栈：</strong></p>\n                      <pre>{this.state.errorInfo.componentStack}</pre>\n                    </>\n                  )}\n                </details>\n              </div>\n            )}\n          </Result>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAA8B,OAAO,CAC9D,OAASC,MAAM,CAAEC,MAAM,KAAQ,MAAM,CACrC,OAASC,cAAc,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAanD;AACA;AACA;AACA,GACA,KAAM,CAAAC,aAAa,QAAS,CAAAV,SAAwB,CAClDW,WAAWA,CAACC,KAAY,CAAE,CACxB,KAAK,CAACA,KAAK,CAAC,CAAC,KAsBfC,YAAY,CAAG,IAAM,CACnB;AACA,IAAI,CAACC,QAAQ,CAAC,CAAEC,QAAQ,CAAE,KAAK,CAAEC,KAAK,CAAEC,SAAS,CAAEC,SAAS,CAAED,SAAU,CAAC,CAAC,CAC1E;AACAE,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC1B,CAAC,MAEDC,WAAW,CAAG,IAAM,CAClB;AACA,IAAI,CAACR,QAAQ,CAAC,CAAEC,QAAQ,CAAE,KAAK,CAAEC,KAAK,CAAEC,SAAS,CAAEC,SAAS,CAAED,SAAU,CAAC,CAAC,CAC5E,CAAC,CA/BC,IAAI,CAACM,KAAK,CAAG,CAAER,QAAQ,CAAE,KAAM,CAAC,CAClC,CAEA,MAAO,CAAAS,wBAAwBA,CAACR,KAAY,CAAS,CACnD;AACA,MAAO,CAAED,QAAQ,CAAE,IAAI,CAAEC,KAAM,CAAC,CAClC,CAEAS,iBAAiBA,CAACT,KAAY,CAAEE,SAAoB,CAAE,CACpD;AACAQ,OAAO,CAACV,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAEE,SAAS,CAAC,CAEjE;AACA;AAEA,IAAI,CAACJ,QAAQ,CAAC,CACZE,KAAK,CACLE,SACF,CAAC,CAAC,CACJ,CAcAS,MAAMA,CAAA,CAAG,CACP,GAAI,IAAI,CAACJ,KAAK,CAACR,QAAQ,CAAE,CACvB;AACA,GAAI,IAAI,CAACH,KAAK,CAACgB,QAAQ,CAAE,CACvB,MAAO,KAAI,CAAChB,KAAK,CAACgB,QAAQ,CAC5B,CAEA;AACA,mBACEvB,IAAA,QAAKwB,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAC,QAAA,cACnD3B,IAAA,CAACJ,MAAM,EACLgC,MAAM,CAAC,OAAO,CACdC,KAAK,CAAC,sCAAQ,CACdC,QAAQ,CAAC,sLAAgC,CACzCC,KAAK,CAAE,cACL/B,IAAA,CAACH,MAAM,EAACmC,IAAI,CAAC,SAAS,CAACC,IAAI,cAAEjC,IAAA,CAACF,cAAc,GAAE,CAAE,CAACoC,OAAO,CAAE,IAAI,CAAC1B,YAAa,CAAAmB,QAAA,CAAc,0BAE1F,EAFiF,QAEzE,CAAC,cACT3B,IAAA,CAACH,MAAM,EAACqC,OAAO,CAAE,IAAI,CAACjB,WAAY,CAAAU,QAAA,CAAa,cAE/C,EAFuC,OAE/B,CAAC,CACT,CAAAA,QAAA,CAEDQ,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,EAAI,IAAI,CAACnB,KAAK,CAACP,KAAK,eACzDX,IAAA,QAAKwB,KAAK,CAAE,CAAEE,SAAS,CAAE,MAAM,CAAEY,SAAS,CAAE,EAAG,CAAE,CAAAX,QAAA,cAC/CzB,KAAA,YAASsB,KAAK,CAAE,CAAEe,UAAU,CAAE,UAAW,CAAE,CAAAZ,QAAA,eACzC3B,IAAA,YAAA2B,QAAA,CAAS,8DAAU,CAAS,CAAC,cAC7BzB,KAAA,MAAAyB,QAAA,eAAG3B,IAAA,WAAA2B,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAAC,IAAI,CAACT,KAAK,CAACP,KAAK,CAAC6B,OAAO,EAAI,CAAC,cACvDxC,IAAA,MAAA2B,QAAA,cAAG3B,IAAA,WAAA2B,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAAG,CAAC,cAC7B3B,IAAA,QAAA2B,QAAA,CAAM,IAAI,CAACT,KAAK,CAACP,KAAK,CAAC8B,KAAK,CAAM,CAAC,CAClC,IAAI,CAACvB,KAAK,CAACL,SAAS,eACnBX,KAAA,CAAAE,SAAA,EAAAuB,QAAA,eACE3B,IAAA,MAAA2B,QAAA,cAAG3B,IAAA,WAAA2B,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAAG,CAAC,cAC7B3B,IAAA,QAAA2B,QAAA,CAAM,IAAI,CAACT,KAAK,CAACL,SAAS,CAAC6B,cAAc,CAAM,CAAC,EAChD,CACH,EACM,CAAC,CACP,CACN,CACK,CAAC,CACN,CAAC,CAEV,CAEA,MAAO,KAAI,CAACnC,KAAK,CAACoB,QAAQ,CAC5B,CACF,CAEA,cAAe,CAAAtB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}