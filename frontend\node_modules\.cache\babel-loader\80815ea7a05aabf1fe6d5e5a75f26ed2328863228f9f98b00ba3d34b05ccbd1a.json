{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{Card,Form,Input,Button,Space,Row,Col,Typography,Steps,message,Divider,Select,Table,Modal,InputNumber,Checkbox,Tag}from'antd';import{SaveOutlined,ArrowLeftOutlined,PlusOutlined,DeleteOutlined,EditOutlined}from'@ant-design/icons';import{useAppDispatch}from'../../hooks/redux';import{createCoreBOM}from'../../store/slices/bomSlice';import{ROUTES,UNITS}from'../../constants';import{ConfirmDialog}from'../../components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{TextArea}=Input;const{Step}=Steps;const CoreBOMCreatePage=()=>{const navigate=useNavigate();const dispatch=useAppDispatch();const[form]=Form.useForm();const[itemForm]=Form.useForm();const[ruleForm]=Form.useForm();const[currentStep,setCurrentStep]=useState(0);const[loading,setLoading]=useState(false);const[bomItems,setBomItems]=useState([]);const[configRules,setConfigRules]=useState([]);const[itemModalVisible,setItemModalVisible]=useState(false);const[ruleModalVisible,setRuleModalVisible]=useState(false);const[editingItem,setEditingItem]=useState(null);const[editingRule,setEditingRule]=useState(null);const steps=[{title:'基本信息',description:'填写BOM基本信息'},{title:'BOM结构',description:'添加物料清单'},{title:'配置规则',description:'设置配置规则'},{title:'完成',description:'确认并保存'}];const handleNext=async()=>{if(currentStep===0){try{await form.validateFields();setCurrentStep(1);}catch(error){message.error('请完善基本信息');}}else if(currentStep===1){if(bomItems.length===0){message.error('请至少添加一个物料');return;}setCurrentStep(2);}else if(currentStep===2){setCurrentStep(3);}};const handlePrev=()=>{setCurrentStep(currentStep-1);};const handleSave=async()=>{try{setLoading(true);const basicInfo=await form.validateFields();const bomData=_objectSpread(_objectSpread({},basicInfo),{},{items:bomItems,configRules:configRules,status:'DRAFT'});await dispatch(createCoreBOM(bomData)).unwrap();message.success('BOM创建成功');navigate(ROUTES.CORE_BOM);}catch(error){message.error('创建失败');}finally{setLoading(false);}};const handleCancel=()=>{navigate(ROUTES.CORE_BOM);};// BOM项目管理\nconst handleAddItem=()=>{setEditingItem(null);itemForm.resetFields();setItemModalVisible(true);};const handleEditItem=item=>{setEditingItem(item);itemForm.setFieldsValue(item);setItemModalVisible(true);};const handleDeleteItem=id=>{const item=bomItems.find(item=>item.id===id);if(!item)return;ConfirmDialog.confirm({title:'确认删除',content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u4EE5\\u4E0B\\u7269\\u6599\\u5417\\uFF1F\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u7269\\u6599\\u7F16\\u7801\\uFF1A\"}),item.materialCode]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u7269\\u6599\\u540D\\u79F0\\uFF1A\"}),item.materialName]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6570\\u91CF\\uFF1A\"}),item.quantity,\" \",item.unit]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#ff4d4f',marginTop:12},children:\"\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\uFF01\"})]}),type:'warning',onConfirm:()=>{setBomItems(bomItems.filter(item=>item.id!==id));message.success('删除成功');}});};const handleItemModalOk=async()=>{try{const values=await itemForm.validateFields();const newItem={id:(editingItem===null||editingItem===void 0?void 0:editingItem.id)||Date.now().toString(),parentId:values.parentId,materialId:values.materialId,materialCode:values.materialCode,materialName:values.materialName,materialSpec:values.materialSpec,quantity:values.quantity,unit:values.unit,level:values.level||1,sequence:values.sequence||bomItems.length+1,isOptional:values.isOptional||false,isMandatory:values.isMandatory||true,isAlternative:values.isAlternative||false,alternativeGroup:values.alternativeGroup,unitPrice:values.unitPrice,totalPrice:(values.quantity||0)*(values.unitPrice||0),supplier:values.supplier,leadTime:values.leadTime,moq:values.moq,packageSize:values.packageSize,remarks:values.remarks};if(editingItem){setBomItems(bomItems.map(item=>item.id===editingItem.id?newItem:item));message.success('修改成功');}else{setBomItems([...bomItems,newItem]);message.success('添加成功');}setItemModalVisible(false);}catch(error){message.error('请完善物料信息');}};// 配置规则管理\nconst handleAddRule=()=>{setEditingRule(null);ruleForm.resetFields();setRuleModalVisible(true);};const handleEditRule=rule=>{setEditingRule(rule);ruleForm.setFieldsValue(rule);setRuleModalVisible(true);};const handleDeleteRule=id=>{const rule=configRules.find(rule=>rule.id===id);if(!rule)return;ConfirmDialog.confirm({title:'确认删除',content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u4EE5\\u4E0B\\u914D\\u7F6E\\u89C4\\u5219\\u5417\\uFF1F\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u89C4\\u5219\\u540D\\u79F0\\uFF1A\"}),rule.name]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u89C4\\u5219\\u7C7B\\u578B\\uFF1A\"}),rule.type]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#ff4d4f',marginTop:12},children:\"\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\uFF01\"})]}),type:'warning',onConfirm:()=>{setConfigRules(configRules.filter(rule=>rule.id!==id));message.success('删除成功');}});};const handleRuleModalOk=async()=>{try{const values=await ruleForm.validateFields();const newRule={id:(editingRule===null||editingRule===void 0?void 0:editingRule.id)||Date.now().toString(),name:values.name,condition:values.condition,action:values.action,priority:values.priority||1,isActive:values.isActive!==false,description:values.description};if(editingRule){setConfigRules(configRules.map(rule=>rule.id===editingRule.id?newRule:rule));message.success('修改成功');}else{setConfigRules([...configRules,newRule]);message.success('添加成功');}setRuleModalVisible(false);}catch(error){message.error('请完善规则信息');}};// BOM项目表格列定义\nconst itemColumns=[{title:'层级',dataIndex:'level',key:'level',width:60},{title:'物料编码',dataIndex:'materialCode',key:'materialCode',width:120},{title:'物料名称',dataIndex:'materialName',key:'materialName',ellipsis:true},{title:'规格',dataIndex:'materialSpec',key:'materialSpec',ellipsis:true},{title:'数量',dataIndex:'quantity',key:'quantity',width:80},{title:'单位',dataIndex:'unit',key:'unit',width:60},{title:'单价',dataIndex:'unitPrice',key:'unitPrice',width:80,render:value=>value?\"\\xA5\".concat(value.toFixed(2)):'-'},{title:'总价',dataIndex:'totalPrice',key:'totalPrice',width:80,render:value=>value?\"\\xA5\".concat(value.toFixed(2)):'-'},{title:'属性',key:'attributes',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:4,children:[record.isOptional&&/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:\"\\u53EF\\u9009\"}),record.isMandatory&&/*#__PURE__*/_jsx(Tag,{color:\"green\",children:\"\\u5FC5\\u9009\"}),record.isAlternative&&/*#__PURE__*/_jsx(Tag,{color:\"orange\",children:\"\\u4E92\\u65A5\"})]})},{title:'操作',key:'action',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"text\",size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEditItem(record)}),/*#__PURE__*/_jsx(Button,{type:\"text\",size:\"small\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>handleDeleteItem(record.id)})]})}];// 配置规则表格列定义\nconst ruleColumns=[{title:'规则名称',dataIndex:'name',key:'name'},{title:'条件',dataIndex:'condition',key:'condition',ellipsis:true},{title:'动作',dataIndex:'action',key:'action',ellipsis:true},{title:'优先级',dataIndex:'priority',key:'priority',width:80},{title:'状态',dataIndex:'isActive',key:'isActive',width:80,render:isActive=>/*#__PURE__*/_jsx(Tag,{color:isActive?'green':'red',children:isActive?'启用':'禁用'})},{title:'操作',key:'action',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"text\",size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEditRule(record)}),/*#__PURE__*/_jsx(Button,{type:\"text\",size:\"small\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>handleDeleteRule(record.id)})]})}];// 渲染步骤内容\nconst renderStepContent=()=>{switch(currentStep){case 0:return/*#__PURE__*/_jsx(Form,{form:form,layout:\"vertical\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"BOM\\u540D\\u79F0\",rules:[{required:true,message:'请输入BOM名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"code\",label:\"BOM\\u7F16\\u7801\",rules:[{required:true,message:'请输入BOM编码'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u7F16\\u7801\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"version\",label:\"\\u7248\\u672C\\u53F7\",rules:[{required:true,message:'请输入版本号'}],initialValue:\"V1.0\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7248\\u672C\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,children:/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u63CF\\u8FF0\",rules:[{required:true,message:'请输入描述'}],children:/*#__PURE__*/_jsx(TextArea,{rows:4,placeholder:\"\\u8BF7\\u8F93\\u5165BOM\\u63CF\\u8FF0\"})})})]})});case 1:return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:5,children:\"BOM\\u7ED3\\u6784\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAddItem,children:\"\\u6DFB\\u52A0\\u7269\\u6599\"})})]}),/*#__PURE__*/_jsx(Table,{columns:itemColumns,dataSource:bomItems,rowKey:\"id\",pagination:false,scroll:{x:1000},locale:{emptyText:'暂无物料，请点击\"添加物料\"按钮添加'}})]});case 2:return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:5,children:\"\\u914D\\u7F6E\\u89C4\\u5219\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAddRule,children:\"\\u6DFB\\u52A0\\u89C4\\u5219\"})})]}),/*#__PURE__*/_jsx(Table,{columns:ruleColumns,dataSource:configRules,rowKey:\"id\",pagination:false,locale:{emptyText:'暂无配置规则，可选择添加'}})]});case 3:const basicInfo=form.getFieldsValue();return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:5,children:\"\\u786E\\u8BA4\\u4FE1\\u606F\"}),/*#__PURE__*/_jsxs(Card,{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Title,{level:5,children:\"\\u57FA\\u672C\\u4FE1\\u606F\"}),/*#__PURE__*/_jsxs(Row,{gutter:[16,8],children:[/*#__PURE__*/_jsxs(Col,{span:8,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"BOM\\u540D\\u79F0\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:basicInfo.name})]}),/*#__PURE__*/_jsxs(Col,{span:8,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"BOM\\u7F16\\u7801\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:basicInfo.code})]}),/*#__PURE__*/_jsxs(Col,{span:8,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u7248\\u672C\\u53F7\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:basicInfo.version})]}),/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u63CF\\u8FF0\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:basicInfo.description})]})]})]}),/*#__PURE__*/_jsxs(Card,{style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Title,{level:5,children:[\"BOM\\u7ED3\\u6784\\uFF08\",bomItems.length,\"\\u4E2A\\u7269\\u6599\\uFF09\"]}),/*#__PURE__*/_jsx(Table,{columns:itemColumns.filter(col=>col.key!=='action'),dataSource:bomItems,rowKey:\"id\",pagination:false,size:\"small\",scroll:{x:800}})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Title,{level:5,children:[\"\\u914D\\u7F6E\\u89C4\\u5219\\uFF08\",configRules.length,\"\\u4E2A\\u89C4\\u5219\\uFF09\"]}),/*#__PURE__*/_jsx(Table,{columns:ruleColumns.filter(col=>col.key!=='action'),dataSource:configRules,rowKey:\"id\",pagination:false,size:\"small\"})]})]});default:return null;}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:24},children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ArrowLeftOutlined,{}),onClick:handleCancel,children:\"\\u8FD4\\u56DE\"}),/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u521B\\u5EFA\\u6838\\u5FC3BOM\"})]})})}),/*#__PURE__*/_jsx(Steps,{current:currentStep,style:{marginBottom:32},children:steps.map(item=>/*#__PURE__*/_jsx(Step,{title:item.title,description:item.description},item.title))}),/*#__PURE__*/_jsx(\"div\",{style:{minHeight:400},children:renderStepContent()}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",children:[/*#__PURE__*/_jsx(Col,{children:currentStep>0&&/*#__PURE__*/_jsx(Button,{onClick:handlePrev,children:\"\\u4E0A\\u4E00\\u6B65\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCancel,children:\"\\u53D6\\u6D88\"}),currentStep<steps.length-1?/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:handleNext,children:\"\\u4E0B\\u4E00\\u6B65\"}):/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(SaveOutlined,{}),loading:loading,onClick:handleSave,children:\"\\u4FDD\\u5B58\"})]})})]})]}),/*#__PURE__*/_jsx(Modal,{title:editingItem?'编辑物料':'添加物料',open:itemModalVisible,onOk:handleItemModalOk,onCancel:()=>setItemModalVisible(false),width:800,okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Form,{form:itemForm,layout:\"vertical\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"materialCode\",label:\"\\u7269\\u6599\\u7F16\\u7801\",rules:[{required:true,message:'请输入物料编码'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u7F16\\u7801\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"materialName\",label:\"\\u7269\\u6599\\u540D\\u79F0\",rules:[{required:true,message:'请输入物料名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,children:/*#__PURE__*/_jsx(Form.Item,{name:\"materialSpec\",label:\"\\u7269\\u6599\\u89C4\\u683C\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u89C4\\u683C\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"quantity\",label:\"\\u6570\\u91CF\",rules:[{required:true,message:'请输入数量'}],children:/*#__PURE__*/_jsx(InputNumber,{min:0,precision:2,style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"unit\",label:\"\\u5355\\u4F4D\",rules:[{required:true,message:'请选择单位'}],children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u5355\\u4F4D\",children:UNITS.map(unit=>/*#__PURE__*/_jsx(Select.Option,{value:unit,children:unit},unit))})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"level\",label:\"\\u5C42\\u7EA7\",initialValue:1,children:/*#__PURE__*/_jsx(InputNumber,{min:1,max:10,style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"unitPrice\",label:\"\\u5355\\u4EF7\",children:/*#__PURE__*/_jsx(InputNumber,{min:0,precision:2,style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"supplier\",label:\"\\u4F9B\\u5E94\\u5546\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u4F9B\\u5E94\\u5546\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"leadTime\",label:\"\\u4EA4\\u671F(\\u5929)\",children:/*#__PURE__*/_jsx(InputNumber,{min:0,style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Form.Item,{name:\"isOptional\",valuePropName:\"checked\",children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u53EF\\u9009\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"isMandatory\",valuePropName:\"checked\",initialValue:true,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u5FC5\\u9009\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"isAlternative\",valuePropName:\"checked\",children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u4E92\\u65A5\"})})]})}),/*#__PURE__*/_jsx(Col,{xs:24,children:/*#__PURE__*/_jsx(Form.Item,{name:\"remarks\",label:\"\\u5907\\u6CE8\",children:/*#__PURE__*/_jsx(TextArea,{rows:3,placeholder:\"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\"})})})]})})}),/*#__PURE__*/_jsx(Modal,{title:editingRule?'编辑配置规则':'添加配置规则',open:ruleModalVisible,onOk:handleRuleModalOk,onCancel:()=>setRuleModalVisible(false),width:600,okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsxs(Form,{form:ruleForm,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"\\u89C4\\u5219\\u540D\\u79F0\",rules:[{required:true,message:'请输入规则名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u89C4\\u5219\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"condition\",label:\"\\u6761\\u4EF6\\u8868\\u8FBE\\u5F0F\",rules:[{required:true,message:'请输入条件表达式'}],children:/*#__PURE__*/_jsx(TextArea,{rows:3,placeholder:\"\\u4F8B\\u5982\\uFF1Afrequency == '5G' && power > 100\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"action\",label:\"\\u6267\\u884C\\u52A8\\u4F5C\",rules:[{required:true,message:'请输入执行动作'}],children:/*#__PURE__*/_jsx(TextArea,{rows:3,placeholder:\"\\u4F8B\\u5982\\uFF1Ainclude('ANT-5G-001'); exclude('ANT-4G-001')\"})}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"priority\",label:\"\\u4F18\\u5148\\u7EA7\",initialValue:1,children:/*#__PURE__*/_jsx(InputNumber,{min:1,max:100,style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"isActive\",valuePropName:\"checked\",initialValue:true,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u542F\\u7528\\u89C4\\u5219\"})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u89C4\\u5219\\u63CF\\u8FF0\",children:/*#__PURE__*/_jsx(TextArea,{rows:2,placeholder:\"\\u8BF7\\u8F93\\u5165\\u89C4\\u5219\\u63CF\\u8FF0\"})})]})})]});};export default CoreBOMCreatePage;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Card", "Form", "Input", "<PERSON><PERSON>", "Space", "Row", "Col", "Typography", "Steps", "message", "Divider", "Select", "Table", "Modal", "InputNumber", "Checkbox", "Tag", "SaveOutlined", "ArrowLeftOutlined", "PlusOutlined", "DeleteOutlined", "EditOutlined", "useAppDispatch", "createCoreBOM", "ROUTES", "UNITS", "ConfirmDialog", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "TextArea", "Step", "CoreBOMCreatePage", "navigate", "dispatch", "form", "useForm", "itemForm", "ruleForm", "currentStep", "setCurrentStep", "loading", "setLoading", "bomItems", "setBomItems", "configRules", "setConfigRules", "itemModalVisible", "setItemModalVisible", "ruleModalVisible", "setRuleModalVisible", "editingItem", "setEditingItem", "editingRule", "setEditingRule", "steps", "title", "description", "handleNext", "validateFields", "error", "length", "handlePrev", "handleSave", "basicInfo", "bomData", "_objectSpread", "items", "status", "unwrap", "success", "CORE_BOM", "handleCancel", "handleAddItem", "resetFields", "handleEditItem", "item", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDeleteItem", "id", "find", "confirm", "content", "children", "materialCode", "materialName", "quantity", "unit", "style", "color", "marginTop", "type", "onConfirm", "filter", "handleItemModalOk", "values", "newItem", "Date", "now", "toString", "parentId", "materialId", "materialSpec", "level", "sequence", "isOptional", "isMandatory", "isAlternative", "alternativeGroup", "unitPrice", "totalPrice", "supplier", "leadTime", "moq", "packageSize", "remarks", "map", "handleAddRule", "handleEditRule", "rule", "handleDeleteRule", "name", "handleRuleModalOk", "newRule", "condition", "action", "priority", "isActive", "itemColumns", "dataIndex", "key", "width", "ellipsis", "render", "value", "concat", "toFixed", "_", "record", "size", "icon", "onClick", "danger", "ruleColumns", "renderStepContent", "layout", "gutter", "xs", "md", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "initialValue", "rows", "justify", "align", "marginBottom", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "x", "locale", "emptyText", "getFieldsValue", "span", "strong", "code", "version", "col", "margin", "current", "minHeight", "open", "onOk", "onCancel", "okText", "cancelText", "min", "precision", "Option", "max", "valuePropName"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/CoreBOMCreatePage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  Space,\n  Row,\n  Col,\n  Typography,\n  Steps,\n  message,\n  Divider,\n  Select,\n  Table,\n  Modal,\n  InputNumber,\n  Checkbox,\n  Tag,\n  Tooltip,\n} from 'antd';\nimport {\n  SaveOutlined,\n  ArrowLeftOutlined,\n  PlusOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  CopyOutlined,\n  UpOutlined,\n  DownOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch } from '../../hooks/redux';\nimport { createCoreBOM } from '../../store/slices/bomSlice';\nimport { BOMItem, ConfigRule } from '../../types';\nimport { ROUTES, MATERIAL_CATEGORIES, UNITS } from '../../constants';\nimport { ConfirmDialog } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Step } = Steps;\n\ninterface BOMFormData {\n  name: string;\n  code: string;\n  version: string;\n  description: string;\n}\n\nconst CoreBOMCreatePage: React.FC = () => {\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [form] = Form.useForm<BOMFormData>();\n  const [itemForm] = Form.useForm();\n  const [ruleForm] = Form.useForm();\n  \n  const [currentStep, setCurrentStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [bomItems, setBomItems] = useState<BOMItem[]>([]);\n  const [configRules, setConfigRules] = useState<ConfigRule[]>([]);\n  const [itemModalVisible, setItemModalVisible] = useState(false);\n  const [ruleModalVisible, setRuleModalVisible] = useState(false);\n  const [editingItem, setEditingItem] = useState<BOMItem | null>(null);\n  const [editingRule, setEditingRule] = useState<ConfigRule | null>(null);\n\n  const steps = [\n    {\n      title: '基本信息',\n      description: '填写BOM基本信息',\n    },\n    {\n      title: 'BOM结构',\n      description: '添加物料清单',\n    },\n    {\n      title: '配置规则',\n      description: '设置配置规则',\n    },\n    {\n      title: '完成',\n      description: '确认并保存',\n    },\n  ];\n\n  const handleNext = async () => {\n    if (currentStep === 0) {\n      try {\n        await form.validateFields();\n        setCurrentStep(1);\n      } catch (error) {\n        message.error('请完善基本信息');\n      }\n    } else if (currentStep === 1) {\n      if (bomItems.length === 0) {\n        message.error('请至少添加一个物料');\n        return;\n      }\n      setCurrentStep(2);\n    } else if (currentStep === 2) {\n      setCurrentStep(3);\n    }\n  };\n\n  const handlePrev = () => {\n    setCurrentStep(currentStep - 1);\n  };\n\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      const basicInfo = await form.validateFields();\n      \n      const bomData = {\n        ...basicInfo,\n        items: bomItems,\n        configRules: configRules,\n        status: 'DRAFT' as const,\n      };\n\n      await dispatch(createCoreBOM(bomData)).unwrap();\n      message.success('BOM创建成功');\n      navigate(ROUTES.CORE_BOM);\n    } catch (error) {\n      message.error('创建失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate(ROUTES.CORE_BOM);\n  };\n\n  // BOM项目管理\n  const handleAddItem = () => {\n    setEditingItem(null);\n    itemForm.resetFields();\n    setItemModalVisible(true);\n  };\n\n  const handleEditItem = (item: BOMItem) => {\n    setEditingItem(item);\n    itemForm.setFieldsValue(item);\n    setItemModalVisible(true);\n  };\n\n  const handleDeleteItem = (id: string) => {\n    const item = bomItems.find(item => item.id === id);\n    if (!item) return;\n    \n    ConfirmDialog.confirm({\n      title: '确认删除',\n      content: (\n        <div>\n          <p>确定要删除以下物料吗？</p>\n          <p><strong>物料编码：</strong>{item.materialCode}</p>\n          <p><strong>物料名称：</strong>{item.materialName}</p>\n          <p><strong>数量：</strong>{item.quantity} {item.unit}</p>\n          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复！</p>\n        </div>\n      ),\n      type: 'warning',\n      onConfirm: () => {\n        setBomItems(bomItems.filter(item => item.id !== id));\n        message.success('删除成功');\n      }\n    });\n  };\n\n  const handleItemModalOk = async () => {\n    try {\n      const values = await itemForm.validateFields();\n      const newItem: BOMItem = {\n        id: editingItem?.id || Date.now().toString(),\n        parentId: values.parentId,\n        materialId: values.materialId,\n        materialCode: values.materialCode,\n        materialName: values.materialName,\n        materialSpec: values.materialSpec,\n        quantity: values.quantity,\n        unit: values.unit,\n        level: values.level || 1,\n        sequence: values.sequence || bomItems.length + 1,\n        isOptional: values.isOptional || false,\n        isMandatory: values.isMandatory || true,\n        isAlternative: values.isAlternative || false,\n        alternativeGroup: values.alternativeGroup,\n        unitPrice: values.unitPrice,\n        totalPrice: (values.quantity || 0) * (values.unitPrice || 0),\n        supplier: values.supplier,\n        leadTime: values.leadTime,\n        moq: values.moq,\n        packageSize: values.packageSize,\n        remarks: values.remarks,\n      };\n\n      if (editingItem) {\n        setBomItems(bomItems.map(item => item.id === editingItem.id ? newItem : item));\n        message.success('修改成功');\n      } else {\n        setBomItems([...bomItems, newItem]);\n        message.success('添加成功');\n      }\n      \n      setItemModalVisible(false);\n    } catch (error) {\n      message.error('请完善物料信息');\n    }\n  };\n\n  // 配置规则管理\n  const handleAddRule = () => {\n    setEditingRule(null);\n    ruleForm.resetFields();\n    setRuleModalVisible(true);\n  };\n\n  const handleEditRule = (rule: ConfigRule) => {\n    setEditingRule(rule);\n    ruleForm.setFieldsValue(rule);\n    setRuleModalVisible(true);\n  };\n\n  const handleDeleteRule = (id: string) => {\n    const rule = configRules.find(rule => rule.id === id);\n    if (!rule) return;\n    \n    ConfirmDialog.confirm({\n      title: '确认删除',\n      content: (\n        <div>\n          <p>确定要删除以下配置规则吗？</p>\n          <p><strong>规则名称：</strong>{rule.name}</p>\n          <p><strong>规则类型：</strong>{rule.type}</p>\n          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复！</p>\n        </div>\n      ),\n      type: 'warning',\n      onConfirm: () => {\n        setConfigRules(configRules.filter(rule => rule.id !== id));\n        message.success('删除成功');\n      }\n    });\n  };\n\n  const handleRuleModalOk = async () => {\n    try {\n      const values = await ruleForm.validateFields();\n      const newRule: ConfigRule = {\n        id: editingRule?.id || Date.now().toString(),\n        name: values.name,\n        condition: values.condition,\n        action: values.action,\n        priority: values.priority || 1,\n        isActive: values.isActive !== false,\n        description: values.description,\n      };\n\n      if (editingRule) {\n        setConfigRules(configRules.map(rule => rule.id === editingRule.id ? newRule : rule));\n        message.success('修改成功');\n      } else {\n        setConfigRules([...configRules, newRule]);\n        message.success('添加成功');\n      }\n\n      setRuleModalVisible(false);\n    } catch (error) {\n      message.error('请完善规则信息');\n    }\n  };\n\n  // BOM项目表格列定义\n  const itemColumns = [\n    {\n      title: '层级',\n      dataIndex: 'level',\n      key: 'level',\n      width: 60,\n    },\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      ellipsis: true,\n    },\n    {\n      title: '规格',\n      dataIndex: 'materialSpec',\n      key: 'materialSpec',\n      ellipsis: true,\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n    },\n    {\n      title: '单位',\n      dataIndex: 'unit',\n      key: 'unit',\n      width: 60,\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      width: 80,\n      render: (value: number) => value ? `¥${value.toFixed(2)}` : '-',\n    },\n    {\n      title: '总价',\n      dataIndex: 'totalPrice',\n      key: 'totalPrice',\n      width: 80,\n      render: (value: number) => value ? `¥${value.toFixed(2)}` : '-',\n    },\n    {\n      title: '属性',\n      key: 'attributes',\n      width: 120,\n      render: (_: any, record: BOMItem) => (\n        <Space size={4}>\n          {record.isOptional && <Tag color=\"blue\">可选</Tag>}\n          {record.isMandatory && <Tag color=\"green\">必选</Tag>}\n          {record.isAlternative && <Tag color=\"orange\">互斥</Tag>}\n        </Space>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_: any, record: BOMItem) => (\n        <Space size=\"small\">\n          <Button\n            type=\"text\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditItem(record)}\n          />\n          <Button\n            type=\"text\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDeleteItem(record.id)}\n          />\n        </Space>\n      ),\n    },\n  ];\n\n  // 配置规则表格列定义\n  const ruleColumns = [\n    {\n      title: '规则名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '条件',\n      dataIndex: 'condition',\n      key: 'condition',\n      ellipsis: true,\n    },\n    {\n      title: '动作',\n      dataIndex: 'action',\n      key: 'action',\n      ellipsis: true,\n    },\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      width: 80,\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'isActive',\n      width: 80,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_: any, record: ConfigRule) => (\n        <Space size=\"small\">\n          <Button\n            type=\"text\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditRule(record)}\n          />\n          <Button\n            type=\"text\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDeleteRule(record.id)}\n          />\n        </Space>\n      ),\n    },\n  ];\n\n  // 渲染步骤内容\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 0:\n        return (\n          <Form form={form} layout=\"vertical\">\n            <Row gutter={[16, 16]}>\n              <Col xs={24} md={12}>\n                <Form.Item\n                  name=\"name\"\n                  label=\"BOM名称\"\n                  rules={[{ required: true, message: '请输入BOM名称' }]}\n                >\n                  <Input placeholder=\"请输入BOM名称\" />\n                </Form.Item>\n              </Col>\n              <Col xs={24} md={12}>\n                <Form.Item\n                  name=\"code\"\n                  label=\"BOM编码\"\n                  rules={[{ required: true, message: '请输入BOM编码' }]}\n                >\n                  <Input placeholder=\"请输入BOM编码\" />\n                </Form.Item>\n              </Col>\n              <Col xs={24} md={12}>\n                <Form.Item\n                  name=\"version\"\n                  label=\"版本号\"\n                  rules={[{ required: true, message: '请输入版本号' }]}\n                  initialValue=\"V1.0\"\n                >\n                  <Input placeholder=\"请输入版本号\" />\n                </Form.Item>\n              </Col>\n              <Col xs={24}>\n                <Form.Item\n                  name=\"description\"\n                  label=\"描述\"\n                  rules={[{ required: true, message: '请输入描述' }]}\n                >\n                  <TextArea rows={4} placeholder=\"请输入BOM描述\" />\n                </Form.Item>\n              </Col>\n            </Row>\n          </Form>\n        );\n\n      case 1:\n        return (\n          <div>\n            <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n              <Col>\n                <Title level={5}>BOM结构</Title>\n              </Col>\n              <Col>\n                <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddItem}>\n                  添加物料\n                </Button>\n              </Col>\n            </Row>\n            <Table\n              columns={itemColumns}\n              dataSource={bomItems}\n              rowKey=\"id\"\n              pagination={false}\n              scroll={{ x: 1000 }}\n              locale={{ emptyText: '暂无物料，请点击\"添加物料\"按钮添加' }}\n            />\n          </div>\n        );\n\n      case 2:\n        return (\n          <div>\n            <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n              <Col>\n                <Title level={5}>配置规则</Title>\n              </Col>\n              <Col>\n                <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddRule}>\n                  添加规则\n                </Button>\n              </Col>\n            </Row>\n            <Table\n              columns={ruleColumns}\n              dataSource={configRules}\n              rowKey=\"id\"\n              pagination={false}\n              locale={{ emptyText: '暂无配置规则，可选择添加' }}\n            />\n          </div>\n        );\n\n      case 3:\n        const basicInfo = form.getFieldsValue();\n        return (\n          <div>\n            <Title level={5}>确认信息</Title>\n            <Card style={{ marginBottom: 16 }}>\n              <Title level={5}>基本信息</Title>\n              <Row gutter={[16, 8]}>\n                <Col span={8}>\n                  <Text strong>BOM名称：</Text>\n                  <Text>{basicInfo.name}</Text>\n                </Col>\n                <Col span={8}>\n                  <Text strong>BOM编码：</Text>\n                  <Text>{basicInfo.code}</Text>\n                </Col>\n                <Col span={8}>\n                  <Text strong>版本号：</Text>\n                  <Text>{basicInfo.version}</Text>\n                </Col>\n                <Col span={24}>\n                  <Text strong>描述：</Text>\n                  <Text>{basicInfo.description}</Text>\n                </Col>\n              </Row>\n            </Card>\n\n            <Card style={{ marginBottom: 16 }}>\n              <Title level={5}>BOM结构（{bomItems.length}个物料）</Title>\n              <Table\n                columns={itemColumns.filter(col => col.key !== 'action')}\n                dataSource={bomItems}\n                rowKey=\"id\"\n                pagination={false}\n                size=\"small\"\n                scroll={{ x: 800 }}\n              />\n            </Card>\n\n            <Card>\n              <Title level={5}>配置规则（{configRules.length}个规则）</Title>\n              <Table\n                columns={ruleColumns.filter(col => col.key !== 'action')}\n                dataSource={configRules}\n                rowKey=\"id\"\n                pagination={false}\n                size=\"small\"\n              />\n            </Card>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n          <Col>\n            <Space>\n              <Button icon={<ArrowLeftOutlined />} onClick={handleCancel}>\n                返回\n              </Button>\n              <Title level={4} style={{ margin: 0 }}>\n                创建核心BOM\n              </Title>\n            </Space>\n          </Col>\n        </Row>\n\n        <Steps current={currentStep} style={{ marginBottom: 32 }}>\n          {steps.map(item => (\n            <Step key={item.title} title={item.title} description={item.description} />\n          ))}\n        </Steps>\n\n        <div style={{ minHeight: 400 }}>\n          {renderStepContent()}\n        </div>\n\n        <Divider />\n\n        <Row justify=\"space-between\">\n          <Col>\n            {currentStep > 0 && (\n              <Button onClick={handlePrev}>\n                上一步\n              </Button>\n            )}\n          </Col>\n          <Col>\n            <Space>\n              <Button onClick={handleCancel}>\n                取消\n              </Button>\n              {currentStep < steps.length - 1 ? (\n                <Button type=\"primary\" onClick={handleNext}>\n                  下一步\n                </Button>\n              ) : (\n                <Button\n                  type=\"primary\"\n                  icon={<SaveOutlined />}\n                  loading={loading}\n                  onClick={handleSave}\n                >\n                  保存\n                </Button>\n              )}\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 添加/编辑物料模态框 */}\n      <Modal\n        title={editingItem ? '编辑物料' : '添加物料'}\n        open={itemModalVisible}\n        onOk={handleItemModalOk}\n        onCancel={() => setItemModalVisible(false)}\n        width={800}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form form={itemForm} layout=\"vertical\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"materialCode\"\n                label=\"物料编码\"\n                rules={[{ required: true, message: '请输入物料编码' }]}\n              >\n                <Input placeholder=\"请输入物料编码\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"materialName\"\n                label=\"物料名称\"\n                rules={[{ required: true, message: '请输入物料名称' }]}\n              >\n                <Input placeholder=\"请输入物料名称\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24}>\n              <Form.Item\n                name=\"materialSpec\"\n                label=\"物料规格\"\n              >\n                <Input placeholder=\"请输入物料规格\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"quantity\"\n                label=\"数量\"\n                rules={[{ required: true, message: '请输入数量' }]}\n              >\n                <InputNumber min={0} precision={2} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"unit\"\n                label=\"单位\"\n                rules={[{ required: true, message: '请选择单位' }]}\n              >\n                <Select placeholder=\"请选择单位\">\n                  {UNITS.map(unit => (\n                    <Select.Option key={unit} value={unit}>\n                      {unit}\n                    </Select.Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"level\"\n                label=\"层级\"\n                initialValue={1}\n              >\n                <InputNumber min={1} max={10} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"unitPrice\"\n                label=\"单价\"\n              >\n                <InputNumber min={0} precision={2} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"supplier\"\n                label=\"供应商\"\n              >\n                <Input placeholder=\"请输入供应商\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"leadTime\"\n                label=\"交期(天)\"\n              >\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24}>\n              <Space>\n                <Form.Item name=\"isOptional\" valuePropName=\"checked\">\n                  <Checkbox>可选</Checkbox>\n                </Form.Item>\n                <Form.Item name=\"isMandatory\" valuePropName=\"checked\" initialValue={true}>\n                  <Checkbox>必选</Checkbox>\n                </Form.Item>\n                <Form.Item name=\"isAlternative\" valuePropName=\"checked\">\n                  <Checkbox>互斥</Checkbox>\n                </Form.Item>\n              </Space>\n            </Col>\n            <Col xs={24}>\n              <Form.Item\n                name=\"remarks\"\n                label=\"备注\"\n              >\n                <TextArea rows={3} placeholder=\"请输入备注\" />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n\n      {/* 添加/编辑配置规则模态框 */}\n      <Modal\n        title={editingRule ? '编辑配置规则' : '添加配置规则'}\n        open={ruleModalVisible}\n        onOk={handleRuleModalOk}\n        onCancel={() => setRuleModalVisible(false)}\n        width={600}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form form={ruleForm} layout=\"vertical\">\n          <Form.Item\n            name=\"name\"\n            label=\"规则名称\"\n            rules={[{ required: true, message: '请输入规则名称' }]}\n          >\n            <Input placeholder=\"请输入规则名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"condition\"\n            label=\"条件表达式\"\n            rules={[{ required: true, message: '请输入条件表达式' }]}\n          >\n            <TextArea rows={3} placeholder=\"例如：frequency == '5G' && power > 100\" />\n          </Form.Item>\n          <Form.Item\n            name=\"action\"\n            label=\"执行动作\"\n            rules={[{ required: true, message: '请输入执行动作' }]}\n          >\n            <TextArea rows={3} placeholder=\"例如：include('ANT-5G-001'); exclude('ANT-4G-001')\" />\n          </Form.Item>\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"priority\"\n                label=\"优先级\"\n                initialValue={1}\n              >\n                <InputNumber min={1} max={100} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item name=\"isActive\" valuePropName=\"checked\" initialValue={true}>\n                <Checkbox>启用规则</Checkbox>\n              </Form.Item>\n            </Col>\n          </Row>\n          <Form.Item\n            name=\"description\"\n            label=\"规则描述\"\n          >\n            <TextArea rows={2} placeholder=\"请输入规则描述\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CoreBOMCreatePage;\n"], "mappings": "wHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,IAAI,CACJC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,GAAG,CACHC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,WAAW,CACXC,QAAQ,CACRC,GAAG,KAEE,MAAM,CACb,OACEC,YAAY,CACZC,iBAAiB,CACjBC,YAAY,CACZC,cAAc,CACdC,YAAY,KAIP,mBAAmB,CAE1B,OAASC,cAAc,KAAQ,mBAAmB,CAClD,OAASC,aAAa,KAAQ,6BAA6B,CAE3D,OAASC,MAAM,CAAuBC,KAAK,KAAQ,iBAAiB,CACpE,OAASC,aAAa,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGjD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGzB,UAAU,CAClC,KAAM,CAAE0B,QAAS,CAAC,CAAG/B,KAAK,CAC1B,KAAM,CAAEgC,IAAK,CAAC,CAAG1B,KAAK,CAStB,KAAM,CAAA2B,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAAAC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsC,QAAQ,CAAGf,cAAc,CAAC,CAAC,CACjC,KAAM,CAACgB,IAAI,CAAC,CAAGrC,IAAI,CAACsC,OAAO,CAAc,CAAC,CAC1C,KAAM,CAACC,QAAQ,CAAC,CAAGvC,IAAI,CAACsC,OAAO,CAAC,CAAC,CACjC,KAAM,CAACE,QAAQ,CAAC,CAAGxC,IAAI,CAACsC,OAAO,CAAC,CAAC,CAEjC,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAG7C,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgD,QAAQ,CAAEC,WAAW,CAAC,CAAGjD,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACkD,WAAW,CAAEC,cAAc,CAAC,CAAGnD,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAACoD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACsD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACwD,WAAW,CAAEC,cAAc,CAAC,CAAGzD,QAAQ,CAAiB,IAAI,CAAC,CACpE,KAAM,CAAC0D,WAAW,CAAEC,cAAc,CAAC,CAAG3D,QAAQ,CAAoB,IAAI,CAAC,CAEvE,KAAM,CAAA4D,KAAK,CAAG,CACZ,CACEC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,WACf,CAAC,CACD,CACED,KAAK,CAAE,OAAO,CACdC,WAAW,CAAE,QACf,CAAC,CACD,CACED,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,QACf,CAAC,CACD,CACED,KAAK,CAAE,IAAI,CACXC,WAAW,CAAE,OACf,CAAC,CACF,CAED,KAAM,CAAAC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAInB,WAAW,GAAK,CAAC,CAAE,CACrB,GAAI,CACF,KAAM,CAAAJ,IAAI,CAACwB,cAAc,CAAC,CAAC,CAC3BnB,cAAc,CAAC,CAAC,CAAC,CACnB,CAAE,MAAOoB,KAAK,CAAE,CACdtD,OAAO,CAACsD,KAAK,CAAC,SAAS,CAAC,CAC1B,CACF,CAAC,IAAM,IAAIrB,WAAW,GAAK,CAAC,CAAE,CAC5B,GAAII,QAAQ,CAACkB,MAAM,GAAK,CAAC,CAAE,CACzBvD,OAAO,CAACsD,KAAK,CAAC,WAAW,CAAC,CAC1B,OACF,CACApB,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,IAAM,IAAID,WAAW,GAAK,CAAC,CAAE,CAC5BC,cAAc,CAAC,CAAC,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsB,UAAU,CAAGA,CAAA,GAAM,CACvBtB,cAAc,CAACD,WAAW,CAAG,CAAC,CAAC,CACjC,CAAC,CAED,KAAM,CAAAwB,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACFrB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAsB,SAAS,CAAG,KAAM,CAAA7B,IAAI,CAACwB,cAAc,CAAC,CAAC,CAE7C,KAAM,CAAAM,OAAO,CAAAC,aAAA,CAAAA,aAAA,IACRF,SAAS,MACZG,KAAK,CAAExB,QAAQ,CACfE,WAAW,CAAEA,WAAW,CACxBuB,MAAM,CAAE,OAAgB,EACzB,CAED,KAAM,CAAAlC,QAAQ,CAACd,aAAa,CAAC6C,OAAO,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC,CAC/C/D,OAAO,CAACgE,OAAO,CAAC,SAAS,CAAC,CAC1BrC,QAAQ,CAACZ,MAAM,CAACkD,QAAQ,CAAC,CAC3B,CAAE,MAAOX,KAAK,CAAE,CACdtD,OAAO,CAACsD,KAAK,CAAC,MAAM,CAAC,CACvB,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA8B,YAAY,CAAGA,CAAA,GAAM,CACzBvC,QAAQ,CAACZ,MAAM,CAACkD,QAAQ,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAE,aAAa,CAAGA,CAAA,GAAM,CAC1BrB,cAAc,CAAC,IAAI,CAAC,CACpBf,QAAQ,CAACqC,WAAW,CAAC,CAAC,CACtB1B,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAA2B,cAAc,CAAIC,IAAa,EAAK,CACxCxB,cAAc,CAACwB,IAAI,CAAC,CACpBvC,QAAQ,CAACwC,cAAc,CAACD,IAAI,CAAC,CAC7B5B,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAA8B,gBAAgB,CAAIC,EAAU,EAAK,CACvC,KAAM,CAAAH,IAAI,CAAGjC,QAAQ,CAACqC,IAAI,CAACJ,IAAI,EAAIA,IAAI,CAACG,EAAE,GAAKA,EAAE,CAAC,CAClD,GAAI,CAACH,IAAI,CAAE,OAEXrD,aAAa,CAAC0D,OAAO,CAAC,CACpBzB,KAAK,CAAE,MAAM,CACb0B,OAAO,cACLvD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,MAAA0D,QAAA,CAAG,oEAAW,CAAG,CAAC,cAClBxD,KAAA,MAAAwD,QAAA,eAAG1D,IAAA,WAAA0D,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAACP,IAAI,CAACQ,YAAY,EAAI,CAAC,cAChDzD,KAAA,MAAAwD,QAAA,eAAG1D,IAAA,WAAA0D,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAACP,IAAI,CAACS,YAAY,EAAI,CAAC,cAChD1D,KAAA,MAAAwD,QAAA,eAAG1D,IAAA,WAAA0D,QAAA,CAAQ,oBAAG,CAAQ,CAAC,CAACP,IAAI,CAACU,QAAQ,CAAC,GAAC,CAACV,IAAI,CAACW,IAAI,EAAI,CAAC,cACtD9D,IAAA,MAAG+D,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,EAAG,CAAE,CAAAP,QAAA,CAAC,kDAAQ,CAAG,CAAC,EACxD,CACN,CACDQ,IAAI,CAAE,SAAS,CACfC,SAAS,CAAEA,CAAA,GAAM,CACfhD,WAAW,CAACD,QAAQ,CAACkD,MAAM,CAACjB,IAAI,EAAIA,IAAI,CAACG,EAAE,GAAKA,EAAE,CAAC,CAAC,CACpDzE,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC,CACzB,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAwB,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA1D,QAAQ,CAACsB,cAAc,CAAC,CAAC,CAC9C,KAAM,CAAAqC,OAAgB,CAAG,CACvBjB,EAAE,CAAE,CAAA5B,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE4B,EAAE,GAAIkB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAC5CC,QAAQ,CAAEL,MAAM,CAACK,QAAQ,CACzBC,UAAU,CAAEN,MAAM,CAACM,UAAU,CAC7BjB,YAAY,CAAEW,MAAM,CAACX,YAAY,CACjCC,YAAY,CAAEU,MAAM,CAACV,YAAY,CACjCiB,YAAY,CAAEP,MAAM,CAACO,YAAY,CACjChB,QAAQ,CAAES,MAAM,CAACT,QAAQ,CACzBC,IAAI,CAAEQ,MAAM,CAACR,IAAI,CACjBgB,KAAK,CAAER,MAAM,CAACQ,KAAK,EAAI,CAAC,CACxBC,QAAQ,CAAET,MAAM,CAACS,QAAQ,EAAI7D,QAAQ,CAACkB,MAAM,CAAG,CAAC,CAChD4C,UAAU,CAAEV,MAAM,CAACU,UAAU,EAAI,KAAK,CACtCC,WAAW,CAAEX,MAAM,CAACW,WAAW,EAAI,IAAI,CACvCC,aAAa,CAAEZ,MAAM,CAACY,aAAa,EAAI,KAAK,CAC5CC,gBAAgB,CAAEb,MAAM,CAACa,gBAAgB,CACzCC,SAAS,CAAEd,MAAM,CAACc,SAAS,CAC3BC,UAAU,CAAE,CAACf,MAAM,CAACT,QAAQ,EAAI,CAAC,GAAKS,MAAM,CAACc,SAAS,EAAI,CAAC,CAAC,CAC5DE,QAAQ,CAAEhB,MAAM,CAACgB,QAAQ,CACzBC,QAAQ,CAAEjB,MAAM,CAACiB,QAAQ,CACzBC,GAAG,CAAElB,MAAM,CAACkB,GAAG,CACfC,WAAW,CAAEnB,MAAM,CAACmB,WAAW,CAC/BC,OAAO,CAAEpB,MAAM,CAACoB,OAClB,CAAC,CAED,GAAIhE,WAAW,CAAE,CACfP,WAAW,CAACD,QAAQ,CAACyE,GAAG,CAACxC,IAAI,EAAIA,IAAI,CAACG,EAAE,GAAK5B,WAAW,CAAC4B,EAAE,CAAGiB,OAAO,CAAGpB,IAAI,CAAC,CAAC,CAC9EtE,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC,CACzB,CAAC,IAAM,CACL1B,WAAW,CAAC,CAAC,GAAGD,QAAQ,CAAEqD,OAAO,CAAC,CAAC,CACnC1F,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC,CACzB,CAEAtB,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAE,MAAOY,KAAK,CAAE,CACdtD,OAAO,CAACsD,KAAK,CAAC,SAAS,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAAyD,aAAa,CAAGA,CAAA,GAAM,CAC1B/D,cAAc,CAAC,IAAI,CAAC,CACpBhB,QAAQ,CAACoC,WAAW,CAAC,CAAC,CACtBxB,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAoE,cAAc,CAAIC,IAAgB,EAAK,CAC3CjE,cAAc,CAACiE,IAAI,CAAC,CACpBjF,QAAQ,CAACuC,cAAc,CAAC0C,IAAI,CAAC,CAC7BrE,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAsE,gBAAgB,CAAIzC,EAAU,EAAK,CACvC,KAAM,CAAAwC,IAAI,CAAG1E,WAAW,CAACmC,IAAI,CAACuC,IAAI,EAAIA,IAAI,CAACxC,EAAE,GAAKA,EAAE,CAAC,CACrD,GAAI,CAACwC,IAAI,CAAE,OAEXhG,aAAa,CAAC0D,OAAO,CAAC,CACpBzB,KAAK,CAAE,MAAM,CACb0B,OAAO,cACLvD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,MAAA0D,QAAA,CAAG,gFAAa,CAAG,CAAC,cACpBxD,KAAA,MAAAwD,QAAA,eAAG1D,IAAA,WAAA0D,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAACoC,IAAI,CAACE,IAAI,EAAI,CAAC,cACxC9F,KAAA,MAAAwD,QAAA,eAAG1D,IAAA,WAAA0D,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAACoC,IAAI,CAAC5B,IAAI,EAAI,CAAC,cACxClE,IAAA,MAAG+D,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,EAAG,CAAE,CAAAP,QAAA,CAAC,kDAAQ,CAAG,CAAC,EACxD,CACN,CACDQ,IAAI,CAAE,SAAS,CACfC,SAAS,CAAEA,CAAA,GAAM,CACf9C,cAAc,CAACD,WAAW,CAACgD,MAAM,CAAC0B,IAAI,EAAIA,IAAI,CAACxC,EAAE,GAAKA,EAAE,CAAC,CAAC,CAC1DzE,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC,CACzB,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAoD,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAA3B,MAAM,CAAG,KAAM,CAAAzD,QAAQ,CAACqB,cAAc,CAAC,CAAC,CAC9C,KAAM,CAAAgE,OAAmB,CAAG,CAC1B5C,EAAE,CAAE,CAAA1B,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE0B,EAAE,GAAIkB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAC5CsB,IAAI,CAAE1B,MAAM,CAAC0B,IAAI,CACjBG,SAAS,CAAE7B,MAAM,CAAC6B,SAAS,CAC3BC,MAAM,CAAE9B,MAAM,CAAC8B,MAAM,CACrBC,QAAQ,CAAE/B,MAAM,CAAC+B,QAAQ,EAAI,CAAC,CAC9BC,QAAQ,CAAEhC,MAAM,CAACgC,QAAQ,GAAK,KAAK,CACnCtE,WAAW,CAAEsC,MAAM,CAACtC,WACtB,CAAC,CAED,GAAIJ,WAAW,CAAE,CACfP,cAAc,CAACD,WAAW,CAACuE,GAAG,CAACG,IAAI,EAAIA,IAAI,CAACxC,EAAE,GAAK1B,WAAW,CAAC0B,EAAE,CAAG4C,OAAO,CAAGJ,IAAI,CAAC,CAAC,CACpFjH,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC,CACzB,CAAC,IAAM,CACLxB,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAE8E,OAAO,CAAC,CAAC,CACzCrH,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC,CACzB,CAEApB,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAE,MAAOU,KAAK,CAAE,CACdtD,OAAO,CAACsD,KAAK,CAAC,SAAS,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAAoE,WAAW,CAAG,CAClB,CACExE,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,OAAO,CAClBC,GAAG,CAAE,OAAO,CACZC,KAAK,CAAE,EACT,CAAC,CACD,CACE3E,KAAK,CAAE,MAAM,CACbyE,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GACT,CAAC,CACD,CACE3E,KAAK,CAAE,MAAM,CACbyE,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBE,QAAQ,CAAE,IACZ,CAAC,CACD,CACE5E,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBE,QAAQ,CAAE,IACZ,CAAC,CACD,CACE5E,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EACT,CAAC,CACD,CACE3E,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,EACT,CAAC,CACD,CACE3E,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGC,KAAa,EAAKA,KAAK,QAAAC,MAAA,CAAOD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAK,GAC9D,CAAC,CACD,CACEhF,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGC,KAAa,EAAKA,KAAK,QAAAC,MAAA,CAAOD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAK,GAC9D,CAAC,CACD,CACEhF,KAAK,CAAE,IAAI,CACX0E,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACI,CAAM,CAAEC,MAAe,gBAC9B/G,KAAA,CAAC1B,KAAK,EAAC0I,IAAI,CAAE,CAAE,CAAAxD,QAAA,EACZuD,MAAM,CAACjC,UAAU,eAAIhF,IAAA,CAACZ,GAAG,EAAC4E,KAAK,CAAC,MAAM,CAAAN,QAAA,CAAC,cAAE,CAAK,CAAC,CAC/CuD,MAAM,CAAChC,WAAW,eAAIjF,IAAA,CAACZ,GAAG,EAAC4E,KAAK,CAAC,OAAO,CAAAN,QAAA,CAAC,cAAE,CAAK,CAAC,CACjDuD,MAAM,CAAC/B,aAAa,eAAIlF,IAAA,CAACZ,GAAG,EAAC4E,KAAK,CAAC,QAAQ,CAAAN,QAAA,CAAC,cAAE,CAAK,CAAC,EAChD,CAEX,CAAC,CACD,CACE3B,KAAK,CAAE,IAAI,CACX0E,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACI,CAAM,CAAEC,MAAe,gBAC9B/G,KAAA,CAAC1B,KAAK,EAAC0I,IAAI,CAAC,OAAO,CAAAxD,QAAA,eACjB1D,IAAA,CAACzB,MAAM,EACL2F,IAAI,CAAC,MAAM,CACXgD,IAAI,CAAC,OAAO,CACZC,IAAI,cAAEnH,IAAA,CAACP,YAAY,GAAE,CAAE,CACvB2H,OAAO,CAAEA,CAAA,GAAMlE,cAAc,CAAC+D,MAAM,CAAE,CACvC,CAAC,cACFjH,IAAA,CAACzB,MAAM,EACL2F,IAAI,CAAC,MAAM,CACXgD,IAAI,CAAC,OAAO,CACZG,MAAM,MACNF,IAAI,cAAEnH,IAAA,CAACR,cAAc,GAAE,CAAE,CACzB4H,OAAO,CAAEA,CAAA,GAAM/D,gBAAgB,CAAC4D,MAAM,CAAC3D,EAAE,CAAE,CAC5C,CAAC,EACG,CAEX,CAAC,CACF,CAED;AACA,KAAM,CAAAgE,WAAW,CAAG,CAClB,CACEvF,KAAK,CAAE,MAAM,CACbyE,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MACP,CAAC,CACD,CACE1E,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBE,QAAQ,CAAE,IACZ,CAAC,CACD,CACE5E,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbE,QAAQ,CAAE,IACZ,CAAC,CACD,CACE5E,KAAK,CAAE,KAAK,CACZyE,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EACT,CAAC,CACD,CACE3E,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGN,QAAiB,eACxBtG,IAAA,CAACZ,GAAG,EAAC4E,KAAK,CAAEsC,QAAQ,CAAG,OAAO,CAAG,KAAM,CAAA5C,QAAA,CACpC4C,QAAQ,CAAG,IAAI,CAAG,IAAI,CACpB,CAET,CAAC,CACD,CACEvE,KAAK,CAAE,IAAI,CACX0E,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACI,CAAM,CAAEC,MAAkB,gBACjC/G,KAAA,CAAC1B,KAAK,EAAC0I,IAAI,CAAC,OAAO,CAAAxD,QAAA,eACjB1D,IAAA,CAACzB,MAAM,EACL2F,IAAI,CAAC,MAAM,CACXgD,IAAI,CAAC,OAAO,CACZC,IAAI,cAAEnH,IAAA,CAACP,YAAY,GAAE,CAAE,CACvB2H,OAAO,CAAEA,CAAA,GAAMvB,cAAc,CAACoB,MAAM,CAAE,CACvC,CAAC,cACFjH,IAAA,CAACzB,MAAM,EACL2F,IAAI,CAAC,MAAM,CACXgD,IAAI,CAAC,OAAO,CACZG,MAAM,MACNF,IAAI,cAAEnH,IAAA,CAACR,cAAc,GAAE,CAAE,CACzB4H,OAAO,CAAEA,CAAA,GAAMrB,gBAAgB,CAACkB,MAAM,CAAC3D,EAAE,CAAE,CAC5C,CAAC,EACG,CAEX,CAAC,CACF,CAED;AACA,KAAM,CAAAiE,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,OAAQzG,WAAW,EACjB,IAAK,EAAC,CACJ,mBACEd,IAAA,CAAC3B,IAAI,EAACqC,IAAI,CAAEA,IAAK,CAAC8G,MAAM,CAAC,UAAU,CAAA9D,QAAA,cACjCxD,KAAA,CAACzB,GAAG,EAACgJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAA/D,QAAA,eACpB1D,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAjE,QAAA,cAClB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,MAAM,CACX6B,KAAK,CAAC,iBAAO,CACbC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,UAAW,CAAC,CAAE,CAAA6E,QAAA,cAEjD1D,IAAA,CAAC1B,KAAK,EAAC0J,WAAW,CAAC,mCAAU,CAAE,CAAC,CACvB,CAAC,CACT,CAAC,cACNhI,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAjE,QAAA,cAClB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,MAAM,CACX6B,KAAK,CAAC,iBAAO,CACbC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,UAAW,CAAC,CAAE,CAAA6E,QAAA,cAEjD1D,IAAA,CAAC1B,KAAK,EAAC0J,WAAW,CAAC,mCAAU,CAAE,CAAC,CACvB,CAAC,CACT,CAAC,cACNhI,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAjE,QAAA,cAClB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,SAAS,CACd6B,KAAK,CAAC,oBAAK,CACXC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,QAAS,CAAC,CAAE,CAC/CoJ,YAAY,CAAC,MAAM,CAAAvE,QAAA,cAEnB1D,IAAA,CAAC1B,KAAK,EAAC0J,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACNhI,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAAAhE,QAAA,cACV1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,aAAa,CAClB6B,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA6E,QAAA,cAE9C1D,IAAA,CAACK,QAAQ,EAAC6H,IAAI,CAAE,CAAE,CAACF,WAAW,CAAC,mCAAU,CAAE,CAAC,CACnC,CAAC,CACT,CAAC,EACH,CAAC,CACF,CAAC,CAGX,IAAK,EAAC,CACJ,mBACE9H,KAAA,QAAAwD,QAAA,eACExD,KAAA,CAACzB,GAAG,EAAC0J,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACrE,KAAK,CAAE,CAAEsE,YAAY,CAAE,EAAG,CAAE,CAAA3E,QAAA,eACtE1D,IAAA,CAACtB,GAAG,EAAAgF,QAAA,cACF1D,IAAA,CAACG,KAAK,EAAC2E,KAAK,CAAE,CAAE,CAAApB,QAAA,CAAC,iBAAK,CAAO,CAAC,CAC3B,CAAC,cACN1D,IAAA,CAACtB,GAAG,EAAAgF,QAAA,cACF1D,IAAA,CAACzB,MAAM,EAAC2F,IAAI,CAAC,SAAS,CAACiD,IAAI,cAAEnH,IAAA,CAACT,YAAY,GAAE,CAAE,CAAC6H,OAAO,CAAEpE,aAAc,CAAAU,QAAA,CAAC,0BAEvE,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cACN1D,IAAA,CAAChB,KAAK,EACJsJ,OAAO,CAAE/B,WAAY,CACrBgC,UAAU,CAAErH,QAAS,CACrBsH,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,MAAM,CAAE,CAAEC,SAAS,CAAE,oBAAqB,CAAE,CAC7C,CAAC,EACC,CAAC,CAGV,IAAK,EAAC,CACJ,mBACE3I,KAAA,QAAAwD,QAAA,eACExD,KAAA,CAACzB,GAAG,EAAC0J,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACrE,KAAK,CAAE,CAAEsE,YAAY,CAAE,EAAG,CAAE,CAAA3E,QAAA,eACtE1D,IAAA,CAACtB,GAAG,EAAAgF,QAAA,cACF1D,IAAA,CAACG,KAAK,EAAC2E,KAAK,CAAE,CAAE,CAAApB,QAAA,CAAC,0BAAI,CAAO,CAAC,CAC1B,CAAC,cACN1D,IAAA,CAACtB,GAAG,EAAAgF,QAAA,cACF1D,IAAA,CAACzB,MAAM,EAAC2F,IAAI,CAAC,SAAS,CAACiD,IAAI,cAAEnH,IAAA,CAACT,YAAY,GAAE,CAAE,CAAC6H,OAAO,CAAExB,aAAc,CAAAlC,QAAA,CAAC,0BAEvE,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cACN1D,IAAA,CAAChB,KAAK,EACJsJ,OAAO,CAAEhB,WAAY,CACrBiB,UAAU,CAAEnH,WAAY,CACxBoH,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBG,MAAM,CAAE,CAAEC,SAAS,CAAE,cAAe,CAAE,CACvC,CAAC,EACC,CAAC,CAGV,IAAK,EAAC,CACJ,KAAM,CAAAtG,SAAS,CAAG7B,IAAI,CAACoI,cAAc,CAAC,CAAC,CACvC,mBACE5I,KAAA,QAAAwD,QAAA,eACE1D,IAAA,CAACG,KAAK,EAAC2E,KAAK,CAAE,CAAE,CAAApB,QAAA,CAAC,0BAAI,CAAO,CAAC,cAC7BxD,KAAA,CAAC9B,IAAI,EAAC2F,KAAK,CAAE,CAAEsE,YAAY,CAAE,EAAG,CAAE,CAAA3E,QAAA,eAChC1D,IAAA,CAACG,KAAK,EAAC2E,KAAK,CAAE,CAAE,CAAApB,QAAA,CAAC,0BAAI,CAAO,CAAC,cAC7BxD,KAAA,CAACzB,GAAG,EAACgJ,MAAM,CAAE,CAAC,EAAE,CAAE,CAAC,CAAE,CAAA/D,QAAA,eACnBxD,KAAA,CAACxB,GAAG,EAACqK,IAAI,CAAE,CAAE,CAAArF,QAAA,eACX1D,IAAA,CAACI,IAAI,EAAC4I,MAAM,MAAAtF,QAAA,CAAC,uBAAM,CAAM,CAAC,cAC1B1D,IAAA,CAACI,IAAI,EAAAsD,QAAA,CAAEnB,SAAS,CAACyD,IAAI,CAAO,CAAC,EAC1B,CAAC,cACN9F,KAAA,CAACxB,GAAG,EAACqK,IAAI,CAAE,CAAE,CAAArF,QAAA,eACX1D,IAAA,CAACI,IAAI,EAAC4I,MAAM,MAAAtF,QAAA,CAAC,uBAAM,CAAM,CAAC,cAC1B1D,IAAA,CAACI,IAAI,EAAAsD,QAAA,CAAEnB,SAAS,CAAC0G,IAAI,CAAO,CAAC,EAC1B,CAAC,cACN/I,KAAA,CAACxB,GAAG,EAACqK,IAAI,CAAE,CAAE,CAAArF,QAAA,eACX1D,IAAA,CAACI,IAAI,EAAC4I,MAAM,MAAAtF,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxB1D,IAAA,CAACI,IAAI,EAAAsD,QAAA,CAAEnB,SAAS,CAAC2G,OAAO,CAAO,CAAC,EAC7B,CAAC,cACNhJ,KAAA,CAACxB,GAAG,EAACqK,IAAI,CAAE,EAAG,CAAArF,QAAA,eACZ1D,IAAA,CAACI,IAAI,EAAC4I,MAAM,MAAAtF,QAAA,CAAC,oBAAG,CAAM,CAAC,cACvB1D,IAAA,CAACI,IAAI,EAAAsD,QAAA,CAAEnB,SAAS,CAACP,WAAW,CAAO,CAAC,EACjC,CAAC,EACH,CAAC,EACF,CAAC,cAEP9B,KAAA,CAAC9B,IAAI,EAAC2F,KAAK,CAAE,CAAEsE,YAAY,CAAE,EAAG,CAAE,CAAA3E,QAAA,eAChCxD,KAAA,CAACC,KAAK,EAAC2E,KAAK,CAAE,CAAE,CAAApB,QAAA,EAAC,uBAAM,CAACxC,QAAQ,CAACkB,MAAM,CAAC,0BAAI,EAAO,CAAC,cACpDpC,IAAA,CAAChB,KAAK,EACJsJ,OAAO,CAAE/B,WAAW,CAACnC,MAAM,CAAC+E,GAAG,EAAIA,GAAG,CAAC1C,GAAG,GAAK,QAAQ,CAAE,CACzD8B,UAAU,CAAErH,QAAS,CACrBsH,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBvB,IAAI,CAAC,OAAO,CACZwB,MAAM,CAAE,CAAEC,CAAC,CAAE,GAAI,CAAE,CACpB,CAAC,EACE,CAAC,cAEPzI,KAAA,CAAC9B,IAAI,EAAAsF,QAAA,eACHxD,KAAA,CAACC,KAAK,EAAC2E,KAAK,CAAE,CAAE,CAAApB,QAAA,EAAC,gCAAK,CAACtC,WAAW,CAACgB,MAAM,CAAC,0BAAI,EAAO,CAAC,cACtDpC,IAAA,CAAChB,KAAK,EACJsJ,OAAO,CAAEhB,WAAW,CAAClD,MAAM,CAAC+E,GAAG,EAAIA,GAAG,CAAC1C,GAAG,GAAK,QAAQ,CAAE,CACzD8B,UAAU,CAAEnH,WAAY,CACxBoH,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBvB,IAAI,CAAC,OAAO,CACb,CAAC,EACE,CAAC,EACJ,CAAC,CAGV,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,mBACEhH,KAAA,QAAAwD,QAAA,eACExD,KAAA,CAAC9B,IAAI,EAAAsF,QAAA,eACH1D,IAAA,CAACvB,GAAG,EAAC0J,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACrE,KAAK,CAAE,CAAEsE,YAAY,CAAE,EAAG,CAAE,CAAA3E,QAAA,cACtE1D,IAAA,CAACtB,GAAG,EAAAgF,QAAA,cACFxD,KAAA,CAAC1B,KAAK,EAAAkF,QAAA,eACJ1D,IAAA,CAACzB,MAAM,EAAC4I,IAAI,cAAEnH,IAAA,CAACV,iBAAiB,GAAE,CAAE,CAAC8H,OAAO,CAAErE,YAAa,CAAAW,QAAA,CAAC,cAE5D,CAAQ,CAAC,cACT1D,IAAA,CAACG,KAAK,EAAC2E,KAAK,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEqF,MAAM,CAAE,CAAE,CAAE,CAAA1F,QAAA,CAAC,6BAEvC,CAAO,CAAC,EACH,CAAC,CACL,CAAC,CACH,CAAC,cAEN1D,IAAA,CAACpB,KAAK,EAACyK,OAAO,CAAEvI,WAAY,CAACiD,KAAK,CAAE,CAAEsE,YAAY,CAAE,EAAG,CAAE,CAAA3E,QAAA,CACtD5B,KAAK,CAAC6D,GAAG,CAACxC,IAAI,eACbnD,IAAA,CAACM,IAAI,EAAkByB,KAAK,CAAEoB,IAAI,CAACpB,KAAM,CAACC,WAAW,CAAEmB,IAAI,CAACnB,WAAY,EAA7DmB,IAAI,CAACpB,KAA0D,CAC3E,CAAC,CACG,CAAC,cAER/B,IAAA,QAAK+D,KAAK,CAAE,CAAEuF,SAAS,CAAE,GAAI,CAAE,CAAA5F,QAAA,CAC5B6D,iBAAiB,CAAC,CAAC,CACjB,CAAC,cAENvH,IAAA,CAAClB,OAAO,GAAE,CAAC,cAEXoB,KAAA,CAACzB,GAAG,EAAC0J,OAAO,CAAC,eAAe,CAAAzE,QAAA,eAC1B1D,IAAA,CAACtB,GAAG,EAAAgF,QAAA,CACD5C,WAAW,CAAG,CAAC,eACdd,IAAA,CAACzB,MAAM,EAAC6I,OAAO,CAAE/E,UAAW,CAAAqB,QAAA,CAAC,oBAE7B,CAAQ,CACT,CACE,CAAC,cACN1D,IAAA,CAACtB,GAAG,EAAAgF,QAAA,cACFxD,KAAA,CAAC1B,KAAK,EAAAkF,QAAA,eACJ1D,IAAA,CAACzB,MAAM,EAAC6I,OAAO,CAAErE,YAAa,CAAAW,QAAA,CAAC,cAE/B,CAAQ,CAAC,CACR5C,WAAW,CAAGgB,KAAK,CAACM,MAAM,CAAG,CAAC,cAC7BpC,IAAA,CAACzB,MAAM,EAAC2F,IAAI,CAAC,SAAS,CAACkD,OAAO,CAAEnF,UAAW,CAAAyB,QAAA,CAAC,oBAE5C,CAAQ,CAAC,cAET1D,IAAA,CAACzB,MAAM,EACL2F,IAAI,CAAC,SAAS,CACdiD,IAAI,cAAEnH,IAAA,CAACX,YAAY,GAAE,CAAE,CACvB2B,OAAO,CAAEA,OAAQ,CACjBoG,OAAO,CAAE9E,UAAW,CAAAoB,QAAA,CACrB,cAED,CAAQ,CACT,EACI,CAAC,CACL,CAAC,EACH,CAAC,EACF,CAAC,cAGP1D,IAAA,CAACf,KAAK,EACJ8C,KAAK,CAAEL,WAAW,CAAG,MAAM,CAAG,MAAO,CACrC6H,IAAI,CAAEjI,gBAAiB,CACvBkI,IAAI,CAAEnF,iBAAkB,CACxBoF,QAAQ,CAAEA,CAAA,GAAMlI,mBAAmB,CAAC,KAAK,CAAE,CAC3CmF,KAAK,CAAE,GAAI,CACXgD,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAjG,QAAA,cAEf1D,IAAA,CAAC3B,IAAI,EAACqC,IAAI,CAAEE,QAAS,CAAC4G,MAAM,CAAC,UAAU,CAAA9D,QAAA,cACrCxD,KAAA,CAACzB,GAAG,EAACgJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAA/D,QAAA,eACpB1D,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAjE,QAAA,cAClB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,cAAc,CACnB6B,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA6E,QAAA,cAEhD1D,IAAA,CAAC1B,KAAK,EAAC0J,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNhI,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAjE,QAAA,cAClB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,cAAc,CACnB6B,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA6E,QAAA,cAEhD1D,IAAA,CAAC1B,KAAK,EAAC0J,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNhI,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAAAhE,QAAA,cACV1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,cAAc,CACnB6B,KAAK,CAAC,0BAAM,CAAAnE,QAAA,cAEZ1D,IAAA,CAAC1B,KAAK,EAAC0J,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNhI,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjE,QAAA,cACjB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,UAAU,CACf6B,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA6E,QAAA,cAE9C1D,IAAA,CAACd,WAAW,EAAC0K,GAAG,CAAE,CAAE,CAACC,SAAS,CAAE,CAAE,CAAC9F,KAAK,CAAE,CAAE2C,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACtD,CAAC,CACT,CAAC,cACN1G,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjE,QAAA,cACjB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,MAAM,CACX6B,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA6E,QAAA,cAE9C1D,IAAA,CAACjB,MAAM,EAACiJ,WAAW,CAAC,gCAAO,CAAAtE,QAAA,CACxB7D,KAAK,CAAC8F,GAAG,CAAC7B,IAAI,eACb9D,IAAA,CAACjB,MAAM,CAAC+K,MAAM,EAAYjD,KAAK,CAAE/C,IAAK,CAAAJ,QAAA,CACnCI,IAAI,EADaA,IAEL,CAChB,CAAC,CACI,CAAC,CACA,CAAC,CACT,CAAC,cACN9D,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjE,QAAA,cACjB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,OAAO,CACZ6B,KAAK,CAAC,cAAI,CACVI,YAAY,CAAE,CAAE,CAAAvE,QAAA,cAEhB1D,IAAA,CAACd,WAAW,EAAC0K,GAAG,CAAE,CAAE,CAACG,GAAG,CAAE,EAAG,CAAChG,KAAK,CAAE,CAAE2C,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACjD,CAAC,CACT,CAAC,cACN1G,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjE,QAAA,cACjB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,WAAW,CAChB6B,KAAK,CAAC,cAAI,CAAAnE,QAAA,cAEV1D,IAAA,CAACd,WAAW,EAAC0K,GAAG,CAAE,CAAE,CAACC,SAAS,CAAE,CAAE,CAAC9F,KAAK,CAAE,CAAE2C,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACtD,CAAC,CACT,CAAC,cACN1G,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjE,QAAA,cACjB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,UAAU,CACf6B,KAAK,CAAC,oBAAK,CAAAnE,QAAA,cAEX1D,IAAA,CAAC1B,KAAK,EAAC0J,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACNhI,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjE,QAAA,cACjB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,UAAU,CACf6B,KAAK,CAAC,sBAAO,CAAAnE,QAAA,cAEb1D,IAAA,CAACd,WAAW,EAAC0K,GAAG,CAAE,CAAE,CAAC7F,KAAK,CAAE,CAAE2C,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACxC,CAAC,CACT,CAAC,cACN1G,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAAAhE,QAAA,cACVxD,KAAA,CAAC1B,KAAK,EAAAkF,QAAA,eACJ1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EAAC5B,IAAI,CAAC,YAAY,CAACgE,aAAa,CAAC,SAAS,CAAAtG,QAAA,cAClD1D,IAAA,CAACb,QAAQ,EAAAuE,QAAA,CAAC,cAAE,CAAU,CAAC,CACd,CAAC,cACZ1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EAAC5B,IAAI,CAAC,aAAa,CAACgE,aAAa,CAAC,SAAS,CAAC/B,YAAY,CAAE,IAAK,CAAAvE,QAAA,cACvE1D,IAAA,CAACb,QAAQ,EAAAuE,QAAA,CAAC,cAAE,CAAU,CAAC,CACd,CAAC,cACZ1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EAAC5B,IAAI,CAAC,eAAe,CAACgE,aAAa,CAAC,SAAS,CAAAtG,QAAA,cACrD1D,IAAA,CAACb,QAAQ,EAAAuE,QAAA,CAAC,cAAE,CAAU,CAAC,CACd,CAAC,EACP,CAAC,CACL,CAAC,cACN1D,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAAAhE,QAAA,cACV1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,SAAS,CACd6B,KAAK,CAAC,cAAI,CAAAnE,QAAA,cAEV1D,IAAA,CAACK,QAAQ,EAAC6H,IAAI,CAAE,CAAE,CAACF,WAAW,CAAC,gCAAO,CAAE,CAAC,CAChC,CAAC,CACT,CAAC,EACH,CAAC,CACF,CAAC,CACF,CAAC,cAGRhI,IAAA,CAACf,KAAK,EACJ8C,KAAK,CAAEH,WAAW,CAAG,QAAQ,CAAG,QAAS,CACzC2H,IAAI,CAAE/H,gBAAiB,CACvBgI,IAAI,CAAEvD,iBAAkB,CACxBwD,QAAQ,CAAEA,CAAA,GAAMhI,mBAAmB,CAAC,KAAK,CAAE,CAC3CiF,KAAK,CAAE,GAAI,CACXgD,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAjG,QAAA,cAEfxD,KAAA,CAAC7B,IAAI,EAACqC,IAAI,CAAEG,QAAS,CAAC2G,MAAM,CAAC,UAAU,CAAA9D,QAAA,eACrC1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,MAAM,CACX6B,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA6E,QAAA,cAEhD1D,IAAA,CAAC1B,KAAK,EAAC0J,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,cACZhI,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,WAAW,CAChB6B,KAAK,CAAC,gCAAO,CACbC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,UAAW,CAAC,CAAE,CAAA6E,QAAA,cAEjD1D,IAAA,CAACK,QAAQ,EAAC6H,IAAI,CAAE,CAAE,CAACF,WAAW,CAAC,oDAAqC,CAAE,CAAC,CAC9D,CAAC,cACZhI,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,QAAQ,CACb6B,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElJ,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA6E,QAAA,cAEhD1D,IAAA,CAACK,QAAQ,EAAC6H,IAAI,CAAE,CAAE,CAACF,WAAW,CAAC,gEAAiD,CAAE,CAAC,CAC1E,CAAC,cACZ9H,KAAA,CAACzB,GAAG,EAACgJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAA/D,QAAA,eACpB1D,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAjE,QAAA,cAClB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,UAAU,CACf6B,KAAK,CAAC,oBAAK,CACXI,YAAY,CAAE,CAAE,CAAAvE,QAAA,cAEhB1D,IAAA,CAACd,WAAW,EAAC0K,GAAG,CAAE,CAAE,CAACG,GAAG,CAAE,GAAI,CAAChG,KAAK,CAAE,CAAE2C,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAClD,CAAC,CACT,CAAC,cACN1G,IAAA,CAACtB,GAAG,EAACgJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAjE,QAAA,cAClB1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EAAC5B,IAAI,CAAC,UAAU,CAACgE,aAAa,CAAC,SAAS,CAAC/B,YAAY,CAAE,IAAK,CAAAvE,QAAA,cACpE1D,IAAA,CAACb,QAAQ,EAAAuE,QAAA,CAAC,0BAAI,CAAU,CAAC,CAChB,CAAC,CACT,CAAC,EACH,CAAC,cACN1D,IAAA,CAAC3B,IAAI,CAACuJ,IAAI,EACR5B,IAAI,CAAC,aAAa,CAClB6B,KAAK,CAAC,0BAAM,CAAAnE,QAAA,cAEZ1D,IAAA,CAACK,QAAQ,EAAC6H,IAAI,CAAE,CAAE,CAACF,WAAW,CAAC,4CAAS,CAAE,CAAC,CAClC,CAAC,EACR,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}