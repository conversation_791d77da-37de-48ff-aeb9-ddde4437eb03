{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\system\\\\AuditLogPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Table, Button, Space, Input, Select, Row, Col, Tag, DatePicker, Tooltip, Modal, Descriptions, Timeline, Alert, Statistic, Tabs } from 'antd';\nimport { EyeOutlined, ExportOutlined, ReloadOutlined, UserOutlined, FileTextOutlined, WarningOutlined, CheckCircleOutlined, ExclamationCircleOutlined, SecurityScanOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { formatDate } from '../../utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Search\n} = Input;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TabPane\n} = Tabs;\nconst AuditLogPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [userFilter, setUserFilter] = useState();\n  const [moduleFilter, setModuleFilter] = useState();\n  const [operationFilter, setOperationFilter] = useState();\n  const [statusFilter, setStatusFilter] = useState();\n  const [riskLevelFilter, setRiskLevelFilter] = useState();\n  const [dateRange, setDateRange] = useState(null);\n  const [selectedLog, setSelectedLog] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [activeTab, setActiveTab] = useState('list');\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // TODO: 调用API获取审计日志数据\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    } catch (error) {\n      console.error('加载审计日志失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleViewDetail = record => {\n    setSelectedLog(record);\n    setDetailModalVisible(true);\n  };\n  const handleExport = () => {\n    // TODO: 实现导出功能\n    Modal.success({\n      title: '导出成功',\n      content: '审计日志已导出到Excel文件'\n    });\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'SUCCESS':\n        return 'success';\n      case 'FAILED':\n        return 'error';\n      case 'WARNING':\n        return 'warning';\n      default:\n        return 'default';\n    }\n  };\n  const getRiskLevelColor = level => {\n    switch (level) {\n      case 'LOW':\n        return 'green';\n      case 'MEDIUM':\n        return 'orange';\n      case 'HIGH':\n        return 'red';\n      case 'CRITICAL':\n        return 'purple';\n      default:\n        return 'default';\n    }\n  };\n  const getOperationIcon = operation => {\n    switch (operation) {\n      case 'CREATE':\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 29\n        }, this);\n      case 'UPDATE':\n        return /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 29\n        }, this);\n      case 'DELETE':\n        return /*#__PURE__*/_jsxDEV(WarningOutlined, {\n          style: {\n            color: '#f5222d'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 29\n        }, this);\n      case 'LOGIN':\n        return /*#__PURE__*/_jsxDEV(UserOutlined, {\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 28\n        }, this);\n      case 'LOGOUT':\n        return /*#__PURE__*/_jsxDEV(UserOutlined, {\n          style: {\n            color: '#8c8c8c'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 29\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 23\n        }, this);\n    }\n  };\n\n  // 模拟审计日志数据\n  const mockAuditLogs = [{\n    id: '1',\n    userId: 'user001',\n    userName: '张三',\n    userRole: 'BOM管理员',\n    operation: 'UPDATE',\n    module: 'BOM管理',\n    resourceType: 'CoreBOM',\n    resourceId: 'BOM-001',\n    resourceName: '主控板BOM',\n    oldValue: {\n      version: '1.0',\n      status: 'DRAFT'\n    },\n    newValue: {\n      version: '1.1',\n      status: 'ACTIVE'\n    },\n    reason: '更新产品规格',\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n    timestamp: '2024-03-25T10:30:00Z',\n    status: 'SUCCESS',\n    riskLevel: 'MEDIUM',\n    description: '更新BOM版本并激活'\n  }, {\n    id: '2',\n    userId: 'user002',\n    userName: '李四',\n    userRole: '采购经理',\n    operation: 'DELETE',\n    module: '采购管理',\n    resourceType: 'PurchaseOrder',\n    resourceId: 'PO-2024-001',\n    resourceName: '电子元器件采购订单',\n    reason: '订单取消',\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n    timestamp: '2024-03-25T09:15:00Z',\n    status: 'SUCCESS',\n    riskLevel: 'HIGH',\n    description: '删除采购订单'\n  }, {\n    id: '3',\n    userId: 'user003',\n    userName: '王五',\n    userRole: '系统管理员',\n    operation: 'CREATE',\n    module: '用户管理',\n    resourceType: 'User',\n    resourceId: 'user004',\n    resourceName: '赵六',\n    newValue: {\n      userName: '赵六',\n      role: 'OPERATOR',\n      status: 'ACTIVE'\n    },\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n    timestamp: '2024-03-25T08:45:00Z',\n    status: 'SUCCESS',\n    riskLevel: 'LOW',\n    description: '创建新用户账户'\n  }, {\n    id: '4',\n    userId: 'user001',\n    userName: '张三',\n    userRole: 'BOM管理员',\n    operation: 'LOGIN',\n    module: '认证',\n    resourceType: 'Session',\n    resourceId: 'session-001',\n    resourceName: '用户登录',\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n    timestamp: '2024-03-25T08:00:00Z',\n    status: 'SUCCESS',\n    riskLevel: 'LOW',\n    description: '用户成功登录系统'\n  }, {\n    id: '5',\n    userId: 'unknown',\n    userName: '未知用户',\n    userRole: '',\n    operation: 'LOGIN',\n    module: '认证',\n    resourceType: 'Session',\n    resourceId: 'session-002',\n    resourceName: '登录失败',\n    ipAddress: '*************',\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n    timestamp: '2024-03-25T07:30:00Z',\n    status: 'FAILED',\n    riskLevel: 'CRITICAL',\n    description: '多次登录失败，可能存在安全风险'\n  }];\n  const auditLogColumns = [{\n    title: '时间',\n    dataIndex: 'timestamp',\n    key: 'timestamp',\n    width: 160,\n    render: timestamp => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: formatDate(timestamp, 'YYYY-MM-DD')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: formatDate(timestamp, 'HH:mm:ss')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix()\n  }, {\n    title: '用户',\n    key: 'user',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: record.userName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: record.userRole\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'operation',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [getOperationIcon(record.operation), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: record.operation\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '模块',\n    dataIndex: 'module',\n    key: 'module',\n    width: 100\n  }, {\n    title: '资源',\n    key: 'resource',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: record.resourceName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [record.resourceType, \": \", record.resourceId]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '风险等级',\n    dataIndex: 'riskLevel',\n    key: 'riskLevel',\n    width: 100,\n    render: level => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getRiskLevelColor(level),\n      children: level\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ipAddress',\n    key: 'ipAddress',\n    width: 120\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 80,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewDetail(record)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 9\n    }, this)\n  }];\n  const renderStatistics = () => {\n    const totalLogs = mockAuditLogs.length;\n    const successLogs = mockAuditLogs.filter(log => log.status === 'SUCCESS').length;\n    const failedLogs = mockAuditLogs.filter(log => log.status === 'FAILED').length;\n    const highRiskLogs = mockAuditLogs.filter(log => log.riskLevel === 'HIGH' || log.riskLevel === 'CRITICAL').length;\n    return /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u64CD\\u4F5C\\u6570\",\n            value: totalLogs,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6210\\u529F\\u64CD\\u4F5C\",\n            value: successLogs,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5931\\u8D25\\u64CD\\u4F5C\",\n            value: failedLogs,\n            prefix: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#cf1322'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9AD8\\u98CE\\u9669\\u64CD\\u4F5C\",\n            value: highRiskLogs,\n            prefix: /*#__PURE__*/_jsxDEV(SecurityScanOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#d4380d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this);\n  };\n  const renderSecurityAlerts = () => {\n    const criticalLogs = mockAuditLogs.filter(log => log.riskLevel === 'CRITICAL');\n    if (criticalLogs.length === 0) {\n      return null;\n    }\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u5B89\\u5168\\u8B66\\u544A\",\n      description: `检测到 ${criticalLogs.length} 个高风险操作，请及时处理`,\n      type: \"error\",\n      showIcon: true,\n      closable: true,\n      style: {\n        marginBottom: 16\n      },\n      action: /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        danger: true,\n        children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this);\n  };\n  const renderAuditTimeline = () => {\n    const timelineItems = mockAuditLogs.slice(0, 10).map(log => ({\n      color: log.status === 'FAILED' ? 'red' : log.riskLevel === 'HIGH' ? 'orange' : 'blue',\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [getOperationIcon(log.operation), /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: log.userName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: log.operation\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: log.resourceName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: 12\n            },\n            children: [formatDate(log.timestamp), \" \\u2022 \", log.ipAddress]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(log.status),\n            children: log.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: getRiskLevelColor(log.riskLevel),\n            children: log.riskLevel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)\n    }));\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u64CD\\u4F5C\\u65F6\\u95F4\\u7EBF\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Timeline, {\n        items: timelineItems\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u5BA1\\u8BA1\\u65E5\\u5FD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u7CFB\\u7EDF\\u64CD\\u4F5C\\u8BB0\\u5F55\\u548C\\u5B89\\u5168\\u5BA1\\u8BA1\\u8DDF\\u8E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              placeholder: \"\\u641C\\u7D22\\u7528\\u6237\\u3001\\u64CD\\u4F5C\\u3001\\u8D44\\u6E90\",\n              allowClear: true,\n              style: {\n                width: 200\n              },\n              onSearch: setSearchKeyword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u7528\\u6237\",\n              allowClear: true,\n              style: {\n                width: 100\n              },\n              value: userFilter,\n              onChange: setUserFilter,\n              options: [{\n                label: '张三',\n                value: 'user001'\n              }, {\n                label: '李四',\n                value: 'user002'\n              }, {\n                label: '王五',\n                value: 'user003'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u6A21\\u5757\",\n              allowClear: true,\n              style: {\n                width: 100\n              },\n              value: moduleFilter,\n              onChange: setModuleFilter,\n              options: [{\n                label: 'BOM管理',\n                value: 'BOM管理'\n              }, {\n                label: '采购管理',\n                value: '采购管理'\n              }, {\n                label: '用户管理',\n                value: '用户管理'\n              }, {\n                label: '认证',\n                value: '认证'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u64CD\\u4F5C\\u7C7B\\u578B\",\n              allowClear: true,\n              style: {\n                width: 100\n              },\n              value: operationFilter,\n              onChange: setOperationFilter,\n              options: [{\n                label: '创建',\n                value: 'CREATE'\n              }, {\n                label: '更新',\n                value: 'UPDATE'\n              }, {\n                label: '删除',\n                value: 'DELETE'\n              }, {\n                label: '登录',\n                value: 'LOGIN'\n              }, {\n                label: '登出',\n                value: 'LOGOUT'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u72B6\\u6001\",\n              allowClear: true,\n              style: {\n                width: 100\n              },\n              value: statusFilter,\n              onChange: setStatusFilter,\n              options: [{\n                label: '成功',\n                value: 'SUCCESS'\n              }, {\n                label: '失败',\n                value: 'FAILED'\n              }, {\n                label: '警告',\n                value: 'WARNING'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              value: dateRange,\n              onChange: setDateRange,\n              style: {\n                width: 240\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 29\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 29\n              }, this),\n              onClick: loadData,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this), renderSecurityAlerts(), /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u5BA1\\u8BA1\\u5217\\u8868\",\n          children: [renderStatistics(), /*#__PURE__*/_jsxDEV(Table, {\n            columns: auditLogColumns,\n            dataSource: mockAuditLogs,\n            loading: loading,\n            rowKey: \"id\",\n            scroll: {\n              x: 1200\n            },\n            pagination: {\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)]\n        }, \"list\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u64CD\\u4F5C\\u65F6\\u95F4\\u7EBF\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [16, 16],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              lg: 16,\n              children: renderAuditTimeline()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              lg: 8,\n              children: renderStatistics()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)\n        }, \"timeline\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5BA1\\u8BA1\\u65E5\\u5FD7\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedLog && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u64CD\\u4F5C\\u65F6\\u95F4\",\n          children: formatDate(selectedLog.timestamp)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u64CD\\u4F5C\\u7528\\u6237\",\n          children: [selectedLog.userName, \" (\", selectedLog.userRole, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u64CD\\u4F5C\\u7C7B\\u578B\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [getOperationIcon(selectedLog.operation), selectedLog.operation]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u6240\\u5C5E\\u6A21\\u5757\",\n          children: selectedLog.module\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8D44\\u6E90\\u7C7B\\u578B\",\n          children: selectedLog.resourceType\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8D44\\u6E90ID\",\n          children: selectedLog.resourceId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8D44\\u6E90\\u540D\\u79F0\",\n          span: 2,\n          children: selectedLog.resourceName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u64CD\\u4F5C\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(selectedLog.status),\n            children: selectedLog.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u98CE\\u9669\\u7B49\\u7EA7\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getRiskLevelColor(selectedLog.riskLevel),\n            children: selectedLog.riskLevel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"IP\\u5730\\u5740\",\n          children: selectedLog.ipAddress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u7528\\u6237\\u4EE3\\u7406\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            code: true,\n            style: {\n              fontSize: 12\n            },\n            children: selectedLog.userAgent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 13\n        }, this), selectedLog.reason && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u64CD\\u4F5C\\u539F\\u56E0\",\n          span: 2,\n          children: selectedLog.reason\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u64CD\\u4F5C\\u63CF\\u8FF0\",\n          span: 2,\n          children: selectedLog.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 13\n        }, this), selectedLog.oldValue && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4FEE\\u6539\\u524D\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(\"pre\", {\n            style: {\n              fontSize: 12,\n              background: '#f5f5f5',\n              padding: 8\n            },\n            children: JSON.stringify(selectedLog.oldValue, null, 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 15\n        }, this), selectedLog.newValue && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4FEE\\u6539\\u540E\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(\"pre\", {\n            style: {\n              fontSize: 12,\n              background: '#f5f5f5',\n              padding: 8\n            },\n            children: JSON.stringify(selectedLog.newValue, null, 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 443,\n    columnNumber: 5\n  }, this);\n};\n_s(AuditLogPage, \"O7mruEjFcBIwY3DzRIpA3IJxy8M=\");\n_c = AuditLogPage;\nexport default AuditLogPage;\nvar _c;\n$RefreshReg$(_c, \"AuditLogPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Row", "Col", "Tag", "DatePicker", "<PERSON><PERSON><PERSON>", "Modal", "Descriptions", "Timeline", "<PERSON><PERSON>", "Statistic", "Tabs", "EyeOutlined", "ExportOutlined", "ReloadOutlined", "UserOutlined", "FileTextOutlined", "WarningOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "SecurityScanOutlined", "dayjs", "formatDate", "jsxDEV", "_jsxDEV", "Title", "Text", "Search", "RangePicker", "TabPane", "AuditLogPage", "_s", "loading", "setLoading", "searchKeyword", "setSearchKeyword", "userFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "moduleFilter", "setModuleFilter", "operationFilter", "setOperation<PERSON><PERSON>er", "statusFilter", "setStatus<PERSON>ilter", "riskLevelFilter", "setRiskLevelFilter", "date<PERSON><PERSON><PERSON>", "setDateRange", "<PERSON><PERSON><PERSON>", "setSelectedLog", "detailModalVisible", "setDetailModalVisible", "activeTab", "setActiveTab", "loadData", "Promise", "resolve", "setTimeout", "error", "console", "handleViewDetail", "record", "handleExport", "success", "title", "content", "getStatusColor", "status", "getRiskLevelColor", "level", "getOperationIcon", "operation", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mockAuditLogs", "id", "userId", "userName", "userRole", "module", "resourceType", "resourceId", "resourceName", "oldValue", "version", "newValue", "reason", "ip<PERSON><PERSON><PERSON>", "userAgent", "timestamp", "riskLevel", "description", "role", "auditLogColumns", "dataIndex", "key", "width", "render", "children", "type", "fontSize", "sorter", "a", "b", "unix", "_", "fixed", "icon", "onClick", "renderStatistics", "totalLogs", "length", "successLogs", "filter", "log", "failedLogs", "highRiskLogs", "gutter", "marginBottom", "xs", "sm", "size", "value", "prefix", "valueStyle", "renderSecurityAlerts", "criticalLogs", "message", "showIcon", "closable", "action", "danger", "renderAuditTimeline", "timelineItems", "slice", "map", "strong", "items", "justify", "align", "margin", "placeholder", "allowClear", "onSearch", "onChange", "options", "label", "active<PERSON><PERSON>", "tab", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "lg", "open", "onCancel", "footer", "column", "bordered", "<PERSON><PERSON>", "span", "code", "background", "padding", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/system/AuditLogPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Row,\n  Col,\n  Tag,\n  DatePicker,\n  Tooltip,\n  Modal,\n  Descriptions,\n  Timeline,\n  Alert,\n  Statistic,\n  Progress,\n  Tabs,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  UserOutlined,\n  ClockCircleOutlined,\n  FileTextOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  SecurityScanOutlined,\n  FilterOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { formatDate } from '../../utils';\nimport { OPERATION_TYPES } from '../../constants';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { RangePicker } = DatePicker;\nconst { TabPane } = Tabs;\n\ninterface AuditLog {\n  id: string;\n  userId: string;\n  userName: string;\n  userRole: string;\n  operation: string;\n  module: string;\n  resourceType: string;\n  resourceId: string;\n  resourceName: string;\n  oldValue?: any;\n  newValue?: any;\n  reason?: string;\n  ipAddress: string;\n  userAgent: string;\n  timestamp: string;\n  status: 'SUCCESS' | 'FAILED' | 'WARNING';\n  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n  description: string;\n}\n\nconst AuditLogPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [userFilter, setUserFilter] = useState<string | undefined>();\n  const [moduleFilter, setModuleFilter] = useState<string | undefined>();\n  const [operationFilter, setOperationFilter] = useState<string | undefined>();\n  const [statusFilter, setStatusFilter] = useState<string | undefined>();\n  const [riskLevelFilter, setRiskLevelFilter] = useState<string | undefined>();\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);\n  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [activeTab, setActiveTab] = useState('list');\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // TODO: 调用API获取审计日志数据\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    } catch (error) {\n      console.error('加载审计日志失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleViewDetail = (record: AuditLog) => {\n    setSelectedLog(record);\n    setDetailModalVisible(true);\n  };\n\n  const handleExport = () => {\n    // TODO: 实现导出功能\n    Modal.success({\n      title: '导出成功',\n      content: '审计日志已导出到Excel文件',\n    });\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'SUCCESS': return 'success';\n      case 'FAILED': return 'error';\n      case 'WARNING': return 'warning';\n      default: return 'default';\n    }\n  };\n\n  const getRiskLevelColor = (level: string) => {\n    switch (level) {\n      case 'LOW': return 'green';\n      case 'MEDIUM': return 'orange';\n      case 'HIGH': return 'red';\n      case 'CRITICAL': return 'purple';\n      default: return 'default';\n    }\n  };\n\n  const getOperationIcon = (operation: string) => {\n    switch (operation) {\n      case 'CREATE': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case 'UPDATE': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;\n      case 'DELETE': return <WarningOutlined style={{ color: '#f5222d' }} />;\n      case 'LOGIN': return <UserOutlined style={{ color: '#1890ff' }} />;\n      case 'LOGOUT': return <UserOutlined style={{ color: '#8c8c8c' }} />;\n      default: return <FileTextOutlined />;\n    }\n  };\n\n  // 模拟审计日志数据\n  const mockAuditLogs: AuditLog[] = [\n    {\n      id: '1',\n      userId: 'user001',\n      userName: '张三',\n      userRole: 'BOM管理员',\n      operation: 'UPDATE',\n      module: 'BOM管理',\n      resourceType: 'CoreBOM',\n      resourceId: 'BOM-001',\n      resourceName: '主控板BOM',\n      oldValue: { version: '1.0', status: 'DRAFT' },\n      newValue: { version: '1.1', status: 'ACTIVE' },\n      reason: '更新产品规格',\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T10:30:00Z',\n      status: 'SUCCESS',\n      riskLevel: 'MEDIUM',\n      description: '更新BOM版本并激活',\n    },\n    {\n      id: '2',\n      userId: 'user002',\n      userName: '李四',\n      userRole: '采购经理',\n      operation: 'DELETE',\n      module: '采购管理',\n      resourceType: 'PurchaseOrder',\n      resourceId: 'PO-2024-001',\n      resourceName: '电子元器件采购订单',\n      reason: '订单取消',\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T09:15:00Z',\n      status: 'SUCCESS',\n      riskLevel: 'HIGH',\n      description: '删除采购订单',\n    },\n    {\n      id: '3',\n      userId: 'user003',\n      userName: '王五',\n      userRole: '系统管理员',\n      operation: 'CREATE',\n      module: '用户管理',\n      resourceType: 'User',\n      resourceId: 'user004',\n      resourceName: '赵六',\n      newValue: { userName: '赵六', role: 'OPERATOR', status: 'ACTIVE' },\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T08:45:00Z',\n      status: 'SUCCESS',\n      riskLevel: 'LOW',\n      description: '创建新用户账户',\n    },\n    {\n      id: '4',\n      userId: 'user001',\n      userName: '张三',\n      userRole: 'BOM管理员',\n      operation: 'LOGIN',\n      module: '认证',\n      resourceType: 'Session',\n      resourceId: 'session-001',\n      resourceName: '用户登录',\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T08:00:00Z',\n      status: 'SUCCESS',\n      riskLevel: 'LOW',\n      description: '用户成功登录系统',\n    },\n    {\n      id: '5',\n      userId: 'unknown',\n      userName: '未知用户',\n      userRole: '',\n      operation: 'LOGIN',\n      module: '认证',\n      resourceType: 'Session',\n      resourceId: 'session-002',\n      resourceName: '登录失败',\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T07:30:00Z',\n      status: 'FAILED',\n      riskLevel: 'CRITICAL',\n      description: '多次登录失败，可能存在安全风险',\n    },\n  ];\n\n  const auditLogColumns = [\n    {\n      title: '时间',\n      dataIndex: 'timestamp',\n      key: 'timestamp',\n      width: 160,\n      render: (timestamp: string) => (\n        <div>\n          <div>{formatDate(timestamp, 'YYYY-MM-DD')}</div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {formatDate(timestamp, 'HH:mm:ss')}\n          </Text>\n        </div>\n      ),\n      sorter: (a: AuditLog, b: AuditLog) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),\n    },\n    {\n      title: '用户',\n      key: 'user',\n      width: 120,\n      render: (_: any, record: AuditLog) => (\n        <div>\n          <div>{record.userName}</div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.userRole}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'operation',\n      width: 120,\n      render: (_: any, record: AuditLog) => (\n        <Space>\n          {getOperationIcon(record.operation)}\n          <span>{record.operation}</span>\n        </Space>\n      ),\n    },\n    {\n      title: '模块',\n      dataIndex: 'module',\n      key: 'module',\n      width: 100,\n    },\n    {\n      title: '资源',\n      key: 'resource',\n      width: 150,\n      render: (_: any, record: AuditLog) => (\n        <div>\n          <div>{record.resourceName}</div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.resourceType}: {record.resourceId}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>{status}</Tag>\n      ),\n    },\n    {\n      title: '风险等级',\n      dataIndex: 'riskLevel',\n      key: 'riskLevel',\n      width: 100,\n      render: (level: string) => (\n        <Tag color={getRiskLevelColor(level)}>{level}</Tag>\n      ),\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ipAddress',\n      key: 'ipAddress',\n      width: 120,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 80,\n      fixed: 'right' as const,\n      render: (_: any, record: AuditLog) => (\n        <Tooltip title=\"查看详情\">\n          <Button\n            type=\"text\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewDetail(record)}\n          />\n        </Tooltip>\n      ),\n    },\n  ];\n\n  const renderStatistics = () => {\n    const totalLogs = mockAuditLogs.length;\n    const successLogs = mockAuditLogs.filter(log => log.status === 'SUCCESS').length;\n    const failedLogs = mockAuditLogs.filter(log => log.status === 'FAILED').length;\n    const highRiskLogs = mockAuditLogs.filter(log => log.riskLevel === 'HIGH' || log.riskLevel === 'CRITICAL').length;\n\n    return (\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"总操作数\"\n              value={totalLogs}\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"成功操作\"\n              value={successLogs}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"失败操作\"\n              value={failedLogs}\n              prefix={<ExclamationCircleOutlined />}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"高风险操作\"\n              value={highRiskLogs}\n              prefix={<SecurityScanOutlined />}\n              valueStyle={{ color: '#d4380d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n    );\n  };\n\n  const renderSecurityAlerts = () => {\n    const criticalLogs = mockAuditLogs.filter(log => log.riskLevel === 'CRITICAL');\n    \n    if (criticalLogs.length === 0) {\n      return null;\n    }\n\n    return (\n      <Alert\n        message=\"安全警告\"\n        description={`检测到 ${criticalLogs.length} 个高风险操作，请及时处理`}\n        type=\"error\"\n        showIcon\n        closable\n        style={{ marginBottom: 16 }}\n        action={\n          <Button size=\"small\" danger>\n            查看详情\n          </Button>\n        }\n      />\n    );\n  };\n\n  const renderAuditTimeline = () => {\n    const timelineItems = mockAuditLogs.slice(0, 10).map(log => ({\n      color: log.status === 'FAILED' ? 'red' : log.riskLevel === 'HIGH' ? 'orange' : 'blue',\n      children: (\n        <div>\n          <div style={{ marginBottom: 4 }}>\n            <Space>\n              {getOperationIcon(log.operation)}\n              <Text strong>{log.userName}</Text>\n              <Text>{log.operation}</Text>\n              <Text type=\"secondary\">{log.resourceName}</Text>\n            </Space>\n          </div>\n          <div style={{ marginBottom: 4 }}>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              {formatDate(log.timestamp)} • {log.ipAddress}\n            </Text>\n          </div>\n          <div>\n            <Tag color={getStatusColor(log.status)}>{log.status}</Tag>\n            <Tag color={getRiskLevelColor(log.riskLevel)}>{log.riskLevel}</Tag>\n          </div>\n        </div>\n      ),\n    }));\n\n    return (\n      <Card title=\"操作时间线\" size=\"small\">\n        <Timeline items={timelineItems} />\n      </Card>\n    );\n  };\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>审计日志</Title>\n            <Text type=\"secondary\">\n              系统操作记录和安全审计跟踪\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索用户、操作、资源\"\n                allowClear\n                style={{ width: 200 }}\n                onSearch={setSearchKeyword}\n              />\n              <Select\n                placeholder=\"用户\"\n                allowClear\n                style={{ width: 100 }}\n                value={userFilter}\n                onChange={setUserFilter}\n                options={[\n                  { label: '张三', value: 'user001' },\n                  { label: '李四', value: 'user002' },\n                  { label: '王五', value: 'user003' },\n                ]}\n              />\n              <Select\n                placeholder=\"模块\"\n                allowClear\n                style={{ width: 100 }}\n                value={moduleFilter}\n                onChange={setModuleFilter}\n                options={[\n                  { label: 'BOM管理', value: 'BOM管理' },\n                  { label: '采购管理', value: '采购管理' },\n                  { label: '用户管理', value: '用户管理' },\n                  { label: '认证', value: '认证' },\n                ]}\n              />\n              <Select\n                placeholder=\"操作类型\"\n                allowClear\n                style={{ width: 100 }}\n                value={operationFilter}\n                onChange={setOperationFilter}\n                options={[\n                  { label: '创建', value: 'CREATE' },\n                  { label: '更新', value: 'UPDATE' },\n                  { label: '删除', value: 'DELETE' },\n                  { label: '登录', value: 'LOGIN' },\n                  { label: '登出', value: 'LOGOUT' },\n                ]}\n              />\n              <Select\n                placeholder=\"状态\"\n                allowClear\n                style={{ width: 100 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '成功', value: 'SUCCESS' },\n                  { label: '失败', value: 'FAILED' },\n                  { label: '警告', value: 'WARNING' },\n                ]}\n              />\n              <RangePicker\n                value={dateRange}\n                onChange={setDateRange}\n                style={{ width: 240 }}\n              />\n              <Button icon={<ExportOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n              <Button icon={<ReloadOutlined />} onClick={loadData}>\n                刷新\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        {renderSecurityAlerts()}\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab=\"审计列表\" key=\"list\">\n            {renderStatistics()}\n            <Table\n              columns={auditLogColumns}\n              dataSource={mockAuditLogs}\n              loading={loading}\n              rowKey=\"id\"\n              scroll={{ x: 1200 }}\n              pagination={{\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) =>\n                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n              }}\n            />\n          </TabPane>\n\n          <TabPane tab=\"操作时间线\" key=\"timeline\">\n            <Row gutter={[16, 16]}>\n              <Col xs={24} lg={16}>\n                {renderAuditTimeline()}\n              </Col>\n              <Col xs={24} lg={8}>\n                {renderStatistics()}\n              </Col>\n            </Row>\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 详情模态框 */}\n      <Modal\n        title=\"审计日志详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={800}\n      >\n        {selectedLog && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"操作时间\">\n              {formatDate(selectedLog.timestamp)}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"操作用户\">\n              {selectedLog.userName} ({selectedLog.userRole})\n            </Descriptions.Item>\n            <Descriptions.Item label=\"操作类型\">\n              <Space>\n                {getOperationIcon(selectedLog.operation)}\n                {selectedLog.operation}\n              </Space>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"所属模块\">\n              {selectedLog.module}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"资源类型\">\n              {selectedLog.resourceType}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"资源ID\">\n              {selectedLog.resourceId}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"资源名称\" span={2}>\n              {selectedLog.resourceName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"操作状态\">\n              <Tag color={getStatusColor(selectedLog.status)}>\n                {selectedLog.status}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"风险等级\">\n              <Tag color={getRiskLevelColor(selectedLog.riskLevel)}>\n                {selectedLog.riskLevel}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"IP地址\">\n              {selectedLog.ipAddress}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"用户代理\" span={2}>\n              <Text code style={{ fontSize: 12 }}>\n                {selectedLog.userAgent}\n              </Text>\n            </Descriptions.Item>\n            {selectedLog.reason && (\n              <Descriptions.Item label=\"操作原因\" span={2}>\n                {selectedLog.reason}\n              </Descriptions.Item>\n            )}\n            <Descriptions.Item label=\"操作描述\" span={2}>\n              {selectedLog.description}\n            </Descriptions.Item>\n            {selectedLog.oldValue && (\n              <Descriptions.Item label=\"修改前\" span={2}>\n                <pre style={{ fontSize: 12, background: '#f5f5f5', padding: 8 }}>\n                  {JSON.stringify(selectedLog.oldValue, null, 2)}\n                </pre>\n              </Descriptions.Item>\n            )}\n            {selectedLog.newValue && (\n              <Descriptions.Item label=\"修改后\" span={2}>\n                <pre style={{ fontSize: 12, background: '#f5f5f5', padding: 8 }}>\n                  {JSON.stringify(selectedLog.newValue, null, 2)}\n                </pre>\n              </Descriptions.Item>\n            )}\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default AuditLogPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,YAAY,EACZC,QAAQ,EACRC,KAAK,EACLC,SAAS,EAETC,IAAI,QACC,MAAM;AACb,SAEEC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,YAAY,EAEZC,gBAAgB,EAChBC,eAAe,EACfC,mBAAmB,EACnBC,yBAAyB,EACzBC,oBAAoB,QAEf,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,UAAU,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG/B,UAAU;AAClC,MAAM;EAAEgC;AAAO,CAAC,GAAG5B,KAAK;AACxB,MAAM;EAAE6B;AAAY,CAAC,GAAGxB,UAAU;AAClC,MAAM;EAAEyB;AAAQ,CAAC,GAAGlB,IAAI;AAuBxB,MAAMmB,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAqB,CAAC;EAClE,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAqB,CAAC;EACtE,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAqB,CAAC;EAC5E,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAqB,CAAC;EACtE,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAqB,CAAC;EAC5E,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAoC,IAAI,CAAC;EACnF,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAkB,IAAI,CAAC;EACrE,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,MAAM,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd6D,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BrB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,IAAIsB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,gBAAgB,GAAIC,MAAgB,IAAK;IAC7CZ,cAAc,CAACY,MAAM,CAAC;IACtBV,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAxD,KAAK,CAACyD,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,QAAQ;QAAE,OAAO,OAAO;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIC,KAAa,IAAK;IAC3C,QAAQA,KAAK;MACX,KAAK,KAAK;QAAE,OAAO,OAAO;MAC1B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,MAAM;QAAE,OAAO,KAAK;MACzB,KAAK,UAAU;QAAE,OAAO,QAAQ;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIC,SAAiB,IAAK;IAC9C,QAAQA,SAAS;MACf,KAAK,QAAQ;QAAE,oBAAO/C,OAAA,CAACN,mBAAmB;UAACsD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1E,KAAK,QAAQ;QAAE,oBAAOrD,OAAA,CAACL,yBAAyB;UAACqD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChF,KAAK,QAAQ;QAAE,oBAAOrD,OAAA,CAACP,eAAe;UAACuD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtE,KAAK,OAAO;QAAE,oBAAOrD,OAAA,CAACT,YAAY;UAACyD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClE,KAAK,QAAQ;QAAE,oBAAOrD,OAAA,CAACT,YAAY;UAACyD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE;QAAS,oBAAOrD,OAAA,CAACR,gBAAgB;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMC,aAAyB,GAAG,CAChC;IACEC,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,QAAQ;IAClBX,SAAS,EAAE,QAAQ;IACnBY,MAAM,EAAE,OAAO;IACfC,YAAY,EAAE,SAAS;IACvBC,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,QAAQ;IACtBC,QAAQ,EAAE;MAAEC,OAAO,EAAE,KAAK;MAAErB,MAAM,EAAE;IAAQ,CAAC;IAC7CsB,QAAQ,EAAE;MAAED,OAAO,EAAE,KAAK;MAAErB,MAAM,EAAE;IAAS,CAAC;IAC9CuB,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,eAAe;IAC1BC,SAAS,EAAE,8DAA8D;IACzEC,SAAS,EAAE,sBAAsB;IACjC1B,MAAM,EAAE,SAAS;IACjB2B,SAAS,EAAE,QAAQ;IACnBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhB,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,MAAM;IAChBX,SAAS,EAAE,QAAQ;IACnBY,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,eAAe;IAC7BC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,WAAW;IACzBI,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,eAAe;IAC1BC,SAAS,EAAE,8DAA8D;IACzEC,SAAS,EAAE,sBAAsB;IACjC1B,MAAM,EAAE,SAAS;IACjB2B,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhB,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,OAAO;IACjBX,SAAS,EAAE,QAAQ;IACnBY,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,IAAI;IAClBG,QAAQ,EAAE;MAAER,QAAQ,EAAE,IAAI;MAAEe,IAAI,EAAE,UAAU;MAAE7B,MAAM,EAAE;IAAS,CAAC;IAChEwB,SAAS,EAAE,eAAe;IAC1BC,SAAS,EAAE,8DAA8D;IACzEC,SAAS,EAAE,sBAAsB;IACjC1B,MAAM,EAAE,SAAS;IACjB2B,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhB,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,QAAQ;IAClBX,SAAS,EAAE,OAAO;IAClBY,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,SAAS;IACvBC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,MAAM;IACpBK,SAAS,EAAE,eAAe;IAC1BC,SAAS,EAAE,8DAA8D;IACzEC,SAAS,EAAE,sBAAsB;IACjC1B,MAAM,EAAE,SAAS;IACjB2B,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhB,EAAE,EAAE,GAAG;IACPC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,EAAE;IACZX,SAAS,EAAE,OAAO;IAClBY,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,SAAS;IACvBC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,MAAM;IACpBK,SAAS,EAAE,eAAe;IAC1BC,SAAS,EAAE,8DAA8D;IACzEC,SAAS,EAAE,sBAAsB;IACjC1B,MAAM,EAAE,QAAQ;IAChB2B,SAAS,EAAE,UAAU;IACrBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAME,eAAe,GAAG,CACtB;IACEjC,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGR,SAAiB,iBACxBrE,OAAA;MAAA8E,QAAA,gBACE9E,OAAA;QAAA8E,QAAA,EAAMhF,UAAU,CAACuE,SAAS,EAAE,YAAY;MAAC;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChDrD,OAAA,CAACE,IAAI;QAAC6E,IAAI,EAAC,WAAW;QAAC/B,KAAK,EAAE;UAAEgC,QAAQ,EAAE;QAAG,CAAE;QAAAF,QAAA,EAC5ChF,UAAU,CAACuE,SAAS,EAAE,UAAU;MAAC;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;IACD4B,MAAM,EAAEA,CAACC,CAAW,EAAEC,CAAW,KAAKtF,KAAK,CAACqF,CAAC,CAACb,SAAS,CAAC,CAACe,IAAI,CAAC,CAAC,GAAGvF,KAAK,CAACsF,CAAC,CAACd,SAAS,CAAC,CAACe,IAAI,CAAC;EAC5F,CAAC,EACD;IACE5C,KAAK,EAAE,IAAI;IACXmC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACQ,CAAM,EAAEhD,MAAgB,kBAC/BrC,OAAA;MAAA8E,QAAA,gBACE9E,OAAA;QAAA8E,QAAA,EAAMzC,MAAM,CAACoB;MAAQ;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5BrD,OAAA,CAACE,IAAI;QAAC6E,IAAI,EAAC,WAAW;QAAC/B,KAAK,EAAE;UAAEgC,QAAQ,EAAE;QAAG,CAAE;QAAAF,QAAA,EAC5CzC,MAAM,CAACqB;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXmC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACQ,CAAM,EAAEhD,MAAgB,kBAC/BrC,OAAA,CAAC1B,KAAK;MAAAwG,QAAA,GACHhC,gBAAgB,CAACT,MAAM,CAACU,SAAS,CAAC,eACnC/C,OAAA;QAAA8E,QAAA,EAAOzC,MAAM,CAACU;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B;EAEX,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACEpC,KAAK,EAAE,IAAI;IACXmC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACQ,CAAM,EAAEhD,MAAgB,kBAC/BrC,OAAA;MAAA8E,QAAA,gBACE9E,OAAA;QAAA8E,QAAA,EAAMzC,MAAM,CAACyB;MAAY;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChCrD,OAAA,CAACE,IAAI;QAAC6E,IAAI,EAAC,WAAW;QAAC/B,KAAK,EAAE;UAAEgC,QAAQ,EAAE;QAAG,CAAE;QAAAF,QAAA,GAC5CzC,MAAM,CAACuB,YAAY,EAAC,IAAE,EAACvB,MAAM,CAACwB,UAAU;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGlC,MAAc,iBACrB3C,OAAA,CAACrB,GAAG;MAACsE,KAAK,EAAEP,cAAc,CAACC,MAAM,CAAE;MAAAmC,QAAA,EAAEnC;IAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAErD,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbkC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGhC,KAAa,iBACpB7C,OAAA,CAACrB,GAAG;MAACsE,KAAK,EAAEL,iBAAiB,CAACC,KAAK,CAAE;MAAAiC,QAAA,EAAEjC;IAAK;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEtD,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbkC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEpC,KAAK,EAAE,IAAI;IACXmC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTU,KAAK,EAAE,OAAgB;IACvBT,MAAM,EAAEA,CAACQ,CAAM,EAAEhD,MAAgB,kBAC/BrC,OAAA,CAACnB,OAAO;MAAC2D,KAAK,EAAC,0BAAM;MAAAsC,QAAA,eACnB9E,OAAA,CAAC3B,MAAM;QACL0G,IAAI,EAAC,MAAM;QACXQ,IAAI,eAAEvF,OAAA,CAACZ,WAAW;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBmC,OAAO,EAAEA,CAAA,KAAMpD,gBAAgB,CAACC,MAAM;MAAE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAEb,CAAC,CACF;EAED,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAGpC,aAAa,CAACqC,MAAM;IACtC,MAAMC,WAAW,GAAGtC,aAAa,CAACuC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACnD,MAAM,KAAK,SAAS,CAAC,CAACgD,MAAM;IAChF,MAAMI,UAAU,GAAGzC,aAAa,CAACuC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACnD,MAAM,KAAK,QAAQ,CAAC,CAACgD,MAAM;IAC9E,MAAMK,YAAY,GAAG1C,aAAa,CAACuC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACxB,SAAS,KAAK,MAAM,IAAIwB,GAAG,CAACxB,SAAS,KAAK,UAAU,CAAC,CAACqB,MAAM;IAEjH,oBACE3F,OAAA,CAACvB,GAAG;MAACwH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACjD,KAAK,EAAE;QAAEkD,YAAY,EAAE;MAAG,CAAE;MAAApB,QAAA,gBACjD9E,OAAA,CAACtB,GAAG;QAACyH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACjB9E,OAAA,CAAC9B,IAAI;UAACmI,IAAI,EAAC,OAAO;UAAAvB,QAAA,eAChB9E,OAAA,CAACd,SAAS;YACRsD,KAAK,EAAC,0BAAM;YACZ8D,KAAK,EAAEZ,SAAU;YACjBa,MAAM,eAAEvG,OAAA,CAACR,gBAAgB;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrD,OAAA,CAACtB,GAAG;QAACyH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACjB9E,OAAA,CAAC9B,IAAI;UAACmI,IAAI,EAAC,OAAO;UAAAvB,QAAA,eAChB9E,OAAA,CAACd,SAAS;YACRsD,KAAK,EAAC,0BAAM;YACZ8D,KAAK,EAAEV,WAAY;YACnBW,MAAM,eAAEvG,OAAA,CAACN,mBAAmB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCmD,UAAU,EAAE;cAAEvD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrD,OAAA,CAACtB,GAAG;QAACyH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACjB9E,OAAA,CAAC9B,IAAI;UAACmI,IAAI,EAAC,OAAO;UAAAvB,QAAA,eAChB9E,OAAA,CAACd,SAAS;YACRsD,KAAK,EAAC,0BAAM;YACZ8D,KAAK,EAAEP,UAAW;YAClBQ,MAAM,eAAEvG,OAAA,CAACL,yBAAyB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtCmD,UAAU,EAAE;cAAEvD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrD,OAAA,CAACtB,GAAG;QAACyH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACjB9E,OAAA,CAAC9B,IAAI;UAACmI,IAAI,EAAC,OAAO;UAAAvB,QAAA,eAChB9E,OAAA,CAACd,SAAS;YACRsD,KAAK,EAAC,gCAAO;YACb8D,KAAK,EAAEN,YAAa;YACpBO,MAAM,eAAEvG,OAAA,CAACJ,oBAAoB;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACjCmD,UAAU,EAAE;cAAEvD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMoD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,YAAY,GAAGpD,aAAa,CAACuC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACxB,SAAS,KAAK,UAAU,CAAC;IAE9E,IAAIoC,YAAY,CAACf,MAAM,KAAK,CAAC,EAAE;MAC7B,OAAO,IAAI;IACb;IAEA,oBACE3F,OAAA,CAACf,KAAK;MACJ0H,OAAO,EAAC,0BAAM;MACdpC,WAAW,EAAE,OAAOmC,YAAY,CAACf,MAAM,eAAgB;MACvDZ,IAAI,EAAC,OAAO;MACZ6B,QAAQ;MACRC,QAAQ;MACR7D,KAAK,EAAE;QAAEkD,YAAY,EAAE;MAAG,CAAE;MAC5BY,MAAM,eACJ9G,OAAA,CAAC3B,MAAM;QAACgI,IAAI,EAAC,OAAO;QAACU,MAAM;QAAAjC,QAAA,EAAC;MAE5B;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEN,CAAC;EAED,MAAM2D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,aAAa,GAAG3D,aAAa,CAAC4D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAACrB,GAAG,KAAK;MAC3D7C,KAAK,EAAE6C,GAAG,CAACnD,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAGmD,GAAG,CAACxB,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,MAAM;MACrFQ,QAAQ,eACN9E,OAAA;QAAA8E,QAAA,gBACE9E,OAAA;UAAKgD,KAAK,EAAE;YAAEkD,YAAY,EAAE;UAAE,CAAE;UAAApB,QAAA,eAC9B9E,OAAA,CAAC1B,KAAK;YAAAwG,QAAA,GACHhC,gBAAgB,CAACgD,GAAG,CAAC/C,SAAS,CAAC,eAChC/C,OAAA,CAACE,IAAI;cAACkH,MAAM;cAAAtC,QAAA,EAAEgB,GAAG,CAACrC;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClCrD,OAAA,CAACE,IAAI;cAAA4E,QAAA,EAAEgB,GAAG,CAAC/C;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5BrD,OAAA,CAACE,IAAI;cAAC6E,IAAI,EAAC,WAAW;cAAAD,QAAA,EAAEgB,GAAG,CAAChC;YAAY;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNrD,OAAA;UAAKgD,KAAK,EAAE;YAAEkD,YAAY,EAAE;UAAE,CAAE;UAAApB,QAAA,eAC9B9E,OAAA,CAACE,IAAI;YAAC6E,IAAI,EAAC,WAAW;YAAC/B,KAAK,EAAE;cAAEgC,QAAQ,EAAE;YAAG,CAAE;YAAAF,QAAA,GAC5ChF,UAAU,CAACgG,GAAG,CAACzB,SAAS,CAAC,EAAC,UAAG,EAACyB,GAAG,CAAC3B,SAAS;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNrD,OAAA;UAAA8E,QAAA,gBACE9E,OAAA,CAACrB,GAAG;YAACsE,KAAK,EAAEP,cAAc,CAACoD,GAAG,CAACnD,MAAM,CAAE;YAAAmC,QAAA,EAAEgB,GAAG,CAACnD;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DrD,OAAA,CAACrB,GAAG;YAACsE,KAAK,EAAEL,iBAAiB,CAACkD,GAAG,CAACxB,SAAS,CAAE;YAAAQ,QAAA,EAAEgB,GAAG,CAACxB;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAET,CAAC,CAAC,CAAC;IAEH,oBACErD,OAAA,CAAC9B,IAAI;MAACsE,KAAK,EAAC,gCAAO;MAAC6D,IAAI,EAAC,OAAO;MAAAvB,QAAA,eAC9B9E,OAAA,CAAChB,QAAQ;QAACqI,KAAK,EAAEJ;MAAc;QAAA/D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEX,CAAC;EAED,oBACErD,OAAA;IAAA8E,QAAA,gBACE9E,OAAA,CAAC9B,IAAI;MAAA4G,QAAA,gBACH9E,OAAA,CAACvB,GAAG;QAAC6I,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACvE,KAAK,EAAE;UAAEkD,YAAY,EAAE;QAAG,CAAE;QAAApB,QAAA,gBACtE9E,OAAA,CAACtB,GAAG;UAAAoG,QAAA,gBACF9E,OAAA,CAACC,KAAK;YAAC4C,KAAK,EAAE,CAAE;YAACG,KAAK,EAAE;cAAEwE,MAAM,EAAE;YAAE,CAAE;YAAA1C,QAAA,EAAC;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDrD,OAAA,CAACE,IAAI;YAAC6E,IAAI,EAAC,WAAW;YAAAD,QAAA,EAAC;UAEvB;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNrD,OAAA,CAACtB,GAAG;UAAAoG,QAAA,eACF9E,OAAA,CAAC1B,KAAK;YAAAwG,QAAA,gBACJ9E,OAAA,CAACG,MAAM;cACLsH,WAAW,EAAC,8DAAY;cACxBC,UAAU;cACV1E,KAAK,EAAE;gBAAE4B,KAAK,EAAE;cAAI,CAAE;cACtB+C,QAAQ,EAAEhH;YAAiB;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFrD,OAAA,CAACxB,MAAM;cACLiJ,WAAW,EAAC,cAAI;cAChBC,UAAU;cACV1E,KAAK,EAAE;gBAAE4B,KAAK,EAAE;cAAI,CAAE;cACtB0B,KAAK,EAAE1F,UAAW;cAClBgH,QAAQ,EAAE/G,aAAc;cACxBgH,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAU,CAAC,EACjC;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAU,CAAC,EACjC;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAU,CAAC;YACjC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrD,OAAA,CAACxB,MAAM;cACLiJ,WAAW,EAAC,cAAI;cAChBC,UAAU;cACV1E,KAAK,EAAE;gBAAE4B,KAAK,EAAE;cAAI,CAAE;cACtB0B,KAAK,EAAExF,YAAa;cACpB8G,QAAQ,EAAE7G,eAAgB;cAC1B8G,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,OAAO;gBAAExB,KAAK,EAAE;cAAQ,CAAC,EAClC;gBAAEwB,KAAK,EAAE,MAAM;gBAAExB,KAAK,EAAE;cAAO,CAAC,EAChC;gBAAEwB,KAAK,EAAE,MAAM;gBAAExB,KAAK,EAAE;cAAO,CAAC,EAChC;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAK,CAAC;YAC5B;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrD,OAAA,CAACxB,MAAM;cACLiJ,WAAW,EAAC,0BAAM;cAClBC,UAAU;cACV1E,KAAK,EAAE;gBAAE4B,KAAK,EAAE;cAAI,CAAE;cACtB0B,KAAK,EAAEtF,eAAgB;cACvB4G,QAAQ,EAAE3G,kBAAmB;cAC7B4G,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAS,CAAC,EAChC;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAS,CAAC,EAChC;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAS,CAAC,EAChC;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAQ,CAAC,EAC/B;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAS,CAAC;YAChC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrD,OAAA,CAACxB,MAAM;cACLiJ,WAAW,EAAC,cAAI;cAChBC,UAAU;cACV1E,KAAK,EAAE;gBAAE4B,KAAK,EAAE;cAAI,CAAE;cACtB0B,KAAK,EAAEpF,YAAa;cACpB0G,QAAQ,EAAEzG,eAAgB;cAC1B0G,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAU,CAAC,EACjC;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAS,CAAC,EAChC;gBAAEwB,KAAK,EAAE,IAAI;gBAAExB,KAAK,EAAE;cAAU,CAAC;YACjC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrD,OAAA,CAACI,WAAW;cACVkG,KAAK,EAAEhF,SAAU;cACjBsG,QAAQ,EAAErG,YAAa;cACvByB,KAAK,EAAE;gBAAE4B,KAAK,EAAE;cAAI;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFrD,OAAA,CAAC3B,MAAM;cAACkH,IAAI,eAAEvF,OAAA,CAACX,cAAc;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACmC,OAAO,EAAElD,YAAa;cAAAwC,QAAA,EAAC;YAEzD;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrD,OAAA,CAAC3B,MAAM;cAACkH,IAAI,eAAEvF,OAAA,CAACV,cAAc;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACmC,OAAO,EAAE1D,QAAS;cAAAgD,QAAA,EAAC;YAErD;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELoD,oBAAoB,CAAC,CAAC,eAEvBzG,OAAA,CAACb,IAAI;QAAC4I,SAAS,EAAEnG,SAAU;QAACgG,QAAQ,EAAE/F,YAAa;QAAAiD,QAAA,gBACjD9E,OAAA,CAACK,OAAO;UAAC2H,GAAG,EAAC,0BAAM;UAAAlD,QAAA,GAChBW,gBAAgB,CAAC,CAAC,eACnBzF,OAAA,CAAC5B,KAAK;YACJ6J,OAAO,EAAExD,eAAgB;YACzByD,UAAU,EAAE5E,aAAc;YAC1B9C,OAAO,EAAEA,OAAQ;YACjB2H,MAAM,EAAC,IAAI;YACXC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAK,CAAE;YACpBC,UAAU,EAAE;cACVC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;YAC1C;UAAE;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAdoB,MAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAerB,CAAC,eAEVrD,OAAA,CAACK,OAAO;UAAC2H,GAAG,EAAC,gCAAO;UAAAlD,QAAA,eAClB9E,OAAA,CAACvB,GAAG;YAACwH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAAnB,QAAA,gBACpB9E,OAAA,CAACtB,GAAG;cAACyH,EAAE,EAAE,EAAG;cAACyC,EAAE,EAAE,EAAG;cAAA9D,QAAA,EACjBkC,mBAAmB,CAAC;YAAC;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNrD,OAAA,CAACtB,GAAG;cAACyH,EAAE,EAAE,EAAG;cAACyC,EAAE,EAAE,CAAE;cAAA9D,QAAA,EAChBW,gBAAgB,CAAC;YAAC;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GARiB,UAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAS1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPrD,OAAA,CAAClB,KAAK;MACJ0D,KAAK,EAAC,sCAAQ;MACdqG,IAAI,EAAEnH,kBAAmB;MACzBoH,QAAQ,EAAEA,CAAA,KAAMnH,qBAAqB,CAAC,KAAK,CAAE;MAC7CoH,MAAM,EAAE,cACN/I,OAAA,CAAC3B,MAAM;QAAamH,OAAO,EAAEA,CAAA,KAAM7D,qBAAqB,CAAC,KAAK,CAAE;QAAAmD,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFuB,KAAK,EAAE,GAAI;MAAAE,QAAA,EAEVtD,WAAW,iBACVxB,OAAA,CAACjB,YAAY;QAACiK,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAAnE,QAAA,gBAC/B9E,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAAAhD,QAAA,EAC5BhF,UAAU,CAAC0B,WAAW,CAAC6C,SAAS;QAAC;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAAAhD,QAAA,GAC5BtD,WAAW,CAACiC,QAAQ,EAAC,IAAE,EAACjC,WAAW,CAACkC,QAAQ,EAAC,GAChD;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAAAhD,QAAA,eAC7B9E,OAAA,CAAC1B,KAAK;YAAAwG,QAAA,GACHhC,gBAAgB,CAACtB,WAAW,CAACuB,SAAS,CAAC,EACvCvB,WAAW,CAACuB,SAAS;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAAAhD,QAAA,EAC5BtD,WAAW,CAACmC;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAAAhD,QAAA,EAC5BtD,WAAW,CAACoC;QAAY;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,gBAAM;UAAAhD,QAAA,EAC5BtD,WAAW,CAACqC;QAAU;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAACqB,IAAI,EAAE,CAAE;UAAArE,QAAA,EACrCtD,WAAW,CAACsC;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAAAhD,QAAA,eAC7B9E,OAAA,CAACrB,GAAG;YAACsE,KAAK,EAAEP,cAAc,CAAClB,WAAW,CAACmB,MAAM,CAAE;YAAAmC,QAAA,EAC5CtD,WAAW,CAACmB;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAAAhD,QAAA,eAC7B9E,OAAA,CAACrB,GAAG;YAACsE,KAAK,EAAEL,iBAAiB,CAACpB,WAAW,CAAC8C,SAAS,CAAE;YAAAQ,QAAA,EAClDtD,WAAW,CAAC8C;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,gBAAM;UAAAhD,QAAA,EAC5BtD,WAAW,CAAC2C;QAAS;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACpBrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAACqB,IAAI,EAAE,CAAE;UAAArE,QAAA,eACtC9E,OAAA,CAACE,IAAI;YAACkJ,IAAI;YAACpG,KAAK,EAAE;cAAEgC,QAAQ,EAAE;YAAG,CAAE;YAAAF,QAAA,EAChCtD,WAAW,CAAC4C;UAAS;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,EACnB7B,WAAW,CAAC0C,MAAM,iBACjBlE,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAACqB,IAAI,EAAE,CAAE;UAAArE,QAAA,EACrCtD,WAAW,CAAC0C;QAAM;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACpB,eACDrD,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,0BAAM;UAACqB,IAAI,EAAE,CAAE;UAAArE,QAAA,EACrCtD,WAAW,CAAC+C;QAAW;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EACnB7B,WAAW,CAACuC,QAAQ,iBACnB/D,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,oBAAK;UAACqB,IAAI,EAAE,CAAE;UAAArE,QAAA,eACrC9E,OAAA;YAAKgD,KAAK,EAAE;cAAEgC,QAAQ,EAAE,EAAE;cAAEqE,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE;YAAE,CAAE;YAAAxE,QAAA,EAC7DyE,IAAI,CAACC,SAAS,CAAChI,WAAW,CAACuC,QAAQ,EAAE,IAAI,EAAE,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CACpB,EACA7B,WAAW,CAACyC,QAAQ,iBACnBjE,OAAA,CAACjB,YAAY,CAACmK,IAAI;UAACpB,KAAK,EAAC,oBAAK;UAACqB,IAAI,EAAE,CAAE;UAAArE,QAAA,eACrC9E,OAAA;YAAKgD,KAAK,EAAE;cAAEgC,QAAQ,EAAE,EAAE;cAAEqE,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE;YAAE,CAAE;YAAAxE,QAAA,EAC7DyE,IAAI,CAACC,SAAS,CAAChI,WAAW,CAACyC,QAAQ,EAAE,IAAI,EAAE,CAAC;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CACpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA9jBID,YAAsB;AAAAmJ,EAAA,GAAtBnJ,YAAsB;AAgkB5B,eAAeA,YAAY;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}