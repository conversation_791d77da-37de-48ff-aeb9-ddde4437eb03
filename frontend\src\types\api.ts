// API相关类型定义

export interface FetchParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  code: number;
  timestamp?: string;
}

export interface ListResponse<T> extends ApiResponse<PaginatedResponse<T>> {}

export interface CreateResponse<T> extends ApiResponse<T> {}

export interface UpdateResponse<T> extends ApiResponse<T> {}

export interface DeleteResponse extends ApiResponse<boolean> {}