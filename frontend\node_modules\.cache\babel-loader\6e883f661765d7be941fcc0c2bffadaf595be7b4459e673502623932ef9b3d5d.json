{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Typography,Table,Button,Space,Input,Select,Row,Col,Tag,DatePicker,Tooltip,Modal,Descriptions,Timeline,Alert,Statistic,Tabs}from'antd';import{EyeOutlined,ExportOutlined,ReloadOutlined,UserOutlined,FileTextOutlined,WarningOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SecurityScanOutlined}from'@ant-design/icons';import dayjs from'dayjs';import{formatDate}from'../../utils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Search}=Input;const{RangePicker}=DatePicker;const{TabPane}=Tabs;const AuditLogPage=()=>{const[loading,setLoading]=useState(false);const[userFilter,setUserFilter]=useState();const[moduleFilter,setModuleFilter]=useState();const[operationFilter,setOperationFilter]=useState();const[statusFilter,setStatusFilter]=useState();const[dateRange,setDateRange]=useState(null);const handleDateRangeChange=(dates,dateStrings)=>{setDateRange(dates);};const[selectedLog,setSelectedLog]=useState(null);const[detailModalVisible,setDetailModalVisible]=useState(false);const[activeTab,setActiveTab]=useState('list');useEffect(()=>{loadData();},[]);const loadData=async()=>{setLoading(true);try{// TODO: 调用API获取审计日志数据\nawait new Promise(resolve=>setTimeout(resolve,1000));}catch(error){console.error('加载审计日志失败:',error);}finally{setLoading(false);}};const handleViewDetail=record=>{setSelectedLog(record);setDetailModalVisible(true);};const handleExport=()=>{// TODO: 实现导出功能\nModal.success({title:'导出成功',content:'审计日志已导出到Excel文件'});};const getStatusColor=status=>{switch(status){case'SUCCESS':return'success';case'FAILED':return'error';case'WARNING':return'warning';default:return'default';}};const getRiskLevelColor=level=>{switch(level){case'LOW':return'green';case'MEDIUM':return'orange';case'HIGH':return'red';case'CRITICAL':return'purple';default:return'default';}};const getOperationIcon=operation=>{switch(operation){case'CREATE':return/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'#52c41a'}});case'UPDATE':return/*#__PURE__*/_jsx(ExclamationCircleOutlined,{style:{color:'#faad14'}});case'DELETE':return/*#__PURE__*/_jsx(WarningOutlined,{style:{color:'#f5222d'}});case'LOGIN':return/*#__PURE__*/_jsx(UserOutlined,{style:{color:'#1890ff'}});case'LOGOUT':return/*#__PURE__*/_jsx(UserOutlined,{style:{color:'#8c8c8c'}});default:return/*#__PURE__*/_jsx(FileTextOutlined,{});}};// 模拟审计日志数据\nconst mockAuditLogs=[{id:'1',userId:'user001',userName:'张三',userRole:'BOM管理员',operation:'UPDATE',module:'BOM管理',resourceType:'CoreBOM',resourceId:'BOM-001',resourceName:'主控板BOM',oldValue:{version:'1.0',status:'DRAFT'},newValue:{version:'1.1',status:'ACTIVE'},reason:'更新产品规格',ipAddress:'*************',userAgent:'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',timestamp:'2024-03-25T10:30:00Z',status:'SUCCESS',riskLevel:'MEDIUM',description:'更新BOM版本并激活'},{id:'2',userId:'user002',userName:'李四',userRole:'采购经理',operation:'DELETE',module:'采购管理',resourceType:'PurchaseOrder',resourceId:'PO-2024-001',resourceName:'电子元器件采购订单',reason:'订单取消',ipAddress:'*************',userAgent:'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',timestamp:'2024-03-25T09:15:00Z',status:'SUCCESS',riskLevel:'HIGH',description:'删除采购订单'},{id:'3',userId:'user003',userName:'王五',userRole:'系统管理员',operation:'CREATE',module:'用户管理',resourceType:'User',resourceId:'user004',resourceName:'赵六',newValue:{userName:'赵六',role:'OPERATOR',status:'ACTIVE'},ipAddress:'*************',userAgent:'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',timestamp:'2024-03-25T08:45:00Z',status:'SUCCESS',riskLevel:'LOW',description:'创建新用户账户'},{id:'4',userId:'user001',userName:'张三',userRole:'BOM管理员',operation:'LOGIN',module:'认证',resourceType:'Session',resourceId:'session-001',resourceName:'用户登录',ipAddress:'*************',userAgent:'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',timestamp:'2024-03-25T08:00:00Z',status:'SUCCESS',riskLevel:'LOW',description:'用户成功登录系统'},{id:'5',userId:'unknown',userName:'未知用户',userRole:'',operation:'LOGIN',module:'认证',resourceType:'Session',resourceId:'session-002',resourceName:'登录失败',ipAddress:'*************',userAgent:'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',timestamp:'2024-03-25T07:30:00Z',status:'FAILED',riskLevel:'CRITICAL',description:'多次登录失败，可能存在安全风险'}];const auditLogColumns=[{title:'时间',dataIndex:'timestamp',key:'timestamp',width:160,render:timestamp=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:formatDate(timestamp,'YYYY-MM-DD')}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:formatDate(timestamp,'HH:mm:ss')})]}),sorter:(a,b)=>dayjs(a.timestamp).unix()-dayjs(b.timestamp).unix()},{title:'用户',key:'user',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:record.userName}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:record.userRole})]})},{title:'操作',key:'operation',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{children:[getOperationIcon(record.operation),/*#__PURE__*/_jsx(\"span\",{children:record.operation})]})},{title:'模块',dataIndex:'module',key:'module',width:100},{title:'资源',key:'resource',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:record.resourceName}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:12},children:[record.resourceType,\": \",record.resourceId]})]})},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>/*#__PURE__*/_jsx(Tag,{color:getStatusColor(status),children:status})},{title:'风险等级',dataIndex:'riskLevel',key:'riskLevel',width:100,render:level=>/*#__PURE__*/_jsx(Tag,{color:getRiskLevelColor(level),children:level})},{title:'IP地址',dataIndex:'ipAddress',key:'ipAddress',width:120},{title:'操作',key:'action',width:80,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\\u8BE6\\u60C5\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewDetail(record)})})}];const renderStatistics=()=>{const totalLogs=mockAuditLogs.length;const successLogs=mockAuditLogs.filter(log=>log.status==='SUCCESS').length;const failedLogs=mockAuditLogs.filter(log=>log.status==='FAILED').length;const highRiskLogs=mockAuditLogs.filter(log=>log.riskLevel==='HIGH'||log.riskLevel==='CRITICAL').length;return/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u64CD\\u4F5C\\u6570\",value:totalLogs,prefix:/*#__PURE__*/_jsx(FileTextOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6210\\u529F\\u64CD\\u4F5C\",value:successLogs,prefix:/*#__PURE__*/_jsx(CheckCircleOutlined,{}),valueStyle:{color:'#3f8600'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5931\\u8D25\\u64CD\\u4F5C\",value:failedLogs,prefix:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{}),valueStyle:{color:'#cf1322'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u9AD8\\u98CE\\u9669\\u64CD\\u4F5C\",value:highRiskLogs,prefix:/*#__PURE__*/_jsx(SecurityScanOutlined,{}),valueStyle:{color:'#d4380d'}})})})]});};const renderSecurityAlerts=()=>{const criticalLogs=mockAuditLogs.filter(log=>log.riskLevel==='CRITICAL');if(criticalLogs.length===0){return null;}return/*#__PURE__*/_jsx(Alert,{message:\"\\u5B89\\u5168\\u8B66\\u544A\",description:\"\\u68C0\\u6D4B\\u5230 \".concat(criticalLogs.length,\" \\u4E2A\\u9AD8\\u98CE\\u9669\\u64CD\\u4F5C\\uFF0C\\u8BF7\\u53CA\\u65F6\\u5904\\u7406\"),type:\"error\",showIcon:true,closable:true,style:{marginBottom:16},action:/*#__PURE__*/_jsx(Button,{size:\"small\",danger:true,children:\"\\u67E5\\u770B\\u8BE6\\u60C5\"})});};const renderAuditTimeline=()=>{const timelineItems=mockAuditLogs.slice(0,10).map(log=>({color:log.status==='FAILED'?'red':log.riskLevel==='HIGH'?'orange':'blue',children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:4},children:/*#__PURE__*/_jsxs(Space,{children:[getOperationIcon(log.operation),/*#__PURE__*/_jsx(Text,{strong:true,children:log.userName}),/*#__PURE__*/_jsx(Text,{children:log.operation}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:log.resourceName})]})}),/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:4},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:12},children:[formatDate(log.timestamp),\" \\u2022 \",log.ipAddress]})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Tag,{color:getStatusColor(log.status),children:log.status}),/*#__PURE__*/_jsx(Tag,{color:getRiskLevelColor(log.riskLevel),children:log.riskLevel})]})]})}));return/*#__PURE__*/_jsx(Card,{title:\"\\u64CD\\u4F5C\\u65F6\\u95F4\\u7EBF\",size:\"small\",children:/*#__PURE__*/_jsx(Timeline,{items:timelineItems})});};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u5BA1\\u8BA1\\u65E5\\u5FD7\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u7CFB\\u7EDF\\u64CD\\u4F5C\\u8BB0\\u5F55\\u548C\\u5B89\\u5168\\u5BA1\\u8BA1\\u8DDF\\u8E2A\"})]}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u7528\\u6237\\u3001\\u64CD\\u4F5C\\u3001\\u8D44\\u6E90\",allowClear:true,style:{width:200},onSearch:value=>{// TODO: 实现搜索功能\nconsole.log('搜索:',value);}}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u7528\\u6237\",allowClear:true,style:{width:100},value:userFilter,onChange:setUserFilter,options:[{label:'张三',value:'user001'},{label:'李四',value:'user002'},{label:'王五',value:'user003'}]}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u6A21\\u5757\",allowClear:true,style:{width:100},value:moduleFilter,onChange:setModuleFilter,options:[{label:'BOM管理',value:'BOM管理'},{label:'采购管理',value:'采购管理'},{label:'用户管理',value:'用户管理'},{label:'认证',value:'认证'}]}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u64CD\\u4F5C\\u7C7B\\u578B\",allowClear:true,style:{width:100},value:operationFilter,onChange:setOperationFilter,options:[{label:'创建',value:'CREATE'},{label:'更新',value:'UPDATE'},{label:'删除',value:'DELETE'},{label:'登录',value:'LOGIN'},{label:'登出',value:'LOGOUT'}]}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u72B6\\u6001\",allowClear:true,style:{width:100},value:statusFilter,onChange:setStatusFilter,options:[{label:'成功',value:'SUCCESS'},{label:'失败',value:'FAILED'},{label:'警告',value:'WARNING'}]}),/*#__PURE__*/_jsx(RangePicker,{value:dateRange,onChange:handleDateRangeChange,style:{width:240}}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:loadData,children:\"\\u5237\\u65B0\"})]})})]}),renderSecurityAlerts(),/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:[/*#__PURE__*/_jsxs(TabPane,{tab:\"\\u5BA1\\u8BA1\\u5217\\u8868\",children:[renderStatistics(),/*#__PURE__*/_jsx(Table,{columns:auditLogColumns,dataSource:mockAuditLogs,loading:loading,rowKey:\"id\",scroll:{x:1200},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]},\"list\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u64CD\\u4F5C\\u65F6\\u95F4\\u7EBF\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:16,children:renderAuditTimeline()}),/*#__PURE__*/_jsx(Col,{xs:24,lg:8,children:renderStatistics()})]})},\"timeline\")]})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5BA1\\u8BA1\\u65E5\\u5FD7\\u8BE6\\u60C5\",open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:selectedLog&&/*#__PURE__*/_jsxs(Descriptions,{column:2,bordered:true,children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u64CD\\u4F5C\\u65F6\\u95F4\",children:formatDate(selectedLog.timestamp)}),/*#__PURE__*/_jsxs(Descriptions.Item,{label:\"\\u64CD\\u4F5C\\u7528\\u6237\",children:[selectedLog.userName,\" (\",selectedLog.userRole,\")\"]}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u64CD\\u4F5C\\u7C7B\\u578B\",children:/*#__PURE__*/_jsxs(Space,{children:[getOperationIcon(selectedLog.operation),selectedLog.operation]})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6240\\u5C5E\\u6A21\\u5757\",children:selectedLog.module}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D44\\u6E90\\u7C7B\\u578B\",children:selectedLog.resourceType}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D44\\u6E90ID\",children:selectedLog.resourceId}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D44\\u6E90\\u540D\\u79F0\",span:2,children:selectedLog.resourceName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u64CD\\u4F5C\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Tag,{color:getStatusColor(selectedLog.status),children:selectedLog.status})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u98CE\\u9669\\u7B49\\u7EA7\",children:/*#__PURE__*/_jsx(Tag,{color:getRiskLevelColor(selectedLog.riskLevel),children:selectedLog.riskLevel})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"IP\\u5730\\u5740\",children:selectedLog.ipAddress}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7528\\u6237\\u4EE3\\u7406\",span:2,children:/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:12},children:selectedLog.userAgent})}),selectedLog.reason&&/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u64CD\\u4F5C\\u539F\\u56E0\",span:2,children:selectedLog.reason}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u64CD\\u4F5C\\u63CF\\u8FF0\",span:2,children:selectedLog.description}),selectedLog.oldValue&&/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4FEE\\u6539\\u524D\",span:2,children:/*#__PURE__*/_jsx(\"pre\",{style:{fontSize:12,background:'#f5f5f5',padding:8},children:JSON.stringify(selectedLog.oldValue,null,2)})}),selectedLog.newValue&&/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4FEE\\u6539\\u540E\",span:2,children:/*#__PURE__*/_jsx(\"pre\",{style:{fontSize:12,background:'#f5f5f5',padding:8},children:JSON.stringify(selectedLog.newValue,null,2)})})]})})]});};export default AuditLogPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Row", "Col", "Tag", "DatePicker", "<PERSON><PERSON><PERSON>", "Modal", "Descriptions", "Timeline", "<PERSON><PERSON>", "Statistic", "Tabs", "EyeOutlined", "ExportOutlined", "ReloadOutlined", "UserOutlined", "FileTextOutlined", "WarningOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "SecurityScanOutlined", "dayjs", "formatDate", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Search", "RangePicker", "TabPane", "AuditLogPage", "loading", "setLoading", "userFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "moduleFilter", "setModuleFilter", "operationFilter", "setOperation<PERSON><PERSON>er", "statusFilter", "setStatus<PERSON>ilter", "date<PERSON><PERSON><PERSON>", "setDateRange", "handleDateRangeChange", "dates", "dateStrings", "<PERSON><PERSON><PERSON>", "setSelectedLog", "detailModalVisible", "setDetailModalVisible", "activeTab", "setActiveTab", "loadData", "Promise", "resolve", "setTimeout", "error", "console", "handleViewDetail", "record", "handleExport", "success", "title", "content", "getStatusColor", "status", "getRiskLevelColor", "level", "getOperationIcon", "operation", "style", "color", "mockAuditLogs", "id", "userId", "userName", "userRole", "module", "resourceType", "resourceId", "resourceName", "oldValue", "version", "newValue", "reason", "ip<PERSON><PERSON><PERSON>", "userAgent", "timestamp", "riskLevel", "description", "role", "auditLogColumns", "dataIndex", "key", "width", "render", "children", "type", "fontSize", "sorter", "a", "b", "unix", "_", "fixed", "icon", "onClick", "renderStatistics", "totalLogs", "length", "successLogs", "filter", "log", "failedLogs", "highRiskLogs", "gutter", "marginBottom", "xs", "sm", "size", "value", "prefix", "valueStyle", "renderSecurityAlerts", "criticalLogs", "message", "concat", "showIcon", "closable", "action", "danger", "renderAuditTimeline", "timelineItems", "slice", "map", "strong", "items", "justify", "align", "margin", "placeholder", "allowClear", "onSearch", "onChange", "options", "label", "active<PERSON><PERSON>", "tab", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "lg", "open", "onCancel", "footer", "column", "bordered", "<PERSON><PERSON>", "span", "code", "background", "padding", "JSON", "stringify"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/system/AuditLogPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Row,\n  Col,\n  Tag,\n  DatePicker,\n  Tooltip,\n  Modal,\n  Descriptions,\n  Timeline,\n  Alert,\n  Statistic,\n  Progress,\n  Tabs,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  UserOutlined,\n  FileTextOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  SecurityScanOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { formatDate } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { RangePicker } = DatePicker;\nconst { TabPane } = Tabs;\n\ninterface AuditLog {\n  id: string;\n  userId: string;\n  userName: string;\n  userRole: string;\n  operation: string;\n  module: string;\n  resourceType: string;\n  resourceId: string;\n  resourceName: string;\n  oldValue?: any;\n  newValue?: any;\n  reason?: string;\n  ipAddress: string;\n  userAgent: string;\n  timestamp: string;\n  status: 'SUCCESS' | 'FAILED' | 'WARNING';\n  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n  description: string;\n}\n\nconst AuditLogPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [userFilter, setUserFilter] = useState<string | undefined>();\n  const [moduleFilter, setModuleFilter] = useState<string | undefined>();\n  const [operationFilter, setOperationFilter] = useState<string | undefined>();\n  const [statusFilter, setStatusFilter] = useState<string | undefined>();\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);\n\n  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {\n    setDateRange(dates);\n  };\n  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [activeTab, setActiveTab] = useState('list');\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // TODO: 调用API获取审计日志数据\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    } catch (error) {\n      console.error('加载审计日志失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleViewDetail = (record: AuditLog) => {\n    setSelectedLog(record);\n    setDetailModalVisible(true);\n  };\n\n  const handleExport = () => {\n    // TODO: 实现导出功能\n    Modal.success({\n      title: '导出成功',\n      content: '审计日志已导出到Excel文件',\n    });\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'SUCCESS': return 'success';\n      case 'FAILED': return 'error';\n      case 'WARNING': return 'warning';\n      default: return 'default';\n    }\n  };\n\n  const getRiskLevelColor = (level: string) => {\n    switch (level) {\n      case 'LOW': return 'green';\n      case 'MEDIUM': return 'orange';\n      case 'HIGH': return 'red';\n      case 'CRITICAL': return 'purple';\n      default: return 'default';\n    }\n  };\n\n  const getOperationIcon = (operation: string) => {\n    switch (operation) {\n      case 'CREATE': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case 'UPDATE': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;\n      case 'DELETE': return <WarningOutlined style={{ color: '#f5222d' }} />;\n      case 'LOGIN': return <UserOutlined style={{ color: '#1890ff' }} />;\n      case 'LOGOUT': return <UserOutlined style={{ color: '#8c8c8c' }} />;\n      default: return <FileTextOutlined />;\n    }\n  };\n\n  // 模拟审计日志数据\n  const mockAuditLogs: AuditLog[] = [\n    {\n      id: '1',\n      userId: 'user001',\n      userName: '张三',\n      userRole: 'BOM管理员',\n      operation: 'UPDATE',\n      module: 'BOM管理',\n      resourceType: 'CoreBOM',\n      resourceId: 'BOM-001',\n      resourceName: '主控板BOM',\n      oldValue: { version: '1.0', status: 'DRAFT' },\n      newValue: { version: '1.1', status: 'ACTIVE' },\n      reason: '更新产品规格',\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T10:30:00Z',\n      status: 'SUCCESS',\n      riskLevel: 'MEDIUM',\n      description: '更新BOM版本并激活',\n    },\n    {\n      id: '2',\n      userId: 'user002',\n      userName: '李四',\n      userRole: '采购经理',\n      operation: 'DELETE',\n      module: '采购管理',\n      resourceType: 'PurchaseOrder',\n      resourceId: 'PO-2024-001',\n      resourceName: '电子元器件采购订单',\n      reason: '订单取消',\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T09:15:00Z',\n      status: 'SUCCESS',\n      riskLevel: 'HIGH',\n      description: '删除采购订单',\n    },\n    {\n      id: '3',\n      userId: 'user003',\n      userName: '王五',\n      userRole: '系统管理员',\n      operation: 'CREATE',\n      module: '用户管理',\n      resourceType: 'User',\n      resourceId: 'user004',\n      resourceName: '赵六',\n      newValue: { userName: '赵六', role: 'OPERATOR', status: 'ACTIVE' },\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T08:45:00Z',\n      status: 'SUCCESS',\n      riskLevel: 'LOW',\n      description: '创建新用户账户',\n    },\n    {\n      id: '4',\n      userId: 'user001',\n      userName: '张三',\n      userRole: 'BOM管理员',\n      operation: 'LOGIN',\n      module: '认证',\n      resourceType: 'Session',\n      resourceId: 'session-001',\n      resourceName: '用户登录',\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T08:00:00Z',\n      status: 'SUCCESS',\n      riskLevel: 'LOW',\n      description: '用户成功登录系统',\n    },\n    {\n      id: '5',\n      userId: 'unknown',\n      userName: '未知用户',\n      userRole: '',\n      operation: 'LOGIN',\n      module: '认证',\n      resourceType: 'Session',\n      resourceId: 'session-002',\n      resourceName: '登录失败',\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      timestamp: '2024-03-25T07:30:00Z',\n      status: 'FAILED',\n      riskLevel: 'CRITICAL',\n      description: '多次登录失败，可能存在安全风险',\n    },\n  ];\n\n  const auditLogColumns = [\n    {\n      title: '时间',\n      dataIndex: 'timestamp',\n      key: 'timestamp',\n      width: 160,\n      render: (timestamp: string) => (\n        <div>\n          <div>{formatDate(timestamp, 'YYYY-MM-DD')}</div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {formatDate(timestamp, 'HH:mm:ss')}\n          </Text>\n        </div>\n      ),\n      sorter: (a: AuditLog, b: AuditLog) => dayjs(a.timestamp).unix() - dayjs(b.timestamp).unix(),\n    },\n    {\n      title: '用户',\n      key: 'user',\n      width: 120,\n      render: (_: any, record: AuditLog) => (\n        <div>\n          <div>{record.userName}</div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.userRole}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'operation',\n      width: 120,\n      render: (_: any, record: AuditLog) => (\n        <Space>\n          {getOperationIcon(record.operation)}\n          <span>{record.operation}</span>\n        </Space>\n      ),\n    },\n    {\n      title: '模块',\n      dataIndex: 'module',\n      key: 'module',\n      width: 100,\n    },\n    {\n      title: '资源',\n      key: 'resource',\n      width: 150,\n      render: (_: any, record: AuditLog) => (\n        <div>\n          <div>{record.resourceName}</div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.resourceType}: {record.resourceId}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>{status}</Tag>\n      ),\n    },\n    {\n      title: '风险等级',\n      dataIndex: 'riskLevel',\n      key: 'riskLevel',\n      width: 100,\n      render: (level: string) => (\n        <Tag color={getRiskLevelColor(level)}>{level}</Tag>\n      ),\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ipAddress',\n      key: 'ipAddress',\n      width: 120,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 80,\n      fixed: 'right' as const,\n      render: (_: any, record: AuditLog) => (\n        <Tooltip title=\"查看详情\">\n          <Button\n            type=\"text\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewDetail(record)}\n          />\n        </Tooltip>\n      ),\n    },\n  ];\n\n  const renderStatistics = () => {\n    const totalLogs = mockAuditLogs.length;\n    const successLogs = mockAuditLogs.filter(log => log.status === 'SUCCESS').length;\n    const failedLogs = mockAuditLogs.filter(log => log.status === 'FAILED').length;\n    const highRiskLogs = mockAuditLogs.filter(log => log.riskLevel === 'HIGH' || log.riskLevel === 'CRITICAL').length;\n\n    return (\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"总操作数\"\n              value={totalLogs}\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"成功操作\"\n              value={successLogs}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"失败操作\"\n              value={failedLogs}\n              prefix={<ExclamationCircleOutlined />}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"高风险操作\"\n              value={highRiskLogs}\n              prefix={<SecurityScanOutlined />}\n              valueStyle={{ color: '#d4380d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n    );\n  };\n\n  const renderSecurityAlerts = () => {\n    const criticalLogs = mockAuditLogs.filter(log => log.riskLevel === 'CRITICAL');\n    \n    if (criticalLogs.length === 0) {\n      return null;\n    }\n\n    return (\n      <Alert\n        message=\"安全警告\"\n        description={`检测到 ${criticalLogs.length} 个高风险操作，请及时处理`}\n        type=\"error\"\n        showIcon\n        closable\n        style={{ marginBottom: 16 }}\n        action={\n          <Button size=\"small\" danger>\n            查看详情\n          </Button>\n        }\n      />\n    );\n  };\n\n  const renderAuditTimeline = () => {\n    const timelineItems = mockAuditLogs.slice(0, 10).map(log => ({\n      color: log.status === 'FAILED' ? 'red' : log.riskLevel === 'HIGH' ? 'orange' : 'blue',\n      children: (\n        <div>\n          <div style={{ marginBottom: 4 }}>\n            <Space>\n              {getOperationIcon(log.operation)}\n              <Text strong>{log.userName}</Text>\n              <Text>{log.operation}</Text>\n              <Text type=\"secondary\">{log.resourceName}</Text>\n            </Space>\n          </div>\n          <div style={{ marginBottom: 4 }}>\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              {formatDate(log.timestamp)} • {log.ipAddress}\n            </Text>\n          </div>\n          <div>\n            <Tag color={getStatusColor(log.status)}>{log.status}</Tag>\n            <Tag color={getRiskLevelColor(log.riskLevel)}>{log.riskLevel}</Tag>\n          </div>\n        </div>\n      ),\n    }));\n\n    return (\n      <Card title=\"操作时间线\" size=\"small\">\n        <Timeline items={timelineItems} />\n      </Card>\n    );\n  };\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>审计日志</Title>\n            <Text type=\"secondary\">\n              系统操作记录和安全审计跟踪\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索用户、操作、资源\"\n                allowClear\n                style={{ width: 200 }}\n                onSearch={(value) => {\n                  // TODO: 实现搜索功能\n                  console.log('搜索:', value);\n                }}\n              />\n              <Select\n                placeholder=\"用户\"\n                allowClear\n                style={{ width: 100 }}\n                value={userFilter}\n                onChange={setUserFilter}\n                options={[\n                  { label: '张三', value: 'user001' },\n                  { label: '李四', value: 'user002' },\n                  { label: '王五', value: 'user003' },\n                ]}\n              />\n              <Select\n                placeholder=\"模块\"\n                allowClear\n                style={{ width: 100 }}\n                value={moduleFilter}\n                onChange={setModuleFilter}\n                options={[\n                  { label: 'BOM管理', value: 'BOM管理' },\n                  { label: '采购管理', value: '采购管理' },\n                  { label: '用户管理', value: '用户管理' },\n                  { label: '认证', value: '认证' },\n                ]}\n              />\n              <Select\n                placeholder=\"操作类型\"\n                allowClear\n                style={{ width: 100 }}\n                value={operationFilter}\n                onChange={setOperationFilter}\n                options={[\n                  { label: '创建', value: 'CREATE' },\n                  { label: '更新', value: 'UPDATE' },\n                  { label: '删除', value: 'DELETE' },\n                  { label: '登录', value: 'LOGIN' },\n                  { label: '登出', value: 'LOGOUT' },\n                ]}\n              />\n              <Select\n                placeholder=\"状态\"\n                allowClear\n                style={{ width: 100 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '成功', value: 'SUCCESS' },\n                  { label: '失败', value: 'FAILED' },\n                  { label: '警告', value: 'WARNING' },\n                ]}\n              />\n              <RangePicker\n                value={dateRange}\n                onChange={handleDateRangeChange}\n                style={{ width: 240 }}\n              />\n              <Button icon={<ExportOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n              <Button icon={<ReloadOutlined />} onClick={loadData}>\n                刷新\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        {renderSecurityAlerts()}\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab=\"审计列表\" key=\"list\">\n            {renderStatistics()}\n            <Table\n              columns={auditLogColumns}\n              dataSource={mockAuditLogs}\n              loading={loading}\n              rowKey=\"id\"\n              scroll={{ x: 1200 }}\n              pagination={{\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) =>\n                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n              }}\n            />\n          </TabPane>\n\n          <TabPane tab=\"操作时间线\" key=\"timeline\">\n            <Row gutter={[16, 16]}>\n              <Col xs={24} lg={16}>\n                {renderAuditTimeline()}\n              </Col>\n              <Col xs={24} lg={8}>\n                {renderStatistics()}\n              </Col>\n            </Row>\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 详情模态框 */}\n      <Modal\n        title=\"审计日志详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={800}\n      >\n        {selectedLog && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"操作时间\">\n              {formatDate(selectedLog.timestamp)}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"操作用户\">\n              {selectedLog.userName} ({selectedLog.userRole})\n            </Descriptions.Item>\n            <Descriptions.Item label=\"操作类型\">\n              <Space>\n                {getOperationIcon(selectedLog.operation)}\n                {selectedLog.operation}\n              </Space>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"所属模块\">\n              {selectedLog.module}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"资源类型\">\n              {selectedLog.resourceType}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"资源ID\">\n              {selectedLog.resourceId}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"资源名称\" span={2}>\n              {selectedLog.resourceName}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"操作状态\">\n              <Tag color={getStatusColor(selectedLog.status)}>\n                {selectedLog.status}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"风险等级\">\n              <Tag color={getRiskLevelColor(selectedLog.riskLevel)}>\n                {selectedLog.riskLevel}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"IP地址\">\n              {selectedLog.ipAddress}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"用户代理\" span={2}>\n              <Text code style={{ fontSize: 12 }}>\n                {selectedLog.userAgent}\n              </Text>\n            </Descriptions.Item>\n            {selectedLog.reason && (\n              <Descriptions.Item label=\"操作原因\" span={2}>\n                {selectedLog.reason}\n              </Descriptions.Item>\n            )}\n            <Descriptions.Item label=\"操作描述\" span={2}>\n              {selectedLog.description}\n            </Descriptions.Item>\n            {selectedLog.oldValue && (\n              <Descriptions.Item label=\"修改前\" span={2}>\n                <pre style={{ fontSize: 12, background: '#f5f5f5', padding: 8 }}>\n                  {JSON.stringify(selectedLog.oldValue, null, 2)}\n                </pre>\n              </Descriptions.Item>\n            )}\n            {selectedLog.newValue && (\n              <Descriptions.Item label=\"修改后\" span={2}>\n                <pre style={{ fontSize: 12, background: '#f5f5f5', padding: 8 }}>\n                  {JSON.stringify(selectedLog.newValue, null, 2)}\n                </pre>\n              </Descriptions.Item>\n            )}\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default AuditLogPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,GAAG,CACHC,GAAG,CACHC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,KAAK,CACLC,YAAY,CACZC,QAAQ,CACRC,KAAK,CACLC,SAAS,CAETC,IAAI,KACC,MAAM,CACb,OAEEC,WAAW,CACXC,cAAc,CACdC,cAAc,CACdC,YAAY,CACZC,gBAAgB,CAChBC,eAAe,CACfC,mBAAmB,CACnBC,yBAAyB,CACzBC,oBAAoB,KACf,mBAAmB,CAC1B,MAAO,CAAAC,KAAK,KAAM,OAAO,CAEzB,OAASC,UAAU,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGjC,UAAU,CAClC,KAAM,CAAEkC,MAAO,CAAC,CAAG9B,KAAK,CACxB,KAAM,CAAE+B,WAAY,CAAC,CAAG1B,UAAU,CAClC,KAAM,CAAE2B,OAAQ,CAAC,CAAGpB,IAAI,CAuBxB,KAAM,CAAAqB,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC2C,UAAU,CAAEC,aAAa,CAAC,CAAG5C,QAAQ,CAAqB,CAAC,CAClE,KAAM,CAAC6C,YAAY,CAAEC,eAAe,CAAC,CAAG9C,QAAQ,CAAqB,CAAC,CACtE,KAAM,CAAC+C,eAAe,CAAEC,kBAAkB,CAAC,CAAGhD,QAAQ,CAAqB,CAAC,CAC5E,KAAM,CAACiD,YAAY,CAAEC,eAAe,CAAC,CAAGlD,QAAQ,CAAqB,CAAC,CACtE,KAAM,CAACmD,SAAS,CAAEC,YAAY,CAAC,CAAGpD,QAAQ,CAAoC,IAAI,CAAC,CAEnF,KAAM,CAAAqD,qBAAqB,CAAGA,CAACC,KAAU,CAAEC,WAA6B,GAAK,CAC3EH,YAAY,CAACE,KAAK,CAAC,CACrB,CAAC,CACD,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGzD,QAAQ,CAAkB,IAAI,CAAC,CACrE,KAAM,CAAC0D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAG7D,QAAQ,CAAC,MAAM,CAAC,CAElDC,SAAS,CAAC,IAAM,CACd6D,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BpB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,IAAI,CAAAqB,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CACzD,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CAAC,OAAS,CACRxB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA0B,gBAAgB,CAAIC,MAAgB,EAAK,CAC7CZ,cAAc,CAACY,MAAM,CAAC,CACtBV,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAW,YAAY,CAAGA,CAAA,GAAM,CACzB;AACAxD,KAAK,CAACyD,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,iBACX,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIC,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,SAAS,CAAE,MAAO,SAAS,CAChC,IAAK,QAAQ,CAAE,MAAO,OAAO,CAC7B,IAAK,SAAS,CAAE,MAAO,SAAS,CAChC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,KAAa,EAAK,CAC3C,OAAQA,KAAK,EACX,IAAK,KAAK,CAAE,MAAO,OAAO,CAC1B,IAAK,QAAQ,CAAE,MAAO,QAAQ,CAC9B,IAAK,MAAM,CAAE,MAAO,KAAK,CACzB,IAAK,UAAU,CAAE,MAAO,QAAQ,CAChC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,SAAiB,EAAK,CAC9C,OAAQA,SAAS,EACf,IAAK,QAAQ,CAAE,mBAAO/C,IAAA,CAACN,mBAAmB,EAACsD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC1E,IAAK,QAAQ,CAAE,mBAAOjD,IAAA,CAACL,yBAAyB,EAACqD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAChF,IAAK,QAAQ,CAAE,mBAAOjD,IAAA,CAACP,eAAe,EAACuD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACtE,IAAK,OAAO,CAAE,mBAAOjD,IAAA,CAACT,YAAY,EAACyD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAClE,IAAK,QAAQ,CAAE,mBAAOjD,IAAA,CAACT,YAAY,EAACyD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACnE,QAAS,mBAAOjD,IAAA,CAACR,gBAAgB,GAAE,CAAC,CACtC,CACF,CAAC,CAED;AACA,KAAM,CAAA0D,aAAyB,CAAG,CAChC,CACEC,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,QAAQ,CAClBP,SAAS,CAAE,QAAQ,CACnBQ,MAAM,CAAE,OAAO,CACfC,YAAY,CAAE,SAAS,CACvBC,UAAU,CAAE,SAAS,CACrBC,YAAY,CAAE,QAAQ,CACtBC,QAAQ,CAAE,CAAEC,OAAO,CAAE,KAAK,CAAEjB,MAAM,CAAE,OAAQ,CAAC,CAC7CkB,QAAQ,CAAE,CAAED,OAAO,CAAE,KAAK,CAAEjB,MAAM,CAAE,QAAS,CAAC,CAC9CmB,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,eAAe,CAC1BC,SAAS,CAAE,8DAA8D,CACzEC,SAAS,CAAE,sBAAsB,CACjCtB,MAAM,CAAE,SAAS,CACjBuB,SAAS,CAAE,QAAQ,CACnBC,WAAW,CAAE,YACf,CAAC,CACD,CACEhB,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,MAAM,CAChBP,SAAS,CAAE,QAAQ,CACnBQ,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,eAAe,CAC7BC,UAAU,CAAE,aAAa,CACzBC,YAAY,CAAE,WAAW,CACzBI,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,eAAe,CAC1BC,SAAS,CAAE,8DAA8D,CACzEC,SAAS,CAAE,sBAAsB,CACjCtB,MAAM,CAAE,SAAS,CACjBuB,SAAS,CAAE,MAAM,CACjBC,WAAW,CAAE,QACf,CAAC,CACD,CACEhB,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,OAAO,CACjBP,SAAS,CAAE,QAAQ,CACnBQ,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,MAAM,CACpBC,UAAU,CAAE,SAAS,CACrBC,YAAY,CAAE,IAAI,CAClBG,QAAQ,CAAE,CAAER,QAAQ,CAAE,IAAI,CAAEe,IAAI,CAAE,UAAU,CAAEzB,MAAM,CAAE,QAAS,CAAC,CAChEoB,SAAS,CAAE,eAAe,CAC1BC,SAAS,CAAE,8DAA8D,CACzEC,SAAS,CAAE,sBAAsB,CACjCtB,MAAM,CAAE,SAAS,CACjBuB,SAAS,CAAE,KAAK,CAChBC,WAAW,CAAE,SACf,CAAC,CACD,CACEhB,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,QAAQ,CAClBP,SAAS,CAAE,OAAO,CAClBQ,MAAM,CAAE,IAAI,CACZC,YAAY,CAAE,SAAS,CACvBC,UAAU,CAAE,aAAa,CACzBC,YAAY,CAAE,MAAM,CACpBK,SAAS,CAAE,eAAe,CAC1BC,SAAS,CAAE,8DAA8D,CACzEC,SAAS,CAAE,sBAAsB,CACjCtB,MAAM,CAAE,SAAS,CACjBuB,SAAS,CAAE,KAAK,CAChBC,WAAW,CAAE,UACf,CAAC,CACD,CACEhB,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,EAAE,CACZP,SAAS,CAAE,OAAO,CAClBQ,MAAM,CAAE,IAAI,CACZC,YAAY,CAAE,SAAS,CACvBC,UAAU,CAAE,aAAa,CACzBC,YAAY,CAAE,MAAM,CACpBK,SAAS,CAAE,eAAe,CAC1BC,SAAS,CAAE,8DAA8D,CACzEC,SAAS,CAAE,sBAAsB,CACjCtB,MAAM,CAAE,QAAQ,CAChBuB,SAAS,CAAE,UAAU,CACrBC,WAAW,CAAE,iBACf,CAAC,CACF,CAED,KAAM,CAAAE,eAAe,CAAG,CACtB,CACE7B,KAAK,CAAE,IAAI,CACX8B,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGR,SAAiB,eACxB/D,KAAA,QAAAwE,QAAA,eACE1E,IAAA,QAAA0E,QAAA,CAAM5E,UAAU,CAACmE,SAAS,CAAE,YAAY,CAAC,CAAM,CAAC,cAChDjE,IAAA,CAACI,IAAI,EAACuE,IAAI,CAAC,WAAW,CAAC3B,KAAK,CAAE,CAAE4B,QAAQ,CAAE,EAAG,CAAE,CAAAF,QAAA,CAC5C5E,UAAU,CAACmE,SAAS,CAAE,UAAU,CAAC,CAC9B,CAAC,EACJ,CACN,CACDY,MAAM,CAAEA,CAACC,CAAW,CAAEC,CAAW,GAAKlF,KAAK,CAACiF,CAAC,CAACb,SAAS,CAAC,CAACe,IAAI,CAAC,CAAC,CAAGnF,KAAK,CAACkF,CAAC,CAACd,SAAS,CAAC,CAACe,IAAI,CAAC,CAC5F,CAAC,CACD,CACExC,KAAK,CAAE,IAAI,CACX+B,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACQ,CAAM,CAAE5C,MAAgB,gBAC/BnC,KAAA,QAAAwE,QAAA,eACE1E,IAAA,QAAA0E,QAAA,CAAMrC,MAAM,CAACgB,QAAQ,CAAM,CAAC,cAC5BrD,IAAA,CAACI,IAAI,EAACuE,IAAI,CAAC,WAAW,CAAC3B,KAAK,CAAE,CAAE4B,QAAQ,CAAE,EAAG,CAAE,CAAAF,QAAA,CAC5CrC,MAAM,CAACiB,QAAQ,CACZ,CAAC,EACJ,CAET,CAAC,CACD,CACEd,KAAK,CAAE,IAAI,CACX+B,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACQ,CAAM,CAAE5C,MAAgB,gBAC/BnC,KAAA,CAAC5B,KAAK,EAAAoG,QAAA,EACH5B,gBAAgB,CAACT,MAAM,CAACU,SAAS,CAAC,cACnC/C,IAAA,SAAA0E,QAAA,CAAOrC,MAAM,CAACU,SAAS,CAAO,CAAC,EAC1B,CAEX,CAAC,CACD,CACEP,KAAK,CAAE,IAAI,CACX8B,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GACT,CAAC,CACD,CACEhC,KAAK,CAAE,IAAI,CACX+B,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACQ,CAAM,CAAE5C,MAAgB,gBAC/BnC,KAAA,QAAAwE,QAAA,eACE1E,IAAA,QAAA0E,QAAA,CAAMrC,MAAM,CAACqB,YAAY,CAAM,CAAC,cAChCxD,KAAA,CAACE,IAAI,EAACuE,IAAI,CAAC,WAAW,CAAC3B,KAAK,CAAE,CAAE4B,QAAQ,CAAE,EAAG,CAAE,CAAAF,QAAA,EAC5CrC,MAAM,CAACmB,YAAY,CAAC,IAAE,CAACnB,MAAM,CAACoB,UAAU,EACrC,CAAC,EACJ,CAET,CAAC,CACD,CACEjB,KAAK,CAAE,IAAI,CACX8B,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAG9B,MAAc,eACrB3C,IAAA,CAACrB,GAAG,EAACsE,KAAK,CAAEP,cAAc,CAACC,MAAM,CAAE,CAAA+B,QAAA,CAAE/B,MAAM,CAAM,CAErD,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACb8B,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAG5B,KAAa,eACpB7C,IAAA,CAACrB,GAAG,EAACsE,KAAK,CAAEL,iBAAiB,CAACC,KAAK,CAAE,CAAA6B,QAAA,CAAE7B,KAAK,CAAM,CAEtD,CAAC,CACD,CACEL,KAAK,CAAE,MAAM,CACb8B,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GACT,CAAC,CACD,CACEhC,KAAK,CAAE,IAAI,CACX+B,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,EAAE,CACTU,KAAK,CAAE,OAAgB,CACvBT,MAAM,CAAEA,CAACQ,CAAM,CAAE5C,MAAgB,gBAC/BrC,IAAA,CAACnB,OAAO,EAAC2D,KAAK,CAAC,0BAAM,CAAAkC,QAAA,cACnB1E,IAAA,CAAC3B,MAAM,EACLsG,IAAI,CAAC,MAAM,CACXQ,IAAI,cAAEnF,IAAA,CAACZ,WAAW,GAAE,CAAE,CACtBgG,OAAO,CAAEA,CAAA,GAAMhD,gBAAgB,CAACC,MAAM,CAAE,CACzC,CAAC,CACK,CAEb,CAAC,CACF,CAED,KAAM,CAAAgD,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,SAAS,CAAGpC,aAAa,CAACqC,MAAM,CACtC,KAAM,CAAAC,WAAW,CAAGtC,aAAa,CAACuC,MAAM,CAACC,GAAG,EAAIA,GAAG,CAAC/C,MAAM,GAAK,SAAS,CAAC,CAAC4C,MAAM,CAChF,KAAM,CAAAI,UAAU,CAAGzC,aAAa,CAACuC,MAAM,CAACC,GAAG,EAAIA,GAAG,CAAC/C,MAAM,GAAK,QAAQ,CAAC,CAAC4C,MAAM,CAC9E,KAAM,CAAAK,YAAY,CAAG1C,aAAa,CAACuC,MAAM,CAACC,GAAG,EAAIA,GAAG,CAACxB,SAAS,GAAK,MAAM,EAAIwB,GAAG,CAACxB,SAAS,GAAK,UAAU,CAAC,CAACqB,MAAM,CAEjH,mBACErF,KAAA,CAACzB,GAAG,EAACoH,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC7C,KAAK,CAAE,CAAE8C,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,eACjD1E,IAAA,CAACtB,GAAG,EAACqH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACjB1E,IAAA,CAAC9B,IAAI,EAAC+H,IAAI,CAAC,OAAO,CAAAvB,QAAA,cAChB1E,IAAA,CAACd,SAAS,EACRsD,KAAK,CAAC,0BAAM,CACZ0D,KAAK,CAAEZ,SAAU,CACjBa,MAAM,cAAEnG,IAAA,CAACR,gBAAgB,GAAE,CAAE,CAC9B,CAAC,CACE,CAAC,CACJ,CAAC,cACNQ,IAAA,CAACtB,GAAG,EAACqH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACjB1E,IAAA,CAAC9B,IAAI,EAAC+H,IAAI,CAAC,OAAO,CAAAvB,QAAA,cAChB1E,IAAA,CAACd,SAAS,EACRsD,KAAK,CAAC,0BAAM,CACZ0D,KAAK,CAAEV,WAAY,CACnBW,MAAM,cAAEnG,IAAA,CAACN,mBAAmB,GAAE,CAAE,CAChC0G,UAAU,CAAE,CAAEnD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNjD,IAAA,CAACtB,GAAG,EAACqH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACjB1E,IAAA,CAAC9B,IAAI,EAAC+H,IAAI,CAAC,OAAO,CAAAvB,QAAA,cAChB1E,IAAA,CAACd,SAAS,EACRsD,KAAK,CAAC,0BAAM,CACZ0D,KAAK,CAAEP,UAAW,CAClBQ,MAAM,cAAEnG,IAAA,CAACL,yBAAyB,GAAE,CAAE,CACtCyG,UAAU,CAAE,CAAEnD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNjD,IAAA,CAACtB,GAAG,EAACqH,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAtB,QAAA,cACjB1E,IAAA,CAAC9B,IAAI,EAAC+H,IAAI,CAAC,OAAO,CAAAvB,QAAA,cAChB1E,IAAA,CAACd,SAAS,EACRsD,KAAK,CAAC,gCAAO,CACb0D,KAAK,CAAEN,YAAa,CACpBO,MAAM,cAAEnG,IAAA,CAACJ,oBAAoB,GAAE,CAAE,CACjCwG,UAAU,CAAE,CAAEnD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,KAAM,CAAAoD,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,YAAY,CAAGpD,aAAa,CAACuC,MAAM,CAACC,GAAG,EAAIA,GAAG,CAACxB,SAAS,GAAK,UAAU,CAAC,CAE9E,GAAIoC,YAAY,CAACf,MAAM,GAAK,CAAC,CAAE,CAC7B,MAAO,KAAI,CACb,CAEA,mBACEvF,IAAA,CAACf,KAAK,EACJsH,OAAO,CAAC,0BAAM,CACdpC,WAAW,uBAAAqC,MAAA,CAASF,YAAY,CAACf,MAAM,6EAAgB,CACvDZ,IAAI,CAAC,OAAO,CACZ8B,QAAQ,MACRC,QAAQ,MACR1D,KAAK,CAAE,CAAE8C,YAAY,CAAE,EAAG,CAAE,CAC5Ba,MAAM,cACJ3G,IAAA,CAAC3B,MAAM,EAAC4H,IAAI,CAAC,OAAO,CAACW,MAAM,MAAAlC,QAAA,CAAC,0BAE5B,CAAQ,CACT,CACF,CAAC,CAEN,CAAC,CAED,KAAM,CAAAmC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,aAAa,CAAG5D,aAAa,CAAC6D,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAACC,GAAG,CAACtB,GAAG,GAAK,CAC3DzC,KAAK,CAAEyC,GAAG,CAAC/C,MAAM,GAAK,QAAQ,CAAG,KAAK,CAAG+C,GAAG,CAACxB,SAAS,GAAK,MAAM,CAAG,QAAQ,CAAG,MAAM,CACrFQ,QAAQ,cACNxE,KAAA,QAAAwE,QAAA,eACE1E,IAAA,QAAKgD,KAAK,CAAE,CAAE8C,YAAY,CAAE,CAAE,CAAE,CAAApB,QAAA,cAC9BxE,KAAA,CAAC5B,KAAK,EAAAoG,QAAA,EACH5B,gBAAgB,CAAC4C,GAAG,CAAC3C,SAAS,CAAC,cAChC/C,IAAA,CAACI,IAAI,EAAC6G,MAAM,MAAAvC,QAAA,CAAEgB,GAAG,CAACrC,QAAQ,CAAO,CAAC,cAClCrD,IAAA,CAACI,IAAI,EAAAsE,QAAA,CAAEgB,GAAG,CAAC3C,SAAS,CAAO,CAAC,cAC5B/C,IAAA,CAACI,IAAI,EAACuE,IAAI,CAAC,WAAW,CAAAD,QAAA,CAAEgB,GAAG,CAAChC,YAAY,CAAO,CAAC,EAC3C,CAAC,CACL,CAAC,cACN1D,IAAA,QAAKgD,KAAK,CAAE,CAAE8C,YAAY,CAAE,CAAE,CAAE,CAAApB,QAAA,cAC9BxE,KAAA,CAACE,IAAI,EAACuE,IAAI,CAAC,WAAW,CAAC3B,KAAK,CAAE,CAAE4B,QAAQ,CAAE,EAAG,CAAE,CAAAF,QAAA,EAC5C5E,UAAU,CAAC4F,GAAG,CAACzB,SAAS,CAAC,CAAC,UAAG,CAACyB,GAAG,CAAC3B,SAAS,EACxC,CAAC,CACJ,CAAC,cACN7D,KAAA,QAAAwE,QAAA,eACE1E,IAAA,CAACrB,GAAG,EAACsE,KAAK,CAAEP,cAAc,CAACgD,GAAG,CAAC/C,MAAM,CAAE,CAAA+B,QAAA,CAAEgB,GAAG,CAAC/C,MAAM,CAAM,CAAC,cAC1D3C,IAAA,CAACrB,GAAG,EAACsE,KAAK,CAAEL,iBAAiB,CAAC8C,GAAG,CAACxB,SAAS,CAAE,CAAAQ,QAAA,CAAEgB,GAAG,CAACxB,SAAS,CAAM,CAAC,EAChE,CAAC,EACH,CAET,CAAC,CAAC,CAAC,CAEH,mBACElE,IAAA,CAAC9B,IAAI,EAACsE,KAAK,CAAC,gCAAO,CAACyD,IAAI,CAAC,OAAO,CAAAvB,QAAA,cAC9B1E,IAAA,CAAChB,QAAQ,EAACkI,KAAK,CAAEJ,aAAc,CAAE,CAAC,CAC9B,CAAC,CAEX,CAAC,CAED,mBACE5G,KAAA,QAAAwE,QAAA,eACExE,KAAA,CAAChC,IAAI,EAAAwG,QAAA,eACHxE,KAAA,CAACzB,GAAG,EAAC0I,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACpE,KAAK,CAAE,CAAE8C,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,eACtExE,KAAA,CAACxB,GAAG,EAAAgG,QAAA,eACF1E,IAAA,CAACG,KAAK,EAAC0C,KAAK,CAAE,CAAE,CAACG,KAAK,CAAE,CAAEqE,MAAM,CAAE,CAAE,CAAE,CAAA3C,QAAA,CAAC,0BAAI,CAAO,CAAC,cACnD1E,IAAA,CAACI,IAAI,EAACuE,IAAI,CAAC,WAAW,CAAAD,QAAA,CAAC,gFAEvB,CAAM,CAAC,EACJ,CAAC,cACN1E,IAAA,CAACtB,GAAG,EAAAgG,QAAA,cACFxE,KAAA,CAAC5B,KAAK,EAAAoG,QAAA,eACJ1E,IAAA,CAACK,MAAM,EACLiH,WAAW,CAAC,8DAAY,CACxBC,UAAU,MACVvE,KAAK,CAAE,CAAEwB,KAAK,CAAE,GAAI,CAAE,CACtBgD,QAAQ,CAAGtB,KAAK,EAAK,CACnB;AACA/D,OAAO,CAACuD,GAAG,CAAC,KAAK,CAAEQ,KAAK,CAAC,CAC3B,CAAE,CACH,CAAC,cACFlG,IAAA,CAACxB,MAAM,EACL8I,WAAW,CAAC,cAAI,CAChBC,UAAU,MACVvE,KAAK,CAAE,CAAEwB,KAAK,CAAE,GAAI,CAAE,CACtB0B,KAAK,CAAEvF,UAAW,CAClB8G,QAAQ,CAAE7G,aAAc,CACxB8G,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,SAAU,CAAC,CACjC,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,SAAU,CAAC,CACjC,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,SAAU,CAAC,CACjC,CACH,CAAC,cACFlG,IAAA,CAACxB,MAAM,EACL8I,WAAW,CAAC,cAAI,CAChBC,UAAU,MACVvE,KAAK,CAAE,CAAEwB,KAAK,CAAE,GAAI,CAAE,CACtB0B,KAAK,CAAErF,YAAa,CACpB4G,QAAQ,CAAE3G,eAAgB,CAC1B4G,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,OAAO,CAAEzB,KAAK,CAAE,OAAQ,CAAC,CAClC,CAAEyB,KAAK,CAAE,MAAM,CAAEzB,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEyB,KAAK,CAAE,MAAM,CAAEzB,KAAK,CAAE,MAAO,CAAC,CAChC,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,IAAK,CAAC,CAC5B,CACH,CAAC,cACFlG,IAAA,CAACxB,MAAM,EACL8I,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVvE,KAAK,CAAE,CAAEwB,KAAK,CAAE,GAAI,CAAE,CACtB0B,KAAK,CAAEnF,eAAgB,CACvB0G,QAAQ,CAAEzG,kBAAmB,CAC7B0G,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,QAAS,CAAC,CAChC,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,QAAS,CAAC,CAChC,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,QAAS,CAAC,CAChC,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,OAAQ,CAAC,CAC/B,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,QAAS,CAAC,CAChC,CACH,CAAC,cACFlG,IAAA,CAACxB,MAAM,EACL8I,WAAW,CAAC,cAAI,CAChBC,UAAU,MACVvE,KAAK,CAAE,CAAEwB,KAAK,CAAE,GAAI,CAAE,CACtB0B,KAAK,CAAEjF,YAAa,CACpBwG,QAAQ,CAAEvG,eAAgB,CAC1BwG,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,SAAU,CAAC,CACjC,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,QAAS,CAAC,CAChC,CAAEyB,KAAK,CAAE,IAAI,CAAEzB,KAAK,CAAE,SAAU,CAAC,CACjC,CACH,CAAC,cACFlG,IAAA,CAACM,WAAW,EACV4F,KAAK,CAAE/E,SAAU,CACjBsG,QAAQ,CAAEpG,qBAAsB,CAChC2B,KAAK,CAAE,CAAEwB,KAAK,CAAE,GAAI,CAAE,CACvB,CAAC,cACFxE,IAAA,CAAC3B,MAAM,EAAC8G,IAAI,cAAEnF,IAAA,CAACX,cAAc,GAAE,CAAE,CAAC+F,OAAO,CAAE9C,YAAa,CAAAoC,QAAA,CAAC,cAEzD,CAAQ,CAAC,cACT1E,IAAA,CAAC3B,MAAM,EAAC8G,IAAI,cAAEnF,IAAA,CAACV,cAAc,GAAE,CAAE,CAAC8F,OAAO,CAAEtD,QAAS,CAAA4C,QAAA,CAAC,cAErD,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,CAEL2B,oBAAoB,CAAC,CAAC,cAEvBnG,KAAA,CAACf,IAAI,EAACyI,SAAS,CAAEhG,SAAU,CAAC6F,QAAQ,CAAE5F,YAAa,CAAA6C,QAAA,eACjDxE,KAAA,CAACK,OAAO,EAACsH,GAAG,CAAC,0BAAM,CAAAnD,QAAA,EAChBW,gBAAgB,CAAC,CAAC,cACnBrF,IAAA,CAAC5B,KAAK,EACJ0J,OAAO,CAAEzD,eAAgB,CACzB0D,UAAU,CAAE7E,aAAc,CAC1BzC,OAAO,CAAEA,OAAQ,CACjBuH,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAAhC,MAAA,CACjBgC,KAAK,CAAC,CAAC,CAAC,MAAAhC,MAAA,CAAIgC,KAAK,CAAC,CAAC,CAAC,oBAAAhC,MAAA,CAAQ+B,KAAK,WAC1C,CAAE,CACH,CAAC,GAdoB,MAef,CAAC,cAEVvI,IAAA,CAACO,OAAO,EAACsH,GAAG,CAAC,gCAAO,CAAAnD,QAAA,cAClBxE,KAAA,CAACzB,GAAG,EAACoH,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAnB,QAAA,eACpB1E,IAAA,CAACtB,GAAG,EAACqH,EAAE,CAAE,EAAG,CAAC0C,EAAE,CAAE,EAAG,CAAA/D,QAAA,CACjBmC,mBAAmB,CAAC,CAAC,CACnB,CAAC,cACN7G,IAAA,CAACtB,GAAG,EAACqH,EAAE,CAAE,EAAG,CAAC0C,EAAE,CAAE,CAAE,CAAA/D,QAAA,CAChBW,gBAAgB,CAAC,CAAC,CAChB,CAAC,EACH,CAAC,EARiB,UAShB,CAAC,EACN,CAAC,EACH,CAAC,cAGPrF,IAAA,CAAClB,KAAK,EACJ0D,KAAK,CAAC,sCAAQ,CACdkG,IAAI,CAAEhH,kBAAmB,CACzBiH,QAAQ,CAAEA,CAAA,GAAMhH,qBAAqB,CAAC,KAAK,CAAE,CAC7CiH,MAAM,CAAE,cACN5I,IAAA,CAAC3B,MAAM,EAAa+G,OAAO,CAAEA,CAAA,GAAMzD,qBAAqB,CAAC,KAAK,CAAE,CAAA+C,QAAA,CAAC,cAEjE,EAFY,OAEJ,CAAC,CACT,CACFF,KAAK,CAAE,GAAI,CAAAE,QAAA,CAEVlD,WAAW,eACVtB,KAAA,CAACnB,YAAY,EAAC8J,MAAM,CAAE,CAAE,CAACC,QAAQ,MAAApE,QAAA,eAC/B1E,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAjD,QAAA,CAC5B5E,UAAU,CAAC0B,WAAW,CAACyC,SAAS,CAAC,CACjB,CAAC,cACpB/D,KAAA,CAACnB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAjD,QAAA,EAC5BlD,WAAW,CAAC6B,QAAQ,CAAC,IAAE,CAAC7B,WAAW,CAAC8B,QAAQ,CAAC,GAChD,EAAmB,CAAC,cACpBtD,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAjD,QAAA,cAC7BxE,KAAA,CAAC5B,KAAK,EAAAoG,QAAA,EACH5B,gBAAgB,CAACtB,WAAW,CAACuB,SAAS,CAAC,CACvCvB,WAAW,CAACuB,SAAS,EACjB,CAAC,CACS,CAAC,cACpB/C,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAjD,QAAA,CAC5BlD,WAAW,CAAC+B,MAAM,CACF,CAAC,cACpBvD,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAjD,QAAA,CAC5BlD,WAAW,CAACgC,YAAY,CACR,CAAC,cACpBxD,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,gBAAM,CAAAjD,QAAA,CAC5BlD,WAAW,CAACiC,UAAU,CACN,CAAC,cACpBzD,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAACqB,IAAI,CAAE,CAAE,CAAAtE,QAAA,CACrClD,WAAW,CAACkC,YAAY,CACR,CAAC,cACpB1D,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAjD,QAAA,cAC7B1E,IAAA,CAACrB,GAAG,EAACsE,KAAK,CAAEP,cAAc,CAAClB,WAAW,CAACmB,MAAM,CAAE,CAAA+B,QAAA,CAC5ClD,WAAW,CAACmB,MAAM,CAChB,CAAC,CACW,CAAC,cACpB3C,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAAAjD,QAAA,cAC7B1E,IAAA,CAACrB,GAAG,EAACsE,KAAK,CAAEL,iBAAiB,CAACpB,WAAW,CAAC0C,SAAS,CAAE,CAAAQ,QAAA,CAClDlD,WAAW,CAAC0C,SAAS,CACnB,CAAC,CACW,CAAC,cACpBlE,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,gBAAM,CAAAjD,QAAA,CAC5BlD,WAAW,CAACuC,SAAS,CACL,CAAC,cACpB/D,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAACqB,IAAI,CAAE,CAAE,CAAAtE,QAAA,cACtC1E,IAAA,CAACI,IAAI,EAAC6I,IAAI,MAACjG,KAAK,CAAE,CAAE4B,QAAQ,CAAE,EAAG,CAAE,CAAAF,QAAA,CAChClD,WAAW,CAACwC,SAAS,CAClB,CAAC,CACU,CAAC,CACnBxC,WAAW,CAACsC,MAAM,eACjB9D,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAACqB,IAAI,CAAE,CAAE,CAAAtE,QAAA,CACrClD,WAAW,CAACsC,MAAM,CACF,CACpB,cACD9D,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,0BAAM,CAACqB,IAAI,CAAE,CAAE,CAAAtE,QAAA,CACrClD,WAAW,CAAC2C,WAAW,CACP,CAAC,CACnB3C,WAAW,CAACmC,QAAQ,eACnB3D,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAACqB,IAAI,CAAE,CAAE,CAAAtE,QAAA,cACrC1E,IAAA,QAAKgD,KAAK,CAAE,CAAE4B,QAAQ,CAAE,EAAE,CAAEsE,UAAU,CAAE,SAAS,CAAEC,OAAO,CAAE,CAAE,CAAE,CAAAzE,QAAA,CAC7D0E,IAAI,CAACC,SAAS,CAAC7H,WAAW,CAACmC,QAAQ,CAAE,IAAI,CAAE,CAAC,CAAC,CAC3C,CAAC,CACW,CACpB,CACAnC,WAAW,CAACqC,QAAQ,eACnB7D,IAAA,CAACjB,YAAY,CAACgK,IAAI,EAACpB,KAAK,CAAC,oBAAK,CAACqB,IAAI,CAAE,CAAE,CAAAtE,QAAA,cACrC1E,IAAA,QAAKgD,KAAK,CAAE,CAAE4B,QAAQ,CAAE,EAAE,CAAEsE,UAAU,CAAE,SAAS,CAAEC,OAAO,CAAE,CAAE,CAAE,CAAAzE,QAAA,CAC7D0E,IAAI,CAACC,SAAS,CAAC7H,WAAW,CAACqC,QAAQ,CAAE,IAAI,CAAE,CAAC,CAAC,CAC3C,CAAC,CACW,CACpB,EACW,CACf,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAArD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}