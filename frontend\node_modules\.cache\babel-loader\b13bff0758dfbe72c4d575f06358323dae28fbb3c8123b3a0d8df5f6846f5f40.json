{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\inventory\\\\CuttingPlanPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Row, Col, Statistic, Typography, Modal, Form, Input, InputNumber, Select, DatePicker, Alert, Tabs, Tag, Tooltip, Progress, Divider, message, Checkbox } from 'antd';\nimport { PlusOutlined, CalculatorOutlined, ScissorOutlined, ExportOutlined, ReloadOutlined, EyeOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  TextArea\n} = Input;\nconst CuttingPlanPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('plans');\n  const [planModalVisible, setPlanModalVisible] = useState(false);\n  const [optimizeModalVisible, setOptimizeModalVisible] = useState(false);\n  const [schemeModalVisible, setSchemeModalVisible] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [planForm] = Form.useForm();\n  const [optimizeForm] = Form.useForm();\n  const loadData = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n  };\n  useEffect(() => {\n    loadData();\n  }, []);\n  const handleCreatePlan = () => {\n    setPlanModalVisible(true);\n    planForm.resetFields();\n  };\n  const handleOptimize = record => {\n    setSelectedPlan(record);\n    setOptimizeModalVisible(true);\n    optimizeForm.resetFields();\n  };\n  const handleViewScheme = record => {\n    setSelectedPlan(record);\n    setSchemeModalVisible(true);\n  };\n  const handlePlanModalOk = () => {\n    planForm.validateFields().then(values => {\n      console.log('创建切割计划:', values);\n      message.success('切割计划创建成功');\n      setPlanModalVisible(false);\n      loadData();\n    });\n  };\n  const handleOptimizeModalOk = () => {\n    optimizeForm.validateFields().then(values => {\n      console.log('优化参数:', values);\n      message.success('切割优化计算已开始，请稍候查看结果');\n      setOptimizeModalVisible(false);\n      loadData();\n    });\n  };\n\n  // 模拟切割计划数据\n  const mockPlans = [{\n    id: '1',\n    planName: '5G基站电缆切割计划-202403',\n    materialCode: 'CABLE-001',\n    materialName: '同轴电缆',\n    specification: 'RG-58/U',\n    standardLength: 100,\n    unit: 'M',\n    unitPrice: 4.5,\n    totalDemand: 1250,\n    planDate: '2024-03-25T00:00:00Z',\n    status: 'optimized',\n    algorithm: '首次适应算法',\n    utilizationRate: 92.5,\n    wasteRate: 7.5,\n    totalCost: 5625,\n    wasteCost: 421.88,\n    createdBy: '工艺工程师',\n    createdDate: '2024-03-20T00:00:00Z'\n  }, {\n    id: '2',\n    planName: '不锈钢板切割计划-202403',\n    materialCode: 'STEEL-PLATE-001',\n    materialName: '不锈钢板',\n    specification: '304-2mm',\n    standardLength: 2000,\n    unit: 'MM',\n    unitPrice: 0.15,\n    totalDemand: 8500,\n    planDate: '2024-03-28T00:00:00Z',\n    status: 'calculating',\n    algorithm: '最佳适应算法',\n    utilizationRate: 0,\n    wasteRate: 0,\n    totalCost: 1275,\n    wasteCost: 0,\n    createdBy: '生产计划员',\n    createdDate: '2024-03-22T00:00:00Z'\n  }, {\n    id: '3',\n    planName: '铜线切割计划-202403',\n    materialCode: 'COPPER-WIRE-001',\n    materialName: '铜线',\n    specification: '2.5mm²',\n    standardLength: 500,\n    unit: 'M',\n    unitPrice: 8.2,\n    totalDemand: 2800,\n    planDate: '2024-03-30T00:00:00Z',\n    status: 'draft',\n    algorithm: '',\n    utilizationRate: 0,\n    wasteRate: 0,\n    totalCost: 22960,\n    wasteCost: 0,\n    createdBy: '工艺工程师',\n    createdDate: '2024-03-24T00:00:00Z'\n  }];\n\n  // 模拟切割方案数据\n  const mockSchemes = [{\n    id: '1',\n    planId: '1',\n    schemeNo: 1,\n    pattern: '25M + 25M + 25M + 25M',\n    cuts: [25, 25, 25, 25],\n    quantity: 8,\n    utilization: 100,\n    waste: 0,\n    wasteLength: 0\n  }, {\n    id: '2',\n    planId: '1',\n    schemeNo: 2,\n    pattern: '30M + 30M + 30M + 10M',\n    cuts: [30, 30, 30, 10],\n    quantity: 6,\n    utilization: 100,\n    waste: 0,\n    wasteLength: 0\n  }, {\n    id: '3',\n    planId: '1',\n    schemeNo: 3,\n    pattern: '35M + 35M + 20M + 8M',\n    cuts: [35, 35, 20, 8],\n    quantity: 4,\n    utilization: 98,\n    waste: 2,\n    wasteLength: 2\n  }];\n\n  // 模拟需求数据\n  const mockDemands = [{\n    id: '1',\n    length: 25,\n    quantity: 32,\n    priority: 'HIGH',\n    orderCode: 'ORD-5G-001',\n    customerName: '中国移动',\n    dueDate: '2024-04-05T00:00:00Z'\n  }, {\n    id: '2',\n    length: 30,\n    quantity: 18,\n    priority: 'HIGH',\n    orderCode: 'ORD-5G-002',\n    customerName: '中国联通',\n    dueDate: '2024-04-08T00:00:00Z'\n  }, {\n    id: '3',\n    length: 35,\n    quantity: 8,\n    priority: 'MEDIUM',\n    orderCode: 'ORD-5G-003',\n    customerName: '中国电信',\n    dueDate: '2024-04-10T00:00:00Z'\n  }, {\n    id: '4',\n    length: 20,\n    quantity: 4,\n    priority: 'MEDIUM',\n    orderCode: 'ORD-5G-004',\n    customerName: '华为技术',\n    dueDate: '2024-04-12T00:00:00Z'\n  }];\n  const planColumns = [{\n    title: '计划名称',\n    dataIndex: 'planName',\n    key: 'planName',\n    width: 200,\n    fixed: 'left'\n  }, {\n    title: '物料信息',\n    key: 'material',\n    width: 200,\n    render: record => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: record.materialCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: record.materialName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '规格',\n    dataIndex: 'specification',\n    key: 'specification',\n    width: 120\n  }, {\n    title: '标准长度',\n    key: 'standardLength',\n    width: 100,\n    render: record => `${record.standardLength}${record.unit}`\n  }, {\n    title: '总需求',\n    key: 'totalDemand',\n    width: 100,\n    render: record => `${record.totalDemand}${record.unit}`\n  }, {\n    title: '利用率',\n    dataIndex: 'utilizationRate',\n    key: 'utilizationRate',\n    width: 100,\n    render: value => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: value,\n      size: \"small\",\n      status: value >= 90 ? 'success' : value >= 80 ? 'normal' : 'exception'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '浪费率',\n    dataIndex: 'wasteRate',\n    key: 'wasteRate',\n    width: 100,\n    render: value => /*#__PURE__*/_jsxDEV(Tag, {\n      color: value <= 5 ? 'green' : value <= 10 ? 'orange' : 'red',\n      children: [value.toFixed(1), \"%\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => {\n      const statusConfig = {\n        draft: {\n          color: 'default',\n          text: '草稿'\n        },\n        calculating: {\n          color: 'processing',\n          text: '计算中'\n        },\n        optimized: {\n          color: 'success',\n          text: '已优化'\n        },\n        executing: {\n          color: 'warning',\n          text: '执行中'\n        },\n        completed: {\n          color: 'success',\n          text: '已完成'\n        }\n      };\n      const config = statusConfig[status];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: config.color,\n        children: config.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '计划日期',\n    dataIndex: 'planDate',\n    key: 'planDate',\n    width: 120,\n    render: date => new Date(date).toLocaleDateString()\n  }, {\n    title: '操作',\n    key: 'actions',\n    width: 200,\n    fixed: 'right',\n    render: record => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u65B9\\u6848\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewScheme(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u4F18\\u5316\\u8BA1\\u7B97\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(CalculatorOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleOptimize(record),\n          disabled: record.status === 'calculating'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this), record.status === 'optimized' && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5F00\\u59CB\\u6267\\u884C\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 23\n          }, this),\n          onClick: () => {\n            message.success('切割计划已开始执行');\n            loadData();\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 21\n          }, this),\n          disabled: record.status === 'executing' || record.status === 'completed'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 21\n          }, this),\n          onClick: () => {\n            Modal.confirm({\n              title: '确认删除',\n              content: '确定要删除这个切割计划吗？',\n              onOk: () => {\n                message.success('删除成功');\n                loadData();\n              }\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 9\n    }, this)\n  }];\n  const schemeColumns = [{\n    title: '方案编号',\n    dataIndex: 'schemeNo',\n    key: 'schemeNo',\n    width: 80\n  }, {\n    title: '切割模式',\n    dataIndex: 'pattern',\n    key: 'pattern',\n    width: 200\n  }, {\n    title: '切割长度',\n    dataIndex: 'cuts',\n    key: 'cuts',\n    width: 150,\n    render: cuts => cuts.join(' + ')\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    key: 'quantity',\n    width: 80\n  }, {\n    title: '利用率',\n    dataIndex: 'utilization',\n    key: 'utilization',\n    width: 100,\n    render: value => `${value}%`\n  }, {\n    title: '浪费长度',\n    dataIndex: 'wasteLength',\n    key: 'wasteLength',\n    width: 100,\n    render: (value, record) => `${value}${(selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.unit) || 'M'}`\n  }];\n  const demandColumns = [{\n    title: '长度',\n    dataIndex: 'length',\n    key: 'length',\n    width: 80,\n    render: value => `${value}M`\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    key: 'quantity',\n    width: 80\n  }, {\n    title: '优先级',\n    dataIndex: 'priority',\n    key: 'priority',\n    width: 80,\n    render: priority => /*#__PURE__*/_jsxDEV(Tag, {\n      color: priority === 'HIGH' ? 'red' : priority === 'MEDIUM' ? 'orange' : 'green',\n      children: priority === 'HIGH' ? '高' : priority === 'MEDIUM' ? '中' : '低'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '订单编号',\n    dataIndex: 'orderCode',\n    key: 'orderCode',\n    width: 120\n  }, {\n    title: '客户',\n    dataIndex: 'customerName',\n    key: 'customerName',\n    width: 120\n  }, {\n    title: '交期',\n    dataIndex: 'dueDate',\n    key: 'dueDate',\n    width: 100,\n    render: date => new Date(date).toLocaleDateString()\n  }];\n  const renderStatistics = () => /*#__PURE__*/_jsxDEV(Row, {\n    gutter: [16, 16],\n    style: {\n      marginBottom: 16\n    },\n    children: [/*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u603B\\u8BA1\\u5212\\u6570\",\n          value: mockPlans.length,\n          prefix: /*#__PURE__*/_jsxDEV(ScissorOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u5E73\\u5747\\u5229\\u7528\\u7387\",\n          value: 88.3,\n          suffix: \"%\",\n          valueStyle: {\n            color: '#3f8600'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u603B\\u8282\\u7EA6\\u6210\\u672C\",\n          value: 1250,\n          prefix: \"\\xA5\",\n          precision: 0,\n          valueStyle: {\n            color: '#3f8600'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u6D6A\\u8D39\\u91D1\\u989D\",\n          value: 421.88,\n          prefix: \"\\xA5\",\n          precision: 2,\n          valueStyle: {\n            color: '#cf1322'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 525,\n    columnNumber: 5\n  }, this);\n  const renderPlansTab = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [renderStatistics(), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 23\n              }, this),\n              onClick: handleCreatePlan,\n              children: \"\\u65B0\\u5EFA\\u8BA1\\u5212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u51FA\\u8BA1\\u5212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 29\n              }, this),\n              onClick: loadData,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u5207\\u5272\\u4F18\\u5316\\u63D0\\u793A\",\n        description: \"\\u7CFB\\u7EDF\\u4F1A\\u6839\\u636E\\u9700\\u6C42\\u81EA\\u52A8\\u751F\\u6210\\u6700\\u4F18\\u5207\\u5272\\u65B9\\u6848\\uFF0C\\u5EFA\\u8BAE\\u5B9A\\u671F\\u68C0\\u67E5\\u8BA1\\u5212\\u6267\\u884C\\u60C5\\u51B5\\uFF0C\\u53CA\\u65F6\\u8C03\\u6574\\u4F18\\u5316\\u53C2\\u6570\\u3002\",\n        type: \"info\",\n        showIcon: true,\n        closable: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: planColumns,\n        dataSource: mockPlans,\n        loading: loading,\n        rowKey: \"id\",\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 571,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u5207\\u5272\\u8BA1\\u5212\\u7BA1\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u7BA1\\u7406\\u957F\\u5EA6\\u578B\\u6750\\u6599\\u7684\\u5207\\u5272\\u8BA1\\u5212\\uFF0C\\u4F18\\u5316\\u6750\\u6599\\u5229\\u7528\\u7387\\uFF0C\\u51CF\\u5C11\\u6D6A\\u8D39\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        children: /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(ScissorOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 31\n            }, this), \"\\u5207\\u5272\\u8BA1\\u5212\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 25\n          }, this),\n          children: renderPlansTab()\n        }, \"plans\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u65B0\\u5EFA\\u5207\\u5272\\u8BA1\\u5212\",\n      open: planModalVisible,\n      onOk: handlePlanModalOk,\n      onCancel: () => setPlanModalVisible(false),\n      okText: \"\\u521B\\u5EFA\",\n      cancelText: \"\\u53D6\\u6D88\",\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: planForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"planName\",\n              label: \"\\u8BA1\\u5212\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入计划名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA1\\u5212\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"materialCode\",\n              label: \"\\u7269\\u6599\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请选择物料'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u7269\\u6599\",\n                children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"CABLE-001\",\n                  children: \"CABLE-001 - \\u540C\\u8F74\\u7535\\u7F06\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"STEEL-PLATE-001\",\n                  children: \"STEEL-PLATE-001 - \\u4E0D\\u9508\\u94A2\\u677F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"COPPER-WIRE-001\",\n                  children: \"COPPER-WIRE-001 - \\u94DC\\u7EBF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"standardLength\",\n              label: \"\\u6807\\u51C6\\u957F\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请输入标准长度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u6807\\u51C6\\u957F\\u5EA6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"unit\",\n              label: \"\\u5355\\u4F4D\",\n              rules: [{\n                required: true,\n                message: '请选择单位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5355\\u4F4D\",\n                children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"M\",\n                  children: \"\\u7C73(M)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"MM\",\n                  children: \"\\u6BEB\\u7C73(MM)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"CM\",\n                  children: \"\\u5398\\u7C73(CM)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"unitPrice\",\n              label: \"\\u5355\\u4EF7\",\n              rules: [{\n                required: true,\n                message: '请输入单价'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                precision: 2,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u5355\\u4EF7\",\n                addonBefore: \"\\xA5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"planDate\",\n              label: \"\\u8BA1\\u5212\\u65E5\\u671F\",\n              rules: [{\n                required: true,\n                message: '请选择计划日期'\n              }],\n              children: /*#__PURE__*/_jsxDEV(DatePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"algorithm\",\n              label: \"\\u4F18\\u5316\\u7B97\\u6CD5\",\n              initialValue: \"first_fit\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u4F18\\u5316\\u7B97\\u6CD5\",\n                children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"first_fit\",\n                  children: \"\\u9996\\u6B21\\u9002\\u5E94\\u7B97\\u6CD5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"best_fit\",\n                  children: \"\\u6700\\u4F73\\u9002\\u5E94\\u7B97\\u6CD5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"worst_fit\",\n                  children: \"\\u6700\\u574F\\u9002\\u5E94\\u7B97\\u6CD5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"genetic\",\n                  children: \"\\u9057\\u4F20\\u7B97\\u6CD5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u8BA1\\u5212\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BA1\\u5212\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5207\\u5272\\u4F18\\u5316\\u8BBE\\u7F6E\",\n      open: optimizeModalVisible,\n      onOk: handleOptimizeModalOk,\n      onCancel: () => setOptimizeModalVisible(false),\n      okText: \"\\u5F00\\u59CB\\u4F18\\u5316\",\n      cancelText: \"\\u53D6\\u6D88\",\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: optimizeForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u4F18\\u5316\\u53C2\\u6570\\u8BBE\\u7F6E\",\n          description: \"\\u8C03\\u6574\\u4F18\\u5316\\u53C2\\u6570\\u53EF\\u4EE5\\u83B7\\u5F97\\u4E0D\\u540C\\u7684\\u5207\\u5272\\u65B9\\u6848\\uFF0C\\u5EFA\\u8BAE\\u6839\\u636E\\u5B9E\\u9645\\u751F\\u4EA7\\u60C5\\u51B5\\u9009\\u62E9\\u5408\\u9002\\u7684\\u53C2\\u6570\\u3002\",\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"algorithm\",\n          label: \"\\u4F18\\u5316\\u7B97\\u6CD5\",\n          initialValue: \"best_fit\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u4F18\\u5316\\u7B97\\u6CD5\",\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"first_fit\",\n              children: \"\\u9996\\u6B21\\u9002\\u5E94\\u7B97\\u6CD5 - \\u5FEB\\u901F\\u8BA1\\u7B97\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"best_fit\",\n              children: \"\\u6700\\u4F73\\u9002\\u5E94\\u7B97\\u6CD5 - \\u5E73\\u8861\\u6548\\u679C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"worst_fit\",\n              children: \"\\u6700\\u574F\\u9002\\u5E94\\u7B97\\u6CD5 - \\u51CF\\u5C11\\u4F59\\u6599\\u79CD\\u7C7B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"genetic\",\n              children: \"\\u9057\\u4F20\\u7B97\\u6CD5 - \\u6700\\u4F18\\u89E3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"minUtilization\",\n              label: \"\\u6700\\u5C0F\\u5229\\u7528\\u7387(%)\",\n              initialValue: 85,\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 50,\n                max: 100,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u6700\\u5C0F\\u5229\\u7528\\u7387\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"maxWasteLength\",\n              label: \"\\u6700\\u5927\\u6D6A\\u8D39\\u957F\\u5EA6\",\n              initialValue: 5,\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u6700\\u5927\\u6D6A\\u8D39\\u957F\\u5EA6\",\n                addonAfter: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.unit) || 'M'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"priorityOrder\",\n          label: \"\\u4F18\\u5148\\u7EA7\\u6392\\u5E8F\",\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            options: [{\n              label: '优先满足高优先级订单',\n              value: 'priority'\n            }, {\n              label: '优先满足紧急交期',\n              value: 'dueDate'\n            }, {\n              label: '优先减少浪费',\n              value: 'waste'\n            }, {\n              label: '优先减少方案数量',\n              value: 'schemes'\n            }],\n            defaultValue: ['priority', 'waste']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"allowRemnant\",\n          valuePropName: \"checked\",\n          initialValue: true,\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            children: \"\\u5141\\u8BB8\\u751F\\u6210\\u4F59\\u6599\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 754,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `切割方案详情 - ${selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.planName}`,\n      open: schemeModalVisible,\n      onCancel: () => setSchemeModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setSchemeModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 38\n        }, this),\n        children: \"\\u5BFC\\u51FA\\u65B9\\u6848\"\n      }, \"export\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 54\n        }, this),\n        children: \"\\u6267\\u884C\\u8BA1\\u5212\"\n      }, \"execute\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 846,\n        columnNumber: 11\n      }, this)],\n      width: 1000,\n      children: selectedPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u5229\\u7528\\u7387\",\n              value: selectedPlan.utilizationRate,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#3f8600'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6D6A\\u8D39\\u7387\",\n              value: selectedPlan.wasteRate,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#cf1322'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u6210\\u672C\",\n              value: selectedPlan.totalCost,\n              prefix: \"\\xA5\",\n              precision: 2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6D6A\\u8D39\\u6210\\u672C\",\n              value: selectedPlan.wasteCost,\n              prefix: \"\\xA5\",\n              precision: 2,\n              valueStyle: {\n                color: '#cf1322'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u9700\\u6C42\\u6E05\\u5355\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Table, {\n          columns: demandColumns,\n          dataSource: mockDemands,\n          rowKey: \"id\",\n          size: \"small\",\n          pagination: false,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 891,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u5207\\u5272\\u65B9\\u6848\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Table, {\n          columns: schemeColumns,\n          dataSource: mockSchemes,\n          rowKey: \"id\",\n          size: \"small\",\n          pagination: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 835,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 621,\n    columnNumber: 5\n  }, this);\n};\n_s(CuttingPlanPage, \"EkmChMIL5Ki+8BSdrQeR0RFL1JU=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = CuttingPlanPage;\nexport default CuttingPlanPage;\nvar _c;\n$RefreshReg$(_c, \"CuttingPlanPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Row", "Col", "Statistic", "Typography", "Modal", "Form", "Input", "InputNumber", "Select", "DatePicker", "<PERSON><PERSON>", "Tabs", "Tag", "<PERSON><PERSON><PERSON>", "Progress", "Divider", "message", "Checkbox", "PlusOutlined", "CalculatorOutlined", "ScissorOutlined", "ExportOutlined", "ReloadOutlined", "EyeOutlined", "EditOutlined", "DeleteOutlined", "PlayCircleOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TabPane", "TextArea", "CuttingPlanPage", "_s", "loading", "setLoading", "activeTab", "setActiveTab", "planModalVisible", "setPlanModalVisible", "optimizeModalVisible", "setOptimizeModalVisible", "schemeModalVisible", "setSchemeModalVisible", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "planForm", "useForm", "optimizeForm", "loadData", "setTimeout", "handleCreatePlan", "resetFields", "handleOptimize", "record", "handleViewScheme", "handlePlanModalOk", "validateFields", "then", "values", "console", "log", "success", "handleOptimizeModalOk", "mockPlans", "id", "planName", "materialCode", "materialName", "specification", "standardLength", "unit", "unitPrice", "totalDemand", "planDate", "status", "algorithm", "utilizationRate", "wasteRate", "totalCost", "wasteCost", "created<PERSON>y", "createdDate", "mockSchemes", "planId", "schemeNo", "pattern", "cuts", "quantity", "utilization", "waste", "wasteLength", "mockDemands", "length", "priority", "orderCode", "customerName", "dueDate", "planColumns", "title", "dataIndex", "key", "width", "fixed", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "style", "fontSize", "value", "percent", "size", "color", "toFixed", "statusConfig", "draft", "text", "calculating", "optimized", "executing", "completed", "config", "date", "Date", "toLocaleDateString", "icon", "onClick", "disabled", "danger", "confirm", "content", "onOk", "schemeColumns", "join", "demandColumns", "renderStatistics", "gutter", "marginBottom", "xs", "sm", "prefix", "suffix", "valueStyle", "precision", "renderPlansTab", "justify", "align", "description", "showIcon", "closable", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "level", "margin", "active<PERSON><PERSON>", "onChange", "tab", "open", "onCancel", "okText", "cancelText", "form", "layout", "span", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "Option", "min", "addonBefore", "initialValue", "rows", "max", "addonAfter", "Group", "options", "defaultValue", "valuePropName", "footer", "orientation", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/inventory/CuttingPlanPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Row,\n  Col,\n  Statistic,\n  Typography,\n  Modal,\n  Form,\n  Input,\n  InputNumber,\n  Select,\n  DatePicker,\n  Alert,\n  Tabs,\n  Tag,\n  Tooltip,\n  Progress,\n  Divider,\n  message,\n  Checkbox,\n} from 'antd';\nimport {\n  PlusOutlined,\n  CalculatorOutlined,\n  Sc<PERSON>orOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  ExportOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  CheckCircleOutlined,\n} from '@ant-design/icons';\nimport { formatCurrency } from '../../utils/format';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { TextArea } = Input;\n\ninterface CuttingPlan {\n  id: string;\n  planName: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  standardLength: number;\n  unit: string;\n  unitPrice: number;\n  totalDemand: number;\n  planDate: string;\n  status: 'draft' | 'calculating' | 'optimized' | 'executing' | 'completed';\n  algorithm: string;\n  utilizationRate: number;\n  wasteRate: number;\n  totalCost: number;\n  wasteCost: number;\n  createdBy: string;\n  createdDate: string;\n}\n\ninterface CuttingScheme {\n  id: string;\n  planId: string;\n  schemeNo: number;\n  pattern: string;\n  cuts: number[];\n  quantity: number;\n  utilization: number;\n  waste: number;\n  wasteLength: number;\n}\n\ninterface DemandItem {\n  id: string;\n  length: number;\n  quantity: number;\n  priority: string;\n  orderCode: string;\n  customerName: string;\n  dueDate: string;\n}\n\nconst CuttingPlanPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('plans');\n  const [planModalVisible, setPlanModalVisible] = useState(false);\n  const [optimizeModalVisible, setOptimizeModalVisible] = useState(false);\n  const [schemeModalVisible, setSchemeModalVisible] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState<CuttingPlan | null>(null);\n  const [planForm] = Form.useForm();\n  const [optimizeForm] = Form.useForm();\n\n  const loadData = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n  };\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const handleCreatePlan = () => {\n    setPlanModalVisible(true);\n    planForm.resetFields();\n  };\n\n  const handleOptimize = (record: CuttingPlan) => {\n    setSelectedPlan(record);\n    setOptimizeModalVisible(true);\n    optimizeForm.resetFields();\n  };\n\n  const handleViewScheme = (record: CuttingPlan) => {\n    setSelectedPlan(record);\n    setSchemeModalVisible(true);\n  };\n\n  const handlePlanModalOk = () => {\n    planForm.validateFields().then(values => {\n      console.log('创建切割计划:', values);\n      message.success('切割计划创建成功');\n      setPlanModalVisible(false);\n      loadData();\n    });\n  };\n\n  const handleOptimizeModalOk = () => {\n    optimizeForm.validateFields().then(values => {\n      console.log('优化参数:', values);\n      message.success('切割优化计算已开始，请稍候查看结果');\n      setOptimizeModalVisible(false);\n      loadData();\n    });\n  };\n\n  // 模拟切割计划数据\n  const mockPlans: CuttingPlan[] = [\n    {\n      id: '1',\n      planName: '5G基站电缆切割计划-202403',\n      materialCode: 'CABLE-001',\n      materialName: '同轴电缆',\n      specification: 'RG-58/U',\n      standardLength: 100,\n      unit: 'M',\n      unitPrice: 4.5,\n      totalDemand: 1250,\n      planDate: '2024-03-25T00:00:00Z',\n      status: 'optimized',\n      algorithm: '首次适应算法',\n      utilizationRate: 92.5,\n      wasteRate: 7.5,\n      totalCost: 5625,\n      wasteCost: 421.88,\n      createdBy: '工艺工程师',\n      createdDate: '2024-03-20T00:00:00Z',\n    },\n    {\n      id: '2',\n      planName: '不锈钢板切割计划-202403',\n      materialCode: 'STEEL-PLATE-001',\n      materialName: '不锈钢板',\n      specification: '304-2mm',\n      standardLength: 2000,\n      unit: 'MM',\n      unitPrice: 0.15,\n      totalDemand: 8500,\n      planDate: '2024-03-28T00:00:00Z',\n      status: 'calculating',\n      algorithm: '最佳适应算法',\n      utilizationRate: 0,\n      wasteRate: 0,\n      totalCost: 1275,\n      wasteCost: 0,\n      createdBy: '生产计划员',\n      createdDate: '2024-03-22T00:00:00Z',\n    },\n    {\n      id: '3',\n      planName: '铜线切割计划-202403',\n      materialCode: 'COPPER-WIRE-001',\n      materialName: '铜线',\n      specification: '2.5mm²',\n      standardLength: 500,\n      unit: 'M',\n      unitPrice: 8.2,\n      totalDemand: 2800,\n      planDate: '2024-03-30T00:00:00Z',\n      status: 'draft',\n      algorithm: '',\n      utilizationRate: 0,\n      wasteRate: 0,\n      totalCost: 22960,\n      wasteCost: 0,\n      createdBy: '工艺工程师',\n      createdDate: '2024-03-24T00:00:00Z',\n    },\n  ];\n\n  // 模拟切割方案数据\n  const mockSchemes: CuttingScheme[] = [\n    {\n      id: '1',\n      planId: '1',\n      schemeNo: 1,\n      pattern: '25M + 25M + 25M + 25M',\n      cuts: [25, 25, 25, 25],\n      quantity: 8,\n      utilization: 100,\n      waste: 0,\n      wasteLength: 0,\n    },\n    {\n      id: '2',\n      planId: '1',\n      schemeNo: 2,\n      pattern: '30M + 30M + 30M + 10M',\n      cuts: [30, 30, 30, 10],\n      quantity: 6,\n      utilization: 100,\n      waste: 0,\n      wasteLength: 0,\n    },\n    {\n      id: '3',\n      planId: '1',\n      schemeNo: 3,\n      pattern: '35M + 35M + 20M + 8M',\n      cuts: [35, 35, 20, 8],\n      quantity: 4,\n      utilization: 98,\n      waste: 2,\n      wasteLength: 2,\n    },\n  ];\n\n  // 模拟需求数据\n  const mockDemands: DemandItem[] = [\n    {\n      id: '1',\n      length: 25,\n      quantity: 32,\n      priority: 'HIGH',\n      orderCode: 'ORD-5G-001',\n      customerName: '中国移动',\n      dueDate: '2024-04-05T00:00:00Z',\n    },\n    {\n      id: '2',\n      length: 30,\n      quantity: 18,\n      priority: 'HIGH',\n      orderCode: 'ORD-5G-002',\n      customerName: '中国联通',\n      dueDate: '2024-04-08T00:00:00Z',\n    },\n    {\n      id: '3',\n      length: 35,\n      quantity: 8,\n      priority: 'MEDIUM',\n      orderCode: 'ORD-5G-003',\n      customerName: '中国电信',\n      dueDate: '2024-04-10T00:00:00Z',\n    },\n    {\n      id: '4',\n      length: 20,\n      quantity: 4,\n      priority: 'MEDIUM',\n      orderCode: 'ORD-5G-004',\n      customerName: '华为技术',\n      dueDate: '2024-04-12T00:00:00Z',\n    },\n  ];\n\n  const planColumns = [\n    {\n      title: '计划名称',\n      dataIndex: 'planName',\n      key: 'planName',\n      width: 200,\n      fixed: 'left' as const,\n    },\n    {\n      title: '物料信息',\n      key: 'material',\n      width: 200,\n      render: (record: CuttingPlan) => (\n        <div>\n          <div>{record.materialCode}</div>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.materialName}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '规格',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 120,\n    },\n    {\n      title: '标准长度',\n      key: 'standardLength',\n      width: 100,\n      render: (record: CuttingPlan) => `${record.standardLength}${record.unit}`,\n    },\n    {\n      title: '总需求',\n      key: 'totalDemand',\n      width: 100,\n      render: (record: CuttingPlan) => `${record.totalDemand}${record.unit}`,\n    },\n    {\n      title: '利用率',\n      dataIndex: 'utilizationRate',\n      key: 'utilizationRate',\n      width: 100,\n      render: (value: number) => (\n        <Progress\n          percent={value}\n          size=\"small\"\n          status={value >= 90 ? 'success' : value >= 80 ? 'normal' : 'exception'}\n        />\n      ),\n    },\n    {\n      title: '浪费率',\n      dataIndex: 'wasteRate',\n      key: 'wasteRate',\n      width: 100,\n      render: (value: number) => (\n        <Tag color={value <= 5 ? 'green' : value <= 10 ? 'orange' : 'red'}>\n          {value.toFixed(1)}%\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => {\n        const statusConfig = {\n          draft: { color: 'default', text: '草稿' },\n          calculating: { color: 'processing', text: '计算中' },\n          optimized: { color: 'success', text: '已优化' },\n          executing: { color: 'warning', text: '执行中' },\n          completed: { color: 'success', text: '已完成' },\n        };\n        const config = statusConfig[status as keyof typeof statusConfig];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n    {\n      title: '计划日期',\n      dataIndex: 'planDate',\n      key: 'planDate',\n      width: 120,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 200,\n      fixed: 'right' as const,\n      render: (record: CuttingPlan) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看方案\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewScheme(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"优化计算\">\n            <Button\n              type=\"text\"\n              icon={<CalculatorOutlined />}\n              onClick={() => handleOptimize(record)}\n              disabled={record.status === 'calculating'}\n            />\n          </Tooltip>\n          {record.status === 'optimized' && (\n            <Tooltip title=\"开始执行\">\n              <Button\n                type=\"text\"\n                icon={<PlayCircleOutlined />}\n                onClick={() => {\n                  message.success('切割计划已开始执行');\n                  loadData();\n                }}\n              />\n            </Tooltip>\n          )}\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              disabled={record.status === 'executing' || record.status === 'completed'}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => {\n                Modal.confirm({\n                  title: '确认删除',\n                  content: '确定要删除这个切割计划吗？',\n                  onOk: () => {\n                    message.success('删除成功');\n                    loadData();\n                  },\n                });\n              }}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  const schemeColumns = [\n    {\n      title: '方案编号',\n      dataIndex: 'schemeNo',\n      key: 'schemeNo',\n      width: 80,\n    },\n    {\n      title: '切割模式',\n      dataIndex: 'pattern',\n      key: 'pattern',\n      width: 200,\n    },\n    {\n      title: '切割长度',\n      dataIndex: 'cuts',\n      key: 'cuts',\n      width: 150,\n      render: (cuts: number[]) => cuts.join(' + '),\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n    },\n    {\n      title: '利用率',\n      dataIndex: 'utilization',\n      key: 'utilization',\n      width: 100,\n      render: (value: number) => `${value}%`,\n    },\n    {\n      title: '浪费长度',\n      dataIndex: 'wasteLength',\n      key: 'wasteLength',\n      width: 100,\n      render: (value: number, record: CuttingScheme) => \n        `${value}${selectedPlan?.unit || 'M'}`,\n    },\n  ];\n\n  const demandColumns = [\n    {\n      title: '长度',\n      dataIndex: 'length',\n      key: 'length',\n      width: 80,\n      render: (value: number) => `${value}M`,\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n    },\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      width: 80,\n      render: (priority: string) => (\n        <Tag color={priority === 'HIGH' ? 'red' : priority === 'MEDIUM' ? 'orange' : 'green'}>\n          {priority === 'HIGH' ? '高' : priority === 'MEDIUM' ? '中' : '低'}\n        </Tag>\n      ),\n    },\n    {\n      title: '订单编号',\n      dataIndex: 'orderCode',\n      key: 'orderCode',\n      width: 120,\n    },\n    {\n      title: '客户',\n      dataIndex: 'customerName',\n      key: 'customerName',\n      width: 120,\n    },\n    {\n      title: '交期',\n      dataIndex: 'dueDate',\n      key: 'dueDate',\n      width: 100,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n  ];\n\n  const renderStatistics = () => (\n    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n      <Col xs={12} sm={6}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"总计划数\"\n            value={mockPlans.length}\n            prefix={<ScissorOutlined />}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={6}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"平均利用率\"\n            value={88.3}\n            suffix=\"%\"\n            valueStyle={{ color: '#3f8600' }}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={6}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"总节约成本\"\n            value={1250}\n            prefix=\"¥\"\n            precision={0}\n            valueStyle={{ color: '#3f8600' }}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={6}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"浪费金额\"\n            value={421.88}\n            prefix=\"¥\"\n            precision={2}\n            valueStyle={{ color: '#cf1322' }}\n          />\n        </Card>\n      </Col>\n    </Row>\n  );\n\n  const renderPlansTab = () => (\n    <div>\n      {renderStatistics()}\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={handleCreatePlan}\n              >\n                新建计划\n              </Button>\n              <Button icon={<ExportOutlined />}>\n                导出计划\n              </Button>\n              <Button icon={<ReloadOutlined />} onClick={loadData}>\n                刷新\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Alert\n          message=\"切割优化提示\"\n          description=\"系统会根据需求自动生成最优切割方案，建议定期检查计划执行情况，及时调整优化参数。\"\n          type=\"info\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n\n        <Table\n          columns={planColumns}\n          dataSource={mockPlans}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              切割计划管理\n            </Title>\n            <Text type=\"secondary\">\n              管理长度型材料的切割计划，优化材料利用率，减少浪费\n            </Text>\n          </Col>\n        </Row>\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab={<span><ScissorOutlined />切割计划</span>} key=\"plans\">\n            {renderPlansTab()}\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 新建计划模态框 */}\n      <Modal\n        title=\"新建切割计划\"\n        open={planModalVisible}\n        onOk={handlePlanModalOk}\n        onCancel={() => setPlanModalVisible(false)}\n        okText=\"创建\"\n        cancelText=\"取消\"\n        width={800}\n      >\n        <Form form={planForm} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"planName\"\n                label=\"计划名称\"\n                rules={[{ required: true, message: '请输入计划名称' }]}\n              >\n                <Input placeholder=\"请输入计划名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"materialCode\"\n                label=\"物料编码\"\n                rules={[{ required: true, message: '请选择物料' }]}\n              >\n                <Select placeholder=\"请选择物料\">\n                  <Select.Option value=\"CABLE-001\">CABLE-001 - 同轴电缆</Select.Option>\n                  <Select.Option value=\"STEEL-PLATE-001\">STEEL-PLATE-001 - 不锈钢板</Select.Option>\n                  <Select.Option value=\"COPPER-WIRE-001\">COPPER-WIRE-001 - 铜线</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"standardLength\"\n                label=\"标准长度\"\n                rules={[{ required: true, message: '请输入标准长度' }]}\n              >\n                <InputNumber\n                  min={1}\n                  style={{ width: '100%' }}\n                  placeholder=\"标准长度\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"unit\"\n                label=\"单位\"\n                rules={[{ required: true, message: '请选择单位' }]}\n              >\n                <Select placeholder=\"请选择单位\">\n                  <Select.Option value=\"M\">米(M)</Select.Option>\n                  <Select.Option value=\"MM\">毫米(MM)</Select.Option>\n                  <Select.Option value=\"CM\">厘米(CM)</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"unitPrice\"\n                label=\"单价\"\n                rules={[{ required: true, message: '请输入单价' }]}\n              >\n                <InputNumber\n                  min={0}\n                  precision={2}\n                  style={{ width: '100%' }}\n                  placeholder=\"单价\"\n                  addonBefore=\"¥\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"planDate\"\n                label=\"计划日期\"\n                rules={[{ required: true, message: '请选择计划日期' }]}\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"algorithm\"\n                label=\"优化算法\"\n                initialValue=\"first_fit\"\n              >\n                <Select placeholder=\"请选择优化算法\">\n                  <Select.Option value=\"first_fit\">首次适应算法</Select.Option>\n                  <Select.Option value=\"best_fit\">最佳适应算法</Select.Option>\n                  <Select.Option value=\"worst_fit\">最坏适应算法</Select.Option>\n                  <Select.Option value=\"genetic\">遗传算法</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"description\" label=\"计划描述\">\n            <TextArea rows={3} placeholder=\"请输入计划描述\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 优化计算模态框 */}\n      <Modal\n        title=\"切割优化设置\"\n        open={optimizeModalVisible}\n        onOk={handleOptimizeModalOk}\n        onCancel={() => setOptimizeModalVisible(false)}\n        okText=\"开始优化\"\n        cancelText=\"取消\"\n        width={600}\n      >\n        <Form form={optimizeForm} layout=\"vertical\">\n          <Alert\n            message=\"优化参数设置\"\n            description=\"调整优化参数可以获得不同的切割方案，建议根据实际生产情况选择合适的参数。\"\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n\n          <Form.Item\n            name=\"algorithm\"\n            label=\"优化算法\"\n            initialValue=\"best_fit\"\n          >\n            <Select placeholder=\"请选择优化算法\">\n              <Select.Option value=\"first_fit\">首次适应算法 - 快速计算</Select.Option>\n              <Select.Option value=\"best_fit\">最佳适应算法 - 平衡效果</Select.Option>\n              <Select.Option value=\"worst_fit\">最坏适应算法 - 减少余料种类</Select.Option>\n              <Select.Option value=\"genetic\">遗传算法 - 最优解</Select.Option>\n            </Select>\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"minUtilization\"\n                label=\"最小利用率(%)\"\n                initialValue={85}\n              >\n                <InputNumber\n                  min={50}\n                  max={100}\n                  style={{ width: '100%' }}\n                  placeholder=\"最小利用率\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"maxWasteLength\"\n                label=\"最大浪费长度\"\n                initialValue={5}\n              >\n                <InputNumber\n                  min={0}\n                  style={{ width: '100%' }}\n                  placeholder=\"最大浪费长度\"\n                  addonAfter={selectedPlan?.unit || 'M'}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"priorityOrder\" label=\"优先级排序\">\n            <Checkbox.Group\n              options={[\n                { label: '优先满足高优先级订单', value: 'priority' },\n                { label: '优先满足紧急交期', value: 'dueDate' },\n                { label: '优先减少浪费', value: 'waste' },\n                { label: '优先减少方案数量', value: 'schemes' },\n              ]}\n              defaultValue={['priority', 'waste']}\n            />\n          </Form.Item>\n\n          <Form.Item name=\"allowRemnant\" valuePropName=\"checked\" initialValue={true}>\n            <Checkbox>允许生成余料</Checkbox>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 切割方案模态框 */}\n      <Modal\n        title={`切割方案详情 - ${selectedPlan?.planName}`}\n        open={schemeModalVisible}\n        onCancel={() => setSchemeModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setSchemeModalVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"export\" icon={<ExportOutlined />}>\n            导出方案\n          </Button>,\n          <Button key=\"execute\" type=\"primary\" icon={<PlayCircleOutlined />}>\n            执行计划\n          </Button>,\n        ]}\n        width={1000}\n      >\n        {selectedPlan && (\n          <div>\n            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n              <Col span={6}>\n                <Statistic\n                  title=\"总利用率\"\n                  value={selectedPlan.utilizationRate}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#3f8600' }}\n                />\n              </Col>\n              <Col span={6}>\n                <Statistic\n                  title=\"浪费率\"\n                  value={selectedPlan.wasteRate}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#cf1322' }}\n                />\n              </Col>\n              <Col span={6}>\n                <Statistic\n                  title=\"总成本\"\n                  value={selectedPlan.totalCost}\n                  prefix=\"¥\"\n                  precision={2}\n                />\n              </Col>\n              <Col span={6}>\n                <Statistic\n                  title=\"浪费成本\"\n                  value={selectedPlan.wasteCost}\n                  prefix=\"¥\"\n                  precision={2}\n                  valueStyle={{ color: '#cf1322' }}\n                />\n              </Col>\n            </Row>\n\n            <Divider orientation=\"left\">需求清单</Divider>\n            <Table\n              columns={demandColumns}\n              dataSource={mockDemands}\n              rowKey=\"id\"\n              size=\"small\"\n              pagination={false}\n              style={{ marginBottom: 16 }}\n            />\n\n            <Divider orientation=\"left\">切割方案</Divider>\n            <Table\n              columns={schemeColumns}\n              dataSource={mockSchemes}\n              rowKey=\"id\"\n              size=\"small\"\n              pagination={false}\n            />\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default CuttingPlanPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,kBAAkB,EAClBC,eAAe,EAEfC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,kBAAkB,QAGb,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG3B,UAAU;AAClC,MAAM;EAAE4B;AAAQ,CAAC,GAAGpB,IAAI;AACxB,MAAM;EAAEqB;AAAS,CAAC,GAAG1B,KAAK;AA6C1B,MAAM2B,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACiD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAqB,IAAI,CAAC;EAC1E,MAAM,CAACqD,QAAQ,CAAC,GAAG1C,IAAI,CAAC2C,OAAO,CAAC,CAAC;EACjC,MAAM,CAACC,YAAY,CAAC,GAAG5C,IAAI,CAAC2C,OAAO,CAAC,CAAC;EAErC,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrBd,UAAU,CAAC,IAAI,CAAC;IAChBe,UAAU,CAAC,MAAM;MACff,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAEDzC,SAAS,CAAC,MAAM;IACduD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7BZ,mBAAmB,CAAC,IAAI,CAAC;IACzBO,QAAQ,CAACM,WAAW,CAAC,CAAC;EACxB,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAmB,IAAK;IAC9CT,eAAe,CAACS,MAAM,CAAC;IACvBb,uBAAuB,CAAC,IAAI,CAAC;IAC7BO,YAAY,CAACI,WAAW,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMG,gBAAgB,GAAID,MAAmB,IAAK;IAChDT,eAAe,CAACS,MAAM,CAAC;IACvBX,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMa,iBAAiB,GAAGA,CAAA,KAAM;IAC9BV,QAAQ,CAACW,cAAc,CAAC,CAAC,CAACC,IAAI,CAACC,MAAM,IAAI;MACvCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,MAAM,CAAC;MAC9B5C,OAAO,CAAC+C,OAAO,CAAC,UAAU,CAAC;MAC3BvB,mBAAmB,CAAC,KAAK,CAAC;MAC1BU,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMc,qBAAqB,GAAGA,CAAA,KAAM;IAClCf,YAAY,CAACS,cAAc,CAAC,CAAC,CAACC,IAAI,CAACC,MAAM,IAAI;MAC3CC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;MAC5B5C,OAAO,CAAC+C,OAAO,CAAC,mBAAmB,CAAC;MACpCrB,uBAAuB,CAAC,KAAK,CAAC;MAC9BQ,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMe,SAAwB,GAAG,CAC/B;IACEC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,mBAAmB;IAC7BC,YAAY,EAAE,WAAW;IACzBC,YAAY,EAAE,MAAM;IACpBC,aAAa,EAAE,SAAS;IACxBC,cAAc,EAAE,GAAG;IACnBC,IAAI,EAAE,GAAG;IACTC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,sBAAsB;IAChCC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE,QAAQ;IACnBC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,OAAO;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEjB,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,iBAAiB;IAC3BC,YAAY,EAAE,iBAAiB;IAC/BC,YAAY,EAAE,MAAM;IACpBC,aAAa,EAAE,SAAS;IACxBC,cAAc,EAAE,IAAI;IACpBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,sBAAsB;IAChCC,MAAM,EAAE,aAAa;IACrBC,SAAS,EAAE,QAAQ;IACnBC,eAAe,EAAE,CAAC;IAClBC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,OAAO;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEjB,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,eAAe;IACzBC,YAAY,EAAE,iBAAiB;IAC/BC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,GAAG;IACnBC,IAAI,EAAE,GAAG;IACTC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,sBAAsB;IAChCC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,EAAE;IACbC,eAAe,EAAE,CAAC;IAClBC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,OAAO;IAClBC,WAAW,EAAE;EACf,CAAC,CACF;;EAED;EACA,MAAMC,WAA4B,GAAG,CACnC;IACElB,EAAE,EAAE,GAAG;IACPmB,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,uBAAuB;IAChCC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACtBC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE,GAAG;IAChBC,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE;EACf,CAAC,EACD;IACE1B,EAAE,EAAE,GAAG;IACPmB,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,uBAAuB;IAChCC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACtBC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE,GAAG;IAChBC,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE;EACf,CAAC,EACD;IACE1B,EAAE,EAAE,GAAG;IACPmB,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,sBAAsB;IAC/BC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACrBC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE;EACf,CAAC,CACF;;EAED;EACA,MAAMC,WAAyB,GAAG,CAChC;IACE3B,EAAE,EAAE,GAAG;IACP4B,MAAM,EAAE,EAAE;IACVL,QAAQ,EAAE,EAAE;IACZM,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE;EACX,CAAC,EACD;IACEhC,EAAE,EAAE,GAAG;IACP4B,MAAM,EAAE,EAAE;IACVL,QAAQ,EAAE,EAAE;IACZM,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE;EACX,CAAC,EACD;IACEhC,EAAE,EAAE,GAAG;IACP4B,MAAM,EAAE,EAAE;IACVL,QAAQ,EAAE,CAAC;IACXM,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE;EACX,CAAC,EACD;IACEhC,EAAE,EAAE,GAAG;IACP4B,MAAM,EAAE,EAAE;IACVL,QAAQ,EAAE,CAAC;IACXM,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGlD,MAAmB,iBAC1B3B,OAAA;MAAA8E,QAAA,gBACE9E,OAAA;QAAA8E,QAAA,EAAMnD,MAAM,CAACa;MAAY;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChClF,OAAA,CAACE,IAAI;QAACiF,IAAI,EAAC,WAAW;QAACC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAChDnD,MAAM,CAACc;MAAY;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGlD,MAAmB,IAAK,GAAGA,MAAM,CAACgB,cAAc,GAAGhB,MAAM,CAACiB,IAAI;EACzE,CAAC,EACD;IACE4B,KAAK,EAAE,KAAK;IACZE,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGlD,MAAmB,IAAK,GAAGA,MAAM,CAACmB,WAAW,GAAGnB,MAAM,CAACiB,IAAI;EACtE,CAAC,EACD;IACE4B,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGS,KAAa,iBACpBtF,OAAA,CAACd,QAAQ;MACPqG,OAAO,EAAED,KAAM;MACfE,IAAI,EAAC,OAAO;MACZxC,MAAM,EAAEsC,KAAK,IAAI,EAAE,GAAG,SAAS,GAAGA,KAAK,IAAI,EAAE,GAAG,QAAQ,GAAG;IAAY;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE;EAEL,CAAC,EACD;IACEV,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGS,KAAa,iBACpBtF,OAAA,CAAChB,GAAG;MAACyG,KAAK,EAAEH,KAAK,IAAI,CAAC,GAAG,OAAO,GAAGA,KAAK,IAAI,EAAE,GAAG,QAAQ,GAAG,KAAM;MAAAR,QAAA,GAC/DQ,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,EAAC,GACpB;IAAA;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAET,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAG7B,MAAc,IAAK;MAC1B,MAAM2C,YAAY,GAAG;QACnBC,KAAK,EAAE;UAAEH,KAAK,EAAE,SAAS;UAAEI,IAAI,EAAE;QAAK,CAAC;QACvCC,WAAW,EAAE;UAAEL,KAAK,EAAE,YAAY;UAAEI,IAAI,EAAE;QAAM,CAAC;QACjDE,SAAS,EAAE;UAAEN,KAAK,EAAE,SAAS;UAAEI,IAAI,EAAE;QAAM,CAAC;QAC5CG,SAAS,EAAE;UAAEP,KAAK,EAAE,SAAS;UAAEI,IAAI,EAAE;QAAM,CAAC;QAC5CI,SAAS,EAAE;UAAER,KAAK,EAAE,SAAS;UAAEI,IAAI,EAAE;QAAM;MAC7C,CAAC;MACD,MAAMK,MAAM,GAAGP,YAAY,CAAC3C,MAAM,CAA8B;MAChE,oBAAOhD,OAAA,CAAChB,GAAG;QAACyG,KAAK,EAAES,MAAM,CAACT,KAAM;QAAAX,QAAA,EAAEoB,MAAM,CAACL;MAAI;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACtD;EACF,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGsB,IAAY,IAAK,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC;EAC9D,CAAC,EACD;IACE7B,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAgB;IACvBC,MAAM,EAAGlD,MAAmB,iBAC1B3B,OAAA,CAAC7B,KAAK;MAACqH,IAAI,EAAC,OAAO;MAAAV,QAAA,gBACjB9E,OAAA,CAACf,OAAO;QAACuF,KAAK,EAAC,0BAAM;QAAAM,QAAA,eACnB9E,OAAA,CAAC9B,MAAM;UACLiH,IAAI,EAAC,MAAM;UACXmB,IAAI,eAAEtG,OAAA,CAACL,WAAW;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBqB,OAAO,EAAEA,CAAA,KAAM3E,gBAAgB,CAACD,MAAM;QAAE;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlF,OAAA,CAACf,OAAO;QAACuF,KAAK,EAAC,0BAAM;QAAAM,QAAA,eACnB9E,OAAA,CAAC9B,MAAM;UACLiH,IAAI,EAAC,MAAM;UACXmB,IAAI,eAAEtG,OAAA,CAACT,kBAAkB;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BqB,OAAO,EAAEA,CAAA,KAAM7E,cAAc,CAACC,MAAM,CAAE;UACtC6E,QAAQ,EAAE7E,MAAM,CAACqB,MAAM,KAAK;QAAc;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EACTvD,MAAM,CAACqB,MAAM,KAAK,WAAW,iBAC5BhD,OAAA,CAACf,OAAO;QAACuF,KAAK,EAAC,0BAAM;QAAAM,QAAA,eACnB9E,OAAA,CAAC9B,MAAM;UACLiH,IAAI,EAAC,MAAM;UACXmB,IAAI,eAAEtG,OAAA,CAACF,kBAAkB;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BqB,OAAO,EAAEA,CAAA,KAAM;YACbnH,OAAO,CAAC+C,OAAO,CAAC,WAAW,CAAC;YAC5Bb,QAAQ,CAAC,CAAC;UACZ;QAAE;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CACV,eACDlF,OAAA,CAACf,OAAO;QAACuF,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB9E,OAAA,CAAC9B,MAAM;UACLiH,IAAI,EAAC,MAAM;UACXmB,IAAI,eAAEtG,OAAA,CAACJ,YAAY;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBsB,QAAQ,EAAE7E,MAAM,CAACqB,MAAM,KAAK,WAAW,IAAIrB,MAAM,CAACqB,MAAM,KAAK;QAAY;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlF,OAAA,CAACf,OAAO;QAACuF,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB9E,OAAA,CAAC9B,MAAM;UACLiH,IAAI,EAAC,MAAM;UACXsB,MAAM;UACNH,IAAI,eAAEtG,OAAA,CAACH,cAAc;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBqB,OAAO,EAAEA,CAAA,KAAM;YACb/H,KAAK,CAACkI,OAAO,CAAC;cACZlC,KAAK,EAAE,MAAM;cACbmC,OAAO,EAAE,eAAe;cACxBC,IAAI,EAAEA,CAAA,KAAM;gBACVxH,OAAO,CAAC+C,OAAO,CAAC,MAAM,CAAC;gBACvBb,QAAQ,CAAC,CAAC;cACZ;YACF,CAAC,CAAC;UACJ;QAAE;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;EAED,MAAM2B,aAAa,GAAG,CACpB;IACErC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGjB,IAAc,IAAKA,IAAI,CAACkD,IAAI,CAAC,KAAK;EAC7C,CAAC,EACD;IACEtC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGS,KAAa,IAAK,GAAGA,KAAK;EACrC,CAAC,EACD;IACEd,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACS,KAAa,EAAE3D,MAAqB,KAC3C,GAAG2D,KAAK,GAAG,CAAArE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE2B,IAAI,KAAI,GAAG;EACxC,CAAC,CACF;EAED,MAAMmE,aAAa,GAAG,CACpB;IACEvC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGS,KAAa,IAAK,GAAGA,KAAK;EACrC,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGV,QAAgB,iBACvBnE,OAAA,CAAChB,GAAG;MAACyG,KAAK,EAAEtB,QAAQ,KAAK,MAAM,GAAG,KAAK,GAAGA,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAQ;MAAAW,QAAA,EAClFX,QAAQ,KAAK,MAAM,GAAG,GAAG,GAAGA,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG;IAAG;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D;EAET,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGsB,IAAY,IAAK,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC;EAC9D,CAAC,CACF;EAED,MAAMW,gBAAgB,GAAGA,CAAA,kBACvBhH,OAAA,CAAC5B,GAAG;IAAC6I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;IAAC7B,KAAK,EAAE;MAAE8B,YAAY,EAAE;IAAG,CAAE;IAAApC,QAAA,gBACjD9E,OAAA,CAAC3B,GAAG;MAAC8I,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAtC,QAAA,eACjB9E,OAAA,CAAChC,IAAI;QAACwH,IAAI,EAAC,OAAO;QAAAV,QAAA,eAChB9E,OAAA,CAAC1B,SAAS;UACRkG,KAAK,EAAC,0BAAM;UACZc,KAAK,EAAEjD,SAAS,CAAC6B,MAAO;UACxBmD,MAAM,eAAErH,OAAA,CAACR,eAAe;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNlF,OAAA,CAAC3B,GAAG;MAAC8I,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAtC,QAAA,eACjB9E,OAAA,CAAChC,IAAI;QAACwH,IAAI,EAAC,OAAO;QAAAV,QAAA,eAChB9E,OAAA,CAAC1B,SAAS;UACRkG,KAAK,EAAC,gCAAO;UACbc,KAAK,EAAE,IAAK;UACZgC,MAAM,EAAC,GAAG;UACVC,UAAU,EAAE;YAAE9B,KAAK,EAAE;UAAU;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNlF,OAAA,CAAC3B,GAAG;MAAC8I,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAtC,QAAA,eACjB9E,OAAA,CAAChC,IAAI;QAACwH,IAAI,EAAC,OAAO;QAAAV,QAAA,eAChB9E,OAAA,CAAC1B,SAAS;UACRkG,KAAK,EAAC,gCAAO;UACbc,KAAK,EAAE,IAAK;UACZ+B,MAAM,EAAC,MAAG;UACVG,SAAS,EAAE,CAAE;UACbD,UAAU,EAAE;YAAE9B,KAAK,EAAE;UAAU;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNlF,OAAA,CAAC3B,GAAG;MAAC8I,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAtC,QAAA,eACjB9E,OAAA,CAAChC,IAAI;QAACwH,IAAI,EAAC,OAAO;QAAAV,QAAA,eAChB9E,OAAA,CAAC1B,SAAS;UACRkG,KAAK,EAAC,0BAAM;UACZc,KAAK,EAAE,MAAO;UACd+B,MAAM,EAAC,MAAG;UACVG,SAAS,EAAE,CAAE;UACbD,UAAU,EAAE;YAAE9B,KAAK,EAAE;UAAU;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMuC,cAAc,GAAGA,CAAA,kBACrBzH,OAAA;IAAA8E,QAAA,GACGkC,gBAAgB,CAAC,CAAC,eACnBhH,OAAA,CAAChC,IAAI;MAAA8G,QAAA,gBACH9E,OAAA,CAAC5B,GAAG;QAACsJ,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACvC,KAAK,EAAE;UAAE8B,YAAY,EAAE;QAAG,CAAE;QAAApC,QAAA,eACtE9E,OAAA,CAAC3B,GAAG;UAAAyG,QAAA,eACF9E,OAAA,CAAC7B,KAAK;YAAA2G,QAAA,gBACJ9E,OAAA,CAAC9B,MAAM;cACLiH,IAAI,EAAC,SAAS;cACdmB,IAAI,eAAEtG,OAAA,CAACV,YAAY;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBqB,OAAO,EAAE/E,gBAAiB;cAAAsD,QAAA,EAC3B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlF,OAAA,CAAC9B,MAAM;cAACoI,IAAI,eAAEtG,OAAA,CAACP,cAAc;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlF,OAAA,CAAC9B,MAAM;cAACoI,IAAI,eAAEtG,OAAA,CAACN,cAAc;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACqB,OAAO,EAAEjF,QAAS;cAAAwD,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlF,OAAA,CAAClB,KAAK;QACJM,OAAO,EAAC,sCAAQ;QAChBwI,WAAW,EAAC,kPAA0C;QACtDzC,IAAI,EAAC,MAAM;QACX0C,QAAQ;QACRC,QAAQ;QACR1C,KAAK,EAAE;UAAE8B,YAAY,EAAE;QAAG;MAAE;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEFlF,OAAA,CAAC/B,KAAK;QACJ8J,OAAO,EAAExD,WAAY;QACrByD,UAAU,EAAE3F,SAAU;QACtB9B,OAAO,EAAEA,OAAQ;QACjB0H,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,oBACElF,OAAA;IAAA8E,QAAA,gBACE9E,OAAA,CAAChC,IAAI;MAAA8G,QAAA,gBACH9E,OAAA,CAAC5B,GAAG;QAACsJ,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACvC,KAAK,EAAE;UAAE8B,YAAY,EAAE;QAAG,CAAE;QAAApC,QAAA,eACtE9E,OAAA,CAAC3B,GAAG;UAAAyG,QAAA,gBACF9E,OAAA,CAACC,KAAK;YAACyI,KAAK,EAAE,CAAE;YAACtD,KAAK,EAAE;cAAEuD,MAAM,EAAE;YAAE,CAAE;YAAA7D,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlF,OAAA,CAACE,IAAI;YAACiF,IAAI,EAAC,WAAW;YAAAL,QAAA,EAAC;UAEvB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlF,OAAA,CAACjB,IAAI;QAAC6J,SAAS,EAAEnI,SAAU;QAACoI,QAAQ,EAAEnI,YAAa;QAAAoE,QAAA,eACjD9E,OAAA,CAACG,OAAO;UAAC2I,GAAG,eAAE9I,OAAA;YAAA8E,QAAA,gBAAM9E,OAAA,CAACR,eAAe;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAJ,QAAA,EAChD2C,cAAc,CAAC;QAAC,GADqC,OAAO;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPlF,OAAA,CAACxB,KAAK;MACJgG,KAAK,EAAC,sCAAQ;MACduE,IAAI,EAAEpI,gBAAiB;MACvBiG,IAAI,EAAE/E,iBAAkB;MACxBmH,QAAQ,EAAEA,CAAA,KAAMpI,mBAAmB,CAAC,KAAK,CAAE;MAC3CqI,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MACfvE,KAAK,EAAE,GAAI;MAAAG,QAAA,eAEX9E,OAAA,CAACvB,IAAI;QAAC0K,IAAI,EAAEhI,QAAS;QAACiI,MAAM,EAAC,UAAU;QAAAtE,QAAA,gBACrC9E,OAAA,CAAC5B,GAAG;UAAC6I,MAAM,EAAE,EAAG;UAAAnC,QAAA,gBACd9E,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,EAAG;YAAAvE,QAAA,eACZ9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA0F,QAAA,eAEhD9E,OAAA,CAACtB,KAAK;gBAACiL,WAAW,EAAC;cAAS;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,EAAG;YAAAvE,QAAA,eACZ9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtK,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA0F,QAAA,eAE9C9E,OAAA,CAACpB,MAAM;gBAAC+K,WAAW,EAAC,gCAAO;gBAAA7E,QAAA,gBACzB9E,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,WAAW;kBAAAR,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eACjElF,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,iBAAiB;kBAAAR,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC7ElF,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,iBAAiB;kBAAAR,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAAC5B,GAAG;UAAC6I,MAAM,EAAE,EAAG;UAAAnC,QAAA,gBACd9E,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACX9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA0F,QAAA,eAEhD9E,OAAA,CAACrB,WAAW;gBACVkL,GAAG,EAAE,CAAE;gBACPzE,KAAK,EAAE;kBAAET,KAAK,EAAE;gBAAO,CAAE;gBACzBgF,WAAW,EAAC;cAAM;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACX9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtK,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA0F,QAAA,eAE9C9E,OAAA,CAACpB,MAAM;gBAAC+K,WAAW,EAAC,gCAAO;gBAAA7E,QAAA,gBACzB9E,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,GAAG;kBAAAR,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC7ClF,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChDlF,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACX9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtK,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA0F,QAAA,eAE9C9E,OAAA,CAACrB,WAAW;gBACVkL,GAAG,EAAE,CAAE;gBACPrC,SAAS,EAAE,CAAE;gBACbpC,KAAK,EAAE;kBAAET,KAAK,EAAE;gBAAO,CAAE;gBACzBgF,WAAW,EAAC,cAAI;gBAChBG,WAAW,EAAC;cAAG;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAAC5B,GAAG;UAAC6I,MAAM,EAAE,EAAG;UAAAnC,QAAA,gBACd9E,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,EAAG;YAAAvE,QAAA,eACZ9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA0F,QAAA,eAEhD9E,OAAA,CAACnB,UAAU;gBAACuG,KAAK,EAAE;kBAAET,KAAK,EAAE;gBAAO;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,EAAG;YAAAvE,QAAA,eACZ9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,0BAAM;cACZO,YAAY,EAAC,WAAW;cAAAjF,QAAA,eAExB9E,OAAA,CAACpB,MAAM;gBAAC+K,WAAW,EAAC,4CAAS;gBAAA7E,QAAA,gBAC3B9E,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,WAAW;kBAAAR,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eACvDlF,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,UAAU;kBAAAR,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eACtDlF,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,WAAW;kBAAAR,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eACvDlF,OAAA,CAACpB,MAAM,CAACgL,MAAM;kBAACtE,KAAK,EAAC,SAAS;kBAAAR,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAACvB,IAAI,CAAC6K,IAAI;UAACC,IAAI,EAAC,aAAa;UAACC,KAAK,EAAC,0BAAM;UAAA1E,QAAA,eACxC9E,OAAA,CAACI,QAAQ;YAAC4J,IAAI,EAAE,CAAE;YAACL,WAAW,EAAC;UAAS;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRlF,OAAA,CAACxB,KAAK;MACJgG,KAAK,EAAC,sCAAQ;MACduE,IAAI,EAAElI,oBAAqB;MAC3B+F,IAAI,EAAExE,qBAAsB;MAC5B4G,QAAQ,EAAEA,CAAA,KAAMlI,uBAAuB,CAAC,KAAK,CAAE;MAC/CmI,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfvE,KAAK,EAAE,GAAI;MAAAG,QAAA,eAEX9E,OAAA,CAACvB,IAAI;QAAC0K,IAAI,EAAE9H,YAAa;QAAC+H,MAAM,EAAC,UAAU;QAAAtE,QAAA,gBACzC9E,OAAA,CAAClB,KAAK;UACJM,OAAO,EAAC,sCAAQ;UAChBwI,WAAW,EAAC,0NAAsC;UAClDzC,IAAI,EAAC,MAAM;UACX0C,QAAQ;UACRzC,KAAK,EAAE;YAAE8B,YAAY,EAAE;UAAG;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFlF,OAAA,CAACvB,IAAI,CAAC6K,IAAI;UACRC,IAAI,EAAC,WAAW;UAChBC,KAAK,EAAC,0BAAM;UACZO,YAAY,EAAC,UAAU;UAAAjF,QAAA,eAEvB9E,OAAA,CAACpB,MAAM;YAAC+K,WAAW,EAAC,4CAAS;YAAA7E,QAAA,gBAC3B9E,OAAA,CAACpB,MAAM,CAACgL,MAAM;cAACtE,KAAK,EAAC,WAAW;cAAAR,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC9DlF,OAAA,CAACpB,MAAM,CAACgL,MAAM;cAACtE,KAAK,EAAC,UAAU;cAAAR,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC7DlF,OAAA,CAACpB,MAAM,CAACgL,MAAM;cAACtE,KAAK,EAAC,WAAW;cAAAR,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChElF,OAAA,CAACpB,MAAM,CAACgL,MAAM;cAACtE,KAAK,EAAC,SAAS;cAAAR,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZlF,OAAA,CAAC5B,GAAG;UAAC6I,MAAM,EAAE,EAAG;UAAAnC,QAAA,gBACd9E,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,EAAG;YAAAvE,QAAA,eACZ9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAC,mCAAU;cAChBO,YAAY,EAAE,EAAG;cAAAjF,QAAA,eAEjB9E,OAAA,CAACrB,WAAW;gBACVkL,GAAG,EAAE,EAAG;gBACRI,GAAG,EAAE,GAAI;gBACT7E,KAAK,EAAE;kBAAET,KAAK,EAAE;gBAAO,CAAE;gBACzBgF,WAAW,EAAC;cAAO;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,EAAG;YAAAvE,QAAA,eACZ9E,OAAA,CAACvB,IAAI,CAAC6K,IAAI;cACRC,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAC,sCAAQ;cACdO,YAAY,EAAE,CAAE;cAAAjF,QAAA,eAEhB9E,OAAA,CAACrB,WAAW;gBACVkL,GAAG,EAAE,CAAE;gBACPzE,KAAK,EAAE;kBAAET,KAAK,EAAE;gBAAO,CAAE;gBACzBgF,WAAW,EAAC,sCAAQ;gBACpBO,UAAU,EAAE,CAAAjJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE2B,IAAI,KAAI;cAAI;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAACvB,IAAI,CAAC6K,IAAI;UAACC,IAAI,EAAC,eAAe;UAACC,KAAK,EAAC,gCAAO;UAAA1E,QAAA,eAC3C9E,OAAA,CAACX,QAAQ,CAAC8K,KAAK;YACbC,OAAO,EAAE,CACP;cAAEZ,KAAK,EAAE,YAAY;cAAElE,KAAK,EAAE;YAAW,CAAC,EAC1C;cAAEkE,KAAK,EAAE,UAAU;cAAElE,KAAK,EAAE;YAAU,CAAC,EACvC;cAAEkE,KAAK,EAAE,QAAQ;cAAElE,KAAK,EAAE;YAAQ,CAAC,EACnC;cAAEkE,KAAK,EAAE,UAAU;cAAElE,KAAK,EAAE;YAAU,CAAC,CACvC;YACF+E,YAAY,EAAE,CAAC,UAAU,EAAE,OAAO;UAAE;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlF,OAAA,CAACvB,IAAI,CAAC6K,IAAI;UAACC,IAAI,EAAC,cAAc;UAACe,aAAa,EAAC,SAAS;UAACP,YAAY,EAAE,IAAK;UAAAjF,QAAA,eACxE9E,OAAA,CAACX,QAAQ;YAAAyF,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRlF,OAAA,CAACxB,KAAK;MACJgG,KAAK,EAAE,YAAYvD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEsB,QAAQ,EAAG;MAC5CwG,IAAI,EAAEhI,kBAAmB;MACzBiI,QAAQ,EAAEA,CAAA,KAAMhI,qBAAqB,CAAC,KAAK,CAAE;MAC7CuJ,MAAM,EAAE,cACNvK,OAAA,CAAC9B,MAAM;QAAaqI,OAAO,EAAEA,CAAA,KAAMvF,qBAAqB,CAAC,KAAK,CAAE;QAAA8D,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACTlF,OAAA,CAAC9B,MAAM;QAAcoI,IAAI,eAAEtG,OAAA,CAACP,cAAc;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAAC;MAE/C,GAFY,QAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEZ,CAAC,eACTlF,OAAA,CAAC9B,MAAM;QAAeiH,IAAI,EAAC,SAAS;QAACmB,IAAI,eAAEtG,OAAA,CAACF,kBAAkB;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAAC;MAEnE,GAFY,SAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEb,CAAC,CACT;MACFP,KAAK,EAAE,IAAK;MAAAG,QAAA,EAEX7D,YAAY,iBACXjB,OAAA;QAAA8E,QAAA,gBACE9E,OAAA,CAAC5B,GAAG;UAAC6I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAC7B,KAAK,EAAE;YAAE8B,YAAY,EAAE;UAAG,CAAE;UAAApC,QAAA,gBACjD9E,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACX9E,OAAA,CAAC1B,SAAS;cACRkG,KAAK,EAAC,0BAAM;cACZc,KAAK,EAAErE,YAAY,CAACiC,eAAgB;cACpCoE,MAAM,EAAC,GAAG;cACVC,UAAU,EAAE;gBAAE9B,KAAK,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlF,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACX9E,OAAA,CAAC1B,SAAS;cACRkG,KAAK,EAAC,oBAAK;cACXc,KAAK,EAAErE,YAAY,CAACkC,SAAU;cAC9BmE,MAAM,EAAC,GAAG;cACVC,UAAU,EAAE;gBAAE9B,KAAK,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlF,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACX9E,OAAA,CAAC1B,SAAS;cACRkG,KAAK,EAAC,oBAAK;cACXc,KAAK,EAAErE,YAAY,CAACmC,SAAU;cAC9BiE,MAAM,EAAC,MAAG;cACVG,SAAS,EAAE;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlF,OAAA,CAAC3B,GAAG;YAACgL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACX9E,OAAA,CAAC1B,SAAS;cACRkG,KAAK,EAAC,0BAAM;cACZc,KAAK,EAAErE,YAAY,CAACoC,SAAU;cAC9BgE,MAAM,EAAC,MAAG;cACVG,SAAS,EAAE,CAAE;cACbD,UAAU,EAAE;gBAAE9B,KAAK,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAACb,OAAO;UAACqL,WAAW,EAAC,MAAM;UAAA1F,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1ClF,OAAA,CAAC/B,KAAK;UACJ8J,OAAO,EAAEhB,aAAc;UACvBiB,UAAU,EAAE/D,WAAY;UACxBgE,MAAM,EAAC,IAAI;UACXzC,IAAI,EAAC,OAAO;UACZ4C,UAAU,EAAE,KAAM;UAClBhD,KAAK,EAAE;YAAE8B,YAAY,EAAE;UAAG;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFlF,OAAA,CAACb,OAAO;UAACqL,WAAW,EAAC,MAAM;UAAA1F,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1ClF,OAAA,CAAC/B,KAAK;UACJ8J,OAAO,EAAElB,aAAc;UACvBmB,UAAU,EAAExE,WAAY;UACxByE,MAAM,EAAC,IAAI;UACXzC,IAAI,EAAC,OAAO;UACZ4C,UAAU,EAAE;QAAM;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAxzBID,eAAyB;EAAA,QAOV5B,IAAI,CAAC2C,OAAO,EACR3C,IAAI,CAAC2C,OAAO;AAAA;AAAAqJ,EAAA,GAR/BpK,eAAyB;AA0zB/B,eAAeA,eAAe;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}