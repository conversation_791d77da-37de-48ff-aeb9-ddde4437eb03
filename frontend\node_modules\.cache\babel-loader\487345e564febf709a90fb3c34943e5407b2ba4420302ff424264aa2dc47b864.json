{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useEffect}from'react';import{useNavigate,useLocation}from'react-router-dom';import{Form,Input,Button,Card,Typography,Space,Alert,Checkbox,Divider,Row,Col}from'antd';import{UserOutlined,LockOutlined,LoginOutlined}from'@ant-design/icons';import{useAppDispatch,useAppSelector}from'../../hooks/redux';import{login,clearError}from'../../store/slices/authSlice';import{ROUTES}from'../../constants';import{LoadingSpinner}from'../../components';import{errorHandler,ErrorType}from'../../utils/errorHandler';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const LoginPage=()=>{const[form]=Form.useForm();const navigate=useNavigate();const location=useLocation();const dispatch=useAppDispatch();const{loading,error,isAuthenticated}=useAppSelector(state=>state.auth);// 如果已经登录，重定向到目标页面或仪表板\nuseEffect(()=>{if(isAuthenticated){var _location$state,_location$state$from;const from=((_location$state=location.state)===null||_location$state===void 0?void 0:(_location$state$from=_location$state.from)===null||_location$state$from===void 0?void 0:_location$state$from.pathname)||ROUTES.DASHBOARD;navigate(from,{replace:true});}},[isAuthenticated,navigate,location]);// 清除错误信息\nuseEffect(()=>{return()=>{dispatch(clearError());};},[dispatch]);// 处理登录\nconst handleLogin=async values=>{try{await dispatch(login({username:values.username,password:values.password})).unwrap();// 登录成功后会通过useEffect重定向\n}catch(error){// 使用新的错误处理器\nerrorHandler.handleError({type:ErrorType.BUSINESS,message:error.message||'登录失败，请检查用户名和密码',details:error});}};// 演示账号登录\nconst handleDemoLogin=role=>{const demoAccounts={admin:{username:'admin',password:'admin123'},bom_manager:{username:'bom_manager',password:'bom123'},sales_pmc:{username:'sales_pmc',password:'sales123'},purchase_manager:{username:'purchase_manager',password:'purchase123'},warehouse_manager:{username:'warehouse_manager',password:'warehouse123'},finance_manager:{username:'finance_manager',password:'finance123'},service_technician:{username:'service_tech',password:'service123'},operator:{username:'operator',password:'operator123'}};const account=demoAccounts[role];if(account){form.setFieldsValue(account);handleLogin(_objectSpread(_objectSpread({},account),{},{remember:false}));}};return/*#__PURE__*/_jsxs(_Fragment,{children:[loading&&/*#__PURE__*/_jsx(LoadingSpinner,{fullscreen:true,overlay:true,text:\"\\u6B63\\u5728\\u767B\\u5F55...\",size:\"large\"}),/*#__PURE__*/_jsx(\"div\",{style:{minHeight:'100vh',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',display:'flex',alignItems:'center',justifyContent:'center',padding:'20px'},children:/*#__PURE__*/_jsxs(Row,{gutter:[32,32],style:{width:'100%',maxWidth:1200},children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{color:'white',textAlign:'center'},children:[/*#__PURE__*/_jsx(Title,{level:1,style:{color:'white',marginBottom:24},children:\"Link-BOM-S\"}),/*#__PURE__*/_jsx(Title,{level:3,style:{color:'white',fontWeight:'normal',marginBottom:32},children:\"\\u5929\\u7EBF\\u884C\\u4E1ABOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"}),/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsx(Text,{style:{color:'rgba(255,255,255,0.9)',fontSize:16},children:\"\\u4E13\\u4E3A\\u5929\\u7EBF\\u884C\\u4E1A\\u5C0F\\u5FAE\\u5236\\u9020\\u4F01\\u4E1A\\u8BBE\\u8BA1\\u7684\\u7EFC\\u5408BOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'left',maxWidth:400,margin:'0 auto'},children:[/*#__PURE__*/_jsx(Text,{style:{color:'rgba(255,255,255,0.8)',display:'block',marginBottom:8},children:\"\\u2713 \\u6838\\u5FC3BOM\\uFF08150%\\uFF09+ \\u8BA2\\u5355\\u5FEB\\u901F\\u6D3E\\u751F\\uFF08100%\\uFF09\"}),/*#__PURE__*/_jsx(Text,{style:{color:'rgba(255,255,255,0.8)',display:'block',marginBottom:8},children:\"\\u2713 \\u6309\\u5355\\u5408\\u5355\\u91C7\\u8D2D + \\u5305\\u88C5/MOQ\\u53D6\\u6574\"}),/*#__PURE__*/_jsx(Text,{style:{color:'rgba(255,255,255,0.8)',display:'block',marginBottom:8},children:\"\\u2713 \\u957F\\u5EA6\\u578B\\u5207\\u5272\\u4F18\\u5316 + \\u4F59\\u6599\\u53F0\\u8D26\"}),/*#__PURE__*/_jsx(Text,{style:{color:'rgba(255,255,255,0.8)',display:'block',marginBottom:8},children:\"\\u2713 \\u670D\\u52A1BOM + \\u6210\\u672C\\u900F\\u660E\\u5EA6\"})]})]})]})}),/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsxs(Card,{style:{maxWidth:400,margin:'0 auto',borderRadius:12,boxShadow:'0 8px 32px rgba(0,0,0,0.1)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',marginBottom:32},children:[/*#__PURE__*/_jsx(Title,{level:3,style:{marginBottom:8},children:\"\\u7528\\u6237\\u767B\\u5F55\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u8BF7\\u8F93\\u5165\\u60A8\\u7684\\u8D26\\u53F7\\u548C\\u5BC6\\u7801\"})]}),error&&/*#__PURE__*/_jsx(Alert,{message:error,type:\"error\",showIcon:true,style:{marginBottom:24},closable:true,onClose:()=>dispatch(clearError())}),/*#__PURE__*/_jsxs(Form,{form:form,name:\"login\",onFinish:handleLogin,autoComplete:\"off\",size:\"large\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"username\",rules:[{required:true,message:'请输入用户名'},{min:3,message:'用户名至少3个字符'}],children:/*#__PURE__*/_jsx(Input,{prefix:/*#__PURE__*/_jsx(UserOutlined,{}),placeholder:\"\\u7528\\u6237\\u540D\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"password\",rules:[{required:true,message:'请输入密码'},{min:6,message:'密码至少6个字符'}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(LockOutlined,{}),placeholder:\"\\u5BC6\\u7801\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"remember\",valuePropName:\"checked\",children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u8BB0\\u4F4F\\u6211\"})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(LoginOutlined,{}),block:true,children:\"\\u767B\\u5F55\"})})]}),/*#__PURE__*/_jsx(Divider,{children:\"\\u6F14\\u793A\\u8D26\\u53F7\"}),/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},size:\"small\",children:[/*#__PURE__*/_jsxs(Row,{gutter:[8,8],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{size:\"small\",block:true,onClick:()=>handleDemoLogin('admin'),children:\"\\u7CFB\\u7EDF\\u7BA1\\u7406\\u5458\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{size:\"small\",block:true,onClick:()=>handleDemoLogin('bom_manager'),children:\"BOM\\u7BA1\\u7406\\u5458\"})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[8,8],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{size:\"small\",block:true,onClick:()=>handleDemoLogin('sales_pmc'),children:\"\\u9500\\u552E/PMC\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{size:\"small\",block:true,onClick:()=>handleDemoLogin('purchase_manager'),children:\"\\u91C7\\u8D2D\\u7ECF\\u7406\"})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[8,8],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{size:\"small\",block:true,onClick:()=>handleDemoLogin('warehouse_manager'),children:\"\\u4ED3\\u5E93\\u7ECF\\u7406\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{size:\"small\",block:true,onClick:()=>handleDemoLogin('finance_manager'),children:\"\\u8D22\\u52A1\\u7ECF\\u7406\"})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[8,8],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{size:\"small\",block:true,onClick:()=>handleDemoLogin('service_technician'),children:\"\\u670D\\u52A1\\u6280\\u672F\\u5458\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{size:\"small\",block:true,onClick:()=>handleDemoLogin('operator'),children:\"\\u64CD\\u4F5C\\u5458\"})})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',marginTop:24},children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:\"\\xA9 2024 Link-BOM-S. \\u5929\\u7EBF\\u884C\\u4E1ABOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"})})]})})]})})]});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useLocation", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "Space", "<PERSON><PERSON>", "Checkbox", "Divider", "Row", "Col", "UserOutlined", "LockOutlined", "LoginOutlined", "useAppDispatch", "useAppSelector", "login", "clearError", "ROUTES", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Title", "Text", "LoginPage", "form", "useForm", "navigate", "location", "dispatch", "loading", "error", "isAuthenticated", "state", "auth", "_location$state", "_location$state$from", "from", "pathname", "DASHBOARD", "replace", "handleLogin", "values", "username", "password", "unwrap", "handleError", "type", "BUSINESS", "message", "details", "handleDemoLogin", "role", "demoAccounts", "admin", "bom_manager", "sales_pmc", "purchase_manager", "warehouse_manager", "finance_manager", "service_technician", "operator", "account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectSpread", "remember", "children", "fullscreen", "overlay", "text", "size", "style", "minHeight", "background", "display", "alignItems", "justifyContent", "padding", "gutter", "width", "max<PERSON><PERSON><PERSON>", "xs", "lg", "color", "textAlign", "level", "marginBottom", "fontWeight", "direction", "fontSize", "margin", "borderRadius", "boxShadow", "showIcon", "closable", "onClose", "name", "onFinish", "autoComplete", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "Password", "valuePropName", "htmlType", "icon", "block", "span", "onClick", "marginTop"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/auth/LoginPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Form,\n  Input,\n  Button,\n  Card,\n  Typography,\n  Space,\n  Alert,\n  Checkbox,\n  Divider,\n  Row,\n  Col,\n} from 'antd';\nimport {\n  UserOutlined,\n  LockOutlined,\n  LoginOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { login, clearError } from '../../store/slices/authSlice';\nimport { ROUTES } from '../../constants';\nimport { LoadingSpinner } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title, Text } = Typography;\n\ninterface LoginFormData {\n  username: string;\n  password: string;\n  remember: boolean;\n}\n\nconst LoginPage: React.FC = () => {\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const { loading, error, isAuthenticated } = useAppSelector(state => state.auth);\n\n  // 如果已经登录，重定向到目标页面或仪表板\n  useEffect(() => {\n    if (isAuthenticated) {\n      const from = (location.state as any)?.from?.pathname || ROUTES.DASHBOARD;\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, navigate, location]);\n\n  // 清除错误信息\n  useEffect(() => {\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n\n  // 处理登录\n  const handleLogin = async (values: LoginFormData) => {\n    try {\n      await dispatch(login({\n        username: values.username,\n        password: values.password,\n      })).unwrap();\n      \n      // 登录成功后会通过useEffect重定向\n    } catch (error: any) {\n      // 使用新的错误处理器\n      errorHandler.handleError({\n        type: ErrorType.BUSINESS,\n        message: error.message || '登录失败，请检查用户名和密码',\n        details: error\n      });\n    }\n  };\n\n  // 演示账号登录\n  const handleDemoLogin = (role: string) => {\n    const demoAccounts = {\n      admin: { username: 'admin', password: 'admin123' },\n      bom_manager: { username: 'bom_manager', password: 'bom123' },\n      sales_pmc: { username: 'sales_pmc', password: 'sales123' },\n      purchase_manager: { username: 'purchase_manager', password: 'purchase123' },\n      warehouse_manager: { username: 'warehouse_manager', password: 'warehouse123' },\n      finance_manager: { username: 'finance_manager', password: 'finance123' },\n      service_technician: { username: 'service_tech', password: 'service123' },\n      operator: { username: 'operator', password: 'operator123' },\n    };\n\n    const account = demoAccounts[role as keyof typeof demoAccounts];\n    if (account) {\n      form.setFieldsValue(account);\n      handleLogin({ ...account, remember: false });\n    }\n  };\n\n  return (\n    <>\n      {loading && (\n        <LoadingSpinner\n          fullscreen\n          overlay\n          text=\"正在登录...\"\n          size=\"large\"\n        />\n      )}\n      <div style={{\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '20px',\n      }}>\n      <Row gutter={[32, 32]} style={{ width: '100%', maxWidth: 1200 }}>\n        {/* 左侧：系统介绍 */}\n        <Col xs={24} lg={12}>\n          <div style={{ color: 'white', textAlign: 'center' }}>\n            <Title level={1} style={{ color: 'white', marginBottom: 24 }}>\n              Link-BOM-S\n            </Title>\n            <Title level={3} style={{ color: 'white', fontWeight: 'normal', marginBottom: 32 }}>\n              天线行业BOM管理系统\n            </Title>\n            <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n              <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: 16 }}>\n                专为天线行业小微制造企业设计的综合BOM管理系统\n              </Text>\n              <div style={{ textAlign: 'left', maxWidth: 400, margin: '0 auto' }}>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 核心BOM（150%）+ 订单快速派生（100%）\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 按单合单采购 + 包装/MOQ取整\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 长度型切割优化 + 余料台账\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 服务BOM + 成本透明度\n                </Text>\n              </div>\n            </Space>\n          </div>\n        </Col>\n\n        {/* 右侧：登录表单 */}\n        <Col xs={24} lg={12}>\n          <Card\n            style={{\n              maxWidth: 400,\n              margin: '0 auto',\n              borderRadius: 12,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n            }}\n          >\n            <div style={{ textAlign: 'center', marginBottom: 32 }}>\n              <Title level={3} style={{ marginBottom: 8 }}>\n                用户登录\n              </Title>\n              <Text type=\"secondary\">\n                请输入您的账号和密码\n              </Text>\n            </div>\n\n            {error && (\n              <Alert\n                message={error}\n                type=\"error\"\n                showIcon\n                style={{ marginBottom: 24 }}\n                closable\n                onClose={() => dispatch(clearError())}\n              />\n            )}\n\n            <Form\n              form={form}\n              name=\"login\"\n              onFinish={handleLogin}\n              autoComplete=\"off\"\n              size=\"large\"\n            >\n              <Form.Item\n                name=\"username\"\n                rules={[\n                  { required: true, message: '请输入用户名' },\n                  { min: 3, message: '用户名至少3个字符' },\n                ]}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  placeholder=\"用户名\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"password\"\n                rules={[\n                  { required: true, message: '请输入密码' },\n                  { min: 6, message: '密码至少6个字符' },\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"密码\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"remember\" valuePropName=\"checked\">\n                <Checkbox>记住我</Checkbox>\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<LoginOutlined />}\n                  block\n                >\n                  登录\n                </Button>\n              </Form.Item>\n            </Form>\n\n            <Divider>演示账号</Divider>\n\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"small\">\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('admin')}\n                  >\n                    系统管理员\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('bom_manager')}\n                  >\n                    BOM管理员\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('sales_pmc')}\n                  >\n                    销售/PMC\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('purchase_manager')}\n                  >\n                    采购经理\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('warehouse_manager')}\n                  >\n                    仓库经理\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('finance_manager')}\n                  >\n                    财务经理\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('service_technician')}\n                  >\n                    服务技术员\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('operator')}\n                  >\n                    操作员\n                  </Button>\n                </Col>\n              </Row>\n            </Space>\n\n            <div style={{ textAlign: 'center', marginTop: 24 }}>\n              <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                © 2024 Link-BOM-S. 天线行业BOM管理系统\n              </Text>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n    </>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": "wHAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,UAAU,CACVC,KAAK,CACLC,KAAK,CACLC,QAAQ,CACRC,OAAO,CACPC,GAAG,CACHC,GAAG,KACE,MAAM,CACb,OACEC,YAAY,CACZC,YAAY,CACZC,aAAa,KACR,mBAAmB,CAE1B,OAASC,cAAc,CAAEC,cAAc,KAAQ,mBAAmB,CAClE,OAASC,KAAK,CAAEC,UAAU,KAAQ,8BAA8B,CAChE,OAASC,MAAM,KAAQ,iBAAiB,CACxC,OAASC,cAAc,KAAQ,kBAAkB,CACjD,OAASC,YAAY,CAAEC,SAAS,KAAQ,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnE,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGzB,UAAU,CAQlC,KAAM,CAAA0B,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,IAAI,CAAC,CAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC,CAC7B,KAAM,CAAAC,QAAQ,CAAGnC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoC,QAAQ,CAAGnC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAoC,QAAQ,CAAGrB,cAAc,CAAC,CAAC,CACjC,KAAM,CAAEsB,OAAO,CAAEC,KAAK,CAAEC,eAAgB,CAAC,CAAGvB,cAAc,CAACwB,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CAE/E;AACA3C,SAAS,CAAC,IAAM,CACd,GAAIyC,eAAe,CAAE,KAAAG,eAAA,CAAAC,oBAAA,CACnB,KAAM,CAAAC,IAAI,CAAG,EAAAF,eAAA,CAACP,QAAQ,CAACK,KAAK,UAAAE,eAAA,kBAAAC,oBAAA,CAAfD,eAAA,CAAyBE,IAAI,UAAAD,oBAAA,iBAA7BA,oBAAA,CAA+BE,QAAQ,GAAI1B,MAAM,CAAC2B,SAAS,CACxEZ,QAAQ,CAACU,IAAI,CAAE,CAAEG,OAAO,CAAE,IAAK,CAAC,CAAC,CACnC,CACF,CAAC,CAAE,CAACR,eAAe,CAAEL,QAAQ,CAAEC,QAAQ,CAAC,CAAC,CAEzC;AACArC,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACXsC,QAAQ,CAAClB,UAAU,CAAC,CAAC,CAAC,CACxB,CAAC,CACH,CAAC,CAAE,CAACkB,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAY,WAAW,CAAG,KAAO,CAAAC,MAAqB,EAAK,CACnD,GAAI,CACF,KAAM,CAAAb,QAAQ,CAACnB,KAAK,CAAC,CACnBiC,QAAQ,CAAED,MAAM,CAACC,QAAQ,CACzBC,QAAQ,CAAEF,MAAM,CAACE,QACnB,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAEZ;AACF,CAAE,MAAOd,KAAU,CAAE,CACnB;AACAjB,YAAY,CAACgC,WAAW,CAAC,CACvBC,IAAI,CAAEhC,SAAS,CAACiC,QAAQ,CACxBC,OAAO,CAAElB,KAAK,CAACkB,OAAO,EAAI,gBAAgB,CAC1CC,OAAO,CAAEnB,KACX,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAoB,eAAe,CAAIC,IAAY,EAAK,CACxC,KAAM,CAAAC,YAAY,CAAG,CACnBC,KAAK,CAAE,CAAEX,QAAQ,CAAE,OAAO,CAAEC,QAAQ,CAAE,UAAW,CAAC,CAClDW,WAAW,CAAE,CAAEZ,QAAQ,CAAE,aAAa,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAC5DY,SAAS,CAAE,CAAEb,QAAQ,CAAE,WAAW,CAAEC,QAAQ,CAAE,UAAW,CAAC,CAC1Da,gBAAgB,CAAE,CAAEd,QAAQ,CAAE,kBAAkB,CAAEC,QAAQ,CAAE,aAAc,CAAC,CAC3Ec,iBAAiB,CAAE,CAAEf,QAAQ,CAAE,mBAAmB,CAAEC,QAAQ,CAAE,cAAe,CAAC,CAC9Ee,eAAe,CAAE,CAAEhB,QAAQ,CAAE,iBAAiB,CAAEC,QAAQ,CAAE,YAAa,CAAC,CACxEgB,kBAAkB,CAAE,CAAEjB,QAAQ,CAAE,cAAc,CAAEC,QAAQ,CAAE,YAAa,CAAC,CACxEiB,QAAQ,CAAE,CAAElB,QAAQ,CAAE,UAAU,CAAEC,QAAQ,CAAE,aAAc,CAC5D,CAAC,CAED,KAAM,CAAAkB,OAAO,CAAGT,YAAY,CAACD,IAAI,CAA8B,CAC/D,GAAIU,OAAO,CAAE,CACXrC,IAAI,CAACsC,cAAc,CAACD,OAAO,CAAC,CAC5BrB,WAAW,CAAAuB,aAAA,CAAAA,aAAA,IAAMF,OAAO,MAAEG,QAAQ,CAAE,KAAK,EAAE,CAAC,CAC9C,CACF,CAAC,CAED,mBACE9C,KAAA,CAAAE,SAAA,EAAA6C,QAAA,EACGpC,OAAO,eACNb,IAAA,CAACJ,cAAc,EACbsD,UAAU,MACVC,OAAO,MACPC,IAAI,CAAC,6BAAS,CACdC,IAAI,CAAC,OAAO,CACb,CACF,cACDrD,IAAA,QAAKsD,KAAK,CAAE,CACVC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mDAAmD,CAC/DC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,OAAO,CAAE,MACX,CAAE,CAAAX,QAAA,cACF/C,KAAA,CAAChB,GAAG,EAAC2E,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACP,KAAK,CAAE,CAAEQ,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,IAAK,CAAE,CAAAd,QAAA,eAE9DjD,IAAA,CAACb,GAAG,EAAC6E,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAhB,QAAA,cAClB/C,KAAA,QAAKoD,KAAK,CAAE,CAAEY,KAAK,CAAE,OAAO,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAlB,QAAA,eAClDjD,IAAA,CAACK,KAAK,EAAC+D,KAAK,CAAE,CAAE,CAACd,KAAK,CAAE,CAAEY,KAAK,CAAE,OAAO,CAAEG,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,CAAC,YAE9D,CAAO,CAAC,cACRjD,IAAA,CAACK,KAAK,EAAC+D,KAAK,CAAE,CAAE,CAACd,KAAK,CAAE,CAAEY,KAAK,CAAE,OAAO,CAAEI,UAAU,CAAE,QAAQ,CAAED,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,CAAC,qDAEpF,CAAO,CAAC,cACR/C,KAAA,CAACpB,KAAK,EAACyF,SAAS,CAAC,UAAU,CAAClB,IAAI,CAAC,OAAO,CAACC,KAAK,CAAE,CAAEQ,KAAK,CAAE,MAAO,CAAE,CAAAb,QAAA,eAChEjD,IAAA,CAACM,IAAI,EAACgD,KAAK,CAAE,CAAEY,KAAK,CAAE,uBAAuB,CAAEM,QAAQ,CAAE,EAAG,CAAE,CAAAvB,QAAA,CAAC,mIAE/D,CAAM,CAAC,cACP/C,KAAA,QAAKoD,KAAK,CAAE,CAAEa,SAAS,CAAE,MAAM,CAAEJ,QAAQ,CAAE,GAAG,CAAEU,MAAM,CAAE,QAAS,CAAE,CAAAxB,QAAA,eACjEjD,IAAA,CAACM,IAAI,EAACgD,KAAK,CAAE,CAAEY,KAAK,CAAE,uBAAuB,CAAET,OAAO,CAAE,OAAO,CAAEY,YAAY,CAAE,CAAE,CAAE,CAAApB,QAAA,CAAC,8FAEpF,CAAM,CAAC,cACPjD,IAAA,CAACM,IAAI,EAACgD,KAAK,CAAE,CAAEY,KAAK,CAAE,uBAAuB,CAAET,OAAO,CAAE,OAAO,CAAEY,YAAY,CAAE,CAAE,CAAE,CAAApB,QAAA,CAAC,4EAEpF,CAAM,CAAC,cACPjD,IAAA,CAACM,IAAI,EAACgD,KAAK,CAAE,CAAEY,KAAK,CAAE,uBAAuB,CAAET,OAAO,CAAE,OAAO,CAAEY,YAAY,CAAE,CAAE,CAAE,CAAApB,QAAA,CAAC,8EAEpF,CAAM,CAAC,cACPjD,IAAA,CAACM,IAAI,EAACgD,KAAK,CAAE,CAAEY,KAAK,CAAE,uBAAuB,CAAET,OAAO,CAAE,OAAO,CAAEY,YAAY,CAAE,CAAE,CAAE,CAAApB,QAAA,CAAC,yDAEpF,CAAM,CAAC,EACJ,CAAC,EACD,CAAC,EACL,CAAC,CACH,CAAC,cAGNjD,IAAA,CAACb,GAAG,EAAC6E,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAhB,QAAA,cAClB/C,KAAA,CAACtB,IAAI,EACH0E,KAAK,CAAE,CACLS,QAAQ,CAAE,GAAG,CACbU,MAAM,CAAE,QAAQ,CAChBC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,4BACb,CAAE,CAAA1B,QAAA,eAEF/C,KAAA,QAAKoD,KAAK,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEE,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,eACpDjD,IAAA,CAACK,KAAK,EAAC+D,KAAK,CAAE,CAAE,CAACd,KAAK,CAAE,CAAEe,YAAY,CAAE,CAAE,CAAE,CAAApB,QAAA,CAAC,0BAE7C,CAAO,CAAC,cACRjD,IAAA,CAACM,IAAI,EAACwB,IAAI,CAAC,WAAW,CAAAmB,QAAA,CAAC,8DAEvB,CAAM,CAAC,EACJ,CAAC,CAELnC,KAAK,eACJd,IAAA,CAACjB,KAAK,EACJiD,OAAO,CAAElB,KAAM,CACfgB,IAAI,CAAC,OAAO,CACZ8C,QAAQ,MACRtB,KAAK,CAAE,CAAEe,YAAY,CAAE,EAAG,CAAE,CAC5BQ,QAAQ,MACRC,OAAO,CAAEA,CAAA,GAAMlE,QAAQ,CAAClB,UAAU,CAAC,CAAC,CAAE,CACvC,CACF,cAEDQ,KAAA,CAACzB,IAAI,EACH+B,IAAI,CAAEA,IAAK,CACXuE,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAExD,WAAY,CACtByD,YAAY,CAAC,KAAK,CAClB5B,IAAI,CAAC,OAAO,CAAAJ,QAAA,eAEZjD,IAAA,CAACvB,IAAI,CAACyG,IAAI,EACRH,IAAI,CAAC,UAAU,CACfI,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEpD,OAAO,CAAE,QAAS,CAAC,CACrC,CAAEqD,GAAG,CAAE,CAAC,CAAErD,OAAO,CAAE,WAAY,CAAC,CAChC,CAAAiB,QAAA,cAEFjD,IAAA,CAACtB,KAAK,EACJ4G,MAAM,cAAEtF,IAAA,CAACZ,YAAY,GAAE,CAAE,CACzBmG,WAAW,CAAC,oBAAK,CAClB,CAAC,CACO,CAAC,cAEZvF,IAAA,CAACvB,IAAI,CAACyG,IAAI,EACRH,IAAI,CAAC,UAAU,CACfI,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEpD,OAAO,CAAE,OAAQ,CAAC,CACpC,CAAEqD,GAAG,CAAE,CAAC,CAAErD,OAAO,CAAE,UAAW,CAAC,CAC/B,CAAAiB,QAAA,cAEFjD,IAAA,CAACtB,KAAK,CAAC8G,QAAQ,EACbF,MAAM,cAAEtF,IAAA,CAACX,YAAY,GAAE,CAAE,CACzBkG,WAAW,CAAC,cAAI,CACjB,CAAC,CACO,CAAC,cAEZvF,IAAA,CAACvB,IAAI,CAACyG,IAAI,EAACH,IAAI,CAAC,UAAU,CAACU,aAAa,CAAC,SAAS,CAAAxC,QAAA,cAChDjD,IAAA,CAAChB,QAAQ,EAAAiE,QAAA,CAAC,oBAAG,CAAU,CAAC,CACf,CAAC,cAEZjD,IAAA,CAACvB,IAAI,CAACyG,IAAI,EAAAjC,QAAA,cACRjD,IAAA,CAACrB,MAAM,EACLmD,IAAI,CAAC,SAAS,CACd4D,QAAQ,CAAC,QAAQ,CACjB7E,OAAO,CAAEA,OAAQ,CACjB8E,IAAI,cAAE3F,IAAA,CAACV,aAAa,GAAE,CAAE,CACxBsG,KAAK,MAAA3C,QAAA,CACN,cAED,CAAQ,CAAC,CACA,CAAC,EACR,CAAC,cAEPjD,IAAA,CAACf,OAAO,EAAAgE,QAAA,CAAC,0BAAI,CAAS,CAAC,cAEvB/C,KAAA,CAACpB,KAAK,EAACyF,SAAS,CAAC,UAAU,CAACjB,KAAK,CAAE,CAAEQ,KAAK,CAAE,MAAO,CAAE,CAACT,IAAI,CAAC,OAAO,CAAAJ,QAAA,eAChE/C,KAAA,CAAChB,GAAG,EAAC2E,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAZ,QAAA,eAClBjD,IAAA,CAACb,GAAG,EAAC0G,IAAI,CAAE,EAAG,CAAA5C,QAAA,cACZjD,IAAA,CAACrB,MAAM,EACL0E,IAAI,CAAC,OAAO,CACZuC,KAAK,MACLE,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,OAAO,CAAE,CAAAe,QAAA,CACzC,gCAED,CAAQ,CAAC,CACN,CAAC,cACNjD,IAAA,CAACb,GAAG,EAAC0G,IAAI,CAAE,EAAG,CAAA5C,QAAA,cACZjD,IAAA,CAACrB,MAAM,EACL0E,IAAI,CAAC,OAAO,CACZuC,KAAK,MACLE,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,aAAa,CAAE,CAAAe,QAAA,CAC/C,uBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cACN/C,KAAA,CAAChB,GAAG,EAAC2E,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAZ,QAAA,eAClBjD,IAAA,CAACb,GAAG,EAAC0G,IAAI,CAAE,EAAG,CAAA5C,QAAA,cACZjD,IAAA,CAACrB,MAAM,EACL0E,IAAI,CAAC,OAAO,CACZuC,KAAK,MACLE,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,WAAW,CAAE,CAAAe,QAAA,CAC7C,kBAED,CAAQ,CAAC,CACN,CAAC,cACNjD,IAAA,CAACb,GAAG,EAAC0G,IAAI,CAAE,EAAG,CAAA5C,QAAA,cACZjD,IAAA,CAACrB,MAAM,EACL0E,IAAI,CAAC,OAAO,CACZuC,KAAK,MACLE,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,kBAAkB,CAAE,CAAAe,QAAA,CACpD,0BAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cACN/C,KAAA,CAAChB,GAAG,EAAC2E,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAZ,QAAA,eAClBjD,IAAA,CAACb,GAAG,EAAC0G,IAAI,CAAE,EAAG,CAAA5C,QAAA,cACZjD,IAAA,CAACrB,MAAM,EACL0E,IAAI,CAAC,OAAO,CACZuC,KAAK,MACLE,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,mBAAmB,CAAE,CAAAe,QAAA,CACrD,0BAED,CAAQ,CAAC,CACN,CAAC,cACNjD,IAAA,CAACb,GAAG,EAAC0G,IAAI,CAAE,EAAG,CAAA5C,QAAA,cACZjD,IAAA,CAACrB,MAAM,EACL0E,IAAI,CAAC,OAAO,CACZuC,KAAK,MACLE,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,iBAAiB,CAAE,CAAAe,QAAA,CACnD,0BAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cACN/C,KAAA,CAAChB,GAAG,EAAC2E,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAZ,QAAA,eAClBjD,IAAA,CAACb,GAAG,EAAC0G,IAAI,CAAE,EAAG,CAAA5C,QAAA,cACZjD,IAAA,CAACrB,MAAM,EACL0E,IAAI,CAAC,OAAO,CACZuC,KAAK,MACLE,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,oBAAoB,CAAE,CAAAe,QAAA,CACtD,gCAED,CAAQ,CAAC,CACN,CAAC,cACNjD,IAAA,CAACb,GAAG,EAAC0G,IAAI,CAAE,EAAG,CAAA5C,QAAA,cACZjD,IAAA,CAACrB,MAAM,EACL0E,IAAI,CAAC,OAAO,CACZuC,KAAK,MACLE,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,UAAU,CAAE,CAAAe,QAAA,CAC5C,oBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,EACD,CAAC,cAERjD,IAAA,QAAKsD,KAAK,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAE4B,SAAS,CAAE,EAAG,CAAE,CAAA9C,QAAA,cACjDjD,IAAA,CAACM,IAAI,EAACwB,IAAI,CAAC,WAAW,CAACwB,KAAK,CAAE,CAAEkB,QAAQ,CAAE,EAAG,CAAE,CAAAvB,QAAA,CAAC,2EAEhD,CAAM,CAAC,CACJ,CAAC,EACF,CAAC,CACJ,CAAC,EACH,CAAC,CACH,CAAC,EACJ,CAAC,CAEP,CAAC,CAED,cAAe,CAAA1C,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}