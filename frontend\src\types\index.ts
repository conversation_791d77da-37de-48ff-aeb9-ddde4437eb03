// 用户相关类型
export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  roles: Role[];
  currentRole?: Role;
  avatar?: string;
  phone?: string;
  department?: string;
  isActive: boolean;
  lastLoginTime?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: string;
  name: string;
  code: string;
  description: string;
  permissions: Permission[];
  isActive: boolean;
}

export interface Permission {
  id: string;
  name: string;
  code: string;
  resource: string;
  action: string;
  description: string;
}

// BOM相关类型
export interface CoreBOM {
  id: string;
  name: string;
  code: string;
  version: string;
  description: string;
  status: 'DRAFT' | 'ACTIVE' | 'FROZEN' | 'OBSOLETE';
  items: BOMItem[];
  configRules: ConfigRule[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  frozenAt?: string;
  frozenBy?: string;
}

export interface BOMItem {
  id: string;
  parentId?: string;
  materialId: string;
  materialCode: string;
  materialName: string;
  materialSpec: string;
  quantity: number;
  unit: string;
  level: number;
  sequence: number;
  isOptional: boolean;
  isMandatory: boolean;
  isAlternative: boolean;
  alternativeGroup?: string;
  configConditions?: ConfigCondition[];
  unitPrice?: number;
  totalPrice?: number;
  supplier?: string;
  leadTime?: number;
  moq?: number;
  packageSize?: number;
  remarks?: string;
}

export interface ConfigRule {
  id: string;
  name: string;
  condition: string;
  action: string;
  priority: number;
  isActive: boolean;
  description: string;
}

export interface ConfigCondition {
  parameter: string;
  operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'IN' | 'NOT_IN';
  value: string | number | string[];
}

// 订单BOM类型
export interface OrderBOM {
  id: string;
  orderNumber: string;
  customerName: string;
  customerConfig: CustomerConfig;
  coreBOMId: string;
  coreBOMVersion: string;
  items: BOMItem[];
  status: 'DRAFT' | 'CONFIRMED' | 'FROZEN' | 'CANCELLED';
  totalCost: number;
  estimatedMargin: number;
  deliveryDate: string;
  createdBy: string;
  createdAt: string;
  confirmedAt?: string;
  frozenAt?: string;
}

export interface CustomerConfig {
  [key: string]: string | number | boolean;
}

// 物料相关类型
export interface Material {
  id: string;
  code: string;
  name: string;
  specification: string;
  category: string;
  unit: string;
  unitPrice: number;
  supplier: string;
  leadTime: number;
  moq: number;
  packageSize: number;
  isLengthType: boolean;
  standardLength?: number;
  minReusableLength?: number;
  cuttingAllowance?: number;
  shelfLife?: number;
  storageCondition?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 库存相关类型
export interface Inventory {
  id: string;
  materialId: string;
  materialCode: string;
  materialName: string;
  batchNumber: string;
  quantity: number;
  availableQuantity: number;
  reservedQuantity: number;
  location: string;
  expiryDate?: string;
  receivedDate: string;
  unitPrice: number;
  totalValue: number;
  supplier: string;
  status: 'AVAILABLE' | 'RESERVED' | 'EXPIRED' | 'DAMAGED';
}

export interface RemnantMaterial {
  id: string;
  originalBatchNumber: string;
  materialId: string;
  materialCode: string;
  materialName: string;
  remainingLength: number;
  location: string;
  isReusable: boolean;
  reservedFor?: string;
  createdAt: string;
  estimatedValue: number;
}

// 采购相关类型
export interface PurchaseRequisition {
  id: string;
  requisitionNo: string;
  title: string;
  description: string;
  applicant: string;
  department: string;
  urgency: 'HIGH' | 'MEDIUM' | 'LOW' | 'URGENT';
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
  expectedDate: string;
  createdAt: string;
  totalAmount: number;
  itemCount: number;
  approver?: string;
  items: PurchaseRequisitionItem[];
}

export interface PurchaseRequisitionItem {
  id: string;
  materialCode: string;
  materialName: string;
  specification: string;
  unit: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  supplier: string;
  expectedDate: string;
  remark?: string;
}

export interface PurchaseOrder {
  id: string;
  orderNumber: string;
  supplierName: string;
  supplierId: string;
  status: 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'ORDERED' | 'RECEIVED' | 'CANCELLED';
  urgency: 'HIGH' | 'MEDIUM' | 'LOW';
  totalAmount: number;
  itemCount: number;
  expectedDate: string;
  createdBy: string;
  createdAt: string;
  approvedBy?: string;
  approvedAt?: string;
  orderedAt?: string;
  receivedAt?: string;
  items: PurchaseOrderItem[];
  remarks?: string;
}

export interface PurchaseOrderItem {
  id: string;
  materialId: string;
  materialCode: string;
  materialName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  receivedQuantity?: number;
  remarks?: string;
}

// 成本相关类型
export interface CostAnalysis {
  orderId: string;
  orderNumber: string;
  materialCost: number;
  packagingWaste: number;
  moqWaste: number;
  cuttingWaste: number;
  expiryWaste: number;
  totalWaste: number;
  standardCost: number;
  actualCost: number;
  variance: number;
  variancePercentage: number;
  margin: number;
  marginPercentage: number;
}

// 服务BOM相关类型
export interface ServiceBOM {
  id: string;
  productId: string;
  productCode: string;
  productName: string;
  asBuiltBOM: BOMItem[];
  sparePartsList: SparePart[];
  maintenanceSchedule: MaintenanceItem[];
  qrCode: string;
  deviceSerial: string;
  customerInfo: CustomerInfo;
  warrantyInfo: WarrantyInfo;
  createdAt: string;
}

export interface SparePart {
  id: string;
  materialId: string;
  materialCode: string;
  materialName: string;
  interchangeableWith: string[];
  recommendedStock: number;
  currentStock: number;
  unitPrice: number;
  criticality: 'HIGH' | 'MEDIUM' | 'LOW';
}

export interface MaintenanceItem {
  id: string;
  description: string;
  interval: number;
  intervalUnit: 'DAYS' | 'MONTHS' | 'HOURS' | 'CYCLES';
  lastMaintenance?: string;
  nextMaintenance: string;
  requiredParts: string[];
  estimatedDuration: number;
}

export interface CustomerInfo {
  name: string;
  contact: string;
  phone: string;
  email: string;
  address: string;
}

export interface WarrantyInfo {
  startDate: string;
  endDate: string;
  terms: string;
  isActive: boolean;
}

// ECN相关类型
export interface ECN {
  id: string;
  number: string;
  title: string;
  description: string;
  reason: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'DRAFT' | 'REVIEW' | 'APPROVED' | 'REJECTED' | 'IMPLEMENTED';
  initiator: string;
  approvers: ECNApprover[];
  affectedItems: AffectedItem[];
  implementationPlan: string;
  effectiveDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ECNApprover {
  userId: string;
  userName: string;
  role: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  comment?: string;
  reviewTime?: string;
  approvedAt?: string;
}

export interface AffectedItem {
  type: 'BOM' | 'MATERIAL' | 'ORDER' | 'INVENTORY' | 'SUPPLIER' | 'SERVICE_BOM';
  itemId: string;
  itemName: string;
  changeDescription: string;
  impact: string;
}

export interface ECNComment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  createdAt: string;
  type: 'INFO' | 'SUGGESTION' | 'CONCERN' | 'APPROVAL';
}

// 通用类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  code: number;
}

export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

export interface SearchParams {
  keyword?: string;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  filters?: Array<{ text: string; value: string }>;
  render?: (value: any, record: any) => React.ReactNode;
}

// 仪表板相关类型
export interface DashboardMetrics {
  wasteReduction: WasteMetrics;
  costPerformance: CostMetrics;
  operationalMetrics: OperationalMetrics;
  serviceMetrics: ServiceMetrics;
  dataQuality: DataQualityMetrics;
}

export interface WasteMetrics {
  packagingWasteRate: number;
  moqWasteRate: number;
  cuttingWasteRate: number;
  expiryWasteRate: number;
  totalWasteRate: number;
  wasteReductionTrend: TrendData[];
}

export interface CostMetrics {
  orderMargin: number;
  costVariance: number;
  supplierPriceTrend: TrendData[];
  marginTrend: TrendData[];
}

export interface OperationalMetrics {
  consolidationRate: number;
  remnantUtilizationRate: number;
  inventoryTurnover: number;
  onTimeDeliveryRate: number;
}

export interface ServiceMetrics {
  firstTimeFixRate: number;
  mttr: number; // Mean Time To Repair
  sparePartsFulfillmentRate: number;
  customerSatisfactionScore: number;
}

export interface DataQualityMetrics {
  missingFieldsCount: number;
  duplicateRecordsCount: number;
  inconsistencyCount: number;
  dataCompletenessRate: number;
}

export interface TrendData {
  date: string;
  value: number;
  label?: string;
}

// 批次相关类型
export interface BatchInfo {
  id: string;
  batchNumber: string;
  materialId: string;
  materialCode: string;
  materialName: string;
  quantity: number;
  availableQuantity: number;
  location: string;
  expiryDate?: string;
  receivedDate: string;
  supplier: string;
  status: 'AVAILABLE' | 'RESERVED' | 'EXPIRED' | 'DAMAGED' | 'CONSUMED';
  qualityStatus: 'PASSED' | 'PENDING' | 'FAILED';
  unitPrice: number;
  totalValue: number;
  createdAt: string;
  updatedAt: string;
}

export interface BatchTransaction {
  id: string;
  batchId: string;
  batchNumber: string;
  type: 'IN' | 'OUT' | 'ADJUST' | 'TRANSFER';
  quantity: number;
  reason: string;
  operator: string;
  operatedAt: string;
  fromLocation?: string;
  toLocation?: string;
  relatedOrderId?: string;
  remarks?: string;
}

export interface BatchStatistics {
  totalBatches: number;
  activeBatches: number;
  expiredBatches: number;
  damagedBatches: number;
  totalValue: number;
  averageAge: number;
  turnoverRate: number;
  expiryRisk: number;
}
