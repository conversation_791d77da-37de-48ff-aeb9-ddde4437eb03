import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_BASE_URL, STORAGE_KEYS, ERROR_CODES } from '../constants';
import { ApiResponse } from '../types';
import { errorHandler, ErrorType } from '../utils/errorHandler';

class ApiService {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse<any>>) => {
        return response;
      },
      (error) => {
        if (error.response) {
          const { status, data } = error.response;
          
          switch (status) {
            case ERROR_CODES.UNAUTHORIZED:
              // 清除本地存储的认证信息
              localStorage.removeItem(STORAGE_KEYS.TOKEN);
              localStorage.removeItem(STORAGE_KEYS.USER_INFO);
              localStorage.removeItem(STORAGE_KEYS.CURRENT_ROLE);
              // 使用错误处理器处理权限错误
              errorHandler.handleError({
                type: ErrorType.PERMISSION,
                message: data?.message || '登录已过期，请重新登录',
                code: status,
                details: data,
                timestamp: Date.now()
              });
              // 重定向到登录页
              window.location.href = '/login';
              break;
            case ERROR_CODES.FORBIDDEN:
              errorHandler.handleError({
                type: ErrorType.HTTP,
                message: data?.message || '您没有权限访问此资源',
                code: status,
                details: data,
                timestamp: Date.now()
              });
              break;
            case ERROR_CODES.NOT_FOUND:
              errorHandler.handleError({
                type: ErrorType.HTTP,
                message: data?.message || '请求的资源不存在',
                code: status,
                details: data,
                timestamp: Date.now()
              });
              break;
            case ERROR_CODES.VALIDATION_ERROR:
              errorHandler.handleError({
                type: ErrorType.HTTP,
                message: data?.message || '数据验证失败',
                code: status,
                details: data,
                timestamp: Date.now()
              });
              break;
            case ERROR_CODES.SERVER_ERROR:
              errorHandler.handleError({
                type: ErrorType.HTTP,
                message: data?.message || '服务器内部错误，请稍后重试',
                code: status,
                details: data,
                timestamp: Date.now()
              });
              break;
            default:
              errorHandler.handleError({
                type: ErrorType.HTTP,
                message: data?.message || error.message || '请求失败',
                code: status,
                details: data,
                timestamp: Date.now()
              });
          }
        } else if (error.request) {
          errorHandler.handleError({
            type: ErrorType.NETWORK,
            message: '网络连接失败，请检查网络设置',
            details: error.request,
            timestamp: Date.now()
          });
        } else {
          errorHandler.handleError({
            type: ErrorType.UNKNOWN,
            message: `请求配置错误: ${error.message}`,
            details: error,
            timestamp: Date.now()
          });
        }
        
        return Promise.reject(error);
      }
    );
  }

  // GET 请求
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.get<ApiResponse<T>>(url, config);
      return response.data.data;
    } catch (error) {
      throw error; // 错误已在拦截器中处理
    }
  }

  // POST 请求
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.post<ApiResponse<T>>(url, data, config);
      return response.data.data;
    } catch (error) {
      throw error; // 错误已在拦截器中处理
    }
  }

  // PUT 请求
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.put<ApiResponse<T>>(url, data, config);
      return response.data.data;
    } catch (error) {
      throw error; // 错误已在拦截器中处理
    }
  }

  // DELETE 请求
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.delete<ApiResponse<T>>(url, config);
      return response.data.data;
    } catch (error) {
      throw error; // 错误已在拦截器中处理
    }
  }

  // PATCH 请求
  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.patch<ApiResponse<T>>(url, data, config);
      return response.data.data;
    } catch (error) {
      throw error; // 错误已在拦截器中处理
    }
  }

  // 文件上传
  async upload<T>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const config: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    };

      const response = await this.instance.post<ApiResponse<T>>(url, formData, config);
      return response.data.data;
    } catch (error) {
      throw error; // 错误已在拦截器中处理
    }
  }

  // 文件下载
  async download(url: string, filename?: string): Promise<void> {
    try {
      const response = await this.instance.get(url, {
        responseType: 'blob',
      });

      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      throw error; // 错误已在拦截器中处理
    }
  }

  // 获取原始axios实例（用于特殊需求）
  getInstance(): AxiosInstance {
    return this.instance;
  }
}

export const apiService = new ApiService();
export default apiService;
