{"ast": null, "code": "var _ErrorHandler;import{message,notification}from'antd';/**\n * 错误类型枚举\n */export let ErrorType=/*#__PURE__*/function(ErrorType){ErrorType[\"NETWORK\"]=\"NETWORK\";ErrorType[\"VALIDATION\"]=\"VALIDATION\";ErrorType[\"PERMISSION\"]=\"PERMISSION\";ErrorType[\"BUSINESS\"]=\"BUSINESS\";ErrorType[\"UNKNOWN\"]=\"UNKNOWN\";return ErrorType;}({});/**\n * 错误信息接口\n *//**\n * 全局错误处理器\n */class ErrorHandler{constructor(){this.errorLog=[];}static getInstance(){if(!ErrorHandler.instance){ErrorHandler.instance=new ErrorHandler();}return ErrorHandler.instance;}/**\n   * 处理HTTP错误\n   */handleHttpError(error){var _error$response,_error$response2;const errorInfo={type:ErrorType.NETWORK,code:((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)||0,message:this.getHttpErrorMessage(error),details:(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.data,timestamp:Date.now()};this.logError(errorInfo);this.showErrorNotification(errorInfo);return errorInfo;}/**\n   * 处理业务逻辑错误\n   */handleBusinessError(code,message,details){const errorInfo={type:ErrorType.BUSINESS,code,message,details,timestamp:Date.now()};this.logError(errorInfo);this.showErrorMessage(message);return errorInfo;}/**\n   * 处理验证错误\n   */handleValidationError(message,details){const errorInfo={type:ErrorType.VALIDATION,message,details,timestamp:Date.now()};this.logError(errorInfo);this.showErrorMessage(message);return errorInfo;}/**\n   * 处理权限错误\n   */handlePermissionError(){let message=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'您没有权限执行此操作';const errorInfo={type:ErrorType.PERMISSION,code:403,message,timestamp:Date.now()};this.logError(errorInfo);this.showErrorNotification(errorInfo);return errorInfo;}/**\n   * 处理未知错误\n   */handleUnknownError(error){const errorInfo={type:ErrorType.UNKNOWN,message:error.message||'发生未知错误',details:{stack:error.stack,name:error.name},timestamp:Date.now()};this.logError(errorInfo);this.showErrorNotification(errorInfo);return errorInfo;}/**\n   * 获取HTTP错误消息\n   */getHttpErrorMessage(error){var _error$response3,_error$response4;const status=(_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.status;const data=(_error$response4=error.response)===null||_error$response4===void 0?void 0:_error$response4.data;// 优先使用服务器返回的错误消息\nif(data!==null&&data!==void 0&&data.message){return data.message;}// 根据状态码返回默认消息\nswitch(status){case 400:return'请求参数错误';case 401:return'登录已过期，请重新登录';case 403:return'您没有权限执行此操作';case 404:return'请求的资源不存在';case 408:return'请求超时，请稍后重试';case 409:return'数据冲突，请刷新后重试';case 422:return'数据验证失败';case 429:return'请求过于频繁，请稍后重试';case 500:return'服务器内部错误';case 502:return'网关错误';case 503:return'服务暂时不可用';case 504:return'网关超时';default:if(error.code==='NETWORK_ERROR'||!status){return'网络连接失败，请检查网络设置';}return\"\\u8BF7\\u6C42\\u5931\\u8D25 (\".concat(status,\")\");}}/**\n   * 显示错误消息\n   */showErrorMessage(msg){message.error(msg);}/**\n   * 显示错误通知\n   */showErrorNotification(errorInfo){notification.error({message:'操作失败',description:errorInfo.message,duration:4.5});}/**\n   * 记录错误日志\n   */logError(errorInfo){console.error('[ErrorHandler]',errorInfo);// 保存到本地日志（最多保存100条）\nthis.errorLog.unshift(errorInfo);if(this.errorLog.length>100){this.errorLog=this.errorLog.slice(0,100);}// 在生产环境中，可以在这里添加错误上报逻辑\nif(process.env.NODE_ENV==='production'){this.reportError(errorInfo);}}/**\n   * 上报错误（生产环境）\n   */reportError(errorInfo){// TODO: 实现错误上报逻辑\n// 可以发送到错误监控服务，如 Sentry、Bugsnag 等\nconsole.log('报告错误到监控服务:',errorInfo);}/**\n   * 获取错误日志\n   */getErrorLog(){return[...this.errorLog];}/**\n   * 清空错误日志\n   */clearErrorLog(){this.errorLog=[];}}// 导出单例实例\n_ErrorHandler=ErrorHandler;ErrorHandler.instance=void 0;export const errorHandler=ErrorHandler.getInstance();// 导出便捷方法\nexport const handleHttpError=error=>errorHandler.handleHttpError(error);export const handleBusinessError=(code,message,details)=>errorHandler.handleBusinessError(code,message,details);export const handleValidationError=(message,details)=>errorHandler.handleValidationError(message,details);export const handlePermissionError=message=>errorHandler.handlePermissionError(message);export const handleUnknownError=error=>errorHandler.handleUnknownError(error);", "map": {"version": 3, "names": ["message", "notification", "ErrorType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "errorLog", "getInstance", "instance", "handleHttpError", "error", "_error$response", "_error$response2", "errorInfo", "type", "NETWORK", "code", "response", "status", "getHttpErrorMessage", "details", "data", "timestamp", "Date", "now", "logError", "showErrorNotification", "handleBusinessError", "BUSINESS", "showErrorMessage", "handleValidationError", "VALIDATION", "handlePermissionError", "arguments", "length", "undefined", "PERMISSION", "handleUnknownError", "UNKNOWN", "stack", "name", "_error$response3", "_error$response4", "concat", "msg", "description", "duration", "console", "unshift", "slice", "process", "env", "NODE_ENV", "reportError", "log", "getErrorLog", "clearErrorLog", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/utils/errorHandler.ts"], "sourcesContent": ["import { message, notification } from 'antd';\nimport { AxiosError } from 'axios';\n\n/**\n * 错误类型枚举\n */\nexport enum ErrorType {\n  NETWORK = 'NETWORK',\n  VALIDATION = 'VALIDATION',\n  PERMISSION = 'PERMISSION',\n  BUSINESS = 'BUSINESS',\n  UNKNOWN = 'UNKNOWN'\n}\n\n/**\n * 错误信息接口\n */\nexport interface ErrorInfo {\n  type: ErrorType;\n  code?: string | number;\n  message: string;\n  details?: any;\n  timestamp: number;\n}\n\n/**\n * 全局错误处理器\n */\nclass ErrorHandler {\n  private static instance: ErrorHandler;\n  private errorLog: ErrorInfo[] = [];\n\n  private constructor() {}\n\n  static getInstance(): ErrorHandler {\n    if (!ErrorHandler.instance) {\n      ErrorHandler.instance = new ErrorHandler();\n    }\n    return ErrorHandler.instance;\n  }\n\n  /**\n   * 处理HTTP错误\n   */\n  handleHttpError(error: AxiosError): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.NETWORK,\n      code: error.response?.status || 0,\n      message: this.getHttpErrorMessage(error),\n      details: error.response?.data,\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 处理业务逻辑错误\n   */\n  handleBusinessError(code: string | number, message: string, details?: any): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.BUSINESS,\n      code,\n      message,\n      details,\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorMessage(message);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 处理验证错误\n   */\n  handleValidationError(message: string, details?: any): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.VALIDATION,\n      message,\n      details,\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorMessage(message);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 处理权限错误\n   */\n  handlePermissionError(message: string = '您没有权限执行此操作'): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.PERMISSION,\n      code: 403,\n      message,\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 处理未知错误\n   */\n  handleUnknownError(error: Error): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.UNKNOWN,\n      message: error.message || '发生未知错误',\n      details: {\n        stack: error.stack,\n        name: error.name\n      },\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 获取HTTP错误消息\n   */\n  private getHttpErrorMessage(error: AxiosError): string {\n    const status = error.response?.status;\n    const data = error.response?.data as any;\n\n    // 优先使用服务器返回的错误消息\n    if (data?.message) {\n      return data.message;\n    }\n\n    // 根据状态码返回默认消息\n    switch (status) {\n      case 400:\n        return '请求参数错误';\n      case 401:\n        return '登录已过期，请重新登录';\n      case 403:\n        return '您没有权限执行此操作';\n      case 404:\n        return '请求的资源不存在';\n      case 408:\n        return '请求超时，请稍后重试';\n      case 409:\n        return '数据冲突，请刷新后重试';\n      case 422:\n        return '数据验证失败';\n      case 429:\n        return '请求过于频繁，请稍后重试';\n      case 500:\n        return '服务器内部错误';\n      case 502:\n        return '网关错误';\n      case 503:\n        return '服务暂时不可用';\n      case 504:\n        return '网关超时';\n      default:\n        if (error.code === 'NETWORK_ERROR' || !status) {\n          return '网络连接失败，请检查网络设置';\n        }\n        return `请求失败 (${status})`;\n    }\n  }\n\n  /**\n   * 显示错误消息\n   */\n  private showErrorMessage(msg: string) {\n    message.error(msg);\n  }\n\n  /**\n   * 显示错误通知\n   */\n  private showErrorNotification(errorInfo: ErrorInfo) {\n    notification.error({\n      message: '操作失败',\n      description: errorInfo.message,\n      duration: 4.5,\n    });\n  }\n\n  /**\n   * 记录错误日志\n   */\n  private logError(errorInfo: ErrorInfo) {\n    console.error('[ErrorHandler]', errorInfo);\n    \n    // 保存到本地日志（最多保存100条）\n    this.errorLog.unshift(errorInfo);\n    if (this.errorLog.length > 100) {\n      this.errorLog = this.errorLog.slice(0, 100);\n    }\n\n    // 在生产环境中，可以在这里添加错误上报逻辑\n    if (process.env.NODE_ENV === 'production') {\n      this.reportError(errorInfo);\n    }\n  }\n\n  /**\n   * 上报错误（生产环境）\n   */\n  private reportError(errorInfo: ErrorInfo) {\n    // TODO: 实现错误上报逻辑\n    // 可以发送到错误监控服务，如 Sentry、Bugsnag 等\n    console.log('报告错误到监控服务:', errorInfo);\n  }\n\n  /**\n   * 获取错误日志\n   */\n  getErrorLog(): ErrorInfo[] {\n    return [...this.errorLog];\n  }\n\n  /**\n   * 清空错误日志\n   */\n  clearErrorLog() {\n    this.errorLog = [];\n  }\n}\n\n// 导出单例实例\nexport const errorHandler = ErrorHandler.getInstance();\n\n// 导出便捷方法\nexport const handleHttpError = (error: AxiosError) => errorHandler.handleHttpError(error);\nexport const handleBusinessError = (code: string | number, message: string, details?: any) => \n  errorHandler.handleBusinessError(code, message, details);\nexport const handleValidationError = (message: string, details?: any) => \n  errorHandler.handleValidationError(message, details);\nexport const handlePermissionError = (message?: string) => \n  errorHandler.handlePermissionError(message);\nexport const handleUnknownError = (error: Error) => \n  errorHandler.handleUnknownError(error);"], "mappings": "kBAAA,OAASA,OAAO,CAAEC,YAAY,KAAQ,MAAM,CAG5C;AACA;AACA,GACA,UAAY,CAAAC,SAAS,uBAATA,SAAS,EAATA,SAAS,sBAATA,SAAS,4BAATA,SAAS,4BAATA,SAAS,wBAATA,SAAS,4BAAT,CAAAA,SAAS,OAQrB;AACA;AACA,GASA;AACA;AACA,GACA,KAAM,CAAAC,YAAa,CAITC,WAAWA,CAAA,CAAG,MAFdC,QAAQ,CAAgB,EAAE,CAEX,CAEvB,MAAO,CAAAC,WAAWA,CAAA,CAAiB,CACjC,GAAI,CAACH,YAAY,CAACI,QAAQ,CAAE,CAC1BJ,YAAY,CAACI,QAAQ,CAAG,GAAI,CAAAJ,YAAY,CAAC,CAAC,CAC5C,CACA,MAAO,CAAAA,YAAY,CAACI,QAAQ,CAC9B,CAEA;AACF;AACA,KACEC,eAAeA,CAACC,KAAiB,CAAa,KAAAC,eAAA,CAAAC,gBAAA,CAC5C,KAAM,CAAAC,SAAoB,CAAG,CAC3BC,IAAI,CAAEX,SAAS,CAACY,OAAO,CACvBC,IAAI,CAAE,EAAAL,eAAA,CAAAD,KAAK,CAACO,QAAQ,UAAAN,eAAA,iBAAdA,eAAA,CAAgBO,MAAM,GAAI,CAAC,CACjCjB,OAAO,CAAE,IAAI,CAACkB,mBAAmB,CAACT,KAAK,CAAC,CACxCU,OAAO,EAAAR,gBAAA,CAAEF,KAAK,CAACO,QAAQ,UAAAL,gBAAA,iBAAdA,gBAAA,CAAgBS,IAAI,CAC7BC,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC,CACxB,IAAI,CAACa,qBAAqB,CAACb,SAAS,CAAC,CAErC,MAAO,CAAAA,SAAS,CAClB,CAEA;AACF;AACA,KACEc,mBAAmBA,CAACX,IAAqB,CAAEf,OAAe,CAAEmB,OAAa,CAAa,CACpF,KAAM,CAAAP,SAAoB,CAAG,CAC3BC,IAAI,CAAEX,SAAS,CAACyB,QAAQ,CACxBZ,IAAI,CACJf,OAAO,CACPmB,OAAO,CACPE,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC,CACxB,IAAI,CAACgB,gBAAgB,CAAC5B,OAAO,CAAC,CAE9B,MAAO,CAAAY,SAAS,CAClB,CAEA;AACF;AACA,KACEiB,qBAAqBA,CAAC7B,OAAe,CAAEmB,OAAa,CAAa,CAC/D,KAAM,CAAAP,SAAoB,CAAG,CAC3BC,IAAI,CAAEX,SAAS,CAAC4B,UAAU,CAC1B9B,OAAO,CACPmB,OAAO,CACPE,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC,CACxB,IAAI,CAACgB,gBAAgB,CAAC5B,OAAO,CAAC,CAE9B,MAAO,CAAAY,SAAS,CAClB,CAEA;AACF;AACA,KACEmB,qBAAqBA,CAAA,CAA4C,IAA3C,CAAA/B,OAAe,CAAAgC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,YAAY,CAClD,KAAM,CAAApB,SAAoB,CAAG,CAC3BC,IAAI,CAAEX,SAAS,CAACiC,UAAU,CAC1BpB,IAAI,CAAE,GAAG,CACTf,OAAO,CACPqB,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC,CACxB,IAAI,CAACa,qBAAqB,CAACb,SAAS,CAAC,CAErC,MAAO,CAAAA,SAAS,CAClB,CAEA;AACF;AACA,KACEwB,kBAAkBA,CAAC3B,KAAY,CAAa,CAC1C,KAAM,CAAAG,SAAoB,CAAG,CAC3BC,IAAI,CAAEX,SAAS,CAACmC,OAAO,CACvBrC,OAAO,CAAES,KAAK,CAACT,OAAO,EAAI,QAAQ,CAClCmB,OAAO,CAAE,CACPmB,KAAK,CAAE7B,KAAK,CAAC6B,KAAK,CAClBC,IAAI,CAAE9B,KAAK,CAAC8B,IACd,CAAC,CACDlB,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC,CACxB,IAAI,CAACa,qBAAqB,CAACb,SAAS,CAAC,CAErC,MAAO,CAAAA,SAAS,CAClB,CAEA;AACF;AACA,KACUM,mBAAmBA,CAACT,KAAiB,CAAU,KAAA+B,gBAAA,CAAAC,gBAAA,CACrD,KAAM,CAAAxB,MAAM,EAAAuB,gBAAA,CAAG/B,KAAK,CAACO,QAAQ,UAAAwB,gBAAA,iBAAdA,gBAAA,CAAgBvB,MAAM,CACrC,KAAM,CAAAG,IAAI,EAAAqB,gBAAA,CAAGhC,KAAK,CAACO,QAAQ,UAAAyB,gBAAA,iBAAdA,gBAAA,CAAgBrB,IAAW,CAExC;AACA,GAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEpB,OAAO,CAAE,CACjB,MAAO,CAAAoB,IAAI,CAACpB,OAAO,CACrB,CAEA;AACA,OAAQiB,MAAM,EACZ,IAAK,IAAG,CACN,MAAO,QAAQ,CACjB,IAAK,IAAG,CACN,MAAO,aAAa,CACtB,IAAK,IAAG,CACN,MAAO,YAAY,CACrB,IAAK,IAAG,CACN,MAAO,UAAU,CACnB,IAAK,IAAG,CACN,MAAO,YAAY,CACrB,IAAK,IAAG,CACN,MAAO,aAAa,CACtB,IAAK,IAAG,CACN,MAAO,QAAQ,CACjB,IAAK,IAAG,CACN,MAAO,cAAc,CACvB,IAAK,IAAG,CACN,MAAO,SAAS,CAClB,IAAK,IAAG,CACN,MAAO,MAAM,CACf,IAAK,IAAG,CACN,MAAO,SAAS,CAClB,IAAK,IAAG,CACN,MAAO,MAAM,CACf,QACE,GAAIR,KAAK,CAACM,IAAI,GAAK,eAAe,EAAI,CAACE,MAAM,CAAE,CAC7C,MAAO,gBAAgB,CACzB,CACA,mCAAAyB,MAAA,CAAgBzB,MAAM,MAC1B,CACF,CAEA;AACF;AACA,KACUW,gBAAgBA,CAACe,GAAW,CAAE,CACpC3C,OAAO,CAACS,KAAK,CAACkC,GAAG,CAAC,CACpB,CAEA;AACF;AACA,KACUlB,qBAAqBA,CAACb,SAAoB,CAAE,CAClDX,YAAY,CAACQ,KAAK,CAAC,CACjBT,OAAO,CAAE,MAAM,CACf4C,WAAW,CAAEhC,SAAS,CAACZ,OAAO,CAC9B6C,QAAQ,CAAE,GACZ,CAAC,CAAC,CACJ,CAEA;AACF;AACA,KACUrB,QAAQA,CAACZ,SAAoB,CAAE,CACrCkC,OAAO,CAACrC,KAAK,CAAC,gBAAgB,CAAEG,SAAS,CAAC,CAE1C;AACA,IAAI,CAACP,QAAQ,CAAC0C,OAAO,CAACnC,SAAS,CAAC,CAChC,GAAI,IAAI,CAACP,QAAQ,CAAC4B,MAAM,CAAG,GAAG,CAAE,CAC9B,IAAI,CAAC5B,QAAQ,CAAG,IAAI,CAACA,QAAQ,CAAC2C,KAAK,CAAC,CAAC,CAAE,GAAG,CAAC,CAC7C,CAEA;AACA,GAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,YAAY,CAAE,CACzC,IAAI,CAACC,WAAW,CAACxC,SAAS,CAAC,CAC7B,CACF,CAEA;AACF;AACA,KACUwC,WAAWA,CAACxC,SAAoB,CAAE,CACxC;AACA;AACAkC,OAAO,CAACO,GAAG,CAAC,YAAY,CAAEzC,SAAS,CAAC,CACtC,CAEA;AACF;AACA,KACE0C,WAAWA,CAAA,CAAgB,CACzB,MAAO,CAAC,GAAG,IAAI,CAACjD,QAAQ,CAAC,CAC3B,CAEA;AACF;AACA,KACEkD,aAAaA,CAAA,CAAG,CACd,IAAI,CAAClD,QAAQ,CAAG,EAAE,CACpB,CACF,CAEA;AAAAmD,aAAA,CAjNMrD,YAAY,CAAZA,YAAY,CACDI,QAAQ,QAiNzB,MAAO,MAAM,CAAAkD,YAAY,CAAGtD,YAAY,CAACG,WAAW,CAAC,CAAC,CAEtD;AACA,MAAO,MAAM,CAAAE,eAAe,CAAIC,KAAiB,EAAKgD,YAAY,CAACjD,eAAe,CAACC,KAAK,CAAC,CACzF,MAAO,MAAM,CAAAiB,mBAAmB,CAAGA,CAACX,IAAqB,CAAEf,OAAe,CAAEmB,OAAa,GACvFsC,YAAY,CAAC/B,mBAAmB,CAACX,IAAI,CAAEf,OAAO,CAAEmB,OAAO,CAAC,CAC1D,MAAO,MAAM,CAAAU,qBAAqB,CAAGA,CAAC7B,OAAe,CAAEmB,OAAa,GAClEsC,YAAY,CAAC5B,qBAAqB,CAAC7B,OAAO,CAAEmB,OAAO,CAAC,CACtD,MAAO,MAAM,CAAAY,qBAAqB,CAAI/B,OAAgB,EACpDyD,YAAY,CAAC1B,qBAAqB,CAAC/B,OAAO,CAAC,CAC7C,MAAO,MAAM,CAAAoC,kBAAkB,CAAI3B,KAAY,EAC7CgD,YAAY,CAACrB,kBAAkB,CAAC3B,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}