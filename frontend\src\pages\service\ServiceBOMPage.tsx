import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Select,
  Row,
  Col,
  Tag,
  Modal,
  Form,
  InputNumber,
  Descriptions,
  Tabs,
  Tree,
  Alert,
  Tooltip,
  Divider,
  Badge,
  Checkbox,
} from 'antd';
import * as XLSX from 'xlsx';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  ToolOutlined,
  SettingOutlined,
  SearchOutlined,
  ExportOutlined,
  ImportOutlined,
  FileTextOutlined,
  BranchesOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchServiceBOMs } from '../../store/slices/serviceSlice';
import { formatCurrency } from '../../utils';

const { Title, Text } = Typography;
const { Search } = Input;
const { TextArea } = Input;
const { TabPane } = Tabs;

const ServiceBOMPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { serviceBOMs, loading } = useAppSelector(state => state.service);
  
  const [searchKeyword, setSearchKeyword] = useState('');
  const [deviceTypeFilter, setDeviceTypeFilter] = useState<string>('');
  const [bomModalVisible, setBomModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingBOM, setEditingBOM] = useState<any>(null);
  const [selectedBOM, setSelectedBOM] = useState<any>(null);
  const [bomForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('list');
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');
  const [exportFields, setExportFields] = useState<string[]>(['bomCode', 'bomName', 'deviceType', 'deviceModel', 'version', 'status', 'totalValue', 'itemCount']);

  useEffect(() => {
    loadData();
  }, [searchKeyword, deviceTypeFilter]);

  const loadData = () => {
    dispatch(fetchServiceBOMs({
      keyword: searchKeyword,
      deviceType: deviceTypeFilter,
    }));
  };

  const handleAddBOM = () => {
    setEditingBOM(null);
    bomForm.resetFields();
    setBomModalVisible(true);
  };

  const handleEditBOM = (record: any) => {
    setEditingBOM(record);
    bomForm.setFieldsValue(record);
    setBomModalVisible(true);
  };

  const handleViewBOM = (record: any) => {
    setSelectedBOM(record);
    setDetailModalVisible(true);
  };

  const handleBOMModalOk = async () => {
    try {
      const values = await bomForm.validateFields();
      
      if (editingBOM) {
        // TODO: 实现更新API
        Modal.success({
          title: '更新成功',
          content: '服务BOM已更新',
        });
      } else {
        // TODO: 实现创建API
        Modal.success({
          title: '添加成功',
          content: '服务BOM已添加',
        });
      }
      
      setBomModalVisible(false);
      loadData();
    } catch (error) {
      Modal.error({
        title: '操作失败',
        content: '保存服务BOM时发生错误',
      });
    }
  };

  const handleDeleteBOM = async (record: any) => {
    try {
      // TODO: 实现删除API
      Modal.success({
        title: '删除成功',
        content: '服务BOM已删除',
      });
      loadData();
    } catch (error) {
      Modal.error({
        title: '删除失败',
        content: '删除服务BOM时发生错误',
      });
    }
  };

  // 处理导出
  const handleExport = () => {
    setExportModalVisible(true);
  };

  const executeExport = () => {
    try {
      // 准备导出数据
      const exportData = mockServiceBOMs.map(bom => {
        const data: any = {};
        
        if (exportFields.includes('bomCode')) data['BOM编码'] = bom.bomCode;
        if (exportFields.includes('bomName')) data['BOM名称'] = bom.bomName;
        if (exportFields.includes('deviceType')) data['设备类型'] = bom.deviceType;
        if (exportFields.includes('deviceModel')) data['设备型号'] = bom.deviceModel;
        if (exportFields.includes('version')) data['版本'] = bom.version;
        if (exportFields.includes('status')) data['状态'] = bom.status;
        if (exportFields.includes('totalValue')) data['总价值'] = bom.totalValue;
        if (exportFields.includes('itemCount')) data['项目数'] = bom.itemCount;
        if (exportFields.includes('createdBy')) data['创建人'] = bom.createdBy;
        if (exportFields.includes('createdAt')) data['创建时间'] = bom.createdAt;
        if (exportFields.includes('description')) data['描述'] = bom.description;
        
        return data;
      });

      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '服务BOM列表');

      // 下载文件
      const fileName = `服务BOM列表_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;
      XLSX.writeFile(wb, fileName);
      
      Modal.success({
        title: '导出成功',
        content: '服务BOM数据已导出',
      });
      setExportModalVisible(false);
    } catch (error) {
      Modal.error({
        title: '导出失败',
        content: '导出服务BOM数据时发生错误',
      });
    }
  };

  // 模拟服务BOM数据
  const mockServiceBOMs = [
    {
      id: '1',
      bomCode: 'SBOM-ANT-001',
      bomName: '5G天线系统服务BOM',
      deviceType: '天线设备',
      deviceModel: 'ANT-5G-V2.0',
      version: 'V1.0',
      status: '已发布',
      createdBy: 'service_tech',
      createdAt: '2024-01-15T00:00:00Z',
      description: '5G天线系统的标准服务BOM，包含常用备件和维护工具',
      items: [
        {
          id: '1',
          itemType: '备件',
          itemCode: 'SPARE-001',
          itemName: '天线连接器',
          specification: 'N型连接器',
          quantity: 2,
          unit: 'PCS',
          unitPrice: 150,
          supplier: '华为技术',
          stockLevel: 'A',
          leadTime: 7,
          remarks: '关键备件，建议常备',
        },
        {
          id: '2',
          itemType: '工具',
          itemCode: 'TOOL-001',
          itemName: '扭矩扳手',
          specification: '10-50Nm',
          quantity: 1,
          unit: 'PCS',
          unitPrice: 800,
          supplier: '工具供应商',
          stockLevel: 'B',
          leadTime: 14,
          remarks: '维护必备工具',
        },
        {
          id: '3',
          itemType: '耗材',
          itemCode: 'CONS-001',
          itemName: '防水胶带',
          specification: '3M胶带',
          quantity: 5,
          unit: 'ROLL',
          unitPrice: 25,
          supplier: '3M公司',
          stockLevel: 'C',
          leadTime: 3,
          remarks: '防水密封用',
        },
      ],
      totalValue: 1075,
      itemCount: 3,
    },
    {
      id: '2',
      bomCode: 'SBOM-RF-002',
      bomName: 'RF功率放大器服务BOM',
      deviceType: 'RF设备',
      deviceModel: 'RF-AMP-100W',
      version: 'V1.1',
      status: '草稿',
      createdBy: 'service_tech',
      createdAt: '2024-02-01T00:00:00Z',
      description: 'RF功率放大器的服务BOM，包含功率模块等关键备件',
      items: [
        {
          id: '1',
          itemType: '备件',
          itemCode: 'SPARE-002',
          itemName: '功率模块',
          specification: '100W模块',
          quantity: 1,
          unit: 'PCS',
          unitPrice: 2500,
          supplier: '中兴通讯',
          stockLevel: 'A',
          leadTime: 14,
          remarks: '核心备件',
        },
        {
          id: '2',
          itemType: '备件',
          itemCode: 'SPARE-003',
          itemName: '散热器',
          specification: '铝制散热器',
          quantity: 1,
          unit: 'PCS',
          unitPrice: 300,
          supplier: '散热器厂商',
          stockLevel: 'B',
          leadTime: 7,
          remarks: '散热用',
        },
      ],
      totalValue: 2800,
      itemCount: 2,
    },
  ];

  const bomColumns = [
    {
      title: 'BOM编码',
      dataIndex: 'bomCode',
      key: 'bomCode',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: 'BOM名称',
      dataIndex: 'bomName',
      key: 'bomName',
      ellipsis: true,
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      key: 'deviceType',
      width: 100,
      render: (type: string) => (
        <Tag color={type === '天线设备' ? 'blue' : type === 'RF设备' ? 'green' : 'orange'}>
          {type}
        </Tag>
      ),
    },
    {
      title: '设备型号',
      dataIndex: 'deviceModel',
      key: 'deviceModel',
      width: 120,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
    },
    {
      title: '项目数',
      dataIndex: 'itemCount',
      key: 'itemCount',
      width: 80,
      render: (count: number) => (
        <Badge count={count} showZero color="#1890ff" />
      ),
    },
    {
      title: '总价值',
      dataIndex: 'totalValue',
      key: 'totalValue',
      width: 100,
      render: (value: number) => formatCurrency(value),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const color = status === '已发布' ? 'green' :
                     status === '草稿' ? 'orange' : 'default';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right' as const,
      render: (_: any, record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewBOM(record)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => {
                // TODO: 实现复制功能
                Modal.success({
                  title: '复制成功',
                  content: '服务BOM已复制',
                });
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditBOM(record)}
              disabled={record.status === '已发布'}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: '确定要删除这个服务BOM吗？',
                  onOk: () => handleDeleteBOM(record),
                });
              }}
              disabled={record.status === '已发布'}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const itemColumns = [
    {
      title: '项目类型',
      dataIndex: 'itemType',
      key: 'itemType',
      width: 80,
      render: (type: string) => {
        const color = type === '备件' ? 'red' : type === '工具' ? 'blue' : 'green';
        return <Tag color={color}>{type}</Tag>;
      },
    },
    {
      title: '项目编码',
      dataIndex: 'itemCode',
      key: 'itemCode',
      width: 120,
    },
    {
      title: '项目名称',
      dataIndex: 'itemName',
      key: 'itemName',
      ellipsis: true,
    },
    {
      title: '规格',
      dataIndex: 'specification',
      key: 'specification',
      ellipsis: true,
    },
    {
      title: '数量',
      key: 'quantity',
      width: 80,
      render: (_: any, record: any) => (
        <Text>{record.quantity} {record.unit}</Text>
      ),
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (price: number) => formatCurrency(price),
    },
    {
      title: '总价',
      key: 'totalPrice',
      width: 100,
      render: (_: any, record: any) => formatCurrency(record.quantity * record.unitPrice),
    },
    {
      title: '库存等级',
      dataIndex: 'stockLevel',
      key: 'stockLevel',
      width: 80,
      render: (level: string) => {
        const color = level === 'A' ? 'red' : level === 'B' ? 'orange' : 'green';
        return <Tag color={color}>{level}</Tag>;
      },
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      ellipsis: true,
    },
    {
      title: '交期',
      dataIndex: 'leadTime',
      key: 'leadTime',
      width: 80,
      render: (days: number) => `${days}天`,
    },
  ];

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              服务BOM管理
            </Title>
            <Text type="secondary">
              管理设备的服务BOM，包括备件、工具、耗材等维护所需物料清单
            </Text>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索BOM编码、名称"
                allowClear
                style={{ width: 200 }}
                onSearch={setSearchKeyword}
              />
              <Select
                placeholder="设备类型"
                allowClear
                style={{ width: 120 }}
                value={deviceTypeFilter}
                onChange={setDeviceTypeFilter}
                options={[
                  { label: '天线设备', value: '天线设备' },
                  { label: 'RF设备', value: 'RF设备' },
                  { label: '控制设备', value: '控制设备' },
                  { label: '传输设备', value: '传输设备' },
                  { label: '电源设备', value: '电源设备' },
                ]}
              />
              <Button icon={<ImportOutlined />}>
                导入
              </Button>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddBOM}>
                新建服务BOM
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={bomColumns}
          dataSource={mockServiceBOMs}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1200 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 服务BOM详情模态框 */}
      <Modal
        title="服务BOM详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        width={1200}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          <Button key="export" type="primary" icon={<ExportOutlined />}>
            导出
          </Button>,
        ]}
      >
        {selectedBOM && (
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="BOM编码">{selectedBOM.bomCode}</Descriptions.Item>
                <Descriptions.Item label="BOM名称">{selectedBOM.bomName}</Descriptions.Item>
                <Descriptions.Item label="设备类型">{selectedBOM.deviceType}</Descriptions.Item>
                <Descriptions.Item label="设备型号">{selectedBOM.deviceModel}</Descriptions.Item>
                <Descriptions.Item label="版本">{selectedBOM.version}</Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={selectedBOM.status === '已发布' ? 'green' : 'orange'}>
                    {selectedBOM.status}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="项目数量">{selectedBOM.itemCount}</Descriptions.Item>
                <Descriptions.Item label="总价值">{formatCurrency(selectedBOM.totalValue)}</Descriptions.Item>
                <Descriptions.Item label="创建人">{selectedBOM.createdBy}</Descriptions.Item>
                <Descriptions.Item label="创建时间">{selectedBOM.createdAt}</Descriptions.Item>
                <Descriptions.Item label="描述" span={2}>{selectedBOM.description}</Descriptions.Item>
              </Descriptions>
            </TabPane>
            <TabPane tab="BOM明细" key="items">
              <Table
                columns={itemColumns}
                dataSource={selectedBOM.items}
                rowKey="id"
                pagination={false}
                scroll={{ x: 1000 }}
                summary={() => (
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={6}>
                      <Text strong>合计</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={6}>
                      <Text strong>
                        {formatCurrency(selectedBOM.items?.reduce((sum: number, item: any) =>
                          sum + (item.quantity * item.unitPrice), 0) || 0)}
                      </Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={7} colSpan={3} />
                  </Table.Summary.Row>
                )}
              />
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 导出模态框 */}
      <Modal
        title="导出服务BOM"
        open={exportModalVisible}
        onOk={executeExport}
        onCancel={() => setExportModalVisible(false)}
        okText="导出"
        cancelText="取消"
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ display: 'block', marginBottom: 8 }}>导出格式：</label>
          <Select
            value={exportFormat}
            onChange={setExportFormat}
            style={{ width: '100%' }}
          >
            <Select.Option value="excel">Excel (.xlsx)</Select.Option>
            <Select.Option value="csv">CSV (.csv)</Select.Option>
          </Select>
        </div>
        
        <div>
          <label style={{ display: 'block', marginBottom: 8 }}>导出字段：</label>
          <Checkbox.Group
            value={exportFields}
            onChange={setExportFields}
            style={{ width: '100%' }}
          >
            <Row>
              <Col span={12}>
                <Checkbox value="bomCode">BOM编码</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="bomName">BOM名称</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="deviceType">设备类型</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="deviceModel">设备型号</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="version">版本</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="status">状态</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="totalValue">总价值</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="itemCount">项目数</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="createdBy">创建人</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="createdAt">创建时间</Checkbox>
              </Col>
              <Col span={12}>
                <Checkbox value="description">描述</Checkbox>
              </Col>
            </Row>
          </Checkbox.Group>
        </div>
      </Modal>
    </div>
  );
};

export default ServiceBOMPage;
