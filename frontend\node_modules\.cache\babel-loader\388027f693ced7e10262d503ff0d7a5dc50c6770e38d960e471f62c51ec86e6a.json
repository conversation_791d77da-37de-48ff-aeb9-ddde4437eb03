{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Row,Col,Statistic,Typography,Modal,Form,Input,InputNumber,Select,DatePicker,Alert,Tabs,Tag,Tooltip,Progress,Divider,message,Checkbox}from'antd';import{PlusOutlined,CalculatorOutlined,ScissorOutlined,ExportOutlined,ReloadOutlined,EyeOutlined,EditOutlined,DeleteOutlined,PlayCircleOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{TabPane}=Tabs;const{TextArea}=Input;const CuttingPlanPage=()=>{const[loading,setLoading]=useState(false);const[activeTab,setActiveTab]=useState('plans');const[planModalVisible,setPlanModalVisible]=useState(false);const[optimizeModalVisible,setOptimizeModalVisible]=useState(false);const[schemeModalVisible,setSchemeModalVisible]=useState(false);const[selectedPlan,setSelectedPlan]=useState(null);const[planForm]=Form.useForm();const[optimizeForm]=Form.useForm();const loadData=()=>{setLoading(true);setTimeout(()=>{setLoading(false);},1000);};useEffect(()=>{loadData();},[]);const handleCreatePlan=()=>{setPlanModalVisible(true);planForm.resetFields();};const handleOptimize=record=>{setSelectedPlan(record);setOptimizeModalVisible(true);optimizeForm.resetFields();};const handleViewScheme=record=>{setSelectedPlan(record);setSchemeModalVisible(true);};const handlePlanModalOk=()=>{planForm.validateFields().then(values=>{console.log('创建切割计划:',values);message.success('切割计划创建成功');setPlanModalVisible(false);loadData();});};const handleOptimizeModalOk=()=>{optimizeForm.validateFields().then(values=>{console.log('优化参数:',values);message.success('切割优化计算已开始，请稍候查看结果');setOptimizeModalVisible(false);loadData();});};// 模拟切割计划数据\nconst mockPlans=[{id:'1',planName:'5G基站电缆切割计划-202403',materialCode:'CABLE-001',materialName:'同轴电缆',specification:'RG-58/U',standardLength:100,unit:'M',unitPrice:4.5,totalDemand:1250,planDate:'2024-03-25T00:00:00Z',status:'optimized',algorithm:'首次适应算法',utilizationRate:92.5,wasteRate:7.5,totalCost:5625,wasteCost:421.88,createdBy:'工艺工程师',createdDate:'2024-03-20T00:00:00Z'},{id:'2',planName:'不锈钢板切割计划-202403',materialCode:'STEEL-PLATE-001',materialName:'不锈钢板',specification:'304-2mm',standardLength:2000,unit:'MM',unitPrice:0.15,totalDemand:8500,planDate:'2024-03-28T00:00:00Z',status:'calculating',algorithm:'最佳适应算法',utilizationRate:0,wasteRate:0,totalCost:1275,wasteCost:0,createdBy:'生产计划员',createdDate:'2024-03-22T00:00:00Z'},{id:'3',planName:'铜线切割计划-202403',materialCode:'COPPER-WIRE-001',materialName:'铜线',specification:'2.5mm²',standardLength:500,unit:'M',unitPrice:8.2,totalDemand:2800,planDate:'2024-03-30T00:00:00Z',status:'draft',algorithm:'',utilizationRate:0,wasteRate:0,totalCost:22960,wasteCost:0,createdBy:'工艺工程师',createdDate:'2024-03-24T00:00:00Z'}];// 模拟切割方案数据\nconst mockSchemes=[{id:'1',planId:'1',schemeNo:1,pattern:'25M + 25M + 25M + 25M',cuts:[25,25,25,25],quantity:8,utilization:100,waste:0,wasteLength:0},{id:'2',planId:'1',schemeNo:2,pattern:'30M + 30M + 30M + 10M',cuts:[30,30,30,10],quantity:6,utilization:100,waste:0,wasteLength:0},{id:'3',planId:'1',schemeNo:3,pattern:'35M + 35M + 20M + 8M',cuts:[35,35,20,8],quantity:4,utilization:98,waste:2,wasteLength:2}];// 模拟需求数据\nconst mockDemands=[{id:'1',length:25,quantity:32,priority:'HIGH',orderCode:'ORD-5G-001',customerName:'中国移动',dueDate:'2024-04-05T00:00:00Z'},{id:'2',length:30,quantity:18,priority:'HIGH',orderCode:'ORD-5G-002',customerName:'中国联通',dueDate:'2024-04-08T00:00:00Z'},{id:'3',length:35,quantity:8,priority:'MEDIUM',orderCode:'ORD-5G-003',customerName:'中国电信',dueDate:'2024-04-10T00:00:00Z'},{id:'4',length:20,quantity:4,priority:'MEDIUM',orderCode:'ORD-5G-004',customerName:'华为技术',dueDate:'2024-04-12T00:00:00Z'}];const planColumns=[{title:'计划名称',dataIndex:'planName',key:'planName',width:200,fixed:'left'},{title:'物料信息',key:'material',width:200,render:record=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:record.materialCode}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'12px'},children:record.materialName})]})},{title:'规格',dataIndex:'specification',key:'specification',width:120},{title:'标准长度',key:'standardLength',width:100,render:record=>\"\".concat(record.standardLength).concat(record.unit)},{title:'总需求',key:'totalDemand',width:100,render:record=>\"\".concat(record.totalDemand).concat(record.unit)},{title:'利用率',dataIndex:'utilizationRate',key:'utilizationRate',width:100,render:value=>/*#__PURE__*/_jsx(Progress,{percent:value,size:\"small\",status:value>=90?'success':value>=80?'normal':'exception'})},{title:'浪费率',dataIndex:'wasteRate',key:'wasteRate',width:100,render:value=>/*#__PURE__*/_jsxs(Tag,{color:value<=5?'green':value<=10?'orange':'red',children:[value.toFixed(1),\"%\"]})},{title:'状态',dataIndex:'status',key:'status',width:100,render:status=>{const statusConfig={draft:{color:'default',text:'草稿'},calculating:{color:'processing',text:'计算中'},optimized:{color:'success',text:'已优化'},executing:{color:'warning',text:'执行中'},completed:{color:'success',text:'已完成'}};const config=statusConfig[status];return/*#__PURE__*/_jsx(Tag,{color:config.color,children:config.text});}},{title:'计划日期',dataIndex:'planDate',key:'planDate',width:120,render:date=>new Date(date).toLocaleDateString()},{title:'操作',key:'actions',width:200,fixed:'right',render:record=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\\u65B9\\u6848\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewScheme(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u4F18\\u5316\\u8BA1\\u7B97\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(CalculatorOutlined,{}),onClick:()=>handleOptimize(record),disabled:record.status==='calculating'})}),record.status==='optimized'&&/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5F00\\u59CB\\u6267\\u884C\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),onClick:()=>{message.success('切割计划已开始执行');loadData();}})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),disabled:record.status==='executing'||record.status==='completed'})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>{Modal.confirm({title:'确认删除',content:'确定要删除这个切割计划吗？',onOk:()=>{message.success('删除成功');loadData();}});}})})]})}];const schemeColumns=[{title:'方案编号',dataIndex:'schemeNo',key:'schemeNo',width:80},{title:'切割模式',dataIndex:'pattern',key:'pattern',width:200},{title:'切割长度',dataIndex:'cuts',key:'cuts',width:150,render:cuts=>cuts.join(' + ')},{title:'数量',dataIndex:'quantity',key:'quantity',width:80},{title:'利用率',dataIndex:'utilization',key:'utilization',width:100,render:value=>\"\".concat(value,\"%\")},{title:'浪费长度',dataIndex:'wasteLength',key:'wasteLength',width:100,render:(value,record)=>\"\".concat(value).concat((selectedPlan===null||selectedPlan===void 0?void 0:selectedPlan.unit)||'M')}];const demandColumns=[{title:'长度',dataIndex:'length',key:'length',width:80,render:value=>\"\".concat(value,\"M\")},{title:'数量',dataIndex:'quantity',key:'quantity',width:80},{title:'优先级',dataIndex:'priority',key:'priority',width:80,render:priority=>/*#__PURE__*/_jsx(Tag,{color:priority==='HIGH'?'red':priority==='MEDIUM'?'orange':'green',children:priority==='HIGH'?'高':priority==='MEDIUM'?'中':'低'})},{title:'订单编号',dataIndex:'orderCode',key:'orderCode',width:120},{title:'客户',dataIndex:'customerName',key:'customerName',width:120},{title:'交期',dataIndex:'dueDate',key:'dueDate',width:100,render:date=>new Date(date).toLocaleDateString()}];const renderStatistics=()=>/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:12,sm:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u8BA1\\u5212\\u6570\",value:mockPlans.length,prefix:/*#__PURE__*/_jsx(ScissorOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E73\\u5747\\u5229\\u7528\\u7387\",value:88.3,suffix:\"%\",valueStyle:{color:'#3f8600'}})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u8282\\u7EA6\\u6210\\u672C\",value:1250,prefix:\"\\xA5\",precision:0,valueStyle:{color:'#3f8600'}})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:6,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6D6A\\u8D39\\u91D1\\u989D\",value:421.88,prefix:\"\\xA5\",precision:2,valueStyle:{color:'#cf1322'}})})})]});const renderPlansTab=()=>/*#__PURE__*/_jsxs(\"div\",{children:[renderStatistics(),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleCreatePlan,children:\"\\u65B0\\u5EFA\\u8BA1\\u5212\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\\u8BA1\\u5212\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:loadData,children:\"\\u5237\\u65B0\"})]})})}),/*#__PURE__*/_jsx(Alert,{message:\"\\u5207\\u5272\\u4F18\\u5316\\u63D0\\u793A\",description:\"\\u7CFB\\u7EDF\\u4F1A\\u6839\\u636E\\u9700\\u6C42\\u81EA\\u52A8\\u751F\\u6210\\u6700\\u4F18\\u5207\\u5272\\u65B9\\u6848\\uFF0C\\u5EFA\\u8BAE\\u5B9A\\u671F\\u68C0\\u67E5\\u8BA1\\u5212\\u6267\\u884C\\u60C5\\u51B5\\uFF0C\\u53CA\\u65F6\\u8C03\\u6574\\u4F18\\u5316\\u53C2\\u6570\\u3002\",type:\"info\",showIcon:true,closable:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Table,{columns:planColumns,dataSource:mockPlans,loading:loading,rowKey:\"id\",scroll:{x:1400},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]})]});return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u5207\\u5272\\u8BA1\\u5212\\u7BA1\\u7406\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u7BA1\\u7406\\u957F\\u5EA6\\u578B\\u6750\\u6599\\u7684\\u5207\\u5272\\u8BA1\\u5212\\uFF0C\\u4F18\\u5316\\u6750\\u6599\\u5229\\u7528\\u7387\\uFF0C\\u51CF\\u5C11\\u6D6A\\u8D39\"})]})}),/*#__PURE__*/_jsx(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(ScissorOutlined,{}),\"\\u5207\\u5272\\u8BA1\\u5212\"]}),children:renderPlansTab()},\"plans\")})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u65B0\\u5EFA\\u5207\\u5272\\u8BA1\\u5212\",open:planModalVisible,onOk:handlePlanModalOk,onCancel:()=>setPlanModalVisible(false),okText:\"\\u521B\\u5EFA\",cancelText:\"\\u53D6\\u6D88\",width:800,children:/*#__PURE__*/_jsxs(Form,{form:planForm,layout:\"vertical\",children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"planName\",label:\"\\u8BA1\\u5212\\u540D\\u79F0\",rules:[{required:true,message:'请输入计划名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BA1\\u5212\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"materialCode\",label:\"\\u7269\\u6599\\u7F16\\u7801\",rules:[{required:true,message:'请选择物料'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u7269\\u6599\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"CABLE-001\",children:\"CABLE-001 - \\u540C\\u8F74\\u7535\\u7F06\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"STEEL-PLATE-001\",children:\"STEEL-PLATE-001 - \\u4E0D\\u9508\\u94A2\\u677F\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"COPPER-WIRE-001\",children:\"COPPER-WIRE-001 - \\u94DC\\u7EBF\"})]})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"standardLength\",label:\"\\u6807\\u51C6\\u957F\\u5EA6\",rules:[{required:true,message:'请输入标准长度'}],children:/*#__PURE__*/_jsx(InputNumber,{min:1,style:{width:'100%'},placeholder:\"\\u6807\\u51C6\\u957F\\u5EA6\"})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"unit\",label:\"\\u5355\\u4F4D\",rules:[{required:true,message:'请选择单位'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u5355\\u4F4D\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"M\",children:\"\\u7C73(M)\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"MM\",children:\"\\u6BEB\\u7C73(MM)\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"CM\",children:\"\\u5398\\u7C73(CM)\"})]})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"unitPrice\",label:\"\\u5355\\u4EF7\",rules:[{required:true,message:'请输入单价'}],children:/*#__PURE__*/_jsx(InputNumber,{min:0,precision:2,style:{width:'100%'},placeholder:\"\\u5355\\u4EF7\",addonBefore:\"\\xA5\"})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"planDate\",label:\"\\u8BA1\\u5212\\u65E5\\u671F\",rules:[{required:true,message:'请选择计划日期'}],children:/*#__PURE__*/_jsx(DatePicker,{style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"algorithm\",label:\"\\u4F18\\u5316\\u7B97\\u6CD5\",initialValue:\"first_fit\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u4F18\\u5316\\u7B97\\u6CD5\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"first_fit\",children:\"\\u9996\\u6B21\\u9002\\u5E94\\u7B97\\u6CD5\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"best_fit\",children:\"\\u6700\\u4F73\\u9002\\u5E94\\u7B97\\u6CD5\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"worst_fit\",children:\"\\u6700\\u574F\\u9002\\u5E94\\u7B97\\u6CD5\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"genetic\",children:\"\\u9057\\u4F20\\u7B97\\u6CD5\"})]})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u8BA1\\u5212\\u63CF\\u8FF0\",children:/*#__PURE__*/_jsx(TextArea,{rows:3,placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BA1\\u5212\\u63CF\\u8FF0\"})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5207\\u5272\\u4F18\\u5316\\u8BBE\\u7F6E\",open:optimizeModalVisible,onOk:handleOptimizeModalOk,onCancel:()=>setOptimizeModalVisible(false),okText:\"\\u5F00\\u59CB\\u4F18\\u5316\",cancelText:\"\\u53D6\\u6D88\",width:600,children:/*#__PURE__*/_jsxs(Form,{form:optimizeForm,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u4F18\\u5316\\u53C2\\u6570\\u8BBE\\u7F6E\",description:\"\\u8C03\\u6574\\u4F18\\u5316\\u53C2\\u6570\\u53EF\\u4EE5\\u83B7\\u5F97\\u4E0D\\u540C\\u7684\\u5207\\u5272\\u65B9\\u6848\\uFF0C\\u5EFA\\u8BAE\\u6839\\u636E\\u5B9E\\u9645\\u751F\\u4EA7\\u60C5\\u51B5\\u9009\\u62E9\\u5408\\u9002\\u7684\\u53C2\\u6570\\u3002\",type:\"info\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Form.Item,{name:\"algorithm\",label:\"\\u4F18\\u5316\\u7B97\\u6CD5\",initialValue:\"best_fit\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u4F18\\u5316\\u7B97\\u6CD5\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"first_fit\",children:\"\\u9996\\u6B21\\u9002\\u5E94\\u7B97\\u6CD5 - \\u5FEB\\u901F\\u8BA1\\u7B97\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"best_fit\",children:\"\\u6700\\u4F73\\u9002\\u5E94\\u7B97\\u6CD5 - \\u5E73\\u8861\\u6548\\u679C\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"worst_fit\",children:\"\\u6700\\u574F\\u9002\\u5E94\\u7B97\\u6CD5 - \\u51CF\\u5C11\\u4F59\\u6599\\u79CD\\u7C7B\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"genetic\",children:\"\\u9057\\u4F20\\u7B97\\u6CD5 - \\u6700\\u4F18\\u89E3\"})]})}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"minUtilization\",label:\"\\u6700\\u5C0F\\u5229\\u7528\\u7387(%)\",initialValue:85,children:/*#__PURE__*/_jsx(InputNumber,{min:50,max:100,style:{width:'100%'},placeholder:\"\\u6700\\u5C0F\\u5229\\u7528\\u7387\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"maxWasteLength\",label:\"\\u6700\\u5927\\u6D6A\\u8D39\\u957F\\u5EA6\",initialValue:5,children:/*#__PURE__*/_jsx(InputNumber,{min:0,style:{width:'100%'},placeholder:\"\\u6700\\u5927\\u6D6A\\u8D39\\u957F\\u5EA6\",addonAfter:(selectedPlan===null||selectedPlan===void 0?void 0:selectedPlan.unit)||'M'})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"priorityOrder\",label:\"\\u4F18\\u5148\\u7EA7\\u6392\\u5E8F\",children:/*#__PURE__*/_jsx(Checkbox.Group,{options:[{label:'优先满足高优先级订单',value:'priority'},{label:'优先满足紧急交期',value:'dueDate'},{label:'优先减少浪费',value:'waste'},{label:'优先减少方案数量',value:'schemes'}],defaultValue:['priority','waste']})}),/*#__PURE__*/_jsx(Form.Item,{name:\"allowRemnant\",valuePropName:\"checked\",initialValue:true,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u5141\\u8BB8\\u751F\\u6210\\u4F59\\u6599\"})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5207\\u5272\\u65B9\\u6848\\u8BE6\\u60C5 - \".concat(selectedPlan===null||selectedPlan===void 0?void 0:selectedPlan.planName),open:schemeModalVisible,onCancel:()=>setSchemeModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setSchemeModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\"),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\\u65B9\\u6848\"},\"export\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),children:\"\\u6267\\u884C\\u8BA1\\u5212\"},\"execute\")],width:1000,children:selectedPlan&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u5229\\u7528\\u7387\",value:selectedPlan.utilizationRate,suffix:\"%\",valueStyle:{color:'#3f8600'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6D6A\\u8D39\\u7387\",value:selectedPlan.wasteRate,suffix:\"%\",valueStyle:{color:'#cf1322'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u6210\\u672C\",value:selectedPlan.totalCost,prefix:\"\\xA5\",precision:2})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6D6A\\u8D39\\u6210\\u672C\",value:selectedPlan.wasteCost,prefix:\"\\xA5\",precision:2,valueStyle:{color:'#cf1322'}})})]}),/*#__PURE__*/_jsx(Divider,{orientation:\"left\",children:\"\\u9700\\u6C42\\u6E05\\u5355\"}),/*#__PURE__*/_jsx(Table,{columns:demandColumns,dataSource:mockDemands,rowKey:\"id\",size:\"small\",pagination:false,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Divider,{orientation:\"left\",children:\"\\u5207\\u5272\\u65B9\\u6848\"}),/*#__PURE__*/_jsx(Table,{columns:schemeColumns,dataSource:mockSchemes,rowKey:\"id\",size:\"small\",pagination:false})]})})]});};export default CuttingPlanPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Row", "Col", "Statistic", "Typography", "Modal", "Form", "Input", "InputNumber", "Select", "DatePicker", "<PERSON><PERSON>", "Tabs", "Tag", "<PERSON><PERSON><PERSON>", "Progress", "Divider", "message", "Checkbox", "PlusOutlined", "CalculatorOutlined", "ScissorOutlined", "ExportOutlined", "ReloadOutlined", "EyeOutlined", "EditOutlined", "DeleteOutlined", "PlayCircleOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "TabPane", "TextArea", "CuttingPlanPage", "loading", "setLoading", "activeTab", "setActiveTab", "planModalVisible", "setPlanModalVisible", "optimizeModalVisible", "setOptimizeModalVisible", "schemeModalVisible", "setSchemeModalVisible", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "planForm", "useForm", "optimizeForm", "loadData", "setTimeout", "handleCreatePlan", "resetFields", "handleOptimize", "record", "handleViewScheme", "handlePlanModalOk", "validateFields", "then", "values", "console", "log", "success", "handleOptimizeModalOk", "mockPlans", "id", "planName", "materialCode", "materialName", "specification", "standardLength", "unit", "unitPrice", "totalDemand", "planDate", "status", "algorithm", "utilizationRate", "wasteRate", "totalCost", "wasteCost", "created<PERSON>y", "createdDate", "mockSchemes", "planId", "schemeNo", "pattern", "cuts", "quantity", "utilization", "waste", "wasteLength", "mockDemands", "length", "priority", "orderCode", "customerName", "dueDate", "planColumns", "title", "dataIndex", "key", "width", "fixed", "render", "children", "type", "style", "fontSize", "concat", "value", "percent", "size", "color", "toFixed", "statusConfig", "draft", "text", "calculating", "optimized", "executing", "completed", "config", "date", "Date", "toLocaleDateString", "icon", "onClick", "disabled", "danger", "confirm", "content", "onOk", "schemeColumns", "join", "demandColumns", "renderStatistics", "gutter", "marginBottom", "xs", "sm", "prefix", "suffix", "valueStyle", "precision", "renderPlansTab", "justify", "align", "description", "showIcon", "closable", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "level", "margin", "active<PERSON><PERSON>", "onChange", "tab", "open", "onCancel", "okText", "cancelText", "form", "layout", "span", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "Option", "min", "addonBefore", "initialValue", "rows", "max", "addonAfter", "Group", "options", "defaultValue", "valuePropName", "footer", "orientation"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/inventory/CuttingPlanPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Row,\n  Col,\n  Statistic,\n  Typography,\n  Modal,\n  Form,\n  Input,\n  InputNumber,\n  Select,\n  DatePicker,\n  Alert,\n  Tabs,\n  Tag,\n  Tooltip,\n  Progress,\n  Divider,\n  message,\n  Checkbox,\n} from 'antd';\nimport {\n  PlusOutlined,\n  CalculatorOutlined,\n  Sc<PERSON>orOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  ExportOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  CheckCircleOutlined,\n} from '@ant-design/icons';\nimport { formatCurrency } from '../../utils/format';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { TextArea } = Input;\n\ninterface CuttingPlan {\n  id: string;\n  planName: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  standardLength: number;\n  unit: string;\n  unitPrice: number;\n  totalDemand: number;\n  planDate: string;\n  status: 'draft' | 'calculating' | 'optimized' | 'executing' | 'completed';\n  algorithm: string;\n  utilizationRate: number;\n  wasteRate: number;\n  totalCost: number;\n  wasteCost: number;\n  createdBy: string;\n  createdDate: string;\n}\n\ninterface CuttingScheme {\n  id: string;\n  planId: string;\n  schemeNo: number;\n  pattern: string;\n  cuts: number[];\n  quantity: number;\n  utilization: number;\n  waste: number;\n  wasteLength: number;\n}\n\ninterface DemandItem {\n  id: string;\n  length: number;\n  quantity: number;\n  priority: string;\n  orderCode: string;\n  customerName: string;\n  dueDate: string;\n}\n\nconst CuttingPlanPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('plans');\n  const [planModalVisible, setPlanModalVisible] = useState(false);\n  const [optimizeModalVisible, setOptimizeModalVisible] = useState(false);\n  const [schemeModalVisible, setSchemeModalVisible] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState<CuttingPlan | null>(null);\n  const [planForm] = Form.useForm();\n  const [optimizeForm] = Form.useForm();\n\n  const loadData = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n  };\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const handleCreatePlan = () => {\n    setPlanModalVisible(true);\n    planForm.resetFields();\n  };\n\n  const handleOptimize = (record: CuttingPlan) => {\n    setSelectedPlan(record);\n    setOptimizeModalVisible(true);\n    optimizeForm.resetFields();\n  };\n\n  const handleViewScheme = (record: CuttingPlan) => {\n    setSelectedPlan(record);\n    setSchemeModalVisible(true);\n  };\n\n  const handlePlanModalOk = () => {\n    planForm.validateFields().then(values => {\n      console.log('创建切割计划:', values);\n      message.success('切割计划创建成功');\n      setPlanModalVisible(false);\n      loadData();\n    });\n  };\n\n  const handleOptimizeModalOk = () => {\n    optimizeForm.validateFields().then(values => {\n      console.log('优化参数:', values);\n      message.success('切割优化计算已开始，请稍候查看结果');\n      setOptimizeModalVisible(false);\n      loadData();\n    });\n  };\n\n  // 模拟切割计划数据\n  const mockPlans: CuttingPlan[] = [\n    {\n      id: '1',\n      planName: '5G基站电缆切割计划-202403',\n      materialCode: 'CABLE-001',\n      materialName: '同轴电缆',\n      specification: 'RG-58/U',\n      standardLength: 100,\n      unit: 'M',\n      unitPrice: 4.5,\n      totalDemand: 1250,\n      planDate: '2024-03-25T00:00:00Z',\n      status: 'optimized',\n      algorithm: '首次适应算法',\n      utilizationRate: 92.5,\n      wasteRate: 7.5,\n      totalCost: 5625,\n      wasteCost: 421.88,\n      createdBy: '工艺工程师',\n      createdDate: '2024-03-20T00:00:00Z',\n    },\n    {\n      id: '2',\n      planName: '不锈钢板切割计划-202403',\n      materialCode: 'STEEL-PLATE-001',\n      materialName: '不锈钢板',\n      specification: '304-2mm',\n      standardLength: 2000,\n      unit: 'MM',\n      unitPrice: 0.15,\n      totalDemand: 8500,\n      planDate: '2024-03-28T00:00:00Z',\n      status: 'calculating',\n      algorithm: '最佳适应算法',\n      utilizationRate: 0,\n      wasteRate: 0,\n      totalCost: 1275,\n      wasteCost: 0,\n      createdBy: '生产计划员',\n      createdDate: '2024-03-22T00:00:00Z',\n    },\n    {\n      id: '3',\n      planName: '铜线切割计划-202403',\n      materialCode: 'COPPER-WIRE-001',\n      materialName: '铜线',\n      specification: '2.5mm²',\n      standardLength: 500,\n      unit: 'M',\n      unitPrice: 8.2,\n      totalDemand: 2800,\n      planDate: '2024-03-30T00:00:00Z',\n      status: 'draft',\n      algorithm: '',\n      utilizationRate: 0,\n      wasteRate: 0,\n      totalCost: 22960,\n      wasteCost: 0,\n      createdBy: '工艺工程师',\n      createdDate: '2024-03-24T00:00:00Z',\n    },\n  ];\n\n  // 模拟切割方案数据\n  const mockSchemes: CuttingScheme[] = [\n    {\n      id: '1',\n      planId: '1',\n      schemeNo: 1,\n      pattern: '25M + 25M + 25M + 25M',\n      cuts: [25, 25, 25, 25],\n      quantity: 8,\n      utilization: 100,\n      waste: 0,\n      wasteLength: 0,\n    },\n    {\n      id: '2',\n      planId: '1',\n      schemeNo: 2,\n      pattern: '30M + 30M + 30M + 10M',\n      cuts: [30, 30, 30, 10],\n      quantity: 6,\n      utilization: 100,\n      waste: 0,\n      wasteLength: 0,\n    },\n    {\n      id: '3',\n      planId: '1',\n      schemeNo: 3,\n      pattern: '35M + 35M + 20M + 8M',\n      cuts: [35, 35, 20, 8],\n      quantity: 4,\n      utilization: 98,\n      waste: 2,\n      wasteLength: 2,\n    },\n  ];\n\n  // 模拟需求数据\n  const mockDemands: DemandItem[] = [\n    {\n      id: '1',\n      length: 25,\n      quantity: 32,\n      priority: 'HIGH',\n      orderCode: 'ORD-5G-001',\n      customerName: '中国移动',\n      dueDate: '2024-04-05T00:00:00Z',\n    },\n    {\n      id: '2',\n      length: 30,\n      quantity: 18,\n      priority: 'HIGH',\n      orderCode: 'ORD-5G-002',\n      customerName: '中国联通',\n      dueDate: '2024-04-08T00:00:00Z',\n    },\n    {\n      id: '3',\n      length: 35,\n      quantity: 8,\n      priority: 'MEDIUM',\n      orderCode: 'ORD-5G-003',\n      customerName: '中国电信',\n      dueDate: '2024-04-10T00:00:00Z',\n    },\n    {\n      id: '4',\n      length: 20,\n      quantity: 4,\n      priority: 'MEDIUM',\n      orderCode: 'ORD-5G-004',\n      customerName: '华为技术',\n      dueDate: '2024-04-12T00:00:00Z',\n    },\n  ];\n\n  const planColumns = [\n    {\n      title: '计划名称',\n      dataIndex: 'planName',\n      key: 'planName',\n      width: 200,\n      fixed: 'left' as const,\n    },\n    {\n      title: '物料信息',\n      key: 'material',\n      width: 200,\n      render: (record: CuttingPlan) => (\n        <div>\n          <div>{record.materialCode}</div>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.materialName}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '规格',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 120,\n    },\n    {\n      title: '标准长度',\n      key: 'standardLength',\n      width: 100,\n      render: (record: CuttingPlan) => `${record.standardLength}${record.unit}`,\n    },\n    {\n      title: '总需求',\n      key: 'totalDemand',\n      width: 100,\n      render: (record: CuttingPlan) => `${record.totalDemand}${record.unit}`,\n    },\n    {\n      title: '利用率',\n      dataIndex: 'utilizationRate',\n      key: 'utilizationRate',\n      width: 100,\n      render: (value: number) => (\n        <Progress\n          percent={value}\n          size=\"small\"\n          status={value >= 90 ? 'success' : value >= 80 ? 'normal' : 'exception'}\n        />\n      ),\n    },\n    {\n      title: '浪费率',\n      dataIndex: 'wasteRate',\n      key: 'wasteRate',\n      width: 100,\n      render: (value: number) => (\n        <Tag color={value <= 5 ? 'green' : value <= 10 ? 'orange' : 'red'}>\n          {value.toFixed(1)}%\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => {\n        const statusConfig = {\n          draft: { color: 'default', text: '草稿' },\n          calculating: { color: 'processing', text: '计算中' },\n          optimized: { color: 'success', text: '已优化' },\n          executing: { color: 'warning', text: '执行中' },\n          completed: { color: 'success', text: '已完成' },\n        };\n        const config = statusConfig[status as keyof typeof statusConfig];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n    {\n      title: '计划日期',\n      dataIndex: 'planDate',\n      key: 'planDate',\n      width: 120,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 200,\n      fixed: 'right' as const,\n      render: (record: CuttingPlan) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看方案\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewScheme(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"优化计算\">\n            <Button\n              type=\"text\"\n              icon={<CalculatorOutlined />}\n              onClick={() => handleOptimize(record)}\n              disabled={record.status === 'calculating'}\n            />\n          </Tooltip>\n          {record.status === 'optimized' && (\n            <Tooltip title=\"开始执行\">\n              <Button\n                type=\"text\"\n                icon={<PlayCircleOutlined />}\n                onClick={() => {\n                  message.success('切割计划已开始执行');\n                  loadData();\n                }}\n              />\n            </Tooltip>\n          )}\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              disabled={record.status === 'executing' || record.status === 'completed'}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => {\n                Modal.confirm({\n                  title: '确认删除',\n                  content: '确定要删除这个切割计划吗？',\n                  onOk: () => {\n                    message.success('删除成功');\n                    loadData();\n                  },\n                });\n              }}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  const schemeColumns = [\n    {\n      title: '方案编号',\n      dataIndex: 'schemeNo',\n      key: 'schemeNo',\n      width: 80,\n    },\n    {\n      title: '切割模式',\n      dataIndex: 'pattern',\n      key: 'pattern',\n      width: 200,\n    },\n    {\n      title: '切割长度',\n      dataIndex: 'cuts',\n      key: 'cuts',\n      width: 150,\n      render: (cuts: number[]) => cuts.join(' + '),\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n    },\n    {\n      title: '利用率',\n      dataIndex: 'utilization',\n      key: 'utilization',\n      width: 100,\n      render: (value: number) => `${value}%`,\n    },\n    {\n      title: '浪费长度',\n      dataIndex: 'wasteLength',\n      key: 'wasteLength',\n      width: 100,\n      render: (value: number, record: CuttingScheme) => \n        `${value}${selectedPlan?.unit || 'M'}`,\n    },\n  ];\n\n  const demandColumns = [\n    {\n      title: '长度',\n      dataIndex: 'length',\n      key: 'length',\n      width: 80,\n      render: (value: number) => `${value}M`,\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n    },\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      width: 80,\n      render: (priority: string) => (\n        <Tag color={priority === 'HIGH' ? 'red' : priority === 'MEDIUM' ? 'orange' : 'green'}>\n          {priority === 'HIGH' ? '高' : priority === 'MEDIUM' ? '中' : '低'}\n        </Tag>\n      ),\n    },\n    {\n      title: '订单编号',\n      dataIndex: 'orderCode',\n      key: 'orderCode',\n      width: 120,\n    },\n    {\n      title: '客户',\n      dataIndex: 'customerName',\n      key: 'customerName',\n      width: 120,\n    },\n    {\n      title: '交期',\n      dataIndex: 'dueDate',\n      key: 'dueDate',\n      width: 100,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n  ];\n\n  const renderStatistics = () => (\n    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n      <Col xs={12} sm={6}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"总计划数\"\n            value={mockPlans.length}\n            prefix={<ScissorOutlined />}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={6}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"平均利用率\"\n            value={88.3}\n            suffix=\"%\"\n            valueStyle={{ color: '#3f8600' }}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={6}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"总节约成本\"\n            value={1250}\n            prefix=\"¥\"\n            precision={0}\n            valueStyle={{ color: '#3f8600' }}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={6}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"浪费金额\"\n            value={421.88}\n            prefix=\"¥\"\n            precision={2}\n            valueStyle={{ color: '#cf1322' }}\n          />\n        </Card>\n      </Col>\n    </Row>\n  );\n\n  const renderPlansTab = () => (\n    <div>\n      {renderStatistics()}\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={handleCreatePlan}\n              >\n                新建计划\n              </Button>\n              <Button icon={<ExportOutlined />}>\n                导出计划\n              </Button>\n              <Button icon={<ReloadOutlined />} onClick={loadData}>\n                刷新\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Alert\n          message=\"切割优化提示\"\n          description=\"系统会根据需求自动生成最优切割方案，建议定期检查计划执行情况，及时调整优化参数。\"\n          type=\"info\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n\n        <Table\n          columns={planColumns}\n          dataSource={mockPlans}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              切割计划管理\n            </Title>\n            <Text type=\"secondary\">\n              管理长度型材料的切割计划，优化材料利用率，减少浪费\n            </Text>\n          </Col>\n        </Row>\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab={<span><ScissorOutlined />切割计划</span>} key=\"plans\">\n            {renderPlansTab()}\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 新建计划模态框 */}\n      <Modal\n        title=\"新建切割计划\"\n        open={planModalVisible}\n        onOk={handlePlanModalOk}\n        onCancel={() => setPlanModalVisible(false)}\n        okText=\"创建\"\n        cancelText=\"取消\"\n        width={800}\n      >\n        <Form form={planForm} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"planName\"\n                label=\"计划名称\"\n                rules={[{ required: true, message: '请输入计划名称' }]}\n              >\n                <Input placeholder=\"请输入计划名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"materialCode\"\n                label=\"物料编码\"\n                rules={[{ required: true, message: '请选择物料' }]}\n              >\n                <Select placeholder=\"请选择物料\">\n                  <Select.Option value=\"CABLE-001\">CABLE-001 - 同轴电缆</Select.Option>\n                  <Select.Option value=\"STEEL-PLATE-001\">STEEL-PLATE-001 - 不锈钢板</Select.Option>\n                  <Select.Option value=\"COPPER-WIRE-001\">COPPER-WIRE-001 - 铜线</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"standardLength\"\n                label=\"标准长度\"\n                rules={[{ required: true, message: '请输入标准长度' }]}\n              >\n                <InputNumber\n                  min={1}\n                  style={{ width: '100%' }}\n                  placeholder=\"标准长度\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"unit\"\n                label=\"单位\"\n                rules={[{ required: true, message: '请选择单位' }]}\n              >\n                <Select placeholder=\"请选择单位\">\n                  <Select.Option value=\"M\">米(M)</Select.Option>\n                  <Select.Option value=\"MM\">毫米(MM)</Select.Option>\n                  <Select.Option value=\"CM\">厘米(CM)</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"unitPrice\"\n                label=\"单价\"\n                rules={[{ required: true, message: '请输入单价' }]}\n              >\n                <InputNumber\n                  min={0}\n                  precision={2}\n                  style={{ width: '100%' }}\n                  placeholder=\"单价\"\n                  addonBefore=\"¥\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"planDate\"\n                label=\"计划日期\"\n                rules={[{ required: true, message: '请选择计划日期' }]}\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"algorithm\"\n                label=\"优化算法\"\n                initialValue=\"first_fit\"\n              >\n                <Select placeholder=\"请选择优化算法\">\n                  <Select.Option value=\"first_fit\">首次适应算法</Select.Option>\n                  <Select.Option value=\"best_fit\">最佳适应算法</Select.Option>\n                  <Select.Option value=\"worst_fit\">最坏适应算法</Select.Option>\n                  <Select.Option value=\"genetic\">遗传算法</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"description\" label=\"计划描述\">\n            <TextArea rows={3} placeholder=\"请输入计划描述\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 优化计算模态框 */}\n      <Modal\n        title=\"切割优化设置\"\n        open={optimizeModalVisible}\n        onOk={handleOptimizeModalOk}\n        onCancel={() => setOptimizeModalVisible(false)}\n        okText=\"开始优化\"\n        cancelText=\"取消\"\n        width={600}\n      >\n        <Form form={optimizeForm} layout=\"vertical\">\n          <Alert\n            message=\"优化参数设置\"\n            description=\"调整优化参数可以获得不同的切割方案，建议根据实际生产情况选择合适的参数。\"\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n\n          <Form.Item\n            name=\"algorithm\"\n            label=\"优化算法\"\n            initialValue=\"best_fit\"\n          >\n            <Select placeholder=\"请选择优化算法\">\n              <Select.Option value=\"first_fit\">首次适应算法 - 快速计算</Select.Option>\n              <Select.Option value=\"best_fit\">最佳适应算法 - 平衡效果</Select.Option>\n              <Select.Option value=\"worst_fit\">最坏适应算法 - 减少余料种类</Select.Option>\n              <Select.Option value=\"genetic\">遗传算法 - 最优解</Select.Option>\n            </Select>\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"minUtilization\"\n                label=\"最小利用率(%)\"\n                initialValue={85}\n              >\n                <InputNumber\n                  min={50}\n                  max={100}\n                  style={{ width: '100%' }}\n                  placeholder=\"最小利用率\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"maxWasteLength\"\n                label=\"最大浪费长度\"\n                initialValue={5}\n              >\n                <InputNumber\n                  min={0}\n                  style={{ width: '100%' }}\n                  placeholder=\"最大浪费长度\"\n                  addonAfter={selectedPlan?.unit || 'M'}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"priorityOrder\" label=\"优先级排序\">\n            <Checkbox.Group\n              options={[\n                { label: '优先满足高优先级订单', value: 'priority' },\n                { label: '优先满足紧急交期', value: 'dueDate' },\n                { label: '优先减少浪费', value: 'waste' },\n                { label: '优先减少方案数量', value: 'schemes' },\n              ]}\n              defaultValue={['priority', 'waste']}\n            />\n          </Form.Item>\n\n          <Form.Item name=\"allowRemnant\" valuePropName=\"checked\" initialValue={true}>\n            <Checkbox>允许生成余料</Checkbox>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 切割方案模态框 */}\n      <Modal\n        title={`切割方案详情 - ${selectedPlan?.planName}`}\n        open={schemeModalVisible}\n        onCancel={() => setSchemeModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setSchemeModalVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"export\" icon={<ExportOutlined />}>\n            导出方案\n          </Button>,\n          <Button key=\"execute\" type=\"primary\" icon={<PlayCircleOutlined />}>\n            执行计划\n          </Button>,\n        ]}\n        width={1000}\n      >\n        {selectedPlan && (\n          <div>\n            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n              <Col span={6}>\n                <Statistic\n                  title=\"总利用率\"\n                  value={selectedPlan.utilizationRate}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#3f8600' }}\n                />\n              </Col>\n              <Col span={6}>\n                <Statistic\n                  title=\"浪费率\"\n                  value={selectedPlan.wasteRate}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#cf1322' }}\n                />\n              </Col>\n              <Col span={6}>\n                <Statistic\n                  title=\"总成本\"\n                  value={selectedPlan.totalCost}\n                  prefix=\"¥\"\n                  precision={2}\n                />\n              </Col>\n              <Col span={6}>\n                <Statistic\n                  title=\"浪费成本\"\n                  value={selectedPlan.wasteCost}\n                  prefix=\"¥\"\n                  precision={2}\n                  valueStyle={{ color: '#cf1322' }}\n                />\n              </Col>\n            </Row>\n\n            <Divider orientation=\"left\">需求清单</Divider>\n            <Table\n              columns={demandColumns}\n              dataSource={mockDemands}\n              rowKey=\"id\"\n              size=\"small\"\n              pagination={false}\n              style={{ marginBottom: 16 }}\n            />\n\n            <Divider orientation=\"left\">切割方案</Divider>\n            <Table\n              columns={schemeColumns}\n              dataSource={mockSchemes}\n              rowKey=\"id\"\n              size=\"small\"\n              pagination={false}\n            />\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default CuttingPlanPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,KAAK,CACLC,WAAW,CACXC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,GAAG,CACHC,OAAO,CACPC,QAAQ,CACRC,OAAO,CACPC,OAAO,CACPC,QAAQ,KACH,MAAM,CACb,OACEC,YAAY,CACZC,kBAAkB,CAClBC,eAAe,CAEfC,cAAc,CACdC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,cAAc,CACdC,kBAAkB,KAGb,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG3B,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG7B,UAAU,CAClC,KAAM,CAAE8B,OAAQ,CAAC,CAAGtB,IAAI,CACxB,KAAM,CAAEuB,QAAS,CAAC,CAAG5B,KAAK,CA6C1B,KAAM,CAAA6B,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC4C,SAAS,CAAEC,YAAY,CAAC,CAAG7C,QAAQ,CAAC,OAAO,CAAC,CACnD,KAAM,CAAC8C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACgD,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAACkD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACoD,YAAY,CAAEC,eAAe,CAAC,CAAGrD,QAAQ,CAAqB,IAAI,CAAC,CAC1E,KAAM,CAACsD,QAAQ,CAAC,CAAG3C,IAAI,CAAC4C,OAAO,CAAC,CAAC,CACjC,KAAM,CAACC,YAAY,CAAC,CAAG7C,IAAI,CAAC4C,OAAO,CAAC,CAAC,CAErC,KAAM,CAAAE,QAAQ,CAAGA,CAAA,GAAM,CACrBd,UAAU,CAAC,IAAI,CAAC,CAChBe,UAAU,CAAC,IAAM,CACff,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED1C,SAAS,CAAC,IAAM,CACdwD,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAM,CAC7BZ,mBAAmB,CAAC,IAAI,CAAC,CACzBO,QAAQ,CAACM,WAAW,CAAC,CAAC,CACxB,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIC,MAAmB,EAAK,CAC9CT,eAAe,CAACS,MAAM,CAAC,CACvBb,uBAAuB,CAAC,IAAI,CAAC,CAC7BO,YAAY,CAACI,WAAW,CAAC,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAID,MAAmB,EAAK,CAChDT,eAAe,CAACS,MAAM,CAAC,CACvBX,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAa,iBAAiB,CAAGA,CAAA,GAAM,CAC9BV,QAAQ,CAACW,cAAc,CAAC,CAAC,CAACC,IAAI,CAACC,MAAM,EAAI,CACvCC,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEF,MAAM,CAAC,CAC9B7C,OAAO,CAACgD,OAAO,CAAC,UAAU,CAAC,CAC3BvB,mBAAmB,CAAC,KAAK,CAAC,CAC1BU,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAc,qBAAqB,CAAGA,CAAA,GAAM,CAClCf,YAAY,CAACS,cAAc,CAAC,CAAC,CAACC,IAAI,CAACC,MAAM,EAAI,CAC3CC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEF,MAAM,CAAC,CAC5B7C,OAAO,CAACgD,OAAO,CAAC,mBAAmB,CAAC,CACpCrB,uBAAuB,CAAC,KAAK,CAAC,CAC9BQ,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAe,SAAwB,CAAG,CAC/B,CACEC,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,mBAAmB,CAC7BC,YAAY,CAAE,WAAW,CACzBC,YAAY,CAAE,MAAM,CACpBC,aAAa,CAAE,SAAS,CACxBC,cAAc,CAAE,GAAG,CACnBC,IAAI,CAAE,GAAG,CACTC,SAAS,CAAE,GAAG,CACdC,WAAW,CAAE,IAAI,CACjBC,QAAQ,CAAE,sBAAsB,CAChCC,MAAM,CAAE,WAAW,CACnBC,SAAS,CAAE,QAAQ,CACnBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,IAAI,CACfC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,OAAO,CAClBC,WAAW,CAAE,sBACf,CAAC,CACD,CACEjB,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,iBAAiB,CAC3BC,YAAY,CAAE,iBAAiB,CAC/BC,YAAY,CAAE,MAAM,CACpBC,aAAa,CAAE,SAAS,CACxBC,cAAc,CAAE,IAAI,CACpBC,IAAI,CAAE,IAAI,CACVC,SAAS,CAAE,IAAI,CACfC,WAAW,CAAE,IAAI,CACjBC,QAAQ,CAAE,sBAAsB,CAChCC,MAAM,CAAE,aAAa,CACrBC,SAAS,CAAE,QAAQ,CACnBC,eAAe,CAAE,CAAC,CAClBC,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,IAAI,CACfC,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,OAAO,CAClBC,WAAW,CAAE,sBACf,CAAC,CACD,CACEjB,EAAE,CAAE,GAAG,CACPC,QAAQ,CAAE,eAAe,CACzBC,YAAY,CAAE,iBAAiB,CAC/BC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,QAAQ,CACvBC,cAAc,CAAE,GAAG,CACnBC,IAAI,CAAE,GAAG,CACTC,SAAS,CAAE,GAAG,CACdC,WAAW,CAAE,IAAI,CACjBC,QAAQ,CAAE,sBAAsB,CAChCC,MAAM,CAAE,OAAO,CACfC,SAAS,CAAE,EAAE,CACbC,eAAe,CAAE,CAAC,CAClBC,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,OAAO,CAClBC,WAAW,CAAE,sBACf,CAAC,CACF,CAED;AACA,KAAM,CAAAC,WAA4B,CAAG,CACnC,CACElB,EAAE,CAAE,GAAG,CACPmB,MAAM,CAAE,GAAG,CACXC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,uBAAuB,CAChCC,IAAI,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CACtBC,QAAQ,CAAE,CAAC,CACXC,WAAW,CAAE,GAAG,CAChBC,KAAK,CAAE,CAAC,CACRC,WAAW,CAAE,CACf,CAAC,CACD,CACE1B,EAAE,CAAE,GAAG,CACPmB,MAAM,CAAE,GAAG,CACXC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,uBAAuB,CAChCC,IAAI,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CACtBC,QAAQ,CAAE,CAAC,CACXC,WAAW,CAAE,GAAG,CAChBC,KAAK,CAAE,CAAC,CACRC,WAAW,CAAE,CACf,CAAC,CACD,CACE1B,EAAE,CAAE,GAAG,CACPmB,MAAM,CAAE,GAAG,CACXC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,sBAAsB,CAC/BC,IAAI,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAAE,CAAC,CACXC,WAAW,CAAE,EAAE,CACfC,KAAK,CAAE,CAAC,CACRC,WAAW,CAAE,CACf,CAAC,CACF,CAED;AACA,KAAM,CAAAC,WAAyB,CAAG,CAChC,CACE3B,EAAE,CAAE,GAAG,CACP4B,MAAM,CAAE,EAAE,CACVL,QAAQ,CAAE,EAAE,CACZM,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,YAAY,CACvBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,sBACX,CAAC,CACD,CACEhC,EAAE,CAAE,GAAG,CACP4B,MAAM,CAAE,EAAE,CACVL,QAAQ,CAAE,EAAE,CACZM,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,YAAY,CACvBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,sBACX,CAAC,CACD,CACEhC,EAAE,CAAE,GAAG,CACP4B,MAAM,CAAE,EAAE,CACVL,QAAQ,CAAE,CAAC,CACXM,QAAQ,CAAE,QAAQ,CAClBC,SAAS,CAAE,YAAY,CACvBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,sBACX,CAAC,CACD,CACEhC,EAAE,CAAE,GAAG,CACP4B,MAAM,CAAE,EAAE,CACVL,QAAQ,CAAE,CAAC,CACXM,QAAQ,CAAE,QAAQ,CAClBC,SAAS,CAAE,YAAY,CACvBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,sBACX,CAAC,CACF,CAED,KAAM,CAAAC,WAAW,CAAG,CAClB,CACEC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,MACT,CAAC,CACD,CACEJ,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGlD,MAAmB,eAC1B1B,KAAA,QAAA6E,QAAA,eACE/E,IAAA,QAAA+E,QAAA,CAAMnD,MAAM,CAACa,YAAY,CAAM,CAAC,cAChCzC,IAAA,CAACI,IAAI,EAAC4E,IAAI,CAAC,WAAW,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAH,QAAA,CAChDnD,MAAM,CAACc,YAAY,CAChB,CAAC,EACJ,CAET,CAAC,CACD,CACE+B,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,gBAAgB,CACrBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGlD,MAAmB,KAAAuD,MAAA,CAAQvD,MAAM,CAACgB,cAAc,EAAAuC,MAAA,CAAGvD,MAAM,CAACiB,IAAI,CACzE,CAAC,CACD,CACE4B,KAAK,CAAE,KAAK,CACZE,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGlD,MAAmB,KAAAuD,MAAA,CAAQvD,MAAM,CAACmB,WAAW,EAAAoC,MAAA,CAAGvD,MAAM,CAACiB,IAAI,CACtE,CAAC,CACD,CACE4B,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,iBAAiB,CAC5BC,GAAG,CAAE,iBAAiB,CACtBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGM,KAAa,eACpBpF,IAAA,CAACd,QAAQ,EACPmG,OAAO,CAAED,KAAM,CACfE,IAAI,CAAC,OAAO,CACZrC,MAAM,CAAEmC,KAAK,EAAI,EAAE,CAAG,SAAS,CAAGA,KAAK,EAAI,EAAE,CAAG,QAAQ,CAAG,WAAY,CACxE,CAEL,CAAC,CACD,CACEX,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGM,KAAa,eACpBlF,KAAA,CAAClB,GAAG,EAACuG,KAAK,CAAEH,KAAK,EAAI,CAAC,CAAG,OAAO,CAAGA,KAAK,EAAI,EAAE,CAAG,QAAQ,CAAG,KAAM,CAAAL,QAAA,EAC/DK,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,GACpB,EAAK,CAET,CAAC,CACD,CACEf,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAG7B,MAAc,EAAK,CAC1B,KAAM,CAAAwC,YAAY,CAAG,CACnBC,KAAK,CAAE,CAAEH,KAAK,CAAE,SAAS,CAAEI,IAAI,CAAE,IAAK,CAAC,CACvCC,WAAW,CAAE,CAAEL,KAAK,CAAE,YAAY,CAAEI,IAAI,CAAE,KAAM,CAAC,CACjDE,SAAS,CAAE,CAAEN,KAAK,CAAE,SAAS,CAAEI,IAAI,CAAE,KAAM,CAAC,CAC5CG,SAAS,CAAE,CAAEP,KAAK,CAAE,SAAS,CAAEI,IAAI,CAAE,KAAM,CAAC,CAC5CI,SAAS,CAAE,CAAER,KAAK,CAAE,SAAS,CAAEI,IAAI,CAAE,KAAM,CAC7C,CAAC,CACD,KAAM,CAAAK,MAAM,CAAGP,YAAY,CAACxC,MAAM,CAA8B,CAChE,mBAAOjD,IAAA,CAAChB,GAAG,EAACuG,KAAK,CAAES,MAAM,CAACT,KAAM,CAAAR,QAAA,CAAEiB,MAAM,CAACL,IAAI,CAAM,CAAC,CACtD,CACF,CAAC,CACD,CACElB,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGmB,IAAY,EAAK,GAAI,CAAAC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,CAC9D,CAAC,CACD,CACE1B,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,OAAgB,CACvBC,MAAM,CAAGlD,MAAmB,eAC1B1B,KAAA,CAAC/B,KAAK,EAACmH,IAAI,CAAC,OAAO,CAAAP,QAAA,eACjB/E,IAAA,CAACf,OAAO,EAACwF,KAAK,CAAC,0BAAM,CAAAM,QAAA,cACnB/E,IAAA,CAAC9B,MAAM,EACL8G,IAAI,CAAC,MAAM,CACXoB,IAAI,cAAEpG,IAAA,CAACL,WAAW,GAAE,CAAE,CACtB0G,OAAO,CAAEA,CAAA,GAAMxE,gBAAgB,CAACD,MAAM,CAAE,CACzC,CAAC,CACK,CAAC,cACV5B,IAAA,CAACf,OAAO,EAACwF,KAAK,CAAC,0BAAM,CAAAM,QAAA,cACnB/E,IAAA,CAAC9B,MAAM,EACL8G,IAAI,CAAC,MAAM,CACXoB,IAAI,cAAEpG,IAAA,CAACT,kBAAkB,GAAE,CAAE,CAC7B8G,OAAO,CAAEA,CAAA,GAAM1E,cAAc,CAACC,MAAM,CAAE,CACtC0E,QAAQ,CAAE1E,MAAM,CAACqB,MAAM,GAAK,aAAc,CAC3C,CAAC,CACK,CAAC,CACTrB,MAAM,CAACqB,MAAM,GAAK,WAAW,eAC5BjD,IAAA,CAACf,OAAO,EAACwF,KAAK,CAAC,0BAAM,CAAAM,QAAA,cACnB/E,IAAA,CAAC9B,MAAM,EACL8G,IAAI,CAAC,MAAM,CACXoB,IAAI,cAAEpG,IAAA,CAACF,kBAAkB,GAAE,CAAE,CAC7BuG,OAAO,CAAEA,CAAA,GAAM,CACbjH,OAAO,CAACgD,OAAO,CAAC,WAAW,CAAC,CAC5Bb,QAAQ,CAAC,CAAC,CACZ,CAAE,CACH,CAAC,CACK,CACV,cACDvB,IAAA,CAACf,OAAO,EAACwF,KAAK,CAAC,cAAI,CAAAM,QAAA,cACjB/E,IAAA,CAAC9B,MAAM,EACL8G,IAAI,CAAC,MAAM,CACXoB,IAAI,cAAEpG,IAAA,CAACJ,YAAY,GAAE,CAAE,CACvB0G,QAAQ,CAAE1E,MAAM,CAACqB,MAAM,GAAK,WAAW,EAAIrB,MAAM,CAACqB,MAAM,GAAK,WAAY,CAC1E,CAAC,CACK,CAAC,cACVjD,IAAA,CAACf,OAAO,EAACwF,KAAK,CAAC,cAAI,CAAAM,QAAA,cACjB/E,IAAA,CAAC9B,MAAM,EACL8G,IAAI,CAAC,MAAM,CACXuB,MAAM,MACNH,IAAI,cAAEpG,IAAA,CAACH,cAAc,GAAE,CAAE,CACzBwG,OAAO,CAAEA,CAAA,GAAM,CACb7H,KAAK,CAACgI,OAAO,CAAC,CACZ/B,KAAK,CAAE,MAAM,CACbgC,OAAO,CAAE,eAAe,CACxBC,IAAI,CAAEA,CAAA,GAAM,CACVtH,OAAO,CAACgD,OAAO,CAAC,MAAM,CAAC,CACvBb,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,CAAC,CACJ,CAAE,CACH,CAAC,CACK,CAAC,EACL,CAEX,CAAC,CACF,CAED,KAAM,CAAAoF,aAAa,CAAG,CACpB,CACElC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGjB,IAAc,EAAKA,IAAI,CAAC+C,IAAI,CAAC,KAAK,CAC7C,CAAC,CACD,CACEnC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EACT,CAAC,CACD,CACEH,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGM,KAAa,KAAAD,MAAA,CAAQC,KAAK,KACrC,CAAC,CACD,CACEX,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACM,KAAa,CAAExD,MAAqB,MAAAuD,MAAA,CACxCC,KAAK,EAAAD,MAAA,CAAG,CAAAjE,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE2B,IAAI,GAAI,GAAG,CACxC,CAAC,CACF,CAED,KAAM,CAAAgE,aAAa,CAAG,CACpB,CACEpC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGM,KAAa,KAAAD,MAAA,CAAQC,KAAK,KACrC,CAAC,CACD,CACEX,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EACT,CAAC,CACD,CACEH,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGV,QAAgB,eACvBpE,IAAA,CAAChB,GAAG,EAACuG,KAAK,CAAEnB,QAAQ,GAAK,MAAM,CAAG,KAAK,CAAGA,QAAQ,GAAK,QAAQ,CAAG,QAAQ,CAAG,OAAQ,CAAAW,QAAA,CAClFX,QAAQ,GAAK,MAAM,CAAG,GAAG,CAAGA,QAAQ,GAAK,QAAQ,CAAG,GAAG,CAAG,GAAG,CAC3D,CAET,CAAC,CACD,CACEK,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGmB,IAAY,EAAK,GAAI,CAAAC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,CAC9D,CAAC,CACF,CAED,KAAM,CAAAW,gBAAgB,CAAGA,CAAA,gBACvB5G,KAAA,CAAC9B,GAAG,EAAC2I,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC9B,KAAK,CAAE,CAAE+B,YAAY,CAAE,EAAG,CAAE,CAAAjC,QAAA,eACjD/E,IAAA,CAAC3B,GAAG,EAAC4I,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnC,QAAA,cACjB/E,IAAA,CAAChC,IAAI,EAACsH,IAAI,CAAC,OAAO,CAAAP,QAAA,cAChB/E,IAAA,CAAC1B,SAAS,EACRmG,KAAK,CAAC,0BAAM,CACZW,KAAK,CAAE9C,SAAS,CAAC6B,MAAO,CACxBgD,MAAM,cAAEnH,IAAA,CAACR,eAAe,GAAE,CAAE,CAC7B,CAAC,CACE,CAAC,CACJ,CAAC,cACNQ,IAAA,CAAC3B,GAAG,EAAC4I,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnC,QAAA,cACjB/E,IAAA,CAAChC,IAAI,EAACsH,IAAI,CAAC,OAAO,CAAAP,QAAA,cAChB/E,IAAA,CAAC1B,SAAS,EACRmG,KAAK,CAAC,gCAAO,CACbW,KAAK,CAAE,IAAK,CACZgC,MAAM,CAAC,GAAG,CACVC,UAAU,CAAE,CAAE9B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNvF,IAAA,CAAC3B,GAAG,EAAC4I,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnC,QAAA,cACjB/E,IAAA,CAAChC,IAAI,EAACsH,IAAI,CAAC,OAAO,CAAAP,QAAA,cAChB/E,IAAA,CAAC1B,SAAS,EACRmG,KAAK,CAAC,gCAAO,CACbW,KAAK,CAAE,IAAK,CACZ+B,MAAM,CAAC,MAAG,CACVG,SAAS,CAAE,CAAE,CACbD,UAAU,CAAE,CAAE9B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNvF,IAAA,CAAC3B,GAAG,EAAC4I,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnC,QAAA,cACjB/E,IAAA,CAAChC,IAAI,EAACsH,IAAI,CAAC,OAAO,CAAAP,QAAA,cAChB/E,IAAA,CAAC1B,SAAS,EACRmG,KAAK,CAAC,0BAAM,CACZW,KAAK,CAAE,MAAO,CACd+B,MAAM,CAAC,MAAG,CACVG,SAAS,CAAE,CAAE,CACbD,UAAU,CAAE,CAAE9B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CACN,CAED,KAAM,CAAAgC,cAAc,CAAGA,CAAA,gBACrBrH,KAAA,QAAA6E,QAAA,EACG+B,gBAAgB,CAAC,CAAC,cACnB5G,KAAA,CAAClC,IAAI,EAAA+G,QAAA,eACH/E,IAAA,CAAC5B,GAAG,EAACoJ,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACxC,KAAK,CAAE,CAAE+B,YAAY,CAAE,EAAG,CAAE,CAAAjC,QAAA,cACtE/E,IAAA,CAAC3B,GAAG,EAAA0G,QAAA,cACF7E,KAAA,CAAC/B,KAAK,EAAA4G,QAAA,eACJ/E,IAAA,CAAC9B,MAAM,EACL8G,IAAI,CAAC,SAAS,CACdoB,IAAI,cAAEpG,IAAA,CAACV,YAAY,GAAE,CAAE,CACvB+G,OAAO,CAAE5E,gBAAiB,CAAAsD,QAAA,CAC3B,0BAED,CAAQ,CAAC,cACT/E,IAAA,CAAC9B,MAAM,EAACkI,IAAI,cAAEpG,IAAA,CAACP,cAAc,GAAE,CAAE,CAAAsF,QAAA,CAAC,0BAElC,CAAQ,CAAC,cACT/E,IAAA,CAAC9B,MAAM,EAACkI,IAAI,cAAEpG,IAAA,CAACN,cAAc,GAAE,CAAE,CAAC2G,OAAO,CAAE9E,QAAS,CAAAwD,QAAA,CAAC,cAErD,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,CACH,CAAC,cAEN/E,IAAA,CAAClB,KAAK,EACJM,OAAO,CAAC,sCAAQ,CAChBsI,WAAW,CAAC,kPAA0C,CACtD1C,IAAI,CAAC,MAAM,CACX2C,QAAQ,MACRC,QAAQ,MACR3C,KAAK,CAAE,CAAE+B,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEFhH,IAAA,CAAC/B,KAAK,EACJ4J,OAAO,CAAErD,WAAY,CACrBsD,UAAU,CAAExF,SAAU,CACtB9B,OAAO,CAAEA,OAAQ,CACjBuH,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAApD,MAAA,CACjBoD,KAAK,CAAC,CAAC,CAAC,MAAApD,MAAA,CAAIoD,KAAK,CAAC,CAAC,CAAC,oBAAApD,MAAA,CAAQmD,KAAK,WAC1C,CAAE,CACH,CAAC,EACE,CAAC,EACJ,CACN,CAED,mBACEpI,KAAA,QAAA6E,QAAA,eACE7E,KAAA,CAAClC,IAAI,EAAA+G,QAAA,eACH/E,IAAA,CAAC5B,GAAG,EAACoJ,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACxC,KAAK,CAAE,CAAE+B,YAAY,CAAE,EAAG,CAAE,CAAAjC,QAAA,cACtE7E,KAAA,CAAC7B,GAAG,EAAA0G,QAAA,eACF/E,IAAA,CAACG,KAAK,EAACqI,KAAK,CAAE,CAAE,CAACvD,KAAK,CAAE,CAAEwD,MAAM,CAAE,CAAE,CAAE,CAAA1D,QAAA,CAAC,sCAEvC,CAAO,CAAC,cACR/E,IAAA,CAACI,IAAI,EAAC4E,IAAI,CAAC,WAAW,CAAAD,QAAA,CAAC,wJAEvB,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,cAEN/E,IAAA,CAACjB,IAAI,EAAC2J,SAAS,CAAEhI,SAAU,CAACiI,QAAQ,CAAEhI,YAAa,CAAAoE,QAAA,cACjD/E,IAAA,CAACK,OAAO,EAACuI,GAAG,cAAE1I,KAAA,SAAA6E,QAAA,eAAM/E,IAAA,CAACR,eAAe,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAAuF,QAAA,CAChDwC,cAAc,CAAC,CAAC,EADqC,OAE/C,CAAC,CACN,CAAC,EACH,CAAC,cAGPvH,IAAA,CAACxB,KAAK,EACJiG,KAAK,CAAC,sCAAQ,CACdoE,IAAI,CAAEjI,gBAAiB,CACvB8F,IAAI,CAAE5E,iBAAkB,CACxBgH,QAAQ,CAAEA,CAAA,GAAMjI,mBAAmB,CAAC,KAAK,CAAE,CAC3CkI,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CACfpE,KAAK,CAAE,GAAI,CAAAG,QAAA,cAEX7E,KAAA,CAACzB,IAAI,EAACwK,IAAI,CAAE7H,QAAS,CAAC8H,MAAM,CAAC,UAAU,CAAAnE,QAAA,eACrC7E,KAAA,CAAC9B,GAAG,EAAC2I,MAAM,CAAE,EAAG,CAAAhC,QAAA,eACd/E,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpK,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA2F,QAAA,cAEhD/E,IAAA,CAACtB,KAAK,EAAC+K,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNzJ,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpK,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA2F,QAAA,cAE9C7E,KAAA,CAACtB,MAAM,EAAC6K,WAAW,CAAC,gCAAO,CAAA1E,QAAA,eACzB/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,WAAW,CAAAL,QAAA,CAAC,sCAAgB,CAAe,CAAC,cACjE/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,iBAAiB,CAAAL,QAAA,CAAC,4CAAsB,CAAe,CAAC,cAC7E/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,iBAAiB,CAAAL,QAAA,CAAC,gCAAoB,CAAe,CAAC,EACrE,CAAC,CACA,CAAC,CACT,CAAC,EACH,CAAC,cAEN7E,KAAA,CAAC9B,GAAG,EAAC2I,MAAM,CAAE,EAAG,CAAAhC,QAAA,eACd/E,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,CAAE,CAAApE,QAAA,cACX/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpK,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA2F,QAAA,cAEhD/E,IAAA,CAACrB,WAAW,EACVgL,GAAG,CAAE,CAAE,CACP1E,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CACzB6E,WAAW,CAAC,0BAAM,CACnB,CAAC,CACO,CAAC,CACT,CAAC,cACNzJ,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,CAAE,CAAApE,QAAA,cACX/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,MAAM,CACXC,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpK,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA2F,QAAA,cAE9C7E,KAAA,CAACtB,MAAM,EAAC6K,WAAW,CAAC,gCAAO,CAAA1E,QAAA,eACzB/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,GAAG,CAAAL,QAAA,CAAC,WAAI,CAAe,CAAC,cAC7C/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,IAAI,CAAAL,QAAA,CAAC,kBAAM,CAAe,CAAC,cAChD/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,IAAI,CAAAL,QAAA,CAAC,kBAAM,CAAe,CAAC,EAC1C,CAAC,CACA,CAAC,CACT,CAAC,cACN/E,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,CAAE,CAAApE,QAAA,cACX/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAC,cAAI,CACVC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpK,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA2F,QAAA,cAE9C/E,IAAA,CAACrB,WAAW,EACVgL,GAAG,CAAE,CAAE,CACPrC,SAAS,CAAE,CAAE,CACbrC,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CACzB6E,WAAW,CAAC,cAAI,CAChBG,WAAW,CAAC,MAAG,CAChB,CAAC,CACO,CAAC,CACT,CAAC,EACH,CAAC,cAEN1J,KAAA,CAAC9B,GAAG,EAAC2I,MAAM,CAAE,EAAG,CAAAhC,QAAA,eACd/E,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEpK,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA2F,QAAA,cAEhD/E,IAAA,CAACnB,UAAU,EAACoG,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAC/B,CAAC,CACT,CAAC,cACN5E,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAC,0BAAM,CACZO,YAAY,CAAC,WAAW,CAAA9E,QAAA,cAExB7E,KAAA,CAACtB,MAAM,EAAC6K,WAAW,CAAC,4CAAS,CAAA1E,QAAA,eAC3B/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,WAAW,CAAAL,QAAA,CAAC,sCAAM,CAAe,CAAC,cACvD/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,UAAU,CAAAL,QAAA,CAAC,sCAAM,CAAe,CAAC,cACtD/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,WAAW,CAAAL,QAAA,CAAC,sCAAM,CAAe,CAAC,cACvD/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,SAAS,CAAAL,QAAA,CAAC,0BAAI,CAAe,CAAC,EAC7C,CAAC,CACA,CAAC,CACT,CAAC,EACH,CAAC,cAEN/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EAACC,IAAI,CAAC,aAAa,CAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,cACxC/E,IAAA,CAACM,QAAQ,EAACwJ,IAAI,CAAE,CAAE,CAACL,WAAW,CAAC,4CAAS,CAAE,CAAC,CAClC,CAAC,EACR,CAAC,CACF,CAAC,cAGRzJ,IAAA,CAACxB,KAAK,EACJiG,KAAK,CAAC,sCAAQ,CACdoE,IAAI,CAAE/H,oBAAqB,CAC3B4F,IAAI,CAAErE,qBAAsB,CAC5ByG,QAAQ,CAAEA,CAAA,GAAM/H,uBAAuB,CAAC,KAAK,CAAE,CAC/CgI,MAAM,CAAC,0BAAM,CACbC,UAAU,CAAC,cAAI,CACfpE,KAAK,CAAE,GAAI,CAAAG,QAAA,cAEX7E,KAAA,CAACzB,IAAI,EAACwK,IAAI,CAAE3H,YAAa,CAAC4H,MAAM,CAAC,UAAU,CAAAnE,QAAA,eACzC/E,IAAA,CAAClB,KAAK,EACJM,OAAO,CAAC,sCAAQ,CAChBsI,WAAW,CAAC,0NAAsC,CAClD1C,IAAI,CAAC,MAAM,CACX2C,QAAQ,MACR1C,KAAK,CAAE,CAAE+B,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEFhH,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAC,0BAAM,CACZO,YAAY,CAAC,UAAU,CAAA9E,QAAA,cAEvB7E,KAAA,CAACtB,MAAM,EAAC6K,WAAW,CAAC,4CAAS,CAAA1E,QAAA,eAC3B/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,WAAW,CAAAL,QAAA,CAAC,iEAAa,CAAe,CAAC,cAC9D/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,UAAU,CAAAL,QAAA,CAAC,iEAAa,CAAe,CAAC,cAC7D/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,WAAW,CAAAL,QAAA,CAAC,6EAAe,CAAe,CAAC,cAChE/E,IAAA,CAACpB,MAAM,CAAC8K,MAAM,EAACtE,KAAK,CAAC,SAAS,CAAAL,QAAA,CAAC,+CAAU,CAAe,CAAC,EACnD,CAAC,CACA,CAAC,cAEZ7E,KAAA,CAAC9B,GAAG,EAAC2I,MAAM,CAAE,EAAG,CAAAhC,QAAA,eACd/E,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAC,mCAAU,CAChBO,YAAY,CAAE,EAAG,CAAA9E,QAAA,cAEjB/E,IAAA,CAACrB,WAAW,EACVgL,GAAG,CAAE,EAAG,CACRI,GAAG,CAAE,GAAI,CACT9E,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CACzB6E,WAAW,CAAC,gCAAO,CACpB,CAAC,CACO,CAAC,CACT,CAAC,cACNzJ,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ/E,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EACRC,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAC,sCAAQ,CACdO,YAAY,CAAE,CAAE,CAAA9E,QAAA,cAEhB/E,IAAA,CAACrB,WAAW,EACVgL,GAAG,CAAE,CAAE,CACP1E,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CACzB6E,WAAW,CAAC,sCAAQ,CACpBO,UAAU,CAAE,CAAA9I,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE2B,IAAI,GAAI,GAAI,CACvC,CAAC,CACO,CAAC,CACT,CAAC,EACH,CAAC,cAEN7C,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EAACC,IAAI,CAAC,eAAe,CAACC,KAAK,CAAC,gCAAO,CAAAvE,QAAA,cAC3C/E,IAAA,CAACX,QAAQ,CAAC4K,KAAK,EACbC,OAAO,CAAE,CACP,CAAEZ,KAAK,CAAE,YAAY,CAAElE,KAAK,CAAE,UAAW,CAAC,CAC1C,CAAEkE,KAAK,CAAE,UAAU,CAAElE,KAAK,CAAE,SAAU,CAAC,CACvC,CAAEkE,KAAK,CAAE,QAAQ,CAAElE,KAAK,CAAE,OAAQ,CAAC,CACnC,CAAEkE,KAAK,CAAE,UAAU,CAAElE,KAAK,CAAE,SAAU,CAAC,CACvC,CACF+E,YAAY,CAAE,CAAC,UAAU,CAAE,OAAO,CAAE,CACrC,CAAC,CACO,CAAC,cAEZnK,IAAA,CAACvB,IAAI,CAAC2K,IAAI,EAACC,IAAI,CAAC,cAAc,CAACe,aAAa,CAAC,SAAS,CAACP,YAAY,CAAE,IAAK,CAAA9E,QAAA,cACxE/E,IAAA,CAACX,QAAQ,EAAA0F,QAAA,CAAC,sCAAM,CAAU,CAAC,CAClB,CAAC,EACR,CAAC,CACF,CAAC,cAGR/E,IAAA,CAACxB,KAAK,EACJiG,KAAK,2CAAAU,MAAA,CAAcjE,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsB,QAAQ,CAAG,CAC5CqG,IAAI,CAAE7H,kBAAmB,CACzB8H,QAAQ,CAAEA,CAAA,GAAM7H,qBAAqB,CAAC,KAAK,CAAE,CAC7CoJ,MAAM,CAAE,cACNrK,IAAA,CAAC9B,MAAM,EAAamI,OAAO,CAAEA,CAAA,GAAMpF,qBAAqB,CAAC,KAAK,CAAE,CAAA8D,QAAA,CAAC,cAEjE,EAFY,OAEJ,CAAC,cACT/E,IAAA,CAAC9B,MAAM,EAAckI,IAAI,cAAEpG,IAAA,CAACP,cAAc,GAAE,CAAE,CAAAsF,QAAA,CAAC,0BAE/C,EAFY,QAEJ,CAAC,cACT/E,IAAA,CAAC9B,MAAM,EAAe8G,IAAI,CAAC,SAAS,CAACoB,IAAI,cAAEpG,IAAA,CAACF,kBAAkB,GAAE,CAAE,CAAAiF,QAAA,CAAC,0BAEnE,EAFY,SAEJ,CAAC,CACT,CACFH,KAAK,CAAE,IAAK,CAAAG,QAAA,CAEX7D,YAAY,eACXhB,KAAA,QAAA6E,QAAA,eACE7E,KAAA,CAAC9B,GAAG,EAAC2I,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC9B,KAAK,CAAE,CAAE+B,YAAY,CAAE,EAAG,CAAE,CAAAjC,QAAA,eACjD/E,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,CAAE,CAAApE,QAAA,cACX/E,IAAA,CAAC1B,SAAS,EACRmG,KAAK,CAAC,0BAAM,CACZW,KAAK,CAAElE,YAAY,CAACiC,eAAgB,CACpCiE,MAAM,CAAC,GAAG,CACVC,UAAU,CAAE,CAAE9B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNvF,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,CAAE,CAAApE,QAAA,cACX/E,IAAA,CAAC1B,SAAS,EACRmG,KAAK,CAAC,oBAAK,CACXW,KAAK,CAAElE,YAAY,CAACkC,SAAU,CAC9BgE,MAAM,CAAC,GAAG,CACVC,UAAU,CAAE,CAAE9B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNvF,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,CAAE,CAAApE,QAAA,cACX/E,IAAA,CAAC1B,SAAS,EACRmG,KAAK,CAAC,oBAAK,CACXW,KAAK,CAAElE,YAAY,CAACmC,SAAU,CAC9B8D,MAAM,CAAC,MAAG,CACVG,SAAS,CAAE,CAAE,CACd,CAAC,CACC,CAAC,cACNtH,IAAA,CAAC3B,GAAG,EAAC8K,IAAI,CAAE,CAAE,CAAApE,QAAA,cACX/E,IAAA,CAAC1B,SAAS,EACRmG,KAAK,CAAC,0BAAM,CACZW,KAAK,CAAElE,YAAY,CAACoC,SAAU,CAC9B6D,MAAM,CAAC,MAAG,CACVG,SAAS,CAAE,CAAE,CACbD,UAAU,CAAE,CAAE9B,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,EACH,CAAC,cAENvF,IAAA,CAACb,OAAO,EAACmL,WAAW,CAAC,MAAM,CAAAvF,QAAA,CAAC,0BAAI,CAAS,CAAC,cAC1C/E,IAAA,CAAC/B,KAAK,EACJ4J,OAAO,CAAEhB,aAAc,CACvBiB,UAAU,CAAE5D,WAAY,CACxB6D,MAAM,CAAC,IAAI,CACXzC,IAAI,CAAC,OAAO,CACZ4C,UAAU,CAAE,KAAM,CAClBjD,KAAK,CAAE,CAAE+B,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEFhH,IAAA,CAACb,OAAO,EAACmL,WAAW,CAAC,MAAM,CAAAvF,QAAA,CAAC,0BAAI,CAAS,CAAC,cAC1C/E,IAAA,CAAC/B,KAAK,EACJ4J,OAAO,CAAElB,aAAc,CACvBmB,UAAU,CAAErE,WAAY,CACxBsE,MAAM,CAAC,IAAI,CACXzC,IAAI,CAAC,OAAO,CACZ4C,UAAU,CAAE,KAAM,CACnB,CAAC,EACC,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3H,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}