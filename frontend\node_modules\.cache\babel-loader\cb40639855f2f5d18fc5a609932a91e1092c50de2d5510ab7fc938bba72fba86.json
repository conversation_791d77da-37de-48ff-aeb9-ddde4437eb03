{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useEffect,useState,useMemo,useCallback}from'react';import{Row,Col,Card,Statistic,Progress,Table,List,Badge,Typography,Space,Button,DatePicker}from'antd';import{ArrowDownOutlined,DollarOutlined,WarningOutlined,RiseOutlined,FileTextOutlined,ClockCircleOutlined}from'@ant-design/icons';import{AreaChart,Area,BarChart,Bar,PieChart,Pie,Cell,XAxis,YAxis,CartesianGrid,Tooltip,Legend,ResponsiveContainer}from'recharts';import dayjs from'dayjs';import{useAppDispatch,useAppSelector}from'../../hooks/redux';import{formatCurrency,formatPercentage}from'../../utils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{RangePicker}=DatePicker;// 模拟数据\nconst mockMetrics={totalOrders:156,totalRevenue:2580000,wasteReduction:0.15,costSavings:380000,inventoryTurnover:8.5,onTimeDelivery:0.95};const mockWasteTrend=[{month:'1月',packaging:2.5,moq:3.2,cutting:1.8,expiry:0.5},{month:'2月',packaging:2.1,moq:2.8,cutting:1.5,expiry:0.3},{month:'3月',packaging:1.9,moq:2.5,cutting:1.3,expiry:0.4},{month:'4月',packaging:1.7,moq:2.2,cutting:1.1,expiry:0.2},{month:'5月',packaging:1.5,moq:2.0,cutting:1.0,expiry:0.3},{month:'6月',packaging:1.3,moq:1.8,cutting:0.9,expiry:0.1}];const mockCostTrend=[{month:'1月',material:850000,labor:120000,overhead:80000},{month:'2月',material:920000,labor:125000,overhead:82000},{month:'3月',material:880000,labor:118000,overhead:78000},{month:'4月',material:950000,labor:130000,overhead:85000},{month:'5月',material:890000,labor:122000,overhead:79000},{month:'6月',material:910000,labor:128000,overhead:83000}];const mockOrderStatus=[{name:'已完成',value:45,color:'#52c41a'},{name:'进行中',value:30,color:'#1890ff'},{name:'待开始',value:20,color:'#faad14'},{name:'已延期',value:5,color:'#f5222d'}];const mockRecentOrders=[{id:'ORD-001',customer:'华为技术',amount:125000,status:'进行中',date:'2024-01-15'},{id:'ORD-002',customer:'中兴通讯',amount:98000,status:'已完成',date:'2024-01-14'},{id:'ORD-003',customer:'大唐移动',amount:156000,status:'待开始',date:'2024-01-13'},{id:'ORD-004',customer:'爱立信',amount:89000,status:'进行中',date:'2024-01-12'},{id:'ORD-005',customer:'诺基亚',amount:234000,status:'已完成',date:'2024-01-11'}];const mockAlerts=[{id:1,type:'warning',message:'物料 ANT-001 库存不足，当前库存：50件',time:'2小时前'},{id:2,type:'error',message:'订单 ORD-003 交期可能延误',time:'4小时前'},{id:3,type:'info',message:'ECN-2024-001 已通过审批',time:'6小时前'},{id:4,type:'warning',message:'供应商价格异常波动：RF-002 上涨15%',time:'8小时前'}];const DashboardPage=()=>{const dispatch=useAppDispatch();const{user,currentRole}=useAppSelector(state=>state.auth);const[dateRange,setDateRange]=useState([dayjs().subtract(30,'day'),dayjs()]);useEffect(()=>{// 加载仪表板数据\n// dispatch(fetchDashboardMetrics());\n},[dispatch,dateRange]);// 使用useCallback优化函数，避免不必要的重新渲染\nconst getStatusColor=useCallback(status=>{switch(status){case'已完成':return'success';case'进行中':return'processing';case'待开始':return'warning';case'已延期':return'error';default:return'default';}},[]);const getAlertIcon=useCallback(type=>{switch(type){case'warning':return/*#__PURE__*/_jsx(WarningOutlined,{style:{color:'#faad14'}});case'error':return/*#__PURE__*/_jsx(WarningOutlined,{style:{color:'#f5222d'}});case'info':return/*#__PURE__*/_jsx(ClockCircleOutlined,{style:{color:'#1890ff'}});default:return/*#__PURE__*/_jsx(ClockCircleOutlined,{});}},[]);// 使用useMemo优化计算密集型数据\nconst processedMetrics=useMemo(()=>_objectSpread(_objectSpread({},mockMetrics),{},{wasteReductionFormatted:formatPercentage(mockMetrics.wasteReduction),totalRevenueFormatted:formatCurrency(mockMetrics.totalRevenue),costSavingsFormatted:formatCurrency(mockMetrics.costSavings)}),[]);const handleDateRangeChange=useCallback(dates=>{if(dates){setDateRange(dates);}},[]);const handleRefreshData=useCallback(()=>{// TODO: 实现数据刷新逻辑\nconsole.log('刷新数据');},[]);return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:24},children:[/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(Title,{level:2,style:{margin:0},children:\"\\u4EEA\\u8868\\u677F\"}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\",user===null||user===void 0?void 0:user.name,\"\\uFF01\\u5F53\\u524D\\u89D2\\u8272\\uFF1A\",currentRole===null||currentRole===void 0?void 0:currentRole.name]})]}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(RangePicker,{value:dateRange,onChange:handleDateRangeChange}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:handleRefreshData,children:\"\\u5237\\u65B0\\u6570\\u636E\"})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,lg:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u8BA2\\u5355\\u6570\",value:processedMetrics.totalOrders,prefix:/*#__PURE__*/_jsx(FileTextOutlined,{}),suffix:\"\\u4E2A\",valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,lg:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u6536\\u5165\",value:processedMetrics.totalRevenue,prefix:/*#__PURE__*/_jsx(DollarOutlined,{}),formatter:()=>processedMetrics.totalRevenueFormatted,valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,lg:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6D6A\\u8D39\\u51CF\\u5C11\\u7387\",value:processedMetrics.wasteReduction,prefix:/*#__PURE__*/_jsx(ArrowDownOutlined,{}),formatter:()=>processedMetrics.wasteReductionFormatted,valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,lg:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6210\\u672C\\u8282\\u7EA6\",value:processedMetrics.costSavings,prefix:/*#__PURE__*/_jsx(RiseOutlined,{}),formatter:()=>processedMetrics.costSavingsFormatted,valueStyle:{color:'#52c41a'}})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,children:/*#__PURE__*/_jsxs(Card,{title:\"\\u5E93\\u5B58\\u5468\\u8F6C\\u7387\",extra:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u6B21/\\u5E74\"}),children:[/*#__PURE__*/_jsx(Statistic,{value:processedMetrics.inventoryTurnover,precision:1,valueStyle:{fontSize:32,color:'#1890ff'}}),/*#__PURE__*/_jsx(Progress,{percent:85,strokeColor:\"#1890ff\",showInfo:false,style:{marginTop:16}}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u76EE\\u6807\\uFF1A10\\u6B21/\\u5E74\"})]})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,children:/*#__PURE__*/_jsxs(Card,{title:\"\\u51C6\\u65F6\\u4EA4\\u4ED8\\u7387\",extra:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"%\"}),children:[/*#__PURE__*/_jsx(Statistic,{value:processedMetrics.onTimeDelivery,formatter:value=>formatPercentage(Number(value)),valueStyle:{fontSize:32,color:'#52c41a'}}),/*#__PURE__*/_jsx(Progress,{percent:95,strokeColor:\"#52c41a\",showInfo:false,style:{marginTop:16}}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u76EE\\u6807\\uFF1A\\u226595%\"})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u6D6A\\u8D39\\u8D8B\\u52BF\\u5206\\u6790\",extra:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"%\"}),children:/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:300,children:/*#__PURE__*/_jsxs(AreaChart,{data:mockWasteTrend,children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"month\"}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Area,{type:\"monotone\",dataKey:\"packaging\",stackId:\"1\",stroke:\"#8884d8\",fill:\"#8884d8\",name:\"\\u5305\\u88C5\\u6D6A\\u8D39\"}),/*#__PURE__*/_jsx(Area,{type:\"monotone\",dataKey:\"moq\",stackId:\"1\",stroke:\"#82ca9d\",fill:\"#82ca9d\",name:\"MOQ\\u6D6A\\u8D39\"}),/*#__PURE__*/_jsx(Area,{type:\"monotone\",dataKey:\"cutting\",stackId:\"1\",stroke:\"#ffc658\",fill:\"#ffc658\",name:\"\\u5207\\u5272\\u6D6A\\u8D39\"}),/*#__PURE__*/_jsx(Area,{type:\"monotone\",dataKey:\"expiry\",stackId:\"1\",stroke:\"#ff7300\",fill:\"#ff7300\",name:\"\\u8FC7\\u671F\\u6D6A\\u8D39\"})]})})})}),/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u6210\\u672C\\u6784\\u6210\\u8D8B\\u52BF\",children:/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:300,children:/*#__PURE__*/_jsxs(BarChart,{data:mockCostTrend,children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"month\"}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{formatter:value=>formatCurrency(Number(value))}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"material\",fill:\"#8884d8\",name:\"\\u6750\\u6599\\u6210\\u672C\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"labor\",fill:\"#82ca9d\",name:\"\\u4EBA\\u5DE5\\u6210\\u672C\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"overhead\",fill:\"#ffc658\",name:\"\\u5236\\u9020\\u8D39\\u7528\"})]})})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:8,children:/*#__PURE__*/_jsx(Card,{title:\"\\u8BA2\\u5355\\u72B6\\u6001\\u5206\\u5E03\",children:/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:250,children:/*#__PURE__*/_jsxs(PieChart,{children:[/*#__PURE__*/_jsx(Pie,{data:mockOrderStatus,cx:\"50%\",cy:\"50%\",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:\"value\",children:mockOrderStatus.map((entry,index)=>/*#__PURE__*/_jsx(Cell,{fill:entry.color},\"cell-\".concat(index)))}),/*#__PURE__*/_jsx(Tooltip,{}),/*#__PURE__*/_jsx(Legend,{})]})})})}),/*#__PURE__*/_jsx(Col,{xs:24,lg:16,children:/*#__PURE__*/_jsx(Card,{title:\"\\u6700\\u8FD1\\u8BA2\\u5355\",extra:/*#__PURE__*/_jsx(Button,{type:\"link\",children:\"\\u67E5\\u770B\\u5168\\u90E8\"}),children:/*#__PURE__*/_jsx(Table,{dataSource:mockRecentOrders,pagination:false,size:\"small\",columns:[{title:'订单号',dataIndex:'id',key:'id',render:text=>/*#__PURE__*/_jsx(Text,{strong:true,children:text})},{title:'客户',dataIndex:'customer',key:'customer'},{title:'金额',dataIndex:'amount',key:'amount',render:value=>formatCurrency(value)},{title:'状态',dataIndex:'status',key:'status',render:status=>/*#__PURE__*/_jsx(Badge,{status:getStatusColor(status),text:status})},{title:'日期',dataIndex:'date',key:'date'}]})})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsx(Card,{title:\"\\u7CFB\\u7EDF\\u9884\\u8B66\",extra:/*#__PURE__*/_jsx(Button,{type:\"link\",children:\"\\u67E5\\u770B\\u5168\\u90E8\"}),children:/*#__PURE__*/_jsx(List,{dataSource:mockAlerts,renderItem:item=>/*#__PURE__*/_jsx(List.Item,{children:/*#__PURE__*/_jsx(List.Item.Meta,{avatar:getAlertIcon(item.type),title:item.message,description:item.time})})})})})})]});};export default/*#__PURE__*/React.memo(DashboardPage);", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useMemo", "useCallback", "Row", "Col", "Card", "Statistic", "Progress", "Table", "List", "Badge", "Typography", "Space", "<PERSON><PERSON>", "DatePicker", "ArrowDownOutlined", "DollarOutlined", "WarningOutlined", "RiseOutlined", "FileTextOutlined", "ClockCircleOutlined", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "dayjs", "useAppDispatch", "useAppSelector", "formatCurrency", "formatPercentage", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "RangePicker", "mockMetrics", "totalOrders", "totalRevenue", "wasteReduction", "costSavings", "inventoryTurnover", "onTimeDelivery", "mockWasteTrend", "month", "packaging", "moq", "cutting", "expiry", "mockCostTrend", "material", "labor", "overhead", "mockOrderStatus", "name", "value", "color", "mockRecentOrders", "id", "customer", "amount", "status", "date", "mock<PERSON>ler<PERSON>", "type", "message", "time", "DashboardPage", "dispatch", "user", "currentRole", "state", "auth", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "getStatusColor", "getAlertIcon", "style", "processedMetrics", "_objectSpread", "wasteReductionFormatted", "totalRevenueFormatted", "costSavingsFormatted", "handleDateRangeChange", "dates", "handleRefreshData", "console", "log", "children", "justify", "align", "marginBottom", "level", "margin", "onChange", "onClick", "gutter", "xs", "sm", "lg", "title", "prefix", "suffix", "valueStyle", "formatter", "extra", "precision", "fontSize", "percent", "strokeColor", "showInfo", "marginTop", "Number", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stackId", "stroke", "fill", "cx", "cy", "innerRadius", "outerRadius", "paddingAngle", "map", "entry", "index", "concat", "dataSource", "pagination", "size", "columns", "dataIndex", "key", "render", "text", "strong", "span", "renderItem", "item", "<PERSON><PERSON>", "Meta", "avatar", "description", "memo"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/dashboard/DashboardPage.tsx"], "sourcesContent": ["import React, { useEffect, useState, useMemo, useCallback } from 'react';\nimport {\n  Row,\n  Col,\n  Card,\n  Statistic,\n  Progress,\n  Table,\n  List,\n  Avatar,\n  Badge,\n  Typography,\n  Space,\n  Button,\n  Select,\n  DatePicker,\n  Tabs,\n} from 'antd';\nimport {\n  ArrowUpOutlined,\n  ArrowDownOutlined,\n  DollarOutlined,\n  ShoppingCartOutlined,\n  InboxOutlined,\n  WarningOutlined,\n  RiseOutlined,\n  FileTextOutlined,\n  ToolOutlined,\n  ClockCircleOutlined,\n} from '@ant-design/icons';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n} from 'recharts';\nimport dayjs from 'dayjs';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { formatCurrency, formatPercentage } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\n// 模拟数据\nconst mockMetrics = {\n  totalOrders: 156,\n  totalRevenue: 2580000,\n  wasteReduction: 0.15,\n  costSavings: 380000,\n  inventoryTurnover: 8.5,\n  onTimeDelivery: 0.95,\n};\n\nconst mockWasteTrend = [\n  { month: '1月', packaging: 2.5, moq: 3.2, cutting: 1.8, expiry: 0.5 },\n  { month: '2月', packaging: 2.1, moq: 2.8, cutting: 1.5, expiry: 0.3 },\n  { month: '3月', packaging: 1.9, moq: 2.5, cutting: 1.3, expiry: 0.4 },\n  { month: '4月', packaging: 1.7, moq: 2.2, cutting: 1.1, expiry: 0.2 },\n  { month: '5月', packaging: 1.5, moq: 2.0, cutting: 1.0, expiry: 0.3 },\n  { month: '6月', packaging: 1.3, moq: 1.8, cutting: 0.9, expiry: 0.1 },\n];\n\nconst mockCostTrend = [\n  { month: '1月', material: 850000, labor: 120000, overhead: 80000 },\n  { month: '2月', material: 920000, labor: 125000, overhead: 82000 },\n  { month: '3月', material: 880000, labor: 118000, overhead: 78000 },\n  { month: '4月', material: 950000, labor: 130000, overhead: 85000 },\n  { month: '5月', material: 890000, labor: 122000, overhead: 79000 },\n  { month: '6月', material: 910000, labor: 128000, overhead: 83000 },\n];\n\nconst mockOrderStatus = [\n  { name: '已完成', value: 45, color: '#52c41a' },\n  { name: '进行中', value: 30, color: '#1890ff' },\n  { name: '待开始', value: 20, color: '#faad14' },\n  { name: '已延期', value: 5, color: '#f5222d' },\n];\n\nconst mockRecentOrders = [\n  { id: 'ORD-001', customer: '华为技术', amount: 125000, status: '进行中', date: '2024-01-15' },\n  { id: 'ORD-002', customer: '中兴通讯', amount: 98000, status: '已完成', date: '2024-01-14' },\n  { id: 'ORD-003', customer: '大唐移动', amount: 156000, status: '待开始', date: '2024-01-13' },\n  { id: 'ORD-004', customer: '爱立信', amount: 89000, status: '进行中', date: '2024-01-12' },\n  { id: 'ORD-005', customer: '诺基亚', amount: 234000, status: '已完成', date: '2024-01-11' },\n];\n\nconst mockAlerts = [\n  { id: 1, type: 'warning', message: '物料 ANT-001 库存不足，当前库存：50件', time: '2小时前' },\n  { id: 2, type: 'error', message: '订单 ORD-003 交期可能延误', time: '4小时前' },\n  { id: 3, type: 'info', message: 'ECN-2024-001 已通过审批', time: '6小时前' },\n  { id: 4, type: 'warning', message: '供应商价格异常波动：RF-002 上涨15%', time: '8小时前' },\n];\n\nconst DashboardPage: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { user, currentRole } = useAppSelector(state => state.auth);\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([\n    dayjs().subtract(30, 'day'),\n    dayjs(),\n  ]);\n\n  useEffect(() => {\n    // 加载仪表板数据\n    // dispatch(fetchDashboardMetrics());\n  }, [dispatch, dateRange]);\n\n  // 使用useCallback优化函数，避免不必要的重新渲染\n  const getStatusColor = useCallback((status: string) => {\n    switch (status) {\n      case '已完成': return 'success';\n      case '进行中': return 'processing';\n      case '待开始': return 'warning';\n      case '已延期': return 'error';\n      default: return 'default';\n    }\n  }, []);\n\n  const getAlertIcon = useCallback((type: string) => {\n    switch (type) {\n      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;\n      case 'error': return <WarningOutlined style={{ color: '#f5222d' }} />;\n      case 'info': return <ClockCircleOutlined style={{ color: '#1890ff' }} />;\n      default: return <ClockCircleOutlined />;\n    }\n  }, []);\n\n  // 使用useMemo优化计算密集型数据\n  const processedMetrics = useMemo(() => ({\n    ...mockMetrics,\n    wasteReductionFormatted: formatPercentage(mockMetrics.wasteReduction),\n    totalRevenueFormatted: formatCurrency(mockMetrics.totalRevenue),\n    costSavingsFormatted: formatCurrency(mockMetrics.costSavings),\n  }), []);\n\n  const handleDateRangeChange = useCallback((dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {\n    if (dates) {\n      setDateRange(dates);\n    }\n  }, []);\n\n  const handleRefreshData = useCallback(() => {\n    // TODO: 实现数据刷新逻辑\n    console.log('刷新数据');\n  }, []);\n\n  return (\n    <div>\n      {/* 页面标题和控制 */}\n      <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n        <Col>\n          <Title level={2} style={{ margin: 0 }}>\n            仪表板\n          </Title>\n          <Text type=\"secondary\">\n            欢迎回来，{user?.name}！当前角色：{currentRole?.name}\n          </Text>\n        </Col>\n        <Col>\n          <Space>\n            <RangePicker\n              value={dateRange}\n              onChange={handleDateRangeChange}\n            />\n            <Button type=\"primary\" onClick={handleRefreshData}>\n              刷新数据\n            </Button>\n          </Space>\n        </Col>\n      </Row>\n\n      {/* 关键指标卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={processedMetrics.totalOrders}\n              prefix={<FileTextOutlined />}\n              suffix=\"个\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总收入\"\n              value={processedMetrics.totalRevenue}\n              prefix={<DollarOutlined />}\n              formatter={() => processedMetrics.totalRevenueFormatted}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"浪费减少率\"\n              value={processedMetrics.wasteReduction}\n              prefix={<ArrowDownOutlined />}\n              formatter={() => processedMetrics.wasteReductionFormatted}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"成本节约\"\n              value={processedMetrics.costSavings}\n              prefix={<RiseOutlined />}\n              formatter={() => processedMetrics.costSavingsFormatted}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 运营指标 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12}>\n          <Card title=\"库存周转率\" extra={<Text type=\"secondary\">次/年</Text>}>\n            <Statistic\n              value={processedMetrics.inventoryTurnover}\n              precision={1}\n              valueStyle={{ fontSize: 32, color: '#1890ff' }}\n            />\n            <Progress\n              percent={85}\n              strokeColor=\"#1890ff\"\n              showInfo={false}\n              style={{ marginTop: 16 }}\n            />\n            <Text type=\"secondary\">目标：10次/年</Text>\n          </Card>\n        </Col>\n        <Col xs={24} sm={12}>\n          <Card title=\"准时交付率\" extra={<Text type=\"secondary\">%</Text>}>\n            <Statistic\n              value={processedMetrics.onTimeDelivery}\n              formatter={(value) => formatPercentage(Number(value))}\n              valueStyle={{ fontSize: 32, color: '#52c41a' }}\n            />\n            <Progress\n              percent={95}\n              strokeColor=\"#52c41a\"\n              showInfo={false}\n              style={{ marginTop: 16 }}\n            />\n            <Text type=\"secondary\">目标：≥95%</Text>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 图表区域 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} lg={12}>\n          <Card title=\"浪费趋势分析\" extra={<Text type=\"secondary\">%</Text>}>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <AreaChart data={mockWasteTrend}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"packaging\"\n                  stackId=\"1\"\n                  stroke=\"#8884d8\"\n                  fill=\"#8884d8\"\n                  name=\"包装浪费\"\n                />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"moq\"\n                  stackId=\"1\"\n                  stroke=\"#82ca9d\"\n                  fill=\"#82ca9d\"\n                  name=\"MOQ浪费\"\n                />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"cutting\"\n                  stackId=\"1\"\n                  stroke=\"#ffc658\"\n                  fill=\"#ffc658\"\n                  name=\"切割浪费\"\n                />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"expiry\"\n                  stackId=\"1\"\n                  stroke=\"#ff7300\"\n                  fill=\"#ff7300\"\n                  name=\"过期浪费\"\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n        <Col xs={24} lg={12}>\n          <Card title=\"成本构成趋势\">\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={mockCostTrend}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip formatter={(value) => formatCurrency(Number(value))} />\n                <Legend />\n                <Bar dataKey=\"material\" fill=\"#8884d8\" name=\"材料成本\" />\n                <Bar dataKey=\"labor\" fill=\"#82ca9d\" name=\"人工成本\" />\n                <Bar dataKey=\"overhead\" fill=\"#ffc658\" name=\"制造费用\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 订单状态和最近订单 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} lg={8}>\n          <Card title=\"订单状态分布\">\n            <ResponsiveContainer width=\"100%\" height={250}>\n              <PieChart>\n                <Pie\n                  data={mockOrderStatus}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  innerRadius={60}\n                  outerRadius={100}\n                  paddingAngle={5}\n                  dataKey=\"value\"\n                >\n                  {mockOrderStatus.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n        <Col xs={24} lg={16}>\n          <Card title=\"最近订单\" extra={<Button type=\"link\">查看全部</Button>}>\n            <Table\n              dataSource={mockRecentOrders}\n              pagination={false}\n              size=\"small\"\n              columns={[\n                {\n                  title: '订单号',\n                  dataIndex: 'id',\n                  key: 'id',\n                  render: (text) => <Text strong>{text}</Text>,\n                },\n                {\n                  title: '客户',\n                  dataIndex: 'customer',\n                  key: 'customer',\n                },\n                {\n                  title: '金额',\n                  dataIndex: 'amount',\n                  key: 'amount',\n                  render: (value) => formatCurrency(value),\n                },\n                {\n                  title: '状态',\n                  dataIndex: 'status',\n                  key: 'status',\n                  render: (status) => (\n                    <Badge status={getStatusColor(status)} text={status} />\n                  ),\n                },\n                {\n                  title: '日期',\n                  dataIndex: 'date',\n                  key: 'date',\n                },\n              ]}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 系统预警 */}\n      <Row>\n        <Col span={24}>\n          <Card title=\"系统预警\" extra={<Button type=\"link\">查看全部</Button>}>\n            <List\n              dataSource={mockAlerts}\n              renderItem={(item) => (\n                <List.Item>\n                  <List.Item.Meta\n                    avatar={getAlertIcon(item.type)}\n                    title={item.message}\n                    description={item.time}\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default React.memo(DashboardPage);\n"], "mappings": "wHAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,WAAW,KAAQ,OAAO,CACxE,OACEC,GAAG,CACHC,GAAG,CACHC,IAAI,CACJC,SAAS,CACTC,QAAQ,CACRC,KAAK,CACLC,IAAI,CAEJC,KAAK,CACLC,UAAU,CACVC,KAAK,CACLC,MAAM,CAENC,UAAU,KAEL,MAAM,CACb,OAEEC,iBAAiB,CACjBC,cAAc,CAGdC,eAAe,CACfC,YAAY,CACZC,gBAAgB,CAEhBC,mBAAmB,KACd,mBAAmB,CAC1B,OAGEC,SAAS,CACTC,IAAI,CACJC,QAAQ,CACRC,GAAG,CACHC,QAAQ,CACRC,GAAG,CACHC,IAAI,CACJC,KAAK,CACLC,KAAK,CACLC,aAAa,CACbC,OAAO,CACPC,MAAM,CACNC,mBAAmB,KACd,UAAU,CACjB,MAAO,CAAAC,KAAK,KAAM,OAAO,CAEzB,OAASC,cAAc,CAAEC,cAAc,KAAQ,mBAAmB,CAClE,OAASC,cAAc,CAAEC,gBAAgB,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/D,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGjC,UAAU,CAClC,KAAM,CAAEkC,WAAY,CAAC,CAAG/B,UAAU,CAElC;AACA,KAAM,CAAAgC,WAAW,CAAG,CAClBC,WAAW,CAAE,GAAG,CAChBC,YAAY,CAAE,OAAO,CACrBC,cAAc,CAAE,IAAI,CACpBC,WAAW,CAAE,MAAM,CACnBC,iBAAiB,CAAE,GAAG,CACtBC,cAAc,CAAE,IAClB,CAAC,CAED,KAAM,CAAAC,cAAc,CAAG,CACrB,CAAEC,KAAK,CAAE,IAAI,CAAEC,SAAS,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAG,CAAEC,OAAO,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CACpE,CAAEJ,KAAK,CAAE,IAAI,CAAEC,SAAS,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAG,CAAEC,OAAO,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CACpE,CAAEJ,KAAK,CAAE,IAAI,CAAEC,SAAS,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAG,CAAEC,OAAO,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CACpE,CAAEJ,KAAK,CAAE,IAAI,CAAEC,SAAS,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAG,CAAEC,OAAO,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CACpE,CAAEJ,KAAK,CAAE,IAAI,CAAEC,SAAS,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAG,CAAEC,OAAO,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CACpE,CAAEJ,KAAK,CAAE,IAAI,CAAEC,SAAS,CAAE,GAAG,CAAEC,GAAG,CAAE,GAAG,CAAEC,OAAO,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CACrE,CAED,KAAM,CAAAC,aAAa,CAAG,CACpB,CAAEL,KAAK,CAAE,IAAI,CAAEM,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACjE,CAAER,KAAK,CAAE,IAAI,CAAEM,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACjE,CAAER,KAAK,CAAE,IAAI,CAAEM,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACjE,CAAER,KAAK,CAAE,IAAI,CAAEM,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACjE,CAAER,KAAK,CAAE,IAAI,CAAEM,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACjE,CAAER,KAAK,CAAE,IAAI,CAAEM,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,KAAM,CAAC,CAClE,CAED,KAAM,CAAAC,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,EAAE,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC5C,CAAEF,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,EAAE,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC5C,CAAEF,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,EAAE,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC5C,CAAEF,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC5C,CAED,KAAM,CAAAC,gBAAgB,CAAG,CACvB,CAAEC,EAAE,CAAE,SAAS,CAAEC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAK,CAAEC,IAAI,CAAE,YAAa,CAAC,CACtF,CAAEJ,EAAE,CAAE,SAAS,CAAEC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAK,CAAEC,MAAM,CAAE,KAAK,CAAEC,IAAI,CAAE,YAAa,CAAC,CACrF,CAAEJ,EAAE,CAAE,SAAS,CAAEC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAK,CAAEC,IAAI,CAAE,YAAa,CAAC,CACtF,CAAEJ,EAAE,CAAE,SAAS,CAAEC,QAAQ,CAAE,KAAK,CAAEC,MAAM,CAAE,KAAK,CAAEC,MAAM,CAAE,KAAK,CAAEC,IAAI,CAAE,YAAa,CAAC,CACpF,CAAEJ,EAAE,CAAE,SAAS,CAAEC,QAAQ,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAK,CAAEC,IAAI,CAAE,YAAa,CAAC,CACtF,CAED,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEL,EAAE,CAAE,CAAC,CAAEM,IAAI,CAAE,SAAS,CAAEC,OAAO,CAAE,0BAA0B,CAAEC,IAAI,CAAE,MAAO,CAAC,CAC7E,CAAER,EAAE,CAAE,CAAC,CAAEM,IAAI,CAAE,OAAO,CAAEC,OAAO,CAAE,mBAAmB,CAAEC,IAAI,CAAE,MAAO,CAAC,CACpE,CAAER,EAAE,CAAE,CAAC,CAAEM,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,oBAAoB,CAAEC,IAAI,CAAE,MAAO,CAAC,CACpE,CAAER,EAAE,CAAE,CAAC,CAAEM,IAAI,CAAE,SAAS,CAAEC,OAAO,CAAE,wBAAwB,CAAEC,IAAI,CAAE,MAAO,CAAC,CAC5E,CAED,KAAM,CAAAC,aAAuB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAAC,QAAQ,CAAG3C,cAAc,CAAC,CAAC,CACjC,KAAM,CAAE4C,IAAI,CAAEC,WAAY,CAAC,CAAG5C,cAAc,CAAC6C,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CACjE,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGpF,QAAQ,CAA6B,CACrEkC,KAAK,CAAC,CAAC,CAACmD,QAAQ,CAAC,EAAE,CAAE,KAAK,CAAC,CAC3BnD,KAAK,CAAC,CAAC,CACR,CAAC,CAEFnC,SAAS,CAAC,IAAM,CACd;AACA;AAAA,CACD,CAAE,CAAC+E,QAAQ,CAAEK,SAAS,CAAC,CAAC,CAEzB;AACA,KAAM,CAAAG,cAAc,CAAGpF,WAAW,CAAEqE,MAAc,EAAK,CACrD,OAAQA,MAAM,EACZ,IAAK,KAAK,CAAE,MAAO,SAAS,CAC5B,IAAK,KAAK,CAAE,MAAO,YAAY,CAC/B,IAAK,KAAK,CAAE,MAAO,SAAS,CAC5B,IAAK,KAAK,CAAE,MAAO,OAAO,CAC1B,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAgB,YAAY,CAAGrF,WAAW,CAAEwE,IAAY,EAAK,CACjD,OAAQA,IAAI,EACV,IAAK,SAAS,CAAE,mBAAOlC,IAAA,CAACvB,eAAe,EAACuE,KAAK,CAAE,CAAEtB,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACvE,IAAK,OAAO,CAAE,mBAAO1B,IAAA,CAACvB,eAAe,EAACuE,KAAK,CAAE,CAAEtB,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACrE,IAAK,MAAM,CAAE,mBAAO1B,IAAA,CAACpB,mBAAmB,EAACoE,KAAK,CAAE,CAAEtB,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACxE,QAAS,mBAAO1B,IAAA,CAACpB,mBAAmB,GAAE,CAAC,CACzC,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAqE,gBAAgB,CAAGxF,OAAO,CAAC,IAAAyF,aAAA,CAAAA,aAAA,IAC5B5C,WAAW,MACd6C,uBAAuB,CAAErD,gBAAgB,CAACQ,WAAW,CAACG,cAAc,CAAC,CACrE2C,qBAAqB,CAAEvD,cAAc,CAACS,WAAW,CAACE,YAAY,CAAC,CAC/D6C,oBAAoB,CAAExD,cAAc,CAACS,WAAW,CAACI,WAAW,CAAC,EAC7D,CAAE,EAAE,CAAC,CAEP,KAAM,CAAA4C,qBAAqB,CAAG5F,WAAW,CAAE6F,KAAwC,EAAK,CACtF,GAAIA,KAAK,CAAE,CACTX,YAAY,CAACW,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,iBAAiB,CAAG9F,WAAW,CAAC,IAAM,CAC1C;AACA+F,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,mBACExD,KAAA,QAAAyD,QAAA,eAEEzD,KAAA,CAACvC,GAAG,EAACiG,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACb,KAAK,CAAE,CAAEc,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eACtEzD,KAAA,CAACtC,GAAG,EAAA+F,QAAA,eACF3D,IAAA,CAACG,KAAK,EAAC4D,KAAK,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,MAAM,CAAE,CAAE,CAAE,CAAAL,QAAA,CAAC,oBAEvC,CAAO,CAAC,cACRzD,KAAA,CAACE,IAAI,EAAC8B,IAAI,CAAC,WAAW,CAAAyB,QAAA,EAAC,gCAChB,CAACpB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEf,IAAI,CAAC,sCAAM,CAACgB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEhB,IAAI,EACrC,CAAC,EACJ,CAAC,cACNxB,IAAA,CAACpC,GAAG,EAAA+F,QAAA,cACFzD,KAAA,CAAC9B,KAAK,EAAAuF,QAAA,eACJ3D,IAAA,CAACK,WAAW,EACVoB,KAAK,CAAEkB,SAAU,CACjBsB,QAAQ,CAAEX,qBAAsB,CACjC,CAAC,cACFtD,IAAA,CAAC3B,MAAM,EAAC6D,IAAI,CAAC,SAAS,CAACgC,OAAO,CAAEV,iBAAkB,CAAAG,QAAA,CAAC,0BAEnD,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAGNzD,KAAA,CAACvC,GAAG,EAACwG,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACnB,KAAK,CAAE,CAAEc,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eACjD3D,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAX,QAAA,cACzB3D,IAAA,CAACnC,IAAI,EAAA8F,QAAA,cACH3D,IAAA,CAAClC,SAAS,EACRyG,KAAK,CAAC,0BAAM,CACZ9C,KAAK,CAAEwB,gBAAgB,CAAC1C,WAAY,CACpCiE,MAAM,cAAExE,IAAA,CAACrB,gBAAgB,GAAE,CAAE,CAC7B8F,MAAM,CAAC,QAAG,CACVC,UAAU,CAAE,CAAEhD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN1B,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAX,QAAA,cACzB3D,IAAA,CAACnC,IAAI,EAAA8F,QAAA,cACH3D,IAAA,CAAClC,SAAS,EACRyG,KAAK,CAAC,oBAAK,CACX9C,KAAK,CAAEwB,gBAAgB,CAACzC,YAAa,CACrCgE,MAAM,cAAExE,IAAA,CAACxB,cAAc,GAAE,CAAE,CAC3BmG,SAAS,CAAEA,CAAA,GAAM1B,gBAAgB,CAACG,qBAAsB,CACxDsB,UAAU,CAAE,CAAEhD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN1B,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAX,QAAA,cACzB3D,IAAA,CAACnC,IAAI,EAAA8F,QAAA,cACH3D,IAAA,CAAClC,SAAS,EACRyG,KAAK,CAAC,gCAAO,CACb9C,KAAK,CAAEwB,gBAAgB,CAACxC,cAAe,CACvC+D,MAAM,cAAExE,IAAA,CAACzB,iBAAiB,GAAE,CAAE,CAC9BoG,SAAS,CAAEA,CAAA,GAAM1B,gBAAgB,CAACE,uBAAwB,CAC1DuB,UAAU,CAAE,CAAEhD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN1B,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAX,QAAA,cACzB3D,IAAA,CAACnC,IAAI,EAAA8F,QAAA,cACH3D,IAAA,CAAClC,SAAS,EACRyG,KAAK,CAAC,0BAAM,CACZ9C,KAAK,CAAEwB,gBAAgB,CAACvC,WAAY,CACpC8D,MAAM,cAAExE,IAAA,CAACtB,YAAY,GAAE,CAAE,CACzBiG,SAAS,CAAEA,CAAA,GAAM1B,gBAAgB,CAACI,oBAAqB,CACvDqB,UAAU,CAAE,CAAEhD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGNxB,KAAA,CAACvC,GAAG,EAACwG,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACnB,KAAK,CAAE,CAAEc,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eACjD3D,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAV,QAAA,cAClBzD,KAAA,CAACrC,IAAI,EAAC0G,KAAK,CAAC,gCAAO,CAACK,KAAK,cAAE5E,IAAA,CAACI,IAAI,EAAC8B,IAAI,CAAC,WAAW,CAAAyB,QAAA,CAAC,eAAG,CAAM,CAAE,CAAAA,QAAA,eAC3D3D,IAAA,CAAClC,SAAS,EACR2D,KAAK,CAAEwB,gBAAgB,CAACtC,iBAAkB,CAC1CkE,SAAS,CAAE,CAAE,CACbH,UAAU,CAAE,CAAEI,QAAQ,CAAE,EAAE,CAAEpD,KAAK,CAAE,SAAU,CAAE,CAChD,CAAC,cACF1B,IAAA,CAACjC,QAAQ,EACPgH,OAAO,CAAE,EAAG,CACZC,WAAW,CAAC,SAAS,CACrBC,QAAQ,CAAE,KAAM,CAChBjC,KAAK,CAAE,CAAEkC,SAAS,CAAE,EAAG,CAAE,CAC1B,CAAC,cACFlF,IAAA,CAACI,IAAI,EAAC8B,IAAI,CAAC,WAAW,CAAAyB,QAAA,CAAC,mCAAQ,CAAM,CAAC,EAClC,CAAC,CACJ,CAAC,cACN3D,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAAV,QAAA,cAClBzD,KAAA,CAACrC,IAAI,EAAC0G,KAAK,CAAC,gCAAO,CAACK,KAAK,cAAE5E,IAAA,CAACI,IAAI,EAAC8B,IAAI,CAAC,WAAW,CAAAyB,QAAA,CAAC,GAAC,CAAM,CAAE,CAAAA,QAAA,eACzD3D,IAAA,CAAClC,SAAS,EACR2D,KAAK,CAAEwB,gBAAgB,CAACrC,cAAe,CACvC+D,SAAS,CAAGlD,KAAK,EAAK3B,gBAAgB,CAACqF,MAAM,CAAC1D,KAAK,CAAC,CAAE,CACtDiD,UAAU,CAAE,CAAEI,QAAQ,CAAE,EAAE,CAAEpD,KAAK,CAAE,SAAU,CAAE,CAChD,CAAC,cACF1B,IAAA,CAACjC,QAAQ,EACPgH,OAAO,CAAE,EAAG,CACZC,WAAW,CAAC,SAAS,CACrBC,QAAQ,CAAE,KAAM,CAChBjC,KAAK,CAAE,CAAEkC,SAAS,CAAE,EAAG,CAAE,CAC1B,CAAC,cACFlF,IAAA,CAACI,IAAI,EAAC8B,IAAI,CAAC,WAAW,CAAAyB,QAAA,CAAC,6BAAO,CAAM,CAAC,EACjC,CAAC,CACJ,CAAC,EACH,CAAC,cAGNzD,KAAA,CAACvC,GAAG,EAACwG,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACnB,KAAK,CAAE,CAAEc,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eACjD3D,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAAX,QAAA,cAClB3D,IAAA,CAACnC,IAAI,EAAC0G,KAAK,CAAC,sCAAQ,CAACK,KAAK,cAAE5E,IAAA,CAACI,IAAI,EAAC8B,IAAI,CAAC,WAAW,CAAAyB,QAAA,CAAC,GAAC,CAAM,CAAE,CAAAA,QAAA,cAC1D3D,IAAA,CAACP,mBAAmB,EAAC2F,KAAK,CAAC,MAAM,CAACC,MAAM,CAAE,GAAI,CAAA1B,QAAA,cAC5CzD,KAAA,CAACrB,SAAS,EAACyG,IAAI,CAAEzE,cAAe,CAAA8C,QAAA,eAC9B3D,IAAA,CAACV,aAAa,EAACiG,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCvF,IAAA,CAACZ,KAAK,EAACoG,OAAO,CAAC,OAAO,CAAE,CAAC,cACzBxF,IAAA,CAACX,KAAK,GAAE,CAAC,cACTW,IAAA,CAACT,OAAO,GAAE,CAAC,cACXS,IAAA,CAACR,MAAM,GAAE,CAAC,cACVQ,IAAA,CAAClB,IAAI,EACHoD,IAAI,CAAC,UAAU,CACfsD,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAC,GAAG,CACXC,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAC,SAAS,CACdnE,IAAI,CAAC,0BAAM,CACZ,CAAC,cACFxB,IAAA,CAAClB,IAAI,EACHoD,IAAI,CAAC,UAAU,CACfsD,OAAO,CAAC,KAAK,CACbC,OAAO,CAAC,GAAG,CACXC,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAC,SAAS,CACdnE,IAAI,CAAC,iBAAO,CACb,CAAC,cACFxB,IAAA,CAAClB,IAAI,EACHoD,IAAI,CAAC,UAAU,CACfsD,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAC,GAAG,CACXC,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAC,SAAS,CACdnE,IAAI,CAAC,0BAAM,CACZ,CAAC,cACFxB,IAAA,CAAClB,IAAI,EACHoD,IAAI,CAAC,UAAU,CACfsD,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAC,GAAG,CACXC,MAAM,CAAC,SAAS,CAChBC,IAAI,CAAC,SAAS,CACdnE,IAAI,CAAC,0BAAM,CACZ,CAAC,EACO,CAAC,CACO,CAAC,CAClB,CAAC,CACJ,CAAC,cACNxB,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAAX,QAAA,cAClB3D,IAAA,CAACnC,IAAI,EAAC0G,KAAK,CAAC,sCAAQ,CAAAZ,QAAA,cAClB3D,IAAA,CAACP,mBAAmB,EAAC2F,KAAK,CAAC,MAAM,CAACC,MAAM,CAAE,GAAI,CAAA1B,QAAA,cAC5CzD,KAAA,CAACnB,QAAQ,EAACuG,IAAI,CAAEnE,aAAc,CAAAwC,QAAA,eAC5B3D,IAAA,CAACV,aAAa,EAACiG,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCvF,IAAA,CAACZ,KAAK,EAACoG,OAAO,CAAC,OAAO,CAAE,CAAC,cACzBxF,IAAA,CAACX,KAAK,GAAE,CAAC,cACTW,IAAA,CAACT,OAAO,EAACoF,SAAS,CAAGlD,KAAK,EAAK5B,cAAc,CAACsF,MAAM,CAAC1D,KAAK,CAAC,CAAE,CAAE,CAAC,cAChEzB,IAAA,CAACR,MAAM,GAAE,CAAC,cACVQ,IAAA,CAAChB,GAAG,EAACwG,OAAO,CAAC,UAAU,CAACG,IAAI,CAAC,SAAS,CAACnE,IAAI,CAAC,0BAAM,CAAE,CAAC,cACrDxB,IAAA,CAAChB,GAAG,EAACwG,OAAO,CAAC,OAAO,CAACG,IAAI,CAAC,SAAS,CAACnE,IAAI,CAAC,0BAAM,CAAE,CAAC,cAClDxB,IAAA,CAAChB,GAAG,EAACwG,OAAO,CAAC,UAAU,CAACG,IAAI,CAAC,SAAS,CAACnE,IAAI,CAAC,0BAAM,CAAE,CAAC,EAC7C,CAAC,CACQ,CAAC,CAClB,CAAC,CACJ,CAAC,EACH,CAAC,cAGNtB,KAAA,CAACvC,GAAG,EAACwG,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACnB,KAAK,CAAE,CAAEc,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eACjD3D,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAAX,QAAA,cACjB3D,IAAA,CAACnC,IAAI,EAAC0G,KAAK,CAAC,sCAAQ,CAAAZ,QAAA,cAClB3D,IAAA,CAACP,mBAAmB,EAAC2F,KAAK,CAAC,MAAM,CAACC,MAAM,CAAE,GAAI,CAAA1B,QAAA,cAC5CzD,KAAA,CAACjB,QAAQ,EAAA0E,QAAA,eACP3D,IAAA,CAACd,GAAG,EACFoG,IAAI,CAAE/D,eAAgB,CACtBqE,EAAE,CAAC,KAAK,CACRC,EAAE,CAAC,KAAK,CACRC,WAAW,CAAE,EAAG,CAChBC,WAAW,CAAE,GAAI,CACjBC,YAAY,CAAE,CAAE,CAChBR,OAAO,CAAC,OAAO,CAAA7B,QAAA,CAEdpC,eAAe,CAAC0E,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBAChCnG,IAAA,CAACb,IAAI,EAAuBwG,IAAI,CAAEO,KAAK,CAACxE,KAAM,UAAA0E,MAAA,CAA3BD,KAAK,CAAwB,CACjD,CAAC,CACC,CAAC,cACNnG,IAAA,CAACT,OAAO,GAAE,CAAC,cACXS,IAAA,CAACR,MAAM,GAAE,CAAC,EACF,CAAC,CACQ,CAAC,CAClB,CAAC,CACJ,CAAC,cACNQ,IAAA,CAACpC,GAAG,EAACwG,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAAX,QAAA,cAClB3D,IAAA,CAACnC,IAAI,EAAC0G,KAAK,CAAC,0BAAM,CAACK,KAAK,cAAE5E,IAAA,CAAC3B,MAAM,EAAC6D,IAAI,CAAC,MAAM,CAAAyB,QAAA,CAAC,0BAAI,CAAQ,CAAE,CAAAA,QAAA,cAC1D3D,IAAA,CAAChC,KAAK,EACJqI,UAAU,CAAE1E,gBAAiB,CAC7B2E,UAAU,CAAE,KAAM,CAClBC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAE,CACP,CACEjC,KAAK,CAAE,KAAK,CACZkC,SAAS,CAAE,IAAI,CACfC,GAAG,CAAE,IAAI,CACTC,MAAM,CAAGC,IAAI,eAAK5G,IAAA,CAACI,IAAI,EAACyG,MAAM,MAAAlD,QAAA,CAAEiD,IAAI,CAAO,CAC7C,CAAC,CACD,CACErC,KAAK,CAAE,IAAI,CACXkC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UACP,CAAC,CACD,CACEnC,KAAK,CAAE,IAAI,CACXkC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,MAAM,CAAGlF,KAAK,EAAK5B,cAAc,CAAC4B,KAAK,CACzC,CAAC,CACD,CACE8C,KAAK,CAAE,IAAI,CACXkC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,MAAM,CAAG5E,MAAM,eACb/B,IAAA,CAAC9B,KAAK,EAAC6D,MAAM,CAAEe,cAAc,CAACf,MAAM,CAAE,CAAC6E,IAAI,CAAE7E,MAAO,CAAE,CAE1D,CAAC,CACD,CACEwC,KAAK,CAAE,IAAI,CACXkC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MACP,CAAC,CACD,CACH,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGN1G,IAAA,CAACrC,GAAG,EAAAgG,QAAA,cACF3D,IAAA,CAACpC,GAAG,EAACkJ,IAAI,CAAE,EAAG,CAAAnD,QAAA,cACZ3D,IAAA,CAACnC,IAAI,EAAC0G,KAAK,CAAC,0BAAM,CAACK,KAAK,cAAE5E,IAAA,CAAC3B,MAAM,EAAC6D,IAAI,CAAC,MAAM,CAAAyB,QAAA,CAAC,0BAAI,CAAQ,CAAE,CAAAA,QAAA,cAC1D3D,IAAA,CAAC/B,IAAI,EACHoI,UAAU,CAAEpE,UAAW,CACvB8E,UAAU,CAAGC,IAAI,eACfhH,IAAA,CAAC/B,IAAI,CAACgJ,IAAI,EAAAtD,QAAA,cACR3D,IAAA,CAAC/B,IAAI,CAACgJ,IAAI,CAACC,IAAI,EACbC,MAAM,CAAEpE,YAAY,CAACiE,IAAI,CAAC9E,IAAI,CAAE,CAChCqC,KAAK,CAAEyC,IAAI,CAAC7E,OAAQ,CACpBiF,WAAW,CAAEJ,IAAI,CAAC5E,IAAK,CACxB,CAAC,CACO,CACX,CACH,CAAC,CACE,CAAC,CACJ,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,2BAAe9E,KAAK,CAAC+J,IAAI,CAAChF,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}