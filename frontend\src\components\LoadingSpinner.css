.loading-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 100px;
}

.loading-spinner-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.8);
}

.loading-spinner-fullscreen.with-overlay {
  background-color: rgba(0, 0, 0, 0.5);
}

.loading-spinner-fullscreen .ant-spin-dot {
  font-size: 24px;
}

.loading-spinner-fullscreen .ant-spin-text {
  color: #1890ff;
  font-size: 16px;
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-spinner-fullscreen .ant-spin-dot {
    font-size: 20px;
  }
  
  .loading-spinner-fullscreen .ant-spin-text {
    font-size: 14px;
  }
}