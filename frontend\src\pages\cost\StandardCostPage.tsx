import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  Progress,
  Alert,
  Tag,
  Typography,
  Tabs,
  Tooltip,
  message,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  CalculatorOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  DollarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { formatCurrency } from '../../utils/format';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface StandardCost {
  id: string;
  materialCode: string;
  materialName: string;
  specification: string;
  unit: string;
  standardCost: number;
  actualCost: number;
  variance: number;
  variancePercentage: number;
  lastUpdated: string;
  status: 'normal' | 'warning' | 'alert';
  category: string;
  supplier: string;
  effectiveDate: string;
  expiryDate: string;
}

interface CostVariance {
  id: string;
  orderNumber: string;
  materialCode: string;
  materialName: string;
  standardCost: number;
  actualCost: number;
  variance: number;
  variancePercentage: number;
  quantity: number;
  totalVariance: number;
  reason: string;
  responsiblePerson: string;
  status: 'pending' | 'investigating' | 'resolved';
  createdAt: string;
}

const StandardCostPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<StandardCost | null>(null);
  const [activeTab, setActiveTab] = useState('standards');
  const [form] = Form.useForm();

  // 模拟标准成本数据
  const mockStandardCosts: StandardCost[] = [
    {
      id: '1',
      materialCode: 'ANT-MAIN-001',
      materialName: '5G主天线单元',
      specification: '3.5GHz 64T64R',
      unit: 'PCS',
      standardCost: 12500,
      actualCost: 13200,
      variance: 700,
      variancePercentage: 5.6,
      lastUpdated: '2024-03-15T00:00:00Z',
      status: 'warning',
      category: '天线组件',
      supplier: '华为技术',
      effectiveDate: '2024-01-01',
      expiryDate: '2024-12-31',
    },
    {
      id: '2',
      materialCode: 'RRU-001',
      materialName: '射频拉远单元',
      specification: '200W 3.5GHz',
      unit: 'PCS',
      standardCost: 8500,
      actualCost: 8200,
      variance: -300,
      variancePercentage: -3.5,
      lastUpdated: '2024-03-14T00:00:00Z',
      status: 'normal',
      category: '射频器件',
      supplier: '中兴通讯',
      effectiveDate: '2024-01-01',
      expiryDate: '2024-12-31',
    },
    {
      id: '3',
      materialCode: 'CABLE-001',
      materialName: '同轴电缆',
      specification: '7/8" 50Ω',
      unit: 'M',
      standardCost: 45,
      actualCost: 52,
      variance: 7,
      variancePercentage: 15.6,
      lastUpdated: '2024-03-13T00:00:00Z',
      status: 'alert',
      category: '连接器件',
      supplier: '安费诺',
      effectiveDate: '2024-01-01',
      expiryDate: '2024-12-31',
    },
  ];

  // 模拟成本差异数据
  const mockVariances: CostVariance[] = [
    {
      id: '1',
      orderNumber: 'ORD-2024-001',
      materialCode: 'ANT-MAIN-001',
      materialName: '5G主天线单元',
      standardCost: 12500,
      actualCost: 13200,
      variance: 700,
      variancePercentage: 5.6,
      quantity: 10,
      totalVariance: 7000,
      reason: '供应商价格上涨',
      responsiblePerson: '采购经理',
      status: 'investigating',
      createdAt: '2024-03-15T00:00:00Z',
    },
    {
      id: '2',
      orderNumber: 'ORD-2024-002',
      materialCode: 'CABLE-001',
      materialName: '同轴电缆',
      standardCost: 45,
      actualCost: 52,
      variance: 7,
      variancePercentage: 15.6,
      quantity: 500,
      totalVariance: 3500,
      reason: '汇率波动影响',
      responsiblePerson: '财务经理',
      status: 'pending',
      createdAt: '2024-03-13T00:00:00Z',
    },
  ];

  // 模拟概览数据
  const mockOverview = {
    totalMaterials: 156,
    normalCount: 120,
    warningCount: 25,
    alertCount: 11,
    avgVariance: 3.2,
    totalVarianceAmount: 45600,
    lastUpdateTime: '2024-03-15T10:30:00Z',
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const handleAdd = () => {
    setEditingRecord(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record: StandardCost) => {
    setEditingRecord(record);
    form.setFieldsValue({
      ...record,
      effectiveDate: dayjs(record.effectiveDate),
      expiryDate: dayjs(record.expiryDate),
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条标准成本记录吗？',
      onOk: () => {
        message.success('删除成功');
      },
    });
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success(editingRecord ? '更新成功' : '创建成功');
      setIsModalVisible(false);
      loadData();
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRecalculate = () => {
    Modal.confirm({
      title: '重新计算标准成本',
      content: '这将基于最新的采购价格和市场数据重新计算所有标准成本，确定继续吗？',
      onOk: () => {
        message.success('重新计算完成');
        loadData();
      },
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'green';
      case 'warning': return 'orange';
      case 'alert': return 'red';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal': return '正常';
      case 'warning': return '预警';
      case 'alert': return '异常';
      default: return '未知';
    }
  };

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) {
      return <ArrowUpOutlined style={{ color: '#ff4d4f' }} />;
    } else if (variance < 0) {
      return <ArrowDownOutlined style={{ color: '#52c41a' }} />;
    }
    return <CheckCircleOutlined style={{ color: '#1890ff' }} />;
  };

  const standardCostColumns: ColumnsType<StandardCost> = [
    {
      title: '物料编码',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 120,
      fixed: 'left',
    },
    {
      title: '物料名称',
      dataIndex: 'materialName',
      key: 'materialName',
      ellipsis: true,
    },
    {
      title: '规格型号',
      dataIndex: 'specification',
      key: 'specification',
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 60,
    },
    {
      title: '标准成本',
      dataIndex: 'standardCost',
      key: 'standardCost',
      width: 100,
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: '实际成本',
      dataIndex: 'actualCost',
      key: 'actualCost',
      width: 100,
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: '差异',
      dataIndex: 'variance',
      key: 'variance',
      width: 120,
      render: (variance: number, record: StandardCost) => (
        <Space direction="vertical" size={0}>
          <Space>
            {getVarianceIcon(variance)}
            <Text style={{ color: variance > 0 ? '#ff4d4f' : variance < 0 ? '#52c41a' : '#1890ff' }}>
              {formatCurrency(Math.abs(variance))}
            </Text>
          </Space>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.variancePercentage > 0 ? '+' : ''}{record.variancePercentage.toFixed(1)}%
          </Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 100,
    },
    {
      title: '更新时间',
      dataIndex: 'lastUpdated',
      key: 'lastUpdated',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const varianceColumns: ColumnsType<CostVariance> = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120,
    },
    {
      title: '物料编码',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 120,
    },
    {
      title: '物料名称',
      dataIndex: 'materialName',
      key: 'materialName',
      ellipsis: true,
    },
    {
      title: '标准成本',
      dataIndex: 'standardCost',
      key: 'standardCost',
      width: 100,
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: '实际成本',
      dataIndex: 'actualCost',
      key: 'actualCost',
      width: 100,
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: '单位差异',
      dataIndex: 'variance',
      key: 'variance',
      width: 100,
      render: (variance: number, record: CostVariance) => (
        <Space direction="vertical" size={0}>
          <Text style={{ color: variance > 0 ? '#ff4d4f' : '#52c41a' }}>
            {formatCurrency(Math.abs(variance))}
          </Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.variancePercentage > 0 ? '+' : ''}{record.variancePercentage.toFixed(1)}%
          </Text>
        </Space>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
    },
    {
      title: '总差异',
      dataIndex: 'totalVariance',
      key: 'totalVariance',
      width: 100,
      render: (variance: number) => (
        <Text style={{ color: variance > 0 ? '#ff4d4f' : '#52c41a' }}>
          {formatCurrency(Math.abs(variance))}
        </Text>
      ),
    },
    {
      title: '差异原因',
      dataIndex: 'reason',
      key: 'reason',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const statusMap = {
          pending: { color: 'orange', text: '待处理' },
          investigating: { color: 'blue', text: '调查中' },
          resolved: { color: 'green', text: '已解决' },
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
  ];

  const renderOverviewTab = () => (
    <div>
      {/* 预警信息 */}
      {mockOverview.alertCount > 0 && (
        <Alert
          message="成本异常预警"
          description={`发现 ${mockOverview.alertCount} 个物料成本差异超过预警阈值，建议及时处理。`}
          type="error"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 概览统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="物料总数"
              value={mockOverview.totalMaterials}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="正常物料"
              value={mockOverview.normalCount}
              valueStyle={{ color: '#52c41a' }}
              suffix={
                <Tooltip title={`占比: ${((mockOverview.normalCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                </Tooltip>
              }
            />
            <Progress
              percent={(mockOverview.normalCount / mockOverview.totalMaterials) * 100}
              size="small"
              showInfo={false}
              strokeColor="#52c41a"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="预警物料"
              value={mockOverview.warningCount}
              valueStyle={{ color: '#faad14' }}
              suffix={
                <Tooltip title={`占比: ${((mockOverview.warningCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>
                  <WarningOutlined style={{ color: '#faad14' }} />
                </Tooltip>
              }
            />
            <Progress
              percent={(mockOverview.warningCount / mockOverview.totalMaterials) * 100}
              size="small"
              showInfo={false}
              strokeColor="#faad14"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="异常物料"
              value={mockOverview.alertCount}
              valueStyle={{ color: '#ff4d4f' }}
              suffix={
                <Tooltip title={`占比: ${((mockOverview.alertCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>
                  <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                </Tooltip>
              }
            />
            <Progress
              percent={(mockOverview.alertCount / mockOverview.totalMaterials) * 100}
              size="small"
              showInfo={false}
              strokeColor="#ff4d4f"
            />
          </Card>
        </Col>
      </Row>

      {/* 标准成本列表 */}
      <Card title="标准成本管理" extra={
        <Space>
          <Button icon={<SyncOutlined />} onClick={handleRecalculate}>
            重新计算
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增标准成本
          </Button>
        </Space>
      }>
        <Table
          columns={standardCostColumns}
          dataSource={mockStandardCosts}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            total: mockStandardCosts.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>
    </div>
  );

  const renderVarianceTab = () => (
    <div>
      <Card title="成本差异分析" extra={
        <Space>
          <Button icon={<ExportOutlined />}>
            导出差异报告
          </Button>
        </Space>
      }>
        <Table
          columns={varianceColumns}
          dataSource={mockVariances}
          rowKey="id"
          loading={loading}
          pagination={{
            total: mockVariances.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>
    </div>
  );

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              标准成本管理
            </Title>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索物料编码或名称"
                allowClear
                style={{ width: 200 }}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
              />
              <Select
                placeholder="物料类别"
                allowClear
                style={{ width: 120 }}
                value={selectedCategory}
                onChange={setSelectedCategory}
                options={[
                  { label: '天线组件', value: '天线组件' },
                  { label: '射频器件', value: '射频器件' },
                  { label: '连接器件', value: '连接器件' },
                ]}
              />
              <RangePicker
                value={dateRange}
                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                style={{ width: 240 }}
              />
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="标准成本" key="standards">
            {renderOverviewTab()}
          </TabPane>
          <TabPane tab="成本差异" key="variances">
            {renderVarianceTab()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingRecord ? '编辑标准成本' : '新增标准成本'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="materialCode"
                label="物料编码"
                rules={[{ required: true, message: '请输入物料编码' }]}
              >
                <Input placeholder="请输入物料编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="materialName"
                label="物料名称"
                rules={[{ required: true, message: '请输入物料名称' }]}
              >
                <Input placeholder="请输入物料名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="specification"
                label="规格型号"
              >
                <Input placeholder="请输入规格型号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请选择单位' }]}
              >
                <Select
                  placeholder="请选择单位"
                  options={[
                    { label: 'PCS', value: 'PCS' },
                    { label: 'M', value: 'M' },
                    { label: 'KG', value: 'KG' },
                    { label: 'SET', value: 'SET' },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="standardCost"
                label="标准成本"
                rules={[{ required: true, message: '请输入标准成本' }]}
              >
                <InputNumber
                  placeholder="请输入标准成本"
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={((value: string | undefined) => {
                    const parsed = parseFloat(value!.replace(/¥\s?|(,*)/g, ''));
                    return isNaN(parsed) ? 0 : parsed;
                  }) as any}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="物料类别"
                rules={[{ required: true, message: '请选择物料类别' }]}
              >
                <Select
                  placeholder="请选择物料类别"
                  options={[
                    { label: '天线组件', value: '天线组件' },
                    { label: '射频器件', value: '射频器件' },
                    { label: '连接器件', value: '连接器件' },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="supplier"
                label="主要供应商"
              >
                <Input placeholder="请输入主要供应商" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="effectiveDate"
                label="生效日期"
                rules={[{ required: true, message: '请选择生效日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="expiryDate"
            label="失效日期"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingRecord ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default StandardCostPage;