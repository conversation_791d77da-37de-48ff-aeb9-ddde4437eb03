{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\components\\\\layout\\\\MainLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { Layout, Menu, Avatar, Dropdown, Button, Space, Badge, Breadcrumb, theme } from 'antd';\nimport { MenuFoldOutlined, MenuUnfoldOutlined, DashboardOutlined, FileTextOutlined, ShoppingCartOutlined, InboxOutlined, DollarOutlined, ToolOutlined, SwapOutlined, Bar<PERSON>hartOutlined, SettingOutlined, MobileOutlined, UserOutlined, LogoutOutlined, BellOutlined, SwitcherOutlined } from '@ant-design/icons';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { logout, switchRole } from '../../store/slices/authSlice';\nimport { ROUTES } from '../../constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = Layout;\n\n// 菜单配置\nconst menuItems = [{\n  key: ROUTES.DASHBOARD,\n  icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 11\n  }, this),\n  label: '仪表板'\n}, {\n  key: 'bom',\n  icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 11\n  }, this),\n  label: 'BOM管理',\n  children: [{\n    key: ROUTES.CORE_BOM,\n    label: '核心BOM'\n  }, {\n    key: ROUTES.ORDER_BOM,\n    label: '订单BOM'\n  }]\n}, {\n  key: 'material',\n  icon: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 11\n  }, this),\n  label: '物料管理',\n  children: [{\n    key: ROUTES.MATERIALS,\n    label: '物料清单'\n  }]\n}, {\n  key: 'inventory',\n  icon: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 11\n  }, this),\n  label: '库存管理',\n  children: [{\n    key: ROUTES.INVENTORY,\n    label: '库存查询'\n  }, {\n    key: ROUTES.INVENTORY_RECEIVE,\n    label: '入库管理'\n  }, {\n    key: ROUTES.INVENTORY_ISSUE,\n    label: '出库管理'\n  }, {\n    key: ROUTES.REMNANTS,\n    label: '余料管理'\n  }, {\n    key: ROUTES.CUTTING_PLAN,\n    label: '切割计划'\n  }]\n}, {\n  key: 'purchase',\n  icon: /*#__PURE__*/_jsxDEV(ShoppingCartOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 11\n  }, this),\n  label: '采购管理',\n  children: [{\n    key: ROUTES.PURCHASE,\n    label: '采购订单'\n  }, {\n    key: ROUTES.PURCHASE_REQUISITION,\n    label: '采购申请'\n  }, {\n    key: ROUTES.MRP_CALCULATION,\n    label: 'MRP计算'\n  }, {\n    key: ROUTES.PURCHASE_OPTIMIZATION,\n    label: '采购优化'\n  }]\n}, {\n  key: 'cost',\n  icon: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 11\n  }, this),\n  label: '成本管理',\n  children: [{\n    key: ROUTES.COST_ANALYSIS,\n    label: '成本分析'\n  }, {\n    key: ROUTES.COST_REPORTS,\n    label: '成本报告'\n  }, {\n    key: ROUTES.WASTE_TRACKING,\n    label: '浪费跟踪'\n  }, {\n    key: ROUTES.STANDARD_COST,\n    label: '标准成本'\n  }]\n}, {\n  key: 'service',\n  icon: /*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 11\n  }, this),\n  label: '服务管理',\n  children: [{\n    key: ROUTES.SERVICE_BOM,\n    label: '服务BOM'\n  }, {\n    key: ROUTES.DEVICE_ARCHIVE,\n    label: '设备档案'\n  }, {\n    key: ROUTES.MAINTENANCE,\n    label: '维护记录'\n  }, {\n    key: ROUTES.AS_BUILT_BOM,\n    label: 'As-Built BOM'\n  }]\n}, {\n  key: 'ecn',\n  icon: /*#__PURE__*/_jsxDEV(SwapOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 11\n  }, this),\n  label: 'ECN管理',\n  children: [{\n    key: ROUTES.ECN,\n    label: 'ECN列表'\n  }]\n}, {\n  key: 'reports',\n  icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 11\n  }, this),\n  label: '报告分析',\n  children: [{\n    key: ROUTES.REPORTS,\n    label: '报告中心'\n  }, {\n    key: ROUTES.DASHBOARD_CONFIG,\n    label: '仪表板配置'\n  }]\n}, {\n  key: 'system',\n  icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 11\n  }, this),\n  label: '系统管理',\n  children: [{\n    key: ROUTES.USERS,\n    label: '用户管理'\n  }, {\n    key: ROUTES.ROLES,\n    label: '角色管理'\n  }, {\n    key: ROUTES.PERMISSIONS,\n    label: '权限管理'\n  }, {\n    key: ROUTES.SYSTEM_CONFIG,\n    label: '系统配置'\n  }]\n}, {\n  key: 'mobile',\n  icon: /*#__PURE__*/_jsxDEV(MobileOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 11\n  }, this),\n  label: '移动端',\n  children: [{\n    key: ROUTES.MOBILE,\n    label: '移动首页'\n  }, {\n    key: ROUTES.MOBILE_SCAN,\n    label: '扫码操作'\n  }, {\n    key: ROUTES.MOBILE_INVENTORY,\n    label: '移动库存'\n  }]\n}];\nconst MainLayout = () => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const {\n    user,\n    currentRole\n  } = useAppSelector(state => state.auth);\n  const {\n    token: {\n      colorBgContainer\n    }\n  } = theme.useToken();\n\n  // 处理菜单点击\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n\n  // 处理用户菜单点击\n  const handleUserMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'logout':\n        dispatch(logout());\n        navigate(ROUTES.LOGIN);\n        break;\n      case 'profile':\n        // 跳转到个人资料页面\n        break;\n      default:\n        break;\n    }\n  };\n\n  // 处理角色切换\n  const handleRoleSwitch = roleId => {\n    dispatch(switchRole(roleId));\n  };\n\n  // 用户下拉菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 13\n    }, this),\n    label: '退出登录'\n  }];\n\n  // 角色切换菜单\n  const roleMenuItems = (user === null || user === void 0 ? void 0 : user.roles.map(role => ({\n    key: role.id,\n    label: role.name,\n    onClick: () => handleRoleSwitch(role.id)\n  }))) || [];\n\n  // 生成面包屑\n  const generateBreadcrumb = () => {\n    const pathSnippets = location.pathname.split('/').filter(i => i);\n    const breadcrumbItems = [{\n      title: '首页'\n    }];\n    pathSnippets.forEach((snippet, index) => {\n      const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;\n      const menuItem = findMenuItemByKey(menuItems, url);\n      if (menuItem) {\n        breadcrumbItems.push({\n          title: menuItem.label\n        });\n      }\n    });\n    return breadcrumbItems;\n  };\n\n  // 查找菜单项\n  const findMenuItemByKey = (items, key) => {\n    for (const item of items) {\n      if (item.key === key) {\n        return item;\n      }\n      if (item.children) {\n        const found = findMenuItemByKey(item.children, key);\n        if (found) return found;\n      }\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: 32,\n          margin: 16,\n          background: 'rgba(255, 255, 255, 0.3)',\n          borderRadius: 6,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: 'bold'\n        },\n        children: collapsed ? 'LBS' : 'Link-BOM-S'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        theme: \"dark\",\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: handleMenuClick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 16px',\n          background: colorBgContainer,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 56\n          }, this),\n          onClick: () => setCollapsed(!collapsed),\n          style: {\n            fontSize: '16px',\n            width: 64,\n            height: 64\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          size: \"middle\",\n          children: [(user === null || user === void 0 ? void 0 : user.roles) && user.roles.length > 1 && /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: roleMenuItems\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(SwitcherOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 43\n              }, this),\n              children: currentRole === null || currentRole === void 0 ? void 0 : currentRole.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n            count: 5,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems,\n              onClick: handleUserMenuClick\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 31\n                }, this),\n                src: user === null || user === void 0 ? void 0 : user.avatar\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: user === null || user === void 0 ? void 0 : user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {\n          style: {\n            marginBottom: 16\n          },\n          items: generateBreadcrumb()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: 24,\n            minHeight: 360,\n            background: colorBgContainer,\n            borderRadius: 6\n          },\n          children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"96mu0/JMMZbTebvsofOiV5gNxwY=\", false, function () {\n  return [useNavigate, useLocation, useAppDispatch, useAppSelector, theme.useToken];\n});\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "useNavigate", "useLocation", "Layout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "Space", "Badge", "Breadcrumb", "theme", "MenuFoldOutlined", "MenuUnfoldOutlined", "DashboardOutlined", "FileTextOutlined", "ShoppingCartOutlined", "InboxOutlined", "DollarOutlined", "ToolOutlined", "SwapOutlined", "BarChartOutlined", "SettingOutlined", "MobileOutlined", "UserOutlined", "LogoutOutlined", "BellOutlined", "SwitcherOutlined", "useAppDispatch", "useAppSelector", "logout", "switchRole", "ROUTES", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "menuItems", "key", "DASHBOARD", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "children", "CORE_BOM", "ORDER_BOM", "MATERIALS", "INVENTORY", "INVENTORY_RECEIVE", "INVENTORY_ISSUE", "REMNANTS", "CUTTING_PLAN", "PURCHASE", "PURCHASE_REQUISITION", "MRP_CALCULATION", "PURCHASE_OPTIMIZATION", "COST_ANALYSIS", "COST_REPORTS", "WASTE_TRACKING", "STANDARD_COST", "SERVICE_BOM", "DEVICE_ARCHIVE", "MAINTENANCE", "AS_BUILT_BOM", "ECN", "REPORTS", "DASHBOARD_CONFIG", "USERS", "ROLES", "PERMISSIONS", "SYSTEM_CONFIG", "MOBILE", "MOBILE_SCAN", "MOBILE_INVENTORY", "MainLayout", "_s", "collapsed", "setCollapsed", "navigate", "location", "dispatch", "user", "currentRole", "state", "auth", "token", "colorBgContainer", "useToken", "handleMenuClick", "handleUserMenuClick", "LOGIN", "handleRoleSwitch", "roleId", "userMenuItems", "type", "roleMenuItems", "roles", "map", "role", "id", "name", "onClick", "generateBreadcrumb", "pathSnippets", "pathname", "split", "filter", "i", "breadcrumbItems", "title", "for<PERSON>ach", "snippet", "index", "url", "slice", "join", "menuItem", "findMenuItemByKey", "push", "items", "item", "found", "style", "minHeight", "trigger", "collapsible", "height", "margin", "background", "borderRadius", "display", "alignItems", "justifyContent", "color", "fontWeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "padding", "fontSize", "width", "size", "length", "menu", "placement", "count", "cursor", "src", "avatar", "marginBottom", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Layout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Button,\n  Space,\n  Badge,\n  Breadcrumb,\n  theme,\n  MenuProps,\n} from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  DashboardOutlined,\n  FileTextOutlined,\n  ShoppingCartOutlined,\n  InboxOutlined,\n  DollarOutlined,\n  ToolOutlined,\n  SwapOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  SettingOutlined,\n  MobileOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  SwitcherOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { logout, switchRole } from '../../store/slices/authSlice';\nimport { ROUTES, USER_ROLES } from '../../constants';\n\nconst { Header, Sider, Content } = Layout;\n\n// 菜单配置\nconst menuItems: MenuProps['items'] = [\n  {\n    key: ROUTES.DASHBOARD,\n    icon: <DashboardOutlined />,\n    label: '仪表板',\n  },\n  {\n    key: 'bom',\n    icon: <FileTextOutlined />,\n    label: 'BOM管理',\n    children: [\n      {\n        key: ROUTES.CORE_BOM,\n        label: '核心BOM',\n      },\n      {\n        key: ROUTES.ORDER_BOM,\n        label: '订单BOM',\n      },\n    ],\n  },\n  {\n    key: 'material',\n    icon: <InboxOutlined />,\n    label: '物料管理',\n    children: [\n      {\n        key: ROUTES.MATERIALS,\n        label: '物料清单',\n      },\n    ],\n  },\n  {\n    key: 'inventory',\n    icon: <InboxOutlined />,\n    label: '库存管理',\n    children: [\n      {\n        key: ROUTES.INVENTORY,\n        label: '库存查询',\n      },\n      {\n        key: ROUTES.INVENTORY_RECEIVE,\n        label: '入库管理',\n      },\n      {\n        key: ROUTES.INVENTORY_ISSUE,\n        label: '出库管理',\n      },\n      {\n        key: ROUTES.REMNANTS,\n        label: '余料管理',\n      },\n      {\n        key: ROUTES.CUTTING_PLAN,\n        label: '切割计划',\n      },\n    ],\n  },\n  {\n    key: 'purchase',\n    icon: <ShoppingCartOutlined />,\n    label: '采购管理',\n    children: [\n      {\n        key: ROUTES.PURCHASE,\n        label: '采购订单',\n      },\n      {\n        key: ROUTES.PURCHASE_REQUISITION,\n        label: '采购申请',\n      },\n      {\n        key: ROUTES.MRP_CALCULATION,\n        label: 'MRP计算',\n      },\n      {\n        key: ROUTES.PURCHASE_OPTIMIZATION,\n        label: '采购优化',\n      },\n    ],\n  },\n  {\n    key: 'cost',\n    icon: <DollarOutlined />,\n    label: '成本管理',\n    children: [\n      {\n        key: ROUTES.COST_ANALYSIS,\n        label: '成本分析',\n      },\n      {\n        key: ROUTES.COST_REPORTS,\n        label: '成本报告',\n      },\n      {\n        key: ROUTES.WASTE_TRACKING,\n        label: '浪费跟踪',\n      },\n      {\n        key: ROUTES.STANDARD_COST,\n        label: '标准成本',\n      },\n    ],\n  },\n  {\n    key: 'service',\n    icon: <ToolOutlined />,\n    label: '服务管理',\n    children: [\n      {\n        key: ROUTES.SERVICE_BOM,\n        label: '服务BOM',\n      },\n      {\n        key: ROUTES.DEVICE_ARCHIVE,\n        label: '设备档案',\n      },\n      {\n        key: ROUTES.MAINTENANCE,\n        label: '维护记录',\n      },\n      {\n        key: ROUTES.AS_BUILT_BOM,\n        label: 'As-Built BOM',\n      },\n    ],\n  },\n  {\n    key: 'ecn',\n    icon: <SwapOutlined />,\n    label: 'ECN管理',\n    children: [\n      {\n        key: ROUTES.ECN,\n        label: 'ECN列表',\n      },\n    ],\n  },\n  {\n    key: 'reports',\n    icon: <BarChartOutlined />,\n    label: '报告分析',\n    children: [\n      {\n        key: ROUTES.REPORTS,\n        label: '报告中心',\n      },\n      {\n        key: ROUTES.DASHBOARD_CONFIG,\n        label: '仪表板配置',\n      },\n    ],\n  },\n  {\n    key: 'system',\n    icon: <SettingOutlined />,\n    label: '系统管理',\n    children: [\n      {\n        key: ROUTES.USERS,\n        label: '用户管理',\n      },\n      {\n        key: ROUTES.ROLES,\n        label: '角色管理',\n      },\n      {\n        key: ROUTES.PERMISSIONS,\n        label: '权限管理',\n      },\n      {\n        key: ROUTES.SYSTEM_CONFIG,\n        label: '系统配置',\n      },\n    ],\n  },\n  {\n    key: 'mobile',\n    icon: <MobileOutlined />,\n    label: '移动端',\n    children: [\n      {\n        key: ROUTES.MOBILE,\n        label: '移动首页',\n      },\n      {\n        key: ROUTES.MOBILE_SCAN,\n        label: '扫码操作',\n      },\n      {\n        key: ROUTES.MOBILE_INVENTORY,\n        label: '移动库存',\n      },\n    ],\n  },\n];\n\nconst MainLayout: React.FC = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const { user, currentRole } = useAppSelector(state => state.auth);\n  const {\n    token: { colorBgContainer },\n  } = theme.useToken();\n\n  // 处理菜单点击\n  const handleMenuClick = ({ key }: { key: string }) => {\n    navigate(key);\n  };\n\n  // 处理用户菜单点击\n  const handleUserMenuClick = ({ key }: { key: string }) => {\n    switch (key) {\n      case 'logout':\n        dispatch(logout());\n        navigate(ROUTES.LOGIN);\n        break;\n      case 'profile':\n        // 跳转到个人资料页面\n        break;\n      default:\n        break;\n    }\n  };\n\n  // 处理角色切换\n  const handleRoleSwitch = (roleId: string) => {\n    dispatch(switchRole(roleId));\n  };\n\n  // 用户下拉菜单\n  const userMenuItems: MenuProps['items'] = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n    },\n  ];\n\n  // 角色切换菜单\n  const roleMenuItems: MenuProps['items'] = user?.roles.map(role => ({\n    key: role.id,\n    label: role.name,\n    onClick: () => handleRoleSwitch(role.id),\n  })) || [];\n\n  // 生成面包屑\n  const generateBreadcrumb = () => {\n    const pathSnippets = location.pathname.split('/').filter(i => i);\n    const breadcrumbItems = [\n      {\n        title: '首页',\n      },\n    ];\n\n    pathSnippets.forEach((snippet, index) => {\n      const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;\n      const menuItem = findMenuItemByKey(menuItems, url);\n      if (menuItem) {\n        breadcrumbItems.push({\n          title: menuItem.label as string,\n        });\n      }\n    });\n\n    return breadcrumbItems;\n  };\n\n  // 查找菜单项\n  const findMenuItemByKey = (items: any[], key: string): any => {\n    for (const item of items) {\n      if (item.key === key) {\n        return item;\n      }\n      if (item.children) {\n        const found = findMenuItemByKey(item.children, key);\n        if (found) return found;\n      }\n    }\n    return null;\n  };\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed}>\n        <div style={{ \n          height: 32, \n          margin: 16, \n          background: 'rgba(255, 255, 255, 0.3)',\n          borderRadius: 6,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontWeight: 'bold',\n        }}>\n          {collapsed ? 'LBS' : 'Link-BOM-S'}\n        </div>\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={handleMenuClick}\n        />\n      </Sider>\n      \n      <Layout>\n        <Header style={{ \n          padding: '0 16px', \n          background: colorBgContainer,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n        }}>\n          <Button\n            type=\"text\"\n            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => setCollapsed(!collapsed)}\n            style={{\n              fontSize: '16px',\n              width: 64,\n              height: 64,\n            }}\n          />\n          \n          <Space size=\"middle\">\n            {/* 角色切换 */}\n            {user?.roles && user.roles.length > 1 && (\n              <Dropdown\n                menu={{ items: roleMenuItems }}\n                placement=\"bottomRight\"\n              >\n                <Button type=\"text\" icon={<SwitcherOutlined />}>\n                  {currentRole?.name}\n                </Button>\n              </Dropdown>\n            )}\n            \n            {/* 通知 */}\n            <Badge count={5}>\n              <Button type=\"text\" icon={<BellOutlined />} />\n            </Badge>\n            \n            {/* 用户信息 */}\n            <Dropdown\n              menu={{ items: userMenuItems, onClick: handleUserMenuClick }}\n              placement=\"bottomRight\"\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} src={user?.avatar} />\n                <span>{user?.name}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n        \n        <Content style={{ margin: '16px' }}>\n          <Breadcrumb\n            style={{ marginBottom: 16 }}\n            items={generateBreadcrumb()}\n          />\n          \n          <div\n            style={{\n              padding: 24,\n              minHeight: 360,\n              background: colorBgContainer,\n              borderRadius: 6,\n            }}\n          >\n            <Outlet />\n          </div>\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SACEC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,KAAK,QAEA,MAAM;AACb,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,QACX,mBAAmB;AAE1B,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,MAAM,EAAEC,UAAU,QAAQ,8BAA8B;AACjE,SAASC,MAAM,QAAoB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGlC,MAAM;;AAEzC;AACA,MAAMmC,SAA6B,GAAG,CACpC;EACEC,GAAG,EAAEP,MAAM,CAACQ,SAAS;EACrBC,IAAI,eAAEP,OAAA,CAACpB,iBAAiB;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3BC,KAAK,EAAE;AACT,CAAC,EACD;EACEP,GAAG,EAAE,KAAK;EACVE,IAAI,eAAEP,OAAA,CAACnB,gBAAgB;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC1BC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAACgB,QAAQ;IACpBF,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACiB,SAAS;IACrBH,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,UAAU;EACfE,IAAI,eAAEP,OAAA,CAACjB,aAAa;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAACkB,SAAS;IACrBJ,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,WAAW;EAChBE,IAAI,eAAEP,OAAA,CAACjB,aAAa;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAACmB,SAAS;IACrBL,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACoB,iBAAiB;IAC7BN,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACqB,eAAe;IAC3BP,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACsB,QAAQ;IACpBR,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACuB,YAAY;IACxBT,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,UAAU;EACfE,IAAI,eAAEP,OAAA,CAAClB,oBAAoB;IAAA0B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC9BC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAACwB,QAAQ;IACpBV,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACyB,oBAAoB;IAChCX,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAAC0B,eAAe;IAC3BZ,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAAC2B,qBAAqB;IACjCb,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,MAAM;EACXE,IAAI,eAAEP,OAAA,CAAChB,cAAc;IAAAwB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAAC4B,aAAa;IACzBd,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAAC6B,YAAY;IACxBf,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAAC8B,cAAc;IAC1BhB,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAAC+B,aAAa;IACzBjB,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,SAAS;EACdE,IAAI,eAAEP,OAAA,CAACf,YAAY;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAACgC,WAAW;IACvBlB,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACiC,cAAc;IAC1BnB,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACkC,WAAW;IACvBpB,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACmC,YAAY;IACxBrB,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,KAAK;EACVE,IAAI,eAAEP,OAAA,CAACd,YAAY;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAACoC,GAAG;IACftB,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,SAAS;EACdE,IAAI,eAAEP,OAAA,CAACb,gBAAgB;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC1BC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAACqC,OAAO;IACnBvB,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACsC,gBAAgB;IAC5BxB,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,QAAQ;EACbE,IAAI,eAAEP,OAAA,CAACZ,eAAe;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACzBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAACuC,KAAK;IACjBzB,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACwC,KAAK;IACjB1B,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAACyC,WAAW;IACvB3B,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAAC0C,aAAa;IACzB5B,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACEP,GAAG,EAAE,QAAQ;EACbE,IAAI,eAAEP,OAAA,CAACX,cAAc;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxBC,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,CACR;IACER,GAAG,EAAEP,MAAM,CAAC2C,MAAM;IAClB7B,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAAC4C,WAAW;IACvB9B,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAEP,MAAM,CAAC6C,gBAAgB;IAC5B/B,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,CACF;AAED,MAAMgC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMmF,QAAQ,GAAGjF,WAAW,CAAC,CAAC;EAC9B,MAAMkF,QAAQ,GAAGjF,WAAW,CAAC,CAAC;EAC9B,MAAMkF,QAAQ,GAAGxD,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEyD,IAAI;IAAEC;EAAY,CAAC,GAAGzD,cAAc,CAAC0D,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EACjE,MAAM;IACJC,KAAK,EAAE;MAAEC;IAAiB;EAC5B,CAAC,GAAG/E,KAAK,CAACgF,QAAQ,CAAC,CAAC;;EAEpB;EACA,MAAMC,eAAe,GAAGA,CAAC;IAAErD;EAAqB,CAAC,KAAK;IACpD2C,QAAQ,CAAC3C,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMsD,mBAAmB,GAAGA,CAAC;IAAEtD;EAAqB,CAAC,KAAK;IACxD,QAAQA,GAAG;MACT,KAAK,QAAQ;QACX6C,QAAQ,CAACtD,MAAM,CAAC,CAAC,CAAC;QAClBoD,QAAQ,CAAClD,MAAM,CAAC8D,KAAK,CAAC;QACtB;MACF,KAAK,SAAS;QACZ;QACA;MACF;QACE;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,MAAc,IAAK;IAC3CZ,QAAQ,CAACrD,UAAU,CAACiE,MAAM,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAiC,GAAG,CACxC;IACE1D,GAAG,EAAE,SAAS;IACdE,IAAI,eAAEP,OAAA,CAACV,YAAY;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEoD,IAAI,EAAE;EACR,CAAC,EACD;IACE3D,GAAG,EAAE,QAAQ;IACbE,IAAI,eAAEP,OAAA,CAACT,cAAc;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMqD,aAAiC,GAAG,CAAAd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,CAACC,GAAG,CAACC,IAAI,KAAK;IACjE/D,GAAG,EAAE+D,IAAI,CAACC,EAAE;IACZzD,KAAK,EAAEwD,IAAI,CAACE,IAAI;IAChBC,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAACO,IAAI,CAACC,EAAE;EACzC,CAAC,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,YAAY,GAAGxB,QAAQ,CAACyB,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC;IAChE,MAAMC,eAAe,GAAG,CACtB;MACEC,KAAK,EAAE;IACT,CAAC,CACF;IAEDN,YAAY,CAACO,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;MACvC,MAAMC,GAAG,GAAG,IAAIV,YAAY,CAACW,KAAK,CAAC,CAAC,EAAEF,KAAK,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE;MAC5D,MAAMC,QAAQ,GAAGC,iBAAiB,CAACnF,SAAS,EAAE+E,GAAG,CAAC;MAClD,IAAIG,QAAQ,EAAE;QACZR,eAAe,CAACU,IAAI,CAAC;UACnBT,KAAK,EAAEO,QAAQ,CAAC1E;QAClB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOkE,eAAe;EACxB,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGA,CAACE,KAAY,EAAEpF,GAAW,KAAU;IAC5D,KAAK,MAAMqF,IAAI,IAAID,KAAK,EAAE;MACxB,IAAIC,IAAI,CAACrF,GAAG,KAAKA,GAAG,EAAE;QACpB,OAAOqF,IAAI;MACb;MACA,IAAIA,IAAI,CAAC7E,QAAQ,EAAE;QACjB,MAAM8E,KAAK,GAAGJ,iBAAiB,CAACG,IAAI,CAAC7E,QAAQ,EAAER,GAAG,CAAC;QACnD,IAAIsF,KAAK,EAAE,OAAOA,KAAK;MACzB;IACF;IACA,OAAO,IAAI;EACb,CAAC;EAED,oBACE3F,OAAA,CAAC/B,MAAM;IAAC2H,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAhF,QAAA,gBACpCb,OAAA,CAACE,KAAK;MAAC4F,OAAO,EAAE,IAAK;MAACC,WAAW;MAACjD,SAAS,EAAEA,SAAU;MAAAjC,QAAA,gBACrDb,OAAA;QAAK4F,KAAK,EAAE;UACVI,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,0BAA0B;UACtCC,YAAY,EAAE,CAAC;UACfC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE,OAAO;UACdC,UAAU,EAAE;QACd,CAAE;QAAA3F,QAAA,EACCiC,SAAS,GAAG,KAAK,GAAG;MAAY;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNX,OAAA,CAAC9B,IAAI;QACHO,KAAK,EAAC,MAAM;QACZgI,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAACzD,QAAQ,CAACyB,QAAQ,CAAE;QAClCe,KAAK,EAAErF,SAAU;QACjBmE,OAAO,EAAEb;MAAgB;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERX,OAAA,CAAC/B,MAAM;MAAA4C,QAAA,gBACLb,OAAA,CAACC,MAAM;QAAC2F,KAAK,EAAE;UACbe,OAAO,EAAE,QAAQ;UACjBT,UAAU,EAAE1C,gBAAgB;UAC5B4C,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAzF,QAAA,gBACAb,OAAA,CAAC3B,MAAM;UACL2F,IAAI,EAAC,MAAM;UACXzD,IAAI,EAAEuC,SAAS,gBAAG9C,OAAA,CAACrB,kBAAkB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGX,OAAA,CAACtB,gBAAgB;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChE4D,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,CAACD,SAAS,CAAE;UACxC8C,KAAK,EAAE;YACLgB,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,EAAE;YACTb,MAAM,EAAE;UACV;QAAE;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFX,OAAA,CAAC1B,KAAK;UAACwI,IAAI,EAAC,QAAQ;UAAAjG,QAAA,GAEjB,CAAAsC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,KAAIf,IAAI,CAACe,KAAK,CAAC6C,MAAM,GAAG,CAAC,iBACnC/G,OAAA,CAAC5B,QAAQ;YACP4I,IAAI,EAAE;cAAEvB,KAAK,EAAExB;YAAc,CAAE;YAC/BgD,SAAS,EAAC,aAAa;YAAApG,QAAA,eAEvBb,OAAA,CAAC3B,MAAM;cAAC2F,IAAI,EAAC,MAAM;cAACzD,IAAI,eAAEP,OAAA,CAACP,gBAAgB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAE,QAAA,EAC5CuC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkB;YAAI;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACX,eAGDX,OAAA,CAACzB,KAAK;YAAC2I,KAAK,EAAE,CAAE;YAAArG,QAAA,eACdb,OAAA,CAAC3B,MAAM;cAAC2F,IAAI,EAAC,MAAM;cAACzD,IAAI,eAAEP,OAAA,CAACR,YAAY;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAGRX,OAAA,CAAC5B,QAAQ;YACP4I,IAAI,EAAE;cAAEvB,KAAK,EAAE1B,aAAa;cAAEQ,OAAO,EAAEZ;YAAoB,CAAE;YAC7DsD,SAAS,EAAC,aAAa;YAAApG,QAAA,eAEvBb,OAAA,CAAC1B,KAAK;cAACsH,KAAK,EAAE;gBAAEuB,MAAM,EAAE;cAAU,CAAE;cAAAtG,QAAA,gBAClCb,OAAA,CAAC7B,MAAM;gBAACoC,IAAI,eAAEP,OAAA,CAACV,YAAY;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACyG,GAAG,EAAEjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE;cAAO;gBAAA7G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDX,OAAA;gBAAAa,QAAA,EAAOsC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB;cAAI;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETX,OAAA,CAACG,OAAO;QAACyF,KAAK,EAAE;UAAEK,MAAM,EAAE;QAAO,CAAE;QAAApF,QAAA,gBACjCb,OAAA,CAACxB,UAAU;UACToH,KAAK,EAAE;YAAE0B,YAAY,EAAE;UAAG,CAAE;UAC5B7B,KAAK,EAAEjB,kBAAkB,CAAC;QAAE;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFX,OAAA;UACE4F,KAAK,EAAE;YACLe,OAAO,EAAE,EAAE;YACXd,SAAS,EAAE,GAAG;YACdK,UAAU,EAAE1C,gBAAgB;YAC5B2C,YAAY,EAAE;UAChB,CAAE;UAAAtF,QAAA,eAEFb,OAAA,CAAClC,MAAM;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACkC,EAAA,CA9LID,UAAoB;EAAA,QAEP7E,WAAW,EACXC,WAAW,EACX0B,cAAc,EACDC,cAAc,EAGxClB,KAAK,CAACgF,QAAQ;AAAA;AAAA8D,EAAA,GARd3E,UAAoB;AAgM1B,eAAeA,UAAU;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}