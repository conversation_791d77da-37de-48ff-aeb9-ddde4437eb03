{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\bom\\\\OrderBOMListPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Table, Button, Space, Input, Select, Card, Tag, Popconfirm, message, Modal, Form, Typography, Row, Col, Tooltip, Dropdown, Statistic, Checkbox } from 'antd';\nimport * as XLSX from 'xlsx';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, CopyOutlined, LockOutlined, UnlockOutlined, MoreOutlined, ExportOutlined, ImportOutlined, DollarOutlined, CalendarOutlined } from '@ant-design/icons';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchOrderBOMs } from '../../store/slices/bomSlice';\nimport { ROUTES, ORDER_STATUS } from '../../constants';\nimport { formatDate, formatCurrency } from '../../utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Search\n} = Input;\nconst OrderBOMListPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const {\n    orderBOMs,\n    loading,\n    pagination\n  } = useAppSelector(state => state.bom);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [customerFilter, setCustomerFilter] = useState('');\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState('excel');\n  const [exportFields, setExportFields] = useState(['orderNumber', 'customerName', 'coreBOMVersion', 'status', 'totalCost', 'deliveryDate']);\n  const [copyModalVisible, setCopyModalVisible] = useState(false);\n  const [copyingOrder, setCopyingOrder] = useState(null);\n  const [copyForm] = Form.useForm();\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter, customerFilter]);\n  const loadData = () => {\n    dispatch(fetchOrderBOMs({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      keyword: searchKeyword\n    }));\n  };\n  const handleSearch = value => {\n    setSearchKeyword(value);\n  };\n  const handleStatusFilter = value => {\n    setStatusFilter(value);\n  };\n  const handleCustomerFilter = value => {\n    setCustomerFilter(value);\n  };\n  const handleCreate = () => {\n    navigate(ROUTES.ORDER_BOM_CREATE);\n  };\n  const handleDerive = () => {\n    // 显示核心BOM选择对话框\n    Modal.info({\n      title: '选择核心BOM',\n      content: '请先选择要派生的核心BOM',\n      onOk: () => {\n        navigate('/bom/core'); // 跳转到核心BOM列表选择\n      }\n    });\n  };\n  const handleEdit = record => {\n    navigate(`/bom/order/edit/${record.id}`);\n  };\n  const handleView = record => {\n    navigate(ROUTES.ORDER_BOM_VIEW.replace(':id', record.id));\n  };\n  const handleDelete = async record => {\n    try {\n      // TODO: 实现删除API\n      message.success('删除成功');\n      loadData();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleFreeze = async record => {\n    try {\n      // TODO: 实现冻结API\n      message.success('冻结成功');\n      loadData();\n    } catch (error) {\n      message.error('冻结失败');\n    }\n  };\n  const handleExport = record => {\n    setExportModalVisible(true);\n  };\n  const executeExport = () => {\n    try {\n      // 准备导出数据\n      const exportData = mockData.map(order => {\n        const data = {};\n        if (exportFields.includes('orderNumber')) data['订单号'] = order.orderNumber;\n        if (exportFields.includes('customerName')) data['客户名称'] = order.customerName;\n        if (exportFields.includes('coreBOMVersion')) data['核心BOM'] = order.coreBOMVersion;\n        if (exportFields.includes('status')) data['状态'] = getStatusText(order.status);\n        if (exportFields.includes('totalCost')) data['总成本'] = order.totalCost;\n        if (exportFields.includes('estimatedMargin')) data['预估毛利'] = `${(order.estimatedMargin * 100).toFixed(1)}%`;\n        if (exportFields.includes('deliveryDate')) data['交期'] = formatDate(order.deliveryDate);\n        if (exportFields.includes('createdAt')) data['创建时间'] = formatDate(order.createdAt);\n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, '订单BOM列表');\n\n      // 下载文件\n      const fileName = `订单BOM列表_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n  const handleCopy = record => {\n    setCopyingOrder(record);\n    copyForm.setFieldsValue({\n      orderNumber: `${record.orderNumber}-COPY`,\n      customerName: record.customerName,\n      deliveryDate: null\n    });\n    setCopyModalVisible(true);\n  };\n  const executeCopy = async () => {\n    try {\n      const values = await copyForm.validateFields();\n      // TODO: 实现复制API调用\n      message.success('复制成功');\n      setCopyModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('复制失败');\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case ORDER_STATUS.DRAFT:\n        return 'default';\n      case ORDER_STATUS.CONFIRMED:\n        return 'processing';\n      case ORDER_STATUS.FROZEN:\n        return 'success';\n      case ORDER_STATUS.CANCELLED:\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case ORDER_STATUS.DRAFT:\n        return '草稿';\n      case ORDER_STATUS.CONFIRMED:\n        return '已确认';\n      case ORDER_STATUS.FROZEN:\n        return '已冻结';\n      case ORDER_STATUS.CANCELLED:\n        return '已取消';\n      default:\n        return status;\n    }\n  };\n  const getActionMenuItems = record => [{\n    key: 'copy',\n    icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 13\n    }, this),\n    label: '复制',\n    onClick: () => handleCopy(record)\n  }, {\n    key: 'export',\n    icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 13\n    }, this),\n    label: '导出',\n    onClick: () => handleExport(record)\n  }, {\n    key: 'freeze',\n    icon: record.status === ORDER_STATUS.FROZEN ? /*#__PURE__*/_jsxDEV(UnlockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 53\n    }, this) : /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 74\n    }, this),\n    label: record.status === ORDER_STATUS.FROZEN ? '解冻' : '冻结',\n    onClick: () => handleFreeze(record),\n    disabled: record.status === ORDER_STATUS.DRAFT\n  }];\n  const columns = [{\n    title: '订单号',\n    dataIndex: 'orderNumber',\n    key: 'orderNumber',\n    width: 120,\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      onClick: () => handleView(record),\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户名称',\n    dataIndex: 'customerName',\n    key: 'customerName',\n    ellipsis: true\n  }, {\n    title: '核心BOM',\n    dataIndex: 'coreBOMId',\n    key: 'coreBOMId',\n    width: 120,\n    render: (coreBOMId, record) => /*#__PURE__*/_jsxDEV(\"span\", {\n      children: record.coreBOMVersion\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '总成本',\n    dataIndex: 'totalCost',\n    key: 'totalCost',\n    width: 100,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '预估毛利',\n    dataIndex: 'estimatedMargin',\n    key: 'estimatedMargin',\n    width: 80,\n    render: margin => `${(margin * 100).toFixed(1)}%`\n  }, {\n    title: '交期',\n    dataIndex: 'deliveryDate',\n    key: 'deliveryDate',\n    width: 100,\n    render: date => formatDate(date)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 120,\n    render: date => formatDate(date)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record),\n          disabled: record.status === ORDER_STATUS.FROZEN\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8BA2\\u5355BOM\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 23\n            }, this),\n            disabled: record.status !== ORDER_STATUS.DRAFT\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n        menu: {\n          items: getActionMenuItems(record)\n        },\n        trigger: ['click'],\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(MoreOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 模拟数据\n  const mockData = [{\n    id: '1',\n    orderNumber: 'ORD-2024-001',\n    customerName: '华为技术有限公司',\n    customerConfig: {\n      frequency: '5G',\n      power: 100,\n      antenna_type: 'directional'\n    },\n    coreBOMId: '1',\n    coreBOMVersion: 'ANT-5G-001 V1.0',\n    items: [],\n    status: 'CONFIRMED',\n    totalCost: 125000,\n    estimatedMargin: 0.25,\n    deliveryDate: '2024-02-15',\n    createdBy: 'sales_pmc',\n    createdAt: '2024-01-15T00:00:00Z',\n    confirmedAt: '2024-01-16T00:00:00Z'\n  }, {\n    id: '2',\n    orderNumber: 'ORD-2024-002',\n    customerName: '中兴通讯股份有限公司',\n    customerConfig: {\n      frequency: '4G',\n      power: 80,\n      antenna_type: 'omnidirectional'\n    },\n    coreBOMId: '2',\n    coreBOMVersion: 'ANT-4G-002 V2.1',\n    items: [],\n    status: 'FROZEN',\n    totalCost: 98000,\n    estimatedMargin: 0.30,\n    deliveryDate: '2024-02-20',\n    createdBy: 'sales_pmc',\n    createdAt: '2024-01-16T00:00:00Z',\n    confirmedAt: '2024-01-17T00:00:00Z',\n    frozenAt: '2024-01-18T00:00:00Z'\n  }, {\n    id: '3',\n    orderNumber: 'ORD-2024-003',\n    customerName: '大唐移动通信设备有限公司',\n    customerConfig: {\n      frequency: '5G',\n      power: 120,\n      antenna_type: 'smart'\n    },\n    coreBOMId: '1',\n    coreBOMVersion: 'ANT-5G-001 V1.0',\n    items: [],\n    status: 'DRAFT',\n    totalCost: 156000,\n    estimatedMargin: 0.22,\n    deliveryDate: '2024-03-01',\n    createdBy: 'sales_pmc',\n    createdAt: '2024-01-17T00:00:00Z'\n  }];\n\n  // 统计数据\n  const stats = {\n    total: mockData.length,\n    draft: mockData.filter(item => item.status === ORDER_STATUS.DRAFT).length,\n    confirmed: mockData.filter(item => item.status === ORDER_STATUS.CONFIRMED).length,\n    frozen: mockData.filter(item => item.status === ORDER_STATUS.FROZEN).length,\n    totalValue: mockData.reduce((sum, item) => sum + item.totalCost, 0)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA2\\u5355\\u6570\",\n            value: stats.total,\n            prefix: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8349\\u7A3F\",\n            value: stats.draft,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u786E\\u8BA4\",\n            value: stats.confirmed,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EF7\\u503C\",\n            value: stats.totalValue,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 23\n            }, this),\n            formatter: value => formatCurrency(Number(value)),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u8BA2\\u5355BOM\\u7BA1\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ImportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 29\n              }, this),\n              onClick: () => handleExport(),\n              children: \"\\u6279\\u91CF\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 44\n              }, this),\n              onClick: handleDerive,\n              children: \"\\u6D3E\\u751F\\u8BA2\\u5355BOM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 29\n              }, this),\n              onClick: handleCreate,\n              children: \"\\u624B\\u52A8\\u521B\\u5EFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Search, {\n            placeholder: \"\\u641C\\u7D22\\u8BA2\\u5355\\u53F7\\u6216\\u5BA2\\u6237\\u540D\\u79F0\",\n            allowClear: true,\n            onSearch: handleSearch,\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u72B6\\u6001\\u7B5B\\u9009\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            onChange: handleStatusFilter,\n            options: [{\n              label: '草稿',\n              value: ORDER_STATUS.DRAFT\n            }, {\n              label: '已确认',\n              value: ORDER_STATUS.CONFIRMED\n            }, {\n              label: '已冻结',\n              value: ORDER_STATUS.FROZEN\n            }, {\n              label: '已取消',\n              value: ORDER_STATUS.CANCELLED\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u5BA2\\u6237\\u7B5B\\u9009\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            onChange: handleCustomerFilter,\n            options: [{\n              label: '华为技术',\n              value: '华为技术有限公司'\n            }, {\n              label: '中兴通讯',\n              value: '中兴通讯股份有限公司'\n            }, {\n              label: '大唐移动',\n              value: '大唐移动通信设备有限公司'\n            }, {\n              label: '爱立信',\n              value: '爱立信（中国）通信有限公司'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: mockData,\n        loading: loading,\n        rowKey: \"id\",\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          current: pagination.current,\n          pageSize: pagination.pageSize,\n          total: pagination.total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5BFC\\u51FA\\u8BA2\\u5355BOM\\u6570\\u636E\",\n      open: exportModalVisible,\n      onOk: executeExport,\n      onCancel: () => setExportModalVisible(false),\n      okText: \"\\u5BFC\\u51FA\",\n      cancelText: \"\\u53D6\\u6D88\",\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n            strong: true,\n            children: \"\\u5BFC\\u51FA\\u683C\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: exportFormat,\n            onChange: setExportFormat,\n            style: {\n              width: 120,\n              marginLeft: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"excel\",\n              children: \"Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"csv\",\n              children: \"CSV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Typography.Text, {\n            strong: true,\n            children: \"\\u5BFC\\u51FA\\u5B57\\u6BB5\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: exportFields,\n            onChange: setExportFields,\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"orderNumber\",\n                  children: \"\\u8BA2\\u5355\\u53F7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"customerName\",\n                  children: \"\\u5BA2\\u6237\\u540D\\u79F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"coreBOMVersion\",\n                  children: \"\\u6838\\u5FC3BOM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"status\",\n                  children: \"\\u72B6\\u6001\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"totalCost\",\n                  children: \"\\u603B\\u6210\\u672C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"estimatedMargin\",\n                  children: \"\\u9884\\u4F30\\u6BDB\\u5229\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"deliveryDate\",\n                  children: \"\\u4EA4\\u671F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"createdAt\",\n                  children: \"\\u521B\\u5EFA\\u65F6\\u95F4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u590D\\u5236\\u8BA2\\u5355BOM\",\n      open: copyModalVisible,\n      onOk: executeCopy,\n      onCancel: () => setCopyModalVisible(false),\n      okText: \"\\u786E\\u8BA4\\u590D\\u5236\",\n      cancelText: \"\\u53D6\\u6D88\",\n      width: 500,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: copyForm,\n        layout: \"vertical\",\n        initialValues: {\n          orderNumber: '',\n          customerName: '',\n          deliveryDate: null\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u65B0\\u8BA2\\u5355\\u53F7\",\n          name: \"orderNumber\",\n          rules: [{\n            required: true,\n            message: '请输入订单号'\n          }, {\n            pattern: /^[A-Z0-9-]+$/,\n            message: '订单号只能包含大写字母、数字和连字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u7684\\u8BA2\\u5355\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5BA2\\u6237\\u540D\\u79F0\",\n          name: \"customerName\",\n          rules: [{\n            required: true,\n            message: '请输入客户名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u4EA4\\u671F\",\n          name: \"deliveryDate\",\n          rules: [{\n            required: true,\n            message: '请选择交期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n          type: \"secondary\",\n          children: \"\\u6CE8\\u610F\\uFF1A\\u590D\\u5236\\u5C06\\u521B\\u5EFA\\u4E00\\u4E2A\\u65B0\\u7684\\u8BA2\\u5355BOM\\uFF0C\\u5305\\u542B\\u539F\\u8BA2\\u5355\\u7684\\u6240\\u6709\\u914D\\u7F6E\\u548C\\u7269\\u6599\\u6E05\\u5355\\uFF0C\\u4F46\\u72B6\\u6001\\u5C06\\u91CD\\u7F6E\\u4E3A\\u8349\\u7A3F\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 412,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderBOMListPage, \"BHJvYqokbNmDD91ANvGA6yK5ZDs=\", false, function () {\n  return [useNavigate, useAppDispatch, useAppSelector, Form.useForm];\n});\n_c = OrderBOMListPage;\nexport default OrderBOMListPage;\nvar _c;\n$RefreshReg$(_c, \"OrderBOMListPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "Popconfirm", "message", "Modal", "Form", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Statistic", "Checkbox", "XLSX", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "LockOutlined", "UnlockOutlined", "MoreOutlined", "ExportOutlined", "ImportOutlined", "DollarOutlined", "CalendarOutlined", "useAppDispatch", "useAppSelector", "fetchOrderBOMs", "ROUTES", "ORDER_STATUS", "formatDate", "formatCurrency", "jsxDEV", "_jsxDEV", "Title", "Search", "OrderBOMListPage", "_s", "navigate", "dispatch", "orderBOMs", "loading", "pagination", "state", "bom", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "customerFilter", "setCustomerFilter", "exportModalVisible", "setExportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "copyModalVisible", "setCopyModalVisible", "copyingOrder", "setCopyingOrder", "copyForm", "useForm", "loadData", "current", "pageSize", "page", "keyword", "handleSearch", "value", "handleStatusFilter", "handleCustomerFilter", "handleCreate", "ORDER_BOM_CREATE", "handleDerive", "info", "title", "content", "onOk", "handleEdit", "record", "id", "handleView", "ORDER_BOM_VIEW", "replace", "handleDelete", "success", "error", "handleFreeze", "handleExport", "executeExport", "exportData", "mockData", "map", "order", "data", "includes", "orderNumber", "customerName", "coreBOMVersion", "getStatusText", "status", "totalCost", "<PERSON><PERSON><PERSON><PERSON>", "toFixed", "deliveryDate", "createdAt", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "fileName", "Date", "toISOString", "split", "writeFile", "handleCopy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "executeCopy", "values", "validateFields", "getStatusColor", "DRAFT", "CONFIRMED", "FROZEN", "CANCELLED", "getActionMenuItems", "key", "icon", "_jsxFileName", "lineNumber", "columnNumber", "label", "onClick", "disabled", "columns", "dataIndex", "width", "render", "text", "type", "children", "ellipsis", "coreBOMId", "color", "cost", "margin", "date", "fixed", "_", "size", "onConfirm", "okText", "cancelText", "danger", "menu", "items", "trigger", "customerConfig", "frequency", "power", "antenna_type", "created<PERSON>y", "confirmedAt", "frozenAt", "stats", "total", "length", "draft", "filter", "item", "confirmed", "frozen", "totalValue", "reduce", "sum", "gutter", "style", "marginBottom", "xs", "sm", "prefix", "valueStyle", "formatter", "Number", "justify", "align", "level", "md", "placeholder", "allowClear", "onSearch", "onChange", "options", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "direction", "Text", "strong", "marginLeft", "Option", "Group", "marginTop", "span", "form", "layout", "initialValues", "<PERSON><PERSON>", "name", "rules", "required", "pattern", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/OrderBOMListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  Popconfirm,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Progress,\n  Statistic,\n  Checkbox,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  LockOutlined,\n  UnlockOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  DollarOutlined,\n  CalendarOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchOrderBOMs } from '../../store/slices/bomSlice';\nimport { OrderBOM } from '../../types';\nimport { ROUTES, ORDER_STATUS } from '../../constants';\nimport { formatDate, formatCurrency } from '../../utils';\n\nconst { Title } = Typography;\nconst { Search } = Input;\n\nconst OrderBOMListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const { orderBOMs, loading, pagination } = useAppSelector(state => state.bom);\n\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [customerFilter, setCustomerFilter] = useState<string>('');\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['orderNumber', 'customerName', 'coreBOMVersion', 'status', 'totalCost', 'deliveryDate']);\n  const [copyModalVisible, setCopyModalVisible] = useState(false);\n  const [copyingOrder, setCopyingOrder] = useState<OrderBOM | null>(null);\n  const [copyForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter, customerFilter]);\n\n  const loadData = () => {\n    dispatch(fetchOrderBOMs({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      keyword: searchKeyword,\n    }));\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchKeyword(value);\n  };\n\n  const handleStatusFilter = (value: string) => {\n    setStatusFilter(value);\n  };\n\n  const handleCustomerFilter = (value: string) => {\n    setCustomerFilter(value);\n  };\n\n  const handleCreate = () => {\n    navigate(ROUTES.ORDER_BOM_CREATE);\n  };\n\n  const handleDerive = () => {\n    // 显示核心BOM选择对话框\n    Modal.info({\n      title: '选择核心BOM',\n      content: '请先选择要派生的核心BOM',\n      onOk: () => {\n        navigate('/bom/core'); // 跳转到核心BOM列表选择\n      },\n    });\n  };\n\n  const handleEdit = (record: OrderBOM) => {\n    navigate(`/bom/order/edit/${record.id}`);\n  };\n\n  const handleView = (record: OrderBOM) => {\n    navigate(ROUTES.ORDER_BOM_VIEW.replace(':id', record.id));\n  };\n\n  const handleDelete = async (record: OrderBOM) => {\n    try {\n      // TODO: 实现删除API\n      message.success('删除成功');\n      loadData();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleFreeze = async (record: OrderBOM) => {\n    try {\n      // TODO: 实现冻结API\n      message.success('冻结成功');\n      loadData();\n    } catch (error) {\n      message.error('冻结失败');\n    }\n  };\n\n  const handleExport = (record?: OrderBOM) => {\n    setExportModalVisible(true);\n  };\n\n  const executeExport = () => {\n    try {\n      // 准备导出数据\n      const exportData = mockData.map(order => {\n        const data: any = {};\n        \n        if (exportFields.includes('orderNumber')) data['订单号'] = order.orderNumber;\n        if (exportFields.includes('customerName')) data['客户名称'] = order.customerName;\n        if (exportFields.includes('coreBOMVersion')) data['核心BOM'] = order.coreBOMVersion;\n        if (exportFields.includes('status')) data['状态'] = getStatusText(order.status);\n        if (exportFields.includes('totalCost')) data['总成本'] = order.totalCost;\n        if (exportFields.includes('estimatedMargin')) data['预估毛利'] = `${(order.estimatedMargin * 100).toFixed(1)}%`;\n        if (exportFields.includes('deliveryDate')) data['交期'] = formatDate(order.deliveryDate);\n        if (exportFields.includes('createdAt')) data['创建时间'] = formatDate(order.createdAt);\n        \n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, '订单BOM列表');\n\n      // 下载文件\n      const fileName = `订单BOM列表_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      \n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  const handleCopy = (record: OrderBOM) => {\n    setCopyingOrder(record);\n    copyForm.setFieldsValue({\n      orderNumber: `${record.orderNumber}-COPY`,\n      customerName: record.customerName,\n      deliveryDate: null,\n    });\n    setCopyModalVisible(true);\n  };\n\n  const executeCopy = async () => {\n    try {\n      const values = await copyForm.validateFields();\n      // TODO: 实现复制API调用\n      message.success('复制成功');\n      setCopyModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('复制失败');\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case ORDER_STATUS.DRAFT: return 'default';\n      case ORDER_STATUS.CONFIRMED: return 'processing';\n      case ORDER_STATUS.FROZEN: return 'success';\n      case ORDER_STATUS.CANCELLED: return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case ORDER_STATUS.DRAFT: return '草稿';\n      case ORDER_STATUS.CONFIRMED: return '已确认';\n      case ORDER_STATUS.FROZEN: return '已冻结';\n      case ORDER_STATUS.CANCELLED: return '已取消';\n      default: return status;\n    }\n  };\n\n  const getActionMenuItems = (record: OrderBOM): MenuProps['items'] => [\n    {\n      key: 'copy',\n      icon: <CopyOutlined />,\n      label: '复制',\n      onClick: () => handleCopy(record),\n    },\n    {\n      key: 'export',\n      icon: <ExportOutlined />,\n      label: '导出',\n      onClick: () => handleExport(record),\n    },\n    {\n      key: 'freeze',\n      icon: record.status === ORDER_STATUS.FROZEN ? <UnlockOutlined /> : <LockOutlined />,\n      label: record.status === ORDER_STATUS.FROZEN ? '解冻' : '冻结',\n      onClick: () => handleFreeze(record),\n      disabled: record.status === ORDER_STATUS.DRAFT,\n    },\n  ];\n\n  const columns = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNumber',\n      key: 'orderNumber',\n      width: 120,\n      render: (text: string, record: OrderBOM) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: '客户名称',\n      dataIndex: 'customerName',\n      key: 'customerName',\n      ellipsis: true,\n    },\n    {\n      title: '核心BOM',\n      dataIndex: 'coreBOMId',\n      key: 'coreBOMId',\n      width: 120,\n      render: (coreBOMId: string, record: OrderBOM) => (\n        <span>{record.coreBOMVersion}</span>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '总成本',\n      dataIndex: 'totalCost',\n      key: 'totalCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '预估毛利',\n      dataIndex: 'estimatedMargin',\n      key: 'estimatedMargin',\n      width: 80,\n      render: (margin: number) => `${(margin * 100).toFixed(1)}%`,\n    },\n    {\n      title: '交期',\n      dataIndex: 'deliveryDate',\n      key: 'deliveryDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: OrderBOM) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n              disabled={record.status === ORDER_STATUS.FROZEN}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定要删除这个订单BOM吗？\"\n            onConfirm={() => handleDelete(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n                disabled={record.status !== ORDER_STATUS.DRAFT}\n              />\n            </Tooltip>\n          </Popconfirm>\n          <Dropdown\n            menu={{ items: getActionMenuItems(record) }}\n            trigger={['click']}\n          >\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  // 模拟数据\n  const mockData: OrderBOM[] = [\n    {\n      id: '1',\n      orderNumber: 'ORD-2024-001',\n      customerName: '华为技术有限公司',\n      customerConfig: { frequency: '5G', power: 100, antenna_type: 'directional' },\n      coreBOMId: '1',\n      coreBOMVersion: 'ANT-5G-001 V1.0',\n      items: [],\n      status: 'CONFIRMED',\n      totalCost: 125000,\n      estimatedMargin: 0.25,\n      deliveryDate: '2024-02-15',\n      createdBy: 'sales_pmc',\n      createdAt: '2024-01-15T00:00:00Z',\n      confirmedAt: '2024-01-16T00:00:00Z',\n    },\n    {\n      id: '2',\n      orderNumber: 'ORD-2024-002',\n      customerName: '中兴通讯股份有限公司',\n      customerConfig: { frequency: '4G', power: 80, antenna_type: 'omnidirectional' },\n      coreBOMId: '2',\n      coreBOMVersion: 'ANT-4G-002 V2.1',\n      items: [],\n      status: 'FROZEN',\n      totalCost: 98000,\n      estimatedMargin: 0.30,\n      deliveryDate: '2024-02-20',\n      createdBy: 'sales_pmc',\n      createdAt: '2024-01-16T00:00:00Z',\n      confirmedAt: '2024-01-17T00:00:00Z',\n      frozenAt: '2024-01-18T00:00:00Z',\n    },\n    {\n      id: '3',\n      orderNumber: 'ORD-2024-003',\n      customerName: '大唐移动通信设备有限公司',\n      customerConfig: { frequency: '5G', power: 120, antenna_type: 'smart' },\n      coreBOMId: '1',\n      coreBOMVersion: 'ANT-5G-001 V1.0',\n      items: [],\n      status: 'DRAFT',\n      totalCost: 156000,\n      estimatedMargin: 0.22,\n      deliveryDate: '2024-03-01',\n      createdBy: 'sales_pmc',\n      createdAt: '2024-01-17T00:00:00Z',\n    },\n  ];\n\n  // 统计数据\n  const stats = {\n    total: mockData.length,\n    draft: mockData.filter(item => item.status === ORDER_STATUS.DRAFT).length,\n    confirmed: mockData.filter(item => item.status === ORDER_STATUS.CONFIRMED).length,\n    frozen: mockData.filter(item => item.status === ORDER_STATUS.FROZEN).length,\n    totalValue: mockData.reduce((sum, item) => sum + item.totalCost, 0),\n  };\n\n  return (\n    <div>\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={stats.total}\n              prefix={<CalendarOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"草稿\"\n              value={stats.draft}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"已确认\"\n              value={stats.confirmed}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总价值\"\n              value={stats.totalValue}\n              prefix={<DollarOutlined />}\n              formatter={(value) => formatCurrency(Number(value))}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              订单BOM管理\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<ImportOutlined />}>\n                导入\n              </Button>\n              <Button icon={<ExportOutlined />} onClick={() => handleExport()}>\n                批量导出\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleDerive}>\n                派生订单BOM\n              </Button>\n              <Button icon={<PlusOutlined />} onClick={handleCreate}>\n                手动创建\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n          <Col xs={24} sm={8} md={6}>\n            <Search\n              placeholder=\"搜索订单号或客户名称\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={24} sm={8} md={6}>\n            <Select\n              placeholder=\"状态筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              onChange={handleStatusFilter}\n              options={[\n                { label: '草稿', value: ORDER_STATUS.DRAFT },\n                { label: '已确认', value: ORDER_STATUS.CONFIRMED },\n                { label: '已冻结', value: ORDER_STATUS.FROZEN },\n                { label: '已取消', value: ORDER_STATUS.CANCELLED },\n              ]}\n            />\n          </Col>\n          <Col xs={24} sm={8} md={6}>\n            <Select\n              placeholder=\"客户筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              onChange={handleCustomerFilter}\n              options={[\n                { label: '华为技术', value: '华为技术有限公司' },\n                { label: '中兴通讯', value: '中兴通讯股份有限公司' },\n                { label: '大唐移动', value: '大唐移动通信设备有限公司' },\n                { label: '爱立信', value: '爱立信（中国）通信有限公司' },\n              ]}\n            />\n          </Col>\n        </Row>\n\n        <Table\n          columns={columns}\n          dataSource={mockData}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 导出模态框 */}\n      <Modal\n        title=\"导出订单BOM数据\"\n        open={exportModalVisible}\n        onOk={executeExport}\n        onCancel={() => setExportModalVisible(false)}\n        okText=\"导出\"\n        cancelText=\"取消\"\n        width={600}\n      >\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <Typography.Text strong>导出格式：</Typography.Text>\n            <Select\n              value={exportFormat}\n              onChange={setExportFormat}\n              style={{ width: 120, marginLeft: 8 }}\n            >\n              <Select.Option value=\"excel\">Excel</Select.Option>\n              <Select.Option value=\"csv\">CSV</Select.Option>\n            </Select>\n          </div>\n          \n          <div>\n            <Typography.Text strong>导出字段：</Typography.Text>\n            <Checkbox.Group\n              value={exportFields}\n              onChange={setExportFields}\n              style={{ marginTop: 8 }}\n            >\n              <Row>\n                <Col span={12}>\n                  <Checkbox value=\"orderNumber\">订单号</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"customerName\">客户名称</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"coreBOMVersion\">核心BOM</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"status\">状态</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"totalCost\">总成本</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"estimatedMargin\">预估毛利</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"deliveryDate\">交期</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"createdAt\">创建时间</Checkbox>\n                </Col>\n              </Row>\n            </Checkbox.Group>\n          </div>\n        </Space>\n      </Modal>\n\n      {/* 复制模态框 */}\n      <Modal\n        title=\"复制订单BOM\"\n        open={copyModalVisible}\n        onOk={executeCopy}\n        onCancel={() => setCopyModalVisible(false)}\n        okText=\"确认复制\"\n        cancelText=\"取消\"\n        width={500}\n      >\n        <Form\n          form={copyForm}\n          layout=\"vertical\"\n          initialValues={{\n            orderNumber: '',\n            customerName: '',\n            deliveryDate: null,\n          }}\n        >\n          <Form.Item\n            label=\"新订单号\"\n            name=\"orderNumber\"\n            rules={[\n              { required: true, message: '请输入订单号' },\n              { pattern: /^[A-Z0-9-]+$/, message: '订单号只能包含大写字母、数字和连字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入新的订单号\" />\n          </Form.Item>\n          \n          <Form.Item\n            label=\"客户名称\"\n            name=\"customerName\"\n            rules={[{ required: true, message: '请输入客户名称' }]}\n          >\n            <Input placeholder=\"请输入客户名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            label=\"交期\"\n            name=\"deliveryDate\"\n            rules={[{ required: true, message: '请选择交期' }]}\n          >\n            <Input type=\"date\" />\n          </Form.Item>\n          \n          <Typography.Text type=\"secondary\">\n            注意：复制将创建一个新的订单BOM，包含原订单的所有配置和物料清单，但状态将重置为草稿。\n          </Typography.Text>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderBOMListPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,QAAQ,EAGRC,SAAS,EACTC,QAAQ,QACH,MAAM;AACb,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SACEC,YAAY,EAEZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,gBAAgB,QAEX,mBAAmB;AAE1B,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,cAAc,QAAQ,6BAA6B;AAE5D,SAASC,MAAM,EAAEC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,UAAU,EAAEC,cAAc,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAM;EAAEC;AAAM,CAAC,GAAG7B,UAAU;AAC5B,MAAM;EAAE8B;AAAO,CAAC,GAAGtC,KAAK;AAExB,MAAMuC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,QAAQ,GAAGd,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEe,SAAS;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGhB,cAAc,CAACiB,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EAE7E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC2D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAkB,OAAO,CAAC;EAC1E,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAW,CAAC,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;EACpJ,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAkB,IAAI,CAAC;EACvE,MAAM,CAACqE,QAAQ,CAAC,GAAGzD,IAAI,CAAC0D,OAAO,CAAC,CAAC;EAEjCvE,SAAS,CAAC,MAAM;IACdwE,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACrB,UAAU,CAACsB,OAAO,EAAEtB,UAAU,CAACuB,QAAQ,EAAEpB,aAAa,EAAEE,YAAY,EAAEE,cAAc,CAAC,CAAC;EAE1F,MAAMc,QAAQ,GAAGA,CAAA,KAAM;IACrBxB,QAAQ,CAACZ,cAAc,CAAC;MACtBuC,IAAI,EAAExB,UAAU,CAACsB,OAAO;MACxBC,QAAQ,EAAEvB,UAAU,CAACuB,QAAQ;MAC7BE,OAAO,EAAEtB;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuB,YAAY,GAAIC,KAAa,IAAK;IACtCvB,gBAAgB,CAACuB,KAAK,CAAC;EACzB,CAAC;EAED,MAAMC,kBAAkB,GAAID,KAAa,IAAK;IAC5CrB,eAAe,CAACqB,KAAK,CAAC;EACxB,CAAC;EAED,MAAME,oBAAoB,GAAIF,KAAa,IAAK;IAC9CnB,iBAAiB,CAACmB,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzBlC,QAAQ,CAACV,MAAM,CAAC6C,gBAAgB,CAAC;EACnC,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAvE,KAAK,CAACwE,IAAI,CAAC;MACTC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAEA,CAAA,KAAM;QACVxC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyC,UAAU,GAAIC,MAAgB,IAAK;IACvC1C,QAAQ,CAAC,mBAAmB0C,MAAM,CAACC,EAAE,EAAE,CAAC;EAC1C,CAAC;EAED,MAAMC,UAAU,GAAIF,MAAgB,IAAK;IACvC1C,QAAQ,CAACV,MAAM,CAACuD,cAAc,CAACC,OAAO,CAAC,KAAK,EAAEJ,MAAM,CAACC,EAAE,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOL,MAAgB,IAAK;IAC/C,IAAI;MACF;MACA9E,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC;MACvBvB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdrF,OAAO,CAACqF,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOR,MAAgB,IAAK;IAC/C,IAAI;MACF;MACA9E,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC;MACvBvB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdrF,OAAO,CAACqF,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAME,YAAY,GAAIT,MAAiB,IAAK;IAC1C5B,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMsC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI;MACF;MACA,MAAMC,UAAU,GAAGC,QAAQ,CAACC,GAAG,CAACC,KAAK,IAAI;QACvC,MAAMC,IAAS,GAAG,CAAC,CAAC;QAEpB,IAAIxC,YAAY,CAACyC,QAAQ,CAAC,aAAa,CAAC,EAAED,IAAI,CAAC,KAAK,CAAC,GAAGD,KAAK,CAACG,WAAW;QACzE,IAAI1C,YAAY,CAACyC,QAAQ,CAAC,cAAc,CAAC,EAAED,IAAI,CAAC,MAAM,CAAC,GAAGD,KAAK,CAACI,YAAY;QAC5E,IAAI3C,YAAY,CAACyC,QAAQ,CAAC,gBAAgB,CAAC,EAAED,IAAI,CAAC,OAAO,CAAC,GAAGD,KAAK,CAACK,cAAc;QACjF,IAAI5C,YAAY,CAACyC,QAAQ,CAAC,QAAQ,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,GAAGK,aAAa,CAACN,KAAK,CAACO,MAAM,CAAC;QAC7E,IAAI9C,YAAY,CAACyC,QAAQ,CAAC,WAAW,CAAC,EAAED,IAAI,CAAC,KAAK,CAAC,GAAGD,KAAK,CAACQ,SAAS;QACrE,IAAI/C,YAAY,CAACyC,QAAQ,CAAC,iBAAiB,CAAC,EAAED,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAACD,KAAK,CAACS,eAAe,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;QAC3G,IAAIjD,YAAY,CAACyC,QAAQ,CAAC,cAAc,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,GAAGjE,UAAU,CAACgE,KAAK,CAACW,YAAY,CAAC;QACtF,IAAIlD,YAAY,CAACyC,QAAQ,CAAC,WAAW,CAAC,EAAED,IAAI,CAAC,MAAM,CAAC,GAAGjE,UAAU,CAACgE,KAAK,CAACY,SAAS,CAAC;QAElF,OAAOX,IAAI;MACb,CAAC,CAAC;;MAEF;MACA,MAAMY,EAAE,GAAG/F,IAAI,CAACgG,KAAK,CAACC,aAAa,CAAClB,UAAU,CAAC;MAC/C,MAAMmB,EAAE,GAAGlG,IAAI,CAACgG,KAAK,CAACG,QAAQ,CAAC,CAAC;MAChCnG,IAAI,CAACgG,KAAK,CAACI,iBAAiB,CAACF,EAAE,EAAEH,EAAE,EAAE,SAAS,CAAC;;MAE/C;MACA,MAAMM,QAAQ,GAAG,WAAW,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI/D,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK,EAAE;MACjHzC,IAAI,CAACyG,SAAS,CAACP,EAAE,EAAEG,QAAQ,CAAC;MAE5B/G,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC;MACvBlC,qBAAqB,CAAC,KAAK,CAAC;IAC9B,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdrF,OAAO,CAACqF,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAM+B,UAAU,GAAItC,MAAgB,IAAK;IACvCpB,eAAe,CAACoB,MAAM,CAAC;IACvBnB,QAAQ,CAAC0D,cAAc,CAAC;MACtBtB,WAAW,EAAE,GAAGjB,MAAM,CAACiB,WAAW,OAAO;MACzCC,YAAY,EAAElB,MAAM,CAACkB,YAAY;MACjCO,YAAY,EAAE;IAChB,CAAC,CAAC;IACF/C,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM8D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM5D,QAAQ,CAAC6D,cAAc,CAAC,CAAC;MAC9C;MACAxH,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC;MACvB5B,mBAAmB,CAAC,KAAK,CAAC;MAC1BK,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdrF,OAAO,CAACqF,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMoC,cAAc,GAAItB,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAKxE,YAAY,CAAC+F,KAAK;QAAE,OAAO,SAAS;MACzC,KAAK/F,YAAY,CAACgG,SAAS;QAAE,OAAO,YAAY;MAChD,KAAKhG,YAAY,CAACiG,MAAM;QAAE,OAAO,SAAS;MAC1C,KAAKjG,YAAY,CAACkG,SAAS;QAAE,OAAO,OAAO;MAC3C;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM3B,aAAa,GAAIC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAKxE,YAAY,CAAC+F,KAAK;QAAE,OAAO,IAAI;MACpC,KAAK/F,YAAY,CAACgG,SAAS;QAAE,OAAO,KAAK;MACzC,KAAKhG,YAAY,CAACiG,MAAM;QAAE,OAAO,KAAK;MACtC,KAAKjG,YAAY,CAACkG,SAAS;QAAE,OAAO,KAAK;MACzC;QAAS,OAAO1B,MAAM;IACxB;EACF,CAAC;EAED,MAAM2B,kBAAkB,GAAIhD,MAAgB,IAAyB,CACnE;IACEiD,GAAG,EAAE,MAAM;IACXC,IAAI,eAAEjG,OAAA,CAAChB,YAAY;MAAAgG,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACtC,MAAM;EAClC,CAAC,EACD;IACEiD,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEjG,OAAA,CAACZ,cAAc;MAAA4F,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAACT,MAAM;EACpC,CAAC,EACD;IACEiD,GAAG,EAAE,QAAQ;IACbC,IAAI,EAAElD,MAAM,CAACqB,MAAM,KAAKxE,YAAY,CAACiG,MAAM,gBAAG7F,OAAA,CAACd,cAAc;MAAA8F,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGpG,OAAA,CAACf,YAAY;MAAA+F,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnFC,KAAK,EAAEtD,MAAM,CAACqB,MAAM,KAAKxE,YAAY,CAACiG,MAAM,GAAG,IAAI,GAAG,IAAI;IAC1DS,OAAO,EAAEA,CAAA,KAAM/C,YAAY,CAACR,MAAM,CAAC;IACnCwD,QAAQ,EAAExD,MAAM,CAACqB,MAAM,KAAKxE,YAAY,CAAC+F;EAC3C,CAAC,CACF;EAED,MAAMa,OAAO,GAAG,CACd;IACE7D,KAAK,EAAE,KAAK;IACZ8D,SAAS,EAAE,aAAa;IACxBT,GAAG,EAAE,aAAa;IAClBU,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,IAAY,EAAE7D,MAAgB,kBACrC/C,OAAA,CAACtC,MAAM;MAACmJ,IAAI,EAAC,MAAM;MAACP,OAAO,EAAEA,CAAA,KAAMrD,UAAU,CAACF,MAAM,CAAE;MAAA+D,QAAA,EACnDF;IAAI;MAAA5B,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAEZ,CAAC,EACD;IACEzD,KAAK,EAAE,MAAM;IACb8D,SAAS,EAAE,cAAc;IACzBT,GAAG,EAAE,cAAc;IACnBe,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpE,KAAK,EAAE,OAAO;IACd8D,SAAS,EAAE,WAAW;IACtBT,GAAG,EAAE,WAAW;IAChBU,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACK,SAAiB,EAAEjE,MAAgB,kBAC1C/C,OAAA;MAAA8G,QAAA,EAAO/D,MAAM,CAACmB;IAAc;MAAAc,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAEvC,CAAC,EACD;IACEzD,KAAK,EAAE,IAAI;IACX8D,SAAS,EAAE,QAAQ;IACnBT,GAAG,EAAE,QAAQ;IACbU,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGvC,MAAc,iBACrBpE,OAAA,CAACjC,GAAG;MAACkJ,KAAK,EAAEvB,cAAc,CAACtB,MAAM,CAAE;MAAA0C,QAAA,EAChC3C,aAAa,CAACC,MAAM;IAAC;MAAAY,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEzD,KAAK,EAAE,KAAK;IACZ8D,SAAS,EAAE,WAAW;IACtBT,GAAG,EAAE,WAAW;IAChBU,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGO,IAAY,IAAKpH,cAAc,CAACoH,IAAI;EAC/C,CAAC,EACD;IACEvE,KAAK,EAAE,MAAM;IACb8D,SAAS,EAAE,iBAAiB;IAC5BT,GAAG,EAAE,iBAAiB;IACtBU,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGQ,MAAc,IAAK,GAAG,CAACA,MAAM,GAAG,GAAG,EAAE5C,OAAO,CAAC,CAAC,CAAC;EAC1D,CAAC,EACD;IACE5B,KAAK,EAAE,IAAI;IACX8D,SAAS,EAAE,cAAc;IACzBT,GAAG,EAAE,cAAc;IACnBU,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGS,IAAY,IAAKvH,UAAU,CAACuH,IAAI;EAC3C,CAAC,EACD;IACEzE,KAAK,EAAE,MAAM;IACb8D,SAAS,EAAE,WAAW;IACtBT,GAAG,EAAE,WAAW;IAChBU,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGS,IAAY,IAAKvH,UAAU,CAACuH,IAAI;EAC3C,CAAC,EACD;IACEzE,KAAK,EAAE,IAAI;IACXqD,GAAG,EAAE,QAAQ;IACbU,KAAK,EAAE,GAAG;IACVW,KAAK,EAAE,OAAgB;IACvBV,MAAM,EAAEA,CAACW,CAAM,EAAEvE,MAAgB,kBAC/B/C,OAAA,CAACrC,KAAK;MAAC4J,IAAI,EAAC,OAAO;MAAAT,QAAA,gBACjB9G,OAAA,CAACzB,OAAO;QAACoE,KAAK,EAAC,cAAI;QAAAmE,QAAA,eACjB9G,OAAA,CAACtC,MAAM;UACLmJ,IAAI,EAAC,MAAM;UACXZ,IAAI,eAAEjG,OAAA,CAACjB,WAAW;YAAAiG,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBE,OAAO,EAAEA,CAAA,KAAMrD,UAAU,CAACF,MAAM;QAAE;UAAAiC,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpG,OAAA,CAACzB,OAAO;QAACoE,KAAK,EAAC,cAAI;QAAAmE,QAAA,eACjB9G,OAAA,CAACtC,MAAM;UACLmJ,IAAI,EAAC,MAAM;UACXZ,IAAI,eAAEjG,OAAA,CAACnB,YAAY;YAAAmG,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBE,OAAO,EAAEA,CAAA,KAAMxD,UAAU,CAACC,MAAM,CAAE;UAClCwD,QAAQ,EAAExD,MAAM,CAACqB,MAAM,KAAKxE,YAAY,CAACiG;QAAO;UAAAb,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpG,OAAA,CAAChC,UAAU;QACT2E,KAAK,EAAC,uEAAgB;QACtB6E,SAAS,EAAEA,CAAA,KAAMpE,YAAY,CAACL,MAAM,CAAE;QACtC0E,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAZ,QAAA,eAEf9G,OAAA,CAACzB,OAAO;UAACoE,KAAK,EAAC,cAAI;UAAAmE,QAAA,eACjB9G,OAAA,CAACtC,MAAM;YACLmJ,IAAI,EAAC,MAAM;YACXc,MAAM;YACN1B,IAAI,eAAEjG,OAAA,CAAClB,cAAc;cAAAkG,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBG,QAAQ,EAAExD,MAAM,CAACqB,MAAM,KAAKxE,YAAY,CAAC+F;UAAM;YAAAX,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACbpG,OAAA,CAACxB,QAAQ;QACPoJ,IAAI,EAAE;UAAEC,KAAK,EAAE9B,kBAAkB,CAAChD,MAAM;QAAE,CAAE;QAC5C+E,OAAO,EAAE,CAAC,OAAO,CAAE;QAAAhB,QAAA,eAEnB9G,OAAA,CAACtC,MAAM;UAACmJ,IAAI,EAAC,MAAM;UAACZ,IAAI,eAAEjG,OAAA,CAACb,YAAY;YAAA6F,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAApB,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAEX,CAAC,CACF;;EAED;EACA,MAAMzC,QAAoB,GAAG,CAC3B;IACEX,EAAE,EAAE,GAAG;IACPgB,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,UAAU;IACxB8D,cAAc,EAAE;MAAEC,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE,GAAG;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC5ElB,SAAS,EAAE,GAAG;IACd9C,cAAc,EAAE,iBAAiB;IACjC2D,KAAK,EAAE,EAAE;IACTzD,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE,MAAM;IACjBC,eAAe,EAAE,IAAI;IACrBE,YAAY,EAAE,YAAY;IAC1B2D,SAAS,EAAE,WAAW;IACtB1D,SAAS,EAAE,sBAAsB;IACjC2D,WAAW,EAAE;EACf,CAAC,EACD;IACEpF,EAAE,EAAE,GAAG;IACPgB,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,YAAY;IAC1B8D,cAAc,EAAE;MAAEC,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEC,YAAY,EAAE;IAAkB,CAAC;IAC/ElB,SAAS,EAAE,GAAG;IACd9C,cAAc,EAAE,iBAAiB;IACjC2D,KAAK,EAAE,EAAE;IACTzD,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,KAAK;IAChBC,eAAe,EAAE,IAAI;IACrBE,YAAY,EAAE,YAAY;IAC1B2D,SAAS,EAAE,WAAW;IACtB1D,SAAS,EAAE,sBAAsB;IACjC2D,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE;EACZ,CAAC,EACD;IACErF,EAAE,EAAE,GAAG;IACPgB,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,cAAc;IAC5B8D,cAAc,EAAE;MAAEC,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE,GAAG;MAAEC,YAAY,EAAE;IAAQ,CAAC;IACtElB,SAAS,EAAE,GAAG;IACd9C,cAAc,EAAE,iBAAiB;IACjC2D,KAAK,EAAE,EAAE;IACTzD,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,MAAM;IACjBC,eAAe,EAAE,IAAI;IACrBE,YAAY,EAAE,YAAY;IAC1B2D,SAAS,EAAE,WAAW;IACtB1D,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAM6D,KAAK,GAAG;IACZC,KAAK,EAAE5E,QAAQ,CAAC6E,MAAM;IACtBC,KAAK,EAAE9E,QAAQ,CAAC+E,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACvE,MAAM,KAAKxE,YAAY,CAAC+F,KAAK,CAAC,CAAC6C,MAAM;IACzEI,SAAS,EAAEjF,QAAQ,CAAC+E,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACvE,MAAM,KAAKxE,YAAY,CAACgG,SAAS,CAAC,CAAC4C,MAAM;IACjFK,MAAM,EAAElF,QAAQ,CAAC+E,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACvE,MAAM,KAAKxE,YAAY,CAACiG,MAAM,CAAC,CAAC2C,MAAM;IAC3EM,UAAU,EAAEnF,QAAQ,CAACoF,MAAM,CAAC,CAACC,GAAG,EAAEL,IAAI,KAAKK,GAAG,GAAGL,IAAI,CAACtE,SAAS,EAAE,CAAC;EACpE,CAAC;EAED,oBACErE,OAAA;IAAA8G,QAAA,gBAEE9G,OAAA,CAAC3B,GAAG;MAAC4K,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAArC,QAAA,gBACjD9G,OAAA,CAAC1B,GAAG;QAAC8K,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACjB9G,OAAA,CAAClC,IAAI;UAAAgJ,QAAA,eACH9G,OAAA,CAACvB,SAAS;YACRkE,KAAK,EAAC,0BAAM;YACZP,KAAK,EAAEkG,KAAK,CAACC,KAAM;YACnBe,MAAM,eAAEtJ,OAAA,CAACT,gBAAgB;cAAAyF,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BmD,UAAU,EAAE;cAAEtC,KAAK,EAAE;YAAU;UAAE;YAAAjC,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpG,OAAA,CAAC1B,GAAG;QAAC8K,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACjB9G,OAAA,CAAClC,IAAI;UAAAgJ,QAAA,eACH9G,OAAA,CAACvB,SAAS;YACRkE,KAAK,EAAC,cAAI;YACVP,KAAK,EAAEkG,KAAK,CAACG,KAAM;YACnBc,UAAU,EAAE;cAAEtC,KAAK,EAAE;YAAU;UAAE;YAAAjC,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpG,OAAA,CAAC1B,GAAG;QAAC8K,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACjB9G,OAAA,CAAClC,IAAI;UAAAgJ,QAAA,eACH9G,OAAA,CAACvB,SAAS;YACRkE,KAAK,EAAC,oBAAK;YACXP,KAAK,EAAEkG,KAAK,CAACM,SAAU;YACvBW,UAAU,EAAE;cAAEtC,KAAK,EAAE;YAAU;UAAE;YAAAjC,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpG,OAAA,CAAC1B,GAAG;QAAC8K,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvC,QAAA,eACjB9G,OAAA,CAAClC,IAAI;UAAAgJ,QAAA,eACH9G,OAAA,CAACvB,SAAS;YACRkE,KAAK,EAAC,oBAAK;YACXP,KAAK,EAAEkG,KAAK,CAACQ,UAAW;YACxBQ,MAAM,eAAEtJ,OAAA,CAACV,cAAc;cAAA0F,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BoD,SAAS,EAAGpH,KAAK,IAAKtC,cAAc,CAAC2J,MAAM,CAACrH,KAAK,CAAC,CAAE;YACpDmH,UAAU,EAAE;cAAEtC,KAAK,EAAE;YAAU;UAAE;YAAAjC,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAApB,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpG,OAAA,CAAClC,IAAI;MAAAgJ,QAAA,gBACH9G,OAAA,CAAC3B,GAAG;QAACqL,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACT,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAArC,QAAA,gBACtE9G,OAAA,CAAC1B,GAAG;UAAAwI,QAAA,eACF9G,OAAA,CAACC,KAAK;YAAC2J,KAAK,EAAE,CAAE;YAACV,KAAK,EAAE;cAAE/B,MAAM,EAAE;YAAE,CAAE;YAAAL,QAAA,EAAC;UAEvC;YAAA9B,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpG,OAAA,CAAC1B,GAAG;UAAAwI,QAAA,eACF9G,OAAA,CAACrC,KAAK;YAAAmJ,QAAA,gBACJ9G,OAAA,CAACtC,MAAM;cAACuI,IAAI,eAAEjG,OAAA,CAACX,cAAc;gBAAA2F,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAU,QAAA,EAAC;YAElC;cAAA9B,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpG,OAAA,CAACtC,MAAM;cAACuI,IAAI,eAAEjG,OAAA,CAACZ,cAAc;gBAAA4F,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACE,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAAC,CAAE;cAAAsD,QAAA,EAAC;YAEjE;cAAA9B,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpG,OAAA,CAACtC,MAAM;cAACmJ,IAAI,EAAC,SAAS;cAACZ,IAAI,eAAEjG,OAAA,CAACpB,YAAY;gBAAAoG,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACE,OAAO,EAAE7D,YAAa;cAAAqE,QAAA,EAAC;YAEtE;cAAA9B,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpG,OAAA,CAACtC,MAAM;cAACuI,IAAI,eAAEjG,OAAA,CAACpB,YAAY;gBAAAoG,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACE,OAAO,EAAE/D,YAAa;cAAAuE,QAAA,EAAC;YAEvD;cAAA9B,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAApB,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpG,OAAA,CAAC3B,GAAG;QAAC4K,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAArC,QAAA,gBACjD9G,OAAA,CAAC1B,GAAG;UAAC8K,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACQ,EAAE,EAAE,CAAE;UAAA/C,QAAA,eACxB9G,OAAA,CAACE,MAAM;YACL4J,WAAW,EAAC,8DAAY;YACxBC,UAAU;YACVC,QAAQ,EAAE7H,YAAa;YACvB+G,KAAK,EAAE;cAAExC,KAAK,EAAE;YAAO;UAAE;YAAA1B,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpG,OAAA,CAAC1B,GAAG;UAAC8K,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACQ,EAAE,EAAE,CAAE;UAAA/C,QAAA,eACxB9G,OAAA,CAACnC,MAAM;YACLiM,WAAW,EAAC,0BAAM;YAClBC,UAAU;YACVb,KAAK,EAAE;cAAExC,KAAK,EAAE;YAAO,CAAE;YACzBuD,QAAQ,EAAE5H,kBAAmB;YAC7B6H,OAAO,EAAE,CACP;cAAE7D,KAAK,EAAE,IAAI;cAAEjE,KAAK,EAAExC,YAAY,CAAC+F;YAAM,CAAC,EAC1C;cAAEU,KAAK,EAAE,KAAK;cAAEjE,KAAK,EAAExC,YAAY,CAACgG;YAAU,CAAC,EAC/C;cAAES,KAAK,EAAE,KAAK;cAAEjE,KAAK,EAAExC,YAAY,CAACiG;YAAO,CAAC,EAC5C;cAAEQ,KAAK,EAAE,KAAK;cAAEjE,KAAK,EAAExC,YAAY,CAACkG;YAAU,CAAC;UAC/C;YAAAd,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpG,OAAA,CAAC1B,GAAG;UAAC8K,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACQ,EAAE,EAAE,CAAE;UAAA/C,QAAA,eACxB9G,OAAA,CAACnC,MAAM;YACLiM,WAAW,EAAC,0BAAM;YAClBC,UAAU;YACVb,KAAK,EAAE;cAAExC,KAAK,EAAE;YAAO,CAAE;YACzBuD,QAAQ,EAAE3H,oBAAqB;YAC/B4H,OAAO,EAAE,CACP;cAAE7D,KAAK,EAAE,MAAM;cAAEjE,KAAK,EAAE;YAAW,CAAC,EACpC;cAAEiE,KAAK,EAAE,MAAM;cAAEjE,KAAK,EAAE;YAAa,CAAC,EACtC;cAAEiE,KAAK,EAAE,MAAM;cAAEjE,KAAK,EAAE;YAAe,CAAC,EACxC;cAAEiE,KAAK,EAAE,KAAK;cAAEjE,KAAK,EAAE;YAAgB,CAAC;UACxC;YAAA4C,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpG,OAAA,CAACvC,KAAK;QACJ+I,OAAO,EAAEA,OAAQ;QACjB2D,UAAU,EAAExG,QAAS;QACrBnD,OAAO,EAAEA,OAAQ;QACjB4J,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpB7J,UAAU,EAAE;UACVsB,OAAO,EAAEtB,UAAU,CAACsB,OAAO;UAC3BC,QAAQ,EAAEvB,UAAU,CAACuB,QAAQ;UAC7BuG,KAAK,EAAE9H,UAAU,CAAC8H,KAAK;UACvBgC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAClC,KAAK,EAAEmC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQnC,KAAK;QAC1C;MAAE;QAAAvD,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAApB,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPpG,OAAA,CAAC9B,KAAK;MACJyE,KAAK,EAAC,yCAAW;MACjBgI,IAAI,EAAEzJ,kBAAmB;MACzB2B,IAAI,EAAEY,aAAc;MACpBmH,QAAQ,EAAEA,CAAA,KAAMzJ,qBAAqB,CAAC,KAAK,CAAE;MAC7CsG,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MACfhB,KAAK,EAAE,GAAI;MAAAI,QAAA,eAEX9G,OAAA,CAACrC,KAAK;QAACkN,SAAS,EAAC,UAAU;QAAC3B,KAAK,EAAE;UAAExC,KAAK,EAAE;QAAO,CAAE;QAAAI,QAAA,gBACnD9G,OAAA;UAAA8G,QAAA,gBACE9G,OAAA,CAAC5B,UAAU,CAAC0M,IAAI;YAACC,MAAM;YAAAjE,QAAA,EAAC;UAAK;YAAA9B,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAC/CpG,OAAA,CAACnC,MAAM;YACLuE,KAAK,EAAEhB,YAAa;YACpB6I,QAAQ,EAAE5I,eAAgB;YAC1B6H,KAAK,EAAE;cAAExC,KAAK,EAAE,GAAG;cAAEsE,UAAU,EAAE;YAAE,CAAE;YAAAlE,QAAA,gBAErC9G,OAAA,CAACnC,MAAM,CAACoN,MAAM;cAAC7I,KAAK,EAAC,OAAO;cAAA0E,QAAA,EAAC;YAAK;cAAA9B,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDpG,OAAA,CAACnC,MAAM,CAACoN,MAAM;cAAC7I,KAAK,EAAC,KAAK;cAAA0E,QAAA,EAAC;YAAG;cAAA9B,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAApB,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENpG,OAAA;UAAA8G,QAAA,gBACE9G,OAAA,CAAC5B,UAAU,CAAC0M,IAAI;YAACC,MAAM;YAAAjE,QAAA,EAAC;UAAK;YAAA9B,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAC/CpG,OAAA,CAACtB,QAAQ,CAACwM,KAAK;YACb9I,KAAK,EAAEd,YAAa;YACpB2I,QAAQ,EAAE1I,eAAgB;YAC1B2H,KAAK,EAAE;cAAEiC,SAAS,EAAE;YAAE,CAAE;YAAArE,QAAA,eAExB9G,OAAA,CAAC3B,GAAG;cAAAyI,QAAA,gBACF9G,OAAA,CAAC1B,GAAG;gBAAC8M,IAAI,EAAE,EAAG;gBAAAtE,QAAA,eACZ9G,OAAA,CAACtB,QAAQ;kBAAC0D,KAAK,EAAC,aAAa;kBAAA0E,QAAA,EAAC;gBAAG;kBAAA9B,QAAA,EAAAkB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAApB,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNpG,OAAA,CAAC1B,GAAG;gBAAC8M,IAAI,EAAE,EAAG;gBAAAtE,QAAA,eACZ9G,OAAA,CAACtB,QAAQ;kBAAC0D,KAAK,EAAC,cAAc;kBAAA0E,QAAA,EAAC;gBAAI;kBAAA9B,QAAA,EAAAkB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAApB,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNpG,OAAA,CAAC1B,GAAG;gBAAC8M,IAAI,EAAE,EAAG;gBAAAtE,QAAA,eACZ9G,OAAA,CAACtB,QAAQ;kBAAC0D,KAAK,EAAC,gBAAgB;kBAAA0E,QAAA,EAAC;gBAAK;kBAAA9B,QAAA,EAAAkB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAApB,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNpG,OAAA,CAAC1B,GAAG;gBAAC8M,IAAI,EAAE,EAAG;gBAAAtE,QAAA,eACZ9G,OAAA,CAACtB,QAAQ;kBAAC0D,KAAK,EAAC,QAAQ;kBAAA0E,QAAA,EAAC;gBAAE;kBAAA9B,QAAA,EAAAkB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAApB,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNpG,OAAA,CAAC1B,GAAG;gBAAC8M,IAAI,EAAE,EAAG;gBAAAtE,QAAA,eACZ9G,OAAA,CAACtB,QAAQ;kBAAC0D,KAAK,EAAC,WAAW;kBAAA0E,QAAA,EAAC;gBAAG;kBAAA9B,QAAA,EAAAkB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAApB,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNpG,OAAA,CAAC1B,GAAG;gBAAC8M,IAAI,EAAE,EAAG;gBAAAtE,QAAA,eACZ9G,OAAA,CAACtB,QAAQ;kBAAC0D,KAAK,EAAC,iBAAiB;kBAAA0E,QAAA,EAAC;gBAAI;kBAAA9B,QAAA,EAAAkB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAApB,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNpG,OAAA,CAAC1B,GAAG;gBAAC8M,IAAI,EAAE,EAAG;gBAAAtE,QAAA,eACZ9G,OAAA,CAACtB,QAAQ;kBAAC0D,KAAK,EAAC,cAAc;kBAAA0E,QAAA,EAAC;gBAAE;kBAAA9B,QAAA,EAAAkB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAApB,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNpG,OAAA,CAAC1B,GAAG;gBAAC8M,IAAI,EAAE,EAAG;gBAAAtE,QAAA,eACZ9G,OAAA,CAACtB,QAAQ;kBAAC0D,KAAK,EAAC,WAAW;kBAAA0E,QAAA,EAAC;gBAAI;kBAAA9B,QAAA,EAAAkB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAApB,QAAA,EAAAkB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAApB,QAAA,EAAAkB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAApB,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAApB,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRpG,OAAA,CAAC9B,KAAK;MACJyE,KAAK,EAAC,6BAAS;MACfgI,IAAI,EAAEnJ,gBAAiB;MACvBqB,IAAI,EAAE0C,WAAY;MAClBqF,QAAQ,EAAEA,CAAA,KAAMnJ,mBAAmB,CAAC,KAAK,CAAE;MAC3CgG,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfhB,KAAK,EAAE,GAAI;MAAAI,QAAA,eAEX9G,OAAA,CAAC7B,IAAI;QACHkN,IAAI,EAAEzJ,QAAS;QACf0J,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbvH,WAAW,EAAE,EAAE;UACfC,YAAY,EAAE,EAAE;UAChBO,YAAY,EAAE;QAChB,CAAE;QAAAsC,QAAA,gBAEF9G,OAAA,CAAC7B,IAAI,CAACqN,IAAI;UACRnF,KAAK,EAAC,0BAAM;UACZoF,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE1N,OAAO,EAAE;UAAS,CAAC,EACrC;YAAE2N,OAAO,EAAE,cAAc;YAAE3N,OAAO,EAAE;UAAqB,CAAC,CAC1D;UAAA6I,QAAA,eAEF9G,OAAA,CAACpC,KAAK;YAACkM,WAAW,EAAC;UAAU;YAAA9E,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAEZpG,OAAA,CAAC7B,IAAI,CAACqN,IAAI;UACRnF,KAAK,EAAC,0BAAM;UACZoF,IAAI,EAAC,cAAc;UACnBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1N,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6I,QAAA,eAEhD9G,OAAA,CAACpC,KAAK;YAACkM,WAAW,EAAC;UAAS;YAAA9E,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZpG,OAAA,CAAC7B,IAAI,CAACqN,IAAI;UACRnF,KAAK,EAAC,cAAI;UACVoF,IAAI,EAAC,cAAc;UACnBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1N,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6I,QAAA,eAE9C9G,OAAA,CAACpC,KAAK;YAACiJ,IAAI,EAAC;UAAM;YAAA7B,QAAA,EAAAkB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAApB,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEZpG,OAAA,CAAC5B,UAAU,CAAC0M,IAAI;UAACjE,IAAI,EAAC,WAAW;UAAAC,QAAA,EAAC;QAElC;UAAA9B,QAAA,EAAAkB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC;MAAA;QAAApB,QAAA,EAAAkB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAApB,QAAA,EAAAkB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAApB,QAAA,EAAAkB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChG,EAAA,CAxlBID,gBAA0B;EAAA,QACb3C,WAAW,EACXgC,cAAc,EACYC,cAAc,EAUtCtB,IAAI,CAAC0D,OAAO;AAAA;AAAAgK,EAAA,GAb3B1L,gBAA0B;AA0lBhC,eAAeA,gBAAgB;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}