import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Typography,
  Tag,
  Alert,
  Statistic,
  Progress,
  Tooltip,
  Badge,
  Divider,
  InputNumber,
  DatePicker,
  Upload,
  message,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ExportOutlined,
  ImportOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ScanOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { formatCurrency } from '../../utils/format';

const { Title, Text } = Typography;
const { Search } = Input;
const { TextArea } = Input;
const { Option } = Select;

// 备件接口定义
interface SparePart {
  id: string;
  partNumber: string;
  partName: string;
  category: string;
  specification: string;
  brand: string;
  supplier: string;
  unitPrice: number;
  currency: string;
  stockQuantity: number;
  minStockLevel: number;
  maxStockLevel: number;
  safetyStock: number;
  leadTime: number; // 采购周期（天）
  location: string;
  status: 'active' | 'inactive' | 'discontinued';
  lastPurchaseDate: string;
  lastUsageDate: string;
  usageFrequency: number; // 年使用频率
  criticality: 'high' | 'medium' | 'low';
  compatibleDevices: string[];
  interchangeableParts: string[];
  warrantyPeriod: number; // 保修期（月）
  storageConditions: string;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

// 库存预警接口
interface StockAlert {
  id: string;
  partNumber: string;
  partName: string;
  currentStock: number;
  minLevel: number;
  alertType: 'low_stock' | 'out_of_stock' | 'overstock';
  urgency: 'high' | 'medium' | 'low';
  suggestedAction: string;
}

const SparePartsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [spareParts, setSpareParts] = useState<SparePart[]>([]);
  const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string | undefined>();
  const [statusFilter, setStatusFilter] = useState<string | undefined>();
  const [criticalityFilter, setCriticalityFilter] = useState<string | undefined>();
  const [sparePartModalVisible, setSparePartModalVisible] = useState(false);
  const [editingSparePart, setEditingSparePart] = useState<SparePart | null>(null);
  const [form] = Form.useForm();

  // 模拟数据
  const mockSpareParts: SparePart[] = [
    {
      id: '1',
      partNumber: 'SP-001',
      partName: '伺服电机',
      category: '电机',
      specification: '1.5KW 220V',
      brand: '三菱',
      supplier: '三菱电机',
      unitPrice: 2800,
      currency: 'CNY',
      stockQuantity: 5,
      minStockLevel: 3,
      maxStockLevel: 15,
      safetyStock: 2,
      leadTime: 14,
      location: 'A-01-01',
      status: 'active',
      lastPurchaseDate: '2024-01-15',
      lastUsageDate: '2024-01-10',
      usageFrequency: 12,
      criticality: 'high',
      compatibleDevices: ['设备A', '设备B'],
      interchangeableParts: ['SP-002'],
      warrantyPeriod: 24,
      storageConditions: '常温干燥',
      notes: '关键备件，需保证库存',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-15',
    },
    {
      id: '2',
      partNumber: 'SP-002',
      partName: '轴承',
      category: '机械件',
      specification: '6205-2RS',
      brand: 'SKF',
      supplier: 'SKF中国',
      unitPrice: 85,
      currency: 'CNY',
      stockQuantity: 2,
      minStockLevel: 5,
      maxStockLevel: 20,
      safetyStock: 3,
      leadTime: 7,
      location: 'B-02-03',
      status: 'active',
      lastPurchaseDate: '2024-01-08',
      lastUsageDate: '2024-01-12',
      usageFrequency: 24,
      criticality: 'medium',
      compatibleDevices: ['设备A', '设备C', '设备D'],
      interchangeableParts: [],
      warrantyPeriod: 12,
      storageConditions: '防潮防尘',
      notes: '通用备件',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-08',
    },
    {
      id: '3',
      partNumber: 'SP-003',
      partName: '密封圈',
      category: '密封件',
      specification: 'O型圈 φ50×3',
      brand: '通用',
      supplier: '本地供应商',
      unitPrice: 12,
      currency: 'CNY',
      stockQuantity: 25,
      minStockLevel: 10,
      maxStockLevel: 50,
      safetyStock: 5,
      leadTime: 3,
      location: 'C-01-05',
      status: 'active',
      lastPurchaseDate: '2024-01-20',
      lastUsageDate: '2024-01-18',
      usageFrequency: 36,
      criticality: 'low',
      compatibleDevices: ['设备B', '设备C'],
      interchangeableParts: ['SP-004'],
      warrantyPeriod: 6,
      storageConditions: '避光保存',
      notes: '易损件，使用频繁',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-20',
    },
  ];

  const mockStockAlerts: StockAlert[] = [
    {
      id: '1',
      partNumber: 'SP-002',
      partName: '轴承',
      currentStock: 2,
      minLevel: 5,
      alertType: 'low_stock',
      urgency: 'high',
      suggestedAction: '立即采购 10 个',
    },
    {
      id: '2',
      partNumber: 'SP-001',
      partName: '伺服电机',
      currentStock: 5,
      minLevel: 3,
      alertType: 'low_stock',
      urgency: 'medium',
      suggestedAction: '建议采购 8 个',
    },
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSpareParts(mockSpareParts);
      setStockAlerts(mockStockAlerts);
    } catch (error) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddSparePart = () => {
    setEditingSparePart(null);
    form.resetFields();
    setSparePartModalVisible(true);
  };

  const handleEditSparePart = (record: SparePart) => {
    setEditingSparePart(record);
    form.setFieldsValue(record);
    setSparePartModalVisible(true);
  };

  const handleDeleteSparePart = (record: SparePart) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除备件 "${record.partName}" 吗？`,
      onOk: () => {
        message.success('删除成功');
        loadData();
      },
    });
  };

  const handleSparePartModalOk = async () => {
    try {
      const values = await form.validateFields();
      console.log('备件数据:', values);
      message.success(editingSparePart ? '更新成功' : '创建成功');
      setSparePartModalVisible(false);
      loadData();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const getStockStatus = (part: SparePart) => {
    if (part.stockQuantity <= 0) {
      return { status: 'error', text: '缺货' };
    } else if (part.stockQuantity <= part.minStockLevel) {
      return { status: 'warning', text: '库存不足' };
    } else if (part.stockQuantity >= part.maxStockLevel) {
      return { status: 'processing', text: '库存过多' };
    } else {
      return { status: 'success', text: '正常' };
    }
  };

  const getCriticalityColor = (criticality: string) => {
    switch (criticality) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'orange';
      case 'discontinued': return 'red';
      default: return 'default';
    }
  };

  const sparePartColumns: ColumnsType<SparePart> = [
    {
      title: '备件编号',
      dataIndex: 'partNumber',
      key: 'partNumber',
      width: 120,
      fixed: 'left',
    },
    {
      title: '备件名称',
      dataIndex: 'partName',
      key: 'partName',
      width: 150,
      fixed: 'left',
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 100,
    },
    {
      title: '规格型号',
      dataIndex: 'specification',
      key: 'specification',
      width: 150,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      key: 'brand',
      width: 100,
    },
    {
      title: '当前库存',
      dataIndex: 'stockQuantity',
      key: 'stockQuantity',
      width: 100,
      render: (quantity: number, record: SparePart) => {
        const stockStatus = getStockStatus(record);
        return (
          <Badge 
            status={stockStatus.status as any} 
            text={`${quantity} 个`}
          />
        );
      },
    },
    {
      title: '库存状态',
      key: 'stockStatus',
      width: 100,
      render: (_, record: SparePart) => {
        const stockStatus = getStockStatus(record);
        return (
          <Tag color={stockStatus.status === 'success' ? 'green' : 
                     stockStatus.status === 'warning' ? 'orange' : 'red'}>
            {stockStatus.text}
          </Tag>
        );
      },
    },
    {
      title: '最小库存',
      dataIndex: 'minStockLevel',
      key: 'minStockLevel',
      width: 100,
      render: (level: number) => `${level} 个`,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (price: number) => formatCurrency(price),
    },
    {
      title: '重要性',
      dataIndex: 'criticality',
      key: 'criticality',
      width: 100,
      render: (criticality: string) => (
        <Tag color={getCriticalityColor(criticality)}>
          {criticality === 'high' ? '高' : 
           criticality === 'medium' ? '中' : '低'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status === 'active' ? '启用' : 
           status === 'inactive' ? '停用' : '停产'}
        </Tag>
      ),
    },
    {
      title: '库位',
      dataIndex: 'location',
      key: 'location',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: SparePart) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditSparePart(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => handleDeleteSparePart(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const alertColumns: ColumnsType<StockAlert> = [
    {
      title: '备件编号',
      dataIndex: 'partNumber',
      key: 'partNumber',
    },
    {
      title: '备件名称',
      dataIndex: 'partName',
      key: 'partName',
    },
    {
      title: '当前库存',
      dataIndex: 'currentStock',
      key: 'currentStock',
      render: (stock: number) => `${stock} 个`,
    },
    {
      title: '最小库存',
      dataIndex: 'minLevel',
      key: 'minLevel',
      render: (level: number) => `${level} 个`,
    },
    {
      title: '预警类型',
      dataIndex: 'alertType',
      key: 'alertType',
      render: (type: string) => {
        const typeMap = {
          low_stock: { text: '库存不足', color: 'orange' },
          out_of_stock: { text: '缺货', color: 'red' },
          overstock: { text: '库存过多', color: 'blue' },
        };
        const config = typeMap[type as keyof typeof typeMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '紧急程度',
      dataIndex: 'urgency',
      key: 'urgency',
      render: (urgency: string) => {
        const urgencyMap = {
          high: { text: '高', color: 'red' },
          medium: { text: '中', color: 'orange' },
          low: { text: '低', color: 'green' },
        };
        const config = urgencyMap[urgency as keyof typeof urgencyMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '建议操作',
      dataIndex: 'suggestedAction',
      key: 'suggestedAction',
    },
  ];

  // 统计数据
  const totalParts = spareParts.length;
  const lowStockParts = spareParts.filter(part => part.stockQuantity <= part.minStockLevel).length;
  const outOfStockParts = spareParts.filter(part => part.stockQuantity <= 0).length;
  const totalValue = spareParts.reduce((sum, part) => sum + (part.stockQuantity * part.unitPrice), 0);

  return (
    <div>
      {/* 库存预警 */}
      {stockAlerts.length > 0 && (
        <Alert
          message="库存预警"
          description={`当前有 ${stockAlerts.length} 个备件需要关注，其中 ${stockAlerts.filter(a => a.urgency === 'high').length} 个高优先级预警。`}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: 16 }}
          action={
            <Button size="small" type="link">
              查看详情
            </Button>
          }
        />
      )}

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="备件总数"
              value={totalParts}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="库存不足"
              value={lowStockParts}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="缺货"
              value={outOfStockParts}
              suffix="个"
              valueStyle={{ color: '#f5222d' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="库存总价值"
              value={totalValue}
              precision={2}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              备件管理
            </Title>
            <Text type="secondary">
              管理设备备件库存，包括备件信息、库存水平、采购建议等
            </Text>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索备件编号、名称"
                allowClear
                style={{ width: 200 }}
                onSearch={setSearchKeyword}
              />
              <Select
                placeholder="类别"
                allowClear
                style={{ width: 100 }}
                value={categoryFilter}
                onChange={setCategoryFilter}
                options={[
                  { label: '电机', value: '电机' },
                  { label: '机械件', value: '机械件' },
                  { label: '密封件', value: '密封件' },
                  { label: '电气件', value: '电气件' },
                  { label: '液压件', value: '液压件' },
                ]}
              />
              <Select
                placeholder="状态"
                allowClear
                style={{ width: 100 }}
                value={statusFilter}
                onChange={setStatusFilter}
                options={[
                  { label: '启用', value: 'active' },
                  { label: '停用', value: 'inactive' },
                  { label: '停产', value: 'discontinued' },
                ]}
              />
              <Select
                placeholder="重要性"
                allowClear
                style={{ width: 100 }}
                value={criticalityFilter}
                onChange={setCriticalityFilter}
                options={[
                  { label: '高', value: 'high' },
                  { label: '中', value: 'medium' },
                  { label: '低', value: 'low' },
                ]}
              />
              <Button icon={<ScanOutlined />}>
                扫码查询
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
              <Button icon={<ImportOutlined />}>
                导入
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddSparePart}>
                新增备件
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={sparePartColumns}
          dataSource={spareParts}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1500 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 库存预警详情 */}
      {stockAlerts.length > 0 && (
        <Card title="库存预警详情" style={{ marginTop: 16 }}>
          <Table
            columns={alertColumns}
            dataSource={stockAlerts}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      )}

      {/* 备件编辑模态框 */}
      <Modal
        title={editingSparePart ? '编辑备件' : '新增备件'}
        open={sparePartModalVisible}
        onOk={handleSparePartModalOk}
        onCancel={() => setSparePartModalVisible(false)}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="partNumber"
                label="备件编号"
                rules={[{ required: true, message: '请输入备件编号' }]}
              >
                <Input placeholder="请输入备件编号" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="partName"
                label="备件名称"
                rules={[{ required: true, message: '请输入备件名称' }]}
              >
                <Input placeholder="请输入备件名称" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="category"
                label="类别"
                rules={[{ required: true, message: '请选择类别' }]}
              >
                <Select placeholder="请选择类别">
                  <Option value="电机">电机</Option>
                  <Option value="机械件">机械件</Option>
                  <Option value="密封件">密封件</Option>
                  <Option value="电气件">电气件</Option>
                  <Option value="液压件">液压件</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="specification"
                label="规格型号"
                rules={[{ required: true, message: '请输入规格型号' }]}
              >
                <Input placeholder="请输入规格型号" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="brand"
                label="品牌"
                rules={[{ required: true, message: '请输入品牌' }]}
              >
                <Input placeholder="请输入品牌" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="supplier"
                label="供应商"
                rules={[{ required: true, message: '请输入供应商' }]}
              >
                <Input placeholder="请输入供应商" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="unitPrice"
                label="单价"
                rules={[{ required: true, message: '请输入单价' }]}
              >
                <InputNumber
                  placeholder="请输入单价"
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="stockQuantity"
                label="当前库存"
                rules={[{ required: true, message: '请输入当前库存' }]}
              >
                <InputNumber
                  placeholder="请输入当前库存"
                  style={{ width: '100%' }}
                  min={0}
                  addonAfter="个"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="minStockLevel"
                label="最小库存"
                rules={[{ required: true, message: '请输入最小库存' }]}
              >
                <InputNumber
                  placeholder="最小库存"
                  style={{ width: '100%' }}
                  min={0}
                  addonAfter="个"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="maxStockLevel"
                label="最大库存"
                rules={[{ required: true, message: '请输入最大库存' }]}
              >
                <InputNumber
                  placeholder="最大库存"
                  style={{ width: '100%' }}
                  min={0}
                  addonAfter="个"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="safetyStock"
                label="安全库存"
                rules={[{ required: true, message: '请输入安全库存' }]}
              >
                <InputNumber
                  placeholder="安全库存"
                  style={{ width: '100%' }}
                  min={0}
                  addonAfter="个"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="leadTime"
                label="采购周期"
                rules={[{ required: true, message: '请输入采购周期' }]}
              >
                <InputNumber
                  placeholder="请输入采购周期"
                  style={{ width: '100%' }}
                  min={0}
                  addonAfter="天"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="location"
                label="库位"
                rules={[{ required: true, message: '请输入库位' }]}
              >
                <Input placeholder="请输入库位" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="criticality"
                label="重要性"
                rules={[{ required: true, message: '请选择重要性' }]}
              >
                <Select placeholder="请选择重要性">
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">停用</Option>
                  <Option value="discontinued">停产</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="warrantyPeriod"
                label="保修期"
              >
                <InputNumber
                  placeholder="请输入保修期"
                  style={{ width: '100%' }}
                  min={0}
                  addonAfter="月"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="storageConditions"
                label="存储条件"
              >
                <Input placeholder="请输入存储条件" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="notes"
                label="备注"
              >
                <TextArea rows={3} placeholder="请输入备注信息" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default SparePartsPage;