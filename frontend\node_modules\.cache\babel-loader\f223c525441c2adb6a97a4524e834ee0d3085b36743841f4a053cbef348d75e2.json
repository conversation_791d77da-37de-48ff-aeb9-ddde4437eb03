{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\cost\\\\CostReportsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Table, Button, Space, Select, DatePicker, Row, Col, Statistic, Tag, Modal, Form, Input, Checkbox, Divider, Alert, Progress, Tooltip } from 'antd';\nimport * as XLSX from 'xlsx';\nimport { FileExcelOutlined, FilePdfOutlined, PrinterOutlined, DownloadOutlined, EyeOutlined, SettingOutlined, CalendarOutlined, DollarOutlined, WarningOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCostReports } from '../../store/slices/costSlice';\nimport { formatCurrency, formatDate } from '../../utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TextArea\n} = Input;\nconst CostReportsPage = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    costReports,\n    loading\n  } = useAppSelector(state => state.cost);\n  const [reportType, setReportType] = useState('monthly');\n  const [dateRange, setDateRange] = useState([dayjs().subtract(1, 'month'), dayjs()]);\n  const [customReportVisible, setCustomReportVisible] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [customForm] = Form.useForm();\n  useEffect(() => {\n    loadData();\n  }, [reportType, dateRange]);\n  const loadData = () => {\n    dispatch(fetchCostReports({\n      type: reportType,\n      startDate: dateRange[0].format('YYYY-MM-DD'),\n      endDate: dateRange[1].format('YYYY-MM-DD')\n    }));\n  };\n  const handleExport = (format, reportId) => {\n    try {\n      let exportData = [];\n      let fileName = '';\n      if (reportId) {\n        // 导出特定报告\n        const report = mockReports.find(r => r.id === reportId);\n        if (report) {\n          exportData = [{\n            '报告名称': report.name,\n            '报告类型': report.type,\n            '生成时间': report.generatedAt,\n            '总成本': `¥${report.totalCost.toLocaleString()}`,\n            '状态': report.status\n          }];\n          fileName = `成本报告_${report.name}_${new Date().toISOString().split('T')[0]}`;\n        }\n      } else {\n        // 导出所有报告\n        exportData = mockReports.map(report => ({\n          '报告名称': report.name,\n          '报告类型': report.type,\n          '生成时间': report.generatedAt,\n          '总成本': `¥${report.totalCost.toLocaleString()}`,\n          '状态': report.status\n        }));\n        fileName = `成本报告汇总_${new Date().toISOString().split('T')[0]}`;\n      }\n      if (format === 'excel') {\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, '成本报告');\n        XLSX.writeFile(wb, `${fileName}.xlsx`);\n      } else if (format === 'csv') {\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, '成本报告');\n        XLSX.writeFile(wb, `${fileName}.csv`);\n      }\n      Modal.success({\n        title: '导出成功',\n        content: `报告已导出为 ${format.toUpperCase()} 格式`\n      });\n    } catch (error) {\n      Modal.error({\n        title: '导出失败',\n        content: '导出报告时发生错误'\n      });\n    }\n  };\n  const handlePreview = report => {\n    setSelectedReport(report);\n    setPreviewVisible(true);\n  };\n  const handleCustomReport = async () => {\n    try {\n      const values = await customForm.validateFields();\n      // TODO: 生成自定义报告\n      console.log('自定义报告参数:', values);\n      setCustomReportVisible(false);\n      Modal.success({\n        title: '报告生成成功',\n        content: '自定义报告已生成，请在报告列表中查看'\n      });\n    } catch (error) {\n      console.error('生成报告失败:', error);\n    }\n  };\n\n  // 模拟报告数据\n  const mockReports = [{\n    id: '1',\n    name: '2024年3月成本分析报告',\n    type: '月度报告',\n    period: '2024-03',\n    totalCost: 2850000,\n    wasteAmount: 156000,\n    wastePercentage: 5.47,\n    marginAmount: 712500,\n    marginPercentage: 25.0,\n    status: '已完成',\n    createdAt: '2024-04-01T00:00:00Z',\n    createdBy: 'finance_manager'\n  }, {\n    id: '2',\n    name: '2024年Q1季度成本报告',\n    type: '季度报告',\n    period: '2024-Q1',\n    totalCost: 8250000,\n    wasteAmount: 452000,\n    wastePercentage: 5.48,\n    marginAmount: 2062500,\n    marginPercentage: 25.0,\n    status: '已完成',\n    createdAt: '2024-04-05T00:00:00Z',\n    createdBy: 'finance_manager'\n  }, {\n    id: '3',\n    name: '华为项目成本专项报告',\n    type: '专项报告',\n    period: '2024-03',\n    totalCost: 1250000,\n    wasteAmount: 68000,\n    wastePercentage: 5.44,\n    marginAmount: 312500,\n    marginPercentage: 25.0,\n    status: '进行中',\n    createdAt: '2024-03-28T00:00:00Z',\n    createdBy: 'finance_manager'\n  }];\n  const reportColumns = [{\n    title: '报告名称',\n    dataIndex: 'name',\n    key: 'name',\n    ellipsis: true\n  }, {\n    title: '报告类型',\n    dataIndex: 'type',\n    key: 'type',\n    width: 100,\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: type === '月度报告' ? 'blue' : type === '季度报告' ? 'green' : 'orange',\n      children: type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '报告期间',\n    dataIndex: 'period',\n    key: 'period',\n    width: 100\n  }, {\n    title: '总成本',\n    dataIndex: 'totalCost',\n    key: 'totalCost',\n    width: 120,\n    render: cost => formatCurrency(cost)\n  }, {\n    title: '浪费金额',\n    dataIndex: 'wasteAmount',\n    key: 'wasteAmount',\n    width: 120,\n    render: (amount, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: '#ff4d4f'\n        },\n        children: formatCurrency(amount)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [record.wastePercentage.toFixed(2), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '毛利',\n    dataIndex: 'marginAmount',\n    key: 'marginAmount',\n    width: 120,\n    render: (margin, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: '#52c41a'\n        },\n        children: formatCurrency(margin)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [record.marginPercentage.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === '已完成' ? 'green' : 'processing',\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 120,\n    render: date => formatDate(date)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u9884\\u89C8\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 21\n          }, this),\n          onClick: () => handlePreview(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5BFC\\u51FAExcel\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(FileExcelOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleExport('excel', record.id)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5BFC\\u51FAPDF\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(FilePdfOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleExport('pdf', record.id)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u6253\\u5370\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(PrinterOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 21\n          }, this),\n          onClick: () => window.print()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 统计数据\n  const stats = {\n    totalReports: mockReports.length,\n    completedReports: mockReports.filter(r => r.status === '已完成').length,\n    avgWasteRate: mockReports.reduce((sum, r) => sum + r.wastePercentage, 0) / mockReports.length,\n    totalCostSum: mockReports.reduce((sum, r) => sum + r.totalCost, 0)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u62A5\\u544A\\u603B\\u6570\",\n            value: stats.totalReports,\n            prefix: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: stats.completedReports,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u6D6A\\u8D39\\u7387\",\n            value: stats.avgWasteRate,\n            precision: 2,\n            suffix: \"%\",\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u6210\\u672C\",\n            value: stats.totalCostSum,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 23\n            }, this),\n            formatter: value => formatCurrency(Number(value)),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u6210\\u672C\\u62A5\\u544A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              value: reportType,\n              onChange: setReportType,\n              style: {\n                width: 120\n              },\n              options: [{\n                label: '月度报告',\n                value: 'monthly'\n              }, {\n                label: '季度报告',\n                value: 'quarterly'\n              }, {\n                label: '年度报告',\n                value: 'yearly'\n              }, {\n                label: '专项报告',\n                value: 'special'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              value: dateRange,\n              onChange: dates => dates && setDateRange(dates),\n              style: {\n                width: 240\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 23\n              }, this),\n              onClick: () => setCustomReportVisible(true),\n              children: \"\\u81EA\\u5B9A\\u4E49\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 23\n              }, this),\n              onClick: () => handleExport('excel'),\n              children: \"\\u6279\\u91CF\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: reportColumns,\n        dataSource: mockReports,\n        loading: loading,\n        rowKey: \"id\",\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u751F\\u6210\\u81EA\\u5B9A\\u4E49\\u62A5\\u544A\",\n      open: customReportVisible,\n      onOk: handleCustomReport,\n      onCancel: () => setCustomReportVisible(false),\n      width: 600,\n      okText: \"\\u751F\\u6210\\u62A5\\u544A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: customForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"reportName\",\n          label: \"\\u62A5\\u544A\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入报告名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62A5\\u544A\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"reportType\",\n          label: \"\\u62A5\\u544A\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择报告类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u62A5\\u544A\\u7C7B\\u578B\",\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"cost_analysis\",\n              children: \"\\u6210\\u672C\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"waste_analysis\",\n              children: \"\\u6D6A\\u8D39\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"margin_analysis\",\n              children: \"\\u6BDB\\u5229\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"trend_analysis\",\n              children: \"\\u8D8B\\u52BF\\u5206\\u6790\\u62A5\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"dateRange\",\n          label: \"\\u62A5\\u544A\\u671F\\u95F4\",\n          rules: [{\n            required: true,\n            message: '请选择报告期间'\n          }],\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"includeItems\",\n          label: \"\\u5305\\u542B\\u5185\\u5BB9\",\n          children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"cost_overview\",\n                  children: \"\\u6210\\u672C\\u6982\\u89C8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"waste_analysis\",\n                  children: \"\\u6D6A\\u8D39\\u5206\\u6790\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"trend_charts\",\n                  children: \"\\u8D8B\\u52BF\\u56FE\\u8868\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"order_details\",\n                  children: \"\\u8BA2\\u5355\\u660E\\u7EC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"supplier_analysis\",\n                  children: \"\\u4F9B\\u5E94\\u5546\\u5206\\u6790\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  value: \"recommendations\",\n                  children: \"\\u6539\\u5584\\u5EFA\\u8BAE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u62A5\\u544A\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62A5\\u544A\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `预览报告: ${selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.name}`,\n      open: previewVisible,\n      onCancel: () => setPreviewVisible(false),\n      width: 800,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setPreviewVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 53\n        }, this),\n        children: \"\\u5BFC\\u51FA\"\n      }, \"export\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 11\n      }, this)],\n      children: selectedReport && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u62A5\\u544A\\u9884\\u89C8\",\n          description: \"\\u8FD9\\u662F\\u62A5\\u544A\\u7684\\u9884\\u89C8\\u7248\\u672C\\uFF0C\\u5B8C\\u6574\\u5185\\u5BB9\\u8BF7\\u5BFC\\u51FA\\u67E5\\u770B\",\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u62A5\\u544A\\u6982\\u8981\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u6210\\u672C\",\n              value: selectedReport.totalCost,\n              formatter: value => formatCurrency(Number(value))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6D6A\\u8D39\\u91D1\\u989D\",\n              value: selectedReport.wasteAmount,\n              formatter: value => formatCurrency(Number(value)),\n              valueStyle: {\n                color: '#ff4d4f'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6BDB\\u5229\",\n              value: selectedReport.marginAmount,\n              formatter: value => formatCurrency(Number(value)),\n              valueStyle: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u5173\\u952E\\u6307\\u6807\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u6D6A\\u8D39\\u7387: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedReport.wastePercentage,\n              strokeColor: \"#ff4d4f\",\n              format: percent => `${percent === null || percent === void 0 ? void 0 : percent.toFixed(2)}%`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u6BDB\\u5229\\u7387: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedReport.marginPercentage,\n              strokeColor: \"#52c41a\",\n              format: percent => `${percent === null || percent === void 0 ? void 0 : percent.toFixed(1)}%`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n};\n_s(CostReportsPage, \"598G8NqxjG0ADDCykjRxn1hcIew=\", false, function () {\n  return [useAppDispatch, useAppSelector, Form.useForm];\n});\n_c = CostReportsPage;\nexport default CostReportsPage;\nvar _c;\n$RefreshReg$(_c, \"CostReportsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Table", "<PERSON><PERSON>", "Space", "Select", "DatePicker", "Row", "Col", "Statistic", "Tag", "Modal", "Form", "Input", "Checkbox", "Divider", "<PERSON><PERSON>", "Progress", "<PERSON><PERSON><PERSON>", "XLSX", "FileExcelOutlined", "FilePdfOutlined", "PrinterOutlined", "DownloadOutlined", "EyeOutlined", "SettingOutlined", "CalendarOutlined", "DollarOutlined", "WarningOutlined", "dayjs", "useAppDispatch", "useAppSelector", "fetchCostReports", "formatCurrency", "formatDate", "jsxDEV", "_jsxDEV", "Title", "Text", "RangePicker", "TextArea", "CostReportsPage", "_s", "dispatch", "costReports", "loading", "state", "cost", "reportType", "setReportType", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "customReportVisible", "setCustomReportVisible", "previewVisible", "setPreviewVisible", "selectedReport", "setSelectedReport", "customForm", "useForm", "loadData", "type", "startDate", "format", "endDate", "handleExport", "reportId", "exportData", "fileName", "report", "mockReports", "find", "r", "id", "name", "generatedAt", "totalCost", "toLocaleString", "status", "Date", "toISOString", "split", "map", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "writeFile", "success", "title", "content", "toUpperCase", "error", "handlePreview", "handleCustomReport", "values", "validateFields", "console", "log", "period", "wasteAmount", "wastePercentage", "marginAmount", "marginPercentage", "createdAt", "created<PERSON>y", "reportColumns", "dataIndex", "key", "ellipsis", "width", "render", "color", "children", "_jsxFileName", "lineNumber", "columnNumber", "amount", "record", "direction", "size", "style", "fontSize", "toFixed", "margin", "date", "fixed", "_", "icon", "onClick", "window", "print", "stats", "totalReports", "length", "completedReports", "filter", "avgWasteRate", "reduce", "sum", "totalCostSum", "gutter", "marginBottom", "xs", "sm", "value", "prefix", "valueStyle", "precision", "suffix", "formatter", "Number", "justify", "align", "level", "onChange", "options", "label", "dates", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onOk", "onCancel", "okText", "cancelText", "form", "layout", "<PERSON><PERSON>", "rules", "required", "message", "placeholder", "Option", "Group", "span", "rows", "footer", "description", "showIcon", "orientation", "percent", "strokeColor", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/cost/CostReportsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Table,\n  Button,\n  Space,\n  Select,\n  DatePicker,\n  Row,\n  Col,\n  Statistic,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Checkbox,\n  Divider,\n  Alert,\n  Progress,\n  Tooltip,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  FileExcelOutlined,\n  FilePdfOutlined,\n  PrinterOutlined,\n  DownloadOutlined,\n  EyeOutlined,\n  SettingOutlined,\n  CalendarOutlined,\n  DollarOutlined,\n  RiseOutlined,\n  WarningOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchCostReports } from '../../store/slices/costSlice';\nimport { formatCurrency, formatDate } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\nconst { TextArea } = Input;\n\nconst CostReportsPage: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { costReports, loading } = useAppSelector(state => state.cost);\n\n  const [reportType, setReportType] = useState<string>('monthly');\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([\n    dayjs().subtract(1, 'month'),\n    dayjs(),\n  ]);\n  const [customReportVisible, setCustomReportVisible] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [selectedReport, setSelectedReport] = useState<any>(null);\n  const [customForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [reportType, dateRange]);\n\n  const loadData = () => {\n    dispatch(fetchCostReports({\n      type: reportType,\n      startDate: dateRange[0].format('YYYY-MM-DD'),\n      endDate: dateRange[1].format('YYYY-MM-DD'),\n    }));\n  };\n\n  const handleExport = (format: string, reportId?: string) => {\n    try {\n      let exportData: any[] = [];\n      let fileName = '';\n\n      if (reportId) {\n        // 导出特定报告\n        const report = mockReports.find(r => r.id === reportId);\n        if (report) {\n          exportData = [{\n            '报告名称': report.name,\n            '报告类型': report.type,\n            '生成时间': report.generatedAt,\n            '总成本': `¥${report.totalCost.toLocaleString()}`,\n            '状态': report.status,\n          }];\n          fileName = `成本报告_${report.name}_${new Date().toISOString().split('T')[0]}`;\n        }\n      } else {\n        // 导出所有报告\n        exportData = mockReports.map(report => ({\n          '报告名称': report.name,\n          '报告类型': report.type,\n          '生成时间': report.generatedAt,\n          '总成本': `¥${report.totalCost.toLocaleString()}`,\n          '状态': report.status,\n        }));\n        fileName = `成本报告汇总_${new Date().toISOString().split('T')[0]}`;\n      }\n\n      if (format === 'excel') {\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, '成本报告');\n        XLSX.writeFile(wb, `${fileName}.xlsx`);\n      } else if (format === 'csv') {\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, '成本报告');\n        XLSX.writeFile(wb, `${fileName}.csv`);\n      }\n\n      Modal.success({\n        title: '导出成功',\n        content: `报告已导出为 ${format.toUpperCase()} 格式`,\n      });\n    } catch (error) {\n      Modal.error({\n        title: '导出失败',\n        content: '导出报告时发生错误',\n      });\n    }\n  };\n\n  const handlePreview = (report: any) => {\n    setSelectedReport(report);\n    setPreviewVisible(true);\n  };\n\n  const handleCustomReport = async () => {\n    try {\n      const values = await customForm.validateFields();\n      // TODO: 生成自定义报告\n      console.log('自定义报告参数:', values);\n      setCustomReportVisible(false);\n      Modal.success({\n        title: '报告生成成功',\n        content: '自定义报告已生成，请在报告列表中查看',\n      });\n    } catch (error) {\n      console.error('生成报告失败:', error);\n    }\n  };\n\n  // 模拟报告数据\n  const mockReports = [\n    {\n      id: '1',\n      name: '2024年3月成本分析报告',\n      type: '月度报告',\n      period: '2024-03',\n      totalCost: 2850000,\n      wasteAmount: 156000,\n      wastePercentage: 5.47,\n      marginAmount: 712500,\n      marginPercentage: 25.0,\n      status: '已完成',\n      createdAt: '2024-04-01T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n    {\n      id: '2',\n      name: '2024年Q1季度成本报告',\n      type: '季度报告',\n      period: '2024-Q1',\n      totalCost: 8250000,\n      wasteAmount: 452000,\n      wastePercentage: 5.48,\n      marginAmount: 2062500,\n      marginPercentage: 25.0,\n      status: '已完成',\n      createdAt: '2024-04-05T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n    {\n      id: '3',\n      name: '华为项目成本专项报告',\n      type: '专项报告',\n      period: '2024-03',\n      totalCost: 1250000,\n      wasteAmount: 68000,\n      wastePercentage: 5.44,\n      marginAmount: 312500,\n      marginPercentage: 25.0,\n      status: '进行中',\n      createdAt: '2024-03-28T00:00:00Z',\n      createdBy: 'finance_manager',\n    },\n  ];\n\n  const reportColumns = [\n    {\n      title: '报告名称',\n      dataIndex: 'name',\n      key: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '报告类型',\n      dataIndex: 'type',\n      key: 'type',\n      width: 100,\n      render: (type: string) => (\n        <Tag color={type === '月度报告' ? 'blue' : type === '季度报告' ? 'green' : 'orange'}>\n          {type}\n        </Tag>\n      ),\n    },\n    {\n      title: '报告期间',\n      dataIndex: 'period',\n      key: 'period',\n      width: 100,\n    },\n    {\n      title: '总成本',\n      dataIndex: 'totalCost',\n      key: 'totalCost',\n      width: 120,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '浪费金额',\n      dataIndex: 'wasteAmount',\n      key: 'wasteAmount',\n      width: 120,\n      render: (amount: number, record: any) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ color: '#ff4d4f' }}>{formatCurrency(amount)}</Text>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.wastePercentage.toFixed(2)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '毛利',\n      dataIndex: 'marginAmount',\n      key: 'marginAmount',\n      width: 120,\n      render: (margin: number, record: any) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ color: '#52c41a' }}>{formatCurrency(margin)}</Text>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.marginPercentage.toFixed(1)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={status === '已完成' ? 'green' : 'processing'}>\n          {status}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right' as const,\n      render: (_: any, record: any) => (\n        <Space size=\"small\">\n          <Tooltip title=\"预览\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handlePreview(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"导出Excel\">\n            <Button\n              type=\"text\"\n              icon={<FileExcelOutlined />}\n              onClick={() => handleExport('excel', record.id)}\n            />\n          </Tooltip>\n          <Tooltip title=\"导出PDF\">\n            <Button\n              type=\"text\"\n              icon={<FilePdfOutlined />}\n              onClick={() => handleExport('pdf', record.id)}\n            />\n          </Tooltip>\n          <Tooltip title=\"打印\">\n            <Button\n              type=\"text\"\n              icon={<PrinterOutlined />}\n              onClick={() => window.print()}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  // 统计数据\n  const stats = {\n    totalReports: mockReports.length,\n    completedReports: mockReports.filter(r => r.status === '已完成').length,\n    avgWasteRate: mockReports.reduce((sum, r) => sum + r.wastePercentage, 0) / mockReports.length,\n    totalCostSum: mockReports.reduce((sum, r) => sum + r.totalCost, 0),\n  };\n\n  return (\n    <div>\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"报告总数\"\n              value={stats.totalReports}\n              prefix={<CalendarOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={stats.completedReports}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"平均浪费率\"\n              value={stats.avgWasteRate}\n              precision={2}\n              suffix=\"%\"\n              prefix={<WarningOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总成本\"\n              value={stats.totalCostSum}\n              prefix={<DollarOutlined />}\n              formatter={(value) => formatCurrency(Number(value))}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              成本报告\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Select\n                value={reportType}\n                onChange={setReportType}\n                style={{ width: 120 }}\n                options={[\n                  { label: '月度报告', value: 'monthly' },\n                  { label: '季度报告', value: 'quarterly' },\n                  { label: '年度报告', value: 'yearly' },\n                  { label: '专项报告', value: 'special' },\n                ]}\n              />\n              <RangePicker\n                value={dateRange}\n                onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n                style={{ width: 240 }}\n              />\n              <Button\n                icon={<SettingOutlined />}\n                onClick={() => setCustomReportVisible(true)}\n              >\n                自定义报告\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<DownloadOutlined />}\n                onClick={() => handleExport('excel')}\n              >\n                批量导出\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Table\n          columns={reportColumns}\n          dataSource={mockReports}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1200 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 自定义报告模态框 */}\n      <Modal\n        title=\"生成自定义报告\"\n        open={customReportVisible}\n        onOk={handleCustomReport}\n        onCancel={() => setCustomReportVisible(false)}\n        width={600}\n        okText=\"生成报告\"\n        cancelText=\"取消\"\n      >\n        <Form form={customForm} layout=\"vertical\">\n          <Form.Item\n            name=\"reportName\"\n            label=\"报告名称\"\n            rules={[{ required: true, message: '请输入报告名称' }]}\n          >\n            <Input placeholder=\"请输入报告名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"reportType\"\n            label=\"报告类型\"\n            rules={[{ required: true, message: '请选择报告类型' }]}\n          >\n            <Select placeholder=\"请选择报告类型\">\n              <Select.Option value=\"cost_analysis\">成本分析报告</Select.Option>\n              <Select.Option value=\"waste_analysis\">浪费分析报告</Select.Option>\n              <Select.Option value=\"margin_analysis\">毛利分析报告</Select.Option>\n              <Select.Option value=\"trend_analysis\">趋势分析报告</Select.Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"dateRange\"\n            label=\"报告期间\"\n            rules={[{ required: true, message: '请选择报告期间' }]}\n          >\n            <RangePicker style={{ width: '100%' }} />\n          </Form.Item>\n\n          <Form.Item name=\"includeItems\" label=\"包含内容\">\n            <Checkbox.Group>\n              <Row>\n                <Col span={12}>\n                  <Checkbox value=\"cost_overview\">成本概览</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"waste_analysis\">浪费分析</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"trend_charts\">趋势图表</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"order_details\">订单明细</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"supplier_analysis\">供应商分析</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"recommendations\">改善建议</Checkbox>\n                </Col>\n              </Row>\n            </Checkbox.Group>\n          </Form.Item>\n\n          <Form.Item name=\"description\" label=\"报告描述\">\n            <TextArea rows={3} placeholder=\"请输入报告描述\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 报告预览模态框 */}\n      <Modal\n        title={`预览报告: ${selectedReport?.name}`}\n        open={previewVisible}\n        onCancel={() => setPreviewVisible(false)}\n        width={800}\n        footer={[\n          <Button key=\"close\" onClick={() => setPreviewVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"export\" type=\"primary\" icon={<DownloadOutlined />}>\n            导出\n          </Button>,\n        ]}\n      >\n        {selectedReport && (\n          <div>\n            <Alert\n              message=\"报告预览\"\n              description=\"这是报告的预览版本，完整内容请导出查看\"\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n\n            <Divider orientation=\"left\">报告概要</Divider>\n            <Row gutter={[16, 16]}>\n              <Col span={8}>\n                <Statistic\n                  title=\"总成本\"\n                  value={selectedReport.totalCost}\n                  formatter={(value) => formatCurrency(Number(value))}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"浪费金额\"\n                  value={selectedReport.wasteAmount}\n                  formatter={(value) => formatCurrency(Number(value))}\n                  valueStyle={{ color: '#ff4d4f' }}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"毛利\"\n                  value={selectedReport.marginAmount}\n                  formatter={(value) => formatCurrency(Number(value))}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            </Row>\n\n            <Divider orientation=\"left\">关键指标</Divider>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text>浪费率: </Text>\n                <Progress\n                  percent={selectedReport.wastePercentage}\n                  strokeColor=\"#ff4d4f\"\n                  format={(percent) => `${percent?.toFixed(2)}%`}\n                />\n              </div>\n              <div>\n                <Text>毛利率: </Text>\n                <Progress\n                  percent={selectedReport.marginPercentage}\n                  strokeColor=\"#52c41a\"\n                  format={(percent) => `${percent?.toFixed(1)}%`}\n                />\n              </div>\n            </Space>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default CostReportsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,MAAM;AACb,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SACEC,iBAAiB,EACjBC,eAAe,EACfC,eAAe,EACfC,gBAAgB,EAChBC,WAAW,EACXC,eAAe,EACfC,gBAAgB,EAChBC,cAAc,EAEdC,eAAe,QACV,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,cAAc,EAAEC,UAAU,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGrC,UAAU;AAClC,MAAM;EAAEsC;AAAY,CAAC,GAAGjC,UAAU;AAClC,MAAM;EAAEkC;AAAS,CAAC,GAAG3B,KAAK;AAE1B,MAAM4B,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGb,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEc,WAAW;IAAEC;EAAQ,CAAC,GAAGd,cAAc,CAACe,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAEpE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAS,SAAS,CAAC;EAC/D,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAA6B,CACrE+B,KAAK,CAAC,CAAC,CAACuB,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,EAC5BvB,KAAK,CAAC,CAAC,CACR,CAAC;EACF,MAAM,CAACwB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAM,IAAI,CAAC;EAC/D,MAAM,CAAC6D,UAAU,CAAC,GAAG/C,IAAI,CAACgD,OAAO,CAAC,CAAC;EAEnC7D,SAAS,CAAC,MAAM;IACd8D,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACb,UAAU,EAAEE,SAAS,CAAC,CAAC;EAE3B,MAAMW,QAAQ,GAAGA,CAAA,KAAM;IACrBlB,QAAQ,CAACX,gBAAgB,CAAC;MACxB8B,IAAI,EAAEd,UAAU;MAChBe,SAAS,EAAEb,SAAS,CAAC,CAAC,CAAC,CAACc,MAAM,CAAC,YAAY,CAAC;MAC5CC,OAAO,EAAEf,SAAS,CAAC,CAAC,CAAC,CAACc,MAAM,CAAC,YAAY;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAACF,MAAc,EAAEG,QAAiB,KAAK;IAC1D,IAAI;MACF,IAAIC,UAAiB,GAAG,EAAE;MAC1B,IAAIC,QAAQ,GAAG,EAAE;MAEjB,IAAIF,QAAQ,EAAE;QACZ;QACA,MAAMG,MAAM,GAAGC,WAAW,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKP,QAAQ,CAAC;QACvD,IAAIG,MAAM,EAAE;UACVF,UAAU,GAAG,CAAC;YACZ,MAAM,EAAEE,MAAM,CAACK,IAAI;YACnB,MAAM,EAAEL,MAAM,CAACR,IAAI;YACnB,MAAM,EAAEQ,MAAM,CAACM,WAAW;YAC1B,KAAK,EAAE,IAAIN,MAAM,CAACO,SAAS,CAACC,cAAc,CAAC,CAAC,EAAE;YAC9C,IAAI,EAAER,MAAM,CAACS;UACf,CAAC,CAAC;UACFV,QAAQ,GAAG,QAAQC,MAAM,CAACK,IAAI,IAAI,IAAIK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5E;MACF,CAAC,MAAM;QACL;QACAd,UAAU,GAAGG,WAAW,CAACY,GAAG,CAACb,MAAM,KAAK;UACtC,MAAM,EAAEA,MAAM,CAACK,IAAI;UACnB,MAAM,EAAEL,MAAM,CAACR,IAAI;UACnB,MAAM,EAAEQ,MAAM,CAACM,WAAW;UAC1B,KAAK,EAAE,IAAIN,MAAM,CAACO,SAAS,CAACC,cAAc,CAAC,CAAC,EAAE;UAC9C,IAAI,EAAER,MAAM,CAACS;QACf,CAAC,CAAC,CAAC;QACHV,QAAQ,GAAG,UAAU,IAAIW,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/D;MAEA,IAAIlB,MAAM,KAAK,OAAO,EAAE;QACtB,MAAMoB,EAAE,GAAGjE,IAAI,CAACkE,KAAK,CAACC,aAAa,CAAClB,UAAU,CAAC;QAC/C,MAAMmB,EAAE,GAAGpE,IAAI,CAACkE,KAAK,CAACG,QAAQ,CAAC,CAAC;QAChCrE,IAAI,CAACkE,KAAK,CAACI,iBAAiB,CAACF,EAAE,EAAEH,EAAE,EAAE,MAAM,CAAC;QAC5CjE,IAAI,CAACuE,SAAS,CAACH,EAAE,EAAE,GAAGlB,QAAQ,OAAO,CAAC;MACxC,CAAC,MAAM,IAAIL,MAAM,KAAK,KAAK,EAAE;QAC3B,MAAMoB,EAAE,GAAGjE,IAAI,CAACkE,KAAK,CAACC,aAAa,CAAClB,UAAU,CAAC;QAC/C,MAAMmB,EAAE,GAAGpE,IAAI,CAACkE,KAAK,CAACG,QAAQ,CAAC,CAAC;QAChCrE,IAAI,CAACkE,KAAK,CAACI,iBAAiB,CAACF,EAAE,EAAEH,EAAE,EAAE,MAAM,CAAC;QAC5CjE,IAAI,CAACuE,SAAS,CAACH,EAAE,EAAE,GAAGlB,QAAQ,MAAM,CAAC;MACvC;MAEA1D,KAAK,CAACgF,OAAO,CAAC;QACZC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,UAAU7B,MAAM,CAAC8B,WAAW,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdpF,KAAK,CAACoF,KAAK,CAAC;QACVH,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMG,aAAa,GAAI1B,MAAW,IAAK;IACrCZ,iBAAiB,CAACY,MAAM,CAAC;IACzBd,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMvC,UAAU,CAACwC,cAAc,CAAC,CAAC;MAChD;MACAC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,MAAM,CAAC;MAC/B5C,sBAAsB,CAAC,KAAK,CAAC;MAC7B3C,KAAK,CAACgF,OAAO,CAAC;QACZC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMxB,WAAW,GAAG,CAClB;IACEG,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,eAAe;IACrBb,IAAI,EAAE,MAAM;IACZwC,MAAM,EAAE,SAAS;IACjBzB,SAAS,EAAE,OAAO;IAClB0B,WAAW,EAAE,MAAM;IACnBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,MAAM;IACpBC,gBAAgB,EAAE,IAAI;IACtB3B,MAAM,EAAE,KAAK;IACb4B,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACElC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,eAAe;IACrBb,IAAI,EAAE,MAAM;IACZwC,MAAM,EAAE,SAAS;IACjBzB,SAAS,EAAE,OAAO;IAClB0B,WAAW,EAAE,MAAM;IACnBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,OAAO;IACrBC,gBAAgB,EAAE,IAAI;IACtB3B,MAAM,EAAE,KAAK;IACb4B,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACElC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,YAAY;IAClBb,IAAI,EAAE,MAAM;IACZwC,MAAM,EAAE,SAAS;IACjBzB,SAAS,EAAE,OAAO;IAClB0B,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,MAAM;IACpBC,gBAAgB,EAAE,IAAI;IACtB3B,MAAM,EAAE,KAAK;IACb4B,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF;EAED,MAAMC,aAAa,GAAG,CACpB;IACEjB,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEpB,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGpD,IAAY,iBACnB1B,OAAA,CAAC1B,GAAG;MAACyG,KAAK,EAAErD,IAAI,KAAK,MAAM,GAAG,MAAM,GAAGA,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,QAAS;MAAAsD,QAAA,EACzEtD;IAAI;MAAAO,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACE3B,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE;EACT,CAAC,EACD;IACErB,KAAK,EAAE,KAAK;IACZkB,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGnE,IAAY,IAAKd,cAAc,CAACc,IAAI;EAC/C,CAAC,EACD;IACE6C,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACM,MAAc,EAAEC,MAAW,kBAClCrF,OAAA,CAAChC,KAAK;MAACsH,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAP,QAAA,gBAClChF,OAAA,CAACE,IAAI;QAACsF,KAAK,EAAE;UAAET,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EAAEnF,cAAc,CAACuF,MAAM;MAAC;QAAAnD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEnF,OAAA,CAACE,IAAI;QAACwB,IAAI,EAAC,WAAW;QAAC8D,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAAAT,QAAA,GAC5CK,MAAM,CAACjB,eAAe,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAC,GACrC;MAAA;QAAAzD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAlD,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACE3B,KAAK,EAAE,IAAI;IACXkB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACa,MAAc,EAAEN,MAAW,kBAClCrF,OAAA,CAAChC,KAAK;MAACsH,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAP,QAAA,gBAClChF,OAAA,CAACE,IAAI;QAACsF,KAAK,EAAE;UAAET,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EAAEnF,cAAc,CAAC8F,MAAM;MAAC;QAAA1D,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEnF,OAAA,CAACE,IAAI;QAACwB,IAAI,EAAC,WAAW;QAAC8D,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAAAT,QAAA,GAC5CK,MAAM,CAACf,gBAAgB,CAACoB,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;MAAA;QAAAzD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAlD,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACE3B,KAAK,EAAE,IAAI;IACXkB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGnC,MAAc,iBACrB3C,OAAA,CAAC1B,GAAG;MAACyG,KAAK,EAAEpC,MAAM,KAAK,KAAK,GAAG,OAAO,GAAG,YAAa;MAAAqC,QAAA,EACnDrC;IAAM;MAAAV,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACE3B,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGc,IAAY,IAAK9F,UAAU,CAAC8F,IAAI;EAC3C,CAAC,EACD;IACEpC,KAAK,EAAE,IAAI;IACXmB,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE,GAAG;IACVgB,KAAK,EAAE,OAAgB;IACvBf,MAAM,EAAEA,CAACgB,CAAM,EAAET,MAAW,kBAC1BrF,OAAA,CAAChC,KAAK;MAACuH,IAAI,EAAC,OAAO;MAAAP,QAAA,gBACjBhF,OAAA,CAAClB,OAAO;QAAC0E,KAAK,EAAC,cAAI;QAAAwB,QAAA,eACjBhF,OAAA,CAACjC,MAAM;UACL2D,IAAI,EAAC,MAAM;UACXqE,IAAI,eAAE/F,OAAA,CAACZ,WAAW;YAAA6C,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBa,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAACyB,MAAM;QAAE;UAAApD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVnF,OAAA,CAAClB,OAAO;QAAC0E,KAAK,EAAC,mBAAS;QAAAwB,QAAA,eACtBhF,OAAA,CAACjC,MAAM;UACL2D,IAAI,EAAC,MAAM;UACXqE,IAAI,eAAE/F,OAAA,CAAChB,iBAAiB;YAAAiD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5Ba,OAAO,EAAEA,CAAA,KAAMlE,YAAY,CAAC,OAAO,EAAEuD,MAAM,CAAC/C,EAAE;QAAE;UAAAL,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVnF,OAAA,CAAClB,OAAO;QAAC0E,KAAK,EAAC,iBAAO;QAAAwB,QAAA,eACpBhF,OAAA,CAACjC,MAAM;UACL2D,IAAI,EAAC,MAAM;UACXqE,IAAI,eAAE/F,OAAA,CAACf,eAAe;YAAAgD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Ba,OAAO,EAAEA,CAAA,KAAMlE,YAAY,CAAC,KAAK,EAAEuD,MAAM,CAAC/C,EAAE;QAAE;UAAAL,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVnF,OAAA,CAAClB,OAAO;QAAC0E,KAAK,EAAC,cAAI;QAAAwB,QAAA,eACjBhF,OAAA,CAACjC,MAAM;UACL2D,IAAI,EAAC,MAAM;UACXqE,IAAI,eAAE/F,OAAA,CAACd,eAAe;YAAA+C,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Ba,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,KAAK,CAAC;QAAE;UAAAjE,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAlD,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;;EAED;EACA,MAAMgB,KAAK,GAAG;IACZC,YAAY,EAAEjE,WAAW,CAACkE,MAAM;IAChCC,gBAAgB,EAAEnE,WAAW,CAACoE,MAAM,CAAClE,CAAC,IAAIA,CAAC,CAACM,MAAM,KAAK,KAAK,CAAC,CAAC0D,MAAM;IACpEG,YAAY,EAAErE,WAAW,CAACsE,MAAM,CAAC,CAACC,GAAG,EAAErE,CAAC,KAAKqE,GAAG,GAAGrE,CAAC,CAAC+B,eAAe,EAAE,CAAC,CAAC,GAAGjC,WAAW,CAACkE,MAAM;IAC7FM,YAAY,EAAExE,WAAW,CAACsE,MAAM,CAAC,CAACC,GAAG,EAAErE,CAAC,KAAKqE,GAAG,GAAGrE,CAAC,CAACI,SAAS,EAAE,CAAC;EACnE,CAAC;EAED,oBACEzC,OAAA;IAAAgF,QAAA,gBAEEhF,OAAA,CAAC7B,GAAG;MAACyI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACpB,KAAK,EAAE;QAAEqB,YAAY,EAAE;MAAG,CAAE;MAAA7B,QAAA,gBACjDhF,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACjBhF,OAAA,CAACpC,IAAI;UAAAoH,QAAA,eACHhF,OAAA,CAAC3B,SAAS;YACRmF,KAAK,EAAC,0BAAM;YACZwD,KAAK,EAAEb,KAAK,CAACC,YAAa;YAC1Ba,MAAM,eAAEjH,OAAA,CAACV,gBAAgB;cAAA2C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7B+B,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU;UAAE;YAAA9C,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnF,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACjBhF,OAAA,CAACpC,IAAI;UAAAoH,QAAA,eACHhF,OAAA,CAAC3B,SAAS;YACRmF,KAAK,EAAC,oBAAK;YACXwD,KAAK,EAAEb,KAAK,CAACG,gBAAiB;YAC9BY,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU;UAAE;YAAA9C,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnF,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACjBhF,OAAA,CAACpC,IAAI;UAAAoH,QAAA,eACHhF,OAAA,CAAC3B,SAAS;YACRmF,KAAK,EAAC,gCAAO;YACbwD,KAAK,EAAEb,KAAK,CAACK,YAAa;YAC1BW,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC,GAAG;YACVH,MAAM,eAAEjH,OAAA,CAACR,eAAe;cAAAyC,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B+B,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU;UAAE;YAAA9C,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnF,OAAA,CAAC5B,GAAG;QAAC0I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACjBhF,OAAA,CAACpC,IAAI;UAAAoH,QAAA,eACHhF,OAAA,CAAC3B,SAAS;YACRmF,KAAK,EAAC,oBAAK;YACXwD,KAAK,EAAEb,KAAK,CAACQ,YAAa;YAC1BM,MAAM,eAAEjH,OAAA,CAACT,cAAc;cAAA0C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BkC,SAAS,EAAGL,KAAK,IAAKnH,cAAc,CAACyH,MAAM,CAACN,KAAK,CAAC,CAAE;YACpDE,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU;UAAE;YAAA9C,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAlD,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnF,OAAA,CAACpC,IAAI;MAAAoH,QAAA,gBACHhF,OAAA,CAAC7B,GAAG;QAACoJ,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAChC,KAAK,EAAE;UAAEqB,YAAY,EAAE;QAAG,CAAE;QAAA7B,QAAA,gBACtEhF,OAAA,CAAC5B,GAAG;UAAA4G,QAAA,eACFhF,OAAA,CAACC,KAAK;YAACwH,KAAK,EAAE,CAAE;YAACjC,KAAK,EAAE;cAAEG,MAAM,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAEvC;YAAA/C,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNnF,OAAA,CAAC5B,GAAG;UAAA4G,QAAA,eACFhF,OAAA,CAAChC,KAAK;YAAAgH,QAAA,gBACJhF,OAAA,CAAC/B,MAAM;cACL+I,KAAK,EAAEpG,UAAW;cAClB8G,QAAQ,EAAE7G,aAAc;cACxB2E,KAAK,EAAE;gBAAEX,KAAK,EAAE;cAAI,CAAE;cACtB8C,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,MAAM;gBAAEZ,KAAK,EAAE;cAAU,CAAC,EACnC;gBAAEY,KAAK,EAAE,MAAM;gBAAEZ,KAAK,EAAE;cAAY,CAAC,EACrC;gBAAEY,KAAK,EAAE,MAAM;gBAAEZ,KAAK,EAAE;cAAS,CAAC,EAClC;gBAAEY,KAAK,EAAE,MAAM;gBAAEZ,KAAK,EAAE;cAAU,CAAC;YACnC;cAAA/E,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnF,OAAA,CAACG,WAAW;cACV6G,KAAK,EAAElG,SAAU;cACjB4G,QAAQ,EAAGG,KAAK,IAAKA,KAAK,IAAI9G,YAAY,CAAC8G,KAAmC,CAAE;cAChFrC,KAAK,EAAE;gBAAEX,KAAK,EAAE;cAAI;YAAE;cAAA5C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFnF,OAAA,CAACjC,MAAM;cACLgI,IAAI,eAAE/F,OAAA,CAACX,eAAe;gBAAA4C,QAAA,EAAAgD,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1Ba,OAAO,EAAEA,CAAA,KAAM9E,sBAAsB,CAAC,IAAI,CAAE;cAAA8D,QAAA,EAC7C;YAED;cAAA/C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnF,OAAA,CAACjC,MAAM;cACL2D,IAAI,EAAC,SAAS;cACdqE,IAAI,eAAE/F,OAAA,CAACb,gBAAgB;gBAAA8C,QAAA,EAAAgD,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3Ba,OAAO,EAAEA,CAAA,KAAMlE,YAAY,CAAC,OAAO,CAAE;cAAAkD,QAAA,EACtC;YAED;cAAA/C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAlD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnF,OAAA,CAAClC,KAAK;QACJgK,OAAO,EAAErD,aAAc;QACvBsD,UAAU,EAAE5F,WAAY;QACxB1B,OAAO,EAAEA,OAAQ;QACjBuH,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAAtG,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAlD,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnF,OAAA,CAACzB,KAAK;MACJiF,KAAK,EAAC,4CAAS;MACfiF,IAAI,EAAExH,mBAAoB;MAC1ByH,IAAI,EAAE7E,kBAAmB;MACzB8E,QAAQ,EAAEA,CAAA,KAAMzH,sBAAsB,CAAC,KAAK,CAAE;MAC9C2D,KAAK,EAAE,GAAI;MACX+D,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MAAA7D,QAAA,eAEfhF,OAAA,CAACxB,IAAI;QAACsK,IAAI,EAAEvH,UAAW;QAACwH,MAAM,EAAC,UAAU;QAAA/D,QAAA,gBACvChF,OAAA,CAACxB,IAAI,CAACwK,IAAI;UACRzG,IAAI,EAAC,YAAY;UACjBqF,KAAK,EAAC,0BAAM;UACZqB,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAnE,QAAA,eAEhDhF,OAAA,CAACvB,KAAK;YAAC2K,WAAW,EAAC;UAAS;YAAAnH,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZnF,OAAA,CAACxB,IAAI,CAACwK,IAAI;UACRzG,IAAI,EAAC,YAAY;UACjBqF,KAAK,EAAC,0BAAM;UACZqB,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAnE,QAAA,eAEhDhF,OAAA,CAAC/B,MAAM;YAACmL,WAAW,EAAC,4CAAS;YAAApE,QAAA,gBAC3BhF,OAAA,CAAC/B,MAAM,CAACoL,MAAM;cAACrC,KAAK,EAAC,eAAe;cAAAhC,QAAA,EAAC;YAAM;cAAA/C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC3DnF,OAAA,CAAC/B,MAAM,CAACoL,MAAM;cAACrC,KAAK,EAAC,gBAAgB;cAAAhC,QAAA,EAAC;YAAM;cAAA/C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC5DnF,OAAA,CAAC/B,MAAM,CAACoL,MAAM;cAACrC,KAAK,EAAC,iBAAiB;cAAAhC,QAAA,EAAC;YAAM;cAAA/C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC7DnF,OAAA,CAAC/B,MAAM,CAACoL,MAAM;cAACrC,KAAK,EAAC,gBAAgB;cAAAhC,QAAA,EAAC;YAAM;cAAA/C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAlD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZnF,OAAA,CAACxB,IAAI,CAACwK,IAAI;UACRzG,IAAI,EAAC,WAAW;UAChBqF,KAAK,EAAC,0BAAM;UACZqB,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAnE,QAAA,eAEhDhF,OAAA,CAACG,WAAW;YAACqF,KAAK,EAAE;cAAEX,KAAK,EAAE;YAAO;UAAE;YAAA5C,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEZnF,OAAA,CAACxB,IAAI,CAACwK,IAAI;UAACzG,IAAI,EAAC,cAAc;UAACqF,KAAK,EAAC,0BAAM;UAAA5C,QAAA,eACzChF,OAAA,CAACtB,QAAQ,CAAC4K,KAAK;YAAAtE,QAAA,eACbhF,OAAA,CAAC7B,GAAG;cAAA6G,QAAA,gBACFhF,OAAA,CAAC5B,GAAG;gBAACmL,IAAI,EAAE,EAAG;gBAAAvE,QAAA,eACZhF,OAAA,CAACtB,QAAQ;kBAACsI,KAAK,EAAC,eAAe;kBAAAhC,QAAA,EAAC;gBAAI;kBAAA/C,QAAA,EAAAgD,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAlD,QAAA,EAAAgD,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNnF,OAAA,CAAC5B,GAAG;gBAACmL,IAAI,EAAE,EAAG;gBAAAvE,QAAA,eACZhF,OAAA,CAACtB,QAAQ;kBAACsI,KAAK,EAAC,gBAAgB;kBAAAhC,QAAA,EAAC;gBAAI;kBAAA/C,QAAA,EAAAgD,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAlD,QAAA,EAAAgD,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNnF,OAAA,CAAC5B,GAAG;gBAACmL,IAAI,EAAE,EAAG;gBAAAvE,QAAA,eACZhF,OAAA,CAACtB,QAAQ;kBAACsI,KAAK,EAAC,cAAc;kBAAAhC,QAAA,EAAC;gBAAI;kBAAA/C,QAAA,EAAAgD,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAlD,QAAA,EAAAgD,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNnF,OAAA,CAAC5B,GAAG;gBAACmL,IAAI,EAAE,EAAG;gBAAAvE,QAAA,eACZhF,OAAA,CAACtB,QAAQ;kBAACsI,KAAK,EAAC,eAAe;kBAAAhC,QAAA,EAAC;gBAAI;kBAAA/C,QAAA,EAAAgD,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAlD,QAAA,EAAAgD,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNnF,OAAA,CAAC5B,GAAG;gBAACmL,IAAI,EAAE,EAAG;gBAAAvE,QAAA,eACZhF,OAAA,CAACtB,QAAQ;kBAACsI,KAAK,EAAC,mBAAmB;kBAAAhC,QAAA,EAAC;gBAAK;kBAAA/C,QAAA,EAAAgD,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAlD,QAAA,EAAAgD,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNnF,OAAA,CAAC5B,GAAG;gBAACmL,IAAI,EAAE,EAAG;gBAAAvE,QAAA,eACZhF,OAAA,CAACtB,QAAQ;kBAACsI,KAAK,EAAC,iBAAiB;kBAAAhC,QAAA,EAAC;gBAAI;kBAAA/C,QAAA,EAAAgD,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAlD,QAAA,EAAAgD,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAlD,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAlD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEZnF,OAAA,CAACxB,IAAI,CAACwK,IAAI;UAACzG,IAAI,EAAC,aAAa;UAACqF,KAAK,EAAC,0BAAM;UAAA5C,QAAA,eACxChF,OAAA,CAACI,QAAQ;YAACoJ,IAAI,EAAE,CAAE;YAACJ,WAAW,EAAC;UAAS;YAAAnH,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAlD,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRnF,OAAA,CAACzB,KAAK;MACJiF,KAAK,EAAE,SAASnC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEkB,IAAI,EAAG;MACvCkG,IAAI,EAAEtH,cAAe;MACrBwH,QAAQ,EAAEA,CAAA,KAAMvH,iBAAiB,CAAC,KAAK,CAAE;MACzCyD,KAAK,EAAE,GAAI;MACX4E,MAAM,EAAE,cACNzJ,OAAA,CAACjC,MAAM;QAAaiI,OAAO,EAAEA,CAAA,KAAM5E,iBAAiB,CAAC,KAAK,CAAE;QAAA4D,QAAA,EAAC;MAE7D,GAFY,OAAO;QAAA/C,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACTnF,OAAA,CAACjC,MAAM;QAAc2D,IAAI,EAAC,SAAS;QAACqE,IAAI,eAAE/F,OAAA,CAACb,gBAAgB;UAAA8C,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAH,QAAA,EAAC;MAEhE,GAFY,QAAQ;QAAA/C,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEZ,CAAC,CACT;MAAAH,QAAA,EAED3D,cAAc,iBACbrB,OAAA;QAAAgF,QAAA,gBACEhF,OAAA,CAACpB,KAAK;UACJuK,OAAO,EAAC,0BAAM;UACdO,WAAW,EAAC,oHAAqB;UACjChI,IAAI,EAAC,MAAM;UACXiI,QAAQ;UACRnE,KAAK,EAAE;YAAEqB,YAAY,EAAE;UAAG;QAAE;UAAA5E,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFnF,OAAA,CAACrB,OAAO;UAACiL,WAAW,EAAC,MAAM;UAAA5E,QAAA,EAAC;QAAI;UAAA/C,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1CnF,OAAA,CAAC7B,GAAG;UAACyI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAA5B,QAAA,gBACpBhF,OAAA,CAAC5B,GAAG;YAACmL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACXhF,OAAA,CAAC3B,SAAS;cACRmF,KAAK,EAAC,oBAAK;cACXwD,KAAK,EAAE3F,cAAc,CAACoB,SAAU;cAChC4E,SAAS,EAAGL,KAAK,IAAKnH,cAAc,CAACyH,MAAM,CAACN,KAAK,CAAC;YAAE;cAAA/E,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAAC;YAAAlD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnF,OAAA,CAAC5B,GAAG;YAACmL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACXhF,OAAA,CAAC3B,SAAS;cACRmF,KAAK,EAAC,0BAAM;cACZwD,KAAK,EAAE3F,cAAc,CAAC8C,WAAY;cAClCkD,SAAS,EAAGL,KAAK,IAAKnH,cAAc,CAACyH,MAAM,CAACN,KAAK,CAAC,CAAE;cACpDE,UAAU,EAAE;gBAAEnC,KAAK,EAAE;cAAU;YAAE;cAAA9C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAlD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnF,OAAA,CAAC5B,GAAG;YAACmL,IAAI,EAAE,CAAE;YAAAvE,QAAA,eACXhF,OAAA,CAAC3B,SAAS;cACRmF,KAAK,EAAC,cAAI;cACVwD,KAAK,EAAE3F,cAAc,CAACgD,YAAa;cACnCgD,SAAS,EAAGL,KAAK,IAAKnH,cAAc,CAACyH,MAAM,CAACN,KAAK,CAAC,CAAE;cACpDE,UAAU,EAAE;gBAAEnC,KAAK,EAAE;cAAU;YAAE;cAAA9C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAlD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA,CAACrB,OAAO;UAACiL,WAAW,EAAC,MAAM;UAAA5E,QAAA,EAAC;QAAI;UAAA/C,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1CnF,OAAA,CAAChC,KAAK;UAACsH,SAAS,EAAC,UAAU;UAACE,KAAK,EAAE;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAG,QAAA,gBACnDhF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA,CAACE,IAAI;cAAA8E,QAAA,EAAC;YAAK;cAAA/C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBnF,OAAA,CAACnB,QAAQ;cACPgL,OAAO,EAAExI,cAAc,CAAC+C,eAAgB;cACxC0F,WAAW,EAAC,SAAS;cACrBlI,MAAM,EAAGiI,OAAO,IAAK,GAAGA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnE,OAAO,CAAC,CAAC,CAAC;YAAI;cAAAzD,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAlD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA,CAACE,IAAI;cAAA8E,QAAA,EAAC;YAAK;cAAA/C,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBnF,OAAA,CAACnB,QAAQ;cACPgL,OAAO,EAAExI,cAAc,CAACiD,gBAAiB;cACzCwF,WAAW,EAAC,SAAS;cACrBlI,MAAM,EAAGiI,OAAO,IAAK,GAAGA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEnE,OAAO,CAAC,CAAC,CAAC;YAAI;cAAAzD,QAAA,EAAAgD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAlD,QAAA,EAAAgD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAlD,QAAA,EAAAgD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAlD,QAAA,EAAAgD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACN;MAAAlD,QAAA,EAAAgD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAlD,QAAA,EAAAgD,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC7E,EAAA,CA3gBID,eAAyB;EAAA,QACZX,cAAc,EACEC,cAAc,EAU1BnB,IAAI,CAACgD,OAAO;AAAA;AAAAuI,EAAA,GAZ7B1J,eAAyB;AA6gB/B,eAAeA,eAAe;AAAC,IAAA0J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}