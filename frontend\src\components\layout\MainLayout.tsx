import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Button,
  Space,
  Badge,
  Breadcrumb,
  theme,
  MenuProps,
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  FileTextOutlined,
  ShoppingCartOutlined,
  InboxOutlined,
  DollarOutlined,
  ToolOutlined,
  SwapOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  MobileOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  SwitcherOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { logout, switchRole } from '../../store/slices/authSlice';
import { ROUTES, USER_ROLES } from '../../constants';

const { Header, Sider, Content } = Layout;

// 菜单配置
const menuItems: MenuProps['items'] = [
  {
    key: ROUTES.DASHBOARD,
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: 'bom',
    icon: <FileTextOutlined />,
    label: 'BOM管理',
    children: [
      {
        key: ROUTES.CORE_BOM,
        label: '核心BOM',
      },
      {
        key: ROUTES.ORDER_BOM,
        label: '订单BOM',
      },
    ],
  },
  {
    key: 'material',
    icon: <InboxOutlined />,
    label: '物料管理',
    children: [
      {
        key: ROUTES.MATERIALS,
        label: '物料清单',
      },
    ],
  },
  {
    key: 'inventory',
    icon: <InboxOutlined />,
    label: '库存管理',
    children: [
      {
        key: ROUTES.INVENTORY,
        label: '库存查询',
      },
      {
        key: ROUTES.INVENTORY_RECEIVE,
        label: '入库管理',
      },
      {
        key: ROUTES.INVENTORY_ISSUE,
        label: '出库管理',
      },
      {
        key: ROUTES.REMNANTS,
        label: '余料管理',
      },
      {
        key: ROUTES.CUTTING_PLAN,
        label: '切割计划',
      },
      {
        key: ROUTES.BATCH_TRACKING,
        label: '批次跟踪',
      },
    ],
  },
  {
    key: 'purchase',
    icon: <ShoppingCartOutlined />,
    label: '采购管理',
    children: [
      {
        key: ROUTES.PURCHASE,
        label: '采购订单',
      },
      {
        key: ROUTES.PURCHASE_REQUISITION,
        label: '采购申请',
      },
      {
        key: ROUTES.MRP_CALCULATION,
        label: 'MRP计算',
      },
      {
        key: ROUTES.PURCHASE_OPTIMIZATION,
        label: '采购优化',
      },
    ],
  },
  {
    key: 'cost',
    icon: <DollarOutlined />,
    label: '成本管理',
    children: [
      {
        key: ROUTES.COST_ANALYSIS,
        label: '成本分析',
      },
      {
        key: ROUTES.COST_REPORTS,
        label: '成本报告',
      },
      {
        key: ROUTES.WASTE_TRACKING,
        label: '浪费跟踪',
      },
      {
        key: ROUTES.STANDARD_COST,
        label: '标准成本',
      },
    ],
  },
  {
    key: 'service',
    icon: <ToolOutlined />,
    label: '服务管理',
    children: [
      {
        key: ROUTES.SERVICE_BOM,
        label: '服务BOM',
      },
      {
        key: ROUTES.DEVICE_ARCHIVE,
        label: '设备档案',
      },
      {
        key: ROUTES.MAINTENANCE,
        label: '维护记录',
      },
      {
        key: ROUTES.AS_BUILT_BOM,
        label: 'As-Built BOM',
      },
      {
        key: ROUTES.SPARE_PARTS,
        label: '备件管理',
      },
    ],
  },
  {
    key: 'ecn',
    icon: <SwapOutlined />,
    label: 'ECN管理',
    children: [
      {
        key: ROUTES.ECN,
        label: 'ECN列表',
      },
    ],
  },
  {
    key: 'reports',
    icon: <BarChartOutlined />,
    label: '报告分析',
    children: [
      {
        key: ROUTES.REPORTS,
        label: '报告中心',
      },
      {
        key: ROUTES.DASHBOARD_CONFIG,
        label: '仪表板配置',
      },
    ],
  },
  {
    key: 'system',
    icon: <SettingOutlined />,
    label: '系统管理',
    children: [
      {
        key: ROUTES.USERS,
        label: '用户管理',
      },
      {
        key: ROUTES.ROLES,
        label: '角色管理',
      },
      {
        key: ROUTES.PERMISSIONS,
        label: '权限管理',
      },
      {
        key: ROUTES.SYSTEM_CONFIG,
        label: '系统配置',
      },
      {
        key: ROUTES.AUDIT_LOG,
        label: '审计日志',
      },
    ],
  },
  {
    key: 'mobile',
    icon: <MobileOutlined />,
    label: '移动端',
    children: [
      {
        key: ROUTES.MOBILE,
        label: '移动首页',
      },
      {
        key: ROUTES.MOBILE_SCAN,
        label: '扫码操作',
      },
      {
        key: ROUTES.MOBILE_INVENTORY,
        label: '移动库存',
      },
    ],
  },
];

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user, currentRole } = useAppSelector(state => state.auth);
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'logout':
        dispatch(logout());
        navigate(ROUTES.LOGIN);
        break;
      case 'profile':
        // 跳转到个人资料页面
        break;
      default:
        break;
    }
  };

  // 处理角色切换
  const handleRoleSwitch = (roleId: string) => {
    dispatch(switchRole(roleId));
  };

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 角色切换菜单
  const roleMenuItems: MenuProps['items'] = user?.roles.map(role => ({
    key: role.id,
    label: role.name,
    onClick: () => handleRoleSwitch(role.id),
  })) || [];

  // 生成面包屑
  const generateBreadcrumb = () => {
    const pathSnippets = location.pathname.split('/').filter(i => i);
    const breadcrumbItems = [
      {
        title: '首页',
      },
    ];

    pathSnippets.forEach((snippet, index) => {
      const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
      const menuItem = findMenuItemByKey(menuItems, url);
      if (menuItem) {
        breadcrumbItems.push({
          title: menuItem.label as string,
        });
      }
    });

    return breadcrumbItems;
  };

  // 查找菜单项
  const findMenuItemByKey = (items: any[], key: string): any => {
    for (const item of items) {
      if (item.key === key) {
        return item;
      }
      if (item.children) {
        const found = findMenuItemByKey(item.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold',
        }}>
          {collapsed ? 'LBS' : 'Link-BOM-S'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          padding: '0 16px', 
          background: colorBgContainer,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space size="middle">
            {/* 角色切换 */}
            {user?.roles && user.roles.length > 1 && (
              <Dropdown
                menu={{ items: roleMenuItems }}
                placement="bottomRight"
              >
                <Button type="text" icon={<SwitcherOutlined />}>
                  {currentRole?.name}
                </Button>
              </Dropdown>
            )}
            
            {/* 通知 */}
            <Badge count={5}>
              <Button type="text" icon={<BellOutlined />} />
            </Badge>
            
            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems, onClick: handleUserMenuClick }}
              placement="bottomRight"
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} src={user?.avatar} />
                <span>{user?.name}</span>
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        <Content style={{ margin: '16px' }}>
          <Breadcrumb
            style={{ marginBottom: 16 }}
            items={generateBreadcrumb()}
          />
          
          <div
            style={{
              padding: 24,
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: 6,
            }}
          >
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
