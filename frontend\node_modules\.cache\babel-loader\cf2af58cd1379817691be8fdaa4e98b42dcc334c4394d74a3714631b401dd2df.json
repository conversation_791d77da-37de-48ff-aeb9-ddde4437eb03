{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\auth\\\\LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Form, Input, Button, Card, Typography, Space, Alert, Checkbox, Divider, Row, Col } from 'antd';\nimport { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { login, clearError } from '../../store/slices/authSlice';\nimport { ROUTES } from '../../constants';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst LoginPage = () => {\n  _s();\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const {\n    loading,\n    error,\n    isAuthenticated\n  } = useAppSelector(state => state.auth);\n\n  // 如果已经登录，重定向到目标页面或仪表板\n  useEffect(() => {\n    if (isAuthenticated) {\n      var _location$state, _location$state$from;\n      const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || ROUTES.DASHBOARD;\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate, location]);\n\n  // 清除错误信息\n  useEffect(() => {\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n\n  // 处理登录\n  const handleLogin = async values => {\n    try {\n      await dispatch(login({\n        username: values.username,\n        password: values.password\n      })).unwrap();\n\n      // 登录成功后会通过useEffect重定向\n    } catch (error) {\n      // 使用新的错误处理器\n      errorHandler.handleError({\n        type: ErrorType.BUSINESS,\n        message: error.message || '登录失败，请检查用户名和密码',\n        details: error\n      });\n    }\n  };\n\n  // 演示账号登录\n  const handleDemoLogin = role => {\n    const demoAccounts = {\n      admin: {\n        username: 'admin',\n        password: 'admin123'\n      },\n      bom_manager: {\n        username: 'bom_manager',\n        password: 'bom123'\n      },\n      sales_pmc: {\n        username: 'sales_pmc',\n        password: 'sales123'\n      },\n      purchase_manager: {\n        username: 'purchase_manager',\n        password: 'purchase123'\n      },\n      warehouse_manager: {\n        username: 'warehouse_manager',\n        password: 'warehouse123'\n      },\n      finance_manager: {\n        username: 'finance_manager',\n        password: 'finance123'\n      },\n      service_technician: {\n        username: 'service_tech',\n        password: 'service123'\n      },\n      operator: {\n        username: 'operator',\n        password: 'operator123'\n      }\n    };\n    const account = demoAccounts[role];\n    if (account) {\n      form.setFieldsValue(account);\n      handleLogin({\n        ...account,\n        remember: false\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [32, 32],\n      style: {\n        width: '100%',\n        maxWidth: 1200\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: 'white',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 1,\n            style: {\n              color: 'white',\n              marginBottom: 24\n            },\n            children: \"Link-BOM-S\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 3,\n            style: {\n              color: 'white',\n              fontWeight: 'normal',\n              marginBottom: 32\n            },\n            children: \"\\u5929\\u7EBF\\u884C\\u4E1ABOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"large\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                color: 'rgba(255,255,255,0.9)',\n                fontSize: 16\n              },\n              children: \"\\u4E13\\u4E3A\\u5929\\u7EBF\\u884C\\u4E1A\\u5C0F\\u5FAE\\u5236\\u9020\\u4F01\\u4E1A\\u8BBE\\u8BA1\\u7684\\u7EFC\\u5408BOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'left',\n                maxWidth: 400,\n                margin: '0 auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: 'rgba(255,255,255,0.8)',\n                  display: 'block',\n                  marginBottom: 8\n                },\n                children: \"\\u2713 \\u6838\\u5FC3BOM\\uFF08150%\\uFF09+ \\u8BA2\\u5355\\u5FEB\\u901F\\u6D3E\\u751F\\uFF08100%\\uFF09\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: 'rgba(255,255,255,0.8)',\n                  display: 'block',\n                  marginBottom: 8\n                },\n                children: \"\\u2713 \\u6309\\u5355\\u5408\\u5355\\u91C7\\u8D2D + \\u5305\\u88C5/MOQ\\u53D6\\u6574\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: 'rgba(255,255,255,0.8)',\n                  display: 'block',\n                  marginBottom: 8\n                },\n                children: \"\\u2713 \\u957F\\u5EA6\\u578B\\u5207\\u5272\\u4F18\\u5316 + \\u4F59\\u6599\\u53F0\\u8D26\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: 'rgba(255,255,255,0.8)',\n                  display: 'block',\n                  marginBottom: 8\n                },\n                children: \"\\u2713 \\u670D\\u52A1BOM + \\u6210\\u672C\\u900F\\u660E\\u5EA6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          style: {\n            maxWidth: 400,\n            margin: '0 auto',\n            borderRadius: 12,\n            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginBottom: 32\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 3,\n              style: {\n                marginBottom: 8\n              },\n              children: \"\\u7528\\u6237\\u767B\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u8BF7\\u8F93\\u5165\\u60A8\\u7684\\u8D26\\u53F7\\u548C\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n            message: error,\n            type: \"error\",\n            showIcon: true,\n            style: {\n              marginBottom: 24\n            },\n            closable: true,\n            onClose: () => dispatch(clearError())\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            name: \"login\",\n            onFinish: handleLogin,\n            autoComplete: \"off\",\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 3,\n                message: '用户名至少3个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u7528\\u6237\\u540D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                message: '密码至少6个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u5BC6\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"remember\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                children: \"\\u8BB0\\u4F4F\\u6211\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                icon: /*#__PURE__*/_jsxDEV(LoginOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 25\n                }, this),\n                block: true,\n                children: \"\\u767B\\u5F55\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            children: \"\\u6F14\\u793A\\u8D26\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: [8, 8],\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  block: true,\n                  onClick: () => handleDemoLogin('admin'),\n                  children: \"\\u7CFB\\u7EDF\\u7BA1\\u7406\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  block: true,\n                  onClick: () => handleDemoLogin('bom_manager'),\n                  children: \"BOM\\u7BA1\\u7406\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: [8, 8],\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  block: true,\n                  onClick: () => handleDemoLogin('sales_pmc'),\n                  children: \"\\u9500\\u552E/PMC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  block: true,\n                  onClick: () => handleDemoLogin('purchase_manager'),\n                  children: \"\\u91C7\\u8D2D\\u7ECF\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: [8, 8],\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  block: true,\n                  onClick: () => handleDemoLogin('warehouse_manager'),\n                  children: \"\\u4ED3\\u5E93\\u7ECF\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  block: true,\n                  onClick: () => handleDemoLogin('finance_manager'),\n                  children: \"\\u8D22\\u52A1\\u7ECF\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: [8, 8],\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  block: true,\n                  onClick: () => handleDemoLogin('service_technician'),\n                  children: \"\\u670D\\u52A1\\u6280\\u672F\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  block: true,\n                  onClick: () => handleDemoLogin('operator'),\n                  children: \"\\u64CD\\u4F5C\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginTop: 24\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                fontSize: 12\n              },\n              children: \"\\xA9 2024 Link-BOM-S. \\u5929\\u7EBF\\u884C\\u4E1ABOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"uBQjfJ27qGa1B09nmWXAOPNNFes=\", false, function () {\n  return [Form.useForm, useNavigate, useLocation, useAppDispatch, useAppSelector];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useLocation", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "Space", "<PERSON><PERSON>", "Checkbox", "Divider", "Row", "Col", "UserOutlined", "LockOutlined", "LoginOutlined", "useAppDispatch", "useAppSelector", "login", "clearError", "ROUTES", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "jsxDEV", "_jsxDEV", "Title", "Text", "LoginPage", "_s", "form", "useForm", "navigate", "location", "dispatch", "loading", "error", "isAuthenticated", "state", "auth", "_location$state", "_location$state$from", "from", "pathname", "DASHBOARD", "replace", "handleLogin", "values", "username", "password", "unwrap", "handleError", "type", "BUSINESS", "message", "details", "handleDemoLogin", "role", "demoAccounts", "admin", "bom_manager", "sales_pmc", "purchase_manager", "warehouse_manager", "finance_manager", "service_technician", "operator", "account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remember", "style", "minHeight", "background", "display", "alignItems", "justifyContent", "padding", "children", "gutter", "width", "max<PERSON><PERSON><PERSON>", "xs", "lg", "color", "textAlign", "level", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "direction", "size", "fontSize", "margin", "borderRadius", "boxShadow", "showIcon", "closable", "onClose", "name", "onFinish", "autoComplete", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "Password", "valuePropName", "htmlType", "icon", "block", "span", "onClick", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/auth/LoginPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Form,\n  Input,\n  Button,\n  Card,\n  Typography,\n  Space,\n  Alert,\n  Checkbox,\n  Divider,\n  Row,\n  Col,\n} from 'antd';\nimport {\n  UserOutlined,\n  LockOutlined,\n  LoginOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { login, clearError } from '../../store/slices/authSlice';\nimport { ROUTES } from '../../constants';\nimport { LoadingSpinner } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title, Text } = Typography;\n\ninterface LoginFormData {\n  username: string;\n  password: string;\n  remember: boolean;\n}\n\nconst LoginPage: React.FC = () => {\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const { loading, error, isAuthenticated } = useAppSelector(state => state.auth);\n\n  // 如果已经登录，重定向到目标页面或仪表板\n  useEffect(() => {\n    if (isAuthenticated) {\n      const from = (location.state as any)?.from?.pathname || ROUTES.DASHBOARD;\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, navigate, location]);\n\n  // 清除错误信息\n  useEffect(() => {\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n\n  // 处理登录\n  const handleLogin = async (values: LoginFormData) => {\n    try {\n      await dispatch(login({\n        username: values.username,\n        password: values.password,\n      })).unwrap();\n      \n      // 登录成功后会通过useEffect重定向\n    } catch (error: any) {\n      // 使用新的错误处理器\n      errorHandler.handleError({\n        type: ErrorType.BUSINESS,\n        message: error.message || '登录失败，请检查用户名和密码',\n        details: error\n      });\n    }\n  };\n\n  // 演示账号登录\n  const handleDemoLogin = (role: string) => {\n    const demoAccounts = {\n      admin: { username: 'admin', password: 'admin123' },\n      bom_manager: { username: 'bom_manager', password: 'bom123' },\n      sales_pmc: { username: 'sales_pmc', password: 'sales123' },\n      purchase_manager: { username: 'purchase_manager', password: 'purchase123' },\n      warehouse_manager: { username: 'warehouse_manager', password: 'warehouse123' },\n      finance_manager: { username: 'finance_manager', password: 'finance123' },\n      service_technician: { username: 'service_tech', password: 'service123' },\n      operator: { username: 'operator', password: 'operator123' },\n    };\n\n    const account = demoAccounts[role as keyof typeof demoAccounts];\n    if (account) {\n      form.setFieldsValue(account);\n      handleLogin({ ...account, remember: false });\n    }\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px',\n    }}>\n      <Row gutter={[32, 32]} style={{ width: '100%', maxWidth: 1200 }}>\n        {/* 左侧：系统介绍 */}\n        <Col xs={24} lg={12}>\n          <div style={{ color: 'white', textAlign: 'center' }}>\n            <Title level={1} style={{ color: 'white', marginBottom: 24 }}>\n              Link-BOM-S\n            </Title>\n            <Title level={3} style={{ color: 'white', fontWeight: 'normal', marginBottom: 32 }}>\n              天线行业BOM管理系统\n            </Title>\n            <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n              <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: 16 }}>\n                专为天线行业小微制造企业设计的综合BOM管理系统\n              </Text>\n              <div style={{ textAlign: 'left', maxWidth: 400, margin: '0 auto' }}>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 核心BOM（150%）+ 订单快速派生（100%）\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 按单合单采购 + 包装/MOQ取整\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 长度型切割优化 + 余料台账\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 服务BOM + 成本透明度\n                </Text>\n              </div>\n            </Space>\n          </div>\n        </Col>\n\n        {/* 右侧：登录表单 */}\n        <Col xs={24} lg={12}>\n          <Card\n            style={{\n              maxWidth: 400,\n              margin: '0 auto',\n              borderRadius: 12,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n            }}\n          >\n            <div style={{ textAlign: 'center', marginBottom: 32 }}>\n              <Title level={3} style={{ marginBottom: 8 }}>\n                用户登录\n              </Title>\n              <Text type=\"secondary\">\n                请输入您的账号和密码\n              </Text>\n            </div>\n\n            {error && (\n              <Alert\n                message={error}\n                type=\"error\"\n                showIcon\n                style={{ marginBottom: 24 }}\n                closable\n                onClose={() => dispatch(clearError())}\n              />\n            )}\n\n            <Form\n              form={form}\n              name=\"login\"\n              onFinish={handleLogin}\n              autoComplete=\"off\"\n              size=\"large\"\n            >\n              <Form.Item\n                name=\"username\"\n                rules={[\n                  { required: true, message: '请输入用户名' },\n                  { min: 3, message: '用户名至少3个字符' },\n                ]}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  placeholder=\"用户名\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"password\"\n                rules={[\n                  { required: true, message: '请输入密码' },\n                  { min: 6, message: '密码至少6个字符' },\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"密码\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"remember\" valuePropName=\"checked\">\n                <Checkbox>记住我</Checkbox>\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<LoginOutlined />}\n                  block\n                >\n                  登录\n                </Button>\n              </Form.Item>\n            </Form>\n\n            <Divider>演示账号</Divider>\n\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"small\">\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('admin')}\n                  >\n                    系统管理员\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('bom_manager')}\n                  >\n                    BOM管理员\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('sales_pmc')}\n                  >\n                    销售/PMC\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('purchase_manager')}\n                  >\n                    采购经理\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('warehouse_manager')}\n                  >\n                    仓库经理\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('finance_manager')}\n                  >\n                    财务经理\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('service_technician')}\n                  >\n                    服务技术员\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('operator')}\n                  >\n                    操作员\n                  </Button>\n                </Col>\n              </Row>\n            </Space>\n\n            <div style={{ textAlign: 'center', marginTop: 24 }}>\n              <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                © 2024 Link-BOM-S. 天线行业BOM管理系统\n              </Text>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,aAAa,QACR,mBAAmB;AAE1B,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,KAAK,EAAEC,UAAU,QAAQ,8BAA8B;AAChE,SAASC,MAAM,QAAQ,iBAAiB;AAExC,SAASC,YAAY,EAAEC,SAAS,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAQlC,MAAMqB,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAG3B,IAAI,CAAC4B,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAGjB,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEkB,OAAO;IAAEC,KAAK;IAAEC;EAAgB,CAAC,GAAGnB,cAAc,CAACoB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;;EAE/E;EACAvC,SAAS,CAAC,MAAM;IACd,IAAIqC,eAAe,EAAE;MAAA,IAAAG,eAAA,EAAAC,oBAAA;MACnB,MAAMC,IAAI,GAAG,EAAAF,eAAA,GAACP,QAAQ,CAACK,KAAK,cAAAE,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBE,IAAI,cAAAD,oBAAA,uBAA7BA,oBAAA,CAA+BE,QAAQ,KAAItB,MAAM,CAACuB,SAAS;MACxEZ,QAAQ,CAACU,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACR,eAAe,EAAEL,QAAQ,EAAEC,QAAQ,CAAC,CAAC;;EAEzC;EACAjC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXkC,QAAQ,CAACd,UAAU,CAAC,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACc,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMY,WAAW,GAAG,MAAOC,MAAqB,IAAK;IACnD,IAAI;MACF,MAAMb,QAAQ,CAACf,KAAK,CAAC;QACnB6B,QAAQ,EAAED,MAAM,CAACC,QAAQ;QACzBC,QAAQ,EAAEF,MAAM,CAACE;MACnB,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;;MAEZ;IACF,CAAC,CAAC,OAAOd,KAAU,EAAE;MACnB;MACAd,YAAY,CAAC6B,WAAW,CAAC;QACvBC,IAAI,EAAE7B,SAAS,CAAC8B,QAAQ;QACxBC,OAAO,EAAElB,KAAK,CAACkB,OAAO,IAAI,gBAAgB;QAC1CC,OAAO,EAAEnB;MACX,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMoB,eAAe,GAAIC,IAAY,IAAK;IACxC,MAAMC,YAAY,GAAG;MACnBC,KAAK,EAAE;QAAEX,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAW,CAAC;MAClDW,WAAW,EAAE;QAAEZ,QAAQ,EAAE,aAAa;QAAEC,QAAQ,EAAE;MAAS,CAAC;MAC5DY,SAAS,EAAE;QAAEb,QAAQ,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAW,CAAC;MAC1Da,gBAAgB,EAAE;QAAEd,QAAQ,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAc,CAAC;MAC3Ec,iBAAiB,EAAE;QAAEf,QAAQ,EAAE,mBAAmB;QAAEC,QAAQ,EAAE;MAAe,CAAC;MAC9Ee,eAAe,EAAE;QAAEhB,QAAQ,EAAE,iBAAiB;QAAEC,QAAQ,EAAE;MAAa,CAAC;MACxEgB,kBAAkB,EAAE;QAAEjB,QAAQ,EAAE,cAAc;QAAEC,QAAQ,EAAE;MAAa,CAAC;MACxEiB,QAAQ,EAAE;QAAElB,QAAQ,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAc;IAC5D,CAAC;IAED,MAAMkB,OAAO,GAAGT,YAAY,CAACD,IAAI,CAA8B;IAC/D,IAAIU,OAAO,EAAE;MACXrC,IAAI,CAACsC,cAAc,CAACD,OAAO,CAAC;MAC5BrB,WAAW,CAAC;QAAE,GAAGqB,OAAO;QAAEE,QAAQ,EAAE;MAAM,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,oBACE5C,OAAA;IAAK6C,KAAK,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eACApD,OAAA,CAACb,GAAG;MAACkE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACR,KAAK,EAAE;QAAES,KAAK,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAK,CAAE;MAAAH,QAAA,gBAE9DpD,OAAA,CAACZ,GAAG;QAACoE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAL,QAAA,eAClBpD,OAAA;UAAK6C,KAAK,EAAE;YAAEa,KAAK,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAP,QAAA,gBAClDpD,OAAA,CAACC,KAAK;YAAC2D,KAAK,EAAE,CAAE;YAACf,KAAK,EAAE;cAAEa,KAAK,EAAE,OAAO;cAAEG,YAAY,EAAE;YAAG,CAAE;YAAAT,QAAA,EAAC;UAE9D;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjE,OAAA,CAACC,KAAK;YAAC2D,KAAK,EAAE,CAAE;YAACf,KAAK,EAAE;cAAEa,KAAK,EAAE,OAAO;cAAEQ,UAAU,EAAE,QAAQ;cAAEL,YAAY,EAAE;YAAG,CAAE;YAAAT,QAAA,EAAC;UAEpF;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjE,OAAA,CAACjB,KAAK;YAACoF,SAAS,EAAC,UAAU;YAACC,IAAI,EAAC,OAAO;YAACvB,KAAK,EAAE;cAAES,KAAK,EAAE;YAAO,CAAE;YAAAF,QAAA,gBAChEpD,OAAA,CAACE,IAAI;cAAC2C,KAAK,EAAE;gBAAEa,KAAK,EAAE,uBAAuB;gBAAEW,QAAQ,EAAE;cAAG,CAAE;cAAAjB,QAAA,EAAC;YAE/D;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPjE,OAAA;cAAK6C,KAAK,EAAE;gBAAEc,SAAS,EAAE,MAAM;gBAAEJ,QAAQ,EAAE,GAAG;gBAAEe,MAAM,EAAE;cAAS,CAAE;cAAAlB,QAAA,gBACjEpD,OAAA,CAACE,IAAI;gBAAC2C,KAAK,EAAE;kBAAEa,KAAK,EAAE,uBAAuB;kBAAEV,OAAO,EAAE,OAAO;kBAAEa,YAAY,EAAE;gBAAE,CAAE;gBAAAT,QAAA,EAAC;cAEpF;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjE,OAAA,CAACE,IAAI;gBAAC2C,KAAK,EAAE;kBAAEa,KAAK,EAAE,uBAAuB;kBAAEV,OAAO,EAAE,OAAO;kBAAEa,YAAY,EAAE;gBAAE,CAAE;gBAAAT,QAAA,EAAC;cAEpF;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjE,OAAA,CAACE,IAAI;gBAAC2C,KAAK,EAAE;kBAAEa,KAAK,EAAE,uBAAuB;kBAAEV,OAAO,EAAE,OAAO;kBAAEa,YAAY,EAAE;gBAAE,CAAE;gBAAAT,QAAA,EAAC;cAEpF;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjE,OAAA,CAACE,IAAI;gBAAC2C,KAAK,EAAE;kBAAEa,KAAK,EAAE,uBAAuB;kBAAEV,OAAO,EAAE,OAAO;kBAAEa,YAAY,EAAE;gBAAE,CAAE;gBAAAT,QAAA,EAAC;cAEpF;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjE,OAAA,CAACZ,GAAG;QAACoE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAL,QAAA,eAClBpD,OAAA,CAACnB,IAAI;UACHgE,KAAK,EAAE;YACLU,QAAQ,EAAE,GAAG;YACbe,MAAM,EAAE,QAAQ;YAChBC,YAAY,EAAE,EAAE;YAChBC,SAAS,EAAE;UACb,CAAE;UAAApB,QAAA,gBAEFpD,OAAA;YAAK6C,KAAK,EAAE;cAAEc,SAAS,EAAE,QAAQ;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAAT,QAAA,gBACpDpD,OAAA,CAACC,KAAK;cAAC2D,KAAK,EAAE,CAAE;cAACf,KAAK,EAAE;gBAAEgB,YAAY,EAAE;cAAE,CAAE;cAAAT,QAAA,EAAC;YAE7C;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA,CAACE,IAAI;cAACyB,IAAI,EAAC,WAAW;cAAAyB,QAAA,EAAC;YAEvB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELtD,KAAK,iBACJX,OAAA,CAAChB,KAAK;YACJ6C,OAAO,EAAElB,KAAM;YACfgB,IAAI,EAAC,OAAO;YACZ8C,QAAQ;YACR5B,KAAK,EAAE;cAAEgB,YAAY,EAAE;YAAG,CAAE;YAC5Ba,QAAQ;YACRC,OAAO,EAAEA,CAAA,KAAMlE,QAAQ,CAACd,UAAU,CAAC,CAAC;UAAE;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACF,eAEDjE,OAAA,CAACtB,IAAI;YACH2B,IAAI,EAAEA,IAAK;YACXuE,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAExD,WAAY;YACtByD,YAAY,EAAC,KAAK;YAClBV,IAAI,EAAC,OAAO;YAAAhB,QAAA,gBAEZpD,OAAA,CAACtB,IAAI,CAACqG,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpD,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAEqD,GAAG,EAAE,CAAC;gBAAErD,OAAO,EAAE;cAAY,CAAC,CAChC;cAAAuB,QAAA,eAEFpD,OAAA,CAACrB,KAAK;gBACJwG,MAAM,eAAEnF,OAAA,CAACX,YAAY;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBmB,WAAW,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZjE,OAAA,CAACtB,IAAI,CAACqG,IAAI;cACRH,IAAI,EAAC,UAAU;cACfI,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpD,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAEqD,GAAG,EAAE,CAAC;gBAAErD,OAAO,EAAE;cAAW,CAAC,CAC/B;cAAAuB,QAAA,eAEFpD,OAAA,CAACrB,KAAK,CAAC0G,QAAQ;gBACbF,MAAM,eAAEnF,OAAA,CAACV,YAAY;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBmB,WAAW,EAAC;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZjE,OAAA,CAACtB,IAAI,CAACqG,IAAI;cAACH,IAAI,EAAC,UAAU;cAACU,aAAa,EAAC,SAAS;cAAAlC,QAAA,eAChDpD,OAAA,CAACf,QAAQ;gBAAAmE,QAAA,EAAC;cAAG;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEZjE,OAAA,CAACtB,IAAI,CAACqG,IAAI;cAAA3B,QAAA,eACRpD,OAAA,CAACpB,MAAM;gBACL+C,IAAI,EAAC,SAAS;gBACd4D,QAAQ,EAAC,QAAQ;gBACjB7E,OAAO,EAAEA,OAAQ;gBACjB8E,IAAI,eAAExF,OAAA,CAACT,aAAa;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxBwB,KAAK;gBAAArC,QAAA,EACN;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEPjE,OAAA,CAACd,OAAO;YAAAkE,QAAA,EAAC;UAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAEvBjE,OAAA,CAACjB,KAAK;YAACoF,SAAS,EAAC,UAAU;YAACtB,KAAK,EAAE;cAAES,KAAK,EAAE;YAAO,CAAE;YAACc,IAAI,EAAC,OAAO;YAAAhB,QAAA,gBAChEpD,OAAA,CAACb,GAAG;cAACkE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;cAAAD,QAAA,gBAClBpD,OAAA,CAACZ,GAAG;gBAACsG,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZpD,OAAA,CAACpB,MAAM;kBACLwF,IAAI,EAAC,OAAO;kBACZqB,KAAK;kBACLE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,OAAO,CAAE;kBAAAqB,QAAA,EACzC;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNjE,OAAA,CAACZ,GAAG;gBAACsG,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZpD,OAAA,CAACpB,MAAM;kBACLwF,IAAI,EAAC,OAAO;kBACZqB,KAAK;kBACLE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,aAAa,CAAE;kBAAAqB,QAAA,EAC/C;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjE,OAAA,CAACb,GAAG;cAACkE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;cAAAD,QAAA,gBAClBpD,OAAA,CAACZ,GAAG;gBAACsG,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZpD,OAAA,CAACpB,MAAM;kBACLwF,IAAI,EAAC,OAAO;kBACZqB,KAAK;kBACLE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,WAAW,CAAE;kBAAAqB,QAAA,EAC7C;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNjE,OAAA,CAACZ,GAAG;gBAACsG,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZpD,OAAA,CAACpB,MAAM;kBACLwF,IAAI,EAAC,OAAO;kBACZqB,KAAK;kBACLE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,kBAAkB,CAAE;kBAAAqB,QAAA,EACpD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjE,OAAA,CAACb,GAAG;cAACkE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;cAAAD,QAAA,gBAClBpD,OAAA,CAACZ,GAAG;gBAACsG,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZpD,OAAA,CAACpB,MAAM;kBACLwF,IAAI,EAAC,OAAO;kBACZqB,KAAK;kBACLE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,mBAAmB,CAAE;kBAAAqB,QAAA,EACrD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNjE,OAAA,CAACZ,GAAG;gBAACsG,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZpD,OAAA,CAACpB,MAAM;kBACLwF,IAAI,EAAC,OAAO;kBACZqB,KAAK;kBACLE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,iBAAiB,CAAE;kBAAAqB,QAAA,EACnD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjE,OAAA,CAACb,GAAG;cAACkE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;cAAAD,QAAA,gBAClBpD,OAAA,CAACZ,GAAG;gBAACsG,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZpD,OAAA,CAACpB,MAAM;kBACLwF,IAAI,EAAC,OAAO;kBACZqB,KAAK;kBACLE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,oBAAoB,CAAE;kBAAAqB,QAAA,EACtD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNjE,OAAA,CAACZ,GAAG;gBAACsG,IAAI,EAAE,EAAG;gBAAAtC,QAAA,eACZpD,OAAA,CAACpB,MAAM;kBACLwF,IAAI,EAAC,OAAO;kBACZqB,KAAK;kBACLE,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,UAAU,CAAE;kBAAAqB,QAAA,EAC5C;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAERjE,OAAA;YAAK6C,KAAK,EAAE;cAAEc,SAAS,EAAE,QAAQ;cAAEiC,SAAS,EAAE;YAAG,CAAE;YAAAxC,QAAA,eACjDpD,OAAA,CAACE,IAAI;cAACyB,IAAI,EAAC,WAAW;cAACkB,KAAK,EAAE;gBAAEwB,QAAQ,EAAE;cAAG,CAAE;cAAAjB,QAAA,EAAC;YAEhD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CArRID,SAAmB;EAAA,QACRzB,IAAI,CAAC4B,OAAO,EACV9B,WAAW,EACXC,WAAW,EACXe,cAAc,EACaC,cAAc;AAAA;AAAAoG,EAAA,GALtD1F,SAAmB;AAuRzB,eAAeA,SAAS;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}