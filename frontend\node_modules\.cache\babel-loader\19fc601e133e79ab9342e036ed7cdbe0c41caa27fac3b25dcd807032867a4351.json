{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _objectWithoutProperties from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"showText\",\"text\",\"fullscreen\",\"overlay\",\"size\"];import React from'react';import{Spin}from'antd';import{LoadingOutlined}from'@ant-design/icons';import'./LoadingSpinner.css';import{jsx as _jsx}from\"react/jsx-runtime\";const LoadingSpinner=_ref=>{let{showText=true,text='加载中...',fullscreen=false,overlay=false,size='default'}=_ref,props=_objectWithoutProperties(_ref,_excluded);const antIcon=/*#__PURE__*/_jsx(LoadingOutlined,{style:{fontSize:size==='large'?24:16},spin:true});const spinner=/*#__PURE__*/_jsx(Spin,_objectSpread({indicator:antIcon,tip:showText?text:undefined,size:size},props));if(fullscreen){return/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner-fullscreen \".concat(overlay?'with-overlay':''),children:spinner});}return/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner-container\",children:spinner});};export default LoadingSpinner;", "map": {"version": 3, "names": ["React", "Spin", "LoadingOutlined", "jsx", "_jsx", "LoadingSpinner", "_ref", "showText", "text", "fullscreen", "overlay", "size", "props", "_objectWithoutProperties", "_excluded", "antIcon", "style", "fontSize", "spin", "spinner", "_objectSpread", "indicator", "tip", "undefined", "className", "concat", "children"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { Spin, SpinProps } from 'antd';\nimport { LoadingOutlined } from '@ant-design/icons';\nimport './LoadingSpinner.css';\n\ninterface LoadingSpinnerProps extends SpinProps {\n  /** 是否显示加载文本 */\n  showText?: boolean;\n  /** 自定义加载文本 */\n  text?: string;\n  /** 是否全屏显示 */\n  fullscreen?: boolean;\n  /** 是否显示遮罩层 */\n  overlay?: boolean;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  showText = true,\n  text = '加载中...',\n  fullscreen = false,\n  overlay = false,\n  size = 'default',\n  ...props\n}) => {\n  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 24 : 16 }} spin />;\n\n  const spinner = (\n    <Spin\n      indicator={antIcon}\n      tip={showText ? text : undefined}\n      size={size}\n      {...props}\n    />\n  );\n\n  if (fullscreen) {\n    return (\n      <div className={`loading-spinner-fullscreen ${overlay ? 'with-overlay' : ''}`}>\n        {spinner}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"loading-spinner-container\">\n      {spinner}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;"], "mappings": "uUAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAmB,MAAM,CACtC,OAASC,eAAe,KAAQ,mBAAmB,CACnD,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAa9B,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAOhD,IAPiD,CACrDC,QAAQ,CAAG,IAAI,CACfC,IAAI,CAAG,QAAQ,CACfC,UAAU,CAAG,KAAK,CAClBC,OAAO,CAAG,KAAK,CACfC,IAAI,CAAG,SAET,CAAC,CAAAL,IAAA,CADIM,KAAK,CAAAC,wBAAA,CAAAP,IAAA,CAAAQ,SAAA,EAER,KAAM,CAAAC,OAAO,cAAGX,IAAA,CAACF,eAAe,EAACc,KAAK,CAAE,CAAEC,QAAQ,CAAEN,IAAI,GAAK,OAAO,CAAG,EAAE,CAAG,EAAG,CAAE,CAACO,IAAI,MAAE,CAAC,CAEzF,KAAM,CAAAC,OAAO,cACXf,IAAA,CAACH,IAAI,CAAAmB,aAAA,EACHC,SAAS,CAAEN,OAAQ,CACnBO,GAAG,CAAEf,QAAQ,CAAGC,IAAI,CAAGe,SAAU,CACjCZ,IAAI,CAAEA,IAAK,EACPC,KAAK,CACV,CACF,CAED,GAAIH,UAAU,CAAE,CACd,mBACEL,IAAA,QAAKoB,SAAS,+BAAAC,MAAA,CAAgCf,OAAO,CAAG,cAAc,CAAG,EAAE,CAAG,CAAAgB,QAAA,CAC3EP,OAAO,CACL,CAAC,CAEV,CAEA,mBACEf,IAAA,QAAKoB,SAAS,CAAC,2BAA2B,CAAAE,QAAA,CACvCP,OAAO,CACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAd,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}