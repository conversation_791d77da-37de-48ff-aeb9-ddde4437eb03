{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useNavigate}from'react-router-dom';import{Table,Button,Space,Input,Select,Card,Tag,Popconfirm,message,Modal,Form,Typography,Row,Col,Tooltip,Dropdown,Descriptions,Checkbox}from'antd';import*as XLSX from'xlsx';import{PlusOutlined,EditOutlined,DeleteOutlined,EyeOutlined,MoreOutlined,ExportOutlined,ReloadOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const{Search}=Input;const{Option}=Select;const{TextArea}=Input;const PermissionListPage=()=>{const navigate=useNavigate();const[permissions,setPermissions]=useState([]);const[loading,setLoading]=useState(false);const[searchKeyword,setSearchKeyword]=useState('');const[resourceFilter,setResourceFilter]=useState('');const[actionFilter,setActionFilter]=useState('');const[permissionModalVisible,setPermissionModalVisible]=useState(false);const[detailModalVisible,setDetailModalVisible]=useState(false);const[editingPermission,setEditingPermission]=useState(null);const[selectedPermission,setSelectedPermission]=useState(null);const[permissionForm]=Form.useForm();const[exportModalVisible,setExportModalVisible]=useState(false);const[exportFormat,setExportFormat]=useState('excel');const[exportFields,setExportFields]=useState(['name','code','resource','action','description']);useEffect(()=>{loadData();},[searchKeyword,resourceFilter,actionFilter]);const loadData=async()=>{setLoading(true);try{// TODO: 实现API调用\n// 模拟数据\nconst mockPermissions=[{id:'1',name:'BOM查看',code:'BOM_VIEW',resource:'BOM',action:'VIEW',description:'BOM查看权限，允许用户查看BOM信息'},{id:'2',name:'BOM创建',code:'BOM_CREATE',resource:'BOM',action:'CREATE',description:'BOM创建权限，允许用户创建新的BOM'},{id:'3',name:'BOM编辑',code:'BOM_EDIT',resource:'BOM',action:'EDIT',description:'BOM编辑权限，允许用户编辑现有BOM'},{id:'4',name:'BOM删除',code:'BOM_DELETE',resource:'BOM',action:'DELETE',description:'BOM删除权限，允许用户删除BOM'},{id:'5',name:'物料查看',code:'MATERIAL_VIEW',resource:'MATERIAL',action:'VIEW',description:'物料查看权限，允许用户查看物料信息'},{id:'6',name:'物料创建',code:'MATERIAL_CREATE',resource:'MATERIAL',action:'CREATE',description:'物料创建权限，允许用户创建新物料'},{id:'7',name:'物料编辑',code:'MATERIAL_EDIT',resource:'MATERIAL',action:'EDIT',description:'物料编辑权限，允许用户编辑物料信息'},{id:'8',name:'用户管理',code:'USER_MANAGE',resource:'USER',action:'MANAGE',description:'用户管理权限，允许管理系统用户'},{id:'9',name:'角色管理',code:'ROLE_MANAGE',resource:'ROLE',action:'MANAGE',description:'角色管理权限，允许管理系统角色'},{id:'10',name:'系统配置',code:'SYSTEM_CONFIG',resource:'SYSTEM',action:'CONFIG',description:'系统配置权限，允许配置系统参数'},{id:'11',name:'库存查看',code:'INVENTORY_VIEW',resource:'INVENTORY',action:'VIEW',description:'库存查看权限，允许查看库存信息'},{id:'12',name:'采购管理',code:'PURCHASE_MANAGE',resource:'PURCHASE',action:'MANAGE',description:'采购管理权限，允许管理采购流程'}];setPermissions(mockPermissions);}catch(error){message.error('加载权限列表失败');}finally{setLoading(false);}};const handleSearch=value=>{setSearchKeyword(value);};const handleResourceFilter=value=>{setResourceFilter(value);};const handleActionFilter=value=>{setActionFilter(value);};const handleCreate=()=>{setEditingPermission(null);permissionForm.resetFields();setPermissionModalVisible(true);};const handleEdit=record=>{setEditingPermission(record);permissionForm.setFieldsValue(record);setPermissionModalVisible(true);};const handleView=record=>{setSelectedPermission(record);setDetailModalVisible(true);};const handleDelete=async record=>{try{// TODO: 实现删除API\nmessage.success('删除成功');loadData();}catch(error){message.error('删除失败');}};const handleModalOk=async()=>{try{const values=await permissionForm.validateFields();if(editingPermission){// TODO: 实现更新API\nmessage.success('更新成功');}else{// TODO: 实现创建API\nmessage.success('创建成功');}setPermissionModalVisible(false);loadData();}catch(error){message.error('操作失败');}};const handleExport=()=>{setExportModalVisible(true);};const executeExport=()=>{try{// 准备导出数据\nconst exportData=permissions.map(permission=>{const data={};if(exportFields.includes('name'))data['权限名称']=permission.name;if(exportFields.includes('code'))data['权限编码']=permission.code;if(exportFields.includes('resource'))data['资源']=permission.resource;if(exportFields.includes('action'))data['操作']=permission.action;if(exportFields.includes('description'))data['描述']=permission.description||'';return data;});// 创建工作簿\nconst ws=XLSX.utils.json_to_sheet(exportData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'权限列表');// 下载文件\nconst fileName=\"\\u6743\\u9650\\u5217\\u8868_\".concat(new Date().toISOString().split('T')[0],\".\").concat(exportFormat==='excel'?'xlsx':'csv');XLSX.writeFile(wb,fileName);message.success('导出成功');setExportModalVisible(false);}catch(error){message.error('导出失败');}};const getActionMenuItems=record=>[{key:'export',icon:/*#__PURE__*/_jsx(ExportOutlined,{}),label:'导出信息',onClick:()=>{setExportModalVisible(true);}}];const getResourceColor=resource=>{const colorMap={BOM:'blue',MATERIAL:'green',USER:'orange',ROLE:'purple',SYSTEM:'red',INVENTORY:'cyan',PURCHASE:'magenta'};return colorMap[resource]||'default';};const getActionColor=action=>{const colorMap={VIEW:'blue',CREATE:'green',EDIT:'orange',DELETE:'red',MANAGE:'purple',CONFIG:'magenta'};return colorMap[action]||'default';};const columns=[{title:'权限名称',dataIndex:'name',key:'name',width:150,render:(text,record)=>/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:()=>handleView(record),children:text})},{title:'权限编码',dataIndex:'code',key:'code',width:150,render:text=>/*#__PURE__*/_jsx(\"code\",{style:{background:'#f5f5f5',padding:'2px 4px',borderRadius:'2px'},children:text})},{title:'资源',dataIndex:'resource',key:'resource',width:120,render:resource=>/*#__PURE__*/_jsx(Tag,{color:getResourceColor(resource),children:resource})},{title:'操作',dataIndex:'action',key:'action',width:100,render:action=>/*#__PURE__*/_jsx(Tag,{color:getActionColor(action),children:action})},{title:'描述',dataIndex:'description',key:'description',ellipsis:true},{title:'操作',key:'operation',width:150,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record)})}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u6743\\u9650\\u5417\\uFF1F\",onConfirm:()=>handleDelete(record),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{})})})}),/*#__PURE__*/_jsx(Dropdown,{menu:{items:getActionMenuItems(record)},trigger:['click'],children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(MoreOutlined,{})})})]})}];// 获取唯一的资源列表\nconst uniqueResources=Array.from(new Set(permissions.map(p=>p.resource)));// 获取唯一的操作列表\nconst uniqueActions=Array.from(new Set(permissions.map(p=>p.action)));return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u6743\\u9650\\u7BA1\\u7406\"})}),/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsxs(Row,{gutter:[8,8],justify:\"end\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u6743\\u9650\\u540D\\u79F0\\u3001\\u7F16\\u7801\",allowClear:true,style:{width:200},onSearch:handleSearch})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8D44\\u6E90\\u7B5B\\u9009\",allowClear:true,style:{width:120},onChange:handleResourceFilter,children:uniqueResources.map(resource=>/*#__PURE__*/_jsx(Option,{value:resource,children:resource},resource))})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u64CD\\u4F5C\\u7B5B\\u9009\",allowClear:true,style:{width:100},onChange:handleActionFilter,children:uniqueActions.map(action=>/*#__PURE__*/_jsx(Option,{value:action,children:action},action))})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:loadData,children:\"\\u5237\\u65B0\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleCreate,children:\"\\u65B0\\u589E\\u6743\\u9650\"})})]})})]})}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:permissions,loading:loading,rowKey:\"id\",scroll:{x:1000},pagination:{total:permissions.length,pageSize:20,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsx(Modal,{title:editingPermission?'编辑权限':'新增权限',open:permissionModalVisible,onOk:handleModalOk,onCancel:()=>setPermissionModalVisible(false),width:500,destroyOnClose:true,children:/*#__PURE__*/_jsxs(Form,{form:permissionForm,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"\\u6743\\u9650\\u540D\\u79F0\",rules:[{required:true,message:'请输入权限名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u6743\\u9650\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"code\",label:\"\\u6743\\u9650\\u7F16\\u7801\",rules:[{required:true,message:'请输入权限编码'},{pattern:/^[A-Z_]+$/,message:'权限编码只能包含大写字母和下划线'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u6743\\u9650\\u7F16\\u7801\"})}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"resource\",label:\"\\u8D44\\u6E90\",rules:[{required:true,message:'请选择资源'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8D44\\u6E90\",children:[/*#__PURE__*/_jsx(Option,{value:\"BOM\",children:\"BOM\"}),/*#__PURE__*/_jsx(Option,{value:\"MATERIAL\",children:\"MATERIAL\"}),/*#__PURE__*/_jsx(Option,{value:\"USER\",children:\"USER\"}),/*#__PURE__*/_jsx(Option,{value:\"ROLE\",children:\"ROLE\"}),/*#__PURE__*/_jsx(Option,{value:\"SYSTEM\",children:\"SYSTEM\"}),/*#__PURE__*/_jsx(Option,{value:\"INVENTORY\",children:\"INVENTORY\"}),/*#__PURE__*/_jsx(Option,{value:\"PURCHASE\",children:\"PURCHASE\"}),/*#__PURE__*/_jsx(Option,{value:\"ECN\",children:\"ECN\"}),/*#__PURE__*/_jsx(Option,{value:\"COST\",children:\"COST\"}),/*#__PURE__*/_jsx(Option,{value:\"REPORT\",children:\"REPORT\"})]})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"action\",label:\"\\u64CD\\u4F5C\",rules:[{required:true,message:'请选择操作'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u64CD\\u4F5C\",children:[/*#__PURE__*/_jsx(Option,{value:\"VIEW\",children:\"VIEW\"}),/*#__PURE__*/_jsx(Option,{value:\"CREATE\",children:\"CREATE\"}),/*#__PURE__*/_jsx(Option,{value:\"EDIT\",children:\"EDIT\"}),/*#__PURE__*/_jsx(Option,{value:\"DELETE\",children:\"DELETE\"}),/*#__PURE__*/_jsx(Option,{value:\"MANAGE\",children:\"MANAGE\"}),/*#__PURE__*/_jsx(Option,{value:\"CONFIG\",children:\"CONFIG\"}),/*#__PURE__*/_jsx(Option,{value:\"APPROVE\",children:\"APPROVE\"}),/*#__PURE__*/_jsx(Option,{value:\"EXPORT\",children:\"EXPORT\"}),/*#__PURE__*/_jsx(Option,{value:\"IMPORT\",children:\"IMPORT\"})]})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u63CF\\u8FF0\",children:/*#__PURE__*/_jsx(TextArea,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u6743\\u9650\\u63CF\\u8FF0\",rows:3})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u6743\\u9650\\u8BE6\\u60C5\",open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>{setDetailModalVisible(false);if(selectedPermission){handleEdit(selectedPermission);}},children:\"\\u7F16\\u8F91\"},\"edit\")],width:500,children:selectedPermission&&/*#__PURE__*/_jsxs(Descriptions,{column:1,bordered:true,children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6743\\u9650\\u540D\\u79F0\",children:selectedPermission.name}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6743\\u9650\\u7F16\\u7801\",children:/*#__PURE__*/_jsx(\"code\",{style:{background:'#f5f5f5',padding:'2px 4px',borderRadius:'2px'},children:selectedPermission.code})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8D44\\u6E90\",children:/*#__PURE__*/_jsx(Tag,{color:getResourceColor(selectedPermission.resource),children:selectedPermission.resource})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u64CD\\u4F5C\",children:/*#__PURE__*/_jsx(Tag,{color:getActionColor(selectedPermission.action),children:selectedPermission.action})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u63CF\\u8FF0\",children:selectedPermission.description||'暂无描述'})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5BFC\\u51FA\\u6743\\u9650\\u6570\\u636E\",open:exportModalVisible,onOk:executeExport,onCancel:()=>setExportModalVisible(false),okText:\"\\u5BFC\\u51FA\",cancelText:\"\\u53D6\\u6D88\",width:500,children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Typography.Text,{strong:true,children:\"\\u5BFC\\u51FA\\u683C\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Select,{value:exportFormat,onChange:setExportFormat,style:{width:120,marginLeft:8},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"excel\",children:\"Excel\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"csv\",children:\"CSV\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Typography.Text,{strong:true,children:\"\\u5BFC\\u51FA\\u5B57\\u6BB5\\uFF1A\"}),/*#__PURE__*/_jsx(Checkbox.Group,{value:exportFields,onChange:setExportFields,style:{marginTop:8},children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"name\",children:\"\\u6743\\u9650\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"code\",children:\"\\u6743\\u9650\\u7F16\\u7801\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"resource\",children:\"\\u8D44\\u6E90\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"action\",children:\"\\u64CD\\u4F5C\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"description\",children:\"\\u63CF\\u8FF0\"})})]})})]})]})})]});};export default PermissionListPage;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "Popconfirm", "message", "Modal", "Form", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Descriptions", "Checkbox", "XLSX", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "MoreOutlined", "ExportOutlined", "ReloadOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Search", "Option", "TextArea", "PermissionListPage", "navigate", "permissions", "setPermissions", "loading", "setLoading", "searchKeyword", "setSearchKeyword", "resourceFilter", "setResourceFilter", "actionFilter", "setActionFilter", "permissionModalVisible", "setPermissionModalVisible", "detailModalVisible", "setDetailModalVisible", "editingPermission", "setEditingPermission", "selectedPermission", "setSelectedPermission", "permissionForm", "useForm", "exportModalVisible", "setExportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "loadData", "mockPermissions", "id", "name", "code", "resource", "action", "description", "error", "handleSearch", "value", "handleResourceFilter", "handleActionFilter", "handleCreate", "resetFields", "handleEdit", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleView", "handleDelete", "success", "handleModalOk", "values", "validateFields", "handleExport", "executeExport", "exportData", "map", "permission", "data", "includes", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "fileName", "concat", "Date", "toISOString", "split", "writeFile", "getActionMenuItems", "key", "icon", "label", "onClick", "getResourceColor", "colorMap", "BOM", "MATERIAL", "USER", "ROLE", "SYSTEM", "INVENTORY", "PURCHASE", "getActionColor", "VIEW", "CREATE", "EDIT", "DELETE", "MANAGE", "CONFIG", "columns", "title", "dataIndex", "width", "render", "text", "type", "children", "style", "background", "padding", "borderRadius", "color", "ellipsis", "fixed", "_", "size", "onConfirm", "okText", "cancelText", "danger", "menu", "items", "trigger", "uniqueResources", "Array", "from", "Set", "p", "uniqueActions", "marginBottom", "gutter", "align", "level", "margin", "flex", "justify", "placeholder", "allowClear", "onSearch", "onChange", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "total", "length", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onOk", "onCancel", "destroyOnClose", "form", "layout", "<PERSON><PERSON>", "rules", "required", "pattern", "span", "rows", "footer", "column", "bordered", "direction", "Text", "strong", "marginLeft", "Group", "marginTop"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/system/PermissionListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  Popconfirm,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Switch,\n  Descriptions,\n  Badge,\n  Checkbox,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  SettingOutlined,\n  SecurityScanOutlined,\n} from '@ant-design/icons';\n\nimport { Permission } from '../../types';\nimport { ROUTES, PERMISSIONS } from '../../constants';\nimport { formatDate } from '../../utils';\n\nconst { Title } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst PermissionListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [resourceFilter, setResourceFilter] = useState<string>('');\n  const [actionFilter, setActionFilter] = useState<string>('');\n  const [permissionModalVisible, setPermissionModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);\n  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null);\n  const [permissionForm] = Form.useForm();\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['name', 'code', 'resource', 'action', 'description']);\n\n  useEffect(() => {\n    loadData();\n  }, [searchKeyword, resourceFilter, actionFilter]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // TODO: 实现API调用\n      // 模拟数据\n      const mockPermissions: Permission[] = [\n        {\n          id: '1',\n          name: 'BOM查看',\n          code: 'BOM_VIEW',\n          resource: 'BOM',\n          action: 'VIEW',\n          description: 'BOM查看权限，允许用户查看BOM信息',\n        },\n        {\n          id: '2',\n          name: 'BOM创建',\n          code: 'BOM_CREATE',\n          resource: 'BOM',\n          action: 'CREATE',\n          description: 'BOM创建权限，允许用户创建新的BOM',\n        },\n        {\n          id: '3',\n          name: 'BOM编辑',\n          code: 'BOM_EDIT',\n          resource: 'BOM',\n          action: 'EDIT',\n          description: 'BOM编辑权限，允许用户编辑现有BOM',\n        },\n        {\n          id: '4',\n          name: 'BOM删除',\n          code: 'BOM_DELETE',\n          resource: 'BOM',\n          action: 'DELETE',\n          description: 'BOM删除权限，允许用户删除BOM',\n        },\n        {\n          id: '5',\n          name: '物料查看',\n          code: 'MATERIAL_VIEW',\n          resource: 'MATERIAL',\n          action: 'VIEW',\n          description: '物料查看权限，允许用户查看物料信息',\n        },\n        {\n          id: '6',\n          name: '物料创建',\n          code: 'MATERIAL_CREATE',\n          resource: 'MATERIAL',\n          action: 'CREATE',\n          description: '物料创建权限，允许用户创建新物料',\n        },\n        {\n          id: '7',\n          name: '物料编辑',\n          code: 'MATERIAL_EDIT',\n          resource: 'MATERIAL',\n          action: 'EDIT',\n          description: '物料编辑权限，允许用户编辑物料信息',\n        },\n        {\n          id: '8',\n          name: '用户管理',\n          code: 'USER_MANAGE',\n          resource: 'USER',\n          action: 'MANAGE',\n          description: '用户管理权限，允许管理系统用户',\n        },\n        {\n          id: '9',\n          name: '角色管理',\n          code: 'ROLE_MANAGE',\n          resource: 'ROLE',\n          action: 'MANAGE',\n          description: '角色管理权限，允许管理系统角色',\n        },\n        {\n          id: '10',\n          name: '系统配置',\n          code: 'SYSTEM_CONFIG',\n          resource: 'SYSTEM',\n          action: 'CONFIG',\n          description: '系统配置权限，允许配置系统参数',\n        },\n        {\n          id: '11',\n          name: '库存查看',\n          code: 'INVENTORY_VIEW',\n          resource: 'INVENTORY',\n          action: 'VIEW',\n          description: '库存查看权限，允许查看库存信息',\n        },\n        {\n          id: '12',\n          name: '采购管理',\n          code: 'PURCHASE_MANAGE',\n          resource: 'PURCHASE',\n          action: 'MANAGE',\n          description: '采购管理权限，允许管理采购流程',\n        },\n      ];\n      setPermissions(mockPermissions);\n    } catch (error) {\n      message.error('加载权限列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchKeyword(value);\n  };\n\n  const handleResourceFilter = (value: string) => {\n    setResourceFilter(value);\n  };\n\n  const handleActionFilter = (value: string) => {\n    setActionFilter(value);\n  };\n\n  const handleCreate = () => {\n    setEditingPermission(null);\n    permissionForm.resetFields();\n    setPermissionModalVisible(true);\n  };\n\n  const handleEdit = (record: Permission) => {\n    setEditingPermission(record);\n    permissionForm.setFieldsValue(record);\n    setPermissionModalVisible(true);\n  };\n\n  const handleView = (record: Permission) => {\n    setSelectedPermission(record);\n    setDetailModalVisible(true);\n  };\n\n  const handleDelete = async (record: Permission) => {\n    try {\n      // TODO: 实现删除API\n      message.success('删除成功');\n      loadData();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await permissionForm.validateFields();\n\n      if (editingPermission) {\n        // TODO: 实现更新API\n        message.success('更新成功');\n      } else {\n        // TODO: 实现创建API\n        message.success('创建成功');\n      }\n\n      setPermissionModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const executeExport = () => {\n    try {\n      // 准备导出数据\n      const exportData = permissions.map(permission => {\n        const data: any = {};\n        \n        if (exportFields.includes('name')) data['权限名称'] = permission.name;\n        if (exportFields.includes('code')) data['权限编码'] = permission.code;\n        if (exportFields.includes('resource')) data['资源'] = permission.resource;\n        if (exportFields.includes('action')) data['操作'] = permission.action;\n        if (exportFields.includes('description')) data['描述'] = permission.description || '';\n        \n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, '权限列表');\n\n      // 下载文件\n      const fileName = `权限列表_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      \n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  const getActionMenuItems = (record: Permission): MenuProps['items'] => [\n    {\n      key: 'export',\n      icon: <ExportOutlined />,\n      label: '导出信息',\n      onClick: () => {\n        setExportModalVisible(true);\n      },\n    },\n  ];\n\n  const getResourceColor = (resource: string) => {\n    const colorMap: { [key: string]: string } = {\n      BOM: 'blue',\n      MATERIAL: 'green',\n      USER: 'orange',\n      ROLE: 'purple',\n      SYSTEM: 'red',\n      INVENTORY: 'cyan',\n      PURCHASE: 'magenta',\n    };\n    return colorMap[resource] || 'default';\n  };\n\n  const getActionColor = (action: string) => {\n    const colorMap: { [key: string]: string } = {\n      VIEW: 'blue',\n      CREATE: 'green',\n      EDIT: 'orange',\n      DELETE: 'red',\n      MANAGE: 'purple',\n      CONFIG: 'magenta',\n    };\n    return colorMap[action] || 'default';\n  };\n\n  const columns = [\n    {\n      title: '权限名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 150,\n      render: (text: string, record: Permission) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: '权限编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 150,\n      render: (text: string) => (\n        <code style={{ background: '#f5f5f5', padding: '2px 4px', borderRadius: '2px' }}>\n          {text}\n        </code>\n      ),\n    },\n    {\n      title: '资源',\n      dataIndex: 'resource',\n      key: 'resource',\n      width: 120,\n      render: (resource: string) => (\n        <Tag color={getResourceColor(resource)}>{resource}</Tag>\n      ),\n    },\n    {\n      title: '操作',\n      dataIndex: 'action',\n      key: 'action',\n      width: 100,\n      render: (action: string) => (\n        <Tag color={getActionColor(action)}>{action}</Tag>\n      ),\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true,\n    },\n    {\n      title: '操作',\n      key: 'operation',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: Permission) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定要删除这个权限吗？\"\n            onConfirm={() => handleDelete(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n          <Dropdown\n            menu={{ items: getActionMenuItems(record) }}\n            trigger={['click']}\n          >\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  // 获取唯一的资源列表\n  const uniqueResources = Array.from(new Set(permissions.map(p => p.resource)));\n  // 获取唯一的操作列表\n  const uniqueActions = Array.from(new Set(permissions.map(p => p.action)));\n\n  return (\n    <div>\n      <Card>\n        <div style={{ marginBottom: 16 }}>\n          <Row gutter={[16, 16]} align=\"middle\">\n            <Col>\n              <Title level={4} style={{ margin: 0 }}>权限管理</Title>\n            </Col>\n            <Col flex=\"auto\">\n              <Row gutter={[8, 8]} justify=\"end\">\n                <Col>\n                  <Search\n                    placeholder=\"搜索权限名称、编码\"\n                    allowClear\n                    style={{ width: 200 }}\n                    onSearch={handleSearch}\n                  />\n                </Col>\n                <Col>\n                  <Select\n                    placeholder=\"资源筛选\"\n                    allowClear\n                    style={{ width: 120 }}\n                    onChange={handleResourceFilter}\n                  >\n                    {uniqueResources.map(resource => (\n                      <Option key={resource} value={resource}>{resource}</Option>\n                    ))}\n                  </Select>\n                </Col>\n                <Col>\n                  <Select\n                    placeholder=\"操作筛选\"\n                    allowClear\n                    style={{ width: 100 }}\n                    onChange={handleActionFilter}\n                  >\n                    {uniqueActions.map(action => (\n                      <Option key={action} value={action}>{action}</Option>\n                    ))}\n                  </Select>\n                </Col>\n                <Col>\n                  <Button\n                    icon={<ReloadOutlined />}\n                    onClick={loadData}\n                  >\n                    刷新\n                  </Button>\n                </Col>\n                <Col>\n                  <Button\n                    icon={<ExportOutlined />}\n                    onClick={handleExport}\n                  >\n                    导出\n                  </Button>\n                </Col>\n                <Col>\n                  <Button\n                    type=\"primary\"\n                    icon={<PlusOutlined />}\n                    onClick={handleCreate}\n                  >\n                    新增权限\n                  </Button>\n                </Col>\n              </Row>\n            </Col>\n          </Row>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={permissions}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1000 }}\n          pagination={{\n            total: permissions.length,\n            pageSize: 20,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 权限编辑模态框 */}\n      <Modal\n        title={editingPermission ? '编辑权限' : '新增权限'}\n        open={permissionModalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setPermissionModalVisible(false)}\n        width={500}\n        destroyOnClose\n      >\n        <Form\n          form={permissionForm}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"权限名称\"\n            rules={[{ required: true, message: '请输入权限名称' }]}\n          >\n            <Input placeholder=\"请输入权限名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"code\"\n            label=\"权限编码\"\n            rules={[\n              { required: true, message: '请输入权限编码' },\n              { pattern: /^[A-Z_]+$/, message: '权限编码只能包含大写字母和下划线' },\n            ]}\n          >\n            <Input placeholder=\"请输入权限编码\" />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"resource\"\n                label=\"资源\"\n                rules={[{ required: true, message: '请选择资源' }]}\n              >\n                <Select placeholder=\"请选择资源\">\n                  <Option value=\"BOM\">BOM</Option>\n                  <Option value=\"MATERIAL\">MATERIAL</Option>\n                  <Option value=\"USER\">USER</Option>\n                  <Option value=\"ROLE\">ROLE</Option>\n                  <Option value=\"SYSTEM\">SYSTEM</Option>\n                  <Option value=\"INVENTORY\">INVENTORY</Option>\n                  <Option value=\"PURCHASE\">PURCHASE</Option>\n                  <Option value=\"ECN\">ECN</Option>\n                  <Option value=\"COST\">COST</Option>\n                  <Option value=\"REPORT\">REPORT</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"action\"\n                label=\"操作\"\n                rules={[{ required: true, message: '请选择操作' }]}\n              >\n                <Select placeholder=\"请选择操作\">\n                  <Option value=\"VIEW\">VIEW</Option>\n                  <Option value=\"CREATE\">CREATE</Option>\n                  <Option value=\"EDIT\">EDIT</Option>\n                  <Option value=\"DELETE\">DELETE</Option>\n                  <Option value=\"MANAGE\">MANAGE</Option>\n                  <Option value=\"CONFIG\">CONFIG</Option>\n                  <Option value=\"APPROVE\">APPROVE</Option>\n                  <Option value=\"EXPORT\">EXPORT</Option>\n                  <Option value=\"IMPORT\">IMPORT</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n          >\n            <TextArea\n              placeholder=\"请输入权限描述\"\n              rows={3}\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 权限详情模态框 */}\n      <Modal\n        title=\"权限详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n          <Button\n            key=\"edit\"\n            type=\"primary\"\n            onClick={() => {\n              setDetailModalVisible(false);\n              if (selectedPermission) {\n                handleEdit(selectedPermission);\n              }\n            }}\n          >\n            编辑\n          </Button>,\n        ]}\n        width={500}\n      >\n        {selectedPermission && (\n          <Descriptions column={1} bordered>\n            <Descriptions.Item label=\"权限名称\">\n              {selectedPermission.name}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"权限编码\">\n              <code style={{ background: '#f5f5f5', padding: '2px 4px', borderRadius: '2px' }}>\n                {selectedPermission.code}\n              </code>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"资源\">\n              <Tag color={getResourceColor(selectedPermission.resource)}>\n                {selectedPermission.resource}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"操作\">\n              <Tag color={getActionColor(selectedPermission.action)}>\n                {selectedPermission.action}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"描述\">\n              {selectedPermission.description || '暂无描述'}\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Modal>\n\n      {/* 导出模态框 */}\n      <Modal\n        title=\"导出权限数据\"\n        open={exportModalVisible}\n        onOk={executeExport}\n        onCancel={() => setExportModalVisible(false)}\n        okText=\"导出\"\n        cancelText=\"取消\"\n        width={500}\n      >\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <Typography.Text strong>导出格式：</Typography.Text>\n            <Select\n              value={exportFormat}\n              onChange={setExportFormat}\n              style={{ width: 120, marginLeft: 8 }}\n            >\n              <Select.Option value=\"excel\">Excel</Select.Option>\n              <Select.Option value=\"csv\">CSV</Select.Option>\n            </Select>\n          </div>\n          \n          <div>\n            <Typography.Text strong>导出字段：</Typography.Text>\n            <Checkbox.Group\n              value={exportFields}\n              onChange={setExportFields}\n              style={{ marginTop: 8 }}\n            >\n              <Row>\n                <Col span={12}>\n                  <Checkbox value=\"name\">权限名称</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"code\">权限编码</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"resource\">资源</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"action\">操作</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"description\">描述</Checkbox>\n                </Col>\n              </Row>\n            </Checkbox.Group>\n          </div>\n        </Space>\n      </Modal>\n    </div>\n  );\n};\n\nexport default PermissionListPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,KAAK,CACLC,IAAI,CACJC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,OAAO,CACPC,QAAQ,CAGRC,YAAY,CAEZC,QAAQ,KACH,MAAM,CACb,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,OACEC,YAAY,CAEZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,cAAc,CACdC,cAAc,KAGT,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAM3B,KAAM,CAAEC,KAAM,CAAC,CAAGnB,UAAU,CAC5B,KAAM,CAAEoB,MAAO,CAAC,CAAG5B,KAAK,CACxB,KAAM,CAAE6B,MAAO,CAAC,CAAG5B,MAAM,CACzB,KAAM,CAAE6B,QAAS,CAAC,CAAG9B,KAAK,CAE1B,KAAM,CAAA+B,kBAA4B,CAAGA,CAAA,GAAM,CACzC,KAAM,CAAAC,QAAQ,CAAGpC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACqC,WAAW,CAAEC,cAAc,CAAC,CAAGvC,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAACwC,OAAO,CAAEC,UAAU,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC0C,aAAa,CAAEC,gBAAgB,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC4C,cAAc,CAAEC,iBAAiB,CAAC,CAAG7C,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAAC8C,YAAY,CAAEC,eAAe,CAAC,CAAG/C,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACgD,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CAC3E,KAAM,CAACkD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACoD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrD,QAAQ,CAAoB,IAAI,CAAC,CACnF,KAAM,CAACsD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvD,QAAQ,CAAoB,IAAI,CAAC,CACrF,KAAM,CAACwD,cAAc,CAAC,CAAG5C,IAAI,CAAC6C,OAAO,CAAC,CAAC,CACvC,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC4D,YAAY,CAAEC,eAAe,CAAC,CAAG7D,QAAQ,CAAkB,OAAO,CAAC,CAC1E,KAAM,CAAC8D,YAAY,CAAEC,eAAe,CAAC,CAAG/D,QAAQ,CAAW,CAAC,MAAM,CAAE,MAAM,CAAE,UAAU,CAAE,QAAQ,CAAE,aAAa,CAAC,CAAC,CAEjHD,SAAS,CAAC,IAAM,CACdiE,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACtB,aAAa,CAAEE,cAAc,CAAEE,YAAY,CAAC,CAAC,CAEjD,KAAM,CAAAkB,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BvB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA;AACA,KAAM,CAAAwB,eAA6B,CAAG,CACpC,CACEC,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,KAAK,CACfC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,qBACf,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,YAAY,CAClBC,QAAQ,CAAE,KAAK,CACfC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,qBACf,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,UAAU,CAChBC,QAAQ,CAAE,KAAK,CACfC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,qBACf,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,YAAY,CAClBC,QAAQ,CAAE,KAAK,CACfC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,mBACf,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,eAAe,CACrBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,mBACf,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,iBAAiB,CACvBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,kBACf,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,eAAe,CACrBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,mBACf,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,aAAa,CACnBC,QAAQ,CAAE,MAAM,CAChBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,iBACf,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,aAAa,CACnBC,QAAQ,CAAE,MAAM,CAChBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,iBACf,CAAC,CACD,CACEL,EAAE,CAAE,IAAI,CACRC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,eAAe,CACrBC,QAAQ,CAAE,QAAQ,CAClBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,iBACf,CAAC,CACD,CACEL,EAAE,CAAE,IAAI,CACRC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,gBAAgB,CACtBC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,iBACf,CAAC,CACD,CACEL,EAAE,CAAE,IAAI,CACRC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,iBAAiB,CACvBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,iBACf,CAAC,CACF,CACDhC,cAAc,CAAC0B,eAAe,CAAC,CACjC,CAAE,MAAOO,KAAK,CAAE,CACd9D,OAAO,CAAC8D,KAAK,CAAC,UAAU,CAAC,CAC3B,CAAC,OAAS,CACR/B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgC,YAAY,CAAIC,KAAa,EAAK,CACtC/B,gBAAgB,CAAC+B,KAAK,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAID,KAAa,EAAK,CAC9C7B,iBAAiB,CAAC6B,KAAK,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAIF,KAAa,EAAK,CAC5C3B,eAAe,CAAC2B,KAAK,CAAC,CACxB,CAAC,CAED,KAAM,CAAAG,YAAY,CAAGA,CAAA,GAAM,CACzBxB,oBAAoB,CAAC,IAAI,CAAC,CAC1BG,cAAc,CAACsB,WAAW,CAAC,CAAC,CAC5B7B,yBAAyB,CAAC,IAAI,CAAC,CACjC,CAAC,CAED,KAAM,CAAA8B,UAAU,CAAIC,MAAkB,EAAK,CACzC3B,oBAAoB,CAAC2B,MAAM,CAAC,CAC5BxB,cAAc,CAACyB,cAAc,CAACD,MAAM,CAAC,CACrC/B,yBAAyB,CAAC,IAAI,CAAC,CACjC,CAAC,CAED,KAAM,CAAAiC,UAAU,CAAIF,MAAkB,EAAK,CACzCzB,qBAAqB,CAACyB,MAAM,CAAC,CAC7B7B,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAgC,YAAY,CAAG,KAAO,CAAAH,MAAkB,EAAK,CACjD,GAAI,CACF;AACAtE,OAAO,CAAC0E,OAAO,CAAC,MAAM,CAAC,CACvBpB,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOQ,KAAK,CAAE,CACd9D,OAAO,CAAC8D,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAa,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA9B,cAAc,CAAC+B,cAAc,CAAC,CAAC,CAEpD,GAAInC,iBAAiB,CAAE,CACrB;AACA1C,OAAO,CAAC0E,OAAO,CAAC,MAAM,CAAC,CACzB,CAAC,IAAM,CACL;AACA1E,OAAO,CAAC0E,OAAO,CAAC,MAAM,CAAC,CACzB,CAEAnC,yBAAyB,CAAC,KAAK,CAAC,CAChCe,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOQ,KAAK,CAAE,CACd9D,OAAO,CAAC8D,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAgB,YAAY,CAAGA,CAAA,GAAM,CACzB7B,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA8B,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAGpD,WAAW,CAACqD,GAAG,CAACC,UAAU,EAAI,CAC/C,KAAM,CAAAC,IAAS,CAAG,CAAC,CAAC,CAEpB,GAAI/B,YAAY,CAACgC,QAAQ,CAAC,MAAM,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,UAAU,CAACzB,IAAI,CACjE,GAAIL,YAAY,CAACgC,QAAQ,CAAC,MAAM,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,UAAU,CAACxB,IAAI,CACjE,GAAIN,YAAY,CAACgC,QAAQ,CAAC,UAAU,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,UAAU,CAACvB,QAAQ,CACvE,GAAIP,YAAY,CAACgC,QAAQ,CAAC,QAAQ,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,UAAU,CAACtB,MAAM,CACnE,GAAIR,YAAY,CAACgC,QAAQ,CAAC,aAAa,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,UAAU,CAACrB,WAAW,EAAI,EAAE,CAEnF,MAAO,CAAAsB,IAAI,CACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,EAAE,CAAG3E,IAAI,CAAC4E,KAAK,CAACC,aAAa,CAACP,UAAU,CAAC,CAC/C,KAAM,CAAAQ,EAAE,CAAG9E,IAAI,CAAC4E,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChC/E,IAAI,CAAC4E,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,MAAM,CAAC,CAE5C;AACA,KAAM,CAAAM,QAAQ,6BAAAC,MAAA,CAAW,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAAH,MAAA,CAAI1C,YAAY,GAAK,OAAO,CAAG,MAAM,CAAG,KAAK,CAAE,CAC9GxC,IAAI,CAACsF,SAAS,CAACR,EAAE,CAAEG,QAAQ,CAAC,CAE5B3F,OAAO,CAAC0E,OAAO,CAAC,MAAM,CAAC,CACvBzB,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,MAAOa,KAAK,CAAE,CACd9D,OAAO,CAAC8D,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAmC,kBAAkB,CAAI3B,MAAkB,EAAyB,CACrE,CACE4B,GAAG,CAAE,QAAQ,CACbC,IAAI,cAAEhF,IAAA,CAACH,cAAc,GAAE,CAAC,CACxBoF,KAAK,CAAE,MAAM,CACbC,OAAO,CAAEA,CAAA,GAAM,CACbpD,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CACF,CAAC,CACF,CAED,KAAM,CAAAqD,gBAAgB,CAAI3C,QAAgB,EAAK,CAC7C,KAAM,CAAA4C,QAAmC,CAAG,CAC1CC,GAAG,CAAE,MAAM,CACXC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,QAAQ,CACdC,IAAI,CAAE,QAAQ,CACdC,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,SACZ,CAAC,CACD,MAAO,CAAAP,QAAQ,CAAC5C,QAAQ,CAAC,EAAI,SAAS,CACxC,CAAC,CAED,KAAM,CAAAoD,cAAc,CAAInD,MAAc,EAAK,CACzC,KAAM,CAAA2C,QAAmC,CAAG,CAC1CS,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,OAAO,CACfC,IAAI,CAAE,QAAQ,CACdC,MAAM,CAAE,KAAK,CACbC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,SACV,CAAC,CACD,MAAO,CAAAd,QAAQ,CAAC3C,MAAM,CAAC,EAAI,SAAS,CACtC,CAAC,CAED,KAAM,CAAA0D,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,MAAM,CACjBtB,GAAG,CAAE,MAAM,CACXuB,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACC,IAAY,CAAErD,MAAkB,gBACvCnD,IAAA,CAAC1B,MAAM,EAACmI,IAAI,CAAC,MAAM,CAACvB,OAAO,CAAEA,CAAA,GAAM7B,UAAU,CAACF,MAAM,CAAE,CAAAuD,QAAA,CACnDF,IAAI,CACC,CAEZ,CAAC,CACD,CACEJ,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,MAAM,CACjBtB,GAAG,CAAE,MAAM,CACXuB,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGC,IAAY,eACnBxG,IAAA,SAAM2G,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEC,OAAO,CAAE,SAAS,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAJ,QAAA,CAC7EF,IAAI,CACD,CAEV,CAAC,CACD,CACEJ,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBtB,GAAG,CAAE,UAAU,CACfuB,KAAK,CAAE,GAAG,CACVC,MAAM,CAAG/D,QAAgB,eACvBxC,IAAA,CAACrB,GAAG,EAACoI,KAAK,CAAE5B,gBAAgB,CAAC3C,QAAQ,CAAE,CAAAkE,QAAA,CAAElE,QAAQ,CAAM,CAE3D,CAAC,CACD,CACE4D,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBtB,GAAG,CAAE,QAAQ,CACbuB,KAAK,CAAE,GAAG,CACVC,MAAM,CAAG9D,MAAc,eACrBzC,IAAA,CAACrB,GAAG,EAACoI,KAAK,CAAEnB,cAAc,CAACnD,MAAM,CAAE,CAAAiE,QAAA,CAAEjE,MAAM,CAAM,CAErD,CAAC,CACD,CACE2D,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,aAAa,CACxBtB,GAAG,CAAE,aAAa,CAClBiC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEZ,KAAK,CAAE,IAAI,CACXrB,GAAG,CAAE,WAAW,CAChBuB,KAAK,CAAE,GAAG,CACVW,KAAK,CAAE,OAAgB,CACvBV,MAAM,CAAEA,CAACW,CAAM,CAAE/D,MAAkB,gBACjCjD,KAAA,CAAC3B,KAAK,EAAC4I,IAAI,CAAC,OAAO,CAAAT,QAAA,eACjB1G,IAAA,CAACb,OAAO,EAACiH,KAAK,CAAC,cAAI,CAAAM,QAAA,cACjB1G,IAAA,CAAC1B,MAAM,EACLmI,IAAI,CAAC,MAAM,CACXzB,IAAI,cAAEhF,IAAA,CAACL,WAAW,GAAE,CAAE,CACtBuF,OAAO,CAAEA,CAAA,GAAM7B,UAAU,CAACF,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACVnD,IAAA,CAACb,OAAO,EAACiH,KAAK,CAAC,cAAI,CAAAM,QAAA,cACjB1G,IAAA,CAAC1B,MAAM,EACLmI,IAAI,CAAC,MAAM,CACXzB,IAAI,cAAEhF,IAAA,CAACP,YAAY,GAAE,CAAE,CACvByF,OAAO,CAAEA,CAAA,GAAMhC,UAAU,CAACC,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACVnD,IAAA,CAACpB,UAAU,EACTwH,KAAK,CAAC,oEAAa,CACnBgB,SAAS,CAAEA,CAAA,GAAM9D,YAAY,CAACH,MAAM,CAAE,CACtCkE,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAZ,QAAA,cAEf1G,IAAA,CAACb,OAAO,EAACiH,KAAK,CAAC,cAAI,CAAAM,QAAA,cACjB1G,IAAA,CAAC1B,MAAM,EACLmI,IAAI,CAAC,MAAM,CACXc,MAAM,MACNvC,IAAI,cAAEhF,IAAA,CAACN,cAAc,GAAE,CAAE,CAC1B,CAAC,CACK,CAAC,CACA,CAAC,cACbM,IAAA,CAACZ,QAAQ,EACPoI,IAAI,CAAE,CAAEC,KAAK,CAAE3C,kBAAkB,CAAC3B,MAAM,CAAE,CAAE,CAC5CuE,OAAO,CAAE,CAAC,OAAO,CAAE,CAAAhB,QAAA,cAEnB1G,IAAA,CAAC1B,MAAM,EAACmI,IAAI,CAAC,MAAM,CAACzB,IAAI,cAAEhF,IAAA,CAACJ,YAAY,GAAE,CAAE,CAAE,CAAC,CACtC,CAAC,EACN,CAEX,CAAC,CACF,CAED;AACA,KAAM,CAAA+H,eAAe,CAAGC,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAACrH,WAAW,CAACqD,GAAG,CAACiE,CAAC,EAAIA,CAAC,CAACvF,QAAQ,CAAC,CAAC,CAAC,CAC7E;AACA,KAAM,CAAAwF,aAAa,CAAGJ,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAACrH,WAAW,CAACqD,GAAG,CAACiE,CAAC,EAAIA,CAAC,CAACtF,MAAM,CAAC,CAAC,CAAC,CAEzE,mBACEvC,KAAA,QAAAwG,QAAA,eACExG,KAAA,CAACxB,IAAI,EAAAgI,QAAA,eACH1G,IAAA,QAAK2G,KAAK,CAAE,CAAEsB,YAAY,CAAE,EAAG,CAAE,CAAAvB,QAAA,cAC/BxG,KAAA,CAACjB,GAAG,EAACiJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACC,KAAK,CAAC,QAAQ,CAAAzB,QAAA,eACnC1G,IAAA,CAACd,GAAG,EAAAwH,QAAA,cACF1G,IAAA,CAACG,KAAK,EAACiI,KAAK,CAAE,CAAE,CAACzB,KAAK,CAAE,CAAE0B,MAAM,CAAE,CAAE,CAAE,CAAA3B,QAAA,CAAC,0BAAI,CAAO,CAAC,CAChD,CAAC,cACN1G,IAAA,CAACd,GAAG,EAACoJ,IAAI,CAAC,MAAM,CAAA5B,QAAA,cACdxG,KAAA,CAACjB,GAAG,EAACiJ,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAACK,OAAO,CAAC,KAAK,CAAA7B,QAAA,eAChC1G,IAAA,CAACd,GAAG,EAAAwH,QAAA,cACF1G,IAAA,CAACI,MAAM,EACLoI,WAAW,CAAC,wDAAW,CACvBC,UAAU,MACV9B,KAAK,CAAE,CAAEL,KAAK,CAAE,GAAI,CAAE,CACtBoC,QAAQ,CAAE9F,YAAa,CACxB,CAAC,CACC,CAAC,cACN5C,IAAA,CAACd,GAAG,EAAAwH,QAAA,cACF1G,IAAA,CAACvB,MAAM,EACL+J,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACV9B,KAAK,CAAE,CAAEL,KAAK,CAAE,GAAI,CAAE,CACtBqC,QAAQ,CAAE7F,oBAAqB,CAAA4D,QAAA,CAE9BiB,eAAe,CAAC7D,GAAG,CAACtB,QAAQ,eAC3BxC,IAAA,CAACK,MAAM,EAAgBwC,KAAK,CAAEL,QAAS,CAAAkE,QAAA,CAAElE,QAAQ,EAApCA,QAA6C,CAC3D,CAAC,CACI,CAAC,CACN,CAAC,cACNxC,IAAA,CAACd,GAAG,EAAAwH,QAAA,cACF1G,IAAA,CAACvB,MAAM,EACL+J,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACV9B,KAAK,CAAE,CAAEL,KAAK,CAAE,GAAI,CAAE,CACtBqC,QAAQ,CAAE5F,kBAAmB,CAAA2D,QAAA,CAE5BsB,aAAa,CAAClE,GAAG,CAACrB,MAAM,eACvBzC,IAAA,CAACK,MAAM,EAAcwC,KAAK,CAAEJ,MAAO,CAAAiE,QAAA,CAAEjE,MAAM,EAA9BA,MAAuC,CACrD,CAAC,CACI,CAAC,CACN,CAAC,cACNzC,IAAA,CAACd,GAAG,EAAAwH,QAAA,cACF1G,IAAA,CAAC1B,MAAM,EACL0G,IAAI,cAAEhF,IAAA,CAACF,cAAc,GAAE,CAAE,CACzBoF,OAAO,CAAE/C,QAAS,CAAAuE,QAAA,CACnB,cAED,CAAQ,CAAC,CACN,CAAC,cACN1G,IAAA,CAACd,GAAG,EAAAwH,QAAA,cACF1G,IAAA,CAAC1B,MAAM,EACL0G,IAAI,cAAEhF,IAAA,CAACH,cAAc,GAAE,CAAE,CACzBqF,OAAO,CAAEvB,YAAa,CAAA+C,QAAA,CACvB,cAED,CAAQ,CAAC,CACN,CAAC,cACN1G,IAAA,CAACd,GAAG,EAAAwH,QAAA,cACF1G,IAAA,CAAC1B,MAAM,EACLmI,IAAI,CAAC,SAAS,CACdzB,IAAI,cAAEhF,IAAA,CAACR,YAAY,GAAE,CAAE,CACvB0F,OAAO,CAAElC,YAAa,CAAA0D,QAAA,CACvB,0BAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN1G,IAAA,CAAC3B,KAAK,EACJ8H,OAAO,CAAEA,OAAQ,CACjByC,UAAU,CAAEnI,WAAY,CACxBE,OAAO,CAAEA,OAAQ,CACjBkI,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,KAAK,CAAExI,WAAW,CAACyI,MAAM,CACzBC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACL,KAAK,CAAEM,KAAK,aAAA9E,MAAA,CAAU8E,KAAK,CAAC,CAAC,CAAC,MAAA9E,MAAA,CAAI8E,KAAK,CAAC,CAAC,CAAC,oBAAA9E,MAAA,CAAQwE,KAAK,WACrE,CAAE,CACH,CAAC,EACE,CAAC,cAGPjJ,IAAA,CAAClB,KAAK,EACJsH,KAAK,CAAE7E,iBAAiB,CAAG,MAAM,CAAG,MAAO,CAC3CiI,IAAI,CAAErI,sBAAuB,CAC7BsI,IAAI,CAAEjG,aAAc,CACpBkG,QAAQ,CAAEA,CAAA,GAAMtI,yBAAyB,CAAC,KAAK,CAAE,CACjDkF,KAAK,CAAE,GAAI,CACXqD,cAAc,MAAAjD,QAAA,cAEdxG,KAAA,CAACnB,IAAI,EACH6K,IAAI,CAAEjI,cAAe,CACrBkI,MAAM,CAAC,UAAU,CAAAnD,QAAA,eAEjB1G,IAAA,CAACjB,IAAI,CAAC+K,IAAI,EACRxH,IAAI,CAAC,MAAM,CACX2C,KAAK,CAAC,0BAAM,CACZ8E,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEnL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA6H,QAAA,cAEhD1G,IAAA,CAACxB,KAAK,EAACgK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,cAEZxI,IAAA,CAACjB,IAAI,CAAC+K,IAAI,EACRxH,IAAI,CAAC,MAAM,CACX2C,KAAK,CAAC,0BAAM,CACZ8E,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEnL,OAAO,CAAE,SAAU,CAAC,CACtC,CAAEoL,OAAO,CAAE,WAAW,CAAEpL,OAAO,CAAE,kBAAmB,CAAC,CACrD,CAAA6H,QAAA,cAEF1G,IAAA,CAACxB,KAAK,EAACgK,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,cAEZtI,KAAA,CAACjB,GAAG,EAACiJ,MAAM,CAAE,EAAG,CAAAxB,QAAA,eACd1G,IAAA,CAACd,GAAG,EAACgL,IAAI,CAAE,EAAG,CAAAxD,QAAA,cACZ1G,IAAA,CAACjB,IAAI,CAAC+K,IAAI,EACRxH,IAAI,CAAC,UAAU,CACf2C,KAAK,CAAC,cAAI,CACV8E,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEnL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA6H,QAAA,cAE9CxG,KAAA,CAACzB,MAAM,EAAC+J,WAAW,CAAC,gCAAO,CAAA9B,QAAA,eACzB1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,KAAK,CAAA6D,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,UAAU,CAAA6D,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1C1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,MAAM,CAAA6D,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,MAAM,CAAA6D,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,WAAW,CAAA6D,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5C1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,UAAU,CAAA6D,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1C1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,KAAK,CAAA6D,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,MAAM,CAAA6D,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,QAAM,CAAQ,CAAC,EAChC,CAAC,CACA,CAAC,CACT,CAAC,cACN1G,IAAA,CAACd,GAAG,EAACgL,IAAI,CAAE,EAAG,CAAAxD,QAAA,cACZ1G,IAAA,CAACjB,IAAI,CAAC+K,IAAI,EACRxH,IAAI,CAAC,QAAQ,CACb2C,KAAK,CAAC,cAAI,CACV8E,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEnL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA6H,QAAA,cAE9CxG,KAAA,CAACzB,MAAM,EAAC+J,WAAW,CAAC,gCAAO,CAAA9B,QAAA,eACzB1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,MAAM,CAAA6D,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,MAAM,CAAA6D,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,SAAS,CAAA6D,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC1G,IAAA,CAACK,MAAM,EAACwC,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,QAAM,CAAQ,CAAC,EAChC,CAAC,CACA,CAAC,CACT,CAAC,EACH,CAAC,cAEN1G,IAAA,CAACjB,IAAI,CAAC+K,IAAI,EACRxH,IAAI,CAAC,aAAa,CAClB2C,KAAK,CAAC,cAAI,CAAAyB,QAAA,cAEV1G,IAAA,CAACM,QAAQ,EACPkI,WAAW,CAAC,4CAAS,CACrB2B,IAAI,CAAE,CAAE,CACT,CAAC,CACO,CAAC,EACR,CAAC,CACF,CAAC,cAGRnK,IAAA,CAAClB,KAAK,EACJsH,KAAK,CAAC,0BAAM,CACZoD,IAAI,CAAEnI,kBAAmB,CACzBqI,QAAQ,CAAEA,CAAA,GAAMpI,qBAAqB,CAAC,KAAK,CAAE,CAC7C8I,MAAM,CAAE,cACNpK,IAAA,CAAC1B,MAAM,EAAa4G,OAAO,CAAEA,CAAA,GAAM5D,qBAAqB,CAAC,KAAK,CAAE,CAAAoF,QAAA,CAAC,cAEjE,EAFY,OAEJ,CAAC,cACT1G,IAAA,CAAC1B,MAAM,EAELmI,IAAI,CAAC,SAAS,CACdvB,OAAO,CAAEA,CAAA,GAAM,CACb5D,qBAAqB,CAAC,KAAK,CAAC,CAC5B,GAAIG,kBAAkB,CAAE,CACtByB,UAAU,CAACzB,kBAAkB,CAAC,CAChC,CACF,CAAE,CAAAiF,QAAA,CACH,cAED,EAVM,MAUE,CAAC,CACT,CACFJ,KAAK,CAAE,GAAI,CAAAI,QAAA,CAEVjF,kBAAkB,eACjBvB,KAAA,CAACb,YAAY,EAACgL,MAAM,CAAE,CAAE,CAACC,QAAQ,MAAA5D,QAAA,eAC/B1G,IAAA,CAACX,YAAY,CAACyK,IAAI,EAAC7E,KAAK,CAAC,0BAAM,CAAAyB,QAAA,CAC5BjF,kBAAkB,CAACa,IAAI,CACP,CAAC,cACpBtC,IAAA,CAACX,YAAY,CAACyK,IAAI,EAAC7E,KAAK,CAAC,0BAAM,CAAAyB,QAAA,cAC7B1G,IAAA,SAAM2G,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEC,OAAO,CAAE,SAAS,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAJ,QAAA,CAC7EjF,kBAAkB,CAACc,IAAI,CACpB,CAAC,CACU,CAAC,cACpBvC,IAAA,CAACX,YAAY,CAACyK,IAAI,EAAC7E,KAAK,CAAC,cAAI,CAAAyB,QAAA,cAC3B1G,IAAA,CAACrB,GAAG,EAACoI,KAAK,CAAE5B,gBAAgB,CAAC1D,kBAAkB,CAACe,QAAQ,CAAE,CAAAkE,QAAA,CACvDjF,kBAAkB,CAACe,QAAQ,CACzB,CAAC,CACW,CAAC,cACpBxC,IAAA,CAACX,YAAY,CAACyK,IAAI,EAAC7E,KAAK,CAAC,cAAI,CAAAyB,QAAA,cAC3B1G,IAAA,CAACrB,GAAG,EAACoI,KAAK,CAAEnB,cAAc,CAACnE,kBAAkB,CAACgB,MAAM,CAAE,CAAAiE,QAAA,CACnDjF,kBAAkB,CAACgB,MAAM,CACvB,CAAC,CACW,CAAC,cACpBzC,IAAA,CAACX,YAAY,CAACyK,IAAI,EAAC7E,KAAK,CAAC,cAAI,CAAAyB,QAAA,CAC1BjF,kBAAkB,CAACiB,WAAW,EAAI,MAAM,CACxB,CAAC,EACR,CACf,CACI,CAAC,cAGR1C,IAAA,CAAClB,KAAK,EACJsH,KAAK,CAAC,sCAAQ,CACdoD,IAAI,CAAE3H,kBAAmB,CACzB4H,IAAI,CAAE7F,aAAc,CACpB8F,QAAQ,CAAEA,CAAA,GAAM5H,qBAAqB,CAAC,KAAK,CAAE,CAC7CuF,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CACfhB,KAAK,CAAE,GAAI,CAAAI,QAAA,cAEXxG,KAAA,CAAC3B,KAAK,EAACgM,SAAS,CAAC,UAAU,CAAC5D,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAI,QAAA,eACnDxG,KAAA,QAAAwG,QAAA,eACE1G,IAAA,CAAChB,UAAU,CAACwL,IAAI,EAACC,MAAM,MAAA/D,QAAA,CAAC,gCAAK,CAAiB,CAAC,cAC/CxG,KAAA,CAACzB,MAAM,EACLoE,KAAK,CAAEd,YAAa,CACpB4G,QAAQ,CAAE3G,eAAgB,CAC1B2E,KAAK,CAAE,CAAEL,KAAK,CAAE,GAAG,CAAEoE,UAAU,CAAE,CAAE,CAAE,CAAAhE,QAAA,eAErC1G,IAAA,CAACvB,MAAM,CAAC4B,MAAM,EAACwC,KAAK,CAAC,OAAO,CAAA6D,QAAA,CAAC,OAAK,CAAe,CAAC,cAClD1G,IAAA,CAACvB,MAAM,CAAC4B,MAAM,EAACwC,KAAK,CAAC,KAAK,CAAA6D,QAAA,CAAC,KAAG,CAAe,CAAC,EACxC,CAAC,EACN,CAAC,cAENxG,KAAA,QAAAwG,QAAA,eACE1G,IAAA,CAAChB,UAAU,CAACwL,IAAI,EAACC,MAAM,MAAA/D,QAAA,CAAC,gCAAK,CAAiB,CAAC,cAC/C1G,IAAA,CAACV,QAAQ,CAACqL,KAAK,EACb9H,KAAK,CAAEZ,YAAa,CACpB0G,QAAQ,CAAEzG,eAAgB,CAC1ByE,KAAK,CAAE,CAAEiE,SAAS,CAAE,CAAE,CAAE,CAAAlE,QAAA,cAExBxG,KAAA,CAACjB,GAAG,EAAAyH,QAAA,eACF1G,IAAA,CAACd,GAAG,EAACgL,IAAI,CAAE,EAAG,CAAAxD,QAAA,cACZ1G,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,MAAM,CAAA6D,QAAA,CAAC,0BAAI,CAAU,CAAC,CACnC,CAAC,cACN1G,IAAA,CAACd,GAAG,EAACgL,IAAI,CAAE,EAAG,CAAAxD,QAAA,cACZ1G,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,MAAM,CAAA6D,QAAA,CAAC,0BAAI,CAAU,CAAC,CACnC,CAAC,cACN1G,IAAA,CAACd,GAAG,EAACgL,IAAI,CAAE,EAAG,CAAAxD,QAAA,cACZ1G,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,UAAU,CAAA6D,QAAA,CAAC,cAAE,CAAU,CAAC,CACrC,CAAC,cACN1G,IAAA,CAACd,GAAG,EAACgL,IAAI,CAAE,EAAG,CAAAxD,QAAA,cACZ1G,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,QAAQ,CAAA6D,QAAA,CAAC,cAAE,CAAU,CAAC,CACnC,CAAC,cACN1G,IAAA,CAACd,GAAG,EAACgL,IAAI,CAAE,EAAG,CAAAxD,QAAA,cACZ1G,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,aAAa,CAAA6D,QAAA,CAAC,cAAE,CAAU,CAAC,CACxC,CAAC,EACH,CAAC,CACQ,CAAC,EACd,CAAC,EACD,CAAC,CACH,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}