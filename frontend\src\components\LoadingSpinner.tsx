import React from 'react';
import { Spin, SpinProps } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import './LoadingSpinner.css';

interface LoadingSpinnerProps extends SpinProps {
  /** 是否显示加载文本 */
  showText?: boolean;
  /** 自定义加载文本 */
  text?: string;
  /** 是否全屏显示 */
  fullscreen?: boolean;
  /** 是否显示遮罩层 */
  overlay?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  showText = true,
  text = '加载中...',
  fullscreen = false,
  overlay = false,
  size = 'default',
  ...props
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 24 : 16 }} spin />;

  const spinner = (
    <Spin
      indicator={antIcon}
      tip={showText ? text : undefined}
      size={size}
      {...props}
    />
  );

  if (fullscreen) {
    return (
      <div className={`loading-spinner-fullscreen ${overlay ? 'with-overlay' : ''}`}>
        {spinner}
      </div>
    );
  }

  return (
    <div className="loading-spinner-container">
      {spinner}
    </div>
  );
};

export default LoadingSpinner;