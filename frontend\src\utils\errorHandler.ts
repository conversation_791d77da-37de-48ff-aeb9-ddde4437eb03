import { message, notification } from 'antd';
import { AxiosError } from 'axios';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  HTTP = 'HTTP',
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  BUSINESS = 'BUSINESS',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  type: ErrorType;
  code?: string | number;
  message: string;
  details?: any;
  timestamp: number;
}

/**
 * 全局错误处理器
 * 提供统一的错误处理机制，支持多种错误类型的处理和日志记录
 * 使用单例模式确保全局唯一实例
 */
class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLog: ErrorInfo[] = [];

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * 处理HTTP错误
   * 解析Axios错误对象，生成标准化的错误信息
   * 
   * @param error - Axios错误对象
   * @returns 标准化的错误信息
   */
  handleHttpError(error: AxiosError): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.NETWORK,
      code: error.response?.status || 0,
      message: this.getHttpErrorMessage(error),
      details: error.response?.data,
      timestamp: Date.now()
    };

    this.logError(errorInfo);
    this.showErrorNotification(errorInfo);
    
    return errorInfo;
  }

  /**
   * 处理业务逻辑错误
   */
  handleBusinessError(code: string | number, message: string, details?: any): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.BUSINESS,
      code,
      message,
      details,
      timestamp: Date.now()
    };

    this.logError(errorInfo);
    this.showErrorMessage(message);
    
    return errorInfo;
  }

  /**
   * 处理验证错误
   */
  handleValidationError(message: string, details?: any): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.VALIDATION,
      message,
      details,
      timestamp: Date.now()
    };

    this.logError(errorInfo);
    this.showErrorMessage(message);
    
    return errorInfo;
  }

  /**
   * 处理权限错误
   */
  handlePermissionError(message: string = '您没有权限执行此操作'): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.PERMISSION,
      code: 403,
      message,
      timestamp: Date.now()
    };

    this.logError(errorInfo);
    this.showErrorNotification(errorInfo);
    
    return errorInfo;
  }

  /**
   * 处理未知错误
   */
  handleUnknownError(error: Error): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.UNKNOWN,
      message: error.message || '发生未知错误',
      details: {
        name: error.name,
        stack: error.stack
      },
      timestamp: Date.now()
    };

    this.logError(errorInfo);
    this.showErrorNotification(errorInfo);
    
    return errorInfo;
  }

  /**
   * 通用错误处理方法
   * 接受错误信息对象，进行统一处理
   * 
   * @param errorInfo - 错误信息对象
   * @returns 处理后的错误信息
   */
  handleError(errorInfo: ErrorInfo): ErrorInfo {
    this.logError(errorInfo);
    
    // 根据错误类型选择不同的显示方式
    if (errorInfo.type === ErrorType.VALIDATION) {
      this.showErrorMessage(errorInfo.message);
    } else {
      this.showErrorNotification(errorInfo);
    }
    
    return errorInfo;
  }

  /**
   * 获取HTTP错误消息
   */
  private getHttpErrorMessage(error: AxiosError): string {
    const status = error.response?.status;
    const data = error.response?.data as any;

    // 优先使用服务器返回的错误消息
    if (data?.message) {
      return data.message;
    }

    // 根据状态码返回默认消息
    switch (status) {
      case 400:
        return '请求参数错误';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '您没有权限执行此操作';
      case 404:
        return '请求的资源不存在';
      case 408:
        return '请求超时，请稍后重试';
      case 409:
        return '数据冲突，请刷新后重试';
      case 422:
        return '数据验证失败';
      case 429:
        return '请求过于频繁，请稍后重试';
      case 500:
        return '服务器内部错误';
      case 502:
        return '网关错误';
      case 503:
        return '服务暂时不可用';
      case 504:
        return '网关超时';
      default:
        if (error.code === 'NETWORK_ERROR' || !status) {
          return '网络连接失败，请检查网络设置';
        }
        return `请求失败 (${status})`;
    }
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(msg: string) {
    message.error(msg);
  }

  /**
   * 显示错误通知
   */
  private showErrorNotification(errorInfo: ErrorInfo) {
    notification.error({
      message: '操作失败',
      description: errorInfo.message,
      duration: 4.5,
    });
  }

  /**
   * 记录错误日志
   */
  private logError(errorInfo: ErrorInfo) {
    console.error('[ErrorHandler]', errorInfo);
    
    // 保存到本地日志（最多保存100条）
    this.errorLog.unshift(errorInfo);
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(0, 100);
    }

    // 在生产环境中，可以在这里添加错误上报逻辑
    if (process.env.NODE_ENV === 'production') {
      this.reportError(errorInfo);
    }
  }

  /**
   * 上报错误（生产环境）
   */
  private reportError(errorInfo: ErrorInfo) {
    // TODO: 实现错误上报逻辑
    // 可以发送到错误监控服务，如 Sentry、Bugsnag 等
    console.log('报告错误到监控服务:', errorInfo);
  }

  /**
   * 获取错误日志
   */
  getErrorLog(): ErrorInfo[] {
    return [...this.errorLog];
  }

  /**
   * 清空错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance();

// 导出便捷方法
export const handleHttpError = (error: AxiosError) => errorHandler.handleHttpError(error);
export const handleBusinessError = (code: string | number, message: string, details?: any) => 
  errorHandler.handleBusinessError(code, message, details);
export const handleValidationError = (message: string, details?: any) => 
  errorHandler.handleValidationError(message, details);
export const handlePermissionError = (message?: string) => 
  errorHandler.handlePermissionError(message);
export const handleUnknownError = (error: Error) => 
  errorHandler.handleUnknownError(error);