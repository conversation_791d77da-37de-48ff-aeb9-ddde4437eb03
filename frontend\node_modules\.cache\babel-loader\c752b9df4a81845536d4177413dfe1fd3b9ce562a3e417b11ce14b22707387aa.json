{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useNavigate}from'react-router-dom';import{Table,Button,Space,Input,Select,Card,Tag,Popconfirm,message,Modal,Form,Typography,Row,Col,Tooltip,Dropdown,Switch,Tree,Checkbox,Descriptions}from'antd';import*as XLSX from'xlsx';import{PlusOutlined,EditOutlined,DeleteOutlined,EyeOutlined,MoreOutlined,ExportOutlined,ReloadOutlined,SettingOutlined,TeamOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const{Search}=Input;const{Option}=Select;const{TextArea}=Input;const RoleListPage=()=>{const navigate=useNavigate();const[roles,setRoles]=useState([]);const[loading,setLoading]=useState(false);const[searchKeyword,setSearchKeyword]=useState('');const[statusFilter,setStatusFilter]=useState('');const[roleModalVisible,setRoleModalVisible]=useState(false);const[permissionModalVisible,setPermissionModalVisible]=useState(false);const[editingRole,setEditingRole]=useState(null);const[selectedRole,setSelectedRole]=useState(null);const[roleForm]=Form.useForm();const[checkedPermissions,setCheckedPermissions]=useState([]);const[roleDetailVisible,setRoleDetailVisible]=useState(false);const[viewingRole,setViewingRole]=useState(null);const[exportModalVisible,setExportModalVisible]=useState(false);const[exportFormat,setExportFormat]=useState('excel');const[exportFields,setExportFields]=useState(['name','code','description','permissions','isActive']);useEffect(()=>{loadData();},[searchKeyword,statusFilter]);const loadData=async()=>{setLoading(true);try{// TODO: 实现API调用\n// 模拟数据\nconst mockRoles=[{id:'1',name:'系统管理员',code:'ADMIN',description:'系统管理员，拥有所有权限',permissions:[{id:'1',name:'BOM查看',code:'BOM_VIEW',resource:'BOM',action:'VIEW',description:'BOM查看权限'},{id:'2',name:'BOM创建',code:'BOM_CREATE',resource:'BOM',action:'CREATE',description:'BOM创建权限'},{id:'3',name:'用户管理',code:'USER_MANAGE',resource:'USER',action:'MANAGE',description:'用户管理权限'}],isActive:true},{id:'2',name:'BOM管理员',code:'BOM_MANAGER',description:'BOM管理员，负责BOM相关功能',permissions:[{id:'1',name:'BOM查看',code:'BOM_VIEW',resource:'BOM',action:'VIEW',description:'BOM查看权限'},{id:'2',name:'BOM创建',code:'BOM_CREATE',resource:'BOM',action:'CREATE',description:'BOM创建权限'},{id:'4',name:'BOM编辑',code:'BOM_EDIT',resource:'BOM',action:'EDIT',description:'BOM编辑权限'}],isActive:true},{id:'3',name:'销售PMC',code:'SALES_PMC',description:'销售PMC，负责订单BOM管理',permissions:[{id:'1',name:'BOM查看',code:'BOM_VIEW',resource:'BOM',action:'VIEW',description:'BOM查看权限'},{id:'5',name:'订单BOM创建',code:'ORDER_BOM_CREATE',resource:'ORDER_BOM',action:'CREATE',description:'订单BOM创建权限'}],isActive:false}];setRoles(mockRoles);}catch(error){message.error('加载角色列表失败');}finally{setLoading(false);}};const handleSearch=value=>{setSearchKeyword(value);};const handleStatusFilter=value=>{setStatusFilter(value);};const handleCreate=()=>{setEditingRole(null);roleForm.resetFields();setRoleModalVisible(true);};const handleEdit=record=>{setEditingRole(record);roleForm.setFieldsValue(record);setRoleModalVisible(true);};const handleView=record=>{setViewingRole(record);setRoleDetailVisible(true);};const handleDelete=async record=>{try{// TODO: 实现删除API\nmessage.success('删除成功');loadData();}catch(error){message.error('删除失败');}};const handleToggleStatus=async record=>{try{// TODO: 实现状态切换API\nmessage.success(\"\".concat(record.isActive?'禁用':'启用',\"\\u6210\\u529F\"));loadData();}catch(error){message.error('操作失败');}};const handlePermissionConfig=record=>{setSelectedRole(record);setCheckedPermissions(record.permissions.map(p=>p.id));setPermissionModalVisible(true);};const handleModalOk=async()=>{try{const values=await roleForm.validateFields();if(editingRole){// TODO: 实现更新API\nmessage.success('更新成功');}else{// TODO: 实现创建API\nmessage.success('创建成功');}setRoleModalVisible(false);loadData();}catch(error){message.error('操作失败');}};const handlePermissionOk=async()=>{try{// TODO: 实现权限配置API\nmessage.success('权限配置成功');setPermissionModalVisible(false);loadData();}catch(error){message.error('权限配置失败');}};const handleExport=()=>{setExportModalVisible(true);};const executeExport=()=>{try{// 准备导出数据\nconst exportData=roles.map(role=>{const data={};if(exportFields.includes('name'))data['角色名称']=role.name;if(exportFields.includes('code'))data['角色编码']=role.code;if(exportFields.includes('description'))data['描述']=role.description||'';if(exportFields.includes('permissions')){data['权限列表']=role.permissions.map(p=>p.name).join(', ');data['权限数量']=role.permissions.length;}if(exportFields.includes('isActive'))data['状态']=role.isActive?'启用':'禁用';return data;});// 创建工作簿\nconst ws=XLSX.utils.json_to_sheet(exportData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'角色列表');// 下载文件\nconst fileName=\"\\u89D2\\u8272\\u5217\\u8868_\".concat(new Date().toISOString().split('T')[0],\".\").concat(exportFormat==='excel'?'xlsx':'csv');XLSX.writeFile(wb,fileName);message.success('导出成功');setExportModalVisible(false);}catch(error){message.error('导出失败');}};const getActionMenuItems=record=>[{key:'permission',icon:/*#__PURE__*/_jsx(SettingOutlined,{}),label:'权限配置',onClick:()=>handlePermissionConfig(record)},{key:'toggleStatus',icon:/*#__PURE__*/_jsx(TeamOutlined,{}),label:record.isActive?'禁用角色':'启用角色',onClick:()=>handleToggleStatus(record)},{key:'export',icon:/*#__PURE__*/_jsx(ExportOutlined,{}),label:'导出信息',onClick:()=>{message.info('导出功能开发中');}}];// 权限树数据\nconst permissionTreeData=[{key:'bom',title:'BOM管理',children:[{key:'1',title:'BOM查看'},{key:'2',title:'BOM创建'},{key:'4',title:'BOM编辑'},{key:'6',title:'BOM删除'},{key:'7',title:'BOM冻结'}]},{key:'material',title:'物料管理',children:[{key:'8',title:'物料查看'},{key:'9',title:'物料创建'},{key:'10',title:'物料编辑'},{key:'11',title:'物料删除'}]},{key:'inventory',title:'库存管理',children:[{key:'12',title:'库存查看'},{key:'13',title:'库存入库'},{key:'14',title:'库存出库'},{key:'15',title:'库存调整'}]},{key:'purchase',title:'采购管理',children:[{key:'16',title:'采购查看'},{key:'17',title:'采购创建'},{key:'18',title:'采购审批'}]},{key:'system',title:'系统管理',children:[{key:'3',title:'用户管理'},{key:'19',title:'角色管理'},{key:'20',title:'系统配置'}]}];const columns=[{title:'角色名称',dataIndex:'name',key:'name',width:150,render:(text,record)=>/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:()=>handleView(record),children:text})},{title:'角色编码',dataIndex:'code',key:'code',width:150},{title:'描述',dataIndex:'description',key:'description',ellipsis:true},{title:'权限数量',dataIndex:'permissions',key:'permissions',width:100,render:permissions=>/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:permissions.length})},{title:'状态',dataIndex:'isActive',key:'isActive',width:80,render:isActive=>/*#__PURE__*/_jsx(Tag,{color:isActive?'green':'red',children:isActive?'启用':'禁用'})},{title:'操作',key:'action',width:200,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u6743\\u9650\\u914D\\u7F6E\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(SettingOutlined,{}),onClick:()=>handlePermissionConfig(record)})}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u89D2\\u8272\\u5417\\uFF1F\",onConfirm:()=>handleDelete(record),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{})})})}),/*#__PURE__*/_jsx(Dropdown,{menu:{items:getActionMenuItems(record)},trigger:['click'],children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(MoreOutlined,{})})})]})}];return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u89D2\\u8272\\u7BA1\\u7406\"})}),/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsxs(Row,{gutter:[8,8],justify:\"end\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u89D2\\u8272\\u540D\\u79F0\\u3001\\u7F16\\u7801\",allowClear:true,style:{width:200},onSearch:handleSearch})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u72B6\\u6001\\u7B5B\\u9009\",allowClear:true,style:{width:100},onChange:handleStatusFilter,children:[/*#__PURE__*/_jsx(Option,{value:\"active\",children:\"\\u542F\\u7528\"}),/*#__PURE__*/_jsx(Option,{value:\"inactive\",children:\"\\u7981\\u7528\"})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:loadData,children:\"\\u5237\\u65B0\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleCreate,children:\"\\u65B0\\u589E\\u89D2\\u8272\"})})]})})]})}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:roles,loading:loading,rowKey:\"id\",scroll:{x:1000},pagination:{total:roles.length,pageSize:20,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsx(Modal,{title:editingRole?'编辑角色':'新增角色',open:roleModalVisible,onOk:handleModalOk,onCancel:()=>setRoleModalVisible(false),width:500,destroyOnClose:true,children:/*#__PURE__*/_jsxs(Form,{form:roleForm,layout:\"vertical\",initialValues:{isActive:true},children:[/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"\\u89D2\\u8272\\u540D\\u79F0\",rules:[{required:true,message:'请输入角色名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"code\",label:\"\\u89D2\\u8272\\u7F16\\u7801\",rules:[{required:true,message:'请输入角色编码'},{pattern:/^[A-Z_]+$/,message:'角色编码只能包含大写字母和下划线'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u7F16\\u7801\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u63CF\\u8FF0\",children:/*#__PURE__*/_jsx(TextArea,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u63CF\\u8FF0\",rows:3})}),/*#__PURE__*/_jsx(Form.Item,{name:\"isActive\",label:\"\\u72B6\\u6001\",valuePropName:\"checked\",children:/*#__PURE__*/_jsx(Switch,{checkedChildren:\"\\u542F\\u7528\",unCheckedChildren:\"\\u7981\\u7528\"})})]})}),/*#__PURE__*/_jsxs(Modal,{title:\"\\u6743\\u9650\\u914D\\u7F6E - \".concat(selectedRole===null||selectedRole===void 0?void 0:selectedRole.name),open:permissionModalVisible,onOk:handlePermissionOk,onCancel:()=>setPermissionModalVisible(false),width:600,destroyOnClose:true,children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{size:\"small\",onClick:()=>setCheckedPermissions(permissionTreeData.flatMap(node=>node.children?node.children.map(child=>child.key):[node.key])),children:\"\\u5168\\u9009\"}),/*#__PURE__*/_jsx(Button,{size:\"small\",onClick:()=>setCheckedPermissions([]),children:\"\\u6E05\\u7A7A\"})]})}),/*#__PURE__*/_jsx(Tree,{checkable:true,checkedKeys:checkedPermissions,onCheck:checkedKeys=>{setCheckedPermissions(checkedKeys);},treeData:permissionTreeData,height:400})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u89D2\\u8272\\u8BE6\\u60C5\",open:roleDetailVisible,onCancel:()=>setRoleDetailVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setRoleDetailVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:600,children:viewingRole&&/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:2,children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u89D2\\u8272\\u540D\\u79F0\",span:2,children:viewingRole.name}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u89D2\\u8272\\u7F16\\u7801\",span:2,children:viewingRole.code}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u63CF\\u8FF0\",span:2,children:viewingRole.description||'无'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Tag,{color:viewingRole.isActive?'green':'red',children:viewingRole.isActive?'启用':'禁用'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6743\\u9650\\u6570\\u91CF\",children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:viewingRole.permissions.length})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6743\\u9650\\u5217\\u8868\",span:2,children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:200,overflowY:'auto'},children:viewingRole.permissions.map(permission=>/*#__PURE__*/_jsx(Tag,{style:{margin:'2px'},children:permission.name},permission.id))})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5BFC\\u51FA\\u89D2\\u8272\\u6570\\u636E\",open:exportModalVisible,onOk:executeExport,onCancel:()=>setExportModalVisible(false),width:500,children:/*#__PURE__*/_jsxs(Form,{layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BFC\\u51FA\\u683C\\u5F0F\",children:/*#__PURE__*/_jsxs(Select,{value:exportFormat,onChange:setExportFormat,style:{width:'100%'},children:[/*#__PURE__*/_jsx(Option,{value:\"excel\",children:\"Excel (.xlsx)\"}),/*#__PURE__*/_jsx(Option,{value:\"csv\",children:\"CSV (.csv)\"})]})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BFC\\u51FA\\u5B57\\u6BB5\",children:/*#__PURE__*/_jsx(Checkbox.Group,{value:exportFields,onChange:setExportFields,style:{width:'100%'},children:/*#__PURE__*/_jsxs(Row,{gutter:[8,8],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"name\",children:\"\\u89D2\\u8272\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"code\",children:\"\\u89D2\\u8272\\u7F16\\u7801\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"description\",children:\"\\u63CF\\u8FF0\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"permissions\",children:\"\\u6743\\u9650\\u4FE1\\u606F\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"isActive\",children:\"\\u72B6\\u6001\"})})]})})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(\"div\",{style:{padding:'8px 12px',backgroundColor:'#f6f6f6',borderRadius:'4px'},children:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[\"\\u5C06\\u5BFC\\u51FA \",roles.length,\" \\u6761\\u89D2\\u8272\\u8BB0\\u5F55\"]})})})]})})]});};export default RoleListPage;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "Popconfirm", "message", "Modal", "Form", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Switch", "Tree", "Checkbox", "Descriptions", "XLSX", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "MoreOutlined", "ExportOutlined", "ReloadOutlined", "SettingOutlined", "TeamOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Search", "Option", "TextArea", "RoleListPage", "navigate", "roles", "setRoles", "loading", "setLoading", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "roleModalVisible", "setRoleModalVisible", "permissionModalVisible", "setPermissionModalVisible", "editingRole", "setEditingRole", "selectedR<PERSON>", "setSelectedRole", "roleForm", "useForm", "checkedPermissions", "setCheckedPermissions", "roleDetailVisible", "setRoleDetailVisible", "viewingRole", "setViewingRole", "exportModalVisible", "setExportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "loadData", "mockRoles", "id", "name", "code", "description", "permissions", "resource", "action", "isActive", "error", "handleSearch", "value", "handleStatusFilter", "handleCreate", "resetFields", "handleEdit", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleView", "handleDelete", "success", "handleToggleStatus", "concat", "handlePermissionConfig", "map", "p", "handleModalOk", "values", "validateFields", "handlePermissionOk", "handleExport", "executeExport", "exportData", "role", "data", "includes", "join", "length", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "fileName", "Date", "toISOString", "split", "writeFile", "getActionMenuItems", "key", "icon", "label", "onClick", "info", "permissionTreeData", "title", "children", "columns", "dataIndex", "width", "render", "text", "type", "ellipsis", "color", "fixed", "_", "size", "onConfirm", "okText", "cancelText", "danger", "menu", "items", "trigger", "style", "marginBottom", "gutter", "align", "level", "margin", "flex", "justify", "placeholder", "allowClear", "onSearch", "onChange", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "total", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onOk", "onCancel", "destroyOnClose", "form", "layout", "initialValues", "<PERSON><PERSON>", "rules", "required", "pattern", "rows", "valuePropName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "flatMap", "node", "child", "checkable", "checked<PERSON>eys", "onCheck", "treeData", "height", "footer", "bordered", "column", "span", "maxHeight", "overflowY", "permission", "Group", "padding", "backgroundColor", "borderRadius", "fontSize"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/system/RoleListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  Popconfirm,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Switch,\n  Tree,\n  Divider,\n  Checkbox,\n  Descriptions,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  SettingOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\n\nimport { Role, Permission } from '../../types';\nimport { ROUTES, PERMISSIONS } from '../../constants';\nimport { formatDate } from '../../utils';\n\nconst { Title } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\ninterface PermissionTreeNode {\n  key: string;\n  title: string;\n  children?: PermissionTreeNode[];\n}\n\nconst RoleListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [roleModalVisible, setRoleModalVisible] = useState(false);\n  const [permissionModalVisible, setPermissionModalVisible] = useState(false);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null);\n  const [roleForm] = Form.useForm();\n  const [checkedPermissions, setCheckedPermissions] = useState<string[]>([]);\n  const [roleDetailVisible, setRoleDetailVisible] = useState(false);\n  const [viewingRole, setViewingRole] = useState<Role | null>(null);\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['name', 'code', 'description', 'permissions', 'isActive']);\n\n  useEffect(() => {\n    loadData();\n  }, [searchKeyword, statusFilter]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // TODO: 实现API调用\n      // 模拟数据\n      const mockRoles: Role[] = [\n        {\n          id: '1',\n          name: '系统管理员',\n          code: 'ADMIN',\n          description: '系统管理员，拥有所有权限',\n          permissions: [\n            { id: '1', name: 'BOM查看', code: 'BOM_VIEW', resource: 'BOM', action: 'VIEW', description: 'BOM查看权限' },\n            { id: '2', name: 'BOM创建', code: 'BOM_CREATE', resource: 'BOM', action: 'CREATE', description: 'BOM创建权限' },\n            { id: '3', name: '用户管理', code: 'USER_MANAGE', resource: 'USER', action: 'MANAGE', description: '用户管理权限' },\n          ],\n          isActive: true,\n        },\n        {\n          id: '2',\n          name: 'BOM管理员',\n          code: 'BOM_MANAGER',\n          description: 'BOM管理员，负责BOM相关功能',\n          permissions: [\n            { id: '1', name: 'BOM查看', code: 'BOM_VIEW', resource: 'BOM', action: 'VIEW', description: 'BOM查看权限' },\n            { id: '2', name: 'BOM创建', code: 'BOM_CREATE', resource: 'BOM', action: 'CREATE', description: 'BOM创建权限' },\n            { id: '4', name: 'BOM编辑', code: 'BOM_EDIT', resource: 'BOM', action: 'EDIT', description: 'BOM编辑权限' },\n          ],\n          isActive: true,\n        },\n        {\n          id: '3',\n          name: '销售PMC',\n          code: 'SALES_PMC',\n          description: '销售PMC，负责订单BOM管理',\n          permissions: [\n            { id: '1', name: 'BOM查看', code: 'BOM_VIEW', resource: 'BOM', action: 'VIEW', description: 'BOM查看权限' },\n            { id: '5', name: '订单BOM创建', code: 'ORDER_BOM_CREATE', resource: 'ORDER_BOM', action: 'CREATE', description: '订单BOM创建权限' },\n          ],\n          isActive: false,\n        },\n      ];\n      setRoles(mockRoles);\n    } catch (error) {\n      message.error('加载角色列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchKeyword(value);\n  };\n\n  const handleStatusFilter = (value: string) => {\n    setStatusFilter(value);\n  };\n\n  const handleCreate = () => {\n    setEditingRole(null);\n    roleForm.resetFields();\n    setRoleModalVisible(true);\n  };\n\n  const handleEdit = (record: Role) => {\n    setEditingRole(record);\n    roleForm.setFieldsValue(record);\n    setRoleModalVisible(true);\n  };\n\n  const handleView = (record: Role) => {\n    setViewingRole(record);\n    setRoleDetailVisible(true);\n  };\n\n  const handleDelete = async (record: Role) => {\n    try {\n      // TODO: 实现删除API\n      message.success('删除成功');\n      loadData();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleToggleStatus = async (record: Role) => {\n    try {\n      // TODO: 实现状态切换API\n      message.success(`${record.isActive ? '禁用' : '启用'}成功`);\n      loadData();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const handlePermissionConfig = (record: Role) => {\n    setSelectedRole(record);\n    setCheckedPermissions(record.permissions.map(p => p.id));\n    setPermissionModalVisible(true);\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await roleForm.validateFields();\n\n      if (editingRole) {\n        // TODO: 实现更新API\n        message.success('更新成功');\n      } else {\n        // TODO: 实现创建API\n        message.success('创建成功');\n      }\n\n      setRoleModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const handlePermissionOk = async () => {\n    try {\n      // TODO: 实现权限配置API\n      message.success('权限配置成功');\n      setPermissionModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('权限配置失败');\n    }\n  };\n\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const executeExport = () => {\n    try {\n      // 准备导出数据\n      const exportData = roles.map(role => {\n        const data: any = {};\n        \n        if (exportFields.includes('name')) data['角色名称'] = role.name;\n        if (exportFields.includes('code')) data['角色编码'] = role.code;\n        if (exportFields.includes('description')) data['描述'] = role.description || '';\n        if (exportFields.includes('permissions')) {\n          data['权限列表'] = role.permissions.map(p => p.name).join(', ');\n          data['权限数量'] = role.permissions.length;\n        }\n        if (exportFields.includes('isActive')) data['状态'] = role.isActive ? '启用' : '禁用';\n        \n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, '角色列表');\n\n      // 下载文件\n      const fileName = `角色列表_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      \n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  const getActionMenuItems = (record: Role): MenuProps['items'] => [\n    {\n      key: 'permission',\n      icon: <SettingOutlined />,\n      label: '权限配置',\n      onClick: () => handlePermissionConfig(record),\n    },\n    {\n      key: 'toggleStatus',\n      icon: <TeamOutlined />,\n      label: record.isActive ? '禁用角色' : '启用角色',\n      onClick: () => handleToggleStatus(record),\n    },\n    {\n      key: 'export',\n      icon: <ExportOutlined />,\n      label: '导出信息',\n      onClick: () => {\n        message.info('导出功能开发中');\n      },\n    },\n  ];\n\n  // 权限树数据\n  const permissionTreeData: PermissionTreeNode[] = [\n    {\n      key: 'bom',\n      title: 'BOM管理',\n      children: [\n        { key: '1', title: 'BOM查看' },\n        { key: '2', title: 'BOM创建' },\n        { key: '4', title: 'BOM编辑' },\n        { key: '6', title: 'BOM删除' },\n        { key: '7', title: 'BOM冻结' },\n      ],\n    },\n    {\n      key: 'material',\n      title: '物料管理',\n      children: [\n        { key: '8', title: '物料查看' },\n        { key: '9', title: '物料创建' },\n        { key: '10', title: '物料编辑' },\n        { key: '11', title: '物料删除' },\n      ],\n    },\n    {\n      key: 'inventory',\n      title: '库存管理',\n      children: [\n        { key: '12', title: '库存查看' },\n        { key: '13', title: '库存入库' },\n        { key: '14', title: '库存出库' },\n        { key: '15', title: '库存调整' },\n      ],\n    },\n    {\n      key: 'purchase',\n      title: '采购管理',\n      children: [\n        { key: '16', title: '采购查看' },\n        { key: '17', title: '采购创建' },\n        { key: '18', title: '采购审批' },\n      ],\n    },\n    {\n      key: 'system',\n      title: '系统管理',\n      children: [\n        { key: '3', title: '用户管理' },\n        { key: '19', title: '角色管理' },\n        { key: '20', title: '系统配置' },\n      ],\n    },\n  ];\n\n  const columns = [\n    {\n      title: '角色名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 150,\n      render: (text: string, record: Role) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: '角色编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 150,\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true,\n    },\n    {\n      title: '权限数量',\n      dataIndex: 'permissions',\n      key: 'permissions',\n      width: 100,\n      render: (permissions: Permission[]) => (\n        <Tag color=\"blue\">{permissions.length}</Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'isActive',\n      width: 80,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right' as const,\n      render: (_: any, record: Role) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"权限配置\">\n            <Button\n              type=\"text\"\n              icon={<SettingOutlined />}\n              onClick={() => handlePermissionConfig(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定要删除这个角色吗？\"\n            onConfirm={() => handleDelete(record)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n          <Dropdown\n            menu={{ items: getActionMenuItems(record) }}\n            trigger={['click']}\n          >\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <div style={{ marginBottom: 16 }}>\n          <Row gutter={[16, 16]} align=\"middle\">\n            <Col>\n              <Title level={4} style={{ margin: 0 }}>角色管理</Title>\n            </Col>\n            <Col flex=\"auto\">\n              <Row gutter={[8, 8]} justify=\"end\">\n                <Col>\n                  <Search\n                    placeholder=\"搜索角色名称、编码\"\n                    allowClear\n                    style={{ width: 200 }}\n                    onSearch={handleSearch}\n                  />\n                </Col>\n                <Col>\n                  <Select\n                    placeholder=\"状态筛选\"\n                    allowClear\n                    style={{ width: 100 }}\n                    onChange={handleStatusFilter}\n                  >\n                    <Option value=\"active\">启用</Option>\n                    <Option value=\"inactive\">禁用</Option>\n                  </Select>\n                </Col>\n                <Col>\n                  <Button\n                    icon={<ReloadOutlined />}\n                    onClick={loadData}\n                  >\n                    刷新\n                  </Button>\n                </Col>\n                <Col>\n                  <Button\n                    icon={<ExportOutlined />}\n                    onClick={handleExport}\n                  >\n                    导出\n                  </Button>\n                </Col>\n                <Col>\n                  <Button\n                    type=\"primary\"\n                    icon={<PlusOutlined />}\n                    onClick={handleCreate}\n                  >\n                    新增角色\n                  </Button>\n                </Col>\n              </Row>\n            </Col>\n          </Row>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={roles}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1000 }}\n          pagination={{\n            total: roles.length,\n            pageSize: 20,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 角色编辑模态框 */}\n      <Modal\n        title={editingRole ? '编辑角色' : '新增角色'}\n        open={roleModalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setRoleModalVisible(false)}\n        width={500}\n        destroyOnClose\n      >\n        <Form\n          form={roleForm}\n          layout=\"vertical\"\n          initialValues={{\n            isActive: true,\n          }}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"角色名称\"\n            rules={[{ required: true, message: '请输入角色名称' }]}\n          >\n            <Input placeholder=\"请输入角色名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"code\"\n            label=\"角色编码\"\n            rules={[\n              { required: true, message: '请输入角色编码' },\n              { pattern: /^[A-Z_]+$/, message: '角色编码只能包含大写字母和下划线' },\n            ]}\n          >\n            <Input placeholder=\"请输入角色编码\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n          >\n            <TextArea\n              placeholder=\"请输入角色描述\"\n              rows={3}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"isActive\"\n            label=\"状态\"\n            valuePropName=\"checked\"\n          >\n            <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 权限配置模态框 */}\n      <Modal\n        title={`权限配置 - ${selectedRole?.name}`}\n        open={permissionModalVisible}\n        onOk={handlePermissionOk}\n        onCancel={() => setPermissionModalVisible(false)}\n        width={600}\n        destroyOnClose\n      >\n        <div style={{ marginBottom: 16 }}>\n          <Space>\n            <Button\n              size=\"small\"\n              onClick={() => setCheckedPermissions(permissionTreeData.flatMap(node => \n                node.children ? node.children.map(child => child.key) : [node.key]\n              ))}\n            >\n              全选\n            </Button>\n            <Button\n              size=\"small\"\n              onClick={() => setCheckedPermissions([])}\n            >\n              清空\n            </Button>\n          </Space>\n        </div>\n        \n        <Tree\n          checkable\n          checkedKeys={checkedPermissions}\n          onCheck={(checkedKeys) => {\n            setCheckedPermissions(checkedKeys as string[]);\n          }}\n          treeData={permissionTreeData}\n          height={400}\n        />\n      </Modal>\n\n      {/* 角色详情模态框 */}\n      <Modal\n        title=\"角色详情\"\n        open={roleDetailVisible}\n        onCancel={() => setRoleDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setRoleDetailVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={600}\n      >\n        {viewingRole && (\n          <Descriptions bordered column={2}>\n            <Descriptions.Item label=\"角色名称\" span={2}>\n              {viewingRole.name}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"角色编码\" span={2}>\n              {viewingRole.code}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"描述\" span={2}>\n              {viewingRole.description || '无'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"状态\">\n              <Tag color={viewingRole.isActive ? 'green' : 'red'}>\n                {viewingRole.isActive ? '启用' : '禁用'}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"权限数量\">\n              <Tag color=\"blue\">{viewingRole.permissions.length}</Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"权限列表\" span={2}>\n              <div style={{ maxHeight: 200, overflowY: 'auto' }}>\n                {viewingRole.permissions.map(permission => (\n                  <Tag key={permission.id} style={{ margin: '2px' }}>\n                    {permission.name}\n                  </Tag>\n                ))}\n              </div>\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Modal>\n\n      {/* 导出模态框 */}\n      <Modal\n        title=\"导出角色数据\"\n        open={exportModalVisible}\n        onOk={executeExport}\n        onCancel={() => setExportModalVisible(false)}\n        width={500}\n      >\n        <Form layout=\"vertical\">\n          <Form.Item label=\"导出格式\">\n            <Select\n              value={exportFormat}\n              onChange={setExportFormat}\n              style={{ width: '100%' }}\n            >\n              <Option value=\"excel\">Excel (.xlsx)</Option>\n              <Option value=\"csv\">CSV (.csv)</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item label=\"导出字段\">\n            <Checkbox.Group\n              value={exportFields}\n              onChange={setExportFields}\n              style={{ width: '100%' }}\n            >\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Checkbox value=\"name\">角色名称</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"code\">角色编码</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"description\">描述</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"permissions\">权限信息</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"isActive\">状态</Checkbox>\n                </Col>\n              </Row>\n            </Checkbox.Group>\n          </Form.Item>\n\n          <Form.Item>\n            <div style={{ padding: '8px 12px', backgroundColor: '#f6f6f6', borderRadius: '4px' }}>\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                将导出 {roles.length} 条角色记录\n              </div>\n            </div>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default RoleListPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,KAAK,CACLC,IAAI,CACJC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,OAAO,CACPC,QAAQ,CAERC,MAAM,CACNC,IAAI,CAEJC,QAAQ,CACRC,YAAY,KACP,MAAM,CACb,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,OACEC,YAAY,CAEZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,cAAc,CACdC,cAAc,CACdC,eAAe,CACfC,YAAY,KACP,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAM3B,KAAM,CAAEC,KAAM,CAAC,CAAGvB,UAAU,CAC5B,KAAM,CAAEwB,MAAO,CAAC,CAAGhC,KAAK,CACxB,KAAM,CAAEiC,MAAO,CAAC,CAAGhC,MAAM,CACzB,KAAM,CAAEiC,QAAS,CAAC,CAAGlC,KAAK,CAQ1B,KAAM,CAAAmC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,QAAQ,CAAGxC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACyC,KAAK,CAAEC,QAAQ,CAAC,CAAG3C,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAAC4C,OAAO,CAAEC,UAAU,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC8C,aAAa,CAAEC,gBAAgB,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACgD,YAAY,CAAEC,eAAe,CAAC,CAAGjD,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACkD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACoD,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CAC3E,KAAM,CAACsD,WAAW,CAAEC,cAAc,CAAC,CAAGvD,QAAQ,CAAc,IAAI,CAAC,CACjE,KAAM,CAACwD,YAAY,CAAEC,eAAe,CAAC,CAAGzD,QAAQ,CAAc,IAAI,CAAC,CACnE,KAAM,CAAC0D,QAAQ,CAAC,CAAG9C,IAAI,CAAC+C,OAAO,CAAC,CAAC,CACjC,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG7D,QAAQ,CAAW,EAAE,CAAC,CAC1E,KAAM,CAAC8D,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/D,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACgE,WAAW,CAAEC,cAAc,CAAC,CAAGjE,QAAQ,CAAc,IAAI,CAAC,CACjE,KAAM,CAACkE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACoE,YAAY,CAAEC,eAAe,CAAC,CAAGrE,QAAQ,CAAkB,OAAO,CAAC,CAC1E,KAAM,CAACsE,YAAY,CAAEC,eAAe,CAAC,CAAGvE,QAAQ,CAAW,CAAC,MAAM,CAAE,MAAM,CAAE,aAAa,CAAE,aAAa,CAAE,UAAU,CAAC,CAAC,CAEtHD,SAAS,CAAC,IAAM,CACdyE,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAAC1B,aAAa,CAAEE,YAAY,CAAC,CAAC,CAEjC,KAAM,CAAAwB,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B3B,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA;AACA,KAAM,CAAA4B,SAAiB,CAAG,CACxB,CACEC,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,OAAO,CACbC,WAAW,CAAE,cAAc,CAC3BC,WAAW,CAAE,CACX,CAAEJ,EAAE,CAAE,GAAG,CAAEC,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,UAAU,CAAEG,QAAQ,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAEH,WAAW,CAAE,SAAU,CAAC,CACrG,CAAEH,EAAE,CAAE,GAAG,CAAEC,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,YAAY,CAAEG,QAAQ,CAAE,KAAK,CAAEC,MAAM,CAAE,QAAQ,CAAEH,WAAW,CAAE,SAAU,CAAC,CACzG,CAAEH,EAAE,CAAE,GAAG,CAAEC,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAE,aAAa,CAAEG,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,QAAQ,CAAEH,WAAW,CAAE,QAAS,CAAC,CAC1G,CACDI,QAAQ,CAAE,IACZ,CAAC,CACD,CACEP,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,QAAQ,CACdC,IAAI,CAAE,aAAa,CACnBC,WAAW,CAAE,kBAAkB,CAC/BC,WAAW,CAAE,CACX,CAAEJ,EAAE,CAAE,GAAG,CAAEC,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,UAAU,CAAEG,QAAQ,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAEH,WAAW,CAAE,SAAU,CAAC,CACrG,CAAEH,EAAE,CAAE,GAAG,CAAEC,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,YAAY,CAAEG,QAAQ,CAAE,KAAK,CAAEC,MAAM,CAAE,QAAQ,CAAEH,WAAW,CAAE,SAAU,CAAC,CACzG,CAAEH,EAAE,CAAE,GAAG,CAAEC,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,UAAU,CAAEG,QAAQ,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAEH,WAAW,CAAE,SAAU,CAAC,CACtG,CACDI,QAAQ,CAAE,IACZ,CAAC,CACD,CACEP,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,WAAW,CACjBC,WAAW,CAAE,iBAAiB,CAC9BC,WAAW,CAAE,CACX,CAAEJ,EAAE,CAAE,GAAG,CAAEC,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,UAAU,CAAEG,QAAQ,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAM,CAAEH,WAAW,CAAE,SAAU,CAAC,CACrG,CAAEH,EAAE,CAAE,GAAG,CAAEC,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE,kBAAkB,CAAEG,QAAQ,CAAE,WAAW,CAAEC,MAAM,CAAE,QAAQ,CAAEH,WAAW,CAAE,WAAY,CAAC,CAC1H,CACDI,QAAQ,CAAE,KACZ,CAAC,CACF,CACDtC,QAAQ,CAAC8B,SAAS,CAAC,CACrB,CAAE,MAAOS,KAAK,CAAE,CACdxE,OAAO,CAACwE,KAAK,CAAC,UAAU,CAAC,CAC3B,CAAC,OAAS,CACRrC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsC,YAAY,CAAIC,KAAa,EAAK,CACtCrC,gBAAgB,CAACqC,KAAK,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAID,KAAa,EAAK,CAC5CnC,eAAe,CAACmC,KAAK,CAAC,CACxB,CAAC,CAED,KAAM,CAAAE,YAAY,CAAGA,CAAA,GAAM,CACzB/B,cAAc,CAAC,IAAI,CAAC,CACpBG,QAAQ,CAAC6B,WAAW,CAAC,CAAC,CACtBpC,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAqC,UAAU,CAAIC,MAAY,EAAK,CACnClC,cAAc,CAACkC,MAAM,CAAC,CACtB/B,QAAQ,CAACgC,cAAc,CAACD,MAAM,CAAC,CAC/BtC,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAwC,UAAU,CAAIF,MAAY,EAAK,CACnCxB,cAAc,CAACwB,MAAM,CAAC,CACtB1B,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA6B,YAAY,CAAG,KAAO,CAAAH,MAAY,EAAK,CAC3C,GAAI,CACF;AACA/E,OAAO,CAACmF,OAAO,CAAC,MAAM,CAAC,CACvBrB,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOU,KAAK,CAAE,CACdxE,OAAO,CAACwE,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAY,kBAAkB,CAAG,KAAO,CAAAL,MAAY,EAAK,CACjD,GAAI,CACF;AACA/E,OAAO,CAACmF,OAAO,IAAAE,MAAA,CAAIN,MAAM,CAACR,QAAQ,CAAG,IAAI,CAAG,IAAI,gBAAI,CAAC,CACrDT,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOU,KAAK,CAAE,CACdxE,OAAO,CAACwE,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAc,sBAAsB,CAAIP,MAAY,EAAK,CAC/ChC,eAAe,CAACgC,MAAM,CAAC,CACvB5B,qBAAqB,CAAC4B,MAAM,CAACX,WAAW,CAACmB,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACxB,EAAE,CAAC,CAAC,CACxDrB,yBAAyB,CAAC,IAAI,CAAC,CACjC,CAAC,CAED,KAAM,CAAA8C,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA1C,QAAQ,CAAC2C,cAAc,CAAC,CAAC,CAE9C,GAAI/C,WAAW,CAAE,CACf;AACA5C,OAAO,CAACmF,OAAO,CAAC,MAAM,CAAC,CACzB,CAAC,IAAM,CACL;AACAnF,OAAO,CAACmF,OAAO,CAAC,MAAM,CAAC,CACzB,CAEA1C,mBAAmB,CAAC,KAAK,CAAC,CAC1BqB,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOU,KAAK,CAAE,CACdxE,OAAO,CAACwE,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAoB,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF;AACA5F,OAAO,CAACmF,OAAO,CAAC,QAAQ,CAAC,CACzBxC,yBAAyB,CAAC,KAAK,CAAC,CAChCmB,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOU,KAAK,CAAE,CACdxE,OAAO,CAACwE,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAqB,YAAY,CAAGA,CAAA,GAAM,CACzBpC,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAqC,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAG/D,KAAK,CAACuD,GAAG,CAACS,IAAI,EAAI,CACnC,KAAM,CAAAC,IAAS,CAAG,CAAC,CAAC,CAEpB,GAAIrC,YAAY,CAACsC,QAAQ,CAAC,MAAM,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,IAAI,CAAC/B,IAAI,CAC3D,GAAIL,YAAY,CAACsC,QAAQ,CAAC,MAAM,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,IAAI,CAAC9B,IAAI,CAC3D,GAAIN,YAAY,CAACsC,QAAQ,CAAC,aAAa,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,IAAI,CAAC7B,WAAW,EAAI,EAAE,CAC7E,GAAIP,YAAY,CAACsC,QAAQ,CAAC,aAAa,CAAC,CAAE,CACxCD,IAAI,CAAC,MAAM,CAAC,CAAGD,IAAI,CAAC5B,WAAW,CAACmB,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACvB,IAAI,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC,CAC3DF,IAAI,CAAC,MAAM,CAAC,CAAGD,IAAI,CAAC5B,WAAW,CAACgC,MAAM,CACxC,CACA,GAAIxC,YAAY,CAACsC,QAAQ,CAAC,UAAU,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGD,IAAI,CAACzB,QAAQ,CAAG,IAAI,CAAG,IAAI,CAE/E,MAAO,CAAA0B,IAAI,CACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAAI,EAAE,CAAGzF,IAAI,CAAC0F,KAAK,CAACC,aAAa,CAACR,UAAU,CAAC,CAC/C,KAAM,CAAAS,EAAE,CAAG5F,IAAI,CAAC0F,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChC7F,IAAI,CAAC0F,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,MAAM,CAAC,CAE5C;AACA,KAAM,CAAAM,QAAQ,6BAAAtB,MAAA,CAAW,GAAI,CAAAuB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAAzB,MAAA,CAAI3B,YAAY,GAAK,OAAO,CAAG,MAAM,CAAG,KAAK,CAAE,CAC9G9C,IAAI,CAACmG,SAAS,CAACP,EAAE,CAAEG,QAAQ,CAAC,CAE5B3G,OAAO,CAACmF,OAAO,CAAC,MAAM,CAAC,CACvB1B,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,MAAOe,KAAK,CAAE,CACdxE,OAAO,CAACwE,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAwC,kBAAkB,CAAIjC,MAAY,EAAyB,CAC/D,CACEkC,GAAG,CAAE,YAAY,CACjBC,IAAI,cAAE3F,IAAA,CAACH,eAAe,GAAE,CAAC,CACzB+F,KAAK,CAAE,MAAM,CACbC,OAAO,CAAEA,CAAA,GAAM9B,sBAAsB,CAACP,MAAM,CAC9C,CAAC,CACD,CACEkC,GAAG,CAAE,cAAc,CACnBC,IAAI,cAAE3F,IAAA,CAACF,YAAY,GAAE,CAAC,CACtB8F,KAAK,CAAEpC,MAAM,CAACR,QAAQ,CAAG,MAAM,CAAG,MAAM,CACxC6C,OAAO,CAAEA,CAAA,GAAMhC,kBAAkB,CAACL,MAAM,CAC1C,CAAC,CACD,CACEkC,GAAG,CAAE,QAAQ,CACbC,IAAI,cAAE3F,IAAA,CAACL,cAAc,GAAE,CAAC,CACxBiG,KAAK,CAAE,MAAM,CACbC,OAAO,CAAEA,CAAA,GAAM,CACbpH,OAAO,CAACqH,IAAI,CAAC,SAAS,CAAC,CACzB,CACF,CAAC,CACF,CAED;AACA,KAAM,CAAAC,kBAAwC,CAAG,CAC/C,CACEL,GAAG,CAAE,KAAK,CACVM,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,CACR,CAAEP,GAAG,CAAE,GAAG,CAAEM,KAAK,CAAE,OAAQ,CAAC,CAC5B,CAAEN,GAAG,CAAE,GAAG,CAAEM,KAAK,CAAE,OAAQ,CAAC,CAC5B,CAAEN,GAAG,CAAE,GAAG,CAAEM,KAAK,CAAE,OAAQ,CAAC,CAC5B,CAAEN,GAAG,CAAE,GAAG,CAAEM,KAAK,CAAE,OAAQ,CAAC,CAC5B,CAAEN,GAAG,CAAE,GAAG,CAAEM,KAAK,CAAE,OAAQ,CAAC,CAEhC,CAAC,CACD,CACEN,GAAG,CAAE,UAAU,CACfM,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CAAEP,GAAG,CAAE,GAAG,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC3B,CAAEN,GAAG,CAAE,GAAG,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC3B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC5B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAEhC,CAAC,CACD,CACEN,GAAG,CAAE,WAAW,CAChBM,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CAAEP,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC5B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC5B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC5B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAEhC,CAAC,CACD,CACEN,GAAG,CAAE,UAAU,CACfM,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CAAEP,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC5B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC5B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAEhC,CAAC,CACD,CACEN,GAAG,CAAE,QAAQ,CACbM,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CACR,CAAEP,GAAG,CAAE,GAAG,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC3B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAC5B,CAAEN,GAAG,CAAE,IAAI,CAAEM,KAAK,CAAE,MAAO,CAAC,CAEhC,CAAC,CACF,CAED,KAAM,CAAAE,OAAO,CAAG,CACd,CACEF,KAAK,CAAE,MAAM,CACbG,SAAS,CAAE,MAAM,CACjBT,GAAG,CAAE,MAAM,CACXU,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACC,IAAY,CAAE9C,MAAY,gBACjCxD,IAAA,CAAC9B,MAAM,EAACqI,IAAI,CAAC,MAAM,CAACV,OAAO,CAAEA,CAAA,GAAMnC,UAAU,CAACF,MAAM,CAAE,CAAAyC,QAAA,CACnDK,IAAI,CACC,CAEZ,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbG,SAAS,CAAE,MAAM,CACjBT,GAAG,CAAE,MAAM,CACXU,KAAK,CAAE,GACT,CAAC,CACD,CACEJ,KAAK,CAAE,IAAI,CACXG,SAAS,CAAE,aAAa,CACxBT,GAAG,CAAE,aAAa,CAClBc,QAAQ,CAAE,IACZ,CAAC,CACD,CACER,KAAK,CAAE,MAAM,CACbG,SAAS,CAAE,aAAa,CACxBT,GAAG,CAAE,aAAa,CAClBU,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGxD,WAAyB,eAChC7C,IAAA,CAACzB,GAAG,EAACkI,KAAK,CAAC,MAAM,CAAAR,QAAA,CAAEpD,WAAW,CAACgC,MAAM,CAAM,CAE/C,CAAC,CACD,CACEmB,KAAK,CAAE,IAAI,CACXG,SAAS,CAAE,UAAU,CACrBT,GAAG,CAAE,UAAU,CACfU,KAAK,CAAE,EAAE,CACTC,MAAM,CAAGrD,QAAiB,eACxBhD,IAAA,CAACzB,GAAG,EAACkI,KAAK,CAAEzD,QAAQ,CAAG,OAAO,CAAG,KAAM,CAAAiD,QAAA,CACpCjD,QAAQ,CAAG,IAAI,CAAG,IAAI,CACpB,CAET,CAAC,CACD,CACEgD,KAAK,CAAE,IAAI,CACXN,GAAG,CAAE,QAAQ,CACbU,KAAK,CAAE,GAAG,CACVM,KAAK,CAAE,OAAgB,CACvBL,MAAM,CAAEA,CAACM,CAAM,CAAEnD,MAAY,gBAC3BtD,KAAA,CAAC/B,KAAK,EAACyI,IAAI,CAAC,OAAO,CAAAX,QAAA,eACjBjG,IAAA,CAACjB,OAAO,EAACiH,KAAK,CAAC,cAAI,CAAAC,QAAA,cACjBjG,IAAA,CAAC9B,MAAM,EACLqI,IAAI,CAAC,MAAM,CACXZ,IAAI,cAAE3F,IAAA,CAACP,WAAW,GAAE,CAAE,CACtBoG,OAAO,CAAEA,CAAA,GAAMnC,UAAU,CAACF,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACVxD,IAAA,CAACjB,OAAO,EAACiH,KAAK,CAAC,cAAI,CAAAC,QAAA,cACjBjG,IAAA,CAAC9B,MAAM,EACLqI,IAAI,CAAC,MAAM,CACXZ,IAAI,cAAE3F,IAAA,CAACT,YAAY,GAAE,CAAE,CACvBsG,OAAO,CAAEA,CAAA,GAAMtC,UAAU,CAACC,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACVxD,IAAA,CAACjB,OAAO,EAACiH,KAAK,CAAC,0BAAM,CAAAC,QAAA,cACnBjG,IAAA,CAAC9B,MAAM,EACLqI,IAAI,CAAC,MAAM,CACXZ,IAAI,cAAE3F,IAAA,CAACH,eAAe,GAAE,CAAE,CAC1BgG,OAAO,CAAEA,CAAA,GAAM9B,sBAAsB,CAACP,MAAM,CAAE,CAC/C,CAAC,CACK,CAAC,cACVxD,IAAA,CAACxB,UAAU,EACTwH,KAAK,CAAC,oEAAa,CACnBa,SAAS,CAAEA,CAAA,GAAMlD,YAAY,CAACH,MAAM,CAAE,CACtCsD,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAd,QAAA,cAEfjG,IAAA,CAACjB,OAAO,EAACiH,KAAK,CAAC,cAAI,CAAAC,QAAA,cACjBjG,IAAA,CAAC9B,MAAM,EACLqI,IAAI,CAAC,MAAM,CACXS,MAAM,MACNrB,IAAI,cAAE3F,IAAA,CAACR,cAAc,GAAE,CAAE,CAC1B,CAAC,CACK,CAAC,CACA,CAAC,cACbQ,IAAA,CAAChB,QAAQ,EACPiI,IAAI,CAAE,CAAEC,KAAK,CAAEzB,kBAAkB,CAACjC,MAAM,CAAE,CAAE,CAC5C2D,OAAO,CAAE,CAAC,OAAO,CAAE,CAAAlB,QAAA,cAEnBjG,IAAA,CAAC9B,MAAM,EAACqI,IAAI,CAAC,MAAM,CAACZ,IAAI,cAAE3F,IAAA,CAACN,YAAY,GAAE,CAAE,CAAE,CAAC,CACtC,CAAC,EACN,CAEX,CAAC,CACF,CAED,mBACEQ,KAAA,QAAA+F,QAAA,eACE/F,KAAA,CAAC5B,IAAI,EAAA2H,QAAA,eACHjG,IAAA,QAAKoH,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,cAC/B/F,KAAA,CAACrB,GAAG,EAACyI,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACC,KAAK,CAAC,QAAQ,CAAAtB,QAAA,eACnCjG,IAAA,CAAClB,GAAG,EAAAmH,QAAA,cACFjG,IAAA,CAACG,KAAK,EAACqH,KAAK,CAAE,CAAE,CAACJ,KAAK,CAAE,CAAEK,MAAM,CAAE,CAAE,CAAE,CAAAxB,QAAA,CAAC,0BAAI,CAAO,CAAC,CAChD,CAAC,cACNjG,IAAA,CAAClB,GAAG,EAAC4I,IAAI,CAAC,MAAM,CAAAzB,QAAA,cACd/F,KAAA,CAACrB,GAAG,EAACyI,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAACK,OAAO,CAAC,KAAK,CAAA1B,QAAA,eAChCjG,IAAA,CAAClB,GAAG,EAAAmH,QAAA,cACFjG,IAAA,CAACI,MAAM,EACLwH,WAAW,CAAC,wDAAW,CACvBC,UAAU,MACVT,KAAK,CAAE,CAAEhB,KAAK,CAAE,GAAI,CAAE,CACtB0B,QAAQ,CAAE5E,YAAa,CACxB,CAAC,CACC,CAAC,cACNlD,IAAA,CAAClB,GAAG,EAAAmH,QAAA,cACF/F,KAAA,CAAC7B,MAAM,EACLuJ,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVT,KAAK,CAAE,CAAEhB,KAAK,CAAE,GAAI,CAAE,CACtB2B,QAAQ,CAAE3E,kBAAmB,CAAA6C,QAAA,eAE7BjG,IAAA,CAACK,MAAM,EAAC8C,KAAK,CAAC,QAAQ,CAAA8C,QAAA,CAAC,cAAE,CAAQ,CAAC,cAClCjG,IAAA,CAACK,MAAM,EAAC8C,KAAK,CAAC,UAAU,CAAA8C,QAAA,CAAC,cAAE,CAAQ,CAAC,EAC9B,CAAC,CACN,CAAC,cACNjG,IAAA,CAAClB,GAAG,EAAAmH,QAAA,cACFjG,IAAA,CAAC9B,MAAM,EACLyH,IAAI,cAAE3F,IAAA,CAACJ,cAAc,GAAE,CAAE,CACzBiG,OAAO,CAAEtD,QAAS,CAAA0D,QAAA,CACnB,cAED,CAAQ,CAAC,CACN,CAAC,cACNjG,IAAA,CAAClB,GAAG,EAAAmH,QAAA,cACFjG,IAAA,CAAC9B,MAAM,EACLyH,IAAI,cAAE3F,IAAA,CAACL,cAAc,GAAE,CAAE,CACzBkG,OAAO,CAAEvB,YAAa,CAAA2B,QAAA,CACvB,cAED,CAAQ,CAAC,CACN,CAAC,cACNjG,IAAA,CAAClB,GAAG,EAAAmH,QAAA,cACFjG,IAAA,CAAC9B,MAAM,EACLqI,IAAI,CAAC,SAAS,CACdZ,IAAI,cAAE3F,IAAA,CAACV,YAAY,GAAE,CAAE,CACvBuG,OAAO,CAAExC,YAAa,CAAA4C,QAAA,CACvB,0BAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAENjG,IAAA,CAAC/B,KAAK,EACJiI,OAAO,CAAEA,OAAQ,CACjB8B,UAAU,CAAEvH,KAAM,CAClBE,OAAO,CAAEA,OAAQ,CACjBsH,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,KAAK,CAAE5H,KAAK,CAACoE,MAAM,CACnByD,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACJ,KAAK,CAAEK,KAAK,aAAA5E,MAAA,CAAU4E,KAAK,CAAC,CAAC,CAAC,MAAA5E,MAAA,CAAI4E,KAAK,CAAC,CAAC,CAAC,oBAAA5E,MAAA,CAAQuE,KAAK,WACrE,CAAE,CACH,CAAC,EACE,CAAC,cAGPrI,IAAA,CAACtB,KAAK,EACJsH,KAAK,CAAE3E,WAAW,CAAG,MAAM,CAAG,MAAO,CACrCsH,IAAI,CAAE1H,gBAAiB,CACvB2H,IAAI,CAAE1E,aAAc,CACpB2E,QAAQ,CAAEA,CAAA,GAAM3H,mBAAmB,CAAC,KAAK,CAAE,CAC3CkF,KAAK,CAAE,GAAI,CACX0C,cAAc,MAAA7C,QAAA,cAEd/F,KAAA,CAACvB,IAAI,EACHoK,IAAI,CAAEtH,QAAS,CACfuH,MAAM,CAAC,UAAU,CACjBC,aAAa,CAAE,CACbjG,QAAQ,CAAE,IACZ,CAAE,CAAAiD,QAAA,eAEFjG,IAAA,CAACrB,IAAI,CAACuK,IAAI,EACRxG,IAAI,CAAC,MAAM,CACXkD,KAAK,CAAC,0BAAM,CACZuD,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE3K,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAwH,QAAA,cAEhDjG,IAAA,CAAC5B,KAAK,EAACwJ,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,cAEZ5H,IAAA,CAACrB,IAAI,CAACuK,IAAI,EACRxG,IAAI,CAAC,MAAM,CACXkD,KAAK,CAAC,0BAAM,CACZuD,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAE3K,OAAO,CAAE,SAAU,CAAC,CACtC,CAAE4K,OAAO,CAAE,WAAW,CAAE5K,OAAO,CAAE,kBAAmB,CAAC,CACrD,CAAAwH,QAAA,cAEFjG,IAAA,CAAC5B,KAAK,EAACwJ,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,cAEZ5H,IAAA,CAACrB,IAAI,CAACuK,IAAI,EACRxG,IAAI,CAAC,aAAa,CAClBkD,KAAK,CAAC,cAAI,CAAAK,QAAA,cAEVjG,IAAA,CAACM,QAAQ,EACPsH,WAAW,CAAC,4CAAS,CACrB0B,IAAI,CAAE,CAAE,CACT,CAAC,CACO,CAAC,cAEZtJ,IAAA,CAACrB,IAAI,CAACuK,IAAI,EACRxG,IAAI,CAAC,UAAU,CACfkD,KAAK,CAAC,cAAI,CACV2D,aAAa,CAAC,SAAS,CAAAtD,QAAA,cAEvBjG,IAAA,CAACf,MAAM,EAACuK,eAAe,CAAC,cAAI,CAACC,iBAAiB,CAAC,cAAI,CAAE,CAAC,CAC7C,CAAC,EACR,CAAC,CACF,CAAC,cAGRvJ,KAAA,CAACxB,KAAK,EACJsH,KAAK,+BAAAlC,MAAA,CAAYvC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEmB,IAAI,CAAG,CACtCiG,IAAI,CAAExH,sBAAuB,CAC7ByH,IAAI,CAAEvE,kBAAmB,CACzBwE,QAAQ,CAAEA,CAAA,GAAMzH,yBAAyB,CAAC,KAAK,CAAE,CACjDgF,KAAK,CAAE,GAAI,CACX0C,cAAc,MAAA7C,QAAA,eAEdjG,IAAA,QAAKoH,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAApB,QAAA,cAC/B/F,KAAA,CAAC/B,KAAK,EAAA8H,QAAA,eACJjG,IAAA,CAAC9B,MAAM,EACL0I,IAAI,CAAC,OAAO,CACZf,OAAO,CAAEA,CAAA,GAAMjE,qBAAqB,CAACmE,kBAAkB,CAAC2D,OAAO,CAACC,IAAI,EAClEA,IAAI,CAAC1D,QAAQ,CAAG0D,IAAI,CAAC1D,QAAQ,CAACjC,GAAG,CAAC4F,KAAK,EAAIA,KAAK,CAAClE,GAAG,CAAC,CAAG,CAACiE,IAAI,CAACjE,GAAG,CACnE,CAAC,CAAE,CAAAO,QAAA,CACJ,cAED,CAAQ,CAAC,cACTjG,IAAA,CAAC9B,MAAM,EACL0I,IAAI,CAAC,OAAO,CACZf,OAAO,CAAEA,CAAA,GAAMjE,qBAAqB,CAAC,EAAE,CAAE,CAAAqE,QAAA,CAC1C,cAED,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,cAENjG,IAAA,CAACd,IAAI,EACH2K,SAAS,MACTC,WAAW,CAAEnI,kBAAmB,CAChCoI,OAAO,CAAGD,WAAW,EAAK,CACxBlI,qBAAqB,CAACkI,WAAuB,CAAC,CAChD,CAAE,CACFE,QAAQ,CAAEjE,kBAAmB,CAC7BkE,MAAM,CAAE,GAAI,CACb,CAAC,EACG,CAAC,cAGRjK,IAAA,CAACtB,KAAK,EACJsH,KAAK,CAAC,0BAAM,CACZ2C,IAAI,CAAE9G,iBAAkB,CACxBgH,QAAQ,CAAEA,CAAA,GAAM/G,oBAAoB,CAAC,KAAK,CAAE,CAC5CoI,MAAM,CAAE,cACNlK,IAAA,CAAC9B,MAAM,EAAa2H,OAAO,CAAEA,CAAA,GAAM/D,oBAAoB,CAAC,KAAK,CAAE,CAAAmE,QAAA,CAAC,cAEhE,EAFY,OAEJ,CAAC,CACT,CACFG,KAAK,CAAE,GAAI,CAAAH,QAAA,CAEVlE,WAAW,eACV7B,KAAA,CAACd,YAAY,EAAC+K,QAAQ,MAACC,MAAM,CAAE,CAAE,CAAAnE,QAAA,eAC/BjG,IAAA,CAACZ,YAAY,CAAC8J,IAAI,EAACtD,KAAK,CAAC,0BAAM,CAACyE,IAAI,CAAE,CAAE,CAAApE,QAAA,CACrClE,WAAW,CAACW,IAAI,CACA,CAAC,cACpB1C,IAAA,CAACZ,YAAY,CAAC8J,IAAI,EAACtD,KAAK,CAAC,0BAAM,CAACyE,IAAI,CAAE,CAAE,CAAApE,QAAA,CACrClE,WAAW,CAACY,IAAI,CACA,CAAC,cACpB3C,IAAA,CAACZ,YAAY,CAAC8J,IAAI,EAACtD,KAAK,CAAC,cAAI,CAACyE,IAAI,CAAE,CAAE,CAAApE,QAAA,CACnClE,WAAW,CAACa,WAAW,EAAI,GAAG,CACd,CAAC,cACpB5C,IAAA,CAACZ,YAAY,CAAC8J,IAAI,EAACtD,KAAK,CAAC,cAAI,CAAAK,QAAA,cAC3BjG,IAAA,CAACzB,GAAG,EAACkI,KAAK,CAAE1E,WAAW,CAACiB,QAAQ,CAAG,OAAO,CAAG,KAAM,CAAAiD,QAAA,CAChDlE,WAAW,CAACiB,QAAQ,CAAG,IAAI,CAAG,IAAI,CAChC,CAAC,CACW,CAAC,cACpBhD,IAAA,CAACZ,YAAY,CAAC8J,IAAI,EAACtD,KAAK,CAAC,0BAAM,CAAAK,QAAA,cAC7BjG,IAAA,CAACzB,GAAG,EAACkI,KAAK,CAAC,MAAM,CAAAR,QAAA,CAAElE,WAAW,CAACc,WAAW,CAACgC,MAAM,CAAM,CAAC,CACvC,CAAC,cACpB7E,IAAA,CAACZ,YAAY,CAAC8J,IAAI,EAACtD,KAAK,CAAC,0BAAM,CAACyE,IAAI,CAAE,CAAE,CAAApE,QAAA,cACtCjG,IAAA,QAAKoH,KAAK,CAAE,CAAEkD,SAAS,CAAE,GAAG,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAtE,QAAA,CAC/ClE,WAAW,CAACc,WAAW,CAACmB,GAAG,CAACwG,UAAU,eACrCxK,IAAA,CAACzB,GAAG,EAAqB6I,KAAK,CAAE,CAAEK,MAAM,CAAE,KAAM,CAAE,CAAAxB,QAAA,CAC/CuE,UAAU,CAAC9H,IAAI,EADR8H,UAAU,CAAC/H,EAEhB,CACN,CAAC,CACC,CAAC,CACW,CAAC,EACR,CACf,CACI,CAAC,cAGRzC,IAAA,CAACtB,KAAK,EACJsH,KAAK,CAAC,sCAAQ,CACd2C,IAAI,CAAE1G,kBAAmB,CACzB2G,IAAI,CAAErE,aAAc,CACpBsE,QAAQ,CAAEA,CAAA,GAAM3G,qBAAqB,CAAC,KAAK,CAAE,CAC7CkE,KAAK,CAAE,GAAI,CAAAH,QAAA,cAEX/F,KAAA,CAACvB,IAAI,EAACqK,MAAM,CAAC,UAAU,CAAA/C,QAAA,eACrBjG,IAAA,CAACrB,IAAI,CAACuK,IAAI,EAACtD,KAAK,CAAC,0BAAM,CAAAK,QAAA,cACrB/F,KAAA,CAAC7B,MAAM,EACL8E,KAAK,CAAEhB,YAAa,CACpB4F,QAAQ,CAAE3F,eAAgB,CAC1BgF,KAAK,CAAE,CAAEhB,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,eAEzBjG,IAAA,CAACK,MAAM,EAAC8C,KAAK,CAAC,OAAO,CAAA8C,QAAA,CAAC,eAAa,CAAQ,CAAC,cAC5CjG,IAAA,CAACK,MAAM,EAAC8C,KAAK,CAAC,KAAK,CAAA8C,QAAA,CAAC,YAAU,CAAQ,CAAC,EACjC,CAAC,CACA,CAAC,cAEZjG,IAAA,CAACrB,IAAI,CAACuK,IAAI,EAACtD,KAAK,CAAC,0BAAM,CAAAK,QAAA,cACrBjG,IAAA,CAACb,QAAQ,CAACsL,KAAK,EACbtH,KAAK,CAAEd,YAAa,CACpB0F,QAAQ,CAAEzF,eAAgB,CAC1B8E,KAAK,CAAE,CAAEhB,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,cAEzB/F,KAAA,CAACrB,GAAG,EAACyI,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAArB,QAAA,eAClBjG,IAAA,CAAClB,GAAG,EAACuL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjG,IAAA,CAACb,QAAQ,EAACgE,KAAK,CAAC,MAAM,CAAA8C,QAAA,CAAC,0BAAI,CAAU,CAAC,CACnC,CAAC,cACNjG,IAAA,CAAClB,GAAG,EAACuL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjG,IAAA,CAACb,QAAQ,EAACgE,KAAK,CAAC,MAAM,CAAA8C,QAAA,CAAC,0BAAI,CAAU,CAAC,CACnC,CAAC,cACNjG,IAAA,CAAClB,GAAG,EAACuL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjG,IAAA,CAACb,QAAQ,EAACgE,KAAK,CAAC,aAAa,CAAA8C,QAAA,CAAC,cAAE,CAAU,CAAC,CACxC,CAAC,cACNjG,IAAA,CAAClB,GAAG,EAACuL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjG,IAAA,CAACb,QAAQ,EAACgE,KAAK,CAAC,aAAa,CAAA8C,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC1C,CAAC,cACNjG,IAAA,CAAClB,GAAG,EAACuL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZjG,IAAA,CAACb,QAAQ,EAACgE,KAAK,CAAC,UAAU,CAAA8C,QAAA,CAAC,cAAE,CAAU,CAAC,CACrC,CAAC,EACH,CAAC,CACQ,CAAC,CACR,CAAC,cAEZjG,IAAA,CAACrB,IAAI,CAACuK,IAAI,EAAAjD,QAAA,cACRjG,IAAA,QAAKoH,KAAK,CAAE,CAAEsD,OAAO,CAAE,UAAU,CAAEC,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAA3E,QAAA,cACnF/F,KAAA,QAAKkH,KAAK,CAAE,CAAEyD,QAAQ,CAAE,MAAM,CAAEpE,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,EAAC,qBAC3C,CAACxF,KAAK,CAACoE,MAAM,CAAC,iCACpB,EAAK,CAAC,CACH,CAAC,CACG,CAAC,EACR,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}