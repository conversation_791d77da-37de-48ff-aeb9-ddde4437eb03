{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\components\\\\LoadingSpinner.tsx\";\nimport React from 'react';\nimport { Spin } from 'antd';\nimport { LoadingOutlined } from '@ant-design/icons';\nimport './LoadingSpinner.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  showText = true,\n  text = '加载中...',\n  fullscreen = false,\n  overlay = false,\n  size = 'default',\n  ...props\n}) => {\n  const antIcon = /*#__PURE__*/_jsxDEV(LoadingOutlined, {\n    style: {\n      fontSize: size === 'large' ? 24 : 16\n    },\n    spin: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 19\n  }, this);\n  const spinner = /*#__PURE__*/_jsxDEV(Spin, {\n    indicator: antIcon,\n    tip: showText ? text : undefined,\n    size: size,\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n  if (fullscreen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `loading-spinner-fullscreen ${overlay ? 'with-overlay' : ''}`,\n      children: spinner\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading-spinner-container\",\n    children: spinner\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "Spin", "LoadingOutlined", "jsxDEV", "_jsxDEV", "LoadingSpinner", "showText", "text", "fullscreen", "overlay", "size", "props", "antIcon", "style", "fontSize", "spin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "spinner", "indicator", "tip", "undefined", "className", "children", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { Spin, SpinProps } from 'antd';\nimport { LoadingOutlined } from '@ant-design/icons';\nimport './LoadingSpinner.css';\n\ninterface LoadingSpinnerProps extends SpinProps {\n  /** 是否显示加载文本 */\n  showText?: boolean;\n  /** 自定义加载文本 */\n  text?: string;\n  /** 是否全屏显示 */\n  fullscreen?: boolean;\n  /** 是否显示遮罩层 */\n  overlay?: boolean;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  showText = true,\n  text = '加载中...',\n  fullscreen = false,\n  overlay = false,\n  size = 'default',\n  ...props\n}) => {\n  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 24 : 16 }} spin />;\n\n  const spinner = (\n    <Spin\n      indicator={antIcon}\n      tip={showText ? text : undefined}\n      size={size}\n      {...props}\n    />\n  );\n\n  if (fullscreen) {\n    return (\n      <div className={`loading-spinner-fullscreen ${overlay ? 'with-overlay' : ''}`}>\n        {spinner}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"loading-spinner-container\">\n      {spinner}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAmB,MAAM;AACtC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa9B,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,QAAQ,GAAG,IAAI;EACfC,IAAI,GAAG,QAAQ;EACfC,UAAU,GAAG,KAAK;EAClBC,OAAO,GAAG,KAAK;EACfC,IAAI,GAAG,SAAS;EAChB,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,OAAO,gBAAGR,OAAA,CAACF,eAAe;IAACW,KAAK,EAAE;MAAEC,QAAQ,EAAEJ,IAAI,KAAK,OAAO,GAAG,EAAE,GAAG;IAAG,CAAE;IAACK,IAAI;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAEzF,MAAMC,OAAO,gBACXhB,OAAA,CAACH,IAAI;IACHoB,SAAS,EAAET,OAAQ;IACnBU,GAAG,EAAEhB,QAAQ,GAAGC,IAAI,GAAGgB,SAAU;IACjCb,IAAI,EAAEA,IAAK;IAAA,GACPC;EAAK;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACF;EAED,IAAIX,UAAU,EAAE;IACd,oBACEJ,OAAA;MAAKoB,SAAS,EAAE,8BAA8Bf,OAAO,GAAG,cAAc,GAAG,EAAE,EAAG;MAAAgB,QAAA,EAC3EL;IAAO;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACEf,OAAA;IAAKoB,SAAS,EAAC,2BAA2B;IAAAC,QAAA,EACvCL;EAAO;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACO,EAAA,GAhCIrB,cAA6C;AAkCnD,eAAeA,cAAc;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}