{"ast": null, "code": "// 格式化工具函数\nimport{formatCurrency as _formatCurrency,formatDate as _formatDate}from'./index';// 重新导出格式化函数\nexport const formatCurrency=_formatCurrency;export const formatDate=_formatDate;// 导出默认对象\nconst formatUtils={formatCurrency,formatDate};export default formatUtils;", "map": {"version": 3, "names": ["formatCurrency", "_formatCurrency", "formatDate", "_formatDate", "formatUtils"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/utils/format.ts"], "sourcesContent": ["// 格式化工具函数\r\nimport { formatCurrency as _formatCurrency, formatDate as _formatDate } from './index';\r\n\r\n// 重新导出格式化函数\r\nexport const formatCurrency = _formatCurrency;\r\nexport const formatDate = _formatDate;\r\n\r\n// 导出默认对象\r\nconst formatUtils = {\r\n  formatCurrency,\r\n  formatDate\r\n};\r\n\r\nexport default formatUtils;"], "mappings": "AAAA;AACA,OAASA,cAAc,GAAI,CAAAC,eAAe,CAAEC,UAAU,GAAI,CAAAC,WAAW,KAAQ,SAAS,CAEtF;AACA,MAAO,MAAM,CAAAH,cAAc,CAAGC,eAAe,CAC7C,MAAO,MAAM,CAAAC,UAAU,CAAGC,WAAW,CAErC;AACA,KAAM,CAAAC,WAAW,CAAG,CAClBJ,cAAc,CACdE,UACF,CAAC,CAED,cAAe,CAAAE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}