{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ConfigProvider, App as AntdApp } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport dayjs from 'dayjs';\nimport 'dayjs/locale/zh-cn';\nimport { store } from './store';\nimport { useAppDispatch, useAppSelector } from './hooks/redux';\nimport { initializeAuth } from './store/slices/authSlice';\nimport { ROUTES, THEME_CONFIG } from './constants';\n\n// 页面组件\nimport LoginPage from './pages/auth/LoginPage';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport MainLayout from './components/layout/MainLayout';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\n\n// BOM管理页面\nimport CoreBOMListPage from './pages/bom/CoreBOMListPage';\nimport CoreBOMCreatePage from './pages/bom/CoreBOMCreatePage';\nimport CoreBOMEditPage from './pages/bom/CoreBOMEditPage';\nimport CoreBOMViewPage from './pages/bom/CoreBOMViewPage';\nimport OrderBOMListPage from './pages/bom/OrderBOMListPage';\nimport OrderBOMCreatePage from './pages/bom/OrderBOMCreatePage';\nimport OrderBOMDerivePage from './pages/bom/OrderBOMDerivePage';\nimport OrderBOMViewPage from './pages/bom/OrderBOMViewPage';\n\n// 物料管理页面\nimport MaterialListPage from './pages/material/MaterialListPage';\nimport MaterialCreatePage from './pages/material/MaterialCreatePage';\nimport MaterialEditPage from './pages/material/MaterialEditPage';\n\n// 库存管理页面\nimport InventoryListPage from './pages/inventory/InventoryListPage';\nimport InventoryReceivePage from './pages/inventory/InventoryReceivePage';\nimport InventoryIssuePage from './pages/inventory/InventoryIssuePage';\nimport InventoryAdjustPage from './pages/inventory/InventoryAdjustPage';\nimport RemnantListPage from './pages/inventory/RemnantListPage';\nimport CuttingPlanPage from './pages/inventory/CuttingPlanPage';\n\n// 采购管理页面\nimport PurchaseListPage from './pages/purchase/PurchaseListPage';\nimport PurchaseRequisitionPage from './pages/purchase/PurchaseRequisitionPage';\nimport MRPCalculationPage from './pages/purchase/MRPCalculationPage';\nimport PurchaseOptimizationPage from './pages/purchase/PurchaseOptimizationPage';\n\n// 成本管理页面\nimport CostAnalysisPage from './pages/cost/CostAnalysisPage';\nimport CostReportsPage from './pages/cost/CostReportsPage';\nimport WasteTrackingPage from './pages/cost/WasteTrackingPage';\n\n// 服务管理页面\nimport ServiceBOMListPage from './pages/service/ServiceBOMListPage';\nimport DeviceArchivePage from './pages/service/DeviceArchivePage';\nimport MaintenancePage from './pages/service/MaintenancePage';\n\n// ECN管理页面\nimport ECNListPage from './pages/ecn/ECNListPage';\nimport ECNCreatePage from './pages/ecn/ECNCreatePage';\nimport ECNReviewPage from './pages/ecn/ECNReviewPage';\n\n// 报告页面\nimport ReportsPage from './pages/reports/ReportsPage';\nimport DashboardConfigPage from './pages/reports/DashboardConfigPage';\n\n// 系统管理页面\nimport UserListPage from './pages/system/UserListPage';\nimport RoleListPage from './pages/system/RoleListPage';\nimport PermissionListPage from './pages/system/PermissionListPage';\nimport SystemConfigPage from './pages/system/SystemConfigPage';\n\n// 移动端页面\nimport MobilePage from './pages/mobile/MobilePage';\nimport MobileScanPage from './pages/mobile/MobileScanPage';\nimport MobileInventoryPage from './pages/mobile/MobileInventoryPage';\n\n// 设置dayjs中文\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndayjs.locale('zh-cn');\nconst AppContent = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    isAuthenticated\n  } = useAppSelector(state => state.auth);\n  useEffect(() => {\n    dispatch(initializeAuth());\n  }, [dispatch]);\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: ROUTES.LOGIN,\n      element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(MainLayout, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 32\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        index: true,\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: ROUTES.DASHBOARD,\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 31\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.DASHBOARD,\n        element: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CORE_BOM,\n        element: /*#__PURE__*/_jsxDEV(CoreBOMListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CORE_BOM_CREATE,\n        element: /*#__PURE__*/_jsxDEV(CoreBOMCreatePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CORE_BOM_EDIT,\n        element: /*#__PURE__*/_jsxDEV(CoreBOMEditPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CORE_BOM_VIEW,\n        element: /*#__PURE__*/_jsxDEV(CoreBOMViewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ORDER_BOM,\n        element: /*#__PURE__*/_jsxDEV(OrderBOMListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ORDER_BOM_CREATE,\n        element: /*#__PURE__*/_jsxDEV(OrderBOMCreatePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ORDER_BOM_DERIVE,\n        element: /*#__PURE__*/_jsxDEV(OrderBOMDerivePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ORDER_BOM_VIEW,\n        element: /*#__PURE__*/_jsxDEV(OrderBOMViewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MATERIALS,\n        element: /*#__PURE__*/_jsxDEV(MaterialListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MATERIALS_CREATE,\n        element: /*#__PURE__*/_jsxDEV(MaterialCreatePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MATERIALS_EDIT,\n        element: /*#__PURE__*/_jsxDEV(MaterialEditPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.INVENTORY,\n        element: /*#__PURE__*/_jsxDEV(InventoryListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.INVENTORY_RECEIVE,\n        element: /*#__PURE__*/_jsxDEV(InventoryReceivePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 57\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.INVENTORY_ISSUE,\n        element: /*#__PURE__*/_jsxDEV(InventoryIssuePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.INVENTORY_ADJUST,\n        element: /*#__PURE__*/_jsxDEV(InventoryAdjustPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.REMNANTS,\n        element: /*#__PURE__*/_jsxDEV(RemnantListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CUTTING_PLAN,\n        element: /*#__PURE__*/_jsxDEV(CuttingPlanPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.PURCHASE,\n        element: /*#__PURE__*/_jsxDEV(PurchaseListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.PURCHASE_REQUISITION,\n        element: /*#__PURE__*/_jsxDEV(PurchaseRequisitionPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 60\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MRP_CALCULATION,\n        element: /*#__PURE__*/_jsxDEV(MRPCalculationPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.PURCHASE_OPTIMIZATION,\n        element: /*#__PURE__*/_jsxDEV(PurchaseOptimizationPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 61\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.COST_ANALYSIS,\n        element: /*#__PURE__*/_jsxDEV(CostAnalysisPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.COST_REPORTS,\n        element: /*#__PURE__*/_jsxDEV(CostReportsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.WASTE_TRACKING,\n        element: /*#__PURE__*/_jsxDEV(WasteTrackingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.SERVICE_BOM,\n        element: /*#__PURE__*/_jsxDEV(ServiceBOMListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.DEVICE_ARCHIVE,\n        element: /*#__PURE__*/_jsxDEV(DeviceArchivePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MAINTENANCE,\n        element: /*#__PURE__*/_jsxDEV(MaintenancePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ECN,\n        element: /*#__PURE__*/_jsxDEV(ECNListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ECN_CREATE,\n        element: /*#__PURE__*/_jsxDEV(ECNCreatePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 50\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ECN_REVIEW,\n        element: /*#__PURE__*/_jsxDEV(ECNReviewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 50\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.REPORTS,\n        element: /*#__PURE__*/_jsxDEV(ReportsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 47\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.DASHBOARD_CONFIG,\n        element: /*#__PURE__*/_jsxDEV(DashboardConfigPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.USERS,\n        element: /*#__PURE__*/_jsxDEV(UserListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ROLES,\n        element: /*#__PURE__*/_jsxDEV(RoleListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.PERMISSIONS,\n        element: /*#__PURE__*/_jsxDEV(PermissionListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.SYSTEM_CONFIG,\n        element: /*#__PURE__*/_jsxDEV(SystemConfigPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MOBILE,\n        element: /*#__PURE__*/_jsxDEV(MobilePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MOBILE_SCAN,\n        element: /*#__PURE__*/_jsxDEV(MobileScanPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MOBILE_INVENTORY,\n        element: /*#__PURE__*/_jsxDEV(MobileInventoryPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: ROUTES.DASHBOARD,\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(AppContent, \"S0ObmGEX/4toGTRN7qlPtU2bGbo=\", false, function () {\n  return [useAppDispatch, useAppSelector];\n});\n_c = AppContent;\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(ConfigProvider, {\n      locale: zhCN,\n      theme: {\n        token: {\n          colorPrimary: THEME_CONFIG.primaryColor,\n          borderRadius: THEME_CONFIG.borderRadius\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(AntdApp, {\n        children: /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "App", "AntdApp", "zhCN", "dayjs", "store", "useAppDispatch", "useAppSelector", "initializeAuth", "ROUTES", "THEME_CONFIG", "LoginPage", "DashboardPage", "MainLayout", "ProtectedRoute", "CoreBOMListPage", "CoreBOMCreatePage", "CoreBOMEditPage", "CoreBOMViewPage", "OrderBOMListPage", "OrderBOMCreatePage", "OrderBOMDerivePage", "OrderBOMViewPage", "MaterialListPage", "MaterialCreatePage", "MaterialEditPage", "InventoryListPage", "InventoryReceivePage", "InventoryIssuePage", "InventoryAdjustPage", "RemnantListPage", "CuttingPlanPage", "PurchaseListPage", "PurchaseRequisitionPage", "MRPCalculationPage", "PurchaseOptimizationPage", "CostAnalysisPage", "CostReportsPage", "WasteTrackingPage", "ServiceBOMListPage", "DeviceArchivePage", "MaintenancePage", "ECNListPage", "ECNCreatePage", "ECNReviewPage", "ReportsPage", "DashboardConfigPage", "UserListPage", "RoleListPage", "PermissionListPage", "SystemConfigPage", "MobilePage", "MobileScanPage", "MobileInventoryPage", "jsxDEV", "_jsxDEV", "locale", "A<PERSON><PERSON><PERSON>nt", "_s", "dispatch", "isAuthenticated", "state", "auth", "children", "path", "LOGIN", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "DASHBOARD", "replace", "CORE_BOM", "CORE_BOM_CREATE", "CORE_BOM_EDIT", "CORE_BOM_VIEW", "ORDER_BOM", "ORDER_BOM_CREATE", "ORDER_BOM_DERIVE", "ORDER_BOM_VIEW", "MATERIALS", "MATERIALS_CREATE", "MATERIALS_EDIT", "INVENTORY", "INVENTORY_RECEIVE", "INVENTORY_ISSUE", "INVENTORY_ADJUST", "REMNANTS", "CUTTING_PLAN", "PURCHASE", "PURCHASE_REQUISITION", "MRP_CALCULATION", "PURCHASE_OPTIMIZATION", "COST_ANALYSIS", "COST_REPORTS", "WASTE_TRACKING", "SERVICE_BOM", "DEVICE_ARCHIVE", "MAINTENANCE", "ECN", "ECN_CREATE", "ECN_REVIEW", "REPORTS", "DASHBOARD_CONFIG", "USERS", "ROLES", "PERMISSIONS", "SYSTEM_CONFIG", "MOBILE", "MOBILE_SCAN", "MOBILE_INVENTORY", "_c", "theme", "token", "colorPrimary", "primaryColor", "borderRadius", "_c2", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ConfigProvider, App as AntdApp } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport dayjs from 'dayjs';\nimport 'dayjs/locale/zh-cn';\n\nimport { store } from './store';\nimport { useAppDispatch, useAppSelector } from './hooks/redux';\nimport { initializeAuth } from './store/slices/authSlice';\nimport { ROUTES, THEME_CONFIG } from './constants';\n\n// 页面组件\nimport LoginPage from './pages/auth/LoginPage';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport MainLayout from './components/layout/MainLayout';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\n\n// BOM管理页面\nimport CoreBOMListPage from './pages/bom/CoreBOMListPage';\nimport CoreBOMCreatePage from './pages/bom/CoreBOMCreatePage';\nimport CoreBOMEditPage from './pages/bom/CoreBOMEditPage';\nimport CoreBOMViewPage from './pages/bom/CoreBOMViewPage';\nimport OrderBOMListPage from './pages/bom/OrderBOMListPage';\nimport OrderBOMCreatePage from './pages/bom/OrderBOMCreatePage';\nimport OrderBOMDerivePage from './pages/bom/OrderBOMDerivePage';\nimport OrderBOMViewPage from './pages/bom/OrderBOMViewPage';\n\n// 物料管理页面\nimport MaterialListPage from './pages/material/MaterialListPage';\nimport MaterialCreatePage from './pages/material/MaterialCreatePage';\nimport MaterialEditPage from './pages/material/MaterialEditPage';\n\n// 库存管理页面\nimport InventoryListPage from './pages/inventory/InventoryListPage';\nimport InventoryReceivePage from './pages/inventory/InventoryReceivePage';\nimport InventoryIssuePage from './pages/inventory/InventoryIssuePage';\nimport InventoryAdjustPage from './pages/inventory/InventoryAdjustPage';\nimport RemnantListPage from './pages/inventory/RemnantListPage';\nimport CuttingPlanPage from './pages/inventory/CuttingPlanPage';\n\n// 采购管理页面\nimport PurchaseListPage from './pages/purchase/PurchaseListPage';\nimport PurchaseRequisitionPage from './pages/purchase/PurchaseRequisitionPage';\nimport MRPCalculationPage from './pages/purchase/MRPCalculationPage';\nimport PurchaseOptimizationPage from './pages/purchase/PurchaseOptimizationPage';\n\n// 成本管理页面\nimport CostAnalysisPage from './pages/cost/CostAnalysisPage';\nimport CostReportsPage from './pages/cost/CostReportsPage';\nimport WasteTrackingPage from './pages/cost/WasteTrackingPage';\n\n// 服务管理页面\nimport ServiceBOMListPage from './pages/service/ServiceBOMListPage';\nimport DeviceArchivePage from './pages/service/DeviceArchivePage';\nimport MaintenancePage from './pages/service/MaintenancePage';\n\n// ECN管理页面\nimport ECNListPage from './pages/ecn/ECNListPage';\nimport ECNCreatePage from './pages/ecn/ECNCreatePage';\nimport ECNReviewPage from './pages/ecn/ECNReviewPage';\n\n// 报告页面\nimport ReportsPage from './pages/reports/ReportsPage';\nimport DashboardConfigPage from './pages/reports/DashboardConfigPage';\n\n// 系统管理页面\nimport UserListPage from './pages/system/UserListPage';\nimport RoleListPage from './pages/system/RoleListPage';\nimport PermissionListPage from './pages/system/PermissionListPage';\nimport SystemConfigPage from './pages/system/SystemConfigPage';\n\n// 移动端页面\nimport MobilePage from './pages/mobile/MobilePage';\nimport MobileScanPage from './pages/mobile/MobileScanPage';\nimport MobileInventoryPage from './pages/mobile/MobileInventoryPage';\n\n// 设置dayjs中文\ndayjs.locale('zh-cn');\n\nconst AppContent: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { isAuthenticated } = useAppSelector(state => state.auth);\n\n  useEffect(() => {\n    dispatch(initializeAuth());\n  }, [dispatch]);\n\n  return (\n    <Routes>\n      {/* 登录页面 */}\n      <Route path={ROUTES.LOGIN} element={<LoginPage />} />\n      \n      {/* 受保护的路由 */}\n      <Route path=\"/\" element={<ProtectedRoute><MainLayout /></ProtectedRoute>}>\n        {/* 仪表板 */}\n        <Route index element={<Navigate to={ROUTES.DASHBOARD} replace />} />\n        <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />\n        \n        {/* BOM管理 */}\n        <Route path={ROUTES.CORE_BOM} element={<CoreBOMListPage />} />\n        <Route path={ROUTES.CORE_BOM_CREATE} element={<CoreBOMCreatePage />} />\n        <Route path={ROUTES.CORE_BOM_EDIT} element={<CoreBOMEditPage />} />\n        <Route path={ROUTES.CORE_BOM_VIEW} element={<CoreBOMViewPage />} />\n        \n        <Route path={ROUTES.ORDER_BOM} element={<OrderBOMListPage />} />\n        <Route path={ROUTES.ORDER_BOM_CREATE} element={<OrderBOMCreatePage />} />\n        <Route path={ROUTES.ORDER_BOM_DERIVE} element={<OrderBOMDerivePage />} />\n        <Route path={ROUTES.ORDER_BOM_VIEW} element={<OrderBOMViewPage />} />\n        \n        {/* 物料管理 */}\n        <Route path={ROUTES.MATERIALS} element={<MaterialListPage />} />\n        <Route path={ROUTES.MATERIALS_CREATE} element={<MaterialCreatePage />} />\n        <Route path={ROUTES.MATERIALS_EDIT} element={<MaterialEditPage />} />\n        \n        {/* 库存管理 */}\n        <Route path={ROUTES.INVENTORY} element={<InventoryListPage />} />\n        <Route path={ROUTES.INVENTORY_RECEIVE} element={<InventoryReceivePage />} />\n        <Route path={ROUTES.INVENTORY_ISSUE} element={<InventoryIssuePage />} />\n        <Route path={ROUTES.INVENTORY_ADJUST} element={<InventoryAdjustPage />} />\n        <Route path={ROUTES.REMNANTS} element={<RemnantListPage />} />\n        <Route path={ROUTES.CUTTING_PLAN} element={<CuttingPlanPage />} />\n        \n        {/* 采购管理 */}\n        <Route path={ROUTES.PURCHASE} element={<PurchaseListPage />} />\n        <Route path={ROUTES.PURCHASE_REQUISITION} element={<PurchaseRequisitionPage />} />\n        <Route path={ROUTES.MRP_CALCULATION} element={<MRPCalculationPage />} />\n        <Route path={ROUTES.PURCHASE_OPTIMIZATION} element={<PurchaseOptimizationPage />} />\n        \n        {/* 成本管理 */}\n        <Route path={ROUTES.COST_ANALYSIS} element={<CostAnalysisPage />} />\n        <Route path={ROUTES.COST_REPORTS} element={<CostReportsPage />} />\n        <Route path={ROUTES.WASTE_TRACKING} element={<WasteTrackingPage />} />\n        \n        {/* 服务管理 */}\n        <Route path={ROUTES.SERVICE_BOM} element={<ServiceBOMListPage />} />\n        <Route path={ROUTES.DEVICE_ARCHIVE} element={<DeviceArchivePage />} />\n        <Route path={ROUTES.MAINTENANCE} element={<MaintenancePage />} />\n        \n        {/* ECN管理 */}\n        <Route path={ROUTES.ECN} element={<ECNListPage />} />\n        <Route path={ROUTES.ECN_CREATE} element={<ECNCreatePage />} />\n        <Route path={ROUTES.ECN_REVIEW} element={<ECNReviewPage />} />\n        \n        {/* 报告 */}\n        <Route path={ROUTES.REPORTS} element={<ReportsPage />} />\n        <Route path={ROUTES.DASHBOARD_CONFIG} element={<DashboardConfigPage />} />\n        \n        {/* 系统管理 */}\n        <Route path={ROUTES.USERS} element={<UserListPage />} />\n        <Route path={ROUTES.ROLES} element={<RoleListPage />} />\n        <Route path={ROUTES.PERMISSIONS} element={<PermissionListPage />} />\n        <Route path={ROUTES.SYSTEM_CONFIG} element={<SystemConfigPage />} />\n        \n        {/* 移动端 */}\n        <Route path={ROUTES.MOBILE} element={<MobilePage />} />\n        <Route path={ROUTES.MOBILE_SCAN} element={<MobileScanPage />} />\n        <Route path={ROUTES.MOBILE_INVENTORY} element={<MobileInventoryPage />} />\n      </Route>\n      \n      {/* 404页面 */}\n      <Route path=\"*\" element={<Navigate to={ROUTES.DASHBOARD} replace />} />\n    </Routes>\n  );\n};\n\nconst App: React.FC = () => {\n  return (\n    <Provider store={store}>\n      <ConfigProvider\n        locale={zhCN}\n        theme={{\n          token: {\n            colorPrimary: THEME_CONFIG.primaryColor,\n            borderRadius: THEME_CONFIG.borderRadius,\n          },\n        }}\n      >\n        <AntdApp>\n          <Router>\n            <AppContent />\n          </Router>\n        </AntdApp>\n      </ConfigProvider>\n    </Provider>\n  );\n};\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,cAAc,EAAEC,GAAG,IAAIC,OAAO,QAAQ,MAAM;AACrD,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAE3B,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,cAAc,EAAEC,cAAc,QAAQ,eAAe;AAC9D,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,MAAM,EAAEC,YAAY,QAAQ,aAAa;;AAElD;AACA,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,cAAc,MAAM,kCAAkC;;AAE7D;AACA,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,gBAAgB,MAAM,8BAA8B;;AAE3D;AACA,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,gBAAgB,MAAM,mCAAmC;;AAEhE;AACA,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,oBAAoB,MAAM,wCAAwC;AACzE,OAAOC,kBAAkB,MAAM,sCAAsC;AACrE,OAAOC,mBAAmB,MAAM,uCAAuC;AACvE,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,OAAOC,eAAe,MAAM,mCAAmC;;AAE/D;AACA,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,wBAAwB,MAAM,2CAA2C;;AAEhF;AACA,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,iBAAiB,MAAM,gCAAgC;;AAE9D;AACA,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,eAAe,MAAM,iCAAiC;;AAE7D;AACA,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;;AAErD;AACA,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,mBAAmB,MAAM,qCAAqC;;AAErE;AACA,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,gBAAgB,MAAM,iCAAiC;;AAE9D;AACA,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAnD,KAAK,CAACoD,MAAM,CAAC,OAAO,CAAC;AAErB,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGrD,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEsD;EAAgB,CAAC,GAAGrD,cAAc,CAACsD,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAE/DrE,SAAS,CAAC,MAAM;IACdkE,QAAQ,CAACnD,cAAc,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACmD,QAAQ,CAAC,CAAC;EAEd,oBACEJ,OAAA,CAAC3D,MAAM;IAAAmE,QAAA,gBAELR,OAAA,CAAC1D,KAAK;MAACmE,IAAI,EAAEvD,MAAM,CAACwD,KAAM;MAACC,OAAO,eAAEX,OAAA,CAAC5C,SAAS;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrDf,OAAA,CAAC1D,KAAK;MAACmE,IAAI,EAAC,GAAG;MAACE,OAAO,eAAEX,OAAA,CAACzC,cAAc;QAAAiD,QAAA,eAACR,OAAA,CAAC1C,UAAU;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAE;MAAAP,QAAA,gBAEvER,OAAA,CAAC1D,KAAK;QAAC0E,KAAK;QAACL,OAAO,eAAEX,OAAA,CAACzD,QAAQ;UAAC0E,EAAE,EAAE/D,MAAM,CAACgE,SAAU;UAACC,OAAO;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACgE,SAAU;QAACP,OAAO,eAAEX,OAAA,CAAC3C,aAAa;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG7Df,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACkE,QAAS;QAACT,OAAO,eAAEX,OAAA,CAACxC,eAAe;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Df,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACmE,eAAgB;QAACV,OAAO,eAAEX,OAAA,CAACvC,iBAAiB;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACoE,aAAc;QAACX,OAAO,eAAEX,OAAA,CAACtC,eAAe;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACqE,aAAc;QAACZ,OAAO,eAAEX,OAAA,CAACrC,eAAe;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEnEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACsE,SAAU;QAACb,OAAO,eAAEX,OAAA,CAACpC,gBAAgB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACuE,gBAAiB;QAACd,OAAO,eAAEX,OAAA,CAACnC,kBAAkB;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACwE,gBAAiB;QAACf,OAAO,eAAEX,OAAA,CAAClC,kBAAkB;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACyE,cAAe;QAAChB,OAAO,eAAEX,OAAA,CAACjC,gBAAgB;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGrEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC0E,SAAU;QAACjB,OAAO,eAAEX,OAAA,CAAChC,gBAAgB;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC2E,gBAAiB;QAAClB,OAAO,eAAEX,OAAA,CAAC/B,kBAAkB;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC4E,cAAe;QAACnB,OAAO,eAAEX,OAAA,CAAC9B,gBAAgB;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGrEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC6E,SAAU;QAACpB,OAAO,eAAEX,OAAA,CAAC7B,iBAAiB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC8E,iBAAkB;QAACrB,OAAO,eAAEX,OAAA,CAAC5B,oBAAoB;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5Ef,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC+E,eAAgB;QAACtB,OAAO,eAAEX,OAAA,CAAC3B,kBAAkB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACgF,gBAAiB;QAACvB,OAAO,eAAEX,OAAA,CAAC1B,mBAAmB;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1Ef,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACiF,QAAS;QAACxB,OAAO,eAAEX,OAAA,CAACzB,eAAe;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Df,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACkF,YAAa;QAACzB,OAAO,eAAEX,OAAA,CAACxB,eAAe;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGlEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACmF,QAAS;QAAC1B,OAAO,eAAEX,OAAA,CAACvB,gBAAgB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/Df,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACoF,oBAAqB;QAAC3B,OAAO,eAAEX,OAAA,CAACtB,uBAAuB;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClFf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACqF,eAAgB;QAAC5B,OAAO,eAAEX,OAAA,CAACrB,kBAAkB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACsF,qBAAsB;QAAC7B,OAAO,eAAEX,OAAA,CAACpB,wBAAwB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpFf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACuF,aAAc;QAAC9B,OAAO,eAAEX,OAAA,CAACnB,gBAAgB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACwF,YAAa;QAAC/B,OAAO,eAAEX,OAAA,CAAClB,eAAe;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACyF,cAAe;QAAChC,OAAO,eAAEX,OAAA,CAACjB,iBAAiB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGtEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC0F,WAAY;QAACjC,OAAO,eAAEX,OAAA,CAAChB,kBAAkB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC2F,cAAe;QAAClC,OAAO,eAAEX,OAAA,CAACf,iBAAiB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC4F,WAAY;QAACnC,OAAO,eAAEX,OAAA,CAACd,eAAe;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGjEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC6F,GAAI;QAACpC,OAAO,eAAEX,OAAA,CAACb,WAAW;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC8F,UAAW;QAACrC,OAAO,eAAEX,OAAA,CAACZ,aAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Df,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAAC+F,UAAW;QAACtC,OAAO,eAAEX,OAAA,CAACX,aAAa;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG9Df,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACgG,OAAQ;QAACvC,OAAO,eAAEX,OAAA,CAACV,WAAW;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzDf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACiG,gBAAiB;QAACxC,OAAO,eAAEX,OAAA,CAACT,mBAAmB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1Ef,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACkG,KAAM;QAACzC,OAAO,eAAEX,OAAA,CAACR,YAAY;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACmG,KAAM;QAAC1C,OAAO,eAAEX,OAAA,CAACP,YAAY;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACoG,WAAY;QAAC3C,OAAO,eAAEX,OAAA,CAACN,kBAAkB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACqG,aAAc;QAAC5C,OAAO,eAAEX,OAAA,CAACL,gBAAgB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACsG,MAAO;QAAC7C,OAAO,eAAEX,OAAA,CAACJ,UAAU;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACuG,WAAY;QAAC9C,OAAO,eAAEX,OAAA,CAACH,cAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEf,OAAA,CAAC1D,KAAK;QAACmE,IAAI,EAAEvD,MAAM,CAACwG,gBAAiB;QAAC/C,OAAO,eAAEX,OAAA,CAACF,mBAAmB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eAGRf,OAAA,CAAC1D,KAAK;MAACmE,IAAI,EAAC,GAAG;MAACE,OAAO,eAAEX,OAAA,CAACzD,QAAQ;QAAC0E,EAAE,EAAE/D,MAAM,CAACgE,SAAU;QAACC,OAAO;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjE,CAAC;AAEb,CAAC;AAACZ,EAAA,CApFID,UAAoB;EAAA,QACPnD,cAAc,EACHC,cAAc;AAAA;AAAA2G,EAAA,GAFtCzD,UAAoB;AAsF1B,MAAMxD,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEsD,OAAA,CAACxD,QAAQ;IAACM,KAAK,EAAEA,KAAM;IAAA0D,QAAA,eACrBR,OAAA,CAACvD,cAAc;MACbwD,MAAM,EAAErD,IAAK;MACbgH,KAAK,EAAE;QACLC,KAAK,EAAE;UACLC,YAAY,EAAE3G,YAAY,CAAC4G,YAAY;UACvCC,YAAY,EAAE7G,YAAY,CAAC6G;QAC7B;MACF,CAAE;MAAAxD,QAAA,eAEFR,OAAA,CAACrD,OAAO;QAAA6D,QAAA,eACNR,OAAA,CAAC5D,MAAM;UAAAoE,QAAA,eACLR,OAAA,CAACE,UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEf,CAAC;AAACkD,GAAA,GApBIvH,GAAa;AAsBnB,eAAeA,GAAG;AAAC,IAAAiH,EAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAP,EAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}