{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\service\\\\SparePartsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Modal, Form, Row, Col, Typography, Tag, Alert, Statistic, Tooltip, Badge, InputNumber, message } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, ExportOutlined, ImportOutlined, WarningOutlined, ClockCircleOutlined, ScanOutlined } from '@ant-design/icons';\nimport { formatCurrency } from '../../utils/format';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Search\n} = Input;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\n\n// 备件接口定义\n\n// 库存预警接口\n\nconst SparePartsPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [spareParts, setSpareParts] = useState([]);\n  const [stockAlerts, setStockAlerts] = useState([]);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState();\n  const [statusFilter, setStatusFilter] = useState();\n  const [criticalityFilter, setCriticalityFilter] = useState();\n  const [sparePartModalVisible, setSparePartModalVisible] = useState(false);\n  const [editingSparePart, setEditingSparePart] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockSpareParts = [{\n    id: '1',\n    partNumber: 'SP-001',\n    partName: '伺服电机',\n    category: '电机',\n    specification: '1.5KW 220V',\n    brand: '三菱',\n    supplier: '三菱电机',\n    unitPrice: 2800,\n    currency: 'CNY',\n    stockQuantity: 5,\n    minStockLevel: 3,\n    maxStockLevel: 15,\n    safetyStock: 2,\n    leadTime: 14,\n    location: 'A-01-01',\n    status: 'active',\n    lastPurchaseDate: '2024-01-15',\n    lastUsageDate: '2024-01-10',\n    usageFrequency: 12,\n    criticality: 'high',\n    compatibleDevices: ['设备A', '设备B'],\n    interchangeableParts: ['SP-002'],\n    warrantyPeriod: 24,\n    storageConditions: '常温干燥',\n    notes: '关键备件，需保证库存',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-15'\n  }, {\n    id: '2',\n    partNumber: 'SP-002',\n    partName: '轴承',\n    category: '机械件',\n    specification: '6205-2RS',\n    brand: 'SKF',\n    supplier: 'SKF中国',\n    unitPrice: 85,\n    currency: 'CNY',\n    stockQuantity: 2,\n    minStockLevel: 5,\n    maxStockLevel: 20,\n    safetyStock: 3,\n    leadTime: 7,\n    location: 'B-02-03',\n    status: 'active',\n    lastPurchaseDate: '2024-01-08',\n    lastUsageDate: '2024-01-12',\n    usageFrequency: 24,\n    criticality: 'medium',\n    compatibleDevices: ['设备A', '设备C', '设备D'],\n    interchangeableParts: [],\n    warrantyPeriod: 12,\n    storageConditions: '防潮防尘',\n    notes: '通用备件',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-08'\n  }, {\n    id: '3',\n    partNumber: 'SP-003',\n    partName: '密封圈',\n    category: '密封件',\n    specification: 'O型圈 φ50×3',\n    brand: '通用',\n    supplier: '本地供应商',\n    unitPrice: 12,\n    currency: 'CNY',\n    stockQuantity: 25,\n    minStockLevel: 10,\n    maxStockLevel: 50,\n    safetyStock: 5,\n    leadTime: 3,\n    location: 'C-01-05',\n    status: 'active',\n    lastPurchaseDate: '2024-01-20',\n    lastUsageDate: '2024-01-18',\n    usageFrequency: 36,\n    criticality: 'low',\n    compatibleDevices: ['设备B', '设备C'],\n    interchangeableParts: ['SP-004'],\n    warrantyPeriod: 6,\n    storageConditions: '避光保存',\n    notes: '易损件，使用频繁',\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-20'\n  }];\n  const mockStockAlerts = [{\n    id: '1',\n    partNumber: 'SP-002',\n    partName: '轴承',\n    currentStock: 2,\n    minLevel: 5,\n    alertType: 'low_stock',\n    urgency: 'high',\n    suggestedAction: '立即采购 10 个'\n  }, {\n    id: '2',\n    partNumber: 'SP-001',\n    partName: '伺服电机',\n    currentStock: 5,\n    minLevel: 3,\n    alertType: 'low_stock',\n    urgency: 'medium',\n    suggestedAction: '建议采购 8 个'\n  }];\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSpareParts(mockSpareParts);\n      setStockAlerts(mockStockAlerts);\n    } catch (error) {\n      message.error('加载数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddSparePart = () => {\n    setEditingSparePart(null);\n    form.resetFields();\n    setSparePartModalVisible(true);\n  };\n  const handleEditSparePart = record => {\n    setEditingSparePart(record);\n    form.setFieldsValue(record);\n    setSparePartModalVisible(true);\n  };\n  const handleDeleteSparePart = record => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除备件 \"${record.partName}\" 吗？`,\n      onOk: () => {\n        message.success('删除成功');\n        loadData();\n      }\n    });\n  };\n  const handleSparePartModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('备件数据:', values);\n      message.success(editingSparePart ? '更新成功' : '创建成功');\n      setSparePartModalVisible(false);\n      loadData();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n  const getStockStatus = part => {\n    if (part.stockQuantity <= 0) {\n      return {\n        status: 'error',\n        text: '缺货'\n      };\n    } else if (part.stockQuantity <= part.minStockLevel) {\n      return {\n        status: 'warning',\n        text: '库存不足'\n      };\n    } else if (part.stockQuantity >= part.maxStockLevel) {\n      return {\n        status: 'processing',\n        text: '库存过多'\n      };\n    } else {\n      return {\n        status: 'success',\n        text: '正常'\n      };\n    }\n  };\n  const getCriticalityColor = criticality => {\n    switch (criticality) {\n      case 'high':\n        return 'red';\n      case 'medium':\n        return 'orange';\n      case 'low':\n        return 'green';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'green';\n      case 'inactive':\n        return 'orange';\n      case 'discontinued':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const sparePartColumns = [{\n    title: '备件编号',\n    dataIndex: 'partNumber',\n    key: 'partNumber',\n    width: 120,\n    fixed: 'left'\n  }, {\n    title: '备件名称',\n    dataIndex: 'partName',\n    key: 'partName',\n    width: 150,\n    fixed: 'left'\n  }, {\n    title: '类别',\n    dataIndex: 'category',\n    key: 'category',\n    width: 100\n  }, {\n    title: '规格型号',\n    dataIndex: 'specification',\n    key: 'specification',\n    width: 150\n  }, {\n    title: '品牌',\n    dataIndex: 'brand',\n    key: 'brand',\n    width: 100\n  }, {\n    title: '当前库存',\n    dataIndex: 'stockQuantity',\n    key: 'stockQuantity',\n    width: 100,\n    render: (quantity, record) => {\n      const stockStatus = getStockStatus(record);\n      return /*#__PURE__*/_jsxDEV(Badge, {\n        status: stockStatus.status,\n        text: `${quantity} 个`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '库存状态',\n    key: 'stockStatus',\n    width: 100,\n    render: (_, record) => {\n      const stockStatus = getStockStatus(record);\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: stockStatus.status === 'success' ? 'green' : stockStatus.status === 'warning' ? 'orange' : 'red',\n        children: stockStatus.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '最小库存',\n    dataIndex: 'minStockLevel',\n    key: 'minStockLevel',\n    width: 100,\n    render: level => `${level} 个`\n  }, {\n    title: '单价',\n    dataIndex: 'unitPrice',\n    key: 'unitPrice',\n    width: 100,\n    render: price => formatCurrency(price)\n  }, {\n    title: '重要性',\n    dataIndex: 'criticality',\n    key: 'criticality',\n    width: 100,\n    render: criticality => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getCriticalityColor(criticality),\n      children: criticality === 'high' ? '高' : criticality === 'medium' ? '中' : '低'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: status === 'active' ? '启用' : status === 'inactive' ? '停用' : '停产'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '库位',\n    dataIndex: 'location',\n    key: 'location',\n    width: 100\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEditSparePart(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleDeleteSparePart(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 9\n    }, this)\n  }];\n  const alertColumns = [{\n    title: '备件编号',\n    dataIndex: 'partNumber',\n    key: 'partNumber'\n  }, {\n    title: '备件名称',\n    dataIndex: 'partName',\n    key: 'partName'\n  }, {\n    title: '当前库存',\n    dataIndex: 'currentStock',\n    key: 'currentStock',\n    render: stock => `${stock} 个`\n  }, {\n    title: '最小库存',\n    dataIndex: 'minLevel',\n    key: 'minLevel',\n    render: level => `${level} 个`\n  }, {\n    title: '预警类型',\n    dataIndex: 'alertType',\n    key: 'alertType',\n    render: type => {\n      const typeMap = {\n        low_stock: {\n          text: '库存不足',\n          color: 'orange'\n        },\n        out_of_stock: {\n          text: '缺货',\n          color: 'red'\n        },\n        overstock: {\n          text: '库存过多',\n          color: 'blue'\n        }\n      };\n      const config = typeMap[type];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: config.color,\n        children: config.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '紧急程度',\n    dataIndex: 'urgency',\n    key: 'urgency',\n    render: urgency => {\n      const urgencyMap = {\n        high: {\n          text: '高',\n          color: 'red'\n        },\n        medium: {\n          text: '中',\n          color: 'orange'\n        },\n        low: {\n          text: '低',\n          color: 'green'\n        }\n      };\n      const config = urgencyMap[urgency];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: config.color,\n        children: config.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '建议操作',\n    dataIndex: 'suggestedAction',\n    key: 'suggestedAction'\n  }];\n\n  // 统计数据\n  const totalParts = spareParts.length;\n  const lowStockParts = spareParts.filter(part => part.stockQuantity <= part.minStockLevel).length;\n  const outOfStockParts = spareParts.filter(part => part.stockQuantity <= 0).length;\n  const totalValue = spareParts.reduce((sum, part) => sum + part.stockQuantity * part.unitPrice, 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [stockAlerts.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u5E93\\u5B58\\u9884\\u8B66\",\n      description: `当前有 ${stockAlerts.length} 个备件需要关注，其中 ${stockAlerts.filter(a => a.urgency === 'high').length} 个高优先级预警。`,\n      type: \"warning\",\n      showIcon: true,\n      closable: true,\n      style: {\n        marginBottom: 16\n      },\n      action: /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        type: \"link\",\n        children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5907\\u4EF6\\u603B\\u6570\",\n            value: totalParts,\n            suffix: \"\\u4E2A\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E93\\u5B58\\u4E0D\\u8DB3\",\n            value: lowStockParts,\n            suffix: \"\\u4E2A\",\n            valueStyle: {\n              color: '#faad14'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7F3A\\u8D27\",\n            value: outOfStockParts,\n            suffix: \"\\u4E2A\",\n            valueStyle: {\n              color: '#f5222d'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E93\\u5B58\\u603B\\u4EF7\\u503C\",\n            value: totalValue,\n            precision: 2,\n            formatter: value => formatCurrency(Number(value)),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u5907\\u4EF6\\u7BA1\\u7406\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u7BA1\\u7406\\u8BBE\\u5907\\u5907\\u4EF6\\u5E93\\u5B58\\uFF0C\\u5305\\u62EC\\u5907\\u4EF6\\u4FE1\\u606F\\u3001\\u5E93\\u5B58\\u6C34\\u5E73\\u3001\\u91C7\\u8D2D\\u5EFA\\u8BAE\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              placeholder: \"\\u641C\\u7D22\\u5907\\u4EF6\\u7F16\\u53F7\\u3001\\u540D\\u79F0\",\n              allowClear: true,\n              style: {\n                width: 200\n              },\n              onSearch: setSearchKeyword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u7C7B\\u522B\",\n              allowClear: true,\n              style: {\n                width: 100\n              },\n              value: categoryFilter,\n              onChange: setCategoryFilter,\n              options: [{\n                label: '电机',\n                value: '电机'\n              }, {\n                label: '机械件',\n                value: '机械件'\n              }, {\n                label: '密封件',\n                value: '密封件'\n              }, {\n                label: '电气件',\n                value: '电气件'\n              }, {\n                label: '液压件',\n                value: '液压件'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u72B6\\u6001\",\n              allowClear: true,\n              style: {\n                width: 100\n              },\n              value: statusFilter,\n              onChange: setStatusFilter,\n              options: [{\n                label: '启用',\n                value: 'active'\n              }, {\n                label: '停用',\n                value: 'inactive'\n              }, {\n                label: '停产',\n                value: 'discontinued'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u91CD\\u8981\\u6027\",\n              allowClear: true,\n              style: {\n                width: 100\n              },\n              value: criticalityFilter,\n              onChange: setCriticalityFilter,\n              options: [{\n                label: '高',\n                value: 'high'\n              }, {\n                label: '中',\n                value: 'medium'\n              }, {\n                label: '低',\n                value: 'low'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 29\n              }, this),\n              children: \"\\u626B\\u7801\\u67E5\\u8BE2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ImportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 44\n              }, this),\n              onClick: handleAddSparePart,\n              children: \"\\u65B0\\u589E\\u5907\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: sparePartColumns,\n        dataSource: spareParts,\n        loading: loading,\n        rowKey: \"id\",\n        scroll: {\n          x: 1500\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 7\n    }, this), stockAlerts.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5E93\\u5B58\\u9884\\u8B66\\u8BE6\\u60C5\",\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: alertColumns,\n        dataSource: stockAlerts,\n        rowKey: \"id\",\n        pagination: false,\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 653,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingSparePart ? '编辑备件' : '新增备件',\n      open: sparePartModalVisible,\n      onOk: handleSparePartModalOk,\n      onCancel: () => setSparePartModalVisible(false),\n      width: 800,\n      okText: \"\\u4FDD\\u5B58\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"partNumber\",\n              label: \"\\u5907\\u4EF6\\u7F16\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入备件编号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5907\\u4EF6\\u7F16\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"partName\",\n              label: \"\\u5907\\u4EF6\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入备件名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5907\\u4EF6\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"category\",\n              label: \"\\u7C7B\\u522B\",\n              rules: [{\n                required: true,\n                message: '请选择类别'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u7C7B\\u522B\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u7535\\u673A\",\n                  children: \"\\u7535\\u673A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u673A\\u68B0\\u4EF6\",\n                  children: \"\\u673A\\u68B0\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u5BC6\\u5C01\\u4EF6\",\n                  children: \"\\u5BC6\\u5C01\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u7535\\u6C14\\u4EF6\",\n                  children: \"\\u7535\\u6C14\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\\u6DB2\\u538B\\u4EF6\",\n                  children: \"\\u6DB2\\u538B\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"specification\",\n              label: \"\\u89C4\\u683C\\u578B\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入规格型号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89C4\\u683C\\u578B\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"brand\",\n              label: \"\\u54C1\\u724C\",\n              rules: [{\n                required: true,\n                message: '请输入品牌'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u54C1\\u724C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"supplier\",\n              label: \"\\u4F9B\\u5E94\\u5546\",\n              rules: [{\n                required: true,\n                message: '请输入供应商'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F9B\\u5E94\\u5546\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"unitPrice\",\n              label: \"\\u5355\\u4EF7\",\n              rules: [{\n                required: true,\n                message: '请输入单价'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5355\\u4EF7\",\n                style: {\n                  width: '100%'\n                },\n                min: 0,\n                precision: 2,\n                addonAfter: \"\\u5143\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"stockQuantity\",\n              label: \"\\u5F53\\u524D\\u5E93\\u5B58\",\n              rules: [{\n                required: true,\n                message: '请输入当前库存'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5F53\\u524D\\u5E93\\u5B58\",\n                style: {\n                  width: '100%'\n                },\n                min: 0,\n                addonAfter: \"\\u4E2A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"minStockLevel\",\n              label: \"\\u6700\\u5C0F\\u5E93\\u5B58\",\n              rules: [{\n                required: true,\n                message: '请输入最小库存'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u6700\\u5C0F\\u5E93\\u5B58\",\n                style: {\n                  width: '100%'\n                },\n                min: 0,\n                addonAfter: \"\\u4E2A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 764,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"maxStockLevel\",\n              label: \"\\u6700\\u5927\\u5E93\\u5B58\",\n              rules: [{\n                required: true,\n                message: '请输入最大库存'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u6700\\u5927\\u5E93\\u5B58\",\n                style: {\n                  width: '100%'\n                },\n                min: 0,\n                addonAfter: \"\\u4E2A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"safetyStock\",\n              label: \"\\u5B89\\u5168\\u5E93\\u5B58\",\n              rules: [{\n                required: true,\n                message: '请输入安全库存'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u5B89\\u5168\\u5E93\\u5B58\",\n                style: {\n                  width: '100%'\n                },\n                min: 0,\n                addonAfter: \"\\u4E2A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"leadTime\",\n              label: \"\\u91C7\\u8D2D\\u5468\\u671F\",\n              rules: [{\n                required: true,\n                message: '请输入采购周期'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u91C7\\u8D2D\\u5468\\u671F\",\n                style: {\n                  width: '100%'\n                },\n                min: 0,\n                addonAfter: \"\\u5929\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"location\",\n              label: \"\\u5E93\\u4F4D\",\n              rules: [{\n                required: true,\n                message: '请输入库位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5E93\\u4F4D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"criticality\",\n              label: \"\\u91CD\\u8981\\u6027\",\n              rules: [{\n                required: true,\n                message: '请选择重要性'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u91CD\\u8981\\u6027\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"high\",\n                  children: \"\\u9AD8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"medium\",\n                  children: \"\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"low\",\n                  children: \"\\u4F4E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"active\",\n                  children: \"\\u542F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"inactive\",\n                  children: \"\\u505C\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 850,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"discontinued\",\n                  children: \"\\u505C\\u4EA7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"warrantyPeriod\",\n              label: \"\\u4FDD\\u4FEE\\u671F\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4FDD\\u4FEE\\u671F\",\n                style: {\n                  width: '100%'\n                },\n                min: 0,\n                addonAfter: \"\\u6708\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"storageConditions\",\n              label: \"\\u5B58\\u50A8\\u6761\\u4EF6\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B58\\u50A8\\u6761\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"notes\",\n              label: \"\\u5907\\u6CE8\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 497,\n    columnNumber: 5\n  }, this);\n};\n_s(SparePartsPage, \"zuoNyAl4UWzilpy5VJlHjcEeKaM=\", false, function () {\n  return [Form.useForm];\n});\n_c = SparePartsPage;\nexport default SparePartsPage;\nvar _c;\n$RefreshReg$(_c, \"SparePartsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Modal", "Form", "Row", "Col", "Typography", "Tag", "<PERSON><PERSON>", "Statistic", "<PERSON><PERSON><PERSON>", "Badge", "InputNumber", "message", "PlusOutlined", "EditOutlined", "DeleteOutlined", "ExportOutlined", "ImportOutlined", "WarningOutlined", "ClockCircleOutlined", "ScanOutlined", "formatCurrency", "jsxDEV", "_jsxDEV", "Title", "Text", "Search", "TextArea", "Option", "SparePartsPage", "_s", "loading", "setLoading", "spareParts", "setSpareParts", "stockAlerts", "setStockAlerts", "searchKeyword", "setSearchKeyword", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilter", "setStatus<PERSON>ilter", "criticalityFilter", "setCriticalityFilter", "sparePartModalVisible", "setSparePartModalVisible", "editingSparePart", "setEditingSparePart", "form", "useForm", "mockSpareParts", "id", "partNumber", "partName", "category", "specification", "brand", "supplier", "unitPrice", "currency", "stockQuantity", "minStockLevel", "maxStockLevel", "safetyStock", "leadTime", "location", "status", "lastPurchaseDate", "lastUsageDate", "usageFrequency", "criticality", "compatibleDevices", "interchangeableParts", "warrantyPeriod", "storageConditions", "notes", "createdAt", "updatedAt", "mockStockAlerts", "currentStock", "minLevel", "alertType", "urgency", "suggestedAction", "loadData", "Promise", "resolve", "setTimeout", "error", "handleAddSparePart", "resetFields", "handleEditSparePart", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDeleteSparePart", "confirm", "title", "content", "onOk", "success", "handleSparePartModalOk", "values", "validateFields", "console", "log", "getStockStatus", "part", "text", "getCriticalityColor", "getStatusColor", "sparePartColumns", "dataIndex", "key", "width", "fixed", "render", "quantity", "stockStatus", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "color", "children", "level", "price", "size", "type", "icon", "onClick", "danger", "alertColumns", "stock", "typeMap", "low_stock", "out_of_stock", "overstock", "config", "urgencyMap", "high", "medium", "low", "totalParts", "length", "lowStockParts", "filter", "outOfStockParts", "totalValue", "reduce", "sum", "description", "a", "showIcon", "closable", "style", "marginBottom", "action", "gutter", "xs", "sm", "md", "value", "suffix", "valueStyle", "prefix", "precision", "formatter", "Number", "justify", "align", "margin", "placeholder", "allowClear", "onSearch", "onChange", "options", "label", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "marginTop", "open", "onCancel", "okText", "cancelText", "layout", "<PERSON><PERSON>", "name", "rules", "required", "min", "addonAfter", "rows", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/service/SparePartsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Modal,\n  Form,\n  Row,\n  Col,\n  Typography,\n  Tag,\n  Alert,\n  Statistic,\n  Progress,\n  Tooltip,\n  Badge,\n  Divider,\n  InputNumber,\n  DatePicker,\n  Upload,\n  message,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  ScanOutlined,\n  UploadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { formatCurrency } from '../../utils/format';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\n// 备件接口定义\ninterface SparePart {\n  id: string;\n  partNumber: string;\n  partName: string;\n  category: string;\n  specification: string;\n  brand: string;\n  supplier: string;\n  unitPrice: number;\n  currency: string;\n  stockQuantity: number;\n  minStockLevel: number;\n  maxStockLevel: number;\n  safetyStock: number;\n  leadTime: number; // 采购周期（天）\n  location: string;\n  status: 'active' | 'inactive' | 'discontinued';\n  lastPurchaseDate: string;\n  lastUsageDate: string;\n  usageFrequency: number; // 年使用频率\n  criticality: 'high' | 'medium' | 'low';\n  compatibleDevices: string[];\n  interchangeableParts: string[];\n  warrantyPeriod: number; // 保修期（月）\n  storageConditions: string;\n  notes: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 库存预警接口\ninterface StockAlert {\n  id: string;\n  partNumber: string;\n  partName: string;\n  currentStock: number;\n  minLevel: number;\n  alertType: 'low_stock' | 'out_of_stock' | 'overstock';\n  urgency: 'high' | 'medium' | 'low';\n  suggestedAction: string;\n}\n\nconst SparePartsPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [spareParts, setSpareParts] = useState<SparePart[]>([]);\n  const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([]);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState<string | undefined>();\n  const [statusFilter, setStatusFilter] = useState<string | undefined>();\n  const [criticalityFilter, setCriticalityFilter] = useState<string | undefined>();\n  const [sparePartModalVisible, setSparePartModalVisible] = useState(false);\n  const [editingSparePart, setEditingSparePart] = useState<SparePart | null>(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockSpareParts: SparePart[] = [\n    {\n      id: '1',\n      partNumber: 'SP-001',\n      partName: '伺服电机',\n      category: '电机',\n      specification: '1.5KW 220V',\n      brand: '三菱',\n      supplier: '三菱电机',\n      unitPrice: 2800,\n      currency: 'CNY',\n      stockQuantity: 5,\n      minStockLevel: 3,\n      maxStockLevel: 15,\n      safetyStock: 2,\n      leadTime: 14,\n      location: 'A-01-01',\n      status: 'active',\n      lastPurchaseDate: '2024-01-15',\n      lastUsageDate: '2024-01-10',\n      usageFrequency: 12,\n      criticality: 'high',\n      compatibleDevices: ['设备A', '设备B'],\n      interchangeableParts: ['SP-002'],\n      warrantyPeriod: 24,\n      storageConditions: '常温干燥',\n      notes: '关键备件，需保证库存',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-15',\n    },\n    {\n      id: '2',\n      partNumber: 'SP-002',\n      partName: '轴承',\n      category: '机械件',\n      specification: '6205-2RS',\n      brand: 'SKF',\n      supplier: 'SKF中国',\n      unitPrice: 85,\n      currency: 'CNY',\n      stockQuantity: 2,\n      minStockLevel: 5,\n      maxStockLevel: 20,\n      safetyStock: 3,\n      leadTime: 7,\n      location: 'B-02-03',\n      status: 'active',\n      lastPurchaseDate: '2024-01-08',\n      lastUsageDate: '2024-01-12',\n      usageFrequency: 24,\n      criticality: 'medium',\n      compatibleDevices: ['设备A', '设备C', '设备D'],\n      interchangeableParts: [],\n      warrantyPeriod: 12,\n      storageConditions: '防潮防尘',\n      notes: '通用备件',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-08',\n    },\n    {\n      id: '3',\n      partNumber: 'SP-003',\n      partName: '密封圈',\n      category: '密封件',\n      specification: 'O型圈 φ50×3',\n      brand: '通用',\n      supplier: '本地供应商',\n      unitPrice: 12,\n      currency: 'CNY',\n      stockQuantity: 25,\n      minStockLevel: 10,\n      maxStockLevel: 50,\n      safetyStock: 5,\n      leadTime: 3,\n      location: 'C-01-05',\n      status: 'active',\n      lastPurchaseDate: '2024-01-20',\n      lastUsageDate: '2024-01-18',\n      usageFrequency: 36,\n      criticality: 'low',\n      compatibleDevices: ['设备B', '设备C'],\n      interchangeableParts: ['SP-004'],\n      warrantyPeriod: 6,\n      storageConditions: '避光保存',\n      notes: '易损件，使用频繁',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-20',\n    },\n  ];\n\n  const mockStockAlerts: StockAlert[] = [\n    {\n      id: '1',\n      partNumber: 'SP-002',\n      partName: '轴承',\n      currentStock: 2,\n      minLevel: 5,\n      alertType: 'low_stock',\n      urgency: 'high',\n      suggestedAction: '立即采购 10 个',\n    },\n    {\n      id: '2',\n      partNumber: 'SP-001',\n      partName: '伺服电机',\n      currentStock: 5,\n      minLevel: 3,\n      alertType: 'low_stock',\n      urgency: 'medium',\n      suggestedAction: '建议采购 8 个',\n    },\n  ];\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSpareParts(mockSpareParts);\n      setStockAlerts(mockStockAlerts);\n    } catch (error) {\n      message.error('加载数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddSparePart = () => {\n    setEditingSparePart(null);\n    form.resetFields();\n    setSparePartModalVisible(true);\n  };\n\n  const handleEditSparePart = (record: SparePart) => {\n    setEditingSparePart(record);\n    form.setFieldsValue(record);\n    setSparePartModalVisible(true);\n  };\n\n  const handleDeleteSparePart = (record: SparePart) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除备件 \"${record.partName}\" 吗？`,\n      onOk: () => {\n        message.success('删除成功');\n        loadData();\n      },\n    });\n  };\n\n  const handleSparePartModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('备件数据:', values);\n      message.success(editingSparePart ? '更新成功' : '创建成功');\n      setSparePartModalVisible(false);\n      loadData();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const getStockStatus = (part: SparePart) => {\n    if (part.stockQuantity <= 0) {\n      return { status: 'error', text: '缺货' };\n    } else if (part.stockQuantity <= part.minStockLevel) {\n      return { status: 'warning', text: '库存不足' };\n    } else if (part.stockQuantity >= part.maxStockLevel) {\n      return { status: 'processing', text: '库存过多' };\n    } else {\n      return { status: 'success', text: '正常' };\n    }\n  };\n\n  const getCriticalityColor = (criticality: string) => {\n    switch (criticality) {\n      case 'high': return 'red';\n      case 'medium': return 'orange';\n      case 'low': return 'green';\n      default: return 'default';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': return 'green';\n      case 'inactive': return 'orange';\n      case 'discontinued': return 'red';\n      default: return 'default';\n    }\n  };\n\n  const sparePartColumns: ColumnsType<SparePart> = [\n    {\n      title: '备件编号',\n      dataIndex: 'partNumber',\n      key: 'partNumber',\n      width: 120,\n      fixed: 'left',\n    },\n    {\n      title: '备件名称',\n      dataIndex: 'partName',\n      key: 'partName',\n      width: 150,\n      fixed: 'left',\n    },\n    {\n      title: '类别',\n      dataIndex: 'category',\n      key: 'category',\n      width: 100,\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 150,\n    },\n    {\n      title: '品牌',\n      dataIndex: 'brand',\n      key: 'brand',\n      width: 100,\n    },\n    {\n      title: '当前库存',\n      dataIndex: 'stockQuantity',\n      key: 'stockQuantity',\n      width: 100,\n      render: (quantity: number, record: SparePart) => {\n        const stockStatus = getStockStatus(record);\n        return (\n          <Badge \n            status={stockStatus.status as any} \n            text={`${quantity} 个`}\n          />\n        );\n      },\n    },\n    {\n      title: '库存状态',\n      key: 'stockStatus',\n      width: 100,\n      render: (_, record: SparePart) => {\n        const stockStatus = getStockStatus(record);\n        return (\n          <Tag color={stockStatus.status === 'success' ? 'green' : \n                     stockStatus.status === 'warning' ? 'orange' : 'red'}>\n            {stockStatus.text}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '最小库存',\n      dataIndex: 'minStockLevel',\n      key: 'minStockLevel',\n      width: 100,\n      render: (level: number) => `${level} 个`,\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      width: 100,\n      render: (price: number) => formatCurrency(price),\n    },\n    {\n      title: '重要性',\n      dataIndex: 'criticality',\n      key: 'criticality',\n      width: 100,\n      render: (criticality: string) => (\n        <Tag color={getCriticalityColor(criticality)}>\n          {criticality === 'high' ? '高' : \n           criticality === 'medium' ? '中' : '低'}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {status === 'active' ? '启用' : \n           status === 'inactive' ? '停用' : '停产'}\n        </Tag>\n      ),\n    },\n    {\n      title: '库位',\n      dataIndex: 'location',\n      key: 'location',\n      width: 100,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right',\n      render: (_, record: SparePart) => (\n        <Space size=\"small\">\n          <Tooltip title=\"编辑\">\n            <Button \n              type=\"text\" \n              icon={<EditOutlined />} \n              onClick={() => handleEditSparePart(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button \n              type=\"text\" \n              danger \n              icon={<DeleteOutlined />} \n              onClick={() => handleDeleteSparePart(record)}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  const alertColumns: ColumnsType<StockAlert> = [\n    {\n      title: '备件编号',\n      dataIndex: 'partNumber',\n      key: 'partNumber',\n    },\n    {\n      title: '备件名称',\n      dataIndex: 'partName',\n      key: 'partName',\n    },\n    {\n      title: '当前库存',\n      dataIndex: 'currentStock',\n      key: 'currentStock',\n      render: (stock: number) => `${stock} 个`,\n    },\n    {\n      title: '最小库存',\n      dataIndex: 'minLevel',\n      key: 'minLevel',\n      render: (level: number) => `${level} 个`,\n    },\n    {\n      title: '预警类型',\n      dataIndex: 'alertType',\n      key: 'alertType',\n      render: (type: string) => {\n        const typeMap = {\n          low_stock: { text: '库存不足', color: 'orange' },\n          out_of_stock: { text: '缺货', color: 'red' },\n          overstock: { text: '库存过多', color: 'blue' },\n        };\n        const config = typeMap[type as keyof typeof typeMap];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n    {\n      title: '紧急程度',\n      dataIndex: 'urgency',\n      key: 'urgency',\n      render: (urgency: string) => {\n        const urgencyMap = {\n          high: { text: '高', color: 'red' },\n          medium: { text: '中', color: 'orange' },\n          low: { text: '低', color: 'green' },\n        };\n        const config = urgencyMap[urgency as keyof typeof urgencyMap];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n    {\n      title: '建议操作',\n      dataIndex: 'suggestedAction',\n      key: 'suggestedAction',\n    },\n  ];\n\n  // 统计数据\n  const totalParts = spareParts.length;\n  const lowStockParts = spareParts.filter(part => part.stockQuantity <= part.minStockLevel).length;\n  const outOfStockParts = spareParts.filter(part => part.stockQuantity <= 0).length;\n  const totalValue = spareParts.reduce((sum, part) => sum + (part.stockQuantity * part.unitPrice), 0);\n\n  return (\n    <div>\n      {/* 库存预警 */}\n      {stockAlerts.length > 0 && (\n        <Alert\n          message=\"库存预警\"\n          description={`当前有 ${stockAlerts.length} 个备件需要关注，其中 ${stockAlerts.filter(a => a.urgency === 'high').length} 个高优先级预警。`}\n          type=\"warning\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n          action={\n            <Button size=\"small\" type=\"link\">\n              查看详情\n            </Button>\n          }\n        />\n      )}\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"备件总数\"\n              value={totalParts}\n              suffix=\"个\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"库存不足\"\n              value={lowStockParts}\n              suffix=\"个\"\n              valueStyle={{ color: '#faad14' }}\n              prefix={<WarningOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"缺货\"\n              value={outOfStockParts}\n              suffix=\"个\"\n              valueStyle={{ color: '#f5222d' }}\n              prefix={<ClockCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"库存总价值\"\n              value={totalValue}\n              precision={2}\n              formatter={(value) => formatCurrency(Number(value))}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 主要内容 */}\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              备件管理\n            </Title>\n            <Text type=\"secondary\">\n              管理设备备件库存，包括备件信息、库存水平、采购建议等\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索备件编号、名称\"\n                allowClear\n                style={{ width: 200 }}\n                onSearch={setSearchKeyword}\n              />\n              <Select\n                placeholder=\"类别\"\n                allowClear\n                style={{ width: 100 }}\n                value={categoryFilter}\n                onChange={setCategoryFilter}\n                options={[\n                  { label: '电机', value: '电机' },\n                  { label: '机械件', value: '机械件' },\n                  { label: '密封件', value: '密封件' },\n                  { label: '电气件', value: '电气件' },\n                  { label: '液压件', value: '液压件' },\n                ]}\n              />\n              <Select\n                placeholder=\"状态\"\n                allowClear\n                style={{ width: 100 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '启用', value: 'active' },\n                  { label: '停用', value: 'inactive' },\n                  { label: '停产', value: 'discontinued' },\n                ]}\n              />\n              <Select\n                placeholder=\"重要性\"\n                allowClear\n                style={{ width: 100 }}\n                value={criticalityFilter}\n                onChange={setCriticalityFilter}\n                options={[\n                  { label: '高', value: 'high' },\n                  { label: '中', value: 'medium' },\n                  { label: '低', value: 'low' },\n                ]}\n              />\n              <Button icon={<ScanOutlined />}>\n                扫码查询\n              </Button>\n              <Button icon={<ExportOutlined />}>\n                导出\n              </Button>\n              <Button icon={<ImportOutlined />}>\n                导入\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddSparePart}>\n                新增备件\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Table\n          columns={sparePartColumns}\n          dataSource={spareParts}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1500 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 库存预警详情 */}\n      {stockAlerts.length > 0 && (\n        <Card title=\"库存预警详情\" style={{ marginTop: 16 }}>\n          <Table\n            columns={alertColumns}\n            dataSource={stockAlerts}\n            rowKey=\"id\"\n            pagination={false}\n            size=\"small\"\n          />\n        </Card>\n      )}\n\n      {/* 备件编辑模态框 */}\n      <Modal\n        title={editingSparePart ? '编辑备件' : '新增备件'}\n        open={sparePartModalVisible}\n        onOk={handleSparePartModalOk}\n        onCancel={() => setSparePartModalVisible(false)}\n        width={800}\n        okText=\"保存\"\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"partNumber\"\n                label=\"备件编号\"\n                rules={[{ required: true, message: '请输入备件编号' }]}\n              >\n                <Input placeholder=\"请输入备件编号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"partName\"\n                label=\"备件名称\"\n                rules={[{ required: true, message: '请输入备件名称' }]}\n              >\n                <Input placeholder=\"请输入备件名称\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"category\"\n                label=\"类别\"\n                rules={[{ required: true, message: '请选择类别' }]}\n              >\n                <Select placeholder=\"请选择类别\">\n                  <Option value=\"电机\">电机</Option>\n                  <Option value=\"机械件\">机械件</Option>\n                  <Option value=\"密封件\">密封件</Option>\n                  <Option value=\"电气件\">电气件</Option>\n                  <Option value=\"液压件\">液压件</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"specification\"\n                label=\"规格型号\"\n                rules={[{ required: true, message: '请输入规格型号' }]}\n              >\n                <Input placeholder=\"请输入规格型号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"brand\"\n                label=\"品牌\"\n                rules={[{ required: true, message: '请输入品牌' }]}\n              >\n                <Input placeholder=\"请输入品牌\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"supplier\"\n                label=\"供应商\"\n                rules={[{ required: true, message: '请输入供应商' }]}\n              >\n                <Input placeholder=\"请输入供应商\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"unitPrice\"\n                label=\"单价\"\n                rules={[{ required: true, message: '请输入单价' }]}\n              >\n                <InputNumber\n                  placeholder=\"请输入单价\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  precision={2}\n                  addonAfter=\"元\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"stockQuantity\"\n                label=\"当前库存\"\n                rules={[{ required: true, message: '请输入当前库存' }]}\n              >\n                <InputNumber\n                  placeholder=\"请输入当前库存\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"个\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"minStockLevel\"\n                label=\"最小库存\"\n                rules={[{ required: true, message: '请输入最小库存' }]}\n              >\n                <InputNumber\n                  placeholder=\"最小库存\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"个\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"maxStockLevel\"\n                label=\"最大库存\"\n                rules={[{ required: true, message: '请输入最大库存' }]}\n              >\n                <InputNumber\n                  placeholder=\"最大库存\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"个\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"safetyStock\"\n                label=\"安全库存\"\n                rules={[{ required: true, message: '请输入安全库存' }]}\n              >\n                <InputNumber\n                  placeholder=\"安全库存\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"个\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"leadTime\"\n                label=\"采购周期\"\n                rules={[{ required: true, message: '请输入采购周期' }]}\n              >\n                <InputNumber\n                  placeholder=\"请输入采购周期\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"天\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"location\"\n                label=\"库位\"\n                rules={[{ required: true, message: '请输入库位' }]}\n              >\n                <Input placeholder=\"请输入库位\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"criticality\"\n                label=\"重要性\"\n                rules={[{ required: true, message: '请选择重要性' }]}\n              >\n                <Select placeholder=\"请选择重要性\">\n                  <Option value=\"high\">高</Option>\n                  <Option value=\"medium\">中</Option>\n                  <Option value=\"low\">低</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择状态' }]}\n              >\n                <Select placeholder=\"请选择状态\">\n                  <Option value=\"active\">启用</Option>\n                  <Option value=\"inactive\">停用</Option>\n                  <Option value=\"discontinued\">停产</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"warrantyPeriod\"\n                label=\"保修期\"\n              >\n                <InputNumber\n                  placeholder=\"请输入保修期\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"月\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"storageConditions\"\n                label=\"存储条件\"\n              >\n                <Input placeholder=\"请输入存储条件\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24}>\n              <Form.Item\n                name=\"notes\"\n                label=\"备注\"\n              >\n                <TextArea rows={3} placeholder=\"请输入备注信息\" />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default SparePartsPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,SAAS,EAETC,OAAO,EACPC,KAAK,EAELC,WAAW,EAGXC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EAEdC,cAAc,EACdC,cAAc,EACdC,eAAe,EAEfC,mBAAmB,EACnBC,YAAY,QAEP,mBAAmB;AAE1B,SAASC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAO,CAAC,GAAG3B,KAAK;AACxB,MAAM;EAAE4B;AAAS,CAAC,GAAG5B,KAAK;AAC1B,MAAM;EAAE6B;AAAO,CAAC,GAAG5B,MAAM;;AAEzB;;AA+BA;;AAYA,MAAM6B,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAqB,CAAC;EAC1E,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAqB,CAAC;EACtE,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAqB,CAAC;EAChF,MAAM,CAACoD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAmB,IAAI,CAAC;EAChF,MAAM,CAACwD,IAAI,CAAC,GAAG/C,IAAI,CAACgD,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,cAA2B,GAAG,CAClC;IACEC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,YAAY;IAC3BC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,QAAQ;IAChBC,gBAAgB,EAAE,YAAY;IAC9BC,aAAa,EAAE,YAAY;IAC3BC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,MAAM;IACnBC,iBAAiB,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IACjCC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;IAChCC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,MAAM;IACzBC,KAAK,EAAE,YAAY;IACnBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE1B,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,UAAU;IACzBC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,QAAQ;IAChBC,gBAAgB,EAAE,YAAY;IAC9BC,aAAa,EAAE,YAAY;IAC3BC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,QAAQ;IACrBC,iBAAiB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACxCC,oBAAoB,EAAE,EAAE;IACxBC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,MAAM;IACzBC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE1B,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,WAAW;IAC1BC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,QAAQ;IAChBC,gBAAgB,EAAE,YAAY;IAC9BC,aAAa,EAAE,YAAY;IAC3BC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,KAAK;IAClBC,iBAAiB,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IACjCC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;IAChCC,cAAc,EAAE,CAAC;IACjBC,iBAAiB,EAAE,MAAM;IACzBC,KAAK,EAAE,UAAU;IACjBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EAED,MAAMC,eAA6B,GAAG,CACpC;IACE3B,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,IAAI;IACd0B,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,MAAM;IACfC,eAAe,EAAE;EACnB,CAAC,EACD;IACEhC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChB0B,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,QAAQ;IACjBC,eAAe,EAAE;EACnB,CAAC,CACF;EAED1F,SAAS,CAAC,MAAM;IACd2F,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BrD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,IAAIsD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvDrD,aAAa,CAACiB,cAAc,CAAC;MAC7Bf,cAAc,CAAC2C,eAAe,CAAC;IACjC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACd7E,OAAO,CAAC6E,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0D,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1C,mBAAmB,CAAC,IAAI,CAAC;IACzBC,IAAI,CAAC0C,WAAW,CAAC,CAAC;IAClB7C,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAM8C,mBAAmB,GAAIC,MAAiB,IAAK;IACjD7C,mBAAmB,CAAC6C,MAAM,CAAC;IAC3B5C,IAAI,CAAC6C,cAAc,CAACD,MAAM,CAAC;IAC3B/C,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMiD,qBAAqB,GAAIF,MAAiB,IAAK;IACnD5F,KAAK,CAAC+F,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,YAAYL,MAAM,CAACvC,QAAQ,MAAM;MAC1C6C,IAAI,EAAEA,CAAA,KAAM;QACVvF,OAAO,CAACwF,OAAO,CAAC,MAAM,CAAC;QACvBf,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgB,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMrD,IAAI,CAACsD,cAAc,CAAC,CAAC;MAC1CC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEH,MAAM,CAAC;MAC5B1F,OAAO,CAACwF,OAAO,CAACrD,gBAAgB,GAAG,MAAM,GAAG,MAAM,CAAC;MACnDD,wBAAwB,CAAC,KAAK,CAAC;MAC/BuC,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAMiB,cAAc,GAAIC,IAAe,IAAK;IAC1C,IAAIA,IAAI,CAAC9C,aAAa,IAAI,CAAC,EAAE;MAC3B,OAAO;QAAEM,MAAM,EAAE,OAAO;QAAEyC,IAAI,EAAE;MAAK,CAAC;IACxC,CAAC,MAAM,IAAID,IAAI,CAAC9C,aAAa,IAAI8C,IAAI,CAAC7C,aAAa,EAAE;MACnD,OAAO;QAAEK,MAAM,EAAE,SAAS;QAAEyC,IAAI,EAAE;MAAO,CAAC;IAC5C,CAAC,MAAM,IAAID,IAAI,CAAC9C,aAAa,IAAI8C,IAAI,CAAC5C,aAAa,EAAE;MACnD,OAAO;QAAEI,MAAM,EAAE,YAAY;QAAEyC,IAAI,EAAE;MAAO,CAAC;IAC/C,CAAC,MAAM;MACL,OAAO;QAAEzC,MAAM,EAAE,SAAS;QAAEyC,IAAI,EAAE;MAAK,CAAC;IAC1C;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAItC,WAAmB,IAAK;IACnD,QAAQA,WAAW;MACjB,KAAK,MAAM;QAAE,OAAO,KAAK;MACzB,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,KAAK;QAAE,OAAO,OAAO;MAC1B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMuC,cAAc,GAAI3C,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,OAAO;MAC7B,KAAK,UAAU;QAAE,OAAO,QAAQ;MAChC,KAAK,cAAc;QAAE,OAAO,KAAK;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM4C,gBAAwC,GAAG,CAC/C;IACEd,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACElB,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACElB,KAAK,EAAE,IAAI;IACXe,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXe,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACC,QAAgB,EAAExB,MAAiB,KAAK;MAC/C,MAAMyB,WAAW,GAAGZ,cAAc,CAACb,MAAM,CAAC;MAC1C,oBACEtE,OAAA,CAACb,KAAK;QACJyD,MAAM,EAAEmD,WAAW,CAACnD,MAAc;QAClCyC,IAAI,EAAE,GAAGS,QAAQ;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAEN;EACF,CAAC,EACD;IACEzB,KAAK,EAAE,MAAM;IACbgB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACO,CAAC,EAAE9B,MAAiB,KAAK;MAChC,MAAMyB,WAAW,GAAGZ,cAAc,CAACb,MAAM,CAAC;MAC1C,oBACEtE,OAAA,CAACjB,GAAG;QAACsH,KAAK,EAAEN,WAAW,CAACnD,MAAM,KAAK,SAAS,GAAG,OAAO,GAC3CmD,WAAW,CAACnD,MAAM,KAAK,SAAS,GAAG,QAAQ,GAAG,KAAM;QAAA0D,QAAA,EAC5DP,WAAW,CAACV;MAAI;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAEV;EACF,CAAC,EACD;IACEzB,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGU,KAAa,IAAK,GAAGA,KAAK;EACrC,CAAC,EACD;IACE7B,KAAK,EAAE,IAAI;IACXe,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGW,KAAa,IAAK1G,cAAc,CAAC0G,KAAK;EACjD,CAAC,EACD;IACE9B,KAAK,EAAE,KAAK;IACZe,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAG7C,WAAmB,iBAC1BhD,OAAA,CAACjB,GAAG;MAACsH,KAAK,EAAEf,mBAAmB,CAACtC,WAAW,CAAE;MAAAsD,QAAA,EAC1CtD,WAAW,KAAK,MAAM,GAAG,GAAG,GAC5BA,WAAW,KAAK,QAAQ,GAAG,GAAG,GAAG;IAAG;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC;EAET,CAAC,EACD;IACEzB,KAAK,EAAE,IAAI;IACXe,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGjD,MAAc,iBACrB5C,OAAA,CAACjB,GAAG;MAACsH,KAAK,EAAEd,cAAc,CAAC3C,MAAM,CAAE;MAAA0D,QAAA,EAChC1D,MAAM,KAAK,QAAQ,GAAG,IAAI,GAC1BA,MAAM,KAAK,UAAU,GAAG,IAAI,GAAG;IAAI;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC;EAET,CAAC,EACD;IACEzB,KAAK,EAAE,IAAI;IACXe,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXgB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAEA,CAACO,CAAC,EAAE9B,MAAiB,kBAC3BtE,OAAA,CAACzB,KAAK;MAACkI,IAAI,EAAC,OAAO;MAAAH,QAAA,gBACjBtG,OAAA,CAACd,OAAO;QAACwF,KAAK,EAAC,cAAI;QAAA4B,QAAA,eACjBtG,OAAA,CAAC1B,MAAM;UACLoI,IAAI,EAAC,MAAM;UACXC,IAAI,eAAE3G,OAAA,CAACT,YAAY;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBS,OAAO,EAAEA,CAAA,KAAMvC,mBAAmB,CAACC,MAAM;QAAE;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVnG,OAAA,CAACd,OAAO;QAACwF,KAAK,EAAC,cAAI;QAAA4B,QAAA,eACjBtG,OAAA,CAAC1B,MAAM;UACLoI,IAAI,EAAC,MAAM;UACXG,MAAM;UACNF,IAAI,eAAE3G,OAAA,CAACR,cAAc;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBS,OAAO,EAAEA,CAAA,KAAMpC,qBAAqB,CAACF,MAAM;QAAE;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;EAED,MAAMW,YAAqC,GAAG,CAC5C;IACEpC,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE;EACP,CAAC,EACD;IACEhB,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEhB,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBG,MAAM,EAAGkB,KAAa,IAAK,GAAGA,KAAK;EACrC,CAAC,EACD;IACErC,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfG,MAAM,EAAGU,KAAa,IAAK,GAAGA,KAAK;EACrC,CAAC,EACD;IACE7B,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBG,MAAM,EAAGa,IAAY,IAAK;MACxB,MAAMM,OAAO,GAAG;QACdC,SAAS,EAAE;UAAE5B,IAAI,EAAE,MAAM;UAAEgB,KAAK,EAAE;QAAS,CAAC;QAC5Ca,YAAY,EAAE;UAAE7B,IAAI,EAAE,IAAI;UAAEgB,KAAK,EAAE;QAAM,CAAC;QAC1Cc,SAAS,EAAE;UAAE9B,IAAI,EAAE,MAAM;UAAEgB,KAAK,EAAE;QAAO;MAC3C,CAAC;MACD,MAAMe,MAAM,GAAGJ,OAAO,CAACN,IAAI,CAAyB;MACpD,oBAAO1G,OAAA,CAACjB,GAAG;QAACsH,KAAK,EAAEe,MAAM,CAACf,KAAM;QAAAC,QAAA,EAAEc,MAAM,CAAC/B;MAAI;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACtD;EACF,CAAC,EACD;IACEzB,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdG,MAAM,EAAGjC,OAAe,IAAK;MAC3B,MAAMyD,UAAU,GAAG;QACjBC,IAAI,EAAE;UAAEjC,IAAI,EAAE,GAAG;UAAEgB,KAAK,EAAE;QAAM,CAAC;QACjCkB,MAAM,EAAE;UAAElC,IAAI,EAAE,GAAG;UAAEgB,KAAK,EAAE;QAAS,CAAC;QACtCmB,GAAG,EAAE;UAAEnC,IAAI,EAAE,GAAG;UAAEgB,KAAK,EAAE;QAAQ;MACnC,CAAC;MACD,MAAMe,MAAM,GAAGC,UAAU,CAACzD,OAAO,CAA4B;MAC7D,oBAAO5D,OAAA,CAACjB,GAAG;QAACsH,KAAK,EAAEe,MAAM,CAACf,KAAM;QAAAC,QAAA,EAAEc,MAAM,CAAC/B;MAAI;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACtD;EACF,CAAC,EACD;IACEzB,KAAK,EAAE,MAAM;IACbe,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACA,MAAM+B,UAAU,GAAG/G,UAAU,CAACgH,MAAM;EACpC,MAAMC,aAAa,GAAGjH,UAAU,CAACkH,MAAM,CAACxC,IAAI,IAAIA,IAAI,CAAC9C,aAAa,IAAI8C,IAAI,CAAC7C,aAAa,CAAC,CAACmF,MAAM;EAChG,MAAMG,eAAe,GAAGnH,UAAU,CAACkH,MAAM,CAACxC,IAAI,IAAIA,IAAI,CAAC9C,aAAa,IAAI,CAAC,CAAC,CAACoF,MAAM;EACjF,MAAMI,UAAU,GAAGpH,UAAU,CAACqH,MAAM,CAAC,CAACC,GAAG,EAAE5C,IAAI,KAAK4C,GAAG,GAAI5C,IAAI,CAAC9C,aAAa,GAAG8C,IAAI,CAAChD,SAAU,EAAE,CAAC,CAAC;EAEnG,oBACEpC,OAAA;IAAAsG,QAAA,GAEG1F,WAAW,CAAC8G,MAAM,GAAG,CAAC,iBACrB1H,OAAA,CAAChB,KAAK;MACJK,OAAO,EAAC,0BAAM;MACd4I,WAAW,EAAE,OAAOrH,WAAW,CAAC8G,MAAM,eAAe9G,WAAW,CAACgH,MAAM,CAACM,CAAC,IAAIA,CAAC,CAACtE,OAAO,KAAK,MAAM,CAAC,CAAC8D,MAAM,WAAY;MACrHhB,IAAI,EAAC,SAAS;MACdyB,QAAQ;MACRC,QAAQ;MACRC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAC5BC,MAAM,eACJvI,OAAA,CAAC1B,MAAM;QAACmI,IAAI,EAAC,OAAO;QAACC,IAAI,EAAC,MAAM;QAAAJ,QAAA,EAAC;MAEjC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACF,eAGDnG,OAAA,CAACpB,GAAG;MAAC4J,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACH,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAhC,QAAA,gBACjDtG,OAAA,CAACnB,GAAG;QAAC4J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArC,QAAA,eACzBtG,OAAA,CAAC5B,IAAI;UAAAkI,QAAA,eACHtG,OAAA,CAACf,SAAS;YACRyF,KAAK,EAAC,0BAAM;YACZkE,KAAK,EAAEnB,UAAW;YAClBoB,MAAM,EAAC,QAAG;YACVC,UAAU,EAAE;cAAEzC,KAAK,EAAE;YAAU;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnG,OAAA,CAACnB,GAAG;QAAC4J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArC,QAAA,eACzBtG,OAAA,CAAC5B,IAAI;UAAAkI,QAAA,eACHtG,OAAA,CAACf,SAAS;YACRyF,KAAK,EAAC,0BAAM;YACZkE,KAAK,EAAEjB,aAAc;YACrBkB,MAAM,EAAC,QAAG;YACVC,UAAU,EAAE;cAAEzC,KAAK,EAAE;YAAU,CAAE;YACjC0C,MAAM,eAAE/I,OAAA,CAACL,eAAe;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnG,OAAA,CAACnB,GAAG;QAAC4J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArC,QAAA,eACzBtG,OAAA,CAAC5B,IAAI;UAAAkI,QAAA,eACHtG,OAAA,CAACf,SAAS;YACRyF,KAAK,EAAC,cAAI;YACVkE,KAAK,EAAEf,eAAgB;YACvBgB,MAAM,EAAC,QAAG;YACVC,UAAU,EAAE;cAAEzC,KAAK,EAAE;YAAU,CAAE;YACjC0C,MAAM,eAAE/I,OAAA,CAACJ,mBAAmB;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnG,OAAA,CAACnB,GAAG;QAAC4J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArC,QAAA,eACzBtG,OAAA,CAAC5B,IAAI;UAAAkI,QAAA,eACHtG,OAAA,CAACf,SAAS;YACRyF,KAAK,EAAC,gCAAO;YACbkE,KAAK,EAAEd,UAAW;YAClBkB,SAAS,EAAE,CAAE;YACbC,SAAS,EAAGL,KAAK,IAAK9I,cAAc,CAACoJ,MAAM,CAACN,KAAK,CAAC,CAAE;YACpDE,UAAU,EAAE;cAAEzC,KAAK,EAAE;YAAU;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnG,OAAA,CAAC5B,IAAI;MAAAkI,QAAA,gBACHtG,OAAA,CAACpB,GAAG;QAACuK,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACf,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAhC,QAAA,gBACtEtG,OAAA,CAACnB,GAAG;UAAAyH,QAAA,gBACFtG,OAAA,CAACC,KAAK;YAACsG,KAAK,EAAE,CAAE;YAAC8B,KAAK,EAAE;cAAEgB,MAAM,EAAE;YAAE,CAAE;YAAA/C,QAAA,EAAC;UAEvC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnG,OAAA,CAACE,IAAI;YAACwG,IAAI,EAAC,WAAW;YAAAJ,QAAA,EAAC;UAEvB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNnG,OAAA,CAACnB,GAAG;UAAAyH,QAAA,eACFtG,OAAA,CAACzB,KAAK;YAAA+H,QAAA,gBACJtG,OAAA,CAACG,MAAM;cACLmJ,WAAW,EAAC,wDAAW;cACvBC,UAAU;cACVlB,KAAK,EAAE;gBAAE1C,KAAK,EAAE;cAAI,CAAE;cACtB6D,QAAQ,EAAEzI;YAAiB;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFnG,OAAA,CAACvB,MAAM;cACL6K,WAAW,EAAC,cAAI;cAChBC,UAAU;cACVlB,KAAK,EAAE;gBAAE1C,KAAK,EAAE;cAAI,CAAE;cACtBiD,KAAK,EAAE5H,cAAe;cACtByI,QAAQ,EAAExI,iBAAkB;cAC5ByI,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,KAAK,EAAE;cAAK,CAAC,EAC5B;gBAAEe,KAAK,EAAE,KAAK;gBAAEf,KAAK,EAAE;cAAM,CAAC,EAC9B;gBAAEe,KAAK,EAAE,KAAK;gBAAEf,KAAK,EAAE;cAAM,CAAC,EAC9B;gBAAEe,KAAK,EAAE,KAAK;gBAAEf,KAAK,EAAE;cAAM,CAAC,EAC9B;gBAAEe,KAAK,EAAE,KAAK;gBAAEf,KAAK,EAAE;cAAM,CAAC;YAC9B;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnG,OAAA,CAACvB,MAAM;cACL6K,WAAW,EAAC,cAAI;cAChBC,UAAU;cACVlB,KAAK,EAAE;gBAAE1C,KAAK,EAAE;cAAI,CAAE;cACtBiD,KAAK,EAAE1H,YAAa;cACpBuI,QAAQ,EAAEtI,eAAgB;cAC1BuI,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,IAAI;gBAAEf,KAAK,EAAE;cAAS,CAAC,EAChC;gBAAEe,KAAK,EAAE,IAAI;gBAAEf,KAAK,EAAE;cAAW,CAAC,EAClC;gBAAEe,KAAK,EAAE,IAAI;gBAAEf,KAAK,EAAE;cAAe,CAAC;YACtC;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnG,OAAA,CAACvB,MAAM;cACL6K,WAAW,EAAC,oBAAK;cACjBC,UAAU;cACVlB,KAAK,EAAE;gBAAE1C,KAAK,EAAE;cAAI,CAAE;cACtBiD,KAAK,EAAExH,iBAAkB;cACzBqI,QAAQ,EAAEpI,oBAAqB;cAC/BqI,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,GAAG;gBAAEf,KAAK,EAAE;cAAO,CAAC,EAC7B;gBAAEe,KAAK,EAAE,GAAG;gBAAEf,KAAK,EAAE;cAAS,CAAC,EAC/B;gBAAEe,KAAK,EAAE,GAAG;gBAAEf,KAAK,EAAE;cAAM,CAAC;YAC5B;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFnG,OAAA,CAAC1B,MAAM;cAACqI,IAAI,eAAE3G,OAAA,CAACH,YAAY;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAG,QAAA,EAAC;YAEhC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA,CAAC1B,MAAM;cAACqI,IAAI,eAAE3G,OAAA,CAACP,cAAc;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAG,QAAA,EAAC;YAElC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA,CAAC1B,MAAM;cAACqI,IAAI,eAAE3G,OAAA,CAACN,cAAc;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAG,QAAA,EAAC;YAElC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA,CAAC1B,MAAM;cAACoI,IAAI,EAAC,SAAS;cAACC,IAAI,eAAE3G,OAAA,CAACV,YAAY;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACS,OAAO,EAAEzC,kBAAmB;cAAAmC,QAAA,EAAC;YAE5E;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnG,OAAA,CAAC3B,KAAK;QACJuL,OAAO,EAAEpE,gBAAiB;QAC1BqE,UAAU,EAAEnJ,UAAW;QACvBF,OAAO,EAAEA,OAAQ;QACjBsJ,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAArE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGNvF,WAAW,CAAC8G,MAAM,GAAG,CAAC,iBACrB1H,OAAA,CAAC5B,IAAI;MAACsG,KAAK,EAAC,sCAAQ;MAAC2D,KAAK,EAAE;QAAEkC,SAAS,EAAE;MAAG,CAAE;MAAAjE,QAAA,eAC5CtG,OAAA,CAAC3B,KAAK;QACJuL,OAAO,EAAE9C,YAAa;QACtB+C,UAAU,EAAEjJ,WAAY;QACxBkJ,MAAM,EAAC,IAAI;QACXG,UAAU,EAAE,KAAM;QAClBxD,IAAI,EAAC;MAAO;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDnG,OAAA,CAACtB,KAAK;MACJgG,KAAK,EAAElD,gBAAgB,GAAG,MAAM,GAAG,MAAO;MAC1CgJ,IAAI,EAAElJ,qBAAsB;MAC5BsD,IAAI,EAAEE,sBAAuB;MAC7B2F,QAAQ,EAAEA,CAAA,KAAMlJ,wBAAwB,CAAC,KAAK,CAAE;MAChDoE,KAAK,EAAE,GAAI;MACX+E,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAArE,QAAA,eAEftG,OAAA,CAACrB,IAAI;QAAC+C,IAAI,EAAEA,IAAK;QAACkJ,MAAM,EAAC,UAAU;QAAAtE,QAAA,eACjCtG,OAAA,CAACpB,GAAG;UAAC4J,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAlC,QAAA,gBACpBtG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBnB,KAAK,EAAC,0BAAM;cACZoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhDtG,OAAA,CAACxB,KAAK;gBAAC8K,WAAW,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,UAAU;cACfnB,KAAK,EAAC,0BAAM;cACZoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhDtG,OAAA,CAACxB,KAAK;gBAAC8K,WAAW,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,UAAU;cACfnB,KAAK,EAAC,cAAI;cACVoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiH,QAAA,eAE9CtG,OAAA,CAACvB,MAAM;gBAAC6K,WAAW,EAAC,gCAAO;gBAAAhD,QAAA,gBACzBtG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,cAAI;kBAAAtC,QAAA,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BnG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,oBAAK;kBAAAtC,QAAA,EAAC;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCnG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,oBAAK;kBAAAtC,QAAA,EAAC;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCnG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,oBAAK;kBAAAtC,QAAA,EAAC;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCnG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,oBAAK;kBAAAtC,QAAA,EAAC;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,eAAe;cACpBnB,KAAK,EAAC,0BAAM;cACZoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhDtG,OAAA,CAACxB,KAAK;gBAAC8K,WAAW,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,OAAO;cACZnB,KAAK,EAAC,cAAI;cACVoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiH,QAAA,eAE9CtG,OAAA,CAACxB,KAAK;gBAAC8K,WAAW,EAAC;cAAO;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,UAAU;cACfnB,KAAK,EAAC,oBAAK;cACXoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAiH,QAAA,eAE/CtG,OAAA,CAACxB,KAAK;gBAAC8K,WAAW,EAAC;cAAQ;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBnB,KAAK,EAAC,cAAI;cACVoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiH,QAAA,eAE9CtG,OAAA,CAACZ,WAAW;gBACVkK,WAAW,EAAC,gCAAO;gBACnBjB,KAAK,EAAE;kBAAE1C,KAAK,EAAE;gBAAO,CAAE;gBACzBsF,GAAG,EAAE,CAAE;gBACPjC,SAAS,EAAE,CAAE;gBACbkC,UAAU,EAAC;cAAG;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,eAAe;cACpBnB,KAAK,EAAC,0BAAM;cACZoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhDtG,OAAA,CAACZ,WAAW;gBACVkK,WAAW,EAAC,4CAAS;gBACrBjB,KAAK,EAAE;kBAAE1C,KAAK,EAAE;gBAAO,CAAE;gBACzBsF,GAAG,EAAE,CAAE;gBACPC,UAAU,EAAC;cAAG;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArC,QAAA,eACjBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,eAAe;cACpBnB,KAAK,EAAC,0BAAM;cACZoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhDtG,OAAA,CAACZ,WAAW;gBACVkK,WAAW,EAAC,0BAAM;gBAClBjB,KAAK,EAAE;kBAAE1C,KAAK,EAAE;gBAAO,CAAE;gBACzBsF,GAAG,EAAE,CAAE;gBACPC,UAAU,EAAC;cAAG;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArC,QAAA,eACjBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,eAAe;cACpBnB,KAAK,EAAC,0BAAM;cACZoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhDtG,OAAA,CAACZ,WAAW;gBACVkK,WAAW,EAAC,0BAAM;gBAClBjB,KAAK,EAAE;kBAAE1C,KAAK,EAAE;gBAAO,CAAE;gBACzBsF,GAAG,EAAE,CAAE;gBACPC,UAAU,EAAC;cAAG;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArC,QAAA,eACjBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,aAAa;cAClBnB,KAAK,EAAC,0BAAM;cACZoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhDtG,OAAA,CAACZ,WAAW;gBACVkK,WAAW,EAAC,0BAAM;gBAClBjB,KAAK,EAAE;kBAAE1C,KAAK,EAAE;gBAAO,CAAE;gBACzBsF,GAAG,EAAE,CAAE;gBACPC,UAAU,EAAC;cAAG;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,UAAU;cACfnB,KAAK,EAAC,0BAAM;cACZoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiH,QAAA,eAEhDtG,OAAA,CAACZ,WAAW;gBACVkK,WAAW,EAAC,4CAAS;gBACrBjB,KAAK,EAAE;kBAAE1C,KAAK,EAAE;gBAAO,CAAE;gBACzBsF,GAAG,EAAE,CAAE;gBACPC,UAAU,EAAC;cAAG;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,UAAU;cACfnB,KAAK,EAAC,cAAI;cACVoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiH,QAAA,eAE9CtG,OAAA,CAACxB,KAAK;gBAAC8K,WAAW,EAAC;cAAO;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,aAAa;cAClBnB,KAAK,EAAC,oBAAK;cACXoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAiH,QAAA,eAE/CtG,OAAA,CAACvB,MAAM;gBAAC6K,WAAW,EAAC,sCAAQ;gBAAAhD,QAAA,gBAC1BtG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,MAAM;kBAAAtC,QAAA,EAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/BnG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,QAAQ;kBAAAtC,QAAA,EAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCnG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,KAAK;kBAAAtC,QAAA,EAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,QAAQ;cACbnB,KAAK,EAAC,cAAI;cACVoB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3L,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiH,QAAA,eAE9CtG,OAAA,CAACvB,MAAM;gBAAC6K,WAAW,EAAC,gCAAO;gBAAAhD,QAAA,gBACzBtG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,QAAQ;kBAAAtC,QAAA,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCnG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,UAAU;kBAAAtC,QAAA,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnG,OAAA,CAACK,MAAM;kBAACuI,KAAK,EAAC,cAAc;kBAAAtC,QAAA,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,gBAAgB;cACrBnB,KAAK,EAAC,oBAAK;cAAArD,QAAA,eAEXtG,OAAA,CAACZ,WAAW;gBACVkK,WAAW,EAAC,sCAAQ;gBACpBjB,KAAK,EAAE;kBAAE1C,KAAK,EAAE;gBAAO,CAAE;gBACzBsF,GAAG,EAAE,CAAE;gBACPC,UAAU,EAAC;cAAG;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArC,QAAA,eAClBtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,mBAAmB;cACxBnB,KAAK,EAAC,0BAAM;cAAArD,QAAA,eAEZtG,OAAA,CAACxB,KAAK;gBAAC8K,WAAW,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACnB,GAAG;YAAC4J,EAAE,EAAE,EAAG;YAAAnC,QAAA,eACVtG,OAAA,CAACrB,IAAI,CAACkM,IAAI;cACRC,IAAI,EAAC,OAAO;cACZnB,KAAK,EAAC,cAAI;cAAArD,QAAA,eAEVtG,OAAA,CAACI,QAAQ;gBAAC+K,IAAI,EAAE,CAAE;gBAAC7B,WAAW,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5F,EAAA,CA/xBID,cAAwB;EAAA,QAUb3B,IAAI,CAACgD,OAAO;AAAA;AAAAyJ,EAAA,GAVvB9K,cAAwB;AAiyB9B,eAAeA,cAAc;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}