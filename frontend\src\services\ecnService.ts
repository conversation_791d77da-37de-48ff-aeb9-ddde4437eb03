import { apiService } from './api';
import { ECN, ECNComment, AffectedItem, ECNApprover } from '../types';
import { FetchParams, PaginatedResponse } from '../types/api';

interface ECNCreateData {
  title: string;
  description: string;
  reason: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  implementationPlan: string;
  effectiveDate?: string;
  affectedItems: AffectedItem[];
  approvers: ECNApprover[];
}

interface ECNReviewData {
  action: 'APPROVED' | 'REJECTED';
  comment?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
}

interface ECNCommentData {
  content: string;
  type: 'COMMENT' | 'SUGGESTION' | 'INFO';
}

interface ImpactAnalysisResult {
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  estimatedCost: number;
  estimatedDuration: number;
  affectedBOMs: string[];
  affectedOrders: string[];
  affectedInventory: string[];
  recommendations: string[];
}

class ECNService {
  // 获取ECN列表
  async getECNs(params: FetchParams): Promise<PaginatedResponse<ECN>> {
    return apiService.get<PaginatedResponse<ECN>>('/ecn', { params });
  }

  // 根据ID获取ECN详情
  async getECNById(id: string): Promise<ECN> {
    return apiService.get<ECN>(`/ecn/${id}`);
  }

  // 创建ECN
  async createECN(data: ECNCreateData): Promise<ECN> {
    return apiService.post<ECN>('/ecn', data);
  }

  // 更新ECN
  async updateECN(id: string, data: Partial<ECNCreateData>): Promise<ECN> {
    return apiService.put<ECN>(`/ecn/${id}`, data);
  }

  // 删除ECN
  async deleteECN(id: string): Promise<void> {
    return apiService.delete(`/ecn/${id}`);
  }

  // 批量删除ECN
  async batchDeleteECNs(ids: string[]): Promise<void> {
    return apiService.post('/ecn/batch-delete', { ids });
  }

  // 复制ECN
  async copyECN(id: string): Promise<ECN> {
    return apiService.post<ECN>(`/ecn/${id}/copy`);
  }

  // 提交ECN审核
  async submitECNForReview(id: string): Promise<ECN> {
    return apiService.post<ECN>(`/ecn/${id}/submit`);
  }

  // 审核ECN
  async reviewECN(id: string, data: ECNReviewData): Promise<ECN> {
    return apiService.post<ECN>(`/ecn/${id}/review`, data);
  }

  // 实施ECN
  async implementECN(id: string): Promise<ECN> {
    return apiService.post<ECN>(`/ecn/${id}/implement`);
  }

  // 获取ECN评论
  async getECNComments(ecnId: string): Promise<ECNComment[]> {
    return apiService.get<ECNComment[]>(`/ecn/${ecnId}/comments`);
  }

  // 添加ECN评论
  async addECNComment(ecnId: string, data: ECNCommentData): Promise<ECNComment> {
    return apiService.post<ECNComment>(`/ecn/${ecnId}/comments`, data);
  }

  // 删除ECN评论
  async deleteECNComment(ecnId: string, commentId: string): Promise<void> {
    return apiService.delete(`/ecn/${ecnId}/comments/${commentId}`);
  }

  // 执行影响分析
  async performImpactAnalysis(affectedItems: AffectedItem[]): Promise<ImpactAnalysisResult> {
    return apiService.post<ImpactAnalysisResult>('/ecn/impact-analysis', { affectedItems });
  }

  // 获取ECN统计信息
  async getECNStatistics(): Promise<{
    total: number;
    draft: number;
    review: number;
    approved: number;
    rejected: number;
    implemented: number;
  }> {
    return apiService.get('/ecn/statistics');
  }

  // 获取我的待审批ECN
  async getMyPendingECNs(): Promise<ECN[]> {
    return apiService.get<ECN[]>('/ecn/my-pending');
  }

  // 获取ECN审批历史
  async getECNApprovalHistory(id: string): Promise<{
    approver: ECNApprover;
    action: string;
    comment: string;
    timestamp: string;
  }[]> {
    return apiService.get(`/ecn/${id}/approval-history`);
  }

  // 导出ECN列表
  async exportECNs(params: FetchParams): Promise<Blob> {
    return apiService.get('/ecn/export', { 
      params,
      responseType: 'blob'
    });
  }

  // 获取ECN模板
  async getECNTemplate(): Promise<Blob> {
    return apiService.get('/ecn/template', {
      responseType: 'blob'
    });
  }

  // 批量导入ECN
  async importECNs(file: File): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    const formData = new FormData();
    formData.append('file', file);
    return apiService.post('/ecn/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  // 获取ECN变更历史
  async getECNChangeHistory(id: string): Promise<{
    field: string;
    oldValue: string;
    newValue: string;
    changedBy: string;
    changedAt: string;
  }[]> {
    return apiService.get(`/ecn/${id}/change-history`);
  }

  // 获取相关ECN
  async getRelatedECNs(id: string): Promise<ECN[]> {
    return apiService.get<ECN[]>(`/ecn/${id}/related`);
  }

  // 设置ECN提醒
  async setECNReminder(id: string, reminderDate: string): Promise<void> {
    return apiService.post(`/ecn/${id}/reminder`, { reminderDate });
  }

  // 取消ECN提醒
  async cancelECNReminder(id: string): Promise<void> {
    return apiService.delete(`/ecn/${id}/reminder`);
  }
}

export const ecnService = new ECNService();
export default ecnService;