{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _objectWithoutProperties from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"type\",\"onConfirm\",\"onCancel\",\"confirmText\",\"cancelText\",\"confirmLoading\",\"title\",\"children\"];import React from'react';import{Modal}from'antd';import{ExclamationCircleOutlined,QuestionCircleOutlined,InfoCircleOutlined}from'@ant-design/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ConfirmDialog=_ref=>{let{type='confirm',onConfirm,onCancel,confirmText='确定',cancelText='取消',confirmLoading=false,title,children}=_ref,props=_objectWithoutProperties(_ref,_excluded);const getIcon=()=>{switch(type){case'warning':case'error':return/*#__PURE__*/_jsx(ExclamationCircleOutlined,{style:{color:'#ff4d4f'}});case'info':return/*#__PURE__*/_jsx(InfoCircleOutlined,{style:{color:'#1890ff'}});case'confirm':default:return/*#__PURE__*/_jsx(QuestionCircleOutlined,{style:{color:'#faad14'}});}};const handleOk=async()=>{if(onConfirm){await onConfirm();}};const handleCancel=()=>{if(onCancel){onCancel();}};return/*#__PURE__*/_jsx(Modal,_objectSpread(_objectSpread({title:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:8},children:[getIcon(),/*#__PURE__*/_jsx(\"span\",{children:title})]}),onOk:handleOk,onCancel:handleCancel,okText:confirmText,cancelText:cancelText,confirmLoading:confirmLoading,okButtonProps:{danger:type==='warning'||type==='error'}},props),{},{children:children}));};// 静态方法，用于快速调用\nConfirmDialog.confirm=config=>{const{confirm}=Modal;return confirm({title:config.title,content:config.content,onOk:config.onConfirm,onCancel:config.onCancel,okButtonProps:{danger:config.type==='warning'||config.type==='error'},icon:(()=>{switch(config.type){case'warning':case'error':return/*#__PURE__*/_jsx(ExclamationCircleOutlined,{style:{color:'#ff4d4f'}});case'info':return/*#__PURE__*/_jsx(InfoCircleOutlined,{style:{color:'#1890ff'}});case'confirm':default:return/*#__PURE__*/_jsx(QuestionCircleOutlined,{style:{color:'#faad14'}});}})()});};export default ConfirmDialog;", "map": {"version": 3, "names": ["React", "Modal", "ExclamationCircleOutlined", "QuestionCircleOutlined", "InfoCircleOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "ConfirmDialog", "_ref", "type", "onConfirm", "onCancel", "confirmText", "cancelText", "confirmLoading", "title", "children", "props", "_objectWithoutProperties", "_excluded", "getIcon", "style", "color", "handleOk", "handleCancel", "_objectSpread", "display", "alignItems", "gap", "onOk", "okText", "okButtonProps", "danger", "confirm", "config", "content", "icon"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/ConfirmDialog.tsx"], "sourcesContent": ["import React from 'react';\nimport { Modal, ModalProps } from 'antd';\nimport { ExclamationCircleOutlined, QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';\n\ninterface ConfirmDialogProps extends Omit<ModalProps, 'onOk' | 'onCancel'> {\n  /** 对话框类型 */\n  type?: 'warning' | 'info' | 'confirm' | 'error';\n  /** 确认回调 */\n  onConfirm?: () => void | Promise<void>;\n  /** 取消回调 */\n  onCancel?: () => void;\n  /** 确认按钮文本 */\n  confirmText?: string;\n  /** 取消按钮文本 */\n  cancelText?: string;\n  /** 是否显示确认按钮加载状态 */\n  confirmLoading?: boolean;\n}\n\nconst ConfirmDialog: React.FC<ConfirmDialogProps> = ({\n  type = 'confirm',\n  onConfirm,\n  onCancel,\n  confirmText = '确定',\n  cancelText = '取消',\n  confirmLoading = false,\n  title,\n  children,\n  ...props\n}) => {\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n      case 'error':\n        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case 'info':\n        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;\n      case 'confirm':\n      default:\n        return <QuestionCircleOutlined style={{ color: '#faad14' }} />;\n    }\n  };\n\n  const handleOk = async () => {\n    if (onConfirm) {\n      await onConfirm();\n    }\n  };\n\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n          {getIcon()}\n          <span>{title}</span>\n        </div>\n      }\n      onOk={handleOk}\n      onCancel={handleCancel}\n      okText={confirmText}\n      cancelText={cancelText}\n      confirmLoading={confirmLoading}\n      okButtonProps={{\n        danger: type === 'warning' || type === 'error'\n      }}\n      {...props}\n    >\n      {children}\n    </Modal>\n  );\n};\n\n// 静态方法，用于快速调用\nConfirmDialog.confirm = (config: {\n  title: string;\n  content: React.ReactNode;\n  onConfirm?: () => void | Promise<void>;\n  onCancel?: () => void;\n  type?: 'warning' | 'info' | 'confirm' | 'error';\n}) => {\n  const { confirm } = Modal;\n  \n  return confirm({\n    title: config.title,\n    content: config.content,\n    onOk: config.onConfirm,\n    onCancel: config.onCancel,\n    okButtonProps: {\n      danger: config.type === 'warning' || config.type === 'error'\n    },\n    icon: (() => {\n      switch (config.type) {\n        case 'warning':\n        case 'error':\n          return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;\n        case 'info':\n          return <InfoCircleOutlined style={{ color: '#1890ff' }} />;\n        case 'confirm':\n        default:\n          return <QuestionCircleOutlined style={{ color: '#faad14' }} />;\n      }\n    })()\n  });\n};\n\nexport default ConfirmDialog;"], "mappings": "oXAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,KAAK,KAAoB,MAAM,CACxC,OAASC,yBAAyB,CAAEC,sBAAsB,CAAEC,kBAAkB,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAiB1G,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAU9C,IAV+C,CACnDC,IAAI,CAAG,SAAS,CAChBC,SAAS,CACTC,QAAQ,CACRC,WAAW,CAAG,IAAI,CAClBC,UAAU,CAAG,IAAI,CACjBC,cAAc,CAAG,KAAK,CACtBC,KAAK,CACLC,QAEF,CAAC,CAAAR,IAAA,CADIS,KAAK,CAAAC,wBAAA,CAAAV,IAAA,CAAAW,SAAA,EAER,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACpB,OAAQX,IAAI,EACV,IAAK,SAAS,CACd,IAAK,OAAO,CACV,mBAAOL,IAAA,CAACJ,yBAAyB,EAACqB,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACnE,IAAK,MAAM,CACT,mBAAOlB,IAAA,CAACF,kBAAkB,EAACmB,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC5D,IAAK,SAAS,CACd,QACE,mBAAOlB,IAAA,CAACH,sBAAsB,EAACoB,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAClE,CACF,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAIb,SAAS,CAAE,CACb,KAAM,CAAAA,SAAS,CAAC,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAc,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIb,QAAQ,CAAE,CACZA,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,CAED,mBACEP,IAAA,CAACL,KAAK,CAAA0B,aAAA,CAAAA,aAAA,EACJV,KAAK,cACHT,KAAA,QAAKe,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAZ,QAAA,EAC3DI,OAAO,CAAC,CAAC,cACVhB,IAAA,SAAAY,QAAA,CAAOD,KAAK,CAAO,CAAC,EACjB,CACN,CACDc,IAAI,CAAEN,QAAS,CACfZ,QAAQ,CAAEa,YAAa,CACvBM,MAAM,CAAElB,WAAY,CACpBC,UAAU,CAAEA,UAAW,CACvBC,cAAc,CAAEA,cAAe,CAC/BiB,aAAa,CAAE,CACbC,MAAM,CAAEvB,IAAI,GAAK,SAAS,EAAIA,IAAI,GAAK,OACzC,CAAE,EACEQ,KAAK,MAAAD,QAAA,CAERA,QAAQ,EACJ,CAAC,CAEZ,CAAC,CAED;AACAT,aAAa,CAAC0B,OAAO,CAAIC,MAMxB,EAAK,CACJ,KAAM,CAAED,OAAQ,CAAC,CAAGlC,KAAK,CAEzB,MAAO,CAAAkC,OAAO,CAAC,CACblB,KAAK,CAAEmB,MAAM,CAACnB,KAAK,CACnBoB,OAAO,CAAED,MAAM,CAACC,OAAO,CACvBN,IAAI,CAAEK,MAAM,CAACxB,SAAS,CACtBC,QAAQ,CAAEuB,MAAM,CAACvB,QAAQ,CACzBoB,aAAa,CAAE,CACbC,MAAM,CAAEE,MAAM,CAACzB,IAAI,GAAK,SAAS,EAAIyB,MAAM,CAACzB,IAAI,GAAK,OACvD,CAAC,CACD2B,IAAI,CAAE,CAAC,IAAM,CACX,OAAQF,MAAM,CAACzB,IAAI,EACjB,IAAK,SAAS,CACd,IAAK,OAAO,CACV,mBAAOL,IAAA,CAACJ,yBAAyB,EAACqB,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACnE,IAAK,MAAM,CACT,mBAAOlB,IAAA,CAACF,kBAAkB,EAACmB,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC5D,IAAK,SAAS,CACd,QACE,mBAAOlB,IAAA,CAACH,sBAAsB,EAACoB,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAClE,CACF,CAAC,EAAE,CACL,CAAC,CAAC,CACJ,CAAC,CAED,cAAe,CAAAf,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}