{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Input,Select,DatePicker,Modal,Row,Col,Typography,Tag,Tooltip,message,Tabs,Timeline,Descriptions,Alert,Statistic,Progress}from'antd';import{EyeOutlined,QrcodeOutlined,ExportOutlined,FilterOutlined,Bar<PERSON>hartOutlined,AlertOutlined}from'@ant-design/icons';import{formatCurrency,formatDate}from'../../utils/format';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Search}=Input;const{TabPane}=Tabs;const{RangePicker}=DatePicker;const BatchTrackingPage=()=>{const[loading,setLoading]=useState(false);const[batches,setBatches]=useState([]);const[statistics,setStatistics]=useState({totalBatches:0,activeBatches:0,expiredBatches:0,lowStockBatches:0,totalValue:0,averageAge:0,turnoverRate:0});const[searchKeyword,setSearchKeyword]=useState('');const[statusFilter,setStatusFilter]=useState();const[supplierFilter,setSupplierFilter]=useState();const[dateRange,setDateRange]=useState(null);const handleDateRangeChange=(dates,dateStrings)=>{setDateRange(dates);};const[selectedBatch,setSelectedBatch]=useState(null);const[detailModalVisible,setDetailModalVisible]=useState(false);const[activeTab,setActiveTab]=useState('list');// 模拟数据\nconst mockBatches=[{id:'1',batchNumber:'B2024030001',materialId:'M001',materialCode:'RF-PA-001',materialName:'RF功率放大器',specification:'2.4GHz 20W',supplier:'深圳射频科技',receivedDate:'2024-03-01T08:00:00Z',expiryDate:'2025-03-01T00:00:00Z',originalQuantity:100,currentQuantity:75,reservedQuantity:10,availableQuantity:65,unit:'PCS',unitPrice:150.00,totalValue:11250.00,location:'A区-01-03',status:'AVAILABLE',qualityStatus:'PASSED',qrCode:'QR_B2024030001',transactions:[{id:'T001',type:'RECEIVE',quantity:100,remainingQuantity:100,operator:'张三',department:'仓储部',reference:'PO-2024-001',timestamp:'2024-03-01T08:00:00Z',location:'A区-01-03'},{id:'T002',type:'ISSUE',quantity:-25,remainingQuantity:75,operator:'李四',department:'生产部',workOrder:'WO-2024-001',reference:'ISS-2024-001',timestamp:'2024-03-05T10:30:00Z',location:'A区-01-03'}]},{id:'2',batchNumber:'B2024030002',materialId:'M002',materialCode:'PCB-MAIN-001',materialName:'主控PCB板',specification:'4层板 FR4',supplier:'苏州电路板厂',receivedDate:'2024-03-02T09:15:00Z',expiryDate:'2026-03-02T00:00:00Z',originalQuantity:200,currentQuantity:180,reservedQuantity:20,availableQuantity:160,unit:'PCS',unitPrice:85.00,totalValue:15300.00,location:'A区-02-01',status:'AVAILABLE',qualityStatus:'PASSED',qrCode:'QR_B2024030002',transactions:[{id:'T003',type:'RECEIVE',quantity:200,remainingQuantity:200,operator:'王五',department:'仓储部',reference:'PO-2024-002',timestamp:'2024-03-02T09:15:00Z',location:'A区-02-01'},{id:'T004',type:'ISSUE',quantity:-20,remainingQuantity:180,operator:'赵六',department:'生产部',workOrder:'WO-2024-002',reference:'ISS-2024-002',timestamp:'2024-03-06T14:20:00Z',location:'A区-02-01'}]},{id:'3',batchNumber:'B2024020015',materialId:'M003',materialCode:'CAP-CER-001',materialName:'陶瓷电容',specification:'0805 10uF',supplier:'村田制作所',receivedDate:'2024-02-15T11:30:00Z',expiryDate:'2024-04-15T00:00:00Z',originalQuantity:5000,currentQuantity:500,reservedQuantity:0,availableQuantity:500,unit:'PCS',unitPrice:0.25,totalValue:125.00,location:'B区-03-05',status:'EXPIRED',qualityStatus:'PENDING',qrCode:'QR_B2024020015',transactions:[{id:'T005',type:'RECEIVE',quantity:5000,remainingQuantity:5000,operator:'孙七',department:'仓储部',reference:'PO-2024-003',timestamp:'2024-02-15T11:30:00Z',location:'B区-03-05'},{id:'T006',type:'ISSUE',quantity:-4500,remainingQuantity:500,operator:'周八',department:'生产部',workOrder:'WO-2024-003',reference:'ISS-2024-003',timestamp:'2024-03-10T16:45:00Z',location:'B区-03-05'}]}];useEffect(()=>{loadBatchData();loadStatistics();},[]);const loadBatchData=async()=>{try{setLoading(true);// TODO: 调用API获取批次数据\nsetBatches(mockBatches);}catch(error){message.error('加载批次数据失败');}finally{setLoading(false);}};const loadStatistics=async()=>{try{// TODO: 调用API获取统计数据\nconst stats={totalBatches:mockBatches.length,activeBatches:mockBatches.filter(b=>b.status==='AVAILABLE').length,expiredBatches:mockBatches.filter(b=>b.status==='EXPIRED').length,lowStockBatches:mockBatches.filter(b=>b.currentQuantity<b.originalQuantity*0.2).length,totalValue:mockBatches.reduce((sum,b)=>sum+b.totalValue,0),averageAge:45,turnoverRate:2.5};setStatistics(stats);}catch(error){message.error('加载统计数据失败');}};const handleViewDetail=batch=>{setSelectedBatch(batch);setDetailModalVisible(true);};const handleExport=()=>{// TODO: 实现导出功能\nmessage.success('导出功能开发中');};const getStatusColor=status=>{const colors={AVAILABLE:'green',RESERVED:'blue',EXPIRED:'red',DAMAGED:'orange',CONSUMED:'gray'};return colors[status]||'default';};const getQualityStatusColor=status=>{const colors={PASSED:'green',PENDING:'orange',FAILED:'red'};return colors[status]||'default';};const getTransactionTypeText=type=>{const types={RECEIVE:'入库',ISSUE:'出库',ADJUST:'调整',TRANSFER:'转移',RETURN:'退库'};return types[type]||type;};const getTransactionTypeColor=type=>{const colors={RECEIVE:'green',ISSUE:'red',ADJUST:'orange',TRANSFER:'blue',RETURN:'purple'};return colors[type]||'default';};const filteredBatches=batches.filter(batch=>{if(searchKeyword&&!(batch.batchNumber.toLowerCase().includes(searchKeyword.toLowerCase())||batch.materialCode.toLowerCase().includes(searchKeyword.toLowerCase())||batch.materialName.toLowerCase().includes(searchKeyword.toLowerCase())||batch.supplier.toLowerCase().includes(searchKeyword.toLowerCase()))){return false;}if(statusFilter&&batch.status!==statusFilter){return false;}if(supplierFilter&&batch.supplier!==supplierFilter){return false;}if(dateRange&&dateRange[0]&&dateRange[1]){const receivedDate=new Date(batch.receivedDate);if(receivedDate<dateRange[0].toDate()||receivedDate>dateRange[1].toDate()){return false;}}return true;});const batchColumns=[{title:'批次号',dataIndex:'batchNumber',key:'batchNumber',width:120,fixed:'left',render:(text,record)=>/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:0,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:text}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'12px'},children:record.qrCode})]})},{title:'物料信息',key:'material',width:200,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:0,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:record.materialCode}),/*#__PURE__*/_jsx(Text,{children:record.materialName}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'12px'},children:record.specification})]})},{title:'供应商',dataIndex:'supplier',key:'supplier',width:120},{title:'收货日期',dataIndex:'receivedDate',key:'receivedDate',width:100,render:date=>formatDate(date)},{title:'有效期',dataIndex:'expiryDate',key:'expiryDate',width:100,render:date=>date?formatDate(date):'-'},{title:'库存数量',key:'quantity',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:0,children:[/*#__PURE__*/_jsxs(Text,{children:[\"\\u5F53\\u524D: \",record.currentQuantity,\" \",record.unit]}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:'12px'},children:[\"\\u53EF\\u7528: \",record.availableQuantity,\" | \\u9884\\u7559: \",record.reservedQuantity]})]})},{title:'库存率',key:'stockRate',width:100,render:(_,record)=>{const rate=record.currentQuantity/record.originalQuantity*100;return/*#__PURE__*/_jsx(Progress,{percent:rate,size:\"small\",status:rate<20?'exception':rate<50?'active':'success',showInfo:false});}},{title:'库位',dataIndex:'location',key:'location',width:100},{title:'状态',key:'status',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:0,children:[/*#__PURE__*/_jsx(Tag,{color:getStatusColor(record.status),children:record.status==='AVAILABLE'?'可用':record.status==='RESERVED'?'预留':record.status==='EXPIRED'?'过期':record.status==='DAMAGED'?'损坏':'已消耗'}),/*#__PURE__*/_jsx(Tag,{color:getQualityStatusColor(record.qualityStatus),children:record.qualityStatus==='PASSED'?'合格':record.qualityStatus==='PENDING'?'待检':'不合格'})]})},{title:'总价值',dataIndex:'totalValue',key:'totalValue',width:100,render:value=>formatCurrency(value)},{title:'操作',key:'action',width:120,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\\u8BE6\\u60C5\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewDetail(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\\u4E8C\\u7EF4\\u7801\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(QrcodeOutlined,{}),onClick:()=>{// TODO: 显示二维码\nmessage.info('二维码功能开发中');}})})]})}];const renderStatistics=()=>/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:12,sm:8,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u6279\\u6B21\\u6570\",value:statistics.totalBatches,prefix:/*#__PURE__*/_jsx(BarChartOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:8,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6D3B\\u8DC3\\u6279\\u6B21\",value:statistics.activeBatches,valueStyle:{color:'#3f8600'}})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:8,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8FC7\\u671F\\u6279\\u6B21\",value:statistics.expiredBatches,valueStyle:{color:'#cf1322'},prefix:/*#__PURE__*/_jsx(AlertOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:8,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u4F4E\\u5E93\\u5B58\\u6279\\u6B21\",value:statistics.lowStockBatches,valueStyle:{color:'#fa8c16'}})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:8,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u4EF7\\u503C\",value:statistics.totalValue,formatter:value=>formatCurrency(Number(value))})})}),/*#__PURE__*/_jsx(Col,{xs:12,sm:8,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E73\\u5747\\u5E93\\u9F84\",value:statistics.averageAge,suffix:\"\\u5929\"})})})]});const renderBatchDetail=()=>{if(!selectedBatch)return null;return/*#__PURE__*/_jsx(Modal,{title:\"\\u6279\\u6B21\\u8BE6\\u60C5 - \".concat(selectedBatch.batchNumber),open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:/*#__PURE__*/_jsxs(Tabs,{defaultActiveKey:\"info\",children:[/*#__PURE__*/_jsx(TabPane,{tab:\"\\u57FA\\u672C\\u4FE1\\u606F\",children:/*#__PURE__*/_jsxs(Descriptions,{column:2,bordered:true,size:\"small\",children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6279\\u6B21\\u53F7\",children:selectedBatch.batchNumber}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4E8C\\u7EF4\\u7801\",children:selectedBatch.qrCode}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7269\\u6599\\u7F16\\u7801\",children:selectedBatch.materialCode}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7269\\u6599\\u540D\\u79F0\",children:selectedBatch.materialName}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u89C4\\u683C\",children:selectedBatch.specification}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u4F9B\\u5E94\\u5546\",children:selectedBatch.supplier}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6536\\u8D27\\u65E5\\u671F\",children:formatDate(selectedBatch.receivedDate)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6709\\u6548\\u671F\",children:selectedBatch.expiryDate?formatDate(selectedBatch.expiryDate):'-'}),/*#__PURE__*/_jsxs(Descriptions.Item,{label:\"\\u539F\\u59CB\\u6570\\u91CF\",children:[selectedBatch.originalQuantity,\" \",selectedBatch.unit]}),/*#__PURE__*/_jsxs(Descriptions.Item,{label:\"\\u5F53\\u524D\\u6570\\u91CF\",children:[selectedBatch.currentQuantity,\" \",selectedBatch.unit]}),/*#__PURE__*/_jsxs(Descriptions.Item,{label:\"\\u53EF\\u7528\\u6570\\u91CF\",children:[selectedBatch.availableQuantity,\" \",selectedBatch.unit]}),/*#__PURE__*/_jsxs(Descriptions.Item,{label:\"\\u9884\\u7559\\u6570\\u91CF\",children:[selectedBatch.reservedQuantity,\" \",selectedBatch.unit]}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5355\\u4EF7\",children:formatCurrency(selectedBatch.unitPrice)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u603B\\u4EF7\\u503C\",children:formatCurrency(selectedBatch.totalValue)}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5E93\\u4F4D\",children:selectedBatch.location}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Tag,{color:getStatusColor(selectedBatch.status),children:selectedBatch.status==='AVAILABLE'?'可用':selectedBatch.status==='RESERVED'?'预留':selectedBatch.status==='EXPIRED'?'过期':selectedBatch.status==='DAMAGED'?'损坏':'已消耗'}),/*#__PURE__*/_jsx(Tag,{color:getQualityStatusColor(selectedBatch.qualityStatus),children:selectedBatch.qualityStatus==='PASSED'?'合格':selectedBatch.qualityStatus==='PENDING'?'待检':'不合格'})]})})]})},\"info\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u4EA4\\u6613\\u8BB0\\u5F55\",children:/*#__PURE__*/_jsx(Timeline,{children:selectedBatch.transactions.map(transaction=>/*#__PURE__*/_jsx(Timeline.Item,{color:getTransactionTypeColor(transaction.type),children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Tag,{color:getTransactionTypeColor(transaction.type),children:getTransactionTypeText(transaction.type)}),/*#__PURE__*/_jsxs(Text,{strong:true,children:[transaction.quantity>0?'+':'',transaction.quantity,\" \",selectedBatch.unit]}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u4F59\\u91CF: \",transaction.remainingQuantity,\" \",selectedBatch.unit]})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:'12px'},children:[formatDate(transaction.timestamp),\" | \",transaction.operator,\" | \",transaction.department]})}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:2},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:'12px'},children:[\"\\u53C2\\u8003: \",transaction.reference,transaction.workOrder&&\" | \\u5DE5\\u5355: \".concat(transaction.workOrder),transaction.reason&&\" | \\u539F\\u56E0: \".concat(transaction.reason)]})}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:2},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:'12px'},children:[\"\\u5E93\\u4F4D: \",transaction.location]})})]})},transaction.id))})},\"transactions\")]})});};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u6279\\u6B21\\u8DDF\\u8E2A\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u5168\\u9762\\u8DDF\\u8E2A\\u7269\\u6599\\u6279\\u6B21\\u7684\\u5165\\u5E93\\u3001\\u51FA\\u5E93\\u3001\\u8C03\\u6574\\u7B49\\u5168\\u751F\\u547D\\u5468\\u671F\\u8BB0\\u5F55\"})]}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u6279\\u6B21\\u53F7\\u3001\\u7269\\u6599\\u3001\\u4F9B\\u5E94\\u5546\",allowClear:true,style:{width:250},onSearch:setSearchKeyword}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u72B6\\u6001\",allowClear:true,style:{width:100},value:statusFilter,onChange:setStatusFilter,options:[{label:'可用',value:'AVAILABLE'},{label:'预留',value:'RESERVED'},{label:'过期',value:'EXPIRED'},{label:'损坏',value:'DAMAGED'},{label:'已消耗',value:'CONSUMED'}]}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u4F9B\\u5E94\\u5546\",allowClear:true,style:{width:120},value:supplierFilter,onChange:setSupplierFilter,options:[{label:'深圳射频科技',value:'深圳射频科技'},{label:'苏州电路板厂',value:'苏州电路板厂'},{label:'村田制作所',value:'村田制作所'}]}),/*#__PURE__*/_jsx(RangePicker,{placeholder:['开始日期','结束日期'],value:dateRange,onChange:handleDateRangeChange,style:{width:240}}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(FilterOutlined,{}),children:\"\\u9AD8\\u7EA7\\u7B5B\\u9009\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"})]})})]}),/*#__PURE__*/_jsx(Alert,{message:\"\\u6279\\u6B21\\u8DDF\\u8E2A\\u63D0\\u793A\",description:\"\\u7CFB\\u7EDF\\u81EA\\u52A8\\u8BB0\\u5F55\\u6BCF\\u4E2A\\u6279\\u6B21\\u7684\\u5B8C\\u6574\\u751F\\u547D\\u5468\\u671F\\uFF0C\\u5305\\u62EC\\u5165\\u5E93\\u3001\\u51FA\\u5E93\\u3001\\u8C03\\u6574\\u3001\\u8F6C\\u79FB\\u7B49\\u6240\\u6709\\u64CD\\u4F5C\\uFF0C\\u786E\\u4FDD\\u7269\\u6599\\u7684\\u5B8C\\u5168\\u53EF\\u8FFD\\u6EAF\\u6027\\u3002\",type:\"info\",showIcon:true,closable:true,style:{marginBottom:16}}),/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:[/*#__PURE__*/_jsxs(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(BarChartOutlined,{}),\"\\u7EDF\\u8BA1\\u6982\\u89C8\"]}),children:[renderStatistics(),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u6279\\u6B21\\u72B6\\u6001\\u5206\\u5E03\",size:\"small\",children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u6279\\u6B21\\u72B6\\u6001\\u5206\\u5E03\\u56FE\\u8868\\u5C06\\u5728\\u6B64\\u663E\\u793A\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u5E93\\u9F84\\u5206\\u6790\",size:\"small\",children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u5E93\\u9F84\\u5206\\u6790\\u56FE\\u8868\\u5C06\\u5728\\u6B64\\u663E\\u793A\"})})})]})]},\"overview\"),/*#__PURE__*/_jsxs(TabPane,{tab:\"\\u6279\\u6B21\\u6E05\\u5355\",children:[renderStatistics(),/*#__PURE__*/_jsx(Table,{columns:batchColumns,dataSource:filteredBatches,loading:loading,rowKey:\"id\",scroll:{x:1400},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761\\uFF0C\\u5171 \").concat(total,\" \\u6761\\u8BB0\\u5F55\")}})]},\"list\")]})]}),renderBatchDetail()]});};export default BatchTrackingPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "DatePicker", "Modal", "Row", "Col", "Typography", "Tag", "<PERSON><PERSON><PERSON>", "message", "Tabs", "Timeline", "Descriptions", "<PERSON><PERSON>", "Statistic", "Progress", "EyeOutlined", "QrcodeOutlined", "ExportOutlined", "FilterOutlined", "BarChartOutlined", "Alert<PERSON>ut<PERSON>", "formatCurrency", "formatDate", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Search", "TabPane", "RangePicker", "BatchTrackingPage", "loading", "setLoading", "batches", "setBatches", "statistics", "setStatistics", "totalBatches", "activeBatches", "expiredBatches", "lowStockBatches", "totalValue", "averageAge", "turnoverRate", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "supplierFilter", "setSupplier<PERSON><PERSON>er", "date<PERSON><PERSON><PERSON>", "setDateRange", "handleDateRangeChange", "dates", "dateStrings", "selected<PERSON><PERSON>", "setSelectedBatch", "detailModalVisible", "setDetailModalVisible", "activeTab", "setActiveTab", "mockBatches", "id", "batchNumber", "materialId", "materialCode", "materialName", "specification", "supplier", "receivedDate", "expiryDate", "originalQuantity", "currentQuantity", "reservedQuantity", "availableQuantity", "unit", "unitPrice", "location", "status", "qualityStatus", "qrCode", "transactions", "type", "quantity", "remainingQuantity", "operator", "department", "reference", "timestamp", "workOrder", "loadBatchData", "loadStatistics", "error", "stats", "length", "filter", "b", "reduce", "sum", "handleViewDetail", "batch", "handleExport", "success", "getStatusColor", "colors", "AVAILABLE", "RESERVED", "EXPIRED", "DAMAGED", "CONSUMED", "getQualityStatusColor", "PASSED", "PENDING", "FAILED", "getTransactionTypeText", "types", "RECEIVE", "ISSUE", "ADJUST", "TRANSFER", "RETURN", "getTransactionTypeColor", "filteredBatches", "toLowerCase", "includes", "Date", "toDate", "batchColumns", "title", "dataIndex", "key", "width", "fixed", "render", "text", "record", "direction", "size", "children", "strong", "style", "fontSize", "_", "date", "rate", "percent", "showInfo", "color", "value", "icon", "onClick", "info", "renderStatistics", "gutter", "marginBottom", "xs", "sm", "md", "lg", "prefix", "valueStyle", "formatter", "Number", "suffix", "renderBatchDetail", "concat", "open", "onCancel", "footer", "defaultActiveKey", "tab", "column", "bordered", "<PERSON><PERSON>", "label", "map", "transaction", "marginTop", "reason", "justify", "align", "level", "margin", "placeholder", "allowClear", "onSearch", "onChange", "options", "description", "showIcon", "closable", "active<PERSON><PERSON>", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/inventory/BatchTrackingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  DatePicker,\n  Modal,\n  Form,\n  Row,\n  Col,\n  Typography,\n  Tag,\n  Tooltip,\n  message,\n  Tabs,\n  Timeline,\n  Descriptions,\n  Alert,\n  Statistic,\n  Progress,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  HistoryOutlined,\n  QrcodeOutlined,\n  ExportOutlined,\n  FilterOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  AlertOutlined,\n} from '@ant-design/icons';\nimport { formatCurrency, formatDate } from '../../utils/format';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { TabPane } = Tabs;\nconst { RangePicker } = DatePicker;\n\ninterface BatchInfo {\n  id: string;\n  batchNumber: string;\n  materialId: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  supplier: string;\n  receivedDate: string;\n  expiryDate?: string;\n  originalQuantity: number;\n  currentQuantity: number;\n  reservedQuantity: number;\n  availableQuantity: number;\n  unit: string;\n  unitPrice: number;\n  totalValue: number;\n  location: string;\n  status: 'AVAILABLE' | 'RESERVED' | 'EXPIRED' | 'DAMAGED' | 'CONSUMED';\n  qualityStatus: 'PASSED' | 'PENDING' | 'FAILED';\n  transactions: BatchTransaction[];\n  qrCode: string;\n}\n\ninterface BatchTransaction {\n  id: string;\n  type: 'RECEIVE' | 'ISSUE' | 'ADJUST' | 'TRANSFER' | 'RETURN';\n  quantity: number;\n  remainingQuantity: number;\n  operator: string;\n  department: string;\n  workOrder?: string;\n  reference: string;\n  reason?: string;\n  timestamp: string;\n  location: string;\n}\n\ninterface BatchStatistics {\n  totalBatches: number;\n  activeBatches: number;\n  expiredBatches: number;\n  lowStockBatches: number;\n  totalValue: number;\n  averageAge: number;\n  turnoverRate: number;\n}\n\nconst BatchTrackingPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [batches, setBatches] = useState<BatchInfo[]>([]);\n  const [statistics, setStatistics] = useState<BatchStatistics>({\n    totalBatches: 0,\n    activeBatches: 0,\n    expiredBatches: 0,\n    lowStockBatches: 0,\n    totalValue: 0,\n    averageAge: 0,\n    turnoverRate: 0,\n  });\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string | undefined>();\n  const [supplierFilter, setSupplierFilter] = useState<string | undefined>();\n  const [dateRange, setDateRange] = useState<[any, any] | null>(null);\n\n  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {\n    setDateRange(dates);\n  };\n  const [selectedBatch, setSelectedBatch] = useState<BatchInfo | null>(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [activeTab, setActiveTab] = useState('list');\n\n  // 模拟数据\n  const mockBatches: BatchInfo[] = [\n    {\n      id: '1',\n      batchNumber: 'B2024030001',\n      materialId: 'M001',\n      materialCode: 'RF-PA-001',\n      materialName: 'RF功率放大器',\n      specification: '2.4GHz 20W',\n      supplier: '深圳射频科技',\n      receivedDate: '2024-03-01T08:00:00Z',\n      expiryDate: '2025-03-01T00:00:00Z',\n      originalQuantity: 100,\n      currentQuantity: 75,\n      reservedQuantity: 10,\n      availableQuantity: 65,\n      unit: 'PCS',\n      unitPrice: 150.00,\n      totalValue: 11250.00,\n      location: 'A区-01-03',\n      status: 'AVAILABLE',\n      qualityStatus: 'PASSED',\n      qrCode: 'QR_B2024030001',\n      transactions: [\n        {\n          id: 'T001',\n          type: 'RECEIVE',\n          quantity: 100,\n          remainingQuantity: 100,\n          operator: '张三',\n          department: '仓储部',\n          reference: 'PO-2024-001',\n          timestamp: '2024-03-01T08:00:00Z',\n          location: 'A区-01-03',\n        },\n        {\n          id: 'T002',\n          type: 'ISSUE',\n          quantity: -25,\n          remainingQuantity: 75,\n          operator: '李四',\n          department: '生产部',\n          workOrder: 'WO-2024-001',\n          reference: 'ISS-2024-001',\n          timestamp: '2024-03-05T10:30:00Z',\n          location: 'A区-01-03',\n        },\n      ],\n    },\n    {\n      id: '2',\n      batchNumber: 'B2024030002',\n      materialId: 'M002',\n      materialCode: 'PCB-MAIN-001',\n      materialName: '主控PCB板',\n      specification: '4层板 FR4',\n      supplier: '苏州电路板厂',\n      receivedDate: '2024-03-02T09:15:00Z',\n      expiryDate: '2026-03-02T00:00:00Z',\n      originalQuantity: 200,\n      currentQuantity: 180,\n      reservedQuantity: 20,\n      availableQuantity: 160,\n      unit: 'PCS',\n      unitPrice: 85.00,\n      totalValue: 15300.00,\n      location: 'A区-02-01',\n      status: 'AVAILABLE',\n      qualityStatus: 'PASSED',\n      qrCode: 'QR_B2024030002',\n      transactions: [\n        {\n          id: 'T003',\n          type: 'RECEIVE',\n          quantity: 200,\n          remainingQuantity: 200,\n          operator: '王五',\n          department: '仓储部',\n          reference: 'PO-2024-002',\n          timestamp: '2024-03-02T09:15:00Z',\n          location: 'A区-02-01',\n        },\n        {\n          id: 'T004',\n          type: 'ISSUE',\n          quantity: -20,\n          remainingQuantity: 180,\n          operator: '赵六',\n          department: '生产部',\n          workOrder: 'WO-2024-002',\n          reference: 'ISS-2024-002',\n          timestamp: '2024-03-06T14:20:00Z',\n          location: 'A区-02-01',\n        },\n      ],\n    },\n    {\n      id: '3',\n      batchNumber: 'B2024020015',\n      materialId: 'M003',\n      materialCode: 'CAP-CER-001',\n      materialName: '陶瓷电容',\n      specification: '0805 10uF',\n      supplier: '村田制作所',\n      receivedDate: '2024-02-15T11:30:00Z',\n      expiryDate: '2024-04-15T00:00:00Z',\n      originalQuantity: 5000,\n      currentQuantity: 500,\n      reservedQuantity: 0,\n      availableQuantity: 500,\n      unit: 'PCS',\n      unitPrice: 0.25,\n      totalValue: 125.00,\n      location: 'B区-03-05',\n      status: 'EXPIRED',\n      qualityStatus: 'PENDING',\n      qrCode: 'QR_B2024020015',\n      transactions: [\n        {\n          id: 'T005',\n          type: 'RECEIVE',\n          quantity: 5000,\n          remainingQuantity: 5000,\n          operator: '孙七',\n          department: '仓储部',\n          reference: 'PO-2024-003',\n          timestamp: '2024-02-15T11:30:00Z',\n          location: 'B区-03-05',\n        },\n        {\n          id: 'T006',\n          type: 'ISSUE',\n          quantity: -4500,\n          remainingQuantity: 500,\n          operator: '周八',\n          department: '生产部',\n          workOrder: 'WO-2024-003',\n          reference: 'ISS-2024-003',\n          timestamp: '2024-03-10T16:45:00Z',\n          location: 'B区-03-05',\n        },\n      ],\n    },\n  ];\n\n  useEffect(() => {\n    loadBatchData();\n    loadStatistics();\n  }, []);\n\n  const loadBatchData = async () => {\n    try {\n      setLoading(true);\n      // TODO: 调用API获取批次数据\n      setBatches(mockBatches);\n    } catch (error) {\n      message.error('加载批次数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistics = async () => {\n    try {\n      // TODO: 调用API获取统计数据\n      const stats: BatchStatistics = {\n        totalBatches: mockBatches.length,\n        activeBatches: mockBatches.filter(b => b.status === 'AVAILABLE').length,\n        expiredBatches: mockBatches.filter(b => b.status === 'EXPIRED').length,\n        lowStockBatches: mockBatches.filter(b => b.currentQuantity < b.originalQuantity * 0.2).length,\n        totalValue: mockBatches.reduce((sum, b) => sum + b.totalValue, 0),\n        averageAge: 45,\n        turnoverRate: 2.5,\n      };\n      setStatistics(stats);\n    } catch (error) {\n      message.error('加载统计数据失败');\n    }\n  };\n\n  const handleViewDetail = (batch: BatchInfo) => {\n    setSelectedBatch(batch);\n    setDetailModalVisible(true);\n  };\n\n  const handleExport = () => {\n    // TODO: 实现导出功能\n    message.success('导出功能开发中');\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors = {\n      AVAILABLE: 'green',\n      RESERVED: 'blue',\n      EXPIRED: 'red',\n      DAMAGED: 'orange',\n      CONSUMED: 'gray',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n  const getQualityStatusColor = (status: string) => {\n    const colors = {\n      PASSED: 'green',\n      PENDING: 'orange',\n      FAILED: 'red',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n  const getTransactionTypeText = (type: string) => {\n    const types = {\n      RECEIVE: '入库',\n      ISSUE: '出库',\n      ADJUST: '调整',\n      TRANSFER: '转移',\n      RETURN: '退库',\n    };\n    return types[type as keyof typeof types] || type;\n  };\n\n  const getTransactionTypeColor = (type: string) => {\n    const colors = {\n      RECEIVE: 'green',\n      ISSUE: 'red',\n      ADJUST: 'orange',\n      TRANSFER: 'blue',\n      RETURN: 'purple',\n    };\n    return colors[type as keyof typeof colors] || 'default';\n  };\n\n  const filteredBatches = batches.filter(batch => {\n    if (searchKeyword && !(\n      batch.batchNumber.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n      batch.materialCode.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n      batch.materialName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n      batch.supplier.toLowerCase().includes(searchKeyword.toLowerCase())\n    )) {\n      return false;\n    }\n    if (statusFilter && batch.status !== statusFilter) {\n      return false;\n    }\n    if (supplierFilter && batch.supplier !== supplierFilter) {\n      return false;\n    }\n    if (dateRange && dateRange[0] && dateRange[1]) {\n      const receivedDate = new Date(batch.receivedDate);\n      if (receivedDate < dateRange[0].toDate() || receivedDate > dateRange[1].toDate()) {\n        return false;\n      }\n    }\n    return true;\n  });\n\n  const batchColumns = [\n    {\n      title: '批次号',\n      dataIndex: 'batchNumber',\n      key: 'batchNumber',\n      width: 120,\n      fixed: 'left' as const,\n      render: (text: string, record: BatchInfo) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{text}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.qrCode}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '物料信息',\n      key: 'material',\n      width: 200,\n      render: (_: any, record: BatchInfo) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{record.materialCode}</Text>\n          <Text>{record.materialName}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.specification}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 120,\n    },\n    {\n      title: '收货日期',\n      dataIndex: 'receivedDate',\n      key: 'receivedDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '有效期',\n      dataIndex: 'expiryDate',\n      key: 'expiryDate',\n      width: 100,\n      render: (date: string) => date ? formatDate(date) : '-',\n    },\n    {\n      title: '库存数量',\n      key: 'quantity',\n      width: 120,\n      render: (_: any, record: BatchInfo) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text>当前: {record.currentQuantity} {record.unit}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            可用: {record.availableQuantity} | 预留: {record.reservedQuantity}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '库存率',\n      key: 'stockRate',\n      width: 100,\n      render: (_: any, record: BatchInfo) => {\n        const rate = (record.currentQuantity / record.originalQuantity) * 100;\n        return (\n          <Progress\n            percent={rate}\n            size=\"small\"\n            status={rate < 20 ? 'exception' : rate < 50 ? 'active' : 'success'}\n            showInfo={false}\n          />\n        );\n      },\n    },\n    {\n      title: '库位',\n      dataIndex: 'location',\n      key: 'location',\n      width: 100,\n    },\n    {\n      title: '状态',\n      key: 'status',\n      width: 120,\n      render: (_: any, record: BatchInfo) => (\n        <Space direction=\"vertical\" size={0}>\n          <Tag color={getStatusColor(record.status)}>\n            {record.status === 'AVAILABLE' ? '可用' :\n             record.status === 'RESERVED' ? '预留' :\n             record.status === 'EXPIRED' ? '过期' :\n             record.status === 'DAMAGED' ? '损坏' : '已消耗'}\n          </Tag>\n          <Tag color={getQualityStatusColor(record.qualityStatus)}>\n            {record.qualityStatus === 'PASSED' ? '合格' :\n             record.qualityStatus === 'PENDING' ? '待检' : '不合格'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '总价值',\n      dataIndex: 'totalValue',\n      key: 'totalValue',\n      width: 100,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      fixed: 'right' as const,\n      render: (_: any, record: BatchInfo) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewDetail(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"查看二维码\">\n            <Button\n              type=\"text\"\n              icon={<QrcodeOutlined />}\n              onClick={() => {\n                // TODO: 显示二维码\n                message.info('二维码功能开发中');\n              }}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  const renderStatistics = () => (\n    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"总批次数\"\n            value={statistics.totalBatches}\n            prefix={<BarChartOutlined />}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"活跃批次\"\n            value={statistics.activeBatches}\n            valueStyle={{ color: '#3f8600' }}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"过期批次\"\n            value={statistics.expiredBatches}\n            valueStyle={{ color: '#cf1322' }}\n            prefix={<AlertOutlined />}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"低库存批次\"\n            value={statistics.lowStockBatches}\n            valueStyle={{ color: '#fa8c16' }}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"总价值\"\n            value={statistics.totalValue}\n            formatter={(value) => formatCurrency(Number(value))}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"平均库龄\"\n            value={statistics.averageAge}\n            suffix=\"天\"\n          />\n        </Card>\n      </Col>\n    </Row>\n  );\n\n  const renderBatchDetail = () => {\n    if (!selectedBatch) return null;\n\n    return (\n      <Modal\n        title={`批次详情 - ${selectedBatch.batchNumber}`}\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={800}\n      >\n        <Tabs defaultActiveKey=\"info\">\n          <TabPane tab=\"基本信息\" key=\"info\">\n            <Descriptions column={2} bordered size=\"small\">\n              <Descriptions.Item label=\"批次号\">{selectedBatch.batchNumber}</Descriptions.Item>\n              <Descriptions.Item label=\"二维码\">{selectedBatch.qrCode}</Descriptions.Item>\n              <Descriptions.Item label=\"物料编码\">{selectedBatch.materialCode}</Descriptions.Item>\n              <Descriptions.Item label=\"物料名称\">{selectedBatch.materialName}</Descriptions.Item>\n              <Descriptions.Item label=\"规格\">{selectedBatch.specification}</Descriptions.Item>\n              <Descriptions.Item label=\"供应商\">{selectedBatch.supplier}</Descriptions.Item>\n              <Descriptions.Item label=\"收货日期\">{formatDate(selectedBatch.receivedDate)}</Descriptions.Item>\n              <Descriptions.Item label=\"有效期\">\n                {selectedBatch.expiryDate ? formatDate(selectedBatch.expiryDate) : '-'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"原始数量\">\n                {selectedBatch.originalQuantity} {selectedBatch.unit}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"当前数量\">\n                {selectedBatch.currentQuantity} {selectedBatch.unit}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"可用数量\">\n                {selectedBatch.availableQuantity} {selectedBatch.unit}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"预留数量\">\n                {selectedBatch.reservedQuantity} {selectedBatch.unit}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"单价\">{formatCurrency(selectedBatch.unitPrice)}</Descriptions.Item>\n              <Descriptions.Item label=\"总价值\">{formatCurrency(selectedBatch.totalValue)}</Descriptions.Item>\n              <Descriptions.Item label=\"库位\">{selectedBatch.location}</Descriptions.Item>\n              <Descriptions.Item label=\"状态\">\n                <Space>\n                  <Tag color={getStatusColor(selectedBatch.status)}>\n                    {selectedBatch.status === 'AVAILABLE' ? '可用' :\n                     selectedBatch.status === 'RESERVED' ? '预留' :\n                     selectedBatch.status === 'EXPIRED' ? '过期' :\n                     selectedBatch.status === 'DAMAGED' ? '损坏' : '已消耗'}\n                  </Tag>\n                  <Tag color={getQualityStatusColor(selectedBatch.qualityStatus)}>\n                    {selectedBatch.qualityStatus === 'PASSED' ? '合格' :\n                     selectedBatch.qualityStatus === 'PENDING' ? '待检' : '不合格'}\n                  </Tag>\n                </Space>\n              </Descriptions.Item>\n            </Descriptions>\n          </TabPane>\n          <TabPane tab=\"交易记录\" key=\"transactions\">\n            <Timeline>\n              {selectedBatch.transactions.map((transaction) => (\n                <Timeline.Item\n                  key={transaction.id}\n                  color={getTransactionTypeColor(transaction.type)}\n                >\n                  <div>\n                    <Space>\n                      <Tag color={getTransactionTypeColor(transaction.type)}>\n                        {getTransactionTypeText(transaction.type)}\n                      </Tag>\n                      <Text strong>\n                        {transaction.quantity > 0 ? '+' : ''}{transaction.quantity} {selectedBatch.unit}\n                      </Text>\n                      <Text type=\"secondary\">\n                        余量: {transaction.remainingQuantity} {selectedBatch.unit}\n                      </Text>\n                    </Space>\n                    <div style={{ marginTop: 4 }}>\n                      <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                        {formatDate(transaction.timestamp)} | {transaction.operator} | {transaction.department}\n                      </Text>\n                    </div>\n                    <div style={{ marginTop: 2 }}>\n                      <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                        参考: {transaction.reference}\n                        {transaction.workOrder && ` | 工单: ${transaction.workOrder}`}\n                        {transaction.reason && ` | 原因: ${transaction.reason}`}\n                      </Text>\n                    </div>\n                    <div style={{ marginTop: 2 }}>\n                      <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                        库位: {transaction.location}\n                      </Text>\n                    </div>\n                  </div>\n                </Timeline.Item>\n              ))}\n            </Timeline>\n          </TabPane>\n        </Tabs>\n      </Modal>\n    );\n  };\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              批次跟踪\n            </Title>\n            <Text type=\"secondary\">\n              全面跟踪物料批次的入库、出库、调整等全生命周期记录\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索批次号、物料、供应商\"\n                allowClear\n                style={{ width: 250 }}\n                onSearch={setSearchKeyword}\n              />\n              <Select\n                placeholder=\"状态\"\n                allowClear\n                style={{ width: 100 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '可用', value: 'AVAILABLE' },\n                  { label: '预留', value: 'RESERVED' },\n                  { label: '过期', value: 'EXPIRED' },\n                  { label: '损坏', value: 'DAMAGED' },\n                  { label: '已消耗', value: 'CONSUMED' },\n                ]}\n              />\n              <Select\n                placeholder=\"供应商\"\n                allowClear\n                style={{ width: 120 }}\n                value={supplierFilter}\n                onChange={setSupplierFilter}\n                options={[\n                  { label: '深圳射频科技', value: '深圳射频科技' },\n                  { label: '苏州电路板厂', value: '苏州电路板厂' },\n                  { label: '村田制作所', value: '村田制作所' },\n                ]}\n              />\n              <RangePicker\n                placeholder={['开始日期', '结束日期']}\n                value={dateRange}\n                onChange={handleDateRangeChange}\n                style={{ width: 240 }}\n              />\n              <Button icon={<FilterOutlined />}>\n                高级筛选\n              </Button>\n              <Button icon={<ExportOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Alert\n          message=\"批次跟踪提示\"\n          description=\"系统自动记录每个批次的完整生命周期，包括入库、出库、调整、转移等所有操作，确保物料的完全可追溯性。\"\n          type=\"info\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab={<span><BarChartOutlined />统计概览</span>} key=\"overview\">\n            {renderStatistics()}\n            <Row gutter={[16, 16]}>\n              <Col xs={24} md={12}>\n                <Card title=\"批次状态分布\" size=\"small\">\n                  <Text type=\"secondary\">批次状态分布图表将在此显示</Text>\n                </Card>\n              </Col>\n              <Col xs={24} md={12}>\n                <Card title=\"库龄分析\" size=\"small\">\n                  <Text type=\"secondary\">库龄分析图表将在此显示</Text>\n                </Card>\n              </Col>\n            </Row>\n          </TabPane>\n\n          <TabPane tab=\"批次清单\" key=\"list\">\n            {renderStatistics()}\n            <Table\n              columns={batchColumns}\n              dataSource={filteredBatches}\n              loading={loading}\n              rowKey=\"id\"\n              scroll={{ x: 1400 }}\n              pagination={{\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) =>\n                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,\n              }}\n            />\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {renderBatchDetail()}\n    </div>\n  );\n};\n\nexport default BatchTrackingPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,KAAK,CAELC,GAAG,CACHC,GAAG,CACHC,UAAU,CACVC,GAAG,CACHC,OAAO,CACPC,OAAO,CACPC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,KAAK,CACLC,SAAS,CACTC,QAAQ,KACH,MAAM,CACb,OAEEC,WAAW,CAEXC,cAAc,CACdC,cAAc,CACdC,cAAc,CACdC,gBAAgB,CAChBC,aAAa,KACR,mBAAmB,CAC1B,OAASC,cAAc,CAAEC,UAAU,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhE,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGvB,UAAU,CAClC,KAAM,CAAEwB,MAAO,CAAC,CAAG9B,KAAK,CACxB,KAAM,CAAE+B,OAAQ,CAAC,CAAGrB,IAAI,CACxB,KAAM,CAAEsB,WAAY,CAAC,CAAG9B,UAAU,CAkDlC,KAAM,CAAA+B,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC0C,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAc,EAAE,CAAC,CACvD,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAkB,CAC5D8C,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,eAAe,CAAE,CAAC,CAClBC,UAAU,CAAE,CAAC,CACbC,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,CAChB,CAAC,CAAC,CACF,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACuD,YAAY,CAAEC,eAAe,CAAC,CAAGxD,QAAQ,CAAqB,CAAC,CACtE,KAAM,CAACyD,cAAc,CAAEC,iBAAiB,CAAC,CAAG1D,QAAQ,CAAqB,CAAC,CAC1E,KAAM,CAAC2D,SAAS,CAAEC,YAAY,CAAC,CAAG5D,QAAQ,CAAoB,IAAI,CAAC,CAEnE,KAAM,CAAA6D,qBAAqB,CAAGA,CAACC,KAAU,CAAEC,WAA6B,GAAK,CAC3EH,YAAY,CAACE,KAAK,CAAC,CACrB,CAAC,CACD,KAAM,CAACE,aAAa,CAAEC,gBAAgB,CAAC,CAAGjE,QAAQ,CAAmB,IAAI,CAAC,CAC1E,KAAM,CAACkE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACoE,SAAS,CAAEC,YAAY,CAAC,CAAGrE,QAAQ,CAAC,MAAM,CAAC,CAElD;AACA,KAAM,CAAAsE,WAAwB,CAAG,CAC/B,CACEC,EAAE,CAAE,GAAG,CACPC,WAAW,CAAE,aAAa,CAC1BC,UAAU,CAAE,MAAM,CAClBC,YAAY,CAAE,WAAW,CACzBC,YAAY,CAAE,SAAS,CACvBC,aAAa,CAAE,YAAY,CAC3BC,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,sBAAsB,CACpCC,UAAU,CAAE,sBAAsB,CAClCC,gBAAgB,CAAE,GAAG,CACrBC,eAAe,CAAE,EAAE,CACnBC,gBAAgB,CAAE,EAAE,CACpBC,iBAAiB,CAAE,EAAE,CACrBC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,MAAM,CACjBnC,UAAU,CAAE,QAAQ,CACpBoC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,WAAW,CACnBC,aAAa,CAAE,QAAQ,CACvBC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,CACZ,CACEnB,EAAE,CAAE,MAAM,CACVoB,IAAI,CAAE,SAAS,CACfC,QAAQ,CAAE,GAAG,CACbC,iBAAiB,CAAE,GAAG,CACtBC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAAK,CACjBC,SAAS,CAAE,aAAa,CACxBC,SAAS,CAAE,sBAAsB,CACjCX,QAAQ,CAAE,UACZ,CAAC,CACD,CACEf,EAAE,CAAE,MAAM,CACVoB,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,CAAC,EAAE,CACbC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAAK,CACjBG,SAAS,CAAE,aAAa,CACxBF,SAAS,CAAE,cAAc,CACzBC,SAAS,CAAE,sBAAsB,CACjCX,QAAQ,CAAE,UACZ,CAAC,CAEL,CAAC,CACD,CACEf,EAAE,CAAE,GAAG,CACPC,WAAW,CAAE,aAAa,CAC1BC,UAAU,CAAE,MAAM,CAClBC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,QAAQ,CACtBC,aAAa,CAAE,SAAS,CACxBC,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,sBAAsB,CACpCC,UAAU,CAAE,sBAAsB,CAClCC,gBAAgB,CAAE,GAAG,CACrBC,eAAe,CAAE,GAAG,CACpBC,gBAAgB,CAAE,EAAE,CACpBC,iBAAiB,CAAE,GAAG,CACtBC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,KAAK,CAChBnC,UAAU,CAAE,QAAQ,CACpBoC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,WAAW,CACnBC,aAAa,CAAE,QAAQ,CACvBC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,CACZ,CACEnB,EAAE,CAAE,MAAM,CACVoB,IAAI,CAAE,SAAS,CACfC,QAAQ,CAAE,GAAG,CACbC,iBAAiB,CAAE,GAAG,CACtBC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAAK,CACjBC,SAAS,CAAE,aAAa,CACxBC,SAAS,CAAE,sBAAsB,CACjCX,QAAQ,CAAE,UACZ,CAAC,CACD,CACEf,EAAE,CAAE,MAAM,CACVoB,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,CAAC,EAAE,CACbC,iBAAiB,CAAE,GAAG,CACtBC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAAK,CACjBG,SAAS,CAAE,aAAa,CACxBF,SAAS,CAAE,cAAc,CACzBC,SAAS,CAAE,sBAAsB,CACjCX,QAAQ,CAAE,UACZ,CAAC,CAEL,CAAC,CACD,CACEf,EAAE,CAAE,GAAG,CACPC,WAAW,CAAE,aAAa,CAC1BC,UAAU,CAAE,MAAM,CAClBC,YAAY,CAAE,aAAa,CAC3BC,YAAY,CAAE,MAAM,CACpBC,aAAa,CAAE,WAAW,CAC1BC,QAAQ,CAAE,OAAO,CACjBC,YAAY,CAAE,sBAAsB,CACpCC,UAAU,CAAE,sBAAsB,CAClCC,gBAAgB,CAAE,IAAI,CACtBC,eAAe,CAAE,GAAG,CACpBC,gBAAgB,CAAE,CAAC,CACnBC,iBAAiB,CAAE,GAAG,CACtBC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,IAAI,CACfnC,UAAU,CAAE,MAAM,CAClBoC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,SAAS,CACjBC,aAAa,CAAE,SAAS,CACxBC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,CACZ,CACEnB,EAAE,CAAE,MAAM,CACVoB,IAAI,CAAE,SAAS,CACfC,QAAQ,CAAE,IAAI,CACdC,iBAAiB,CAAE,IAAI,CACvBC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAAK,CACjBC,SAAS,CAAE,aAAa,CACxBC,SAAS,CAAE,sBAAsB,CACjCX,QAAQ,CAAE,UACZ,CAAC,CACD,CACEf,EAAE,CAAE,MAAM,CACVoB,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,CAAC,IAAI,CACfC,iBAAiB,CAAE,GAAG,CACtBC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAAK,CACjBG,SAAS,CAAE,aAAa,CACxBF,SAAS,CAAE,cAAc,CACzBC,SAAS,CAAE,sBAAsB,CACjCX,QAAQ,CAAE,UACZ,CAAC,CAEL,CAAC,CACF,CAEDrF,SAAS,CAAC,IAAM,CACdkG,aAAa,CAAC,CAAC,CACfC,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF1D,UAAU,CAAC,IAAI,CAAC,CAChB;AACAE,UAAU,CAAC2B,WAAW,CAAC,CACzB,CAAE,MAAO+B,KAAK,CAAE,CACdtF,OAAO,CAACsF,KAAK,CAAC,UAAU,CAAC,CAC3B,CAAC,OAAS,CACR5D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA2D,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF;AACA,KAAM,CAAAE,KAAsB,CAAG,CAC7BxD,YAAY,CAAEwB,WAAW,CAACiC,MAAM,CAChCxD,aAAa,CAAEuB,WAAW,CAACkC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAClB,MAAM,GAAK,WAAW,CAAC,CAACgB,MAAM,CACvEvD,cAAc,CAAEsB,WAAW,CAACkC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAClB,MAAM,GAAK,SAAS,CAAC,CAACgB,MAAM,CACtEtD,eAAe,CAAEqB,WAAW,CAACkC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACxB,eAAe,CAAGwB,CAAC,CAACzB,gBAAgB,CAAG,GAAG,CAAC,CAACuB,MAAM,CAC7FrD,UAAU,CAAEoB,WAAW,CAACoC,MAAM,CAAC,CAACC,GAAG,CAAEF,CAAC,GAAKE,GAAG,CAAGF,CAAC,CAACvD,UAAU,CAAE,CAAC,CAAC,CACjEC,UAAU,CAAE,EAAE,CACdC,YAAY,CAAE,GAChB,CAAC,CACDP,aAAa,CAACyD,KAAK,CAAC,CACtB,CAAE,MAAOD,KAAK,CAAE,CACdtF,OAAO,CAACsF,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAC,CAED,KAAM,CAAAO,gBAAgB,CAAIC,KAAgB,EAAK,CAC7C5C,gBAAgB,CAAC4C,KAAK,CAAC,CACvB1C,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA2C,YAAY,CAAGA,CAAA,GAAM,CACzB;AACA/F,OAAO,CAACgG,OAAO,CAAC,SAAS,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIzB,MAAc,EAAK,CACzC,KAAM,CAAA0B,MAAM,CAAG,CACbC,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,MAAM,CAChBC,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,QAAQ,CACjBC,QAAQ,CAAE,MACZ,CAAC,CACD,MAAO,CAAAL,MAAM,CAAC1B,MAAM,CAAwB,EAAI,SAAS,CAC3D,CAAC,CAED,KAAM,CAAAgC,qBAAqB,CAAIhC,MAAc,EAAK,CAChD,KAAM,CAAA0B,MAAM,CAAG,CACbO,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,QAAQ,CACjBC,MAAM,CAAE,KACV,CAAC,CACD,MAAO,CAAAT,MAAM,CAAC1B,MAAM,CAAwB,EAAI,SAAS,CAC3D,CAAC,CAED,KAAM,CAAAoC,sBAAsB,CAAIhC,IAAY,EAAK,CAC/C,KAAM,CAAAiC,KAAK,CAAG,CACZC,OAAO,CAAE,IAAI,CACbC,KAAK,CAAE,IAAI,CACXC,MAAM,CAAE,IAAI,CACZC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,IACV,CAAC,CACD,MAAO,CAAAL,KAAK,CAACjC,IAAI,CAAuB,EAAIA,IAAI,CAClD,CAAC,CAED,KAAM,CAAAuC,uBAAuB,CAAIvC,IAAY,EAAK,CAChD,KAAM,CAAAsB,MAAM,CAAG,CACbY,OAAO,CAAE,OAAO,CAChBC,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,MAAM,CAChBC,MAAM,CAAE,QACV,CAAC,CACD,MAAO,CAAAhB,MAAM,CAACtB,IAAI,CAAwB,EAAI,SAAS,CACzD,CAAC,CAED,KAAM,CAAAwC,eAAe,CAAGzF,OAAO,CAAC8D,MAAM,CAACK,KAAK,EAAI,CAC9C,GAAIxD,aAAa,EAAI,EACnBwD,KAAK,CAACrC,WAAW,CAAC4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,aAAa,CAAC+E,WAAW,CAAC,CAAC,CAAC,EACrEvB,KAAK,CAACnC,YAAY,CAAC0D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,aAAa,CAAC+E,WAAW,CAAC,CAAC,CAAC,EACtEvB,KAAK,CAAClC,YAAY,CAACyD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,aAAa,CAAC+E,WAAW,CAAC,CAAC,CAAC,EACtEvB,KAAK,CAAChC,QAAQ,CAACuD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,aAAa,CAAC+E,WAAW,CAAC,CAAC,CAAC,CACnE,CAAE,CACD,MAAO,MAAK,CACd,CACA,GAAI7E,YAAY,EAAIsD,KAAK,CAACtB,MAAM,GAAKhC,YAAY,CAAE,CACjD,MAAO,MAAK,CACd,CACA,GAAIE,cAAc,EAAIoD,KAAK,CAAChC,QAAQ,GAAKpB,cAAc,CAAE,CACvD,MAAO,MAAK,CACd,CACA,GAAIE,SAAS,EAAIA,SAAS,CAAC,CAAC,CAAC,EAAIA,SAAS,CAAC,CAAC,CAAC,CAAE,CAC7C,KAAM,CAAAmB,YAAY,CAAG,GAAI,CAAAwD,IAAI,CAACzB,KAAK,CAAC/B,YAAY,CAAC,CACjD,GAAIA,YAAY,CAAGnB,SAAS,CAAC,CAAC,CAAC,CAAC4E,MAAM,CAAC,CAAC,EAAIzD,YAAY,CAAGnB,SAAS,CAAC,CAAC,CAAC,CAAC4E,MAAM,CAAC,CAAC,CAAE,CAChF,MAAO,MAAK,CACd,CACF,CACA,MAAO,KAAI,CACb,CAAC,CAAC,CAEF,KAAM,CAAAC,YAAY,CAAG,CACnB,CACEC,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,MAAe,CACtBC,MAAM,CAAEA,CAACC,IAAY,CAAEC,MAAiB,gBACtC/G,KAAA,CAAC5B,KAAK,EAAC4I,SAAS,CAAC,UAAU,CAACC,IAAI,CAAE,CAAE,CAAAC,QAAA,eAClCpH,IAAA,CAACI,IAAI,EAACiH,MAAM,MAAAD,QAAA,CAAEJ,IAAI,CAAO,CAAC,cAC1BhH,IAAA,CAACI,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAC0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAH,QAAA,CAChDH,MAAM,CAACvD,MAAM,CACV,CAAC,EACF,CAEX,CAAC,CACD,CACEgD,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACS,CAAM,CAAEP,MAAiB,gBAChC/G,KAAA,CAAC5B,KAAK,EAAC4I,SAAS,CAAC,UAAU,CAACC,IAAI,CAAE,CAAE,CAAAC,QAAA,eAClCpH,IAAA,CAACI,IAAI,EAACiH,MAAM,MAAAD,QAAA,CAAEH,MAAM,CAACtE,YAAY,CAAO,CAAC,cACzC3C,IAAA,CAACI,IAAI,EAAAgH,QAAA,CAAEH,MAAM,CAACrE,YAAY,CAAO,CAAC,cAClC5C,IAAA,CAACI,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAC0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAH,QAAA,CAChDH,MAAM,CAACpE,aAAa,CACjB,CAAC,EACF,CAEX,CAAC,CACD,CACE6D,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGU,IAAY,EAAK3H,UAAU,CAAC2H,IAAI,CAC3C,CAAC,CACD,CACEf,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGU,IAAY,EAAKA,IAAI,CAAG3H,UAAU,CAAC2H,IAAI,CAAC,CAAG,GACtD,CAAC,CACD,CACEf,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACS,CAAM,CAAEP,MAAiB,gBAChC/G,KAAA,CAAC5B,KAAK,EAAC4I,SAAS,CAAC,UAAU,CAACC,IAAI,CAAE,CAAE,CAAAC,QAAA,eAClClH,KAAA,CAACE,IAAI,EAAAgH,QAAA,EAAC,gBAAI,CAACH,MAAM,CAAC/D,eAAe,CAAC,GAAC,CAAC+D,MAAM,CAAC5D,IAAI,EAAO,CAAC,cACvDnD,KAAA,CAACE,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAC0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAH,QAAA,EAAC,gBAC9C,CAACH,MAAM,CAAC7D,iBAAiB,CAAC,mBAAO,CAAC6D,MAAM,CAAC9D,gBAAgB,EACzD,CAAC,EACF,CAEX,CAAC,CACD,CACEuD,KAAK,CAAE,KAAK,CACZE,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACS,CAAM,CAAEP,MAAiB,GAAK,CACrC,KAAM,CAAAS,IAAI,CAAIT,MAAM,CAAC/D,eAAe,CAAG+D,MAAM,CAAChE,gBAAgB,CAAI,GAAG,CACrE,mBACEjD,IAAA,CAACV,QAAQ,EACPqI,OAAO,CAAED,IAAK,CACdP,IAAI,CAAC,OAAO,CACZ3D,MAAM,CAAEkE,IAAI,CAAG,EAAE,CAAG,WAAW,CAAGA,IAAI,CAAG,EAAE,CAAG,QAAQ,CAAG,SAAU,CACnEE,QAAQ,CAAE,KAAM,CACjB,CAAC,CAEN,CACF,CAAC,CACD,CACElB,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACS,CAAM,CAAEP,MAAiB,gBAChC/G,KAAA,CAAC5B,KAAK,EAAC4I,SAAS,CAAC,UAAU,CAACC,IAAI,CAAE,CAAE,CAAAC,QAAA,eAClCpH,IAAA,CAAClB,GAAG,EAAC+I,KAAK,CAAE5C,cAAc,CAACgC,MAAM,CAACzD,MAAM,CAAE,CAAA4D,QAAA,CACvCH,MAAM,CAACzD,MAAM,GAAK,WAAW,CAAG,IAAI,CACpCyD,MAAM,CAACzD,MAAM,GAAK,UAAU,CAAG,IAAI,CACnCyD,MAAM,CAACzD,MAAM,GAAK,SAAS,CAAG,IAAI,CAClCyD,MAAM,CAACzD,MAAM,GAAK,SAAS,CAAG,IAAI,CAAG,KAAK,CACxC,CAAC,cACNxD,IAAA,CAAClB,GAAG,EAAC+I,KAAK,CAAErC,qBAAqB,CAACyB,MAAM,CAACxD,aAAa,CAAE,CAAA2D,QAAA,CACrDH,MAAM,CAACxD,aAAa,GAAK,QAAQ,CAAG,IAAI,CACxCwD,MAAM,CAACxD,aAAa,GAAK,SAAS,CAAG,IAAI,CAAG,KAAK,CAC/C,CAAC,EACD,CAEX,CAAC,CACD,CACEiD,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGe,KAAa,EAAKjI,cAAc,CAACiI,KAAK,CACjD,CAAC,CACD,CACEpB,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,OAAgB,CACvBC,MAAM,CAAEA,CAACS,CAAM,CAAEP,MAAiB,gBAChC/G,KAAA,CAAC5B,KAAK,EAAC6I,IAAI,CAAC,OAAO,CAAAC,QAAA,eACjBpH,IAAA,CAACjB,OAAO,EAAC2H,KAAK,CAAC,0BAAM,CAAAU,QAAA,cACnBpH,IAAA,CAAC3B,MAAM,EACLuF,IAAI,CAAC,MAAM,CACXmE,IAAI,cAAE/H,IAAA,CAACT,WAAW,GAAE,CAAE,CACtByI,OAAO,CAAEA,CAAA,GAAMnD,gBAAgB,CAACoC,MAAM,CAAE,CACzC,CAAC,CACK,CAAC,cACVjH,IAAA,CAACjB,OAAO,EAAC2H,KAAK,CAAC,gCAAO,CAAAU,QAAA,cACpBpH,IAAA,CAAC3B,MAAM,EACLuF,IAAI,CAAC,MAAM,CACXmE,IAAI,cAAE/H,IAAA,CAACR,cAAc,GAAE,CAAE,CACzBwI,OAAO,CAAEA,CAAA,GAAM,CACb;AACAhJ,OAAO,CAACiJ,IAAI,CAAC,UAAU,CAAC,CAC1B,CAAE,CACH,CAAC,CACK,CAAC,EACL,CAEX,CAAC,CACF,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,gBACvBhI,KAAA,CAACvB,GAAG,EAACwJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACb,KAAK,CAAE,CAAEc,YAAY,CAAE,EAAG,CAAE,CAAAhB,QAAA,eACjDpH,IAAA,CAACpB,GAAG,EAACyJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cAC/BpH,IAAA,CAAC7B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAC,QAAA,cAChBpH,IAAA,CAACX,SAAS,EACRqH,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAEjH,UAAU,CAACE,YAAa,CAC/B0H,MAAM,cAAEzI,IAAA,CAACL,gBAAgB,GAAE,CAAE,CAC9B,CAAC,CACE,CAAC,CACJ,CAAC,cACNK,IAAA,CAACpB,GAAG,EAACyJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cAC/BpH,IAAA,CAAC7B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAC,QAAA,cAChBpH,IAAA,CAACX,SAAS,EACRqH,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAEjH,UAAU,CAACG,aAAc,CAChC0H,UAAU,CAAE,CAAEb,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN7H,IAAA,CAACpB,GAAG,EAACyJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cAC/BpH,IAAA,CAAC7B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAC,QAAA,cAChBpH,IAAA,CAACX,SAAS,EACRqH,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAEjH,UAAU,CAACI,cAAe,CACjCyH,UAAU,CAAE,CAAEb,KAAK,CAAE,SAAU,CAAE,CACjCY,MAAM,cAAEzI,IAAA,CAACJ,aAAa,GAAE,CAAE,CAC3B,CAAC,CACE,CAAC,CACJ,CAAC,cACNI,IAAA,CAACpB,GAAG,EAACyJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cAC/BpH,IAAA,CAAC7B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAC,QAAA,cAChBpH,IAAA,CAACX,SAAS,EACRqH,KAAK,CAAC,gCAAO,CACboB,KAAK,CAAEjH,UAAU,CAACK,eAAgB,CAClCwH,UAAU,CAAE,CAAEb,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN7H,IAAA,CAACpB,GAAG,EAACyJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cAC/BpH,IAAA,CAAC7B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAC,QAAA,cAChBpH,IAAA,CAACX,SAAS,EACRqH,KAAK,CAAC,oBAAK,CACXoB,KAAK,CAAEjH,UAAU,CAACM,UAAW,CAC7BwH,SAAS,CAAGb,KAAK,EAAKjI,cAAc,CAAC+I,MAAM,CAACd,KAAK,CAAC,CAAE,CACrD,CAAC,CACE,CAAC,CACJ,CAAC,cACN9H,IAAA,CAACpB,GAAG,EAACyJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAApB,QAAA,cAC/BpH,IAAA,CAAC7B,IAAI,EAACgJ,IAAI,CAAC,OAAO,CAAAC,QAAA,cAChBpH,IAAA,CAACX,SAAS,EACRqH,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAEjH,UAAU,CAACO,UAAW,CAC7ByH,MAAM,CAAC,QAAG,CACX,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CACN,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAI,CAAC7G,aAAa,CAAE,MAAO,KAAI,CAE/B,mBACEjC,IAAA,CAACtB,KAAK,EACJgI,KAAK,+BAAAqC,MAAA,CAAY9G,aAAa,CAACQ,WAAW,CAAG,CAC7CuG,IAAI,CAAE7G,kBAAmB,CACzB8G,QAAQ,CAAEA,CAAA,GAAM7G,qBAAqB,CAAC,KAAK,CAAE,CAC7C8G,MAAM,CAAE,cACNlJ,IAAA,CAAC3B,MAAM,EAAa2J,OAAO,CAAEA,CAAA,GAAM5F,qBAAqB,CAAC,KAAK,CAAE,CAAAgF,QAAA,CAAC,cAEjE,EAFY,OAEJ,CAAC,CACT,CACFP,KAAK,CAAE,GAAI,CAAAO,QAAA,cAEXlH,KAAA,CAACjB,IAAI,EAACkK,gBAAgB,CAAC,MAAM,CAAA/B,QAAA,eAC3BpH,IAAA,CAACM,OAAO,EAAC8I,GAAG,CAAC,0BAAM,CAAAhC,QAAA,cACjBlH,KAAA,CAACf,YAAY,EAACkK,MAAM,CAAE,CAAE,CAACC,QAAQ,MAACnC,IAAI,CAAC,OAAO,CAAAC,QAAA,eAC5CpH,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAApC,QAAA,CAAEnF,aAAa,CAACQ,WAAW,CAAoB,CAAC,cAC9EzC,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAApC,QAAA,CAAEnF,aAAa,CAACyB,MAAM,CAAoB,CAAC,cACzE1D,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAApC,QAAA,CAAEnF,aAAa,CAACU,YAAY,CAAoB,CAAC,cAChF3C,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAApC,QAAA,CAAEnF,aAAa,CAACW,YAAY,CAAoB,CAAC,cAChF5C,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,cAAI,CAAApC,QAAA,CAAEnF,aAAa,CAACY,aAAa,CAAoB,CAAC,cAC/E7C,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAApC,QAAA,CAAEnF,aAAa,CAACa,QAAQ,CAAoB,CAAC,cAC3E9C,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAApC,QAAA,CAAEtH,UAAU,CAACmC,aAAa,CAACc,YAAY,CAAC,CAAoB,CAAC,cAC5F/C,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAApC,QAAA,CAC3BnF,aAAa,CAACe,UAAU,CAAGlD,UAAU,CAACmC,aAAa,CAACe,UAAU,CAAC,CAAG,GAAG,CACrD,CAAC,cACpB9C,KAAA,CAACf,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAApC,QAAA,EAC5BnF,aAAa,CAACgB,gBAAgB,CAAC,GAAC,CAAChB,aAAa,CAACoB,IAAI,EACnC,CAAC,cACpBnD,KAAA,CAACf,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAApC,QAAA,EAC5BnF,aAAa,CAACiB,eAAe,CAAC,GAAC,CAACjB,aAAa,CAACoB,IAAI,EAClC,CAAC,cACpBnD,KAAA,CAACf,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAApC,QAAA,EAC5BnF,aAAa,CAACmB,iBAAiB,CAAC,GAAC,CAACnB,aAAa,CAACoB,IAAI,EACpC,CAAC,cACpBnD,KAAA,CAACf,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAApC,QAAA,EAC5BnF,aAAa,CAACkB,gBAAgB,CAAC,GAAC,CAAClB,aAAa,CAACoB,IAAI,EACnC,CAAC,cACpBrD,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,cAAI,CAAApC,QAAA,CAAEvH,cAAc,CAACoC,aAAa,CAACqB,SAAS,CAAC,CAAoB,CAAC,cAC3FtD,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAApC,QAAA,CAAEvH,cAAc,CAACoC,aAAa,CAACd,UAAU,CAAC,CAAoB,CAAC,cAC7FnB,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,cAAI,CAAApC,QAAA,CAAEnF,aAAa,CAACsB,QAAQ,CAAoB,CAAC,cAC1EvD,IAAA,CAACb,YAAY,CAACoK,IAAI,EAACC,KAAK,CAAC,cAAI,CAAApC,QAAA,cAC3BlH,KAAA,CAAC5B,KAAK,EAAA8I,QAAA,eACJpH,IAAA,CAAClB,GAAG,EAAC+I,KAAK,CAAE5C,cAAc,CAAChD,aAAa,CAACuB,MAAM,CAAE,CAAA4D,QAAA,CAC9CnF,aAAa,CAACuB,MAAM,GAAK,WAAW,CAAG,IAAI,CAC3CvB,aAAa,CAACuB,MAAM,GAAK,UAAU,CAAG,IAAI,CAC1CvB,aAAa,CAACuB,MAAM,GAAK,SAAS,CAAG,IAAI,CACzCvB,aAAa,CAACuB,MAAM,GAAK,SAAS,CAAG,IAAI,CAAG,KAAK,CAC/C,CAAC,cACNxD,IAAA,CAAClB,GAAG,EAAC+I,KAAK,CAAErC,qBAAqB,CAACvD,aAAa,CAACwB,aAAa,CAAE,CAAA2D,QAAA,CAC5DnF,aAAa,CAACwB,aAAa,GAAK,QAAQ,CAAG,IAAI,CAC/CxB,aAAa,CAACwB,aAAa,GAAK,SAAS,CAAG,IAAI,CAAG,KAAK,CACtD,CAAC,EACD,CAAC,CACS,CAAC,EACR,CAAC,EAzCO,MA0Cf,CAAC,cACVzD,IAAA,CAACM,OAAO,EAAC8I,GAAG,CAAC,0BAAM,CAAAhC,QAAA,cACjBpH,IAAA,CAACd,QAAQ,EAAAkI,QAAA,CACNnF,aAAa,CAAC0B,YAAY,CAAC8F,GAAG,CAAEC,WAAW,eAC1C1J,IAAA,CAACd,QAAQ,CAACqK,IAAI,EAEZ1B,KAAK,CAAE1B,uBAAuB,CAACuD,WAAW,CAAC9F,IAAI,CAAE,CAAAwD,QAAA,cAEjDlH,KAAA,QAAAkH,QAAA,eACElH,KAAA,CAAC5B,KAAK,EAAA8I,QAAA,eACJpH,IAAA,CAAClB,GAAG,EAAC+I,KAAK,CAAE1B,uBAAuB,CAACuD,WAAW,CAAC9F,IAAI,CAAE,CAAAwD,QAAA,CACnDxB,sBAAsB,CAAC8D,WAAW,CAAC9F,IAAI,CAAC,CACtC,CAAC,cACN1D,KAAA,CAACE,IAAI,EAACiH,MAAM,MAAAD,QAAA,EACTsC,WAAW,CAAC7F,QAAQ,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAE6F,WAAW,CAAC7F,QAAQ,CAAC,GAAC,CAAC5B,aAAa,CAACoB,IAAI,EAC3E,CAAC,cACPnD,KAAA,CAACE,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAAwD,QAAA,EAAC,gBACjB,CAACsC,WAAW,CAAC5F,iBAAiB,CAAC,GAAC,CAAC7B,aAAa,CAACoB,IAAI,EACnD,CAAC,EACF,CAAC,cACRrD,IAAA,QAAKsH,KAAK,CAAE,CAAEqC,SAAS,CAAE,CAAE,CAAE,CAAAvC,QAAA,cAC3BlH,KAAA,CAACE,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAC0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAH,QAAA,EAChDtH,UAAU,CAAC4J,WAAW,CAACxF,SAAS,CAAC,CAAC,KAAG,CAACwF,WAAW,CAAC3F,QAAQ,CAAC,KAAG,CAAC2F,WAAW,CAAC1F,UAAU,EAClF,CAAC,CACJ,CAAC,cACNhE,IAAA,QAAKsH,KAAK,CAAE,CAAEqC,SAAS,CAAE,CAAE,CAAE,CAAAvC,QAAA,cAC3BlH,KAAA,CAACE,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAC0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAH,QAAA,EAAC,gBAC9C,CAACsC,WAAW,CAACzF,SAAS,CACzByF,WAAW,CAACvF,SAAS,sBAAA4E,MAAA,CAAcW,WAAW,CAACvF,SAAS,CAAE,CAC1DuF,WAAW,CAACE,MAAM,sBAAAb,MAAA,CAAcW,WAAW,CAACE,MAAM,CAAE,EACjD,CAAC,CACJ,CAAC,cACN5J,IAAA,QAAKsH,KAAK,CAAE,CAAEqC,SAAS,CAAE,CAAE,CAAE,CAAAvC,QAAA,cAC3BlH,KAAA,CAACE,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAC0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAH,QAAA,EAAC,gBAC9C,CAACsC,WAAW,CAACnG,QAAQ,EACrB,CAAC,CACJ,CAAC,EACH,CAAC,EAhCDmG,WAAW,CAAClH,EAiCJ,CAChB,CAAC,CACM,CAAC,EAvCW,cAwCf,CAAC,EACN,CAAC,CACF,CAAC,CAEZ,CAAC,CAED,mBACEtC,KAAA,QAAAkH,QAAA,eACElH,KAAA,CAAC/B,IAAI,EAAAiJ,QAAA,eACHlH,KAAA,CAACvB,GAAG,EAACkL,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACxC,KAAK,CAAE,CAAEc,YAAY,CAAE,EAAG,CAAE,CAAAhB,QAAA,eACtElH,KAAA,CAACtB,GAAG,EAAAwI,QAAA,eACFpH,IAAA,CAACG,KAAK,EAAC4J,KAAK,CAAE,CAAE,CAACzC,KAAK,CAAE,CAAE0C,MAAM,CAAE,CAAE,CAAE,CAAA5C,QAAA,CAAC,0BAEvC,CAAO,CAAC,cACRpH,IAAA,CAACI,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAAwD,QAAA,CAAC,wJAEvB,CAAM,CAAC,EACJ,CAAC,cACNpH,IAAA,CAACpB,GAAG,EAAAwI,QAAA,cACFlH,KAAA,CAAC5B,KAAK,EAAA8I,QAAA,eACJpH,IAAA,CAACK,MAAM,EACL4J,WAAW,CAAC,0EAAc,CAC1BC,UAAU,MACV5C,KAAK,CAAE,CAAET,KAAK,CAAE,GAAI,CAAE,CACtBsD,QAAQ,CAAE5I,gBAAiB,CAC5B,CAAC,cACFvB,IAAA,CAACxB,MAAM,EACLyL,WAAW,CAAC,cAAI,CAChBC,UAAU,MACV5C,KAAK,CAAE,CAAET,KAAK,CAAE,GAAI,CAAE,CACtBiB,KAAK,CAAEtG,YAAa,CACpB4I,QAAQ,CAAE3I,eAAgB,CAC1B4I,OAAO,CAAE,CACP,CAAEb,KAAK,CAAE,IAAI,CAAE1B,KAAK,CAAE,WAAY,CAAC,CACnC,CAAE0B,KAAK,CAAE,IAAI,CAAE1B,KAAK,CAAE,UAAW,CAAC,CAClC,CAAE0B,KAAK,CAAE,IAAI,CAAE1B,KAAK,CAAE,SAAU,CAAC,CACjC,CAAE0B,KAAK,CAAE,IAAI,CAAE1B,KAAK,CAAE,SAAU,CAAC,CACjC,CAAE0B,KAAK,CAAE,KAAK,CAAE1B,KAAK,CAAE,UAAW,CAAC,CACnC,CACH,CAAC,cACF9H,IAAA,CAACxB,MAAM,EACLyL,WAAW,CAAC,oBAAK,CACjBC,UAAU,MACV5C,KAAK,CAAE,CAAET,KAAK,CAAE,GAAI,CAAE,CACtBiB,KAAK,CAAEpG,cAAe,CACtB0I,QAAQ,CAAEzI,iBAAkB,CAC5B0I,OAAO,CAAE,CACP,CAAEb,KAAK,CAAE,QAAQ,CAAE1B,KAAK,CAAE,QAAS,CAAC,CACpC,CAAE0B,KAAK,CAAE,QAAQ,CAAE1B,KAAK,CAAE,QAAS,CAAC,CACpC,CAAE0B,KAAK,CAAE,OAAO,CAAE1B,KAAK,CAAE,OAAQ,CAAC,CAClC,CACH,CAAC,cACF9H,IAAA,CAACO,WAAW,EACV0J,WAAW,CAAE,CAAC,MAAM,CAAE,MAAM,CAAE,CAC9BnC,KAAK,CAAElG,SAAU,CACjBwI,QAAQ,CAAEtI,qBAAsB,CAChCwF,KAAK,CAAE,CAAET,KAAK,CAAE,GAAI,CAAE,CACvB,CAAC,cACF7G,IAAA,CAAC3B,MAAM,EAAC0J,IAAI,cAAE/H,IAAA,CAACN,cAAc,GAAE,CAAE,CAAA0H,QAAA,CAAC,0BAElC,CAAQ,CAAC,cACTpH,IAAA,CAAC3B,MAAM,EAAC0J,IAAI,cAAE/H,IAAA,CAACP,cAAc,GAAE,CAAE,CAACuI,OAAO,CAAEjD,YAAa,CAAAqC,QAAA,CAAC,cAEzD,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAENpH,IAAA,CAACZ,KAAK,EACJJ,OAAO,CAAC,sCAAQ,CAChBsL,WAAW,CAAC,wSAAmD,CAC/D1G,IAAI,CAAC,MAAM,CACX2G,QAAQ,MACRC,QAAQ,MACRlD,KAAK,CAAE,CAAEc,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEFlI,KAAA,CAACjB,IAAI,EAACwL,SAAS,CAAEpI,SAAU,CAAC+H,QAAQ,CAAE9H,YAAa,CAAA8E,QAAA,eACjDlH,KAAA,CAACI,OAAO,EAAC8I,GAAG,cAAElJ,KAAA,SAAAkH,QAAA,eAAMpH,IAAA,CAACL,gBAAgB,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAAyH,QAAA,EACjDc,gBAAgB,CAAC,CAAC,cACnBhI,KAAA,CAACvB,GAAG,EAACwJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAf,QAAA,eACpBpH,IAAA,CAACpB,GAAG,EAACyJ,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAAnB,QAAA,cAClBpH,IAAA,CAAC7B,IAAI,EAACuI,KAAK,CAAC,sCAAQ,CAACS,IAAI,CAAC,OAAO,CAAAC,QAAA,cAC/BpH,IAAA,CAACI,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAAwD,QAAA,CAAC,gFAAa,CAAM,CAAC,CACvC,CAAC,CACJ,CAAC,cACNpH,IAAA,CAACpB,GAAG,EAACyJ,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAAnB,QAAA,cAClBpH,IAAA,CAAC7B,IAAI,EAACuI,KAAK,CAAC,0BAAM,CAACS,IAAI,CAAC,OAAO,CAAAC,QAAA,cAC7BpH,IAAA,CAACI,IAAI,EAACwD,IAAI,CAAC,WAAW,CAAAwD,QAAA,CAAC,oEAAW,CAAM,CAAC,CACrC,CAAC,CACJ,CAAC,EACH,CAAC,GAbiD,UAchD,CAAC,cAEVlH,KAAA,CAACI,OAAO,EAAC8I,GAAG,CAAC,0BAAM,CAAAhC,QAAA,EAChBc,gBAAgB,CAAC,CAAC,cACnBlI,IAAA,CAAC5B,KAAK,EACJsM,OAAO,CAAEjE,YAAa,CACtBkE,UAAU,CAAEvE,eAAgB,CAC5B3F,OAAO,CAAEA,OAAQ,CACjBmK,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAArC,MAAA,CACjBqC,KAAK,CAAC,CAAC,CAAC,MAAArC,MAAA,CAAIqC,KAAK,CAAC,CAAC,CAAC,yBAAArC,MAAA,CAAQoC,KAAK,uBAC1C,CAAE,CACH,CAAC,GAdoB,MAef,CAAC,EACN,CAAC,EACH,CAAC,CAENrC,iBAAiB,CAAC,CAAC,EACjB,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}