{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\components\\\\ConfirmDialog.tsx\";\nimport React from 'react';\nimport { Modal } from 'antd';\nimport { ExclamationCircleOutlined, QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';\n\n/**\n * 确认对话框组件属性接口\n * 继承自 Ant Design Modal 组件，但排除了 onOk 和 onCancel 属性\n */\n\n/**\n * 确认对话框组件\n * 提供统一的确认操作界面，支持不同类型的确认场景\n * \n * @param type - 对话框类型，影响图标和按钮样式\n * @param onConfirm - 确认操作回调，支持异步\n * @param onCancel - 取消操作回调\n * @param confirmText - 确认按钮文本\n * @param cancelText - 取消按钮文本\n * @param confirmLoading - 确认按钮加载状态\n * @param title - 对话框标题\n * @param children - 对话框内容\n * @param props - 其他 Modal 属性\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmDialog = ({\n  type = 'confirm',\n  onConfirm,\n  onCancel,\n  confirmText = '确定',\n  cancelText = '取消',\n  confirmLoading = false,\n  title,\n  children,\n  ...props\n}) => {\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 16\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 16\n        }, this);\n      case 'confirm':\n      default:\n        return /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const handleOk = async () => {\n    if (onConfirm) {\n      await onConfirm();\n    }\n  };\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: 8\n      },\n      children: [getIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 9\n    }, this),\n    onOk: handleOk,\n    onCancel: handleCancel,\n    okText: confirmText,\n    cancelText: cancelText,\n    confirmLoading: confirmLoading,\n    okButtonProps: {\n      danger: type === 'warning' || type === 'error'\n    },\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n\n/**\n * 静态方法：快速调用确认对话框\n * 无需手动管理组件状态，直接调用即可显示确认对话框\n * \n * @param config - 配置对象\n * @param config.title - 对话框标题\n * @param config.content - 对话框内容\n * @param config.onConfirm - 确认回调函数\n * @param config.onCancel - 取消回调函数\n * @param config.type - 对话框类型\n * @returns Modal.confirm 实例\n * \n * @example\n * ```tsx\n * ConfirmDialog.confirm({\n *   title: '确认删除',\n *   content: '确定要删除这个项目吗？',\n *   type: 'warning',\n *   onConfirm: () => {\n *     // 执行删除操作\n *   }\n * });\n * ```\n */\n_c = ConfirmDialog;\nConfirmDialog.confirm = config => {\n  const {\n    confirm\n  } = Modal;\n  return confirm({\n    title: config.title,\n    content: config.content,\n    onOk: config.onConfirm,\n    onCancel: config.onCancel,\n    okButtonProps: {\n      danger: config.type === 'warning' || config.type === 'error'\n    },\n    icon: (() => {\n      switch (config.type) {\n        case 'warning':\n        case 'error':\n          return /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n            style: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 18\n          }, this);\n        case 'info':\n          return /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {\n            style: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 18\n          }, this);\n        case 'confirm':\n        default:\n          return /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {\n            style: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 18\n          }, this);\n      }\n    })()\n  });\n};\nexport default ConfirmDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfirmDialog\");", "map": {"version": 3, "names": ["React", "Modal", "ExclamationCircleOutlined", "QuestionCircleOutlined", "InfoCircleOutlined", "jsxDEV", "_jsxDEV", "ConfirmDialog", "type", "onConfirm", "onCancel", "confirmText", "cancelText", "confirmLoading", "title", "children", "props", "getIcon", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleOk", "handleCancel", "display", "alignItems", "gap", "onOk", "okText", "okButtonProps", "danger", "_c", "confirm", "config", "content", "icon", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/ConfirmDialog.tsx"], "sourcesContent": ["import React from 'react';\nimport { Modal, ModalProps } from 'antd';\nimport { ExclamationCircleOutlined, QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';\n\n/**\n * 确认对话框组件属性接口\n * 继承自 Ant Design Modal 组件，但排除了 onOk 和 onCancel 属性\n */\ninterface ConfirmDialogProps extends Omit<ModalProps, 'onOk' | 'onCancel'> {\n  /** 对话框类型，决定图标和按钮样式 */\n  type?: 'warning' | 'info' | 'confirm' | 'error';\n  /** 确认回调函数，支持异步操作 */\n  onConfirm?: () => void | Promise<void>;\n  /** 取消回调函数 */\n  onCancel?: () => void;\n  /** 确认按钮文本，默认为\"确定\" */\n  confirmText?: string;\n  /** 取消按钮文本，默认为\"取消\" */\n  cancelText?: string;\n  /** 是否显示确认按钮加载状态 */\n  confirmLoading?: boolean;\n}\n\n/**\n * 确认对话框组件\n * 提供统一的确认操作界面，支持不同类型的确认场景\n * \n * @param type - 对话框类型，影响图标和按钮样式\n * @param onConfirm - 确认操作回调，支持异步\n * @param onCancel - 取消操作回调\n * @param confirmText - 确认按钮文本\n * @param cancelText - 取消按钮文本\n * @param confirmLoading - 确认按钮加载状态\n * @param title - 对话框标题\n * @param children - 对话框内容\n * @param props - 其他 Modal 属性\n */\ninterface ConfirmDialogComponent extends React.FC<ConfirmDialogProps> {\n  confirm: (config: {\n    title: string;\n    content: React.ReactNode;\n    onConfirm?: () => void | Promise<void>;\n    onCancel?: () => void;\n    type?: 'warning' | 'info' | 'confirm' | 'error';\n  }) => void;\n}\n\nconst ConfirmDialog: ConfirmDialogComponent = ({\n  type = 'confirm',\n  onConfirm,\n  onCancel,\n  confirmText = '确定',\n  cancelText = '取消',\n  confirmLoading = false,\n  title,\n  children,\n  ...props\n}) => {\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n      case 'error':\n        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case 'info':\n        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;\n      case 'confirm':\n      default:\n        return <QuestionCircleOutlined style={{ color: '#faad14' }} />;\n    }\n  };\n\n  const handleOk = async () => {\n    if (onConfirm) {\n      await onConfirm();\n    }\n  };\n\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n          {getIcon()}\n          <span>{title}</span>\n        </div>\n      }\n      onOk={handleOk}\n      onCancel={handleCancel}\n      okText={confirmText}\n      cancelText={cancelText}\n      confirmLoading={confirmLoading}\n      okButtonProps={{\n        danger: type === 'warning' || type === 'error'\n      }}\n      {...props}\n    >\n      {children}\n    </Modal>\n  );\n};\n\n/**\n * 静态方法：快速调用确认对话框\n * 无需手动管理组件状态，直接调用即可显示确认对话框\n * \n * @param config - 配置对象\n * @param config.title - 对话框标题\n * @param config.content - 对话框内容\n * @param config.onConfirm - 确认回调函数\n * @param config.onCancel - 取消回调函数\n * @param config.type - 对话框类型\n * @returns Modal.confirm 实例\n * \n * @example\n * ```tsx\n * ConfirmDialog.confirm({\n *   title: '确认删除',\n *   content: '确定要删除这个项目吗？',\n *   type: 'warning',\n *   onConfirm: () => {\n *     // 执行删除操作\n *   }\n * });\n * ```\n */\n(ConfirmDialog as ConfirmDialogComponent).confirm = (config: {\n  title: string;\n  content: React.ReactNode;\n  onConfirm?: () => void | Promise<void>;\n  onCancel?: () => void;\n  type?: 'warning' | 'info' | 'confirm' | 'error';\n}) => {\n  const { confirm } = Modal;\n  \n  return confirm({\n    title: config.title,\n    content: config.content,\n    onOk: config.onConfirm,\n    onCancel: config.onCancel,\n    okButtonProps: {\n      danger: config.type === 'warning' || config.type === 'error'\n    },\n    icon: (() => {\n      switch (config.type) {\n        case 'warning':\n        case 'error':\n          return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;\n        case 'info':\n          return <InfoCircleOutlined style={{ color: '#1890ff' }} />;\n        case 'confirm':\n        default:\n          return <QuestionCircleOutlined style={{ color: '#faad14' }} />;\n      }\n    })()\n  });\n};\n\nexport default ConfirmDialog;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAoB,MAAM;AACxC,SAASC,yBAAyB,EAAEC,sBAAsB,EAAEC,kBAAkB,QAAQ,mBAAmB;;AAEzG;AACA;AACA;AACA;;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,SAAAC,MAAA,IAAAC,OAAA;AAwBA,MAAMC,aAAqC,GAAGA,CAAC;EAC7CC,IAAI,GAAG,SAAS;EAChBC,SAAS;EACTC,QAAQ;EACRC,WAAW,GAAG,IAAI;EAClBC,UAAU,GAAG,IAAI;EACjBC,cAAc,GAAG,KAAK;EACtBC,KAAK;EACLC,QAAQ;EACR,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQT,IAAI;MACV,KAAK,SAAS;MACd,KAAK,OAAO;QACV,oBAAOF,OAAA,CAACJ,yBAAyB;UAACgB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE,KAAK,MAAM;QACT,oBAAOjB,OAAA,CAACF,kBAAkB;UAACc,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,SAAS;MACd;QACE,oBAAOjB,OAAA,CAACH,sBAAsB;UAACe,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClE;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAIf,SAAS,EAAE;MACb,MAAMA,SAAS,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIf,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,oBACEJ,OAAA,CAACL,KAAK;IACJa,KAAK,eACHR,OAAA;MAAKY,KAAK,EAAE;QAAEQ,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAb,QAAA,GAC3DE,OAAO,CAAC,CAAC,eACVX,OAAA;QAAAS,QAAA,EAAOD;MAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN;IACDM,IAAI,EAAEL,QAAS;IACfd,QAAQ,EAAEe,YAAa;IACvBK,MAAM,EAAEnB,WAAY;IACpBC,UAAU,EAAEA,UAAW;IACvBC,cAAc,EAAEA,cAAe;IAC/BkB,aAAa,EAAE;MACbC,MAAM,EAAExB,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK;IACzC,CAAE;IAAA,GACEQ,KAAK;IAAAD,QAAA,EAERA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBAU,EAAA,GA3DM1B,aAAqC;AAmF1CA,aAAa,CAA4B2B,OAAO,GAAIC,MAMpD,IAAK;EACJ,MAAM;IAAED;EAAQ,CAAC,GAAGjC,KAAK;EAEzB,OAAOiC,OAAO,CAAC;IACbpB,KAAK,EAAEqB,MAAM,CAACrB,KAAK;IACnBsB,OAAO,EAAED,MAAM,CAACC,OAAO;IACvBP,IAAI,EAAEM,MAAM,CAAC1B,SAAS;IACtBC,QAAQ,EAAEyB,MAAM,CAACzB,QAAQ;IACzBqB,aAAa,EAAE;MACbC,MAAM,EAAEG,MAAM,CAAC3B,IAAI,KAAK,SAAS,IAAI2B,MAAM,CAAC3B,IAAI,KAAK;IACvD,CAAC;IACD6B,IAAI,EAAE,CAAC,MAAM;MACX,QAAQF,MAAM,CAAC3B,IAAI;QACjB,KAAK,SAAS;QACd,KAAK,OAAO;UACV,oBAAOF,OAAA,CAACJ,yBAAyB;YAACgB,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACnE,KAAK,MAAM;UACT,oBAAOjB,OAAA,CAACF,kBAAkB;YAACc,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAC5D,KAAK,SAAS;QACd;UACE,oBAAOjB,OAAA,CAACH,sBAAsB;YAACe,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;MAClE;IACF,CAAC,EAAE;EACL,CAAC,CAAC;AACJ,CAAC;AAED,eAAehB,aAAa;AAAC,IAAA0B,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}