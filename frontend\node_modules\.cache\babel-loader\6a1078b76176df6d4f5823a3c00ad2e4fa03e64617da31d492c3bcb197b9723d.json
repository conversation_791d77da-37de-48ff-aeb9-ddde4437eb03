{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useEffect,useState}from'react';import{useNavigate}from'react-router-dom';import{Table,Button,Space,Input,Select,Card,Tag,Popconfirm,message,Typography,Row,Col,Tooltip,Dropdown,Statistic,DatePicker,Badge,Avatar,Progress,Alert}from'antd';import{PlusOutlined,EditOutlined,DeleteOutlined,EyeOutlined,CopyOutlined,MoreOutlined,ExportOutlined,ReloadOutlined,CheckCircleOutlined,ClockCircleOutlined,ExclamationCircleOutlined,CloseCircleOutlined,UserOutlined,FileTextOutlined}from'@ant-design/icons';import{formatDate}from'../../utils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Search}=Input;const{RangePicker}=DatePicker;const{Option}=Select;const ECNListPage=()=>{const navigate=useNavigate();const[loading,setLoading]=useState(false);const[ecnList,setEcnList]=useState([]);const[searchKeyword,setSearchKeyword]=useState('');const[statusFilter,setStatusFilter]=useState('');const[priorityFilter,setPriorityFilter]=useState('');const[dateRange,setDateRange]=useState(null);const[selectedRowKeys,setSelectedRowKeys]=useState([]);const[pagination,setPagination]=useState({current:1,pageSize:10,total:0});useEffect(()=>{loadData();},[pagination.current,pagination.pageSize,searchKeyword,statusFilter,priorityFilter,dateRange]);const loadData=async()=>{try{setLoading(true);// TODO: 调用API获取ECN列表\n// 模拟数据\nconst mockData=[{id:'1',number:'ECN20241201001',title:'主板电容规格变更',description:'将主板上的电解电容从16V/1000uF更换为25V/1000uF，提高产品可靠性',reason:'客户反馈在高温环境下产品稳定性不足',implementationPlan:'1. 更新BOM清单\\n2. 通知供应商采购新规格电容\\n3. 更新生产工艺文件',priority:'HIGH',status:'REVIEW',initiator:'张工程师',createdAt:'2024-12-01 09:00:00',updatedAt:'2024-12-01 15:00:00',effectiveDate:'2024-12-15',affectedItems:[],approvers:[{userId:'U001',userName:'李质量经理',role:'质量经理',status:'APPROVED',reviewTime:'2024-12-01 14:30:00',comment:'从质量角度看，此变更有助于提升产品可靠性，同意此变更。'},{userId:'U002',userName:'王生产经理',role:'生产经理',status:'PENDING'}]},{id:'2',number:'ECN20241130002',title:'PCB板厚度调整',description:'PCB板厚度从1.6mm调整为2.0mm，增强机械强度',reason:'产品在运输过程中出现PCB弯曲现象',implementationPlan:'1. 联系PCB供应商确认新厚度可行性\\n2. 更新设计文件\\n3. 重新进行EMC测试',priority:'MEDIUM',status:'APPROVED',initiator:'李设计师',createdAt:'2024-11-30 14:00:00',updatedAt:'2024-12-01 10:00:00',effectiveDate:'2024-12-20',affectedItems:[],approvers:[{userId:'U001',userName:'李质量经理',role:'质量经理',status:'APPROVED',reviewTime:'2024-11-30 16:00:00',comment:'同意调整，建议进行充分测试验证。'},{userId:'U002',userName:'王生产经理',role:'生产经理',status:'APPROVED',reviewTime:'2024-12-01 09:30:00',comment:'生产工艺需要相应调整，但可以接受。'}]},{id:'3',number:'ECN20241129003',title:'连接器型号更换',description:'将USB连接器从Type-A更换为Type-C',reason:'市场需求变化，Type-C接口更受欢迎',implementationPlan:'1. 重新设计PCB布局\\n2. 更新外壳模具\\n3. 软件驱动适配',priority:'URGENT',status:'REJECTED',initiator:'赵产品经理',createdAt:'2024-11-29 10:00:00',updatedAt:'2024-11-30 16:00:00',effectiveDate:'2024-12-25',affectedItems:[],approvers:[{userId:'U001',userName:'李质量经理',role:'质量经理',status:'REJECTED',reviewTime:'2024-11-30 15:30:00',comment:'变更影响范围太大，建议在下一代产品中考虑。'}]}];setEcnList(mockData);setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{total:mockData.length}));}catch(error){message.error('加载ECN列表失败');}finally{setLoading(false);}};const handleSearch=value=>{setSearchKeyword(value);setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:1}));};const handleStatusFilter=value=>{setStatusFilter(value);setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:1}));};const handlePriorityFilter=value=>{setPriorityFilter(value);setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:1}));};const handleDateRangeChange=(dates,dateStrings)=>{setDateRange(dates);setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:1}));};const handleView=record=>{navigate(\"/ecn/review/\".concat(record.id));};const handleEdit=record=>{navigate(\"/ecn/edit/\".concat(record.id));};const handleCopy=record=>{// TODO: 实现复制功能\nmessage.info('复制功能开发中');};const handleDelete=async record=>{try{// TODO: 调用API删除ECN\nmessage.success('删除成功');loadData();}catch(error){message.error('删除失败');}};const handleBatchDelete=async()=>{if(selectedRowKeys.length===0){message.warning('请选择要删除的ECN');return;}try{// TODO: 调用API批量删除\nmessage.success(\"\\u6210\\u529F\\u5220\\u9664 \".concat(selectedRowKeys.length,\" \\u4E2AECN\"));setSelectedRowKeys([]);loadData();}catch(error){message.error('批量删除失败');}};const handleExport=()=>{// TODO: 实现导出功能\nmessage.info('导出功能开发中');};const getStatusConfig=status=>{const statusMap={'DRAFT':{text:'草稿',color:'default',icon:/*#__PURE__*/_jsx(FileTextOutlined,{})},'REVIEW':{text:'审核中',color:'processing',icon:/*#__PURE__*/_jsx(ClockCircleOutlined,{})},'APPROVED':{text:'已批准',color:'success',icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{})},'REJECTED':{text:'已拒绝',color:'error',icon:/*#__PURE__*/_jsx(CloseCircleOutlined,{})},'IMPLEMENTED':{text:'已实施',color:'success',icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{})},'CANCELLED':{text:'已取消',color:'default',icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{})}};return statusMap[status]||{text:status,color:'default',icon:null};};const getPriorityConfig=priority=>{const priorityMap={'LOW':{text:'低',color:'blue'},'MEDIUM':{text:'中',color:'orange'},'HIGH':{text:'高',color:'red'},'URGENT':{text:'紧急',color:'magenta'}};return priorityMap[priority]||{text:priority,color:'default'};};const getApprovalProgress=approvers=>{const total=approvers.length;const approved=approvers.filter(a=>a.status==='APPROVED').length;const rejected=approvers.filter(a=>a.status==='REJECTED').length;if(rejected>0){return{percent:100,status:'exception'};}const percent=total>0?approved/total*100:0;return{percent,status:percent===100?'success':'active'};};const columns=[{title:'ECN编号',dataIndex:'number',key:'number',width:150,render:(text,record)=>/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:()=>handleView(record),children:text})},{title:'标题',dataIndex:'title',key:'title',ellipsis:{showTitle:false},render:title=>/*#__PURE__*/_jsx(Tooltip,{placement:\"topLeft\",title:title,children:title})},{title:'优先级',dataIndex:'priority',key:'priority',width:100,render:priority=>{const config=getPriorityConfig(priority);return/*#__PURE__*/_jsx(Tag,{color:config.color,children:config.text});}},{title:'状态',dataIndex:'status',key:'status',width:120,render:status=>{const config=getStatusConfig(status);return/*#__PURE__*/_jsx(Badge,{status:config.color,text:/*#__PURE__*/_jsxs(Space,{children:[config.icon,config.text]})});}},{title:'发起人',dataIndex:'initiator',key:'initiator',width:120,render:initiator=>/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Avatar,{icon:/*#__PURE__*/_jsx(UserOutlined,{}),size:\"small\"}),initiator]})},{title:'审批进度',key:'approvalProgress',width:150,render:(_,record)=>{const progress=getApprovalProgress(record.approvers);return/*#__PURE__*/_jsx(Progress,{percent:progress.percent,status:progress.status,size:\"small\",format:percent=>\"\".concat(record.approvers.filter(a=>a.status==='APPROVED').length,\"/\").concat(record.approvers.length)});}},{title:'创建时间',dataIndex:'createdAt',key:'createdAt',width:150,render:date=>formatDate(date)},{title:'生效日期',dataIndex:'effectiveDate',key:'effectiveDate',width:120,render:date=>date?formatDate(date):'-'},{title:'操作',key:'action',width:200,fixed:'right',render:(_,record)=>{const items=[{key:'view',label:'查看详情',icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record)},{key:'edit',label:'编辑',icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record),disabled:record.status!=='DRAFT'},{key:'copy',label:'复制',icon:/*#__PURE__*/_jsx(CopyOutlined,{}),onClick:()=>handleCopy(record)},{type:'divider'},{key:'delete',label:'删除',icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),danger:true,disabled:record.status==='APPROVED'||record.status==='IMPLEMENTED'}];return/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Dropdown,{menu:{items},trigger:['click'],children:/*#__PURE__*/_jsx(Button,{size:\"small\",icon:/*#__PURE__*/_jsx(MoreOutlined,{})})}),(record.status==='DRAFT'||record.status==='REVIEW')&&/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2AECN\\u5417\\uFF1F\",onConfirm:()=>handleDelete(record),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Button,{size:\"small\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{})})})]});}}];const rowSelection={selectedRowKeys,onChange:newSelectedRowKeys=>{setSelectedRowKeys(newSelectedRowKeys);},getCheckboxProps:record=>({disabled:record.status==='APPROVED'||record.status==='IMPLEMENTED'})};// 统计数据\nconst statistics={total:ecnList.length,draft:ecnList.filter(item=>item.status==='DRAFT').length,review:ecnList.filter(item=>item.status==='REVIEW').length,approved:ecnList.filter(item=>item.status==='APPROVED').length,rejected:ecnList.filter(item=>item.status==='REJECTED').length};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u6570\",value:statistics.total,prefix:/*#__PURE__*/_jsx(FileTextOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8349\\u7A3F\",value:statistics.draft,valueStyle:{color:'#999'},prefix:/*#__PURE__*/_jsx(FileTextOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5BA1\\u6838\\u4E2D\",value:statistics.review,valueStyle:{color:'#1890ff'},prefix:/*#__PURE__*/_jsx(ClockCircleOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u6279\\u51C6\",value:statistics.approved,valueStyle:{color:'#52c41a'},prefix:/*#__PURE__*/_jsx(CheckCircleOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u62D2\\u7EDD\",value:statistics.rejected,valueStyle:{color:'#ff4d4f'},prefix:/*#__PURE__*/_jsx(CloseCircleOutlined,{})})})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:16},children:[/*#__PURE__*/_jsx(Title,{level:4,children:\"ECN\\u5217\\u8868\"}),/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:()=>navigate('/ecn/create'),children:\"\\u521B\\u5EFAECN\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:handleExport,children:\"\\u5BFC\\u51FA\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:loadData,children:\"\\u5237\\u65B0\"})]})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:8,lg:6,children:/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22ECN\\u7F16\\u53F7\\u3001\\u6807\\u9898\",allowClear:true,onSearch:handleSearch,style:{width:'100%'}})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:8,lg:6,children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u72B6\\u6001\\u7B5B\\u9009\",allowClear:true,style:{width:'100%'},value:statusFilter,onChange:handleStatusFilter,children:[/*#__PURE__*/_jsx(Option,{value:\"DRAFT\",children:\"\\u8349\\u7A3F\"}),/*#__PURE__*/_jsx(Option,{value:\"REVIEW\",children:\"\\u5BA1\\u6838\\u4E2D\"}),/*#__PURE__*/_jsx(Option,{value:\"APPROVED\",children:\"\\u5DF2\\u6279\\u51C6\"}),/*#__PURE__*/_jsx(Option,{value:\"REJECTED\",children:\"\\u5DF2\\u62D2\\u7EDD\"}),/*#__PURE__*/_jsx(Option,{value:\"IMPLEMENTED\",children:\"\\u5DF2\\u5B9E\\u65BD\"}),/*#__PURE__*/_jsx(Option,{value:\"CANCELLED\",children:\"\\u5DF2\\u53D6\\u6D88\"})]})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:8,lg:6,children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u4F18\\u5148\\u7EA7\\u7B5B\\u9009\",allowClear:true,style:{width:'100%'},value:priorityFilter,onChange:handlePriorityFilter,children:[/*#__PURE__*/_jsx(Option,{value:\"LOW\",children:\"\\u4F4E\"}),/*#__PURE__*/_jsx(Option,{value:\"MEDIUM\",children:\"\\u4E2D\"}),/*#__PURE__*/_jsx(Option,{value:\"HIGH\",children:\"\\u9AD8\"}),/*#__PURE__*/_jsx(Option,{value:\"URGENT\",children:\"\\u7D27\\u6025\"})]})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:8,lg:6,children:/*#__PURE__*/_jsx(RangePicker,{placeholder:['开始日期','结束日期'],style:{width:'100%'},value:dateRange,onChange:handleDateRangeChange})})]}),selectedRowKeys.length>0&&/*#__PURE__*/_jsx(Alert,{message:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u5DF2\\u9009\\u62E9 \",selectedRowKeys.length,\" \\u9879\"]}),/*#__PURE__*/_jsx(Button,{size:\"small\",onClick:()=>setSelectedRowKeys([]),children:\"\\u53D6\\u6D88\\u9009\\u62E9\"}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u9009\\u4E2D\\u7684 \".concat(selectedRowKeys.length,\" \\u4E2AECN\\u5417\\uFF1F\"),onConfirm:handleBatchDelete,okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Button,{size:\"small\",danger:true,children:\"\\u6279\\u91CF\\u5220\\u9664\"})})]}),type:\"info\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:ecnList,rowKey:\"id\",loading:loading,rowSelection:rowSelection,pagination:_objectSpread(_objectSpread({},pagination),{},{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\"),onChange:(page,pageSize)=>{setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:page,pageSize:pageSize||10}));}}),scroll:{x:1200}})]})]});};export default ECNListPage;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "Popconfirm", "message", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Statistic", "DatePicker", "Badge", "Avatar", "Progress", "<PERSON><PERSON>", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "MoreOutlined", "ExportOutlined", "ReloadOutlined", "CheckCircleOutlined", "ClockCircleOutlined", "ExclamationCircleOutlined", "CloseCircleOutlined", "UserOutlined", "FileTextOutlined", "formatDate", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Search", "RangePicker", "Option", "ECNListPage", "navigate", "loading", "setLoading", "ecnList", "setEcnList", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "priorityFilter", "setPriorityFilter", "date<PERSON><PERSON><PERSON>", "setDateRange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "pagination", "setPagination", "current", "pageSize", "total", "loadData", "mockData", "id", "number", "title", "description", "reason", "implementationPlan", "priority", "status", "initiator", "createdAt", "updatedAt", "effectiveDate", "affectedItems", "approvers", "userId", "userName", "role", "reviewTime", "comment", "prev", "_objectSpread", "length", "error", "handleSearch", "value", "handleStatusFilter", "handlePriorityFilter", "handleDateRangeChange", "dates", "dateStrings", "handleView", "record", "concat", "handleEdit", "handleCopy", "info", "handleDelete", "success", "handleBatchDelete", "warning", "handleExport", "getStatusConfig", "statusMap", "text", "color", "icon", "getPriorityConfig", "priorityMap", "getApprovalProgress", "approved", "filter", "a", "rejected", "percent", "columns", "dataIndex", "key", "width", "render", "type", "onClick", "children", "ellipsis", "showTitle", "placement", "config", "size", "_", "progress", "format", "date", "fixed", "items", "label", "disabled", "danger", "menu", "trigger", "onConfirm", "okText", "cancelText", "rowSelection", "onChange", "newSelectedRowKeys", "getCheckboxProps", "statistics", "draft", "item", "review", "gutter", "style", "marginBottom", "xs", "sm", "md", "lg", "prefix", "valueStyle", "display", "justifyContent", "alignItems", "level", "placeholder", "allowClear", "onSearch", "showIcon", "dataSource", "<PERSON><PERSON><PERSON>", "showSizeChanger", "showQuickJumper", "showTotal", "range", "page", "scroll", "x"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/ecn/ECNListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  Popconfirm,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Statistic,\n  DatePicker,\n  Badge,\n  Avatar,\n  Progress,\n  Alert,\n  Divider,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  FilterOutlined,\n  ReloadOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  ExclamationCircleOutlined,\n  CloseCircleOutlined,\n  UserOutlined,\n  CalendarOutlined,\n  FileTextOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { ECN } from '../../types';\nimport { formatDate } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { RangePicker } = DatePicker;\nconst { Option } = Select;\n\nconst ECNListPage: React.FC = () => {\n  const navigate = useNavigate();\n  \n  const [loading, setLoading] = useState(false);\n  const [ecnList, setEcnList] = useState<ECN[]>([]);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [priorityFilter, setPriorityFilter] = useState<string>('');\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0,\n  });\n\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter, priorityFilter, dateRange]);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      // TODO: 调用API获取ECN列表\n      // 模拟数据\n      const mockData: ECN[] = [\n        {\n          id: '1',\n          number: 'ECN20241201001',\n          title: '主板电容规格变更',\n          description: '将主板上的电解电容从16V/1000uF更换为25V/1000uF，提高产品可靠性',\n          reason: '客户反馈在高温环境下产品稳定性不足',\n          implementationPlan: '1. 更新BOM清单\\n2. 通知供应商采购新规格电容\\n3. 更新生产工艺文件',\n          priority: 'HIGH',\n          status: 'REVIEW',\n          initiator: '张工程师',\n          createdAt: '2024-12-01 09:00:00',\n          updatedAt: '2024-12-01 15:00:00',\n          effectiveDate: '2024-12-15',\n          affectedItems: [],\n          approvers: [\n            {\n              userId: 'U001',\n              userName: '李质量经理',\n              role: '质量经理',\n              status: 'APPROVED',\n              reviewTime: '2024-12-01 14:30:00',\n              comment: '从质量角度看，此变更有助于提升产品可靠性，同意此变更。'\n            },\n            {\n              userId: 'U002',\n              userName: '王生产经理',\n              role: '生产经理',\n              status: 'PENDING'\n            }\n          ]\n        },\n        {\n          id: '2',\n          number: 'ECN20241130002',\n          title: 'PCB板厚度调整',\n          description: 'PCB板厚度从1.6mm调整为2.0mm，增强机械强度',\n          reason: '产品在运输过程中出现PCB弯曲现象',\n          implementationPlan: '1. 联系PCB供应商确认新厚度可行性\\n2. 更新设计文件\\n3. 重新进行EMC测试',\n          priority: 'MEDIUM',\n          status: 'APPROVED',\n          initiator: '李设计师',\n          createdAt: '2024-11-30 14:00:00',\n          updatedAt: '2024-12-01 10:00:00',\n          effectiveDate: '2024-12-20',\n          affectedItems: [],\n          approvers: [\n            {\n              userId: 'U001',\n              userName: '李质量经理',\n              role: '质量经理',\n              status: 'APPROVED',\n              reviewTime: '2024-11-30 16:00:00',\n              comment: '同意调整，建议进行充分测试验证。'\n            },\n            {\n              userId: 'U002',\n              userName: '王生产经理',\n              role: '生产经理',\n              status: 'APPROVED',\n              reviewTime: '2024-12-01 09:30:00',\n              comment: '生产工艺需要相应调整，但可以接受。'\n            }\n          ]\n        },\n        {\n          id: '3',\n          number: 'ECN20241129003',\n          title: '连接器型号更换',\n          description: '将USB连接器从Type-A更换为Type-C',\n          reason: '市场需求变化，Type-C接口更受欢迎',\n          implementationPlan: '1. 重新设计PCB布局\\n2. 更新外壳模具\\n3. 软件驱动适配',\n          priority: 'URGENT',\n          status: 'REJECTED',\n          initiator: '赵产品经理',\n          createdAt: '2024-11-29 10:00:00',\n          updatedAt: '2024-11-30 16:00:00',\n          effectiveDate: '2024-12-25',\n          affectedItems: [],\n          approvers: [\n            {\n              userId: 'U001',\n              userName: '李质量经理',\n              role: '质量经理',\n              status: 'REJECTED',\n              reviewTime: '2024-11-30 15:30:00',\n              comment: '变更影响范围太大，建议在下一代产品中考虑。'\n            }\n          ]\n        }\n      ];\n      \n      setEcnList(mockData);\n      setPagination(prev => ({ ...prev, total: mockData.length }));\n    } catch (error) {\n      message.error('加载ECN列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchKeyword(value);\n    setPagination(prev => ({ ...prev, current: 1 }));\n  };\n\n  const handleStatusFilter = (value: string) => {\n    setStatusFilter(value);\n    setPagination(prev => ({ ...prev, current: 1 }));\n  };\n\n  const handlePriorityFilter = (value: string) => {\n    setPriorityFilter(value);\n    setPagination(prev => ({ ...prev, current: 1 }));\n  };\n\n  const handleDateRangeChange = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null, dateStrings: [string, string]) => {\n    setDateRange(dates);\n    setPagination(prev => ({ ...prev, current: 1 }));\n  };\n\n  const handleView = (record: ECN) => {\n    navigate(`/ecn/review/${record.id}`);\n  };\n\n  const handleEdit = (record: ECN) => {\n    navigate(`/ecn/edit/${record.id}`);\n  };\n\n  const handleCopy = (record: ECN) => {\n    // TODO: 实现复制功能\n    message.info('复制功能开发中');\n  };\n\n  const handleDelete = async (record: ECN) => {\n    try {\n      // TODO: 调用API删除ECN\n      message.success('删除成功');\n      loadData();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleBatchDelete = async () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning('请选择要删除的ECN');\n      return;\n    }\n    \n    try {\n      // TODO: 调用API批量删除\n      message.success(`成功删除 ${selectedRowKeys.length} 个ECN`);\n      setSelectedRowKeys([]);\n      loadData();\n    } catch (error) {\n      message.error('批量删除失败');\n    }\n  };\n\n  const handleExport = () => {\n    // TODO: 实现导出功能\n    message.info('导出功能开发中');\n  };\n\n  const getStatusConfig = (status: string) => {\n    const statusMap: Record<string, { text: string; color: string; icon: React.ReactNode }> = {\n      'DRAFT': { text: '草稿', color: 'default', icon: <FileTextOutlined /> },\n      'REVIEW': { text: '审核中', color: 'processing', icon: <ClockCircleOutlined /> },\n      'APPROVED': { text: '已批准', color: 'success', icon: <CheckCircleOutlined /> },\n      'REJECTED': { text: '已拒绝', color: 'error', icon: <CloseCircleOutlined /> },\n      'IMPLEMENTED': { text: '已实施', color: 'success', icon: <CheckCircleOutlined /> },\n      'CANCELLED': { text: '已取消', color: 'default', icon: <ExclamationCircleOutlined /> }\n    };\n    return statusMap[status] || { text: status, color: 'default', icon: null };\n  };\n\n  const getPriorityConfig = (priority: string) => {\n    const priorityMap: Record<string, { text: string; color: string }> = {\n      'LOW': { text: '低', color: 'blue' },\n      'MEDIUM': { text: '中', color: 'orange' },\n      'HIGH': { text: '高', color: 'red' },\n      'URGENT': { text: '紧急', color: 'magenta' }\n    };\n    return priorityMap[priority] || { text: priority, color: 'default' };\n  };\n\n  const getApprovalProgress = (approvers: any[]) => {\n    const total = approvers.length;\n    const approved = approvers.filter(a => a.status === 'APPROVED').length;\n    const rejected = approvers.filter(a => a.status === 'REJECTED').length;\n    \n    if (rejected > 0) {\n      return { percent: 100, status: 'exception' as const };\n    }\n    \n    const percent = total > 0 ? (approved / total) * 100 : 0;\n    return { percent, status: percent === 100 ? 'success' as const : 'active' as const };\n  };\n\n  const columns = [\n    {\n      title: 'ECN编号',\n      dataIndex: 'number',\n      key: 'number',\n      width: 150,\n      render: (text: string, record: ECN) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: '标题',\n      dataIndex: 'title',\n      key: 'title',\n      ellipsis: {\n        showTitle: false,\n      },\n      render: (title: string) => (\n        <Tooltip placement=\"topLeft\" title={title}>\n          {title}\n        </Tooltip>\n      ),\n    },\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      width: 100,\n      render: (priority: string) => {\n        const config = getPriorityConfig(priority);\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 120,\n      render: (status: string) => {\n        const config = getStatusConfig(status);\n        return (\n          <Badge\n            status={config.color as any}\n            text={\n              <Space>\n                {config.icon}\n                {config.text}\n              </Space>\n            }\n          />\n        );\n      },\n    },\n    {\n      title: '发起人',\n      dataIndex: 'initiator',\n      key: 'initiator',\n      width: 120,\n      render: (initiator: string) => (\n        <Space>\n          <Avatar icon={<UserOutlined />} size=\"small\" />\n          {initiator}\n        </Space>\n      ),\n    },\n    {\n      title: '审批进度',\n      key: 'approvalProgress',\n      width: 150,\n      render: (_: any, record: ECN) => {\n        const progress = getApprovalProgress(record.approvers);\n        return (\n          <Progress\n            percent={progress.percent}\n            status={progress.status}\n            size=\"small\"\n            format={(percent) => `${record.approvers.filter(a => a.status === 'APPROVED').length}/${record.approvers.length}`}\n          />\n        );\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 150,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '生效日期',\n      dataIndex: 'effectiveDate',\n      key: 'effectiveDate',\n      width: 120,\n      render: (date: string) => date ? formatDate(date) : '-',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right' as const,\n      render: (_: any, record: ECN) => {\n        const items: MenuProps['items'] = [\n          {\n            key: 'view',\n            label: '查看详情',\n            icon: <EyeOutlined />,\n            onClick: () => handleView(record),\n          },\n          {\n            key: 'edit',\n            label: '编辑',\n            icon: <EditOutlined />,\n            onClick: () => handleEdit(record),\n            disabled: record.status !== 'DRAFT',\n          },\n          {\n            key: 'copy',\n            label: '复制',\n            icon: <CopyOutlined />,\n            onClick: () => handleCopy(record),\n          },\n          {\n            type: 'divider',\n          },\n          {\n            key: 'delete',\n            label: '删除',\n            icon: <DeleteOutlined />,\n            danger: true,\n            disabled: record.status === 'APPROVED' || record.status === 'IMPLEMENTED',\n          },\n        ];\n\n        return (\n          <Space>\n            <Button\n              type=\"primary\"\n              size=\"small\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            >\n              查看\n            </Button>\n            <Dropdown menu={{ items }} trigger={['click']}>\n              <Button size=\"small\" icon={<MoreOutlined />} />\n            </Dropdown>\n            {(record.status === 'DRAFT' || record.status === 'REVIEW') && (\n              <Popconfirm\n                title=\"确定要删除这个ECN吗？\"\n                onConfirm={() => handleDelete(record)}\n                okText=\"确定\"\n                cancelText=\"取消\"\n              >\n                <Button\n                  size=\"small\"\n                  danger\n                  icon={<DeleteOutlined />}\n                />\n              </Popconfirm>\n            )}\n          </Space>\n        );\n      },\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: (record: ECN) => ({\n      disabled: record.status === 'APPROVED' || record.status === 'IMPLEMENTED',\n    }),\n  };\n\n  // 统计数据\n  const statistics = {\n    total: ecnList.length,\n    draft: ecnList.filter(item => item.status === 'DRAFT').length,\n    review: ecnList.filter(item => item.status === 'REVIEW').length,\n    approved: ecnList.filter(item => item.status === 'APPROVED').length,\n    rejected: ecnList.filter(item => item.status === 'REJECTED').length,\n  };\n\n  return (\n    <div>\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={12} md={6} lg={4}>\n          <Card>\n            <Statistic\n              title=\"总数\"\n              value={statistics.total}\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6} lg={4}>\n          <Card>\n            <Statistic\n              title=\"草稿\"\n              value={statistics.draft}\n              valueStyle={{ color: '#999' }}\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6} lg={4}>\n          <Card>\n            <Statistic\n              title=\"审核中\"\n              value={statistics.review}\n              valueStyle={{ color: '#1890ff' }}\n              prefix={<ClockCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6} lg={4}>\n          <Card>\n            <Statistic\n              title=\"已批准\"\n              value={statistics.approved}\n              valueStyle={{ color: '#52c41a' }}\n              prefix={<CheckCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6} lg={4}>\n          <Card>\n            <Statistic\n              title=\"已拒绝\"\n              value={statistics.rejected}\n              valueStyle={{ color: '#ff4d4f' }}\n              prefix={<CloseCircleOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>\n          <Title level={4}>ECN列表</Title>\n          <Space>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={() => navigate('/ecn/create')}\n            >\n              创建ECN\n            </Button>\n            <Button\n              icon={<ExportOutlined />}\n              onClick={handleExport}\n            >\n              导出\n            </Button>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={loadData}\n            >\n              刷新\n            </Button>\n          </Space>\n        </div>\n\n        {/* 搜索和筛选 */}\n        <Row gutter={16} style={{ marginBottom: 16 }}>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Search\n              placeholder=\"搜索ECN编号、标题\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Select\n              placeholder=\"状态筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              value={statusFilter}\n              onChange={handleStatusFilter}\n            >\n              <Option value=\"DRAFT\">草稿</Option>\n              <Option value=\"REVIEW\">审核中</Option>\n              <Option value=\"APPROVED\">已批准</Option>\n              <Option value=\"REJECTED\">已拒绝</Option>\n              <Option value=\"IMPLEMENTED\">已实施</Option>\n              <Option value=\"CANCELLED\">已取消</Option>\n            </Select>\n          </Col>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Select\n              placeholder=\"优先级筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              value={priorityFilter}\n              onChange={handlePriorityFilter}\n            >\n              <Option value=\"LOW\">低</Option>\n              <Option value=\"MEDIUM\">中</Option>\n              <Option value=\"HIGH\">高</Option>\n              <Option value=\"URGENT\">紧急</Option>\n            </Select>\n          </Col>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <RangePicker\n              placeholder={['开始日期', '结束日期']}\n              style={{ width: '100%' }}\n              value={dateRange}\n              onChange={handleDateRangeChange}\n            />\n          </Col>\n        </Row>\n\n        {/* 批量操作 */}\n        {selectedRowKeys.length > 0 && (\n          <Alert\n            message={\n              <Space>\n                <span>已选择 {selectedRowKeys.length} 项</span>\n                <Button size=\"small\" onClick={() => setSelectedRowKeys([])}>\n                  取消选择\n                </Button>\n                <Popconfirm\n                  title={`确定要删除选中的 ${selectedRowKeys.length} 个ECN吗？`}\n                  onConfirm={handleBatchDelete}\n                  okText=\"确定\"\n                  cancelText=\"取消\"\n                >\n                  <Button size=\"small\" danger>\n                    批量删除\n                  </Button>\n                </Popconfirm>\n              </Space>\n            }\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        )}\n\n        <Table\n          columns={columns}\n          dataSource={ecnList}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={rowSelection}\n          pagination={{\n            ...pagination,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || 10 }));\n            },\n          }}\n          scroll={{ x: 1200 }}\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default ECNListPage;\n"], "mappings": "wHAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,UAAU,CACVC,OAAO,CAGPC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,OAAO,CACPC,QAAQ,CAERC,SAAS,CACTC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,QAAQ,CACRC,KAAK,KAEA,MAAM,CACb,OACEC,YAAY,CAEZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,YAAY,CACZC,cAAc,CAEdC,cAAc,CACdC,mBAAmB,CACnBC,mBAAmB,CACnBC,yBAAyB,CACzBC,mBAAmB,CACnBC,YAAY,CAEZC,gBAAgB,KACX,mBAAmB,CAI1B,OAASC,UAAU,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG/B,UAAU,CAClC,KAAM,CAAEgC,MAAO,CAAC,CAAGtC,KAAK,CACxB,KAAM,CAAEuC,WAAY,CAAC,CAAG3B,UAAU,CAClC,KAAM,CAAE4B,MAAO,CAAC,CAAGvC,MAAM,CAEzB,KAAM,CAAAwC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAC,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAC+C,OAAO,CAAEC,UAAU,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACkD,OAAO,CAAEC,UAAU,CAAC,CAAGnD,QAAQ,CAAQ,EAAE,CAAC,CACjD,KAAM,CAACoD,aAAa,CAAEC,gBAAgB,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACsD,YAAY,CAAEC,eAAe,CAAC,CAAGvD,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACwD,cAAc,CAAEC,iBAAiB,CAAC,CAAGzD,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAAC0D,SAAS,CAAEC,YAAY,CAAC,CAAG3D,QAAQ,CAAkD,IAAI,CAAC,CACjG,KAAM,CAAC4D,eAAe,CAAEC,kBAAkB,CAAC,CAAG7D,QAAQ,CAAc,EAAE,CAAC,CACvE,KAAM,CAAC8D,UAAU,CAAEC,aAAa,CAAC,CAAG/D,QAAQ,CAAC,CAC3CgE,OAAO,CAAE,CAAC,CACVC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CACT,CAAC,CAAC,CAEFnE,SAAS,CAAC,IAAM,CACdoE,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACL,UAAU,CAACE,OAAO,CAAEF,UAAU,CAACG,QAAQ,CAAEb,aAAa,CAAEE,YAAY,CAAEE,cAAc,CAAEE,SAAS,CAAC,CAAC,CAErG,KAAM,CAAAS,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACFlB,UAAU,CAAC,IAAI,CAAC,CAChB;AACA;AACA,KAAM,CAAAmB,QAAe,CAAG,CACtB,CACEC,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,gBAAgB,CACxBC,KAAK,CAAE,UAAU,CACjBC,WAAW,CAAE,2CAA2C,CACxDC,MAAM,CAAE,mBAAmB,CAC3BC,kBAAkB,CAAE,0CAA0C,CAC9DC,QAAQ,CAAE,MAAM,CAChBC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,qBAAqB,CAChCC,SAAS,CAAE,qBAAqB,CAChCC,aAAa,CAAE,YAAY,CAC3BC,aAAa,CAAE,EAAE,CACjBC,SAAS,CAAE,CACT,CACEC,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,MAAM,CACZT,MAAM,CAAE,UAAU,CAClBU,UAAU,CAAE,qBAAqB,CACjCC,OAAO,CAAE,6BACX,CAAC,CACD,CACEJ,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,MAAM,CACZT,MAAM,CAAE,SACV,CAAC,CAEL,CAAC,CACD,CACEP,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,gBAAgB,CACxBC,KAAK,CAAE,UAAU,CACjBC,WAAW,CAAE,6BAA6B,CAC1CC,MAAM,CAAE,mBAAmB,CAC3BC,kBAAkB,CAAE,8CAA8C,CAClEC,QAAQ,CAAE,QAAQ,CAClBC,MAAM,CAAE,UAAU,CAClBC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,qBAAqB,CAChCC,SAAS,CAAE,qBAAqB,CAChCC,aAAa,CAAE,YAAY,CAC3BC,aAAa,CAAE,EAAE,CACjBC,SAAS,CAAE,CACT,CACEC,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,MAAM,CACZT,MAAM,CAAE,UAAU,CAClBU,UAAU,CAAE,qBAAqB,CACjCC,OAAO,CAAE,kBACX,CAAC,CACD,CACEJ,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,MAAM,CACZT,MAAM,CAAE,UAAU,CAClBU,UAAU,CAAE,qBAAqB,CACjCC,OAAO,CAAE,mBACX,CAAC,CAEL,CAAC,CACD,CACElB,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,gBAAgB,CACxBC,KAAK,CAAE,SAAS,CAChBC,WAAW,CAAE,yBAAyB,CACtCC,MAAM,CAAE,qBAAqB,CAC7BC,kBAAkB,CAAE,oCAAoC,CACxDC,QAAQ,CAAE,QAAQ,CAClBC,MAAM,CAAE,UAAU,CAClBC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,qBAAqB,CAChCC,SAAS,CAAE,qBAAqB,CAChCC,aAAa,CAAE,YAAY,CAC3BC,aAAa,CAAE,EAAE,CACjBC,SAAS,CAAE,CACT,CACEC,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,MAAM,CACZT,MAAM,CAAE,UAAU,CAClBU,UAAU,CAAE,qBAAqB,CACjCC,OAAO,CAAE,uBACX,CAAC,CAEL,CAAC,CACF,CAEDpC,UAAU,CAACiB,QAAQ,CAAC,CACpBL,aAAa,CAACyB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEtB,KAAK,CAAEE,QAAQ,CAACsB,MAAM,EAAG,CAAC,CAC9D,CAAE,MAAOC,KAAK,CAAE,CACdjF,OAAO,CAACiF,KAAK,CAAC,WAAW,CAAC,CAC5B,CAAC,OAAS,CACR1C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA2C,YAAY,CAAIC,KAAa,EAAK,CACtCxC,gBAAgB,CAACwC,KAAK,CAAC,CACvB9B,aAAa,CAACyB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAExB,OAAO,CAAE,CAAC,EAAG,CAAC,CAClD,CAAC,CAED,KAAM,CAAA8B,kBAAkB,CAAID,KAAa,EAAK,CAC5CtC,eAAe,CAACsC,KAAK,CAAC,CACtB9B,aAAa,CAACyB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAExB,OAAO,CAAE,CAAC,EAAG,CAAC,CAClD,CAAC,CAED,KAAM,CAAA+B,oBAAoB,CAAIF,KAAa,EAAK,CAC9CpC,iBAAiB,CAACoC,KAAK,CAAC,CACxB9B,aAAa,CAACyB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAExB,OAAO,CAAE,CAAC,EAAG,CAAC,CAClD,CAAC,CAED,KAAM,CAAAgC,qBAAqB,CAAGA,CAACC,KAAsD,CAAEC,WAA6B,GAAK,CACvHvC,YAAY,CAACsC,KAAK,CAAC,CACnBlC,aAAa,CAACyB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAExB,OAAO,CAAE,CAAC,EAAG,CAAC,CAClD,CAAC,CAED,KAAM,CAAAmC,UAAU,CAAIC,MAAW,EAAK,CAClCrD,QAAQ,gBAAAsD,MAAA,CAAgBD,MAAM,CAAC/B,EAAE,CAAE,CAAC,CACtC,CAAC,CAED,KAAM,CAAAiC,UAAU,CAAIF,MAAW,EAAK,CAClCrD,QAAQ,cAAAsD,MAAA,CAAcD,MAAM,CAAC/B,EAAE,CAAE,CAAC,CACpC,CAAC,CAED,KAAM,CAAAkC,UAAU,CAAIH,MAAW,EAAK,CAClC;AACA1F,OAAO,CAAC8F,IAAI,CAAC,SAAS,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAL,MAAW,EAAK,CAC1C,GAAI,CACF;AACA1F,OAAO,CAACgG,OAAO,CAAC,MAAM,CAAC,CACvBvC,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOwB,KAAK,CAAE,CACdjF,OAAO,CAACiF,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI/C,eAAe,CAAC8B,MAAM,GAAK,CAAC,CAAE,CAChChF,OAAO,CAACkG,OAAO,CAAC,YAAY,CAAC,CAC7B,OACF,CAEA,GAAI,CACF;AACAlG,OAAO,CAACgG,OAAO,6BAAAL,MAAA,CAASzC,eAAe,CAAC8B,MAAM,cAAO,CAAC,CACtD7B,kBAAkB,CAAC,EAAE,CAAC,CACtBM,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOwB,KAAK,CAAE,CACdjF,OAAO,CAACiF,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAkB,YAAY,CAAGA,CAAA,GAAM,CACzB;AACAnG,OAAO,CAAC8F,IAAI,CAAC,SAAS,CAAC,CACzB,CAAC,CAED,KAAM,CAAAM,eAAe,CAAIlC,MAAc,EAAK,CAC1C,KAAM,CAAAmC,SAAiF,CAAG,CACxF,OAAO,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,cAAE5E,IAAA,CAACH,gBAAgB,GAAE,CAAE,CAAC,CACrE,QAAQ,CAAE,CAAE6E,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,YAAY,CAAEC,IAAI,cAAE5E,IAAA,CAACP,mBAAmB,GAAE,CAAE,CAAC,CAC7E,UAAU,CAAE,CAAEiF,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,cAAE5E,IAAA,CAACR,mBAAmB,GAAE,CAAE,CAAC,CAC5E,UAAU,CAAE,CAAEkF,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,OAAO,CAAEC,IAAI,cAAE5E,IAAA,CAACL,mBAAmB,GAAE,CAAE,CAAC,CAC1E,aAAa,CAAE,CAAE+E,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,cAAE5E,IAAA,CAACR,mBAAmB,GAAE,CAAE,CAAC,CAC/E,WAAW,CAAE,CAAEkF,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,cAAE5E,IAAA,CAACN,yBAAyB,GAAE,CAAE,CACpF,CAAC,CACD,MAAO,CAAA+E,SAAS,CAACnC,MAAM,CAAC,EAAI,CAAEoC,IAAI,CAAEpC,MAAM,CAAEqC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC5E,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIxC,QAAgB,EAAK,CAC9C,KAAM,CAAAyC,WAA4D,CAAG,CACnE,KAAK,CAAE,CAAEJ,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,MAAO,CAAC,CACnC,QAAQ,CAAE,CAAED,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,QAAS,CAAC,CACxC,MAAM,CAAE,CAAED,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,KAAM,CAAC,CACnC,QAAQ,CAAE,CAAED,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAU,CAC3C,CAAC,CACD,MAAO,CAAAG,WAAW,CAACzC,QAAQ,CAAC,EAAI,CAAEqC,IAAI,CAAErC,QAAQ,CAAEsC,KAAK,CAAE,SAAU,CAAC,CACtE,CAAC,CAED,KAAM,CAAAI,mBAAmB,CAAInC,SAAgB,EAAK,CAChD,KAAM,CAAAhB,KAAK,CAAGgB,SAAS,CAACQ,MAAM,CAC9B,KAAM,CAAA4B,QAAQ,CAAGpC,SAAS,CAACqC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC5C,MAAM,GAAK,UAAU,CAAC,CAACc,MAAM,CACtE,KAAM,CAAA+B,QAAQ,CAAGvC,SAAS,CAACqC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC5C,MAAM,GAAK,UAAU,CAAC,CAACc,MAAM,CAEtE,GAAI+B,QAAQ,CAAG,CAAC,CAAE,CAChB,MAAO,CAAEC,OAAO,CAAE,GAAG,CAAE9C,MAAM,CAAE,WAAqB,CAAC,CACvD,CAEA,KAAM,CAAA8C,OAAO,CAAGxD,KAAK,CAAG,CAAC,CAAIoD,QAAQ,CAAGpD,KAAK,CAAI,GAAG,CAAG,CAAC,CACxD,MAAO,CAAEwD,OAAO,CAAE9C,MAAM,CAAE8C,OAAO,GAAK,GAAG,CAAG,SAAS,CAAY,QAAkB,CAAC,CACtF,CAAC,CAED,KAAM,CAAAC,OAAO,CAAG,CACd,CACEpD,KAAK,CAAE,OAAO,CACdqD,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACf,IAAY,CAAEZ,MAAW,gBAChC9D,IAAA,CAACnC,MAAM,EAAC6H,IAAI,CAAC,MAAM,CAACC,OAAO,CAAEA,CAAA,GAAM9B,UAAU,CAACC,MAAM,CAAE,CAAA8B,QAAA,CACnDlB,IAAI,CACC,CAEZ,CAAC,CACD,CACEzC,KAAK,CAAE,IAAI,CACXqD,SAAS,CAAE,OAAO,CAClBC,GAAG,CAAE,OAAO,CACZM,QAAQ,CAAE,CACRC,SAAS,CAAE,KACb,CAAC,CACDL,MAAM,CAAGxD,KAAa,eACpBjC,IAAA,CAACxB,OAAO,EAACuH,SAAS,CAAC,SAAS,CAAC9D,KAAK,CAAEA,KAAM,CAAA2D,QAAA,CACvC3D,KAAK,CACC,CAEb,CAAC,CACD,CACEA,KAAK,CAAE,KAAK,CACZqD,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGpD,QAAgB,EAAK,CAC5B,KAAM,CAAA2D,MAAM,CAAGnB,iBAAiB,CAACxC,QAAQ,CAAC,CAC1C,mBAAOrC,IAAA,CAAC9B,GAAG,EAACyG,KAAK,CAAEqB,MAAM,CAACrB,KAAM,CAAAiB,QAAA,CAAEI,MAAM,CAACtB,IAAI,CAAM,CAAC,CACtD,CACF,CAAC,CACD,CACEzC,KAAK,CAAE,IAAI,CACXqD,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGnD,MAAc,EAAK,CAC1B,KAAM,CAAA0D,MAAM,CAAGxB,eAAe,CAAClC,MAAM,CAAC,CACtC,mBACEtC,IAAA,CAACpB,KAAK,EACJ0D,MAAM,CAAE0D,MAAM,CAACrB,KAAa,CAC5BD,IAAI,cACFxE,KAAA,CAACpC,KAAK,EAAA8H,QAAA,EACHI,MAAM,CAACpB,IAAI,CACXoB,MAAM,CAACtB,IAAI,EACP,CACR,CACF,CAAC,CAEN,CACF,CAAC,CACD,CACEzC,KAAK,CAAE,KAAK,CACZqD,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGlD,SAAiB,eACxBrC,KAAA,CAACpC,KAAK,EAAA8H,QAAA,eACJ5F,IAAA,CAACnB,MAAM,EAAC+F,IAAI,cAAE5E,IAAA,CAACJ,YAAY,GAAE,CAAE,CAACqG,IAAI,CAAC,OAAO,CAAE,CAAC,CAC9C1D,SAAS,EACL,CAEX,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbsD,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACS,CAAM,CAAEpC,MAAW,GAAK,CAC/B,KAAM,CAAAqC,QAAQ,CAAGpB,mBAAmB,CAACjB,MAAM,CAAClB,SAAS,CAAC,CACtD,mBACE5C,IAAA,CAAClB,QAAQ,EACPsG,OAAO,CAAEe,QAAQ,CAACf,OAAQ,CAC1B9C,MAAM,CAAE6D,QAAQ,CAAC7D,MAAO,CACxB2D,IAAI,CAAC,OAAO,CACZG,MAAM,CAAGhB,OAAO,KAAArB,MAAA,CAAQD,MAAM,CAAClB,SAAS,CAACqC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC5C,MAAM,GAAK,UAAU,CAAC,CAACc,MAAM,MAAAW,MAAA,CAAID,MAAM,CAAClB,SAAS,CAACQ,MAAM,CAAG,CACnH,CAAC,CAEN,CACF,CAAC,CACD,CACEnB,KAAK,CAAE,MAAM,CACbqD,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGY,IAAY,EAAKvG,UAAU,CAACuG,IAAI,CAC3C,CAAC,CACD,CACEpE,KAAK,CAAE,MAAM,CACbqD,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGY,IAAY,EAAKA,IAAI,CAAGvG,UAAU,CAACuG,IAAI,CAAC,CAAG,GACtD,CAAC,CACD,CACEpE,KAAK,CAAE,IAAI,CACXsD,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVc,KAAK,CAAE,OAAgB,CACvBb,MAAM,CAAEA,CAACS,CAAM,CAAEpC,MAAW,GAAK,CAC/B,KAAM,CAAAyC,KAAyB,CAAG,CAChC,CACEhB,GAAG,CAAE,MAAM,CACXiB,KAAK,CAAE,MAAM,CACb5B,IAAI,cAAE5E,IAAA,CAACb,WAAW,GAAE,CAAC,CACrBwG,OAAO,CAAEA,CAAA,GAAM9B,UAAU,CAACC,MAAM,CAClC,CAAC,CACD,CACEyB,GAAG,CAAE,MAAM,CACXiB,KAAK,CAAE,IAAI,CACX5B,IAAI,cAAE5E,IAAA,CAACf,YAAY,GAAE,CAAC,CACtB0G,OAAO,CAAEA,CAAA,GAAM3B,UAAU,CAACF,MAAM,CAAC,CACjC2C,QAAQ,CAAE3C,MAAM,CAACxB,MAAM,GAAK,OAC9B,CAAC,CACD,CACEiD,GAAG,CAAE,MAAM,CACXiB,KAAK,CAAE,IAAI,CACX5B,IAAI,cAAE5E,IAAA,CAACZ,YAAY,GAAE,CAAC,CACtBuG,OAAO,CAAEA,CAAA,GAAM1B,UAAU,CAACH,MAAM,CAClC,CAAC,CACD,CACE4B,IAAI,CAAE,SACR,CAAC,CACD,CACEH,GAAG,CAAE,QAAQ,CACbiB,KAAK,CAAE,IAAI,CACX5B,IAAI,cAAE5E,IAAA,CAACd,cAAc,GAAE,CAAC,CACxBwH,MAAM,CAAE,IAAI,CACZD,QAAQ,CAAE3C,MAAM,CAACxB,MAAM,GAAK,UAAU,EAAIwB,MAAM,CAACxB,MAAM,GAAK,aAC9D,CAAC,CACF,CAED,mBACEpC,KAAA,CAACpC,KAAK,EAAA8H,QAAA,eACJ5F,IAAA,CAACnC,MAAM,EACL6H,IAAI,CAAC,SAAS,CACdO,IAAI,CAAC,OAAO,CACZrB,IAAI,cAAE5E,IAAA,CAACb,WAAW,GAAE,CAAE,CACtBwG,OAAO,CAAEA,CAAA,GAAM9B,UAAU,CAACC,MAAM,CAAE,CAAA8B,QAAA,CACnC,cAED,CAAQ,CAAC,cACT5F,IAAA,CAACvB,QAAQ,EAACkI,IAAI,CAAE,CAAEJ,KAAM,CAAE,CAACK,OAAO,CAAE,CAAC,OAAO,CAAE,CAAAhB,QAAA,cAC5C5F,IAAA,CAACnC,MAAM,EAACoI,IAAI,CAAC,OAAO,CAACrB,IAAI,cAAE5E,IAAA,CAACX,YAAY,GAAE,CAAE,CAAE,CAAC,CACvC,CAAC,CACV,CAACyE,MAAM,CAACxB,MAAM,GAAK,OAAO,EAAIwB,MAAM,CAACxB,MAAM,GAAK,QAAQ,gBACvDtC,IAAA,CAAC7B,UAAU,EACT8D,KAAK,CAAC,2DAAc,CACpB4E,SAAS,CAAEA,CAAA,GAAM1C,YAAY,CAACL,MAAM,CAAE,CACtCgD,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAnB,QAAA,cAEf5F,IAAA,CAACnC,MAAM,EACLoI,IAAI,CAAC,OAAO,CACZS,MAAM,MACN9B,IAAI,cAAE5E,IAAA,CAACd,cAAc,GAAE,CAAE,CAC1B,CAAC,CACQ,CACb,EACI,CAAC,CAEZ,CACF,CAAC,CACF,CAED,KAAM,CAAA8H,YAAY,CAAG,CACnB1F,eAAe,CACf2F,QAAQ,CAAGC,kBAA+B,EAAK,CAC7C3F,kBAAkB,CAAC2F,kBAAkB,CAAC,CACxC,CAAC,CACDC,gBAAgB,CAAGrD,MAAW,GAAM,CAClC2C,QAAQ,CAAE3C,MAAM,CAACxB,MAAM,GAAK,UAAU,EAAIwB,MAAM,CAACxB,MAAM,GAAK,aAC9D,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAA8E,UAAU,CAAG,CACjBxF,KAAK,CAAEhB,OAAO,CAACwC,MAAM,CACrBiE,KAAK,CAAEzG,OAAO,CAACqE,MAAM,CAACqC,IAAI,EAAIA,IAAI,CAAChF,MAAM,GAAK,OAAO,CAAC,CAACc,MAAM,CAC7DmE,MAAM,CAAE3G,OAAO,CAACqE,MAAM,CAACqC,IAAI,EAAIA,IAAI,CAAChF,MAAM,GAAK,QAAQ,CAAC,CAACc,MAAM,CAC/D4B,QAAQ,CAAEpE,OAAO,CAACqE,MAAM,CAACqC,IAAI,EAAIA,IAAI,CAAChF,MAAM,GAAK,UAAU,CAAC,CAACc,MAAM,CACnE+B,QAAQ,CAAEvE,OAAO,CAACqE,MAAM,CAACqC,IAAI,EAAIA,IAAI,CAAChF,MAAM,GAAK,UAAU,CAAC,CAACc,MAC/D,CAAC,CAED,mBACElD,KAAA,QAAA0F,QAAA,eAEE1F,KAAA,CAAC5B,GAAG,EAACkJ,MAAM,CAAE,EAAG,CAACC,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAA9B,QAAA,eAC3C5F,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC5F,IAAA,CAAC/B,IAAI,EAAA2H,QAAA,cACH5F,IAAA,CAACtB,SAAS,EACRuD,KAAK,CAAC,cAAI,CACVsB,KAAK,CAAE6D,UAAU,CAACxF,KAAM,CACxBmG,MAAM,cAAE/H,IAAA,CAACH,gBAAgB,GAAE,CAAE,CAC9B,CAAC,CACE,CAAC,CACJ,CAAC,cACNG,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC5F,IAAA,CAAC/B,IAAI,EAAA2H,QAAA,cACH5F,IAAA,CAACtB,SAAS,EACRuD,KAAK,CAAC,cAAI,CACVsB,KAAK,CAAE6D,UAAU,CAACC,KAAM,CACxBW,UAAU,CAAE,CAAErD,KAAK,CAAE,MAAO,CAAE,CAC9BoD,MAAM,cAAE/H,IAAA,CAACH,gBAAgB,GAAE,CAAE,CAC9B,CAAC,CACE,CAAC,CACJ,CAAC,cACNG,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC5F,IAAA,CAAC/B,IAAI,EAAA2H,QAAA,cACH5F,IAAA,CAACtB,SAAS,EACRuD,KAAK,CAAC,oBAAK,CACXsB,KAAK,CAAE6D,UAAU,CAACG,MAAO,CACzBS,UAAU,CAAE,CAAErD,KAAK,CAAE,SAAU,CAAE,CACjCoD,MAAM,cAAE/H,IAAA,CAACP,mBAAmB,GAAE,CAAE,CACjC,CAAC,CACE,CAAC,CACJ,CAAC,cACNO,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC5F,IAAA,CAAC/B,IAAI,EAAA2H,QAAA,cACH5F,IAAA,CAACtB,SAAS,EACRuD,KAAK,CAAC,oBAAK,CACXsB,KAAK,CAAE6D,UAAU,CAACpC,QAAS,CAC3BgD,UAAU,CAAE,CAAErD,KAAK,CAAE,SAAU,CAAE,CACjCoD,MAAM,cAAE/H,IAAA,CAACR,mBAAmB,GAAE,CAAE,CACjC,CAAC,CACE,CAAC,CACJ,CAAC,cACNQ,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC5F,IAAA,CAAC/B,IAAI,EAAA2H,QAAA,cACH5F,IAAA,CAACtB,SAAS,EACRuD,KAAK,CAAC,oBAAK,CACXsB,KAAK,CAAE6D,UAAU,CAACjC,QAAS,CAC3B6C,UAAU,CAAE,CAAErD,KAAK,CAAE,SAAU,CAAE,CACjCoD,MAAM,cAAE/H,IAAA,CAACL,mBAAmB,GAAE,CAAE,CACjC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAENO,KAAA,CAACjC,IAAI,EAAA2H,QAAA,eACH1F,KAAA,QAAKuH,KAAK,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAET,YAAY,CAAE,EAAG,CAAE,CAAA9B,QAAA,eACvG5F,IAAA,CAACG,KAAK,EAACiI,KAAK,CAAE,CAAE,CAAAxC,QAAA,CAAC,iBAAK,CAAO,CAAC,cAC9B1F,KAAA,CAACpC,KAAK,EAAA8H,QAAA,eACJ5F,IAAA,CAACnC,MAAM,EACL6H,IAAI,CAAC,SAAS,CACdd,IAAI,cAAE5E,IAAA,CAAChB,YAAY,GAAE,CAAE,CACvB2G,OAAO,CAAEA,CAAA,GAAMlF,QAAQ,CAAC,aAAa,CAAE,CAAAmF,QAAA,CACxC,iBAED,CAAQ,CAAC,cACT5F,IAAA,CAACnC,MAAM,EACL+G,IAAI,cAAE5E,IAAA,CAACV,cAAc,GAAE,CAAE,CACzBqG,OAAO,CAAEpB,YAAa,CAAAqB,QAAA,CACvB,cAED,CAAQ,CAAC,cACT5F,IAAA,CAACnC,MAAM,EACL+G,IAAI,cAAE5E,IAAA,CAACT,cAAc,GAAE,CAAE,CACzBoG,OAAO,CAAE9D,QAAS,CAAA+D,QAAA,CACnB,cAED,CAAQ,CAAC,EACJ,CAAC,EACL,CAAC,cAGN1F,KAAA,CAAC5B,GAAG,EAACkJ,MAAM,CAAE,EAAG,CAACC,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAA9B,QAAA,eAC3C5F,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC5F,IAAA,CAACK,MAAM,EACLgI,WAAW,CAAC,+CAAY,CACxBC,UAAU,MACVC,QAAQ,CAAEjF,YAAa,CACvBmE,KAAK,CAAE,CAAEjC,KAAK,CAAE,MAAO,CAAE,CAC1B,CAAC,CACC,CAAC,cACNxF,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC1F,KAAA,CAAClC,MAAM,EACLqK,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVb,KAAK,CAAE,CAAEjC,KAAK,CAAE,MAAO,CAAE,CACzBjC,KAAK,CAAEvC,YAAa,CACpBiG,QAAQ,CAAEzD,kBAAmB,CAAAoC,QAAA,eAE7B5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,OAAO,CAAAqC,QAAA,CAAC,cAAE,CAAQ,CAAC,cACjC5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,QAAQ,CAAAqC,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACnC5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,UAAU,CAAAqC,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACrC5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,UAAU,CAAAqC,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACrC5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,aAAa,CAAAqC,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACxC5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,WAAW,CAAAqC,QAAA,CAAC,oBAAG,CAAQ,CAAC,EAChC,CAAC,CACN,CAAC,cACN5F,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC1F,KAAA,CAAClC,MAAM,EACLqK,WAAW,CAAC,gCAAO,CACnBC,UAAU,MACVb,KAAK,CAAE,CAAEjC,KAAK,CAAE,MAAO,CAAE,CACzBjC,KAAK,CAAErC,cAAe,CACtB+F,QAAQ,CAAExD,oBAAqB,CAAAmC,QAAA,eAE/B5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,KAAK,CAAAqC,QAAA,CAAC,QAAC,CAAQ,CAAC,cAC9B5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,QAAQ,CAAAqC,QAAA,CAAC,QAAC,CAAQ,CAAC,cACjC5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,MAAM,CAAAqC,QAAA,CAAC,QAAC,CAAQ,CAAC,cAC/B5F,IAAA,CAACO,MAAM,EAACgD,KAAK,CAAC,QAAQ,CAAAqC,QAAA,CAAC,cAAE,CAAQ,CAAC,EAC5B,CAAC,CACN,CAAC,cACN5F,IAAA,CAACzB,GAAG,EAACoJ,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAlC,QAAA,cAChC5F,IAAA,CAACM,WAAW,EACV+H,WAAW,CAAE,CAAC,MAAM,CAAE,MAAM,CAAE,CAC9BZ,KAAK,CAAE,CAAEjC,KAAK,CAAE,MAAO,CAAE,CACzBjC,KAAK,CAAEnC,SAAU,CACjB6F,QAAQ,CAAEvD,qBAAsB,CACjC,CAAC,CACC,CAAC,EACH,CAAC,CAGLpC,eAAe,CAAC8B,MAAM,CAAG,CAAC,eACzBpD,IAAA,CAACjB,KAAK,EACJX,OAAO,cACL8B,KAAA,CAACpC,KAAK,EAAA8H,QAAA,eACJ1F,KAAA,SAAA0F,QAAA,EAAM,qBAAI,CAACtE,eAAe,CAAC8B,MAAM,CAAC,SAAE,EAAM,CAAC,cAC3CpD,IAAA,CAACnC,MAAM,EAACoI,IAAI,CAAC,OAAO,CAACN,OAAO,CAAEA,CAAA,GAAMpE,kBAAkB,CAAC,EAAE,CAAE,CAAAqE,QAAA,CAAC,0BAE5D,CAAQ,CAAC,cACT5F,IAAA,CAAC7B,UAAU,EACT8D,KAAK,qDAAA8B,MAAA,CAAczC,eAAe,CAAC8B,MAAM,0BAAU,CACnDyD,SAAS,CAAExC,iBAAkB,CAC7ByC,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAnB,QAAA,cAEf5F,IAAA,CAACnC,MAAM,EAACoI,IAAI,CAAC,OAAO,CAACS,MAAM,MAAAd,QAAA,CAAC,0BAE5B,CAAQ,CAAC,CACC,CAAC,EACR,CACR,CACDF,IAAI,CAAC,MAAM,CACX8C,QAAQ,MACRf,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAC7B,CACF,cAED1H,IAAA,CAACpC,KAAK,EACJyH,OAAO,CAAEA,OAAQ,CACjBoD,UAAU,CAAE7H,OAAQ,CACpB8H,MAAM,CAAC,IAAI,CACXhI,OAAO,CAAEA,OAAQ,CACjBsG,YAAY,CAAEA,YAAa,CAC3BxF,UAAU,CAAA2B,aAAA,CAAAA,aAAA,IACL3B,UAAU,MACbmH,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACjH,KAAK,CAAEkH,KAAK,aAAA/E,MAAA,CAAU+E,KAAK,CAAC,CAAC,CAAC,MAAA/E,MAAA,CAAI+E,KAAK,CAAC,CAAC,CAAC,oBAAA/E,MAAA,CAAQnC,KAAK,WAAI,CACvEqF,QAAQ,CAAEA,CAAC8B,IAAI,CAAEpH,QAAQ,GAAK,CAC5BF,aAAa,CAACyB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAExB,OAAO,CAAEqH,IAAI,CAAEpH,QAAQ,CAAEA,QAAQ,EAAI,EAAE,EAAG,CAAC,CAC/E,CAAC,EACD,CACFqH,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACrB,CAAC,EACE,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}