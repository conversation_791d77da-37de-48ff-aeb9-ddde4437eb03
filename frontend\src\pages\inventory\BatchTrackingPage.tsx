import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  Row,
  Col,
  Typography,
  Tag,
  Tooltip,
  message,
  Tabs,
  Timeline,
  Descriptions,
  Alert,
  Statistic,
  Progress,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  HistoryOutlined,
  QrcodeOutlined,
  ExportOutlined,
  FilterOutlined,
  Bar<PERSON><PERSON>Outlined,
  AlertOutlined,
} from '@ant-design/icons';
import { formatCurrency, formatDate } from '../../utils/format';

const { Title, Text } = Typography;
const { Search } = Input;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface BatchInfo {
  id: string;
  batchNumber: string;
  materialId: string;
  materialCode: string;
  materialName: string;
  specification: string;
  supplier: string;
  receivedDate: string;
  expiryDate?: string;
  originalQuantity: number;
  currentQuantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  unit: string;
  unitPrice: number;
  totalValue: number;
  location: string;
  status: 'AVAILABLE' | 'RESERVED' | 'EXPIRED' | 'DAMAGED' | 'CONSUMED';
  qualityStatus: 'PASSED' | 'PENDING' | 'FAILED';
  transactions: BatchTransaction[];
  qrCode: string;
}

interface BatchTransaction {
  id: string;
  type: 'RECEIVE' | 'ISSUE' | 'ADJUST' | 'TRANSFER' | 'RETURN';
  quantity: number;
  remainingQuantity: number;
  operator: string;
  department: string;
  workOrder?: string;
  reference: string;
  reason?: string;
  timestamp: string;
  location: string;
}

interface BatchStatistics {
  totalBatches: number;
  activeBatches: number;
  expiredBatches: number;
  lowStockBatches: number;
  totalValue: number;
  averageAge: number;
  turnoverRate: number;
}

const BatchTrackingPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [batches, setBatches] = useState<BatchInfo[]>([]);
  const [statistics, setStatistics] = useState<BatchStatistics>({
    totalBatches: 0,
    activeBatches: 0,
    expiredBatches: 0,
    lowStockBatches: 0,
    totalValue: 0,
    averageAge: 0,
    turnoverRate: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>();
  const [supplierFilter, setSupplierFilter] = useState<string | undefined>();
  const [dateRange, setDateRange] = useState<[any, any] | null>(null);

  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {
    setDateRange(dates);
  };
  const [selectedBatch, setSelectedBatch] = useState<BatchInfo | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('list');

  // 模拟数据
  const mockBatches: BatchInfo[] = [
    {
      id: '1',
      batchNumber: 'B2024030001',
      materialId: 'M001',
      materialCode: 'RF-PA-001',
      materialName: 'RF功率放大器',
      specification: '2.4GHz 20W',
      supplier: '深圳射频科技',
      receivedDate: '2024-03-01T08:00:00Z',
      expiryDate: '2025-03-01T00:00:00Z',
      originalQuantity: 100,
      currentQuantity: 75,
      reservedQuantity: 10,
      availableQuantity: 65,
      unit: 'PCS',
      unitPrice: 150.00,
      totalValue: 11250.00,
      location: 'A区-01-03',
      status: 'AVAILABLE',
      qualityStatus: 'PASSED',
      qrCode: 'QR_B2024030001',
      transactions: [
        {
          id: 'T001',
          type: 'RECEIVE',
          quantity: 100,
          remainingQuantity: 100,
          operator: '张三',
          department: '仓储部',
          reference: 'PO-2024-001',
          timestamp: '2024-03-01T08:00:00Z',
          location: 'A区-01-03',
        },
        {
          id: 'T002',
          type: 'ISSUE',
          quantity: -25,
          remainingQuantity: 75,
          operator: '李四',
          department: '生产部',
          workOrder: 'WO-2024-001',
          reference: 'ISS-2024-001',
          timestamp: '2024-03-05T10:30:00Z',
          location: 'A区-01-03',
        },
      ],
    },
    {
      id: '2',
      batchNumber: 'B2024030002',
      materialId: 'M002',
      materialCode: 'PCB-MAIN-001',
      materialName: '主控PCB板',
      specification: '4层板 FR4',
      supplier: '苏州电路板厂',
      receivedDate: '2024-03-02T09:15:00Z',
      expiryDate: '2026-03-02T00:00:00Z',
      originalQuantity: 200,
      currentQuantity: 180,
      reservedQuantity: 20,
      availableQuantity: 160,
      unit: 'PCS',
      unitPrice: 85.00,
      totalValue: 15300.00,
      location: 'A区-02-01',
      status: 'AVAILABLE',
      qualityStatus: 'PASSED',
      qrCode: 'QR_B2024030002',
      transactions: [
        {
          id: 'T003',
          type: 'RECEIVE',
          quantity: 200,
          remainingQuantity: 200,
          operator: '王五',
          department: '仓储部',
          reference: 'PO-2024-002',
          timestamp: '2024-03-02T09:15:00Z',
          location: 'A区-02-01',
        },
        {
          id: 'T004',
          type: 'ISSUE',
          quantity: -20,
          remainingQuantity: 180,
          operator: '赵六',
          department: '生产部',
          workOrder: 'WO-2024-002',
          reference: 'ISS-2024-002',
          timestamp: '2024-03-06T14:20:00Z',
          location: 'A区-02-01',
        },
      ],
    },
    {
      id: '3',
      batchNumber: 'B2024020015',
      materialId: 'M003',
      materialCode: 'CAP-CER-001',
      materialName: '陶瓷电容',
      specification: '0805 10uF',
      supplier: '村田制作所',
      receivedDate: '2024-02-15T11:30:00Z',
      expiryDate: '2024-04-15T00:00:00Z',
      originalQuantity: 5000,
      currentQuantity: 500,
      reservedQuantity: 0,
      availableQuantity: 500,
      unit: 'PCS',
      unitPrice: 0.25,
      totalValue: 125.00,
      location: 'B区-03-05',
      status: 'EXPIRED',
      qualityStatus: 'PENDING',
      qrCode: 'QR_B2024020015',
      transactions: [
        {
          id: 'T005',
          type: 'RECEIVE',
          quantity: 5000,
          remainingQuantity: 5000,
          operator: '孙七',
          department: '仓储部',
          reference: 'PO-2024-003',
          timestamp: '2024-02-15T11:30:00Z',
          location: 'B区-03-05',
        },
        {
          id: 'T006',
          type: 'ISSUE',
          quantity: -4500,
          remainingQuantity: 500,
          operator: '周八',
          department: '生产部',
          workOrder: 'WO-2024-003',
          reference: 'ISS-2024-003',
          timestamp: '2024-03-10T16:45:00Z',
          location: 'B区-03-05',
        },
      ],
    },
  ];

  useEffect(() => {
    loadBatchData();
    loadStatistics();
  }, []);

  const loadBatchData = async () => {
    try {
      setLoading(true);
      // TODO: 调用API获取批次数据
      setBatches(mockBatches);
    } catch (error) {
      message.error('加载批次数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      // TODO: 调用API获取统计数据
      const stats: BatchStatistics = {
        totalBatches: mockBatches.length,
        activeBatches: mockBatches.filter(b => b.status === 'AVAILABLE').length,
        expiredBatches: mockBatches.filter(b => b.status === 'EXPIRED').length,
        lowStockBatches: mockBatches.filter(b => b.currentQuantity < b.originalQuantity * 0.2).length,
        totalValue: mockBatches.reduce((sum, b) => sum + b.totalValue, 0),
        averageAge: 45,
        turnoverRate: 2.5,
      };
      setStatistics(stats);
    } catch (error) {
      message.error('加载统计数据失败');
    }
  };

  const handleViewDetail = (batch: BatchInfo) => {
    setSelectedBatch(batch);
    setDetailModalVisible(true);
  };

  const handleExport = () => {
    // TODO: 实现导出功能
    message.success('导出功能开发中');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      AVAILABLE: 'green',
      RESERVED: 'blue',
      EXPIRED: 'red',
      DAMAGED: 'orange',
      CONSUMED: 'gray',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getQualityStatusColor = (status: string) => {
    const colors = {
      PASSED: 'green',
      PENDING: 'orange',
      FAILED: 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getTransactionTypeText = (type: string) => {
    const types = {
      RECEIVE: '入库',
      ISSUE: '出库',
      ADJUST: '调整',
      TRANSFER: '转移',
      RETURN: '退库',
    };
    return types[type as keyof typeof types] || type;
  };

  const getTransactionTypeColor = (type: string) => {
    const colors = {
      RECEIVE: 'green',
      ISSUE: 'red',
      ADJUST: 'orange',
      TRANSFER: 'blue',
      RETURN: 'purple',
    };
    return colors[type as keyof typeof colors] || 'default';
  };

  const filteredBatches = batches.filter(batch => {
    if (searchKeyword && !(
      batch.batchNumber.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      batch.materialCode.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      batch.materialName.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      batch.supplier.toLowerCase().includes(searchKeyword.toLowerCase())
    )) {
      return false;
    }
    if (statusFilter && batch.status !== statusFilter) {
      return false;
    }
    if (supplierFilter && batch.supplier !== supplierFilter) {
      return false;
    }
    if (dateRange && dateRange[0] && dateRange[1]) {
      const receivedDate = new Date(batch.receivedDate);
      if (receivedDate < dateRange[0].toDate() || receivedDate > dateRange[1].toDate()) {
        return false;
      }
    }
    return true;
  });

  const batchColumns = [
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      key: 'batchNumber',
      width: 120,
      fixed: 'left' as const,
      render: (text: string, record: BatchInfo) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.qrCode}
          </Text>
        </Space>
      ),
    },
    {
      title: '物料信息',
      key: 'material',
      width: 200,
      render: (_: any, record: BatchInfo) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.materialCode}</Text>
          <Text>{record.materialName}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.specification}
          </Text>
        </Space>
      ),
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 120,
    },
    {
      title: '收货日期',
      dataIndex: 'receivedDate',
      key: 'receivedDate',
      width: 100,
      render: (date: string) => formatDate(date),
    },
    {
      title: '有效期',
      dataIndex: 'expiryDate',
      key: 'expiryDate',
      width: 100,
      render: (date: string) => date ? formatDate(date) : '-',
    },
    {
      title: '库存数量',
      key: 'quantity',
      width: 120,
      render: (_: any, record: BatchInfo) => (
        <Space direction="vertical" size={0}>
          <Text>当前: {record.currentQuantity} {record.unit}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            可用: {record.availableQuantity} | 预留: {record.reservedQuantity}
          </Text>
        </Space>
      ),
    },
    {
      title: '库存率',
      key: 'stockRate',
      width: 100,
      render: (_: any, record: BatchInfo) => {
        const rate = (record.currentQuantity / record.originalQuantity) * 100;
        return (
          <Progress
            percent={rate}
            size="small"
            status={rate < 20 ? 'exception' : rate < 50 ? 'active' : 'success'}
            showInfo={false}
          />
        );
      },
    },
    {
      title: '库位',
      dataIndex: 'location',
      key: 'location',
      width: 100,
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (_: any, record: BatchInfo) => (
        <Space direction="vertical" size={0}>
          <Tag color={getStatusColor(record.status)}>
            {record.status === 'AVAILABLE' ? '可用' :
             record.status === 'RESERVED' ? '预留' :
             record.status === 'EXPIRED' ? '过期' :
             record.status === 'DAMAGED' ? '损坏' : '已消耗'}
          </Tag>
          <Tag color={getQualityStatusColor(record.qualityStatus)}>
            {record.qualityStatus === 'PASSED' ? '合格' :
             record.qualityStatus === 'PENDING' ? '待检' : '不合格'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '总价值',
      dataIndex: 'totalValue',
      key: 'totalValue',
      width: 100,
      render: (value: number) => formatCurrency(value),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: BatchInfo) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="查看二维码">
            <Button
              type="text"
              icon={<QrcodeOutlined />}
              onClick={() => {
                // TODO: 显示二维码
                message.info('二维码功能开发中');
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const renderStatistics = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card size="small">
          <Statistic
            title="总批次数"
            value={statistics.totalBatches}
            prefix={<BarChartOutlined />}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card size="small">
          <Statistic
            title="活跃批次"
            value={statistics.activeBatches}
            valueStyle={{ color: '#3f8600' }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card size="small">
          <Statistic
            title="过期批次"
            value={statistics.expiredBatches}
            valueStyle={{ color: '#cf1322' }}
            prefix={<AlertOutlined />}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card size="small">
          <Statistic
            title="低库存批次"
            value={statistics.lowStockBatches}
            valueStyle={{ color: '#fa8c16' }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card size="small">
          <Statistic
            title="总价值"
            value={statistics.totalValue}
            formatter={(value) => formatCurrency(Number(value))}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card size="small">
          <Statistic
            title="平均库龄"
            value={statistics.averageAge}
            suffix="天"
          />
        </Card>
      </Col>
    </Row>
  );

  const renderBatchDetail = () => {
    if (!selectedBatch) return null;

    return (
      <Modal
        title={`批次详情 - ${selectedBatch.batchNumber}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        <Tabs defaultActiveKey="info">
          <TabPane tab="基本信息" key="info">
            <Descriptions column={2} bordered size="small">
              <Descriptions.Item label="批次号">{selectedBatch.batchNumber}</Descriptions.Item>
              <Descriptions.Item label="二维码">{selectedBatch.qrCode}</Descriptions.Item>
              <Descriptions.Item label="物料编码">{selectedBatch.materialCode}</Descriptions.Item>
              <Descriptions.Item label="物料名称">{selectedBatch.materialName}</Descriptions.Item>
              <Descriptions.Item label="规格">{selectedBatch.specification}</Descriptions.Item>
              <Descriptions.Item label="供应商">{selectedBatch.supplier}</Descriptions.Item>
              <Descriptions.Item label="收货日期">{formatDate(selectedBatch.receivedDate)}</Descriptions.Item>
              <Descriptions.Item label="有效期">
                {selectedBatch.expiryDate ? formatDate(selectedBatch.expiryDate) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="原始数量">
                {selectedBatch.originalQuantity} {selectedBatch.unit}
              </Descriptions.Item>
              <Descriptions.Item label="当前数量">
                {selectedBatch.currentQuantity} {selectedBatch.unit}
              </Descriptions.Item>
              <Descriptions.Item label="可用数量">
                {selectedBatch.availableQuantity} {selectedBatch.unit}
              </Descriptions.Item>
              <Descriptions.Item label="预留数量">
                {selectedBatch.reservedQuantity} {selectedBatch.unit}
              </Descriptions.Item>
              <Descriptions.Item label="单价">{formatCurrency(selectedBatch.unitPrice)}</Descriptions.Item>
              <Descriptions.Item label="总价值">{formatCurrency(selectedBatch.totalValue)}</Descriptions.Item>
              <Descriptions.Item label="库位">{selectedBatch.location}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Space>
                  <Tag color={getStatusColor(selectedBatch.status)}>
                    {selectedBatch.status === 'AVAILABLE' ? '可用' :
                     selectedBatch.status === 'RESERVED' ? '预留' :
                     selectedBatch.status === 'EXPIRED' ? '过期' :
                     selectedBatch.status === 'DAMAGED' ? '损坏' : '已消耗'}
                  </Tag>
                  <Tag color={getQualityStatusColor(selectedBatch.qualityStatus)}>
                    {selectedBatch.qualityStatus === 'PASSED' ? '合格' :
                     selectedBatch.qualityStatus === 'PENDING' ? '待检' : '不合格'}
                  </Tag>
                </Space>
              </Descriptions.Item>
            </Descriptions>
          </TabPane>
          <TabPane tab="交易记录" key="transactions">
            <Timeline>
              {selectedBatch.transactions.map((transaction) => (
                <Timeline.Item
                  key={transaction.id}
                  color={getTransactionTypeColor(transaction.type)}
                >
                  <div>
                    <Space>
                      <Tag color={getTransactionTypeColor(transaction.type)}>
                        {getTransactionTypeText(transaction.type)}
                      </Tag>
                      <Text strong>
                        {transaction.quantity > 0 ? '+' : ''}{transaction.quantity} {selectedBatch.unit}
                      </Text>
                      <Text type="secondary">
                        余量: {transaction.remainingQuantity} {selectedBatch.unit}
                      </Text>
                    </Space>
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {formatDate(transaction.timestamp)} | {transaction.operator} | {transaction.department}
                      </Text>
                    </div>
                    <div style={{ marginTop: 2 }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        参考: {transaction.reference}
                        {transaction.workOrder && ` | 工单: ${transaction.workOrder}`}
                        {transaction.reason && ` | 原因: ${transaction.reason}`}
                      </Text>
                    </div>
                    <div style={{ marginTop: 2 }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        库位: {transaction.location}
                      </Text>
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </TabPane>
        </Tabs>
      </Modal>
    );
  };

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              批次跟踪
            </Title>
            <Text type="secondary">
              全面跟踪物料批次的入库、出库、调整等全生命周期记录
            </Text>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索批次号、物料、供应商"
                allowClear
                style={{ width: 250 }}
                onSearch={setSearchKeyword}
              />
              <Select
                placeholder="状态"
                allowClear
                style={{ width: 100 }}
                value={statusFilter}
                onChange={setStatusFilter}
                options={[
                  { label: '可用', value: 'AVAILABLE' },
                  { label: '预留', value: 'RESERVED' },
                  { label: '过期', value: 'EXPIRED' },
                  { label: '损坏', value: 'DAMAGED' },
                  { label: '已消耗', value: 'CONSUMED' },
                ]}
              />
              <Select
                placeholder="供应商"
                allowClear
                style={{ width: 120 }}
                value={supplierFilter}
                onChange={setSupplierFilter}
                options={[
                  { label: '深圳射频科技', value: '深圳射频科技' },
                  { label: '苏州电路板厂', value: '苏州电路板厂' },
                  { label: '村田制作所', value: '村田制作所' },
                ]}
              />
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                value={dateRange}
                onChange={handleDateRangeChange}
                style={{ width: 240 }}
              />
              <Button icon={<FilterOutlined />}>
                高级筛选
              </Button>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>

        <Alert
          message="批次跟踪提示"
          description="系统自动记录每个批次的完整生命周期，包括入库、出库、调整、转移等所有操作，确保物料的完全可追溯性。"
          type="info"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<span><BarChartOutlined />统计概览</span>} key="overview">
            {renderStatistics()}
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Card title="批次状态分布" size="small">
                  <Text type="secondary">批次状态分布图表将在此显示</Text>
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card title="库龄分析" size="small">
                  <Text type="secondary">库龄分析图表将在此显示</Text>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="批次清单" key="list">
            {renderStatistics()}
            <Table
              columns={batchColumns}
              dataSource={filteredBatches}
              loading={loading}
              rowKey="id"
              scroll={{ x: 1400 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {renderBatchDetail()}
    </div>
  );
};

export default BatchTrackingPage;