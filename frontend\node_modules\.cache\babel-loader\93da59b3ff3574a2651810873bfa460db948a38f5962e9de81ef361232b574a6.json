{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Input,Select,Modal,Form,Row,Col,Typography,Tag,Alert,Statistic,Tooltip,Badge,InputNumber,message}from'antd';import{PlusOutlined,EditOutlined,DeleteOutlined,ExportOutlined,ImportOutlined,WarningOutlined,ClockCircleOutlined,ScanOutlined}from'@ant-design/icons';import{formatCurrency}from'../../utils/format';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Search}=Input;const{TextArea}=Input;const{Option}=Select;// 备件接口定义\n// 库存预警接口\nconst SparePartsPage=()=>{const[loading,setLoading]=useState(false);const[spareParts,setSpareParts]=useState([]);const[stockAlerts,setStockAlerts]=useState([]);const[searchKeyword,setSearchKeyword]=useState('');const[categoryFilter,setCategoryFilter]=useState();const[statusFilter,setStatusFilter]=useState();const[criticalityFilter,setCriticalityFilter]=useState();const[sparePartModalVisible,setSparePartModalVisible]=useState(false);const[editingSparePart,setEditingSparePart]=useState(null);const[form]=Form.useForm();// 模拟数据\nconst mockSpareParts=[{id:'1',partNumber:'SP-001',partName:'伺服电机',category:'电机',specification:'1.5KW 220V',brand:'三菱',supplier:'三菱电机',unitPrice:2800,currency:'CNY',stockQuantity:5,minStockLevel:3,maxStockLevel:15,safetyStock:2,leadTime:14,location:'A-01-01',status:'active',lastPurchaseDate:'2024-01-15',lastUsageDate:'2024-01-10',usageFrequency:12,criticality:'high',compatibleDevices:['设备A','设备B'],interchangeableParts:['SP-002'],warrantyPeriod:24,storageConditions:'常温干燥',notes:'关键备件，需保证库存',createdAt:'2024-01-01',updatedAt:'2024-01-15'},{id:'2',partNumber:'SP-002',partName:'轴承',category:'机械件',specification:'6205-2RS',brand:'SKF',supplier:'SKF中国',unitPrice:85,currency:'CNY',stockQuantity:2,minStockLevel:5,maxStockLevel:20,safetyStock:3,leadTime:7,location:'B-02-03',status:'active',lastPurchaseDate:'2024-01-08',lastUsageDate:'2024-01-12',usageFrequency:24,criticality:'medium',compatibleDevices:['设备A','设备C','设备D'],interchangeableParts:[],warrantyPeriod:12,storageConditions:'防潮防尘',notes:'通用备件',createdAt:'2024-01-01',updatedAt:'2024-01-08'},{id:'3',partNumber:'SP-003',partName:'密封圈',category:'密封件',specification:'O型圈 φ50×3',brand:'通用',supplier:'本地供应商',unitPrice:12,currency:'CNY',stockQuantity:25,minStockLevel:10,maxStockLevel:50,safetyStock:5,leadTime:3,location:'C-01-05',status:'active',lastPurchaseDate:'2024-01-20',lastUsageDate:'2024-01-18',usageFrequency:36,criticality:'low',compatibleDevices:['设备B','设备C'],interchangeableParts:['SP-004'],warrantyPeriod:6,storageConditions:'避光保存',notes:'易损件，使用频繁',createdAt:'2024-01-01',updatedAt:'2024-01-20'}];const mockStockAlerts=[{id:'1',partNumber:'SP-002',partName:'轴承',currentStock:2,minLevel:5,alertType:'low_stock',urgency:'high',suggestedAction:'立即采购 10 个'},{id:'2',partNumber:'SP-001',partName:'伺服电机',currentStock:5,minLevel:3,alertType:'low_stock',urgency:'medium',suggestedAction:'建议采购 8 个'}];useEffect(()=>{loadData();},[]);const loadData=async()=>{setLoading(true);try{// 模拟API调用\nawait new Promise(resolve=>setTimeout(resolve,1000));setSpareParts(mockSpareParts);setStockAlerts(mockStockAlerts);}catch(error){message.error('加载数据失败');}finally{setLoading(false);}};const handleAddSparePart=()=>{setEditingSparePart(null);form.resetFields();setSparePartModalVisible(true);};const handleEditSparePart=record=>{setEditingSparePart(record);form.setFieldsValue(record);setSparePartModalVisible(true);};const handleDeleteSparePart=record=>{Modal.confirm({title:'确认删除',content:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u5907\\u4EF6 \\\"\".concat(record.partName,\"\\\" \\u5417\\uFF1F\"),onOk:()=>{message.success('删除成功');loadData();}});};const handleSparePartModalOk=async()=>{try{const values=await form.validateFields();console.log('备件数据:',values);message.success(editingSparePart?'更新成功':'创建成功');setSparePartModalVisible(false);loadData();}catch(error){console.error('表单验证失败:',error);}};const getStockStatus=part=>{if(part.stockQuantity<=0){return{status:'error',text:'缺货'};}else if(part.stockQuantity<=part.minStockLevel){return{status:'warning',text:'库存不足'};}else if(part.stockQuantity>=part.maxStockLevel){return{status:'processing',text:'库存过多'};}else{return{status:'success',text:'正常'};}};const getCriticalityColor=criticality=>{switch(criticality){case'high':return'red';case'medium':return'orange';case'low':return'green';default:return'default';}};const getStatusColor=status=>{switch(status){case'active':return'green';case'inactive':return'orange';case'discontinued':return'red';default:return'default';}};const sparePartColumns=[{title:'备件编号',dataIndex:'partNumber',key:'partNumber',width:120,fixed:'left'},{title:'备件名称',dataIndex:'partName',key:'partName',width:150,fixed:'left'},{title:'类别',dataIndex:'category',key:'category',width:100},{title:'规格型号',dataIndex:'specification',key:'specification',width:150},{title:'品牌',dataIndex:'brand',key:'brand',width:100},{title:'当前库存',dataIndex:'stockQuantity',key:'stockQuantity',width:100,render:(quantity,record)=>{const stockStatus=getStockStatus(record);return/*#__PURE__*/_jsx(Badge,{status:stockStatus.status,text:\"\".concat(quantity,\" \\u4E2A\")});}},{title:'库存状态',key:'stockStatus',width:100,render:(_,record)=>{const stockStatus=getStockStatus(record);return/*#__PURE__*/_jsx(Tag,{color:stockStatus.status==='success'?'green':stockStatus.status==='warning'?'orange':'red',children:stockStatus.text});}},{title:'最小库存',dataIndex:'minStockLevel',key:'minStockLevel',width:100,render:level=>\"\".concat(level,\" \\u4E2A\")},{title:'单价',dataIndex:'unitPrice',key:'unitPrice',width:100,render:price=>formatCurrency(price)},{title:'重要性',dataIndex:'criticality',key:'criticality',width:100,render:criticality=>/*#__PURE__*/_jsx(Tag,{color:getCriticalityColor(criticality),children:criticality==='high'?'高':criticality==='medium'?'中':'低'})},{title:'状态',dataIndex:'status',key:'status',width:100,render:status=>/*#__PURE__*/_jsx(Tag,{color:getStatusColor(status),children:status==='active'?'启用':status==='inactive'?'停用':'停产'})},{title:'库位',dataIndex:'location',key:'location',width:100},{title:'操作',key:'action',width:150,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEditSparePart(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>handleDeleteSparePart(record)})})]})}];const alertColumns=[{title:'备件编号',dataIndex:'partNumber',key:'partNumber'},{title:'备件名称',dataIndex:'partName',key:'partName'},{title:'当前库存',dataIndex:'currentStock',key:'currentStock',render:stock=>\"\".concat(stock,\" \\u4E2A\")},{title:'最小库存',dataIndex:'minLevel',key:'minLevel',render:level=>\"\".concat(level,\" \\u4E2A\")},{title:'预警类型',dataIndex:'alertType',key:'alertType',render:type=>{const typeMap={low_stock:{text:'库存不足',color:'orange'},out_of_stock:{text:'缺货',color:'red'},overstock:{text:'库存过多',color:'blue'}};const config=typeMap[type];return/*#__PURE__*/_jsx(Tag,{color:config.color,children:config.text});}},{title:'紧急程度',dataIndex:'urgency',key:'urgency',render:urgency=>{const urgencyMap={high:{text:'高',color:'red'},medium:{text:'中',color:'orange'},low:{text:'低',color:'green'}};const config=urgencyMap[urgency];return/*#__PURE__*/_jsx(Tag,{color:config.color,children:config.text});}},{title:'建议操作',dataIndex:'suggestedAction',key:'suggestedAction'}];// 统计数据\nconst totalParts=spareParts.length;const lowStockParts=spareParts.filter(part=>part.stockQuantity<=part.minStockLevel).length;const outOfStockParts=spareParts.filter(part=>part.stockQuantity<=0).length;const totalValue=spareParts.reduce((sum,part)=>sum+part.stockQuantity*part.unitPrice,0);return/*#__PURE__*/_jsxs(\"div\",{children:[stockAlerts.length>0&&/*#__PURE__*/_jsx(Alert,{message:\"\\u5E93\\u5B58\\u9884\\u8B66\",description:\"\\u5F53\\u524D\\u6709 \".concat(stockAlerts.length,\" \\u4E2A\\u5907\\u4EF6\\u9700\\u8981\\u5173\\u6CE8\\uFF0C\\u5176\\u4E2D \").concat(stockAlerts.filter(a=>a.urgency==='high').length,\" \\u4E2A\\u9AD8\\u4F18\\u5148\\u7EA7\\u9884\\u8B66\\u3002\"),type:\"warning\",showIcon:true,closable:true,style:{marginBottom:16},action:/*#__PURE__*/_jsx(Button,{size:\"small\",type:\"link\",children:\"\\u67E5\\u770B\\u8BE6\\u60C5\"})}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5907\\u4EF6\\u603B\\u6570\",value:totalParts,suffix:\"\\u4E2A\",valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E93\\u5B58\\u4E0D\\u8DB3\",value:lowStockParts,suffix:\"\\u4E2A\",valueStyle:{color:'#faad14'},prefix:/*#__PURE__*/_jsx(WarningOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7F3A\\u8D27\",value:outOfStockParts,suffix:\"\\u4E2A\",valueStyle:{color:'#f5222d'},prefix:/*#__PURE__*/_jsx(ClockCircleOutlined,{})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E93\\u5B58\\u603B\\u4EF7\\u503C\",value:totalValue,precision:2,formatter:value=>formatCurrency(Number(value)),valueStyle:{color:'#52c41a'}})})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u5907\\u4EF6\\u7BA1\\u7406\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u7BA1\\u7406\\u8BBE\\u5907\\u5907\\u4EF6\\u5E93\\u5B58\\uFF0C\\u5305\\u62EC\\u5907\\u4EF6\\u4FE1\\u606F\\u3001\\u5E93\\u5B58\\u6C34\\u5E73\\u3001\\u91C7\\u8D2D\\u5EFA\\u8BAE\\u7B49\"})]}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u5907\\u4EF6\\u7F16\\u53F7\\u3001\\u540D\\u79F0\",allowClear:true,style:{width:200},onSearch:setSearchKeyword}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u7C7B\\u522B\",allowClear:true,style:{width:100},value:categoryFilter,onChange:setCategoryFilter,options:[{label:'电机',value:'电机'},{label:'机械件',value:'机械件'},{label:'密封件',value:'密封件'},{label:'电气件',value:'电气件'},{label:'液压件',value:'液压件'}]}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u72B6\\u6001\",allowClear:true,style:{width:100},value:statusFilter,onChange:setStatusFilter,options:[{label:'启用',value:'active'},{label:'停用',value:'inactive'},{label:'停产',value:'discontinued'}]}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u91CD\\u8981\\u6027\",allowClear:true,style:{width:100},value:criticalityFilter,onChange:setCriticalityFilter,options:[{label:'高',value:'high'},{label:'中',value:'medium'},{label:'低',value:'low'}]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ScanOutlined,{}),children:\"\\u626B\\u7801\\u67E5\\u8BE2\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ImportOutlined,{}),children:\"\\u5BFC\\u5165\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAddSparePart,children:\"\\u65B0\\u589E\\u5907\\u4EF6\"})]})})]}),/*#__PURE__*/_jsx(Table,{columns:sparePartColumns,dataSource:spareParts,loading:loading,rowKey:\"id\",scroll:{x:1500},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),stockAlerts.length>0&&/*#__PURE__*/_jsx(Card,{title:\"\\u5E93\\u5B58\\u9884\\u8B66\\u8BE6\\u60C5\",style:{marginTop:16},children:/*#__PURE__*/_jsx(Table,{columns:alertColumns,dataSource:stockAlerts,rowKey:\"id\",pagination:false,size:\"small\"})}),/*#__PURE__*/_jsx(Modal,{title:editingSparePart?'编辑备件':'新增备件',open:sparePartModalVisible,onOk:handleSparePartModalOk,onCancel:()=>setSparePartModalVisible(false),width:800,okText:\"\\u4FDD\\u5B58\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Form,{form:form,layout:\"vertical\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"partNumber\",label:\"\\u5907\\u4EF6\\u7F16\\u53F7\",rules:[{required:true,message:'请输入备件编号'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5907\\u4EF6\\u7F16\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"partName\",label:\"\\u5907\\u4EF6\\u540D\\u79F0\",rules:[{required:true,message:'请输入备件名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5907\\u4EF6\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"category\",label:\"\\u7C7B\\u522B\",rules:[{required:true,message:'请选择类别'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u7C7B\\u522B\",children:[/*#__PURE__*/_jsx(Option,{value:\"\\u7535\\u673A\",children:\"\\u7535\\u673A\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u673A\\u68B0\\u4EF6\",children:\"\\u673A\\u68B0\\u4EF6\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u5BC6\\u5C01\\u4EF6\",children:\"\\u5BC6\\u5C01\\u4EF6\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u7535\\u6C14\\u4EF6\",children:\"\\u7535\\u6C14\\u4EF6\"}),/*#__PURE__*/_jsx(Option,{value:\"\\u6DB2\\u538B\\u4EF6\",children:\"\\u6DB2\\u538B\\u4EF6\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"specification\",label:\"\\u89C4\\u683C\\u578B\\u53F7\",rules:[{required:true,message:'请输入规格型号'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u89C4\\u683C\\u578B\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"brand\",label:\"\\u54C1\\u724C\",rules:[{required:true,message:'请输入品牌'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u54C1\\u724C\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"supplier\",label:\"\\u4F9B\\u5E94\\u5546\",rules:[{required:true,message:'请输入供应商'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u4F9B\\u5E94\\u5546\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"unitPrice\",label:\"\\u5355\\u4EF7\",rules:[{required:true,message:'请输入单价'}],children:/*#__PURE__*/_jsx(InputNumber,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5355\\u4EF7\",style:{width:'100%'},min:0,precision:2,addonAfter:\"\\u5143\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"stockQuantity\",label:\"\\u5F53\\u524D\\u5E93\\u5B58\",rules:[{required:true,message:'请输入当前库存'}],children:/*#__PURE__*/_jsx(InputNumber,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5F53\\u524D\\u5E93\\u5B58\",style:{width:'100%'},min:0,addonAfter:\"\\u4E2A\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"minStockLevel\",label:\"\\u6700\\u5C0F\\u5E93\\u5B58\",rules:[{required:true,message:'请输入最小库存'}],children:/*#__PURE__*/_jsx(InputNumber,{placeholder:\"\\u6700\\u5C0F\\u5E93\\u5B58\",style:{width:'100%'},min:0,addonAfter:\"\\u4E2A\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"maxStockLevel\",label:\"\\u6700\\u5927\\u5E93\\u5B58\",rules:[{required:true,message:'请输入最大库存'}],children:/*#__PURE__*/_jsx(InputNumber,{placeholder:\"\\u6700\\u5927\\u5E93\\u5B58\",style:{width:'100%'},min:0,addonAfter:\"\\u4E2A\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"safetyStock\",label:\"\\u5B89\\u5168\\u5E93\\u5B58\",rules:[{required:true,message:'请输入安全库存'}],children:/*#__PURE__*/_jsx(InputNumber,{placeholder:\"\\u5B89\\u5168\\u5E93\\u5B58\",style:{width:'100%'},min:0,addonAfter:\"\\u4E2A\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"leadTime\",label:\"\\u91C7\\u8D2D\\u5468\\u671F\",rules:[{required:true,message:'请输入采购周期'}],children:/*#__PURE__*/_jsx(InputNumber,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u91C7\\u8D2D\\u5468\\u671F\",style:{width:'100%'},min:0,addonAfter:\"\\u5929\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"location\",label:\"\\u5E93\\u4F4D\",rules:[{required:true,message:'请输入库位'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5E93\\u4F4D\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"criticality\",label:\"\\u91CD\\u8981\\u6027\",rules:[{required:true,message:'请选择重要性'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u91CD\\u8981\\u6027\",children:[/*#__PURE__*/_jsx(Option,{value:\"high\",children:\"\\u9AD8\"}),/*#__PURE__*/_jsx(Option,{value:\"medium\",children:\"\\u4E2D\"}),/*#__PURE__*/_jsx(Option,{value:\"low\",children:\"\\u4F4E\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"status\",label:\"\\u72B6\\u6001\",rules:[{required:true,message:'请选择状态'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",children:[/*#__PURE__*/_jsx(Option,{value:\"active\",children:\"\\u542F\\u7528\"}),/*#__PURE__*/_jsx(Option,{value:\"inactive\",children:\"\\u505C\\u7528\"}),/*#__PURE__*/_jsx(Option,{value:\"discontinued\",children:\"\\u505C\\u4EA7\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"warrantyPeriod\",label:\"\\u4FDD\\u4FEE\\u671F\",children:/*#__PURE__*/_jsx(InputNumber,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u4FDD\\u4FEE\\u671F\",style:{width:'100%'},min:0,addonAfter:\"\\u6708\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"storageConditions\",label:\"\\u5B58\\u50A8\\u6761\\u4EF6\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5B58\\u50A8\\u6761\\u4EF6\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,children:/*#__PURE__*/_jsx(Form.Item,{name:\"notes\",label:\"\\u5907\\u6CE8\",children:/*#__PURE__*/_jsx(TextArea,{rows:3,placeholder:\"\\u8BF7\\u8F93\\u5165\\u5907\\u6CE8\\u4FE1\\u606F\"})})})]})})})]});};export default SparePartsPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Modal", "Form", "Row", "Col", "Typography", "Tag", "<PERSON><PERSON>", "Statistic", "<PERSON><PERSON><PERSON>", "Badge", "InputNumber", "message", "PlusOutlined", "EditOutlined", "DeleteOutlined", "ExportOutlined", "ImportOutlined", "WarningOutlined", "ClockCircleOutlined", "ScanOutlined", "formatCurrency", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Search", "TextArea", "Option", "SparePartsPage", "loading", "setLoading", "spareParts", "setSpareParts", "stockAlerts", "setStockAlerts", "searchKeyword", "setSearchKeyword", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilter", "setStatus<PERSON>ilter", "criticalityFilter", "setCriticalityFilter", "sparePartModalVisible", "setSparePartModalVisible", "editingSparePart", "setEditingSparePart", "form", "useForm", "mockSpareParts", "id", "partNumber", "partName", "category", "specification", "brand", "supplier", "unitPrice", "currency", "stockQuantity", "minStockLevel", "maxStockLevel", "safetyStock", "leadTime", "location", "status", "lastPurchaseDate", "lastUsageDate", "usageFrequency", "criticality", "compatibleDevices", "interchangeableParts", "warrantyPeriod", "storageConditions", "notes", "createdAt", "updatedAt", "mockStockAlerts", "currentStock", "minLevel", "alertType", "urgency", "suggestedAction", "loadData", "Promise", "resolve", "setTimeout", "error", "handleAddSparePart", "resetFields", "handleEditSparePart", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDeleteSparePart", "confirm", "title", "content", "concat", "onOk", "success", "handleSparePartModalOk", "values", "validateFields", "console", "log", "getStockStatus", "part", "text", "getCriticalityColor", "getStatusColor", "sparePartColumns", "dataIndex", "key", "width", "fixed", "render", "quantity", "stockStatus", "_", "color", "children", "level", "price", "size", "type", "icon", "onClick", "danger", "alertColumns", "stock", "typeMap", "low_stock", "out_of_stock", "overstock", "config", "urgencyMap", "high", "medium", "low", "totalParts", "length", "lowStockParts", "filter", "outOfStockParts", "totalValue", "reduce", "sum", "description", "a", "showIcon", "closable", "style", "marginBottom", "action", "gutter", "xs", "sm", "md", "value", "suffix", "valueStyle", "prefix", "precision", "formatter", "Number", "justify", "align", "margin", "placeholder", "allowClear", "onSearch", "onChange", "options", "label", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "marginTop", "open", "onCancel", "okText", "cancelText", "layout", "<PERSON><PERSON>", "name", "rules", "required", "min", "addonAfter", "rows"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/service/SparePartsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Modal,\n  Form,\n  Row,\n  Col,\n  Typography,\n  Tag,\n  Alert,\n  Statistic,\n  Progress,\n  Tooltip,\n  Badge,\n  Divider,\n  InputNumber,\n  DatePicker,\n  Upload,\n  message,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  ScanOutlined,\n  UploadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { formatCurrency } from '../../utils/format';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\n// 备件接口定义\ninterface SparePart {\n  id: string;\n  partNumber: string;\n  partName: string;\n  category: string;\n  specification: string;\n  brand: string;\n  supplier: string;\n  unitPrice: number;\n  currency: string;\n  stockQuantity: number;\n  minStockLevel: number;\n  maxStockLevel: number;\n  safetyStock: number;\n  leadTime: number; // 采购周期（天）\n  location: string;\n  status: 'active' | 'inactive' | 'discontinued';\n  lastPurchaseDate: string;\n  lastUsageDate: string;\n  usageFrequency: number; // 年使用频率\n  criticality: 'high' | 'medium' | 'low';\n  compatibleDevices: string[];\n  interchangeableParts: string[];\n  warrantyPeriod: number; // 保修期（月）\n  storageConditions: string;\n  notes: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 库存预警接口\ninterface StockAlert {\n  id: string;\n  partNumber: string;\n  partName: string;\n  currentStock: number;\n  minLevel: number;\n  alertType: 'low_stock' | 'out_of_stock' | 'overstock';\n  urgency: 'high' | 'medium' | 'low';\n  suggestedAction: string;\n}\n\nconst SparePartsPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [spareParts, setSpareParts] = useState<SparePart[]>([]);\n  const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([]);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState<string | undefined>();\n  const [statusFilter, setStatusFilter] = useState<string | undefined>();\n  const [criticalityFilter, setCriticalityFilter] = useState<string | undefined>();\n  const [sparePartModalVisible, setSparePartModalVisible] = useState(false);\n  const [editingSparePart, setEditingSparePart] = useState<SparePart | null>(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockSpareParts: SparePart[] = [\n    {\n      id: '1',\n      partNumber: 'SP-001',\n      partName: '伺服电机',\n      category: '电机',\n      specification: '1.5KW 220V',\n      brand: '三菱',\n      supplier: '三菱电机',\n      unitPrice: 2800,\n      currency: 'CNY',\n      stockQuantity: 5,\n      minStockLevel: 3,\n      maxStockLevel: 15,\n      safetyStock: 2,\n      leadTime: 14,\n      location: 'A-01-01',\n      status: 'active',\n      lastPurchaseDate: '2024-01-15',\n      lastUsageDate: '2024-01-10',\n      usageFrequency: 12,\n      criticality: 'high',\n      compatibleDevices: ['设备A', '设备B'],\n      interchangeableParts: ['SP-002'],\n      warrantyPeriod: 24,\n      storageConditions: '常温干燥',\n      notes: '关键备件，需保证库存',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-15',\n    },\n    {\n      id: '2',\n      partNumber: 'SP-002',\n      partName: '轴承',\n      category: '机械件',\n      specification: '6205-2RS',\n      brand: 'SKF',\n      supplier: 'SKF中国',\n      unitPrice: 85,\n      currency: 'CNY',\n      stockQuantity: 2,\n      minStockLevel: 5,\n      maxStockLevel: 20,\n      safetyStock: 3,\n      leadTime: 7,\n      location: 'B-02-03',\n      status: 'active',\n      lastPurchaseDate: '2024-01-08',\n      lastUsageDate: '2024-01-12',\n      usageFrequency: 24,\n      criticality: 'medium',\n      compatibleDevices: ['设备A', '设备C', '设备D'],\n      interchangeableParts: [],\n      warrantyPeriod: 12,\n      storageConditions: '防潮防尘',\n      notes: '通用备件',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-08',\n    },\n    {\n      id: '3',\n      partNumber: 'SP-003',\n      partName: '密封圈',\n      category: '密封件',\n      specification: 'O型圈 φ50×3',\n      brand: '通用',\n      supplier: '本地供应商',\n      unitPrice: 12,\n      currency: 'CNY',\n      stockQuantity: 25,\n      minStockLevel: 10,\n      maxStockLevel: 50,\n      safetyStock: 5,\n      leadTime: 3,\n      location: 'C-01-05',\n      status: 'active',\n      lastPurchaseDate: '2024-01-20',\n      lastUsageDate: '2024-01-18',\n      usageFrequency: 36,\n      criticality: 'low',\n      compatibleDevices: ['设备B', '设备C'],\n      interchangeableParts: ['SP-004'],\n      warrantyPeriod: 6,\n      storageConditions: '避光保存',\n      notes: '易损件，使用频繁',\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-20',\n    },\n  ];\n\n  const mockStockAlerts: StockAlert[] = [\n    {\n      id: '1',\n      partNumber: 'SP-002',\n      partName: '轴承',\n      currentStock: 2,\n      minLevel: 5,\n      alertType: 'low_stock',\n      urgency: 'high',\n      suggestedAction: '立即采购 10 个',\n    },\n    {\n      id: '2',\n      partNumber: 'SP-001',\n      partName: '伺服电机',\n      currentStock: 5,\n      minLevel: 3,\n      alertType: 'low_stock',\n      urgency: 'medium',\n      suggestedAction: '建议采购 8 个',\n    },\n  ];\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSpareParts(mockSpareParts);\n      setStockAlerts(mockStockAlerts);\n    } catch (error) {\n      message.error('加载数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddSparePart = () => {\n    setEditingSparePart(null);\n    form.resetFields();\n    setSparePartModalVisible(true);\n  };\n\n  const handleEditSparePart = (record: SparePart) => {\n    setEditingSparePart(record);\n    form.setFieldsValue(record);\n    setSparePartModalVisible(true);\n  };\n\n  const handleDeleteSparePart = (record: SparePart) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除备件 \"${record.partName}\" 吗？`,\n      onOk: () => {\n        message.success('删除成功');\n        loadData();\n      },\n    });\n  };\n\n  const handleSparePartModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('备件数据:', values);\n      message.success(editingSparePart ? '更新成功' : '创建成功');\n      setSparePartModalVisible(false);\n      loadData();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const getStockStatus = (part: SparePart) => {\n    if (part.stockQuantity <= 0) {\n      return { status: 'error', text: '缺货' };\n    } else if (part.stockQuantity <= part.minStockLevel) {\n      return { status: 'warning', text: '库存不足' };\n    } else if (part.stockQuantity >= part.maxStockLevel) {\n      return { status: 'processing', text: '库存过多' };\n    } else {\n      return { status: 'success', text: '正常' };\n    }\n  };\n\n  const getCriticalityColor = (criticality: string) => {\n    switch (criticality) {\n      case 'high': return 'red';\n      case 'medium': return 'orange';\n      case 'low': return 'green';\n      default: return 'default';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': return 'green';\n      case 'inactive': return 'orange';\n      case 'discontinued': return 'red';\n      default: return 'default';\n    }\n  };\n\n  const sparePartColumns: ColumnsType<SparePart> = [\n    {\n      title: '备件编号',\n      dataIndex: 'partNumber',\n      key: 'partNumber',\n      width: 120,\n      fixed: 'left',\n    },\n    {\n      title: '备件名称',\n      dataIndex: 'partName',\n      key: 'partName',\n      width: 150,\n      fixed: 'left',\n    },\n    {\n      title: '类别',\n      dataIndex: 'category',\n      key: 'category',\n      width: 100,\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      key: 'specification',\n      width: 150,\n    },\n    {\n      title: '品牌',\n      dataIndex: 'brand',\n      key: 'brand',\n      width: 100,\n    },\n    {\n      title: '当前库存',\n      dataIndex: 'stockQuantity',\n      key: 'stockQuantity',\n      width: 100,\n      render: (quantity: number, record: SparePart) => {\n        const stockStatus = getStockStatus(record);\n        return (\n          <Badge \n            status={stockStatus.status as any} \n            text={`${quantity} 个`}\n          />\n        );\n      },\n    },\n    {\n      title: '库存状态',\n      key: 'stockStatus',\n      width: 100,\n      render: (_, record: SparePart) => {\n        const stockStatus = getStockStatus(record);\n        return (\n          <Tag color={stockStatus.status === 'success' ? 'green' : \n                     stockStatus.status === 'warning' ? 'orange' : 'red'}>\n            {stockStatus.text}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '最小库存',\n      dataIndex: 'minStockLevel',\n      key: 'minStockLevel',\n      width: 100,\n      render: (level: number) => `${level} 个`,\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      width: 100,\n      render: (price: number) => formatCurrency(price),\n    },\n    {\n      title: '重要性',\n      dataIndex: 'criticality',\n      key: 'criticality',\n      width: 100,\n      render: (criticality: string) => (\n        <Tag color={getCriticalityColor(criticality)}>\n          {criticality === 'high' ? '高' : \n           criticality === 'medium' ? '中' : '低'}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {status === 'active' ? '启用' : \n           status === 'inactive' ? '停用' : '停产'}\n        </Tag>\n      ),\n    },\n    {\n      title: '库位',\n      dataIndex: 'location',\n      key: 'location',\n      width: 100,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right',\n      render: (_, record: SparePart) => (\n        <Space size=\"small\">\n          <Tooltip title=\"编辑\">\n            <Button \n              type=\"text\" \n              icon={<EditOutlined />} \n              onClick={() => handleEditSparePart(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button \n              type=\"text\" \n              danger \n              icon={<DeleteOutlined />} \n              onClick={() => handleDeleteSparePart(record)}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  const alertColumns: ColumnsType<StockAlert> = [\n    {\n      title: '备件编号',\n      dataIndex: 'partNumber',\n      key: 'partNumber',\n    },\n    {\n      title: '备件名称',\n      dataIndex: 'partName',\n      key: 'partName',\n    },\n    {\n      title: '当前库存',\n      dataIndex: 'currentStock',\n      key: 'currentStock',\n      render: (stock: number) => `${stock} 个`,\n    },\n    {\n      title: '最小库存',\n      dataIndex: 'minLevel',\n      key: 'minLevel',\n      render: (level: number) => `${level} 个`,\n    },\n    {\n      title: '预警类型',\n      dataIndex: 'alertType',\n      key: 'alertType',\n      render: (type: string) => {\n        const typeMap = {\n          low_stock: { text: '库存不足', color: 'orange' },\n          out_of_stock: { text: '缺货', color: 'red' },\n          overstock: { text: '库存过多', color: 'blue' },\n        };\n        const config = typeMap[type as keyof typeof typeMap];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n    {\n      title: '紧急程度',\n      dataIndex: 'urgency',\n      key: 'urgency',\n      render: (urgency: string) => {\n        const urgencyMap = {\n          high: { text: '高', color: 'red' },\n          medium: { text: '中', color: 'orange' },\n          low: { text: '低', color: 'green' },\n        };\n        const config = urgencyMap[urgency as keyof typeof urgencyMap];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n    {\n      title: '建议操作',\n      dataIndex: 'suggestedAction',\n      key: 'suggestedAction',\n    },\n  ];\n\n  // 统计数据\n  const totalParts = spareParts.length;\n  const lowStockParts = spareParts.filter(part => part.stockQuantity <= part.minStockLevel).length;\n  const outOfStockParts = spareParts.filter(part => part.stockQuantity <= 0).length;\n  const totalValue = spareParts.reduce((sum, part) => sum + (part.stockQuantity * part.unitPrice), 0);\n\n  return (\n    <div>\n      {/* 库存预警 */}\n      {stockAlerts.length > 0 && (\n        <Alert\n          message=\"库存预警\"\n          description={`当前有 ${stockAlerts.length} 个备件需要关注，其中 ${stockAlerts.filter(a => a.urgency === 'high').length} 个高优先级预警。`}\n          type=\"warning\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n          action={\n            <Button size=\"small\" type=\"link\">\n              查看详情\n            </Button>\n          }\n        />\n      )}\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"备件总数\"\n              value={totalParts}\n              suffix=\"个\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"库存不足\"\n              value={lowStockParts}\n              suffix=\"个\"\n              valueStyle={{ color: '#faad14' }}\n              prefix={<WarningOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"缺货\"\n              value={outOfStockParts}\n              suffix=\"个\"\n              valueStyle={{ color: '#f5222d' }}\n              prefix={<ClockCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"库存总价值\"\n              value={totalValue}\n              precision={2}\n              formatter={(value) => formatCurrency(Number(value))}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 主要内容 */}\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              备件管理\n            </Title>\n            <Text type=\"secondary\">\n              管理设备备件库存，包括备件信息、库存水平、采购建议等\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索备件编号、名称\"\n                allowClear\n                style={{ width: 200 }}\n                onSearch={setSearchKeyword}\n              />\n              <Select\n                placeholder=\"类别\"\n                allowClear\n                style={{ width: 100 }}\n                value={categoryFilter}\n                onChange={setCategoryFilter}\n                options={[\n                  { label: '电机', value: '电机' },\n                  { label: '机械件', value: '机械件' },\n                  { label: '密封件', value: '密封件' },\n                  { label: '电气件', value: '电气件' },\n                  { label: '液压件', value: '液压件' },\n                ]}\n              />\n              <Select\n                placeholder=\"状态\"\n                allowClear\n                style={{ width: 100 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '启用', value: 'active' },\n                  { label: '停用', value: 'inactive' },\n                  { label: '停产', value: 'discontinued' },\n                ]}\n              />\n              <Select\n                placeholder=\"重要性\"\n                allowClear\n                style={{ width: 100 }}\n                value={criticalityFilter}\n                onChange={setCriticalityFilter}\n                options={[\n                  { label: '高', value: 'high' },\n                  { label: '中', value: 'medium' },\n                  { label: '低', value: 'low' },\n                ]}\n              />\n              <Button icon={<ScanOutlined />}>\n                扫码查询\n              </Button>\n              <Button icon={<ExportOutlined />}>\n                导出\n              </Button>\n              <Button icon={<ImportOutlined />}>\n                导入\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddSparePart}>\n                新增备件\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Table\n          columns={sparePartColumns}\n          dataSource={spareParts}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1500 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 库存预警详情 */}\n      {stockAlerts.length > 0 && (\n        <Card title=\"库存预警详情\" style={{ marginTop: 16 }}>\n          <Table\n            columns={alertColumns}\n            dataSource={stockAlerts}\n            rowKey=\"id\"\n            pagination={false}\n            size=\"small\"\n          />\n        </Card>\n      )}\n\n      {/* 备件编辑模态框 */}\n      <Modal\n        title={editingSparePart ? '编辑备件' : '新增备件'}\n        open={sparePartModalVisible}\n        onOk={handleSparePartModalOk}\n        onCancel={() => setSparePartModalVisible(false)}\n        width={800}\n        okText=\"保存\"\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"partNumber\"\n                label=\"备件编号\"\n                rules={[{ required: true, message: '请输入备件编号' }]}\n              >\n                <Input placeholder=\"请输入备件编号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"partName\"\n                label=\"备件名称\"\n                rules={[{ required: true, message: '请输入备件名称' }]}\n              >\n                <Input placeholder=\"请输入备件名称\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"category\"\n                label=\"类别\"\n                rules={[{ required: true, message: '请选择类别' }]}\n              >\n                <Select placeholder=\"请选择类别\">\n                  <Option value=\"电机\">电机</Option>\n                  <Option value=\"机械件\">机械件</Option>\n                  <Option value=\"密封件\">密封件</Option>\n                  <Option value=\"电气件\">电气件</Option>\n                  <Option value=\"液压件\">液压件</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"specification\"\n                label=\"规格型号\"\n                rules={[{ required: true, message: '请输入规格型号' }]}\n              >\n                <Input placeholder=\"请输入规格型号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"brand\"\n                label=\"品牌\"\n                rules={[{ required: true, message: '请输入品牌' }]}\n              >\n                <Input placeholder=\"请输入品牌\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"supplier\"\n                label=\"供应商\"\n                rules={[{ required: true, message: '请输入供应商' }]}\n              >\n                <Input placeholder=\"请输入供应商\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"unitPrice\"\n                label=\"单价\"\n                rules={[{ required: true, message: '请输入单价' }]}\n              >\n                <InputNumber\n                  placeholder=\"请输入单价\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  precision={2}\n                  addonAfter=\"元\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"stockQuantity\"\n                label=\"当前库存\"\n                rules={[{ required: true, message: '请输入当前库存' }]}\n              >\n                <InputNumber\n                  placeholder=\"请输入当前库存\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"个\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"minStockLevel\"\n                label=\"最小库存\"\n                rules={[{ required: true, message: '请输入最小库存' }]}\n              >\n                <InputNumber\n                  placeholder=\"最小库存\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"个\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"maxStockLevel\"\n                label=\"最大库存\"\n                rules={[{ required: true, message: '请输入最大库存' }]}\n              >\n                <InputNumber\n                  placeholder=\"最大库存\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"个\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"safetyStock\"\n                label=\"安全库存\"\n                rules={[{ required: true, message: '请输入安全库存' }]}\n              >\n                <InputNumber\n                  placeholder=\"安全库存\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"个\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"leadTime\"\n                label=\"采购周期\"\n                rules={[{ required: true, message: '请输入采购周期' }]}\n              >\n                <InputNumber\n                  placeholder=\"请输入采购周期\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"天\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"location\"\n                label=\"库位\"\n                rules={[{ required: true, message: '请输入库位' }]}\n              >\n                <Input placeholder=\"请输入库位\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"criticality\"\n                label=\"重要性\"\n                rules={[{ required: true, message: '请选择重要性' }]}\n              >\n                <Select placeholder=\"请选择重要性\">\n                  <Option value=\"high\">高</Option>\n                  <Option value=\"medium\">中</Option>\n                  <Option value=\"low\">低</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择状态' }]}\n              >\n                <Select placeholder=\"请选择状态\">\n                  <Option value=\"active\">启用</Option>\n                  <Option value=\"inactive\">停用</Option>\n                  <Option value=\"discontinued\">停产</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"warrantyPeriod\"\n                label=\"保修期\"\n              >\n                <InputNumber\n                  placeholder=\"请输入保修期\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  addonAfter=\"月\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"storageConditions\"\n                label=\"存储条件\"\n              >\n                <Input placeholder=\"请输入存储条件\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24}>\n              <Form.Item\n                name=\"notes\"\n                label=\"备注\"\n              >\n                <TextArea rows={3} placeholder=\"请输入备注信息\" />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default SparePartsPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,GAAG,CACHC,GAAG,CACHC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,SAAS,CAETC,OAAO,CACPC,KAAK,CAELC,WAAW,CAGXC,OAAO,KACF,MAAM,CACb,OACEC,YAAY,CACZC,YAAY,CACZC,cAAc,CAEdC,cAAc,CACdC,cAAc,CACdC,eAAe,CAEfC,mBAAmB,CACnBC,YAAY,KAEP,mBAAmB,CAE1B,OAASC,cAAc,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGtB,UAAU,CAClC,KAAM,CAAEuB,MAAO,CAAC,CAAG7B,KAAK,CACxB,KAAM,CAAE8B,QAAS,CAAC,CAAG9B,KAAK,CAC1B,KAAM,CAAE+B,MAAO,CAAC,CAAG9B,MAAM,CAEzB;AA+BA;AAYA,KAAM,CAAA+B,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyC,UAAU,CAAEC,aAAa,CAAC,CAAG1C,QAAQ,CAAc,EAAE,CAAC,CAC7D,KAAM,CAAC2C,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAAC6C,aAAa,CAAEC,gBAAgB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC+C,cAAc,CAAEC,iBAAiB,CAAC,CAAGhD,QAAQ,CAAqB,CAAC,CAC1E,KAAM,CAACiD,YAAY,CAAEC,eAAe,CAAC,CAAGlD,QAAQ,CAAqB,CAAC,CACtE,KAAM,CAACmD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpD,QAAQ,CAAqB,CAAC,CAChF,KAAM,CAACqD,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CACzE,KAAM,CAACuD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxD,QAAQ,CAAmB,IAAI,CAAC,CAChF,KAAM,CAACyD,IAAI,CAAC,CAAGhD,IAAI,CAACiD,OAAO,CAAC,CAAC,CAE7B;AACA,KAAM,CAAAC,cAA2B,CAAG,CAClC,CACEC,EAAE,CAAE,GAAG,CACPC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,IAAI,CACdC,aAAa,CAAE,YAAY,CAC3BC,KAAK,CAAE,IAAI,CACXC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,IAAI,CACfC,QAAQ,CAAE,KAAK,CACfC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,EAAE,CACjBC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,SAAS,CACnBC,MAAM,CAAE,QAAQ,CAChBC,gBAAgB,CAAE,YAAY,CAC9BC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,EAAE,CAClBC,WAAW,CAAE,MAAM,CACnBC,iBAAiB,CAAE,CAAC,KAAK,CAAE,KAAK,CAAC,CACjCC,oBAAoB,CAAE,CAAC,QAAQ,CAAC,CAChCC,cAAc,CAAE,EAAE,CAClBC,iBAAiB,CAAE,MAAM,CACzBC,KAAK,CAAE,YAAY,CACnBC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACD,CACE1B,EAAE,CAAE,GAAG,CACPC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,KAAK,CACfC,aAAa,CAAE,UAAU,CACzBC,KAAK,CAAE,KAAK,CACZC,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,KAAK,CACfC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,EAAE,CACjBC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,SAAS,CACnBC,MAAM,CAAE,QAAQ,CAChBC,gBAAgB,CAAE,YAAY,CAC9BC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,EAAE,CAClBC,WAAW,CAAE,QAAQ,CACrBC,iBAAiB,CAAE,CAAC,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CACxCC,oBAAoB,CAAE,EAAE,CACxBC,cAAc,CAAE,EAAE,CAClBC,iBAAiB,CAAE,MAAM,CACzBC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACD,CACE1B,EAAE,CAAE,GAAG,CACPC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,KAAK,CACfC,aAAa,CAAE,WAAW,CAC1BC,KAAK,CAAE,IAAI,CACXC,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,KAAK,CACfC,aAAa,CAAE,EAAE,CACjBC,aAAa,CAAE,EAAE,CACjBC,aAAa,CAAE,EAAE,CACjBC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,SAAS,CACnBC,MAAM,CAAE,QAAQ,CAChBC,gBAAgB,CAAE,YAAY,CAC9BC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,EAAE,CAClBC,WAAW,CAAE,KAAK,CAClBC,iBAAiB,CAAE,CAAC,KAAK,CAAE,KAAK,CAAC,CACjCC,oBAAoB,CAAE,CAAC,QAAQ,CAAC,CAChCC,cAAc,CAAE,CAAC,CACjBC,iBAAiB,CAAE,MAAM,CACzBC,KAAK,CAAE,UAAU,CACjBC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACF,CAED,KAAM,CAAAC,eAA6B,CAAG,CACpC,CACE3B,EAAE,CAAE,GAAG,CACPC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,IAAI,CACd0B,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,WAAW,CACtBC,OAAO,CAAE,MAAM,CACfC,eAAe,CAAE,WACnB,CAAC,CACD,CACEhC,EAAE,CAAE,GAAG,CACPC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,MAAM,CAChB0B,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,WAAW,CACtBC,OAAO,CAAE,QAAQ,CACjBC,eAAe,CAAE,UACnB,CAAC,CACF,CAED3F,SAAS,CAAC,IAAM,CACd4F,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BrD,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,IAAI,CAAAsD,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CACvDrD,aAAa,CAACiB,cAAc,CAAC,CAC7Bf,cAAc,CAAC2C,eAAe,CAAC,CACjC,CAAE,MAAOU,KAAK,CAAE,CACd9E,OAAO,CAAC8E,KAAK,CAAC,QAAQ,CAAC,CACzB,CAAC,OAAS,CACRzD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA0D,kBAAkB,CAAGA,CAAA,GAAM,CAC/B1C,mBAAmB,CAAC,IAAI,CAAC,CACzBC,IAAI,CAAC0C,WAAW,CAAC,CAAC,CAClB7C,wBAAwB,CAAC,IAAI,CAAC,CAChC,CAAC,CAED,KAAM,CAAA8C,mBAAmB,CAAIC,MAAiB,EAAK,CACjD7C,mBAAmB,CAAC6C,MAAM,CAAC,CAC3B5C,IAAI,CAAC6C,cAAc,CAACD,MAAM,CAAC,CAC3B/C,wBAAwB,CAAC,IAAI,CAAC,CAChC,CAAC,CAED,KAAM,CAAAiD,qBAAqB,CAAIF,MAAiB,EAAK,CACnD7F,KAAK,CAACgG,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,iDAAAC,MAAA,CAAcN,MAAM,CAACvC,QAAQ,mBAAM,CAC1C8C,IAAI,CAAEA,CAAA,GAAM,CACVzF,OAAO,CAAC0F,OAAO,CAAC,MAAM,CAAC,CACvBhB,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAiB,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAtD,IAAI,CAACuD,cAAc,CAAC,CAAC,CAC1CC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEH,MAAM,CAAC,CAC5B5F,OAAO,CAAC0F,OAAO,CAACtD,gBAAgB,CAAG,MAAM,CAAG,MAAM,CAAC,CACnDD,wBAAwB,CAAC,KAAK,CAAC,CAC/BuC,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOI,KAAK,CAAE,CACdgB,OAAO,CAAChB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAAkB,cAAc,CAAIC,IAAe,EAAK,CAC1C,GAAIA,IAAI,CAAC/C,aAAa,EAAI,CAAC,CAAE,CAC3B,MAAO,CAAEM,MAAM,CAAE,OAAO,CAAE0C,IAAI,CAAE,IAAK,CAAC,CACxC,CAAC,IAAM,IAAID,IAAI,CAAC/C,aAAa,EAAI+C,IAAI,CAAC9C,aAAa,CAAE,CACnD,MAAO,CAAEK,MAAM,CAAE,SAAS,CAAE0C,IAAI,CAAE,MAAO,CAAC,CAC5C,CAAC,IAAM,IAAID,IAAI,CAAC/C,aAAa,EAAI+C,IAAI,CAAC7C,aAAa,CAAE,CACnD,MAAO,CAAEI,MAAM,CAAE,YAAY,CAAE0C,IAAI,CAAE,MAAO,CAAC,CAC/C,CAAC,IAAM,CACL,MAAO,CAAE1C,MAAM,CAAE,SAAS,CAAE0C,IAAI,CAAE,IAAK,CAAC,CAC1C,CACF,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAIvC,WAAmB,EAAK,CACnD,OAAQA,WAAW,EACjB,IAAK,MAAM,CAAE,MAAO,KAAK,CACzB,IAAK,QAAQ,CAAE,MAAO,QAAQ,CAC9B,IAAK,KAAK,CAAE,MAAO,OAAO,CAC1B,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAwC,cAAc,CAAI5C,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,QAAQ,CAAE,MAAO,OAAO,CAC7B,IAAK,UAAU,CAAE,MAAO,QAAQ,CAChC,IAAK,cAAc,CAAE,MAAO,KAAK,CACjC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAA6C,gBAAwC,CAAG,CAC/C,CACEf,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,MACT,CAAC,CACD,CACEnB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,MACT,CAAC,CACD,CACEnB,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACElB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GACT,CAAC,CACD,CACElB,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,OAAO,CAClBC,GAAG,CAAE,OAAO,CACZC,KAAK,CAAE,GACT,CAAC,CACD,CACElB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACC,QAAgB,CAAEzB,MAAiB,GAAK,CAC/C,KAAM,CAAA0B,WAAW,CAAGZ,cAAc,CAACd,MAAM,CAAC,CAC1C,mBACEvE,IAAA,CAACb,KAAK,EACJ0D,MAAM,CAAEoD,WAAW,CAACpD,MAAc,CAClC0C,IAAI,IAAAV,MAAA,CAAKmB,QAAQ,WAAK,CACvB,CAAC,CAEN,CACF,CAAC,CACD,CACErB,KAAK,CAAE,MAAM,CACbiB,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACG,CAAC,CAAE3B,MAAiB,GAAK,CAChC,KAAM,CAAA0B,WAAW,CAAGZ,cAAc,CAACd,MAAM,CAAC,CAC1C,mBACEvE,IAAA,CAACjB,GAAG,EAACoH,KAAK,CAAEF,WAAW,CAACpD,MAAM,GAAK,SAAS,CAAG,OAAO,CAC3CoD,WAAW,CAACpD,MAAM,GAAK,SAAS,CAAG,QAAQ,CAAG,KAAM,CAAAuD,QAAA,CAC5DH,WAAW,CAACV,IAAI,CACd,CAAC,CAEV,CACF,CAAC,CACD,CACEZ,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGM,KAAa,KAAAxB,MAAA,CAAQwB,KAAK,WACrC,CAAC,CACD,CACE1B,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGO,KAAa,EAAKxG,cAAc,CAACwG,KAAK,CACjD,CAAC,CACD,CACE3B,KAAK,CAAE,KAAK,CACZgB,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAG9C,WAAmB,eAC1BjD,IAAA,CAACjB,GAAG,EAACoH,KAAK,CAAEX,mBAAmB,CAACvC,WAAW,CAAE,CAAAmD,QAAA,CAC1CnD,WAAW,GAAK,MAAM,CAAG,GAAG,CAC5BA,WAAW,GAAK,QAAQ,CAAG,GAAG,CAAG,GAAG,CAClC,CAET,CAAC,CACD,CACE0B,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGlD,MAAc,eACrB7C,IAAA,CAACjB,GAAG,EAACoH,KAAK,CAAEV,cAAc,CAAC5C,MAAM,CAAE,CAAAuD,QAAA,CAChCvD,MAAM,GAAK,QAAQ,CAAG,IAAI,CAC1BA,MAAM,GAAK,UAAU,CAAG,IAAI,CAAG,IAAI,CACjC,CAET,CAAC,CACD,CACE8B,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACElB,KAAK,CAAE,IAAI,CACXiB,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAEA,CAACG,CAAC,CAAE3B,MAAiB,gBAC3BrE,KAAA,CAAC3B,KAAK,EAACgI,IAAI,CAAC,OAAO,CAAAH,QAAA,eACjBpG,IAAA,CAACd,OAAO,EAACyF,KAAK,CAAC,cAAI,CAAAyB,QAAA,cACjBpG,IAAA,CAAC1B,MAAM,EACLkI,IAAI,CAAC,MAAM,CACXC,IAAI,cAAEzG,IAAA,CAACT,YAAY,GAAE,CAAE,CACvBmH,OAAO,CAAEA,CAAA,GAAMpC,mBAAmB,CAACC,MAAM,CAAE,CAC5C,CAAC,CACK,CAAC,cACVvE,IAAA,CAACd,OAAO,EAACyF,KAAK,CAAC,cAAI,CAAAyB,QAAA,cACjBpG,IAAA,CAAC1B,MAAM,EACLkI,IAAI,CAAC,MAAM,CACXG,MAAM,MACNF,IAAI,cAAEzG,IAAA,CAACR,cAAc,GAAE,CAAE,CACzBkH,OAAO,CAAEA,CAAA,GAAMjC,qBAAqB,CAACF,MAAM,CAAE,CAC9C,CAAC,CACK,CAAC,EACL,CAEX,CAAC,CACF,CAED,KAAM,CAAAqC,YAAqC,CAAG,CAC5C,CACEjC,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YACP,CAAC,CACD,CACEjB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UACP,CAAC,CACD,CACEjB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBG,MAAM,CAAGc,KAAa,KAAAhC,MAAA,CAAQgC,KAAK,WACrC,CAAC,CACD,CACElC,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfG,MAAM,CAAGM,KAAa,KAAAxB,MAAA,CAAQwB,KAAK,WACrC,CAAC,CACD,CACE1B,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBG,MAAM,CAAGS,IAAY,EAAK,CACxB,KAAM,CAAAM,OAAO,CAAG,CACdC,SAAS,CAAE,CAAExB,IAAI,CAAE,MAAM,CAAEY,KAAK,CAAE,QAAS,CAAC,CAC5Ca,YAAY,CAAE,CAAEzB,IAAI,CAAE,IAAI,CAAEY,KAAK,CAAE,KAAM,CAAC,CAC1Cc,SAAS,CAAE,CAAE1B,IAAI,CAAE,MAAM,CAAEY,KAAK,CAAE,MAAO,CAC3C,CAAC,CACD,KAAM,CAAAe,MAAM,CAAGJ,OAAO,CAACN,IAAI,CAAyB,CACpD,mBAAOxG,IAAA,CAACjB,GAAG,EAACoH,KAAK,CAAEe,MAAM,CAACf,KAAM,CAAAC,QAAA,CAAEc,MAAM,CAAC3B,IAAI,CAAM,CAAC,CACtD,CACF,CAAC,CACD,CACEZ,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdG,MAAM,CAAGlC,OAAe,EAAK,CAC3B,KAAM,CAAAsD,UAAU,CAAG,CACjBC,IAAI,CAAE,CAAE7B,IAAI,CAAE,GAAG,CAAEY,KAAK,CAAE,KAAM,CAAC,CACjCkB,MAAM,CAAE,CAAE9B,IAAI,CAAE,GAAG,CAAEY,KAAK,CAAE,QAAS,CAAC,CACtCmB,GAAG,CAAE,CAAE/B,IAAI,CAAE,GAAG,CAAEY,KAAK,CAAE,OAAQ,CACnC,CAAC,CACD,KAAM,CAAAe,MAAM,CAAGC,UAAU,CAACtD,OAAO,CAA4B,CAC7D,mBAAO7D,IAAA,CAACjB,GAAG,EAACoH,KAAK,CAAEe,MAAM,CAACf,KAAM,CAAAC,QAAA,CAAEc,MAAM,CAAC3B,IAAI,CAAM,CAAC,CACtD,CACF,CAAC,CACD,CACEZ,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,iBAAiB,CAC5BC,GAAG,CAAE,iBACP,CAAC,CACF,CAED;AACA,KAAM,CAAA2B,UAAU,CAAG5G,UAAU,CAAC6G,MAAM,CACpC,KAAM,CAAAC,aAAa,CAAG9G,UAAU,CAAC+G,MAAM,CAACpC,IAAI,EAAIA,IAAI,CAAC/C,aAAa,EAAI+C,IAAI,CAAC9C,aAAa,CAAC,CAACgF,MAAM,CAChG,KAAM,CAAAG,eAAe,CAAGhH,UAAU,CAAC+G,MAAM,CAACpC,IAAI,EAAIA,IAAI,CAAC/C,aAAa,EAAI,CAAC,CAAC,CAACiF,MAAM,CACjF,KAAM,CAAAI,UAAU,CAAGjH,UAAU,CAACkH,MAAM,CAAC,CAACC,GAAG,CAAExC,IAAI,GAAKwC,GAAG,CAAIxC,IAAI,CAAC/C,aAAa,CAAG+C,IAAI,CAACjD,SAAU,CAAE,CAAC,CAAC,CAEnG,mBACEnC,KAAA,QAAAkG,QAAA,EAEGvF,WAAW,CAAC2G,MAAM,CAAG,CAAC,eACrBxH,IAAA,CAAChB,KAAK,EACJK,OAAO,CAAC,0BAAM,CACd0I,WAAW,uBAAAlD,MAAA,CAAShE,WAAW,CAAC2G,MAAM,mEAAA3C,MAAA,CAAehE,WAAW,CAAC6G,MAAM,CAACM,CAAC,EAAIA,CAAC,CAACnE,OAAO,GAAK,MAAM,CAAC,CAAC2D,MAAM,qDAAY,CACrHhB,IAAI,CAAC,SAAS,CACdyB,QAAQ,MACRC,QAAQ,MACRC,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAC5BC,MAAM,cACJrI,IAAA,CAAC1B,MAAM,EAACiI,IAAI,CAAC,OAAO,CAACC,IAAI,CAAC,MAAM,CAAAJ,QAAA,CAAC,0BAEjC,CAAQ,CACT,CACF,CACF,cAGDlG,KAAA,CAACtB,GAAG,EAAC0J,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACH,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAAhC,QAAA,eACjDpG,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAArC,QAAA,cACzBpG,IAAA,CAAC5B,IAAI,EAAAgI,QAAA,cACHpG,IAAA,CAACf,SAAS,EACR0F,KAAK,CAAC,0BAAM,CACZ+D,KAAK,CAAEnB,UAAW,CAClBoB,MAAM,CAAC,QAAG,CACVC,UAAU,CAAE,CAAEzC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNnG,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAArC,QAAA,cACzBpG,IAAA,CAAC5B,IAAI,EAAAgI,QAAA,cACHpG,IAAA,CAACf,SAAS,EACR0F,KAAK,CAAC,0BAAM,CACZ+D,KAAK,CAAEjB,aAAc,CACrBkB,MAAM,CAAC,QAAG,CACVC,UAAU,CAAE,CAAEzC,KAAK,CAAE,SAAU,CAAE,CACjC0C,MAAM,cAAE7I,IAAA,CAACL,eAAe,GAAE,CAAE,CAC7B,CAAC,CACE,CAAC,CACJ,CAAC,cACNK,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAArC,QAAA,cACzBpG,IAAA,CAAC5B,IAAI,EAAAgI,QAAA,cACHpG,IAAA,CAACf,SAAS,EACR0F,KAAK,CAAC,cAAI,CACV+D,KAAK,CAAEf,eAAgB,CACvBgB,MAAM,CAAC,QAAG,CACVC,UAAU,CAAE,CAAEzC,KAAK,CAAE,SAAU,CAAE,CACjC0C,MAAM,cAAE7I,IAAA,CAACJ,mBAAmB,GAAE,CAAE,CACjC,CAAC,CACE,CAAC,CACJ,CAAC,cACNI,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAArC,QAAA,cACzBpG,IAAA,CAAC5B,IAAI,EAAAgI,QAAA,cACHpG,IAAA,CAACf,SAAS,EACR0F,KAAK,CAAC,gCAAO,CACb+D,KAAK,CAAEd,UAAW,CAClBkB,SAAS,CAAE,CAAE,CACbC,SAAS,CAAGL,KAAK,EAAK5I,cAAc,CAACkJ,MAAM,CAACN,KAAK,CAAC,CAAE,CACpDE,UAAU,CAAE,CAAEzC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGNjG,KAAA,CAAC9B,IAAI,EAAAgI,QAAA,eACHlG,KAAA,CAACtB,GAAG,EAACqK,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACf,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAAhC,QAAA,eACtElG,KAAA,CAACrB,GAAG,EAAAuH,QAAA,eACFpG,IAAA,CAACG,KAAK,EAACkG,KAAK,CAAE,CAAE,CAAC8B,KAAK,CAAE,CAAEgB,MAAM,CAAE,CAAE,CAAE,CAAA/C,QAAA,CAAC,0BAEvC,CAAO,CAAC,cACRpG,IAAA,CAACI,IAAI,EAACoG,IAAI,CAAC,WAAW,CAAAJ,QAAA,CAAC,8JAEvB,CAAM,CAAC,EACJ,CAAC,cACNpG,IAAA,CAACnB,GAAG,EAAAuH,QAAA,cACFlG,KAAA,CAAC3B,KAAK,EAAA6H,QAAA,eACJpG,IAAA,CAACK,MAAM,EACL+I,WAAW,CAAC,wDAAW,CACvBC,UAAU,MACVlB,KAAK,CAAE,CAAEtC,KAAK,CAAE,GAAI,CAAE,CACtByD,QAAQ,CAAEtI,gBAAiB,CAC5B,CAAC,cACFhB,IAAA,CAACvB,MAAM,EACL2K,WAAW,CAAC,cAAI,CAChBC,UAAU,MACVlB,KAAK,CAAE,CAAEtC,KAAK,CAAE,GAAI,CAAE,CACtB6C,KAAK,CAAEzH,cAAe,CACtBsI,QAAQ,CAAErI,iBAAkB,CAC5BsI,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,IAAI,CAAEf,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAEe,KAAK,CAAE,KAAK,CAAEf,KAAK,CAAE,KAAM,CAAC,CAC9B,CAAEe,KAAK,CAAE,KAAK,CAAEf,KAAK,CAAE,KAAM,CAAC,CAC9B,CAAEe,KAAK,CAAE,KAAK,CAAEf,KAAK,CAAE,KAAM,CAAC,CAC9B,CAAEe,KAAK,CAAE,KAAK,CAAEf,KAAK,CAAE,KAAM,CAAC,CAC9B,CACH,CAAC,cACF1I,IAAA,CAACvB,MAAM,EACL2K,WAAW,CAAC,cAAI,CAChBC,UAAU,MACVlB,KAAK,CAAE,CAAEtC,KAAK,CAAE,GAAI,CAAE,CACtB6C,KAAK,CAAEvH,YAAa,CACpBoI,QAAQ,CAAEnI,eAAgB,CAC1BoI,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,IAAI,CAAEf,KAAK,CAAE,QAAS,CAAC,CAChC,CAAEe,KAAK,CAAE,IAAI,CAAEf,KAAK,CAAE,UAAW,CAAC,CAClC,CAAEe,KAAK,CAAE,IAAI,CAAEf,KAAK,CAAE,cAAe,CAAC,CACtC,CACH,CAAC,cACF1I,IAAA,CAACvB,MAAM,EACL2K,WAAW,CAAC,oBAAK,CACjBC,UAAU,MACVlB,KAAK,CAAE,CAAEtC,KAAK,CAAE,GAAI,CAAE,CACtB6C,KAAK,CAAErH,iBAAkB,CACzBkI,QAAQ,CAAEjI,oBAAqB,CAC/BkI,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,GAAG,CAAEf,KAAK,CAAE,MAAO,CAAC,CAC7B,CAAEe,KAAK,CAAE,GAAG,CAAEf,KAAK,CAAE,QAAS,CAAC,CAC/B,CAAEe,KAAK,CAAE,GAAG,CAAEf,KAAK,CAAE,KAAM,CAAC,CAC5B,CACH,CAAC,cACF1I,IAAA,CAAC1B,MAAM,EAACmI,IAAI,cAAEzG,IAAA,CAACH,YAAY,GAAE,CAAE,CAAAuG,QAAA,CAAC,0BAEhC,CAAQ,CAAC,cACTpG,IAAA,CAAC1B,MAAM,EAACmI,IAAI,cAAEzG,IAAA,CAACP,cAAc,GAAE,CAAE,CAAA2G,QAAA,CAAC,cAElC,CAAQ,CAAC,cACTpG,IAAA,CAAC1B,MAAM,EAACmI,IAAI,cAAEzG,IAAA,CAACN,cAAc,GAAE,CAAE,CAAA0G,QAAA,CAAC,cAElC,CAAQ,CAAC,cACTpG,IAAA,CAAC1B,MAAM,EAACkI,IAAI,CAAC,SAAS,CAACC,IAAI,cAAEzG,IAAA,CAACV,YAAY,GAAE,CAAE,CAACoH,OAAO,CAAEtC,kBAAmB,CAAAgC,QAAA,CAAC,0BAE5E,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAENpG,IAAA,CAAC3B,KAAK,EACJqL,OAAO,CAAEhE,gBAAiB,CAC1BiE,UAAU,CAAEhJ,UAAW,CACvBF,OAAO,CAAEA,OAAQ,CACjBmJ,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAAvF,MAAA,CACjBuF,KAAK,CAAC,CAAC,CAAC,MAAAvF,MAAA,CAAIuF,KAAK,CAAC,CAAC,CAAC,oBAAAvF,MAAA,CAAQsF,KAAK,WAC1C,CAAE,CACH,CAAC,EACE,CAAC,CAGNtJ,WAAW,CAAC2G,MAAM,CAAG,CAAC,eACrBxH,IAAA,CAAC5B,IAAI,EAACuG,KAAK,CAAC,sCAAQ,CAACwD,KAAK,CAAE,CAAEkC,SAAS,CAAE,EAAG,CAAE,CAAAjE,QAAA,cAC5CpG,IAAA,CAAC3B,KAAK,EACJqL,OAAO,CAAE9C,YAAa,CACtB+C,UAAU,CAAE9I,WAAY,CACxB+I,MAAM,CAAC,IAAI,CACXG,UAAU,CAAE,KAAM,CAClBxD,IAAI,CAAC,OAAO,CACb,CAAC,CACE,CACP,cAGDvG,IAAA,CAACtB,KAAK,EACJiG,KAAK,CAAElD,gBAAgB,CAAG,MAAM,CAAG,MAAO,CAC1C6I,IAAI,CAAE/I,qBAAsB,CAC5BuD,IAAI,CAAEE,sBAAuB,CAC7BuF,QAAQ,CAAEA,CAAA,GAAM/I,wBAAwB,CAAC,KAAK,CAAE,CAChDqE,KAAK,CAAE,GAAI,CACX2E,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAArE,QAAA,cAEfpG,IAAA,CAACrB,IAAI,EAACgD,IAAI,CAAEA,IAAK,CAAC+I,MAAM,CAAC,UAAU,CAAAtE,QAAA,cACjClG,KAAA,CAACtB,GAAG,EAAC0J,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAlC,QAAA,eACpBpG,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,YAAY,CACjBnB,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+G,QAAA,cAEhDpG,IAAA,CAACxB,KAAK,EAAC4K,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNpJ,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,UAAU,CACfnB,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+G,QAAA,cAEhDpG,IAAA,CAACxB,KAAK,EAAC4K,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNpJ,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,UAAU,CACfnB,KAAK,CAAC,cAAI,CACVoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA+G,QAAA,cAE9ClG,KAAA,CAACzB,MAAM,EAAC2K,WAAW,CAAC,gCAAO,CAAAhD,QAAA,eACzBpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,cAAI,CAAAtC,QAAA,CAAC,cAAE,CAAQ,CAAC,cAC9BpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,oBAAK,CAAAtC,QAAA,CAAC,oBAAG,CAAQ,CAAC,cAChCpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,oBAAK,CAAAtC,QAAA,CAAC,oBAAG,CAAQ,CAAC,cAChCpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,oBAAK,CAAAtC,QAAA,CAAC,oBAAG,CAAQ,CAAC,cAChCpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,oBAAK,CAAAtC,QAAA,CAAC,oBAAG,CAAQ,CAAC,EAC1B,CAAC,CACA,CAAC,CACT,CAAC,cACNpG,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,eAAe,CACpBnB,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+G,QAAA,cAEhDpG,IAAA,CAACxB,KAAK,EAAC4K,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNpJ,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,OAAO,CACZnB,KAAK,CAAC,cAAI,CACVoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA+G,QAAA,cAE9CpG,IAAA,CAACxB,KAAK,EAAC4K,WAAW,CAAC,gCAAO,CAAE,CAAC,CACpB,CAAC,CACT,CAAC,cACNpJ,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,UAAU,CACfnB,KAAK,CAAC,oBAAK,CACXoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,QAAS,CAAC,CAAE,CAAA+G,QAAA,cAE/CpG,IAAA,CAACxB,KAAK,EAAC4K,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,CACT,CAAC,cACNpJ,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,WAAW,CAChBnB,KAAK,CAAC,cAAI,CACVoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA+G,QAAA,cAE9CpG,IAAA,CAACZ,WAAW,EACVgK,WAAW,CAAC,gCAAO,CACnBjB,KAAK,CAAE,CAAEtC,KAAK,CAAE,MAAO,CAAE,CACzBkF,GAAG,CAAE,CAAE,CACPjC,SAAS,CAAE,CAAE,CACbkC,UAAU,CAAC,QAAG,CACf,CAAC,CACO,CAAC,CACT,CAAC,cACNhL,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,eAAe,CACpBnB,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+G,QAAA,cAEhDpG,IAAA,CAACZ,WAAW,EACVgK,WAAW,CAAC,4CAAS,CACrBjB,KAAK,CAAE,CAAEtC,KAAK,CAAE,MAAO,CAAE,CACzBkF,GAAG,CAAE,CAAE,CACPC,UAAU,CAAC,QAAG,CACf,CAAC,CACO,CAAC,CACT,CAAC,cACNhL,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAArC,QAAA,cACjBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,eAAe,CACpBnB,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+G,QAAA,cAEhDpG,IAAA,CAACZ,WAAW,EACVgK,WAAW,CAAC,0BAAM,CAClBjB,KAAK,CAAE,CAAEtC,KAAK,CAAE,MAAO,CAAE,CACzBkF,GAAG,CAAE,CAAE,CACPC,UAAU,CAAC,QAAG,CACf,CAAC,CACO,CAAC,CACT,CAAC,cACNhL,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAArC,QAAA,cACjBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,eAAe,CACpBnB,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+G,QAAA,cAEhDpG,IAAA,CAACZ,WAAW,EACVgK,WAAW,CAAC,0BAAM,CAClBjB,KAAK,CAAE,CAAEtC,KAAK,CAAE,MAAO,CAAE,CACzBkF,GAAG,CAAE,CAAE,CACPC,UAAU,CAAC,QAAG,CACf,CAAC,CACO,CAAC,CACT,CAAC,cACNhL,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAArC,QAAA,cACjBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,aAAa,CAClBnB,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+G,QAAA,cAEhDpG,IAAA,CAACZ,WAAW,EACVgK,WAAW,CAAC,0BAAM,CAClBjB,KAAK,CAAE,CAAEtC,KAAK,CAAE,MAAO,CAAE,CACzBkF,GAAG,CAAE,CAAE,CACPC,UAAU,CAAC,QAAG,CACf,CAAC,CACO,CAAC,CACT,CAAC,cACNhL,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,UAAU,CACfnB,KAAK,CAAC,0BAAM,CACZoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+G,QAAA,cAEhDpG,IAAA,CAACZ,WAAW,EACVgK,WAAW,CAAC,4CAAS,CACrBjB,KAAK,CAAE,CAAEtC,KAAK,CAAE,MAAO,CAAE,CACzBkF,GAAG,CAAE,CAAE,CACPC,UAAU,CAAC,QAAG,CACf,CAAC,CACO,CAAC,CACT,CAAC,cACNhL,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,UAAU,CACfnB,KAAK,CAAC,cAAI,CACVoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA+G,QAAA,cAE9CpG,IAAA,CAACxB,KAAK,EAAC4K,WAAW,CAAC,gCAAO,CAAE,CAAC,CACpB,CAAC,CACT,CAAC,cACNpJ,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,aAAa,CAClBnB,KAAK,CAAC,oBAAK,CACXoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,QAAS,CAAC,CAAE,CAAA+G,QAAA,cAE/ClG,KAAA,CAACzB,MAAM,EAAC2K,WAAW,CAAC,sCAAQ,CAAAhD,QAAA,eAC1BpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,MAAM,CAAAtC,QAAA,CAAC,QAAC,CAAQ,CAAC,cAC/BpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,QAAQ,CAAAtC,QAAA,CAAC,QAAC,CAAQ,CAAC,cACjCpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,KAAK,CAAAtC,QAAA,CAAC,QAAC,CAAQ,CAAC,EACxB,CAAC,CACA,CAAC,CACT,CAAC,cACNpG,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,QAAQ,CACbnB,KAAK,CAAC,cAAI,CACVoB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA+G,QAAA,cAE9ClG,KAAA,CAACzB,MAAM,EAAC2K,WAAW,CAAC,gCAAO,CAAAhD,QAAA,eACzBpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,QAAQ,CAAAtC,QAAA,CAAC,cAAE,CAAQ,CAAC,cAClCpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,UAAU,CAAAtC,QAAA,CAAC,cAAE,CAAQ,CAAC,cACpCpG,IAAA,CAACO,MAAM,EAACmI,KAAK,CAAC,cAAc,CAAAtC,QAAA,CAAC,cAAE,CAAQ,CAAC,EAClC,CAAC,CACA,CAAC,CACT,CAAC,cACNpG,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,gBAAgB,CACrBnB,KAAK,CAAC,oBAAK,CAAArD,QAAA,cAEXpG,IAAA,CAACZ,WAAW,EACVgK,WAAW,CAAC,sCAAQ,CACpBjB,KAAK,CAAE,CAAEtC,KAAK,CAAE,MAAO,CAAE,CACzBkF,GAAG,CAAE,CAAE,CACPC,UAAU,CAAC,QAAG,CACf,CAAC,CACO,CAAC,CACT,CAAC,cACNhL,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAArC,QAAA,cAClBpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,mBAAmB,CACxBnB,KAAK,CAAC,0BAAM,CAAArD,QAAA,cAEZpG,IAAA,CAACxB,KAAK,EAAC4K,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNpJ,IAAA,CAACnB,GAAG,EAAC0J,EAAE,CAAE,EAAG,CAAAnC,QAAA,cACVpG,IAAA,CAACrB,IAAI,CAACgM,IAAI,EACRC,IAAI,CAAC,OAAO,CACZnB,KAAK,CAAC,cAAI,CAAArD,QAAA,cAEVpG,IAAA,CAACM,QAAQ,EAAC2K,IAAI,CAAE,CAAE,CAAC7B,WAAW,CAAC,4CAAS,CAAE,CAAC,CAClC,CAAC,CACT,CAAC,EACH,CAAC,CACF,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5I,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}