import { apiService as api } from './api';
import { BatchInfo, BatchTransaction, BatchStatistics } from '../types';

/**
 * 批次跟踪服务类
 * 提供批次信息管理、交易记录、统计分析等功能
 */
export class BatchTrackingService {
  private static instance: BatchTrackingService;
  private baseUrl = '/api/batch-tracking';

  private constructor() {}

  public static getInstance(): BatchTrackingService {
    if (!BatchTrackingService.instance) {
      BatchTrackingService.instance = new BatchTrackingService();
    }
    return BatchTrackingService.instance;
  }

  /**
   * 获取批次列表
   * @param params 查询参数
   */
  async getBatches(params?: {
    page?: number;
    pageSize?: number;
    materialCode?: string;
    batchNumber?: string;
    status?: string;
    supplier?: string;
    dateRange?: [string, string];
  }) {
    try {
      const response = await api.get<any>(`${this.baseUrl}/batches`, { params });
      return response.data;
    } catch (error) {
      console.error('获取批次列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取批次详情
   * @param batchId 批次ID
   */
  async getBatchDetail(batchId: string) {
    try {
      const response = await api.get<{data: BatchInfo}>(`${this.baseUrl}/batches/${batchId}`);
      return response.data;
    } catch (error) {
      console.error('获取批次详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建批次
   * @param batchData 批次数据
   */
  async createBatch(batchData: Partial<BatchInfo>) {
    try {
      const response = await api.post<{data: BatchInfo}>(`${this.baseUrl}/batches`, batchData);
      return response.data;
    } catch (error) {
      console.error('创建批次失败:', error);
      throw error;
    }
  }

  /**
   * 更新批次信息
   * @param batchId 批次ID
   * @param batchData 批次数据
   */
  async updateBatch(batchId: string, batchData: Partial<BatchInfo>) {
    try {
      const response = await api.put<{data: BatchInfo}>(`${this.baseUrl}/batches/${batchId}`, batchData);
      return response.data;
    } catch (error) {
      console.error('更新批次失败:', error);
      throw error;
    }
  }

  /**
   * 删除批次
   * @param batchId 批次ID
   */
  async deleteBatch(batchId: string) {
    try {
      const response = await api.delete<any>(`${this.baseUrl}/batches/${batchId}`);
      return response.data;
    } catch (error) {
      console.error('删除批次失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除批次
   * @param batchIds 批次ID数组
   */
  async deleteBatches(batchIds: string[]) {
    try {
      const response = await api.delete<any>(`${this.baseUrl}/batches/batch`, {
        data: { batchIds }
      });
      return response.data;
    } catch (error) {
      console.error('批量删除批次失败:', error);
      throw error;
    }
  }

  /**
   * 获取批次交易记录
   * @param batchId 批次ID
   * @param params 查询参数
   */
  async getBatchTransactions(batchId: string, params?: {
    page?: number;
    pageSize?: number;
    transactionType?: string;
    dateRange?: [string, string];
  }) {
    try {
      const response = await api.get<any>(`${this.baseUrl}/batches/${batchId}/transactions`, { params });
      return response.data;
    } catch (error) {
      console.error('获取批次交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 添加批次交易记录
   * @param batchId 批次ID
   * @param transactionData 交易数据
   */
  async addBatchTransaction(batchId: string, transactionData: Partial<BatchTransaction>) {
    try {
      const response = await api.post<{data: BatchTransaction}>(`${this.baseUrl}/batches/${batchId}/transactions`, transactionData);
      return response.data;
    } catch (error) {
      console.error('添加批次交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取批次统计信息
   * @param params 查询参数
   */
  async getBatchStatistics(params?: {
    materialCode?: string;
    supplier?: string;
    dateRange?: [string, string];
  }) {
    try {
      const response = await api.get<{data: BatchStatistics}>(`${this.baseUrl}/statistics`, { params });
      return response.data;
    } catch (error) {
      console.error('获取批次统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 批次追溯
   * @param batchNumber 批次号
   */
  async traceBatch(batchNumber: string) {
    try {
      const response = await api.get<any>(`${this.baseUrl}/trace/${batchNumber}`);
      return response.data;
    } catch (error) {
      console.error('批次追溯失败:', error);
      throw error;
    }
  }

  /**
   * 获取批次质检记录
   * @param batchId 批次ID
   */
  async getBatchQualityRecords(batchId: string) {
    try {
      const response = await api.get<any>(`${this.baseUrl}/batches/${batchId}/quality`);
      return response.data;
    } catch (error) {
      console.error('获取批次质检记录失败:', error);
      throw error;
    }
  }

  /**
   * 添加批次质检记录
   * @param batchId 批次ID
   * @param qualityData 质检数据
   */
  async addBatchQualityRecord(batchId: string, qualityData: any) {
    try {
      const response = await api.post<any>(`${this.baseUrl}/batches/${batchId}/quality`, qualityData);
      return response.data;
    } catch (error) {
      console.error('添加批次质检记录失败:', error);
      throw error;
    }
  }

  /**
   * 批次状态变更
   * @param batchId 批次ID
   * @param status 新状态
   * @param reason 变更原因
   */
  async changeBatchStatus(batchId: string, status: string, reason?: string) {
    try {
      const response = await api.put<any>(`${this.baseUrl}/batches/${batchId}/status`, {
        status,
        reason
      });
      return response.data;
    } catch (error) {
      console.error('批次状态变更失败:', error);
      throw error;
    }
  }

  /**
   * 获取批次库存分布
   * @param batchId 批次ID
   */
  async getBatchInventoryDistribution(batchId: string) {
    try {
      const response = await api.get<any>(`${this.baseUrl}/batches/${batchId}/inventory-distribution`);
      return response.data;
    } catch (error) {
      console.error('获取批次库存分布失败:', error);
      throw error;
    }
  }

  /**
   * 批次合并
   * @param sourceBatchIds 源批次ID数组
   * @param targetBatchData 目标批次数据
   */
  async mergeBatches(sourceBatchIds: string[], targetBatchData: Partial<BatchInfo>) {
    try {
      const response = await api.post<{data: BatchInfo}>(`${this.baseUrl}/batches/merge`, {
        sourceBatchIds,
        targetBatchData
      });
      return response.data;
    } catch (error) {
      console.error('批次合并失败:', error);
      throw error;
    }
  }

  /**
   * 批次拆分
   * @param batchId 批次ID
   * @param splitData 拆分数据
   */
  async splitBatch(batchId: string, splitData: {
    quantities: number[];
    newBatchNumbers?: string[];
  }) {
    try {
      const response = await api.post<any>(`${this.baseUrl}/batches/${batchId}/split`, splitData);
      return response.data;
    } catch (error) {
      console.error('批次拆分失败:', error);
      throw error;
    }
  }

  /**
   * 获取批次报告
   * @param params 报告参数
   */
  async getBatchReport(params: {
    reportType: 'summary' | 'detail' | 'quality' | 'movement';
    batchIds?: string[];
    materialCode?: string;
    dateRange?: [string, string];
    format?: 'json' | 'excel' | 'pdf';
  }) {
    try {
      const response = await api.get<any>(`${this.baseUrl}/reports`, { params });
      return response.data;
    } catch (error) {
      console.error('获取批次报告失败:', error);
      throw error;
    }
  }

  /**
   * 导出批次数据
   * @param params 导出参数
   */
  async exportBatches(params?: {
    batchIds?: string[];
    materialCode?: string;
    status?: string;
    dateRange?: [string, string];
    format?: 'excel' | 'csv';
  }) {
    try {
      const response = await api.get<Blob>(`${this.baseUrl}/export`, {
        params,
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error('导出批次数据失败:', error);
      throw error;
    }
  }

  /**
   * 导入批次数据
   * @param file 文件
   */
  async importBatches(file: File) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await api.post<any>(`${this.baseUrl}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('导入批次数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取批次预警信息
   * @param params 查询参数
   */
  async getBatchAlerts(params?: {
    alertType?: 'expiry' | 'quality' | 'quantity';
    severity?: 'low' | 'medium' | 'high';
    status?: 'active' | 'resolved';
  }) {
    try {
      const response = await api.get<any>(`${this.baseUrl}/alerts`, { params });
      return response.data;
    } catch (error) {
      console.error('获取批次预警信息失败:', error);
      throw error;
    }
  }

  /**
   * 处理批次预警
   * @param alertId 预警ID
   * @param action 处理动作
   */
  async handleBatchAlert(alertId: string, action: 'resolve' | 'ignore' | 'escalate') {
    try {
      const response = await api.put<any>(`${this.baseUrl}/alerts/${alertId}`, { action });
      return response.data;
    } catch (error) {
      console.error('处理批次预警失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const batchTrackingService = BatchTrackingService.getInstance();
export default batchTrackingService;