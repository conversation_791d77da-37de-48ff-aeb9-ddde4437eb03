{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ConfigProvider, App as AntdApp } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport dayjs from 'dayjs';\nimport 'dayjs/locale/zh-cn';\nimport { store } from './store';\nimport { useAppDispatch, useAppSelector } from './hooks/redux';\nimport { initializeAuth } from './store/slices/authSlice';\nimport { ROUTES, THEME_CONFIG } from './constants';\n\n// 页面组件\nimport LoginPage from './pages/auth/LoginPage';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport MainLayout from './components/layout/MainLayout';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\n\n// BOM管理页面\nimport CoreBOMListPage from './pages/bom/CoreBOMListPage';\nimport CoreBOMCreatePage from './pages/bom/CoreBOMCreatePage';\nimport CoreBOMEditPage from './pages/bom/CoreBOMEditPage';\nimport CoreBOMViewPage from './pages/bom/CoreBOMViewPage';\nimport OrderBOMListPage from './pages/bom/OrderBOMListPage';\nimport OrderBOMCreatePage from './pages/bom/OrderBOMCreatePage';\nimport OrderBOMDerivePage from './pages/bom/OrderBOMDerivePage';\nimport OrderBOMViewPage from './pages/bom/OrderBOMViewPage';\n\n// 物料管理页面\nimport MaterialListPage from './pages/material/MaterialListPage';\nimport MaterialCreatePage from './pages/material/MaterialCreatePage';\nimport MaterialEditPage from './pages/material/MaterialEditPage';\n\n// 库存管理页面\nimport InventoryListPage from './pages/inventory/InventoryListPage';\nimport InventoryReceivePage from './pages/inventory/InventoryReceivePage';\nimport InventoryIssuePage from './pages/inventory/InventoryIssuePage';\nimport InventoryAdjustPage from './pages/inventory/InventoryAdjustPage';\nimport RemnantListPage from './pages/inventory/RemnantListPage';\nimport CuttingPlanPage from './pages/inventory/CuttingPlanPage';\n\n// 采购管理页面\nimport PurchaseListPage from './pages/purchase/PurchaseListPage';\nimport PurchaseRequisitionPage from './pages/purchase/PurchaseRequisitionPage';\nimport MRPCalculationPage from './pages/purchase/MRPCalculationPage';\nimport PurchaseOptimizationPage from './pages/purchase/PurchaseOptimizationPage';\n\n// 成本管理页面\nimport CostAnalysisPage from './pages/cost/CostAnalysisPage';\nimport CostReportsPage from './pages/cost/CostReportsPage';\nimport WasteTrackingPage from './pages/cost/WasteTrackingPage';\nimport StandardCostPage from './pages/cost/StandardCostPage';\n\n// 服务管理页面\nimport ServiceBOMListPage from './pages/service/ServiceBOMListPage';\nimport DeviceArchivePage from './pages/service/DeviceArchivePage';\nimport MaintenancePage from './pages/service/MaintenancePage';\n\n// ECN管理页面\nimport ECNListPage from './pages/ecn/ECNListPage';\nimport ECNCreatePage from './pages/ecn/ECNCreatePage';\nimport ECNReviewPage from './pages/ecn/ECNReviewPage';\n\n// 报告页面\nimport ReportsPage from './pages/reports/ReportsPage';\nimport DashboardConfigPage from './pages/reports/DashboardConfigPage';\n\n// 系统管理页面\nimport UserListPage from './pages/system/UserListPage';\nimport RoleListPage from './pages/system/RoleListPage';\nimport PermissionListPage from './pages/system/PermissionListPage';\nimport SystemConfigPage from './pages/system/SystemConfigPage';\n\n// 移动端页面\nimport MobilePage from './pages/mobile/MobilePage';\nimport MobileScanPage from './pages/mobile/MobileScanPage';\nimport MobileInventoryPage from './pages/mobile/MobileInventoryPage';\n\n// 设置dayjs中文\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndayjs.locale('zh-cn');\nconst AppContent = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    isAuthenticated\n  } = useAppSelector(state => state.auth);\n  useEffect(() => {\n    dispatch(initializeAuth());\n  }, [dispatch]);\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: ROUTES.LOGIN,\n      element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(MainLayout, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 32\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        index: true,\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: ROUTES.DASHBOARD,\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 31\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.DASHBOARD,\n        element: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CORE_BOM,\n        element: /*#__PURE__*/_jsxDEV(CoreBOMListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CORE_BOM_CREATE,\n        element: /*#__PURE__*/_jsxDEV(CoreBOMCreatePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CORE_BOM_EDIT,\n        element: /*#__PURE__*/_jsxDEV(CoreBOMEditPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CORE_BOM_VIEW,\n        element: /*#__PURE__*/_jsxDEV(CoreBOMViewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ORDER_BOM,\n        element: /*#__PURE__*/_jsxDEV(OrderBOMListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ORDER_BOM_CREATE,\n        element: /*#__PURE__*/_jsxDEV(OrderBOMCreatePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ORDER_BOM_DERIVE,\n        element: /*#__PURE__*/_jsxDEV(OrderBOMDerivePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ORDER_BOM_VIEW,\n        element: /*#__PURE__*/_jsxDEV(OrderBOMViewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MATERIALS,\n        element: /*#__PURE__*/_jsxDEV(MaterialListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MATERIALS_CREATE,\n        element: /*#__PURE__*/_jsxDEV(MaterialCreatePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MATERIALS_EDIT,\n        element: /*#__PURE__*/_jsxDEV(MaterialEditPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.INVENTORY,\n        element: /*#__PURE__*/_jsxDEV(InventoryListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.INVENTORY_RECEIVE,\n        element: /*#__PURE__*/_jsxDEV(InventoryReceivePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 57\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.INVENTORY_ISSUE,\n        element: /*#__PURE__*/_jsxDEV(InventoryIssuePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.INVENTORY_ADJUST,\n        element: /*#__PURE__*/_jsxDEV(InventoryAdjustPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.REMNANTS,\n        element: /*#__PURE__*/_jsxDEV(RemnantListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.CUTTING_PLAN,\n        element: /*#__PURE__*/_jsxDEV(CuttingPlanPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.PURCHASE,\n        element: /*#__PURE__*/_jsxDEV(PurchaseListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.PURCHASE_REQUISITION,\n        element: /*#__PURE__*/_jsxDEV(PurchaseRequisitionPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 60\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MRP_CALCULATION,\n        element: /*#__PURE__*/_jsxDEV(MRPCalculationPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.PURCHASE_OPTIMIZATION,\n        element: /*#__PURE__*/_jsxDEV(PurchaseOptimizationPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 61\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.COST_ANALYSIS,\n        element: /*#__PURE__*/_jsxDEV(CostAnalysisPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.COST_REPORTS,\n        element: /*#__PURE__*/_jsxDEV(CostReportsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.WASTE_TRACKING,\n        element: /*#__PURE__*/_jsxDEV(WasteTrackingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.STANDARD_COST,\n        element: /*#__PURE__*/_jsxDEV(StandardCostPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.SERVICE_BOM,\n        element: /*#__PURE__*/_jsxDEV(ServiceBOMListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.DEVICE_ARCHIVE,\n        element: /*#__PURE__*/_jsxDEV(DeviceArchivePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MAINTENANCE,\n        element: /*#__PURE__*/_jsxDEV(MaintenancePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ECN,\n        element: /*#__PURE__*/_jsxDEV(ECNListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ECN_CREATE,\n        element: /*#__PURE__*/_jsxDEV(ECNCreatePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 50\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ECN_REVIEW,\n        element: /*#__PURE__*/_jsxDEV(ECNReviewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 50\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.REPORTS,\n        element: /*#__PURE__*/_jsxDEV(ReportsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 47\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.DASHBOARD_CONFIG,\n        element: /*#__PURE__*/_jsxDEV(DashboardConfigPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.USERS,\n        element: /*#__PURE__*/_jsxDEV(UserListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.ROLES,\n        element: /*#__PURE__*/_jsxDEV(RoleListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.PERMISSIONS,\n        element: /*#__PURE__*/_jsxDEV(PermissionListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.SYSTEM_CONFIG,\n        element: /*#__PURE__*/_jsxDEV(SystemConfigPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MOBILE,\n        element: /*#__PURE__*/_jsxDEV(MobilePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MOBILE_SCAN,\n        element: /*#__PURE__*/_jsxDEV(MobileScanPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 51\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: ROUTES.MOBILE_INVENTORY,\n        element: /*#__PURE__*/_jsxDEV(MobileInventoryPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 56\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: ROUTES.DASHBOARD,\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(AppContent, \"S0ObmGEX/4toGTRN7qlPtU2bGbo=\", false, function () {\n  return [useAppDispatch, useAppSelector];\n});\n_c = AppContent;\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(ConfigProvider, {\n      locale: zhCN,\n      theme: {\n        token: {\n          colorPrimary: THEME_CONFIG.primaryColor,\n          borderRadius: THEME_CONFIG.borderRadius\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(AntdApp, {\n        children: /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n};\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "App", "AntdApp", "zhCN", "dayjs", "store", "useAppDispatch", "useAppSelector", "initializeAuth", "ROUTES", "THEME_CONFIG", "LoginPage", "DashboardPage", "MainLayout", "ProtectedRoute", "CoreBOMListPage", "CoreBOMCreatePage", "CoreBOMEditPage", "CoreBOMViewPage", "OrderBOMListPage", "OrderBOMCreatePage", "OrderBOMDerivePage", "OrderBOMViewPage", "MaterialListPage", "MaterialCreatePage", "MaterialEditPage", "InventoryListPage", "InventoryReceivePage", "InventoryIssuePage", "InventoryAdjustPage", "RemnantListPage", "CuttingPlanPage", "PurchaseListPage", "PurchaseRequisitionPage", "MRPCalculationPage", "PurchaseOptimizationPage", "CostAnalysisPage", "CostReportsPage", "WasteTrackingPage", "StandardCostPage", "ServiceBOMListPage", "DeviceArchivePage", "MaintenancePage", "ECNListPage", "ECNCreatePage", "ECNReviewPage", "ReportsPage", "DashboardConfigPage", "UserListPage", "RoleListPage", "PermissionListPage", "SystemConfigPage", "MobilePage", "MobileScanPage", "MobileInventoryPage", "jsxDEV", "_jsxDEV", "locale", "A<PERSON><PERSON><PERSON>nt", "_s", "dispatch", "isAuthenticated", "state", "auth", "children", "path", "LOGIN", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "DASHBOARD", "replace", "CORE_BOM", "CORE_BOM_CREATE", "CORE_BOM_EDIT", "CORE_BOM_VIEW", "ORDER_BOM", "ORDER_BOM_CREATE", "ORDER_BOM_DERIVE", "ORDER_BOM_VIEW", "MATERIALS", "MATERIALS_CREATE", "MATERIALS_EDIT", "INVENTORY", "INVENTORY_RECEIVE", "INVENTORY_ISSUE", "INVENTORY_ADJUST", "REMNANTS", "CUTTING_PLAN", "PURCHASE", "PURCHASE_REQUISITION", "MRP_CALCULATION", "PURCHASE_OPTIMIZATION", "COST_ANALYSIS", "COST_REPORTS", "WASTE_TRACKING", "STANDARD_COST", "SERVICE_BOM", "DEVICE_ARCHIVE", "MAINTENANCE", "ECN", "ECN_CREATE", "ECN_REVIEW", "REPORTS", "DASHBOARD_CONFIG", "USERS", "ROLES", "PERMISSIONS", "SYSTEM_CONFIG", "MOBILE", "MOBILE_SCAN", "MOBILE_INVENTORY", "_c", "theme", "token", "colorPrimary", "primaryColor", "borderRadius", "_c2", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ConfigProvider, App as AntdApp } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport dayjs from 'dayjs';\nimport 'dayjs/locale/zh-cn';\n\nimport { store } from './store';\nimport { useAppDispatch, useAppSelector } from './hooks/redux';\nimport { initializeAuth } from './store/slices/authSlice';\nimport { ROUTES, THEME_CONFIG } from './constants';\n\n// 页面组件\nimport LoginPage from './pages/auth/LoginPage';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport MainLayout from './components/layout/MainLayout';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\n\n// BOM管理页面\nimport CoreBOMListPage from './pages/bom/CoreBOMListPage';\nimport CoreBOMCreatePage from './pages/bom/CoreBOMCreatePage';\nimport CoreBOMEditPage from './pages/bom/CoreBOMEditPage';\nimport CoreBOMViewPage from './pages/bom/CoreBOMViewPage';\nimport OrderBOMListPage from './pages/bom/OrderBOMListPage';\nimport OrderBOMCreatePage from './pages/bom/OrderBOMCreatePage';\nimport OrderBOMDerivePage from './pages/bom/OrderBOMDerivePage';\nimport OrderBOMViewPage from './pages/bom/OrderBOMViewPage';\n\n// 物料管理页面\nimport MaterialListPage from './pages/material/MaterialListPage';\nimport MaterialCreatePage from './pages/material/MaterialCreatePage';\nimport MaterialEditPage from './pages/material/MaterialEditPage';\n\n// 库存管理页面\nimport InventoryListPage from './pages/inventory/InventoryListPage';\nimport InventoryReceivePage from './pages/inventory/InventoryReceivePage';\nimport InventoryIssuePage from './pages/inventory/InventoryIssuePage';\nimport InventoryAdjustPage from './pages/inventory/InventoryAdjustPage';\nimport RemnantListPage from './pages/inventory/RemnantListPage';\nimport CuttingPlanPage from './pages/inventory/CuttingPlanPage';\n\n// 采购管理页面\nimport PurchaseListPage from './pages/purchase/PurchaseListPage';\nimport PurchaseRequisitionPage from './pages/purchase/PurchaseRequisitionPage';\nimport MRPCalculationPage from './pages/purchase/MRPCalculationPage';\nimport PurchaseOptimizationPage from './pages/purchase/PurchaseOptimizationPage';\n\n// 成本管理页面\nimport CostAnalysisPage from './pages/cost/CostAnalysisPage';\nimport CostReportsPage from './pages/cost/CostReportsPage';\nimport WasteTrackingPage from './pages/cost/WasteTrackingPage';\nimport StandardCostPage from './pages/cost/StandardCostPage';\n\n// 服务管理页面\nimport ServiceBOMListPage from './pages/service/ServiceBOMListPage';\nimport DeviceArchivePage from './pages/service/DeviceArchivePage';\nimport MaintenancePage from './pages/service/MaintenancePage';\n\n// ECN管理页面\nimport ECNListPage from './pages/ecn/ECNListPage';\nimport ECNCreatePage from './pages/ecn/ECNCreatePage';\nimport ECNReviewPage from './pages/ecn/ECNReviewPage';\n\n// 报告页面\nimport ReportsPage from './pages/reports/ReportsPage';\nimport DashboardConfigPage from './pages/reports/DashboardConfigPage';\n\n// 系统管理页面\nimport UserListPage from './pages/system/UserListPage';\nimport RoleListPage from './pages/system/RoleListPage';\nimport PermissionListPage from './pages/system/PermissionListPage';\nimport SystemConfigPage from './pages/system/SystemConfigPage';\n\n// 移动端页面\nimport MobilePage from './pages/mobile/MobilePage';\nimport MobileScanPage from './pages/mobile/MobileScanPage';\nimport MobileInventoryPage from './pages/mobile/MobileInventoryPage';\n\n// 设置dayjs中文\ndayjs.locale('zh-cn');\n\nconst AppContent: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { isAuthenticated } = useAppSelector(state => state.auth);\n\n  useEffect(() => {\n    dispatch(initializeAuth());\n  }, [dispatch]);\n\n  return (\n    <Routes>\n      {/* 登录页面 */}\n      <Route path={ROUTES.LOGIN} element={<LoginPage />} />\n      \n      {/* 受保护的路由 */}\n      <Route path=\"/\" element={<ProtectedRoute><MainLayout /></ProtectedRoute>}>\n        {/* 仪表板 */}\n        <Route index element={<Navigate to={ROUTES.DASHBOARD} replace />} />\n        <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />\n        \n        {/* BOM管理 */}\n        <Route path={ROUTES.CORE_BOM} element={<CoreBOMListPage />} />\n        <Route path={ROUTES.CORE_BOM_CREATE} element={<CoreBOMCreatePage />} />\n        <Route path={ROUTES.CORE_BOM_EDIT} element={<CoreBOMEditPage />} />\n        <Route path={ROUTES.CORE_BOM_VIEW} element={<CoreBOMViewPage />} />\n        \n        <Route path={ROUTES.ORDER_BOM} element={<OrderBOMListPage />} />\n        <Route path={ROUTES.ORDER_BOM_CREATE} element={<OrderBOMCreatePage />} />\n        <Route path={ROUTES.ORDER_BOM_DERIVE} element={<OrderBOMDerivePage />} />\n        <Route path={ROUTES.ORDER_BOM_VIEW} element={<OrderBOMViewPage />} />\n        \n        {/* 物料管理 */}\n        <Route path={ROUTES.MATERIALS} element={<MaterialListPage />} />\n        <Route path={ROUTES.MATERIALS_CREATE} element={<MaterialCreatePage />} />\n        <Route path={ROUTES.MATERIALS_EDIT} element={<MaterialEditPage />} />\n        \n        {/* 库存管理 */}\n        <Route path={ROUTES.INVENTORY} element={<InventoryListPage />} />\n        <Route path={ROUTES.INVENTORY_RECEIVE} element={<InventoryReceivePage />} />\n        <Route path={ROUTES.INVENTORY_ISSUE} element={<InventoryIssuePage />} />\n        <Route path={ROUTES.INVENTORY_ADJUST} element={<InventoryAdjustPage />} />\n        <Route path={ROUTES.REMNANTS} element={<RemnantListPage />} />\n        <Route path={ROUTES.CUTTING_PLAN} element={<CuttingPlanPage />} />\n        \n        {/* 采购管理 */}\n        <Route path={ROUTES.PURCHASE} element={<PurchaseListPage />} />\n        <Route path={ROUTES.PURCHASE_REQUISITION} element={<PurchaseRequisitionPage />} />\n        <Route path={ROUTES.MRP_CALCULATION} element={<MRPCalculationPage />} />\n        <Route path={ROUTES.PURCHASE_OPTIMIZATION} element={<PurchaseOptimizationPage />} />\n        \n        {/* 成本管理 */}\n        <Route path={ROUTES.COST_ANALYSIS} element={<CostAnalysisPage />} />\n        <Route path={ROUTES.COST_REPORTS} element={<CostReportsPage />} />\n        <Route path={ROUTES.WASTE_TRACKING} element={<WasteTrackingPage />} />\n        <Route path={ROUTES.STANDARD_COST} element={<StandardCostPage />} />\n        \n        {/* 服务管理 */}\n        <Route path={ROUTES.SERVICE_BOM} element={<ServiceBOMListPage />} />\n        <Route path={ROUTES.DEVICE_ARCHIVE} element={<DeviceArchivePage />} />\n        <Route path={ROUTES.MAINTENANCE} element={<MaintenancePage />} />\n        \n        {/* ECN管理 */}\n        <Route path={ROUTES.ECN} element={<ECNListPage />} />\n        <Route path={ROUTES.ECN_CREATE} element={<ECNCreatePage />} />\n        <Route path={ROUTES.ECN_REVIEW} element={<ECNReviewPage />} />\n        \n        {/* 报告 */}\n        <Route path={ROUTES.REPORTS} element={<ReportsPage />} />\n        <Route path={ROUTES.DASHBOARD_CONFIG} element={<DashboardConfigPage />} />\n        \n        {/* 系统管理 */}\n        <Route path={ROUTES.USERS} element={<UserListPage />} />\n        <Route path={ROUTES.ROLES} element={<RoleListPage />} />\n        <Route path={ROUTES.PERMISSIONS} element={<PermissionListPage />} />\n        <Route path={ROUTES.SYSTEM_CONFIG} element={<SystemConfigPage />} />\n        \n        {/* 移动端 */}\n        <Route path={ROUTES.MOBILE} element={<MobilePage />} />\n        <Route path={ROUTES.MOBILE_SCAN} element={<MobileScanPage />} />\n        <Route path={ROUTES.MOBILE_INVENTORY} element={<MobileInventoryPage />} />\n      </Route>\n      \n      {/* 404页面 */}\n      <Route path=\"*\" element={<Navigate to={ROUTES.DASHBOARD} replace />} />\n    </Routes>\n  );\n};\n\nconst App: React.FC = () => {\n  return (\n    <Provider store={store}>\n      <ConfigProvider\n        locale={zhCN}\n        theme={{\n          token: {\n            colorPrimary: THEME_CONFIG.primaryColor,\n            borderRadius: THEME_CONFIG.borderRadius,\n          },\n        }}\n      >\n        <AntdApp>\n          <Router>\n            <AppContent />\n          </Router>\n        </AntdApp>\n      </ConfigProvider>\n    </Provider>\n  );\n};\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,cAAc,EAAEC,GAAG,IAAIC,OAAO,QAAQ,MAAM;AACrD,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAE3B,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,cAAc,EAAEC,cAAc,QAAQ,eAAe;AAC9D,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,MAAM,EAAEC,YAAY,QAAQ,aAAa;;AAElD;AACA,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,cAAc,MAAM,kCAAkC;;AAE7D;AACA,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,gBAAgB,MAAM,8BAA8B;;AAE3D;AACA,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,gBAAgB,MAAM,mCAAmC;;AAEhE;AACA,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,oBAAoB,MAAM,wCAAwC;AACzE,OAAOC,kBAAkB,MAAM,sCAAsC;AACrE,OAAOC,mBAAmB,MAAM,uCAAuC;AACvE,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,OAAOC,eAAe,MAAM,mCAAmC;;AAE/D;AACA,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,wBAAwB,MAAM,2CAA2C;;AAEhF;AACA,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,gBAAgB,MAAM,+BAA+B;;AAE5D;AACA,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,eAAe,MAAM,iCAAiC;;AAE7D;AACA,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;;AAErD;AACA,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,mBAAmB,MAAM,qCAAqC;;AAErE;AACA,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,gBAAgB,MAAM,iCAAiC;;AAE9D;AACA,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACApD,KAAK,CAACqD,MAAM,CAAC,OAAO,CAAC;AAErB,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGtD,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEuD;EAAgB,CAAC,GAAGtD,cAAc,CAACuD,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAE/DtE,SAAS,CAAC,MAAM;IACdmE,QAAQ,CAACpD,cAAc,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACoD,QAAQ,CAAC,CAAC;EAEd,oBACEJ,OAAA,CAAC5D,MAAM;IAAAoE,QAAA,gBAELR,OAAA,CAAC3D,KAAK;MAACoE,IAAI,EAAExD,MAAM,CAACyD,KAAM;MAACC,OAAO,eAAEX,OAAA,CAAC7C,SAAS;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrDf,OAAA,CAAC3D,KAAK;MAACoE,IAAI,EAAC,GAAG;MAACE,OAAO,eAAEX,OAAA,CAAC1C,cAAc;QAAAkD,QAAA,eAACR,OAAA,CAAC3C,UAAU;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAE;MAAAP,QAAA,gBAEvER,OAAA,CAAC3D,KAAK;QAAC2E,KAAK;QAACL,OAAO,eAAEX,OAAA,CAAC1D,QAAQ;UAAC2E,EAAE,EAAEhE,MAAM,CAACiE,SAAU;UAACC,OAAO;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACiE,SAAU;QAACP,OAAO,eAAEX,OAAA,CAAC5C,aAAa;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG7Df,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACmE,QAAS;QAACT,OAAO,eAAEX,OAAA,CAACzC,eAAe;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Df,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACoE,eAAgB;QAACV,OAAO,eAAEX,OAAA,CAACxC,iBAAiB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACqE,aAAc;QAACX,OAAO,eAAEX,OAAA,CAACvC,eAAe;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACsE,aAAc;QAACZ,OAAO,eAAEX,OAAA,CAACtC,eAAe;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEnEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACuE,SAAU;QAACb,OAAO,eAAEX,OAAA,CAACrC,gBAAgB;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACwE,gBAAiB;QAACd,OAAO,eAAEX,OAAA,CAACpC,kBAAkB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACyE,gBAAiB;QAACf,OAAO,eAAEX,OAAA,CAACnC,kBAAkB;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC0E,cAAe;QAAChB,OAAO,eAAEX,OAAA,CAAClC,gBAAgB;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGrEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC2E,SAAU;QAACjB,OAAO,eAAEX,OAAA,CAACjC,gBAAgB;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC4E,gBAAiB;QAAClB,OAAO,eAAEX,OAAA,CAAChC,kBAAkB;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC6E,cAAe;QAACnB,OAAO,eAAEX,OAAA,CAAC/B,gBAAgB;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGrEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC8E,SAAU;QAACpB,OAAO,eAAEX,OAAA,CAAC9B,iBAAiB;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC+E,iBAAkB;QAACrB,OAAO,eAAEX,OAAA,CAAC7B,oBAAoB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5Ef,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACgF,eAAgB;QAACtB,OAAO,eAAEX,OAAA,CAAC5B,kBAAkB;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACiF,gBAAiB;QAACvB,OAAO,eAAEX,OAAA,CAAC3B,mBAAmB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1Ef,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACkF,QAAS;QAACxB,OAAO,eAAEX,OAAA,CAAC1B,eAAe;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Df,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACmF,YAAa;QAACzB,OAAO,eAAEX,OAAA,CAACzB,eAAe;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGlEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACoF,QAAS;QAAC1B,OAAO,eAAEX,OAAA,CAACxB,gBAAgB;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/Df,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACqF,oBAAqB;QAAC3B,OAAO,eAAEX,OAAA,CAACvB,uBAAuB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClFf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACsF,eAAgB;QAAC5B,OAAO,eAAEX,OAAA,CAACtB,kBAAkB;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACuF,qBAAsB;QAAC7B,OAAO,eAAEX,OAAA,CAACrB,wBAAwB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpFf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACwF,aAAc;QAAC9B,OAAO,eAAEX,OAAA,CAACpB,gBAAgB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACyF,YAAa;QAAC/B,OAAO,eAAEX,OAAA,CAACnB,eAAe;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC0F,cAAe;QAAChC,OAAO,eAAEX,OAAA,CAAClB,iBAAiB;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC2F,aAAc;QAACjC,OAAO,eAAEX,OAAA,CAACjB,gBAAgB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC4F,WAAY;QAAClC,OAAO,eAAEX,OAAA,CAAChB,kBAAkB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC6F,cAAe;QAACnC,OAAO,eAAEX,OAAA,CAACf,iBAAiB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC8F,WAAY;QAACpC,OAAO,eAAEX,OAAA,CAACd,eAAe;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGjEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC+F,GAAI;QAACrC,OAAO,eAAEX,OAAA,CAACb,WAAW;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACgG,UAAW;QAACtC,OAAO,eAAEX,OAAA,CAACZ,aAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Df,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACiG,UAAW;QAACvC,OAAO,eAAEX,OAAA,CAACX,aAAa;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG9Df,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACkG,OAAQ;QAACxC,OAAO,eAAEX,OAAA,CAACV,WAAW;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzDf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACmG,gBAAiB;QAACzC,OAAO,eAAEX,OAAA,CAACT,mBAAmB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1Ef,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACoG,KAAM;QAAC1C,OAAO,eAAEX,OAAA,CAACR,YAAY;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACqG,KAAM;QAAC3C,OAAO,eAAEX,OAAA,CAACP,YAAY;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACsG,WAAY;QAAC5C,OAAO,eAAEX,OAAA,CAACN,kBAAkB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACuG,aAAc;QAAC7C,OAAO,eAAEX,OAAA,CAACL,gBAAgB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACwG,MAAO;QAAC9C,OAAO,eAAEX,OAAA,CAACJ,UAAU;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAACyG,WAAY;QAAC/C,OAAO,eAAEX,OAAA,CAACH,cAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEf,OAAA,CAAC3D,KAAK;QAACoE,IAAI,EAAExD,MAAM,CAAC0G,gBAAiB;QAAChD,OAAO,eAAEX,OAAA,CAACF,mBAAmB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eAGRf,OAAA,CAAC3D,KAAK;MAACoE,IAAI,EAAC,GAAG;MAACE,OAAO,eAAEX,OAAA,CAAC1D,QAAQ;QAAC2E,EAAE,EAAEhE,MAAM,CAACiE,SAAU;QAACC,OAAO;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjE,CAAC;AAEb,CAAC;AAACZ,EAAA,CArFID,UAAoB;EAAA,QACPpD,cAAc,EACHC,cAAc;AAAA;AAAA6G,EAAA,GAFtC1D,UAAoB;AAuF1B,MAAMzD,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEuD,OAAA,CAACzD,QAAQ;IAACM,KAAK,EAAEA,KAAM;IAAA2D,QAAA,eACrBR,OAAA,CAACxD,cAAc;MACbyD,MAAM,EAAEtD,IAAK;MACbkH,KAAK,EAAE;QACLC,KAAK,EAAE;UACLC,YAAY,EAAE7G,YAAY,CAAC8G,YAAY;UACvCC,YAAY,EAAE/G,YAAY,CAAC+G;QAC7B;MACF,CAAE;MAAAzD,QAAA,eAEFR,OAAA,CAACtD,OAAO;QAAA8D,QAAA,eACNR,OAAA,CAAC7D,MAAM;UAAAqE,QAAA,eACLR,OAAA,CAACE,UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEf,CAAC;AAACmD,GAAA,GApBIzH,GAAa;AAsBnB,eAAeA,GAAG;AAAC,IAAAmH,EAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAP,EAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}