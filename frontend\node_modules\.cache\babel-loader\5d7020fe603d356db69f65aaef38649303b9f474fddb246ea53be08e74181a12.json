{"ast": null, "code": "// 格式化工具函数\nimport { formatCurrency as _formatCurrency, formatDate as _formatDate } from './index';\n\n// 重新导出格式化函数\nexport const formatCurrency = _formatCurrency;\nexport const formatDate = _formatDate;\n\n// 导出默认对象\nexport default {\n  formatCurrency,\n  formatDate\n};", "map": {"version": 3, "names": ["formatCurrency", "_formatCurrency", "formatDate", "_formatDate"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/utils/format.ts"], "sourcesContent": ["// 格式化工具函数\r\nimport { formatCurrency as _formatCurrency, formatDate as _formatDate } from './index';\r\n\r\n// 重新导出格式化函数\r\nexport const formatCurrency = _formatCurrency;\r\nexport const formatDate = _formatDate;\r\n\r\n// 导出默认对象\r\nexport default {\r\n  formatCurrency,\r\n  formatDate\r\n};"], "mappings": "AAAA;AACA,SAASA,cAAc,IAAIC,eAAe,EAAEC,UAAU,IAAIC,WAAW,QAAQ,SAAS;;AAEtF;AACA,OAAO,MAAMH,cAAc,GAAGC,eAAe;AAC7C,OAAO,MAAMC,UAAU,GAAGC,WAAW;;AAErC;AACA,eAAe;EACbH,cAAc;EACdE;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}