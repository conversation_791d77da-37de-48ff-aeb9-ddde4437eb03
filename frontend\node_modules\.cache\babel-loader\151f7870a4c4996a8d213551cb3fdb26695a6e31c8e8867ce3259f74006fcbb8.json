{"ast": null, "code": "import axios from'axios';import{API_BASE_URL,STORAGE_KEYS,ERROR_CODES}from'../constants';import{errorHandler,ErrorType}from'../utils/errorHandler';class ApiService{constructor(){this.instance=void 0;this.instance=axios.create({baseURL:API_BASE_URL,timeout:30000,headers:{'Content-Type':'application/json'}});this.setupInterceptors();}setupInterceptors(){// 请求拦截器\nthis.instance.interceptors.request.use(config=>{const token=localStorage.getItem(STORAGE_KEYS.TOKEN);if(token){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>{return Promise.reject(error);});// 响应拦截器\nthis.instance.interceptors.response.use(response=>{return response;},error=>{if(error.response){const{status,data}=error.response;switch(status){case ERROR_CODES.UNAUTHORIZED:// 清除本地存储的认证信息\nlocalStorage.removeItem(STORAGE_KEYS.TOKEN);localStorage.removeItem(STORAGE_KEYS.USER_INFO);localStorage.removeItem(STORAGE_KEYS.CURRENT_ROLE);// 使用错误处理器处理权限错误\nerrorHandler.handleError({type:ErrorType.PERMISSION,message:(data===null||data===void 0?void 0:data.message)||'登录已过期，请重新登录',code:status,details:data});// 重定向到登录页\nwindow.location.href='/login';break;case ERROR_CODES.FORBIDDEN:errorHandler.handleError({type:ErrorType.PERMISSION,message:(data===null||data===void 0?void 0:data.message)||'权限不足，无法访问该资源',code:status,details:data});break;case ERROR_CODES.NOT_FOUND:errorHandler.handleError({type:ErrorType.HTTP,message:(data===null||data===void 0?void 0:data.message)||'请求的资源不存在',code:status,details:data});break;case ERROR_CODES.VALIDATION_ERROR:errorHandler.handleError({type:ErrorType.VALIDATION,message:(data===null||data===void 0?void 0:data.message)||'数据验证失败',code:status,details:data});break;case ERROR_CODES.SERVER_ERROR:errorHandler.handleError({type:ErrorType.HTTP,message:(data===null||data===void 0?void 0:data.message)||'服务器内部错误，请稍后重试',code:status,details:data});break;default:errorHandler.handleError({type:ErrorType.HTTP,message:(data===null||data===void 0?void 0:data.message)||error.message||'请求失败',code:status,details:data});}}else if(error.request){errorHandler.handleError({type:ErrorType.NETWORK,message:'网络连接失败，请检查网络设置',details:error.request});}else{errorHandler.handleError({type:ErrorType.UNKNOWN,message:\"\\u8BF7\\u6C42\\u914D\\u7F6E\\u9519\\u8BEF: \".concat(error.message),details:error});}return Promise.reject(error);});}// GET 请求\nasync get(url,config){try{const response=await this.instance.get(url,config);return response.data.data;}catch(error){throw error;// 错误已在拦截器中处理\n}}// POST 请求\nasync post(url,data,config){try{const response=await this.instance.post(url,data,config);return response.data.data;}catch(error){throw error;// 错误已在拦截器中处理\n}}// PUT 请求\nasync put(url,data,config){try{const response=await this.instance.put(url,data,config);return response.data.data;}catch(error){throw error;// 错误已在拦截器中处理\n}}// DELETE 请求\nasync delete(url,config){try{const response=await this.instance.delete(url,config);return response.data.data;}catch(error){throw error;// 错误已在拦截器中处理\n}}// PATCH 请求\nasync patch(url,data,config){try{const response=await this.instance.patch(url,data,config);return response.data.data;}catch(error){throw error;// 错误已在拦截器中处理\n}}// 文件上传\nasync upload(url,file,onProgress){try{const formData=new FormData();formData.append('file',file);const config={headers:{'Content-Type':'multipart/form-data'},onUploadProgress:progressEvent=>{if(onProgress&&progressEvent.total){const progress=Math.round(progressEvent.loaded*100/progressEvent.total);onProgress(progress);}}};const response=await this.instance.post(url,formData,config);return response.data.data;}catch(error){throw error;// 错误已在拦截器中处理\n}}// 文件下载\nasync download(url,filename){try{const response=await this.instance.get(url,{responseType:'blob'});const blob=new Blob([response.data]);const downloadUrl=window.URL.createObjectURL(blob);const link=document.createElement('a');link.href=downloadUrl;link.download=filename||'download';document.body.appendChild(link);link.click();document.body.removeChild(link);window.URL.revokeObjectURL(downloadUrl);}catch(error){throw error;// 错误已在拦截器中处理\n}}// 获取原始axios实例（用于特殊需求）\ngetInstance(){return this.instance;}}export const apiService=new ApiService();export default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "STORAGE_KEYS", "ERROR_CODES", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "ApiService", "constructor", "instance", "create", "baseURL", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "TOKEN", "Authorization", "concat", "error", "Promise", "reject", "response", "status", "data", "UNAUTHORIZED", "removeItem", "USER_INFO", "CURRENT_ROLE", "handleError", "type", "PERMISSION", "message", "code", "details", "window", "location", "href", "FORBIDDEN", "NOT_FOUND", "HTTP", "VALIDATION_ERROR", "VALIDATION", "SERVER_ERROR", "NETWORK", "UNKNOWN", "get", "url", "post", "put", "delete", "patch", "upload", "file", "onProgress", "formData", "FormData", "append", "onUploadProgress", "progressEvent", "total", "progress", "Math", "round", "loaded", "download", "filename", "responseType", "blob", "Blob", "downloadUrl", "URL", "createObjectURL", "link", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "getInstance", "apiService"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { API_BASE_URL, STORAGE_KEYS, ERROR_CODES } from '../constants';\nimport { ApiResponse } from '../types';\nimport { errorHandler, ErrorType } from '../utils/errorHandler';\n\nclass ApiService {\n  private instance: AxiosInstance;\n\n  constructor() {\n    this.instance = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // 请求拦截器\n    this.instance.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // 响应拦截器\n    this.instance.interceptors.response.use(\n      (response: AxiosResponse<ApiResponse<any>>) => {\n        return response;\n      },\n      (error) => {\n        if (error.response) {\n          const { status, data } = error.response;\n          \n          switch (status) {\n            case ERROR_CODES.UNAUTHORIZED:\n              // 清除本地存储的认证信息\n              localStorage.removeItem(STORAGE_KEYS.TOKEN);\n              localStorage.removeItem(STORAGE_KEYS.USER_INFO);\n              localStorage.removeItem(STORAGE_KEYS.CURRENT_ROLE);\n              // 使用错误处理器处理权限错误\n              errorHandler.handleError({\n                type: ErrorType.PERMISSION,\n                message: data?.message || '登录已过期，请重新登录',\n                code: status,\n                details: data\n              });\n              // 重定向到登录页\n              window.location.href = '/login';\n              break;\n            case ERROR_CODES.FORBIDDEN:\n              errorHandler.handleError({\n                type: ErrorType.PERMISSION,\n                message: data?.message || '权限不足，无法访问该资源',\n                code: status,\n                details: data\n              });\n              break;\n            case ERROR_CODES.NOT_FOUND:\n              errorHandler.handleError({\n                type: ErrorType.HTTP,\n                message: data?.message || '请求的资源不存在',\n                code: status,\n                details: data\n              });\n              break;\n            case ERROR_CODES.VALIDATION_ERROR:\n              errorHandler.handleError({\n                type: ErrorType.VALIDATION,\n                message: data?.message || '数据验证失败',\n                code: status,\n                details: data\n              });\n              break;\n            case ERROR_CODES.SERVER_ERROR:\n              errorHandler.handleError({\n                type: ErrorType.HTTP,\n                message: data?.message || '服务器内部错误，请稍后重试',\n                code: status,\n                details: data\n              });\n              break;\n            default:\n              errorHandler.handleError({\n                type: ErrorType.HTTP,\n                message: data?.message || error.message || '请求失败',\n                code: status,\n                details: data\n              });\n          }\n        } else if (error.request) {\n          errorHandler.handleError({\n            type: ErrorType.NETWORK,\n            message: '网络连接失败，请检查网络设置',\n            details: error.request\n          });\n        } else {\n          errorHandler.handleError({\n            type: ErrorType.UNKNOWN,\n            message: `请求配置错误: ${error.message}`,\n            details: error\n          });\n        }\n        \n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // GET 请求\n  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.get<ApiResponse<T>>(url, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // POST 请求\n  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.post<ApiResponse<T>>(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // PUT 请求\n  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.put<ApiResponse<T>>(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // DELETE 请求\n  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.delete<ApiResponse<T>>(url, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // PATCH 请求\n  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    try {\n      const response = await this.instance.patch<ApiResponse<T>>(url, data, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 文件上传\n  async upload<T>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const config: AxiosRequestConfig = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        onUploadProgress: (progressEvent) => {\n          if (onProgress && progressEvent.total) {\n            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    };\n\n      const response = await this.instance.post<ApiResponse<T>>(url, formData, config);\n      return response.data.data;\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 文件下载\n  async download(url: string, filename?: string): Promise<void> {\n    try {\n      const response = await this.instance.get(url, {\n        responseType: 'blob',\n      });\n\n      const blob = new Blob([response.data]);\n      const downloadUrl = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = filename || 'download';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(downloadUrl);\n    } catch (error) {\n      throw error; // 错误已在拦截器中处理\n    }\n  }\n\n  // 获取原始axios实例（用于特殊需求）\n  getInstance(): AxiosInstance {\n    return this.instance;\n  }\n}\n\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAA4D,OAAO,CAC/E,OAASC,YAAY,CAAEC,YAAY,CAAEC,WAAW,KAAQ,cAAc,CAEtE,OAASC,YAAY,CAAEC,SAAS,KAAQ,uBAAuB,CAE/D,KAAM,CAAAC,UAAW,CAGfC,WAAWA,CAAA,CAAG,MAFNC,QAAQ,QAGd,IAAI,CAACA,QAAQ,CAAGR,KAAK,CAACS,MAAM,CAAC,CAC3BC,OAAO,CAAET,YAAY,CACrBU,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAC1B,CAEQA,iBAAiBA,CAAA,CAAG,CAC1B;AACA,IAAI,CAACL,QAAQ,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,CAAC,CACtD,GAAIH,KAAK,CAAE,CACTD,MAAM,CAACL,OAAO,CAACU,aAAa,WAAAC,MAAA,CAAaL,KAAK,CAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAO,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,IAAI,CAAChB,QAAQ,CAACM,YAAY,CAACa,QAAQ,CAACX,GAAG,CACpCW,QAAyC,EAAK,CAC7C,MAAO,CAAAA,QAAQ,CACjB,CAAC,CACAH,KAAK,EAAK,CACT,GAAIA,KAAK,CAACG,QAAQ,CAAE,CAClB,KAAM,CAAEC,MAAM,CAAEC,IAAK,CAAC,CAAGL,KAAK,CAACG,QAAQ,CAEvC,OAAQC,MAAM,EACZ,IAAK,CAAAzB,WAAW,CAAC2B,YAAY,CAC3B;AACAX,YAAY,CAACY,UAAU,CAAC7B,YAAY,CAACmB,KAAK,CAAC,CAC3CF,YAAY,CAACY,UAAU,CAAC7B,YAAY,CAAC8B,SAAS,CAAC,CAC/Cb,YAAY,CAACY,UAAU,CAAC7B,YAAY,CAAC+B,YAAY,CAAC,CAClD;AACA7B,YAAY,CAAC8B,WAAW,CAAC,CACvBC,IAAI,CAAE9B,SAAS,CAAC+B,UAAU,CAC1BC,OAAO,CAAE,CAAAR,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEQ,OAAO,GAAI,aAAa,CACvCC,IAAI,CAAEV,MAAM,CACZW,OAAO,CAAEV,IACX,CAAC,CAAC,CACF;AACAW,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAQ,CAC/B,MACF,IAAK,CAAAvC,WAAW,CAACwC,SAAS,CACxBvC,YAAY,CAAC8B,WAAW,CAAC,CACvBC,IAAI,CAAE9B,SAAS,CAAC+B,UAAU,CAC1BC,OAAO,CAAE,CAAAR,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEQ,OAAO,GAAI,cAAc,CACxCC,IAAI,CAAEV,MAAM,CACZW,OAAO,CAAEV,IACX,CAAC,CAAC,CACF,MACF,IAAK,CAAA1B,WAAW,CAACyC,SAAS,CACxBxC,YAAY,CAAC8B,WAAW,CAAC,CACvBC,IAAI,CAAE9B,SAAS,CAACwC,IAAI,CACpBR,OAAO,CAAE,CAAAR,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEQ,OAAO,GAAI,UAAU,CACpCC,IAAI,CAAEV,MAAM,CACZW,OAAO,CAAEV,IACX,CAAC,CAAC,CACF,MACF,IAAK,CAAA1B,WAAW,CAAC2C,gBAAgB,CAC/B1C,YAAY,CAAC8B,WAAW,CAAC,CACvBC,IAAI,CAAE9B,SAAS,CAAC0C,UAAU,CAC1BV,OAAO,CAAE,CAAAR,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEQ,OAAO,GAAI,QAAQ,CAClCC,IAAI,CAAEV,MAAM,CACZW,OAAO,CAAEV,IACX,CAAC,CAAC,CACF,MACF,IAAK,CAAA1B,WAAW,CAAC6C,YAAY,CAC3B5C,YAAY,CAAC8B,WAAW,CAAC,CACvBC,IAAI,CAAE9B,SAAS,CAACwC,IAAI,CACpBR,OAAO,CAAE,CAAAR,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEQ,OAAO,GAAI,eAAe,CACzCC,IAAI,CAAEV,MAAM,CACZW,OAAO,CAAEV,IACX,CAAC,CAAC,CACF,MACF,QACEzB,YAAY,CAAC8B,WAAW,CAAC,CACvBC,IAAI,CAAE9B,SAAS,CAACwC,IAAI,CACpBR,OAAO,CAAE,CAAAR,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEQ,OAAO,GAAIb,KAAK,CAACa,OAAO,EAAI,MAAM,CACjDC,IAAI,CAAEV,MAAM,CACZW,OAAO,CAAEV,IACX,CAAC,CAAC,CACN,CACF,CAAC,IAAM,IAAIL,KAAK,CAACT,OAAO,CAAE,CACxBX,YAAY,CAAC8B,WAAW,CAAC,CACvBC,IAAI,CAAE9B,SAAS,CAAC4C,OAAO,CACvBZ,OAAO,CAAE,gBAAgB,CACzBE,OAAO,CAAEf,KAAK,CAACT,OACjB,CAAC,CAAC,CACJ,CAAC,IAAM,CACLX,YAAY,CAAC8B,WAAW,CAAC,CACvBC,IAAI,CAAE9B,SAAS,CAAC6C,OAAO,CACvBb,OAAO,0CAAAd,MAAA,CAAaC,KAAK,CAACa,OAAO,CAAE,CACnCE,OAAO,CAAEf,KACX,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CACH,CAEA;AACA,KAAM,CAAA2B,GAAGA,CAAIC,GAAW,CAAEnC,MAA2B,CAAc,CACjE,GAAI,CACF,KAAM,CAAAU,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC2C,GAAG,CAAiBC,GAAG,CAAEnC,MAAM,CAAC,CACrE,MAAO,CAAAU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAE,MAAOL,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CAAE;AACf,CACF,CAEA;AACA,KAAM,CAAA6B,IAAIA,CAAID,GAAW,CAAEvB,IAAU,CAAEZ,MAA2B,CAAc,CAC9E,GAAI,CACF,KAAM,CAAAU,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC6C,IAAI,CAAiBD,GAAG,CAAEvB,IAAI,CAAEZ,MAAM,CAAC,CAC5E,MAAO,CAAAU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAE,MAAOL,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CAAE;AACf,CACF,CAEA;AACA,KAAM,CAAA8B,GAAGA,CAAIF,GAAW,CAAEvB,IAAU,CAAEZ,MAA2B,CAAc,CAC7E,GAAI,CACF,KAAM,CAAAU,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC8C,GAAG,CAAiBF,GAAG,CAAEvB,IAAI,CAAEZ,MAAM,CAAC,CAC3E,MAAO,CAAAU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAE,MAAOL,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CAAE;AACf,CACF,CAEA;AACA,KAAM,CAAA+B,MAAMA,CAAIH,GAAW,CAAEnC,MAA2B,CAAc,CACpE,GAAI,CACF,KAAM,CAAAU,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC+C,MAAM,CAAiBH,GAAG,CAAEnC,MAAM,CAAC,CACxE,MAAO,CAAAU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAE,MAAOL,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CAAE;AACf,CACF,CAEA;AACA,KAAM,CAAAgC,KAAKA,CAAIJ,GAAW,CAAEvB,IAAU,CAAEZ,MAA2B,CAAc,CAC/E,GAAI,CACF,KAAM,CAAAU,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAACgD,KAAK,CAAiBJ,GAAG,CAAEvB,IAAI,CAAEZ,MAAM,CAAC,CAC7E,MAAO,CAAAU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAE,MAAOL,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CAAE;AACf,CACF,CAEA;AACA,KAAM,CAAAiC,MAAMA,CAAIL,GAAW,CAAEM,IAAU,CAAEC,UAAuC,CAAc,CAC5F,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEJ,IAAI,CAAC,CAE7B,KAAM,CAAAzC,MAA0B,CAAG,CACjCL,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CAAC,CACDmD,gBAAgB,CAAGC,aAAa,EAAK,CACnC,GAAIL,UAAU,EAAIK,aAAa,CAACC,KAAK,CAAE,CACrC,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAAEJ,aAAa,CAACK,MAAM,CAAG,GAAG,CAAIL,aAAa,CAACC,KAAK,CAAC,CACjFN,UAAU,CAACO,QAAQ,CAAC,CACtB,CACF,CACF,CAAC,CAEC,KAAM,CAAAvC,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC6C,IAAI,CAAiBD,GAAG,CAAEQ,QAAQ,CAAE3C,MAAM,CAAC,CAChF,MAAO,CAAAU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAE,MAAOL,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CAAE;AACf,CACF,CAEA;AACA,KAAM,CAAA8C,QAAQA,CAAClB,GAAW,CAAEmB,QAAiB,CAAiB,CAC5D,GAAI,CACF,KAAM,CAAA5C,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC2C,GAAG,CAACC,GAAG,CAAE,CAC5CoB,YAAY,CAAE,MAChB,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC/C,QAAQ,CAACE,IAAI,CAAC,CAAC,CACtC,KAAM,CAAA8C,WAAW,CAAGnC,MAAM,CAACoC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC,CACpD,KAAM,CAAAK,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACpC,IAAI,CAAGiC,WAAW,CACvBG,IAAI,CAACR,QAAQ,CAAGC,QAAQ,EAAI,UAAU,CACtCQ,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,CAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC,CACZJ,QAAQ,CAACE,IAAI,CAACG,WAAW,CAACN,IAAI,CAAC,CAC/BtC,MAAM,CAACoC,GAAG,CAACS,eAAe,CAACV,WAAW,CAAC,CACzC,CAAE,MAAOnD,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CAAE;AACf,CACF,CAEA;AACA8D,WAAWA,CAAA,CAAkB,CAC3B,MAAO,KAAI,CAAC9E,QAAQ,CACtB,CACF,CAEA,MAAO,MAAM,CAAA+E,UAAU,CAAG,GAAI,CAAAjF,UAAU,CAAC,CAAC,CAC1C,cAAe,CAAAiF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}