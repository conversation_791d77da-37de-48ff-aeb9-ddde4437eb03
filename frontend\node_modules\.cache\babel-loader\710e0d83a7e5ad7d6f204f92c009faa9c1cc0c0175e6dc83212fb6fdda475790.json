{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Row,Col,Button,Table,Space,Tag,Modal,Form,InputNumber,Select,DatePicker,Checkbox,Divider,Typography,Statistic,Progress,Tabs,Alert,message}from'antd';import{SettingOutlined,ShoppingCartOutlined,ExportOutlined,ReloadOutlined}from'@ant-design/icons';import{formatCurrency}from'../../utils/format';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{TabPane}=Tabs;const{RangePicker}=DatePicker;const PurchaseOptimizationPage=()=>{const[loading,setLoading]=useState(false);const[optimizationModalVisible,setOptimizationModalVisible]=useState(false);const[optimizationForm]=Form.useForm();const[activeTab,setActiveTab]=useState('overview');useEffect(()=>{loadData();},[]);const loadData=()=>{// TODO: 加载优化数据\n};const handleOptimize=async()=>{try{const values=await optimizationForm.validateFields();setLoading(true);// TODO: 调用优化API\nawait new Promise(resolve=>setTimeout(resolve,2000));setOptimizationModalVisible(false);message.success('采购优化完成');}catch(error){message.error('采购优化失败');}finally{setLoading(false);}};// 模拟优化结果数据\nconst mockOptimizationData={overview:{totalItems:45,optimizedItems:38,totalSavings:125000,wasteReduction:18.5,mergedOrders:12,optimizationRate:84.4},results:[{id:'1',materialCode:'ANT-MAIN-001',materialName:'5G主天线单元',originalQuantity:75,optimizedQuantity:80,packageSize:10,moq:50,unitPrice:1500,originalCost:112500,optimizedCost:120000,wasteAmount:7500,wasteType:'包装浪费',supplier:'华为技术',leadTime:14,urgency:'HIGH'},{id:'2',materialCode:'RF-AMP-001',materialName:'RF功率放大器',originalQuantity:35,optimizedQuantity:50,packageSize:5,moq:50,unitPrice:2500,originalCost:87500,optimizedCost:125000,wasteAmount:37500,wasteType:'MOQ浪费',supplier:'中兴通讯',leadTime:21,urgency:'HIGH'},{id:'3',materialCode:'CABLE-001',materialName:'同轴电缆',originalQuantity:280,optimizedQuantity:300,packageSize:100,moq:200,unitPrice:25,originalCost:7000,optimizedCost:7500,wasteAmount:500,wasteType:'包装浪费',supplier:'电缆供应商',leadTime:7,urgency:'MEDIUM'}],suggestions:[{id:'1',supplier:'华为技术',totalValue:245000,itemCount:8,wasteAmount:12500,wastePercentage:5.1,urgency:'HIGH',leadTime:14,items:[]},{id:'2',supplier:'中兴通讯',totalValue:187500,itemCount:5,wasteAmount:37500,wastePercentage:20.0,urgency:'HIGH',leadTime:21,items:[]},{id:'3',supplier:'电缆供应商',totalValue:45000,itemCount:12,wasteAmount:2500,wastePercentage:5.6,urgency:'MEDIUM',leadTime:7,items:[]}]};const optimizationColumns=[{title:'物料编码',dataIndex:'materialCode',key:'materialCode',width:120,fixed:'left'},{title:'物料名称',dataIndex:'materialName',key:'materialName',width:200},{title:'原始需求',dataIndex:'originalQuantity',key:'originalQuantity',width:100,render:value=>value.toLocaleString()},{title:'优化数量',dataIndex:'optimizedQuantity',key:'optimizedQuantity',width:100,render:(value,record)=>/*#__PURE__*/_jsx(\"span\",{style:{color:value>record.originalQuantity?'#faad14':'#52c41a'},children:value.toLocaleString()})},{title:'包装规格',dataIndex:'packageSize',key:'packageSize',width:100,render:value=>\"\".concat(value,\"/\\u5305\")},{title:'MOQ',dataIndex:'moq',key:'moq',width:80,render:value=>value.toLocaleString()},{title:'单价',dataIndex:'unitPrice',key:'unitPrice',width:100,render:value=>formatCurrency(value)},{title:'原始成本',dataIndex:'originalCost',key:'originalCost',width:120,render:value=>formatCurrency(value)},{title:'优化成本',dataIndex:'optimizedCost',key:'optimizedCost',width:120,render:(value,record)=>/*#__PURE__*/_jsx(\"span\",{style:{color:value>record.originalCost?'#ff4d4f':'#52c41a'},children:formatCurrency(value)})},{title:'浪费金额',dataIndex:'wasteAmount',key:'wasteAmount',width:120,render:value=>/*#__PURE__*/_jsx(\"span\",{style:{color:'#ff4d4f'},children:formatCurrency(value)})},{title:'浪费类型',dataIndex:'wasteType',key:'wasteType',width:100,render:type=>{const color=type==='包装浪费'?'orange':type==='MOQ浪费'?'red':'blue';return/*#__PURE__*/_jsx(Tag,{color:color,children:type});}},{title:'供应商',dataIndex:'supplier',key:'supplier',width:120},{title:'交期',dataIndex:'leadTime',key:'leadTime',width:80,render:days=>\"\".concat(days,\"\\u5929\")},{title:'紧急度',dataIndex:'urgency',key:'urgency',width:80,render:urgency=>{const color=urgency==='HIGH'?'red':urgency==='MEDIUM'?'orange':'green';return/*#__PURE__*/_jsx(Tag,{color:color,children:urgency});}}];const suggestionColumns=[{title:'供应商',dataIndex:'supplier',key:'supplier',width:150},{title:'采购金额',dataIndex:'totalValue',key:'totalValue',width:120,render:value=>formatCurrency(value)},{title:'物料数量',dataIndex:'itemCount',key:'itemCount',width:80,render:count=>\"\".concat(count,\"\\u9879\")},{title:'浪费金额',dataIndex:'wasteAmount',key:'wasteAmount',width:120,render:value=>/*#__PURE__*/_jsx(\"span\",{style:{color:'#ff4d4f'},children:formatCurrency(value)})},{title:'浪费率',dataIndex:'wastePercentage',key:'wastePercentage',width:100,render:percentage=>/*#__PURE__*/_jsxs(\"span\",{style:{color:percentage>10?'#ff4d4f':'#faad14'},children:[percentage.toFixed(1),\"%\"]})},{title:'紧急度',dataIndex:'urgency',key:'urgency',width:80,render:urgency=>{const color=urgency==='HIGH'?'red':urgency==='MEDIUM'?'orange':'green';return/*#__PURE__*/_jsx(Tag,{color:color,children:urgency});}},{title:'交期',dataIndex:'leadTime',key:'leadTime',width:80,render:days=>\"\".concat(days,\"\\u5929\")},{title:'操作',key:'action',width:120,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsx(Space,{size:\"small\",children:/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"small\",icon:/*#__PURE__*/_jsx(ShoppingCartOutlined,{}),onClick:()=>{Modal.confirm({title:'生成采购订单',content:\"\\u786E\\u5B9A\\u4E3A\\u4F9B\\u5E94\\u5546 \".concat(record.supplier,\" \\u751F\\u6210\\u91C7\\u8D2D\\u8BA2\\u5355\\u5417\\uFF1F\"),onOk:()=>{message.success('采购订单已生成');}});},children:\"\\u751F\\u6210\\u8BA2\\u5355\"})})}];const renderOverviewTab=()=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u7269\\u6599\\u9879\",value:mockOptimizationData.overview.totalItems,valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u4F18\\u5316\\u9879\",value:mockOptimizationData.overview.optimizedItems,valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8282\\u7701\\u91D1\\u989D\",value:mockOptimizationData.overview.totalSavings,prefix:\"\\xA5\",valueStyle:{color:'#52c41a'},formatter:value=>formatCurrency(Number(value))})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6D6A\\u8D39\\u51CF\\u5C11\",value:mockOptimizationData.overview.wasteReduction,suffix:\"%\",valueStyle:{color:'#52c41a'}})})})]}),/*#__PURE__*/_jsx(Card,{title:\"\\u4F18\\u5316\\u8FDB\\u5EA6\",style:{marginBottom:24},children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4F18\\u5316\\u5B8C\\u6210\\u7387\"}),/*#__PURE__*/_jsx(Progress,{percent:mockOptimizationData.overview.optimizationRate,status:\"active\",strokeColor:\"#52c41a\"})]})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BA2\\u5355\\u5408\\u5E76\\u6570\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:24,fontWeight:'bold',color:'#1890ff'},children:mockOptimizationData.overview.mergedOrders})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u4F18\\u5316\\u5EFA\\u8BAE\",children:/*#__PURE__*/_jsx(Alert,{message:\"\\u4F18\\u5316\\u5EFA\\u8BAE\",description:/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u5EFA\\u8BAE\\u5408\\u5E76\\u534E\\u4E3A\\u6280\\u672F\\u548C\\u4E2D\\u5174\\u901A\\u8BAF\\u7684\\u8BA2\\u5355\\uFF0C\\u53EF\\u51CF\\u5C1115%\\u7684\\u91C7\\u8D2D\\u6210\\u672C\"}),/*#__PURE__*/_jsx(\"li\",{children:\"CABLE-001\\u7684\\u5305\\u88C5\\u89C4\\u683C\\u53EF\\u4EE5\\u8C03\\u6574\\uFF0C\\u51CF\\u5C11\\u5305\\u88C5\\u6D6A\\u8D39\"}),/*#__PURE__*/_jsx(\"li\",{children:\"RF-AMP-001\\u7684MOQ\\u8F83\\u9AD8\\uFF0C\\u5EFA\\u8BAE\\u5BFB\\u627E\\u66FF\\u4EE3\\u4F9B\\u5E94\\u5546\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u5EFA\\u8BAE\\u8BBE\\u7F6E\\u7D27\\u6025\\u8BA2\\u5355\\u767D\\u540D\\u5355\\uFF0C\\u4F18\\u5148\\u4FDD\\u8BC1\\u5173\\u952E\\u4EA4\\u4ED8\"})]}),type:\"info\",showIcon:true})})]});const renderOptimizationTab=()=>/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Card,{title:\"\\u4F18\\u5316\\u7ED3\\u679C\\u8BE6\\u60C5\",children:/*#__PURE__*/_jsx(Table,{columns:optimizationColumns,dataSource:mockOptimizationData.results,rowKey:\"id\",scroll:{x:1400},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})})});const renderSuggestionTab=()=>/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Card,{title:\"\\u91C7\\u8D2D\\u5EFA\\u8BAE\",children:/*#__PURE__*/_jsx(Table,{columns:suggestionColumns,dataSource:mockOptimizationData.suggestions,rowKey:\"id\",scroll:{x:1000},pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})})});return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:24},children:[/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u91C7\\u8D2D\\u4F18\\u5316\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u5305\\u88C5\\u4F18\\u5316\\u3001MOQ\\u5904\\u7406\\u548C\\u91C7\\u8D2D\\u5EFA\\u8BAE\\u751F\\u6210\"})]}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:loadData,children:\"\\u5237\\u65B0\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(SettingOutlined,{}),onClick:()=>setOptimizationModalVisible(true),children:\"\\u5F00\\u59CB\\u4F18\\u5316\"})]})})]}),/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:[/*#__PURE__*/_jsx(TabPane,{tab:\"\\u4F18\\u5316\\u6982\\u89C8\",children:renderOverviewTab()},\"overview\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u4F18\\u5316\\u7ED3\\u679C\",children:renderOptimizationTab()},\"optimization\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u91C7\\u8D2D\\u5EFA\\u8BAE\",children:renderSuggestionTab()},\"suggestion\")]})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u91C7\\u8D2D\\u4F18\\u5316\\u8BBE\\u7F6E\",open:optimizationModalVisible,onOk:handleOptimize,onCancel:()=>setOptimizationModalVisible(false),width:600,okText:\"\\u5F00\\u59CB\\u4F18\\u5316\",cancelText:\"\\u53D6\\u6D88\",confirmLoading:loading,children:/*#__PURE__*/_jsxs(Form,{form:optimizationForm,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"dateRange\",label:\"\\u4F18\\u5316\\u5468\\u671F\",rules:[{required:true,message:'请选择优化周期'}],children:/*#__PURE__*/_jsx(RangePicker,{style:{width:'100%'},placeholder:['开始日期','结束日期']})}),/*#__PURE__*/_jsx(Divider,{orientation:\"left\",children:\"\\u4F18\\u5316\\u7B56\\u7565\"}),/*#__PURE__*/_jsx(Form.Item,{name:\"enablePackageOptimization\",valuePropName:\"checked\",initialValue:true,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u542F\\u7528\\u5305\\u88C5\\u4F18\\u5316\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"enableMOQOptimization\",valuePropName:\"checked\",initialValue:true,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u542F\\u7528MOQ\\u4F18\\u5316\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"enableOrderMerging\",valuePropName:\"checked\",initialValue:true,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u542F\\u7528\\u8BA2\\u5355\\u5408\\u5E76\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"enableWasteTracking\",valuePropName:\"checked\",initialValue:true,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u542F\\u7528\\u6D6A\\u8D39\\u5F52\\u56E0\\u8DDF\\u8E2A\"})}),/*#__PURE__*/_jsx(Divider,{orientation:\"left\",children:\"\\u4F18\\u5316\\u53C2\\u6570\"}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"wasteThreshold\",label:\"\\u6D6A\\u8D39\\u9608\\u503C(%)\",initialValue:10,children:/*#__PURE__*/_jsx(InputNumber,{min:0,max:50,style:{width:'100%'},placeholder:\"\\u6D6A\\u8D39\\u9608\\u503C\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"mergingWindow\",label:\"\\u5408\\u5E76\\u65F6\\u95F4\\u7A97\\u53E3(\\u5929)\",initialValue:30,children:/*#__PURE__*/_jsx(InputNumber,{min:1,max:90,style:{width:'100%'},placeholder:\"\\u5408\\u5E76\\u65F6\\u95F4\\u7A97\\u53E3\"})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"priorityLevel\",label:\"\\u4F18\\u5148\\u7EA7\",initialValue:\"MEDIUM\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u9009\\u62E9\\u4F18\\u5148\\u7EA7\",children:[/*#__PURE__*/_jsx(Select.Option,{value:\"HIGH\",children:\"\\u9AD8\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"MEDIUM\",children:\"\\u4E2D\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"LOW\",children:\"\\u4F4E\"})]})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"urgentBypass\",valuePropName:\"checked\",initialValue:false,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u7D27\\u6025\\u8BA2\\u5355\\u7ED5\\u8FC7\\u4F18\\u5316\"})})})]})]})})]});};export default PurchaseOptimizationPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "InputNumber", "Select", "DatePicker", "Checkbox", "Divider", "Typography", "Statistic", "Progress", "Tabs", "<PERSON><PERSON>", "message", "SettingOutlined", "ShoppingCartOutlined", "ExportOutlined", "ReloadOutlined", "formatCurrency", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "TabPane", "RangePicker", "PurchaseOptimizationPage", "loading", "setLoading", "optimizationModalVisible", "setOptimizationModalVisible", "optimizationForm", "useForm", "activeTab", "setActiveTab", "loadData", "handleOptimize", "values", "validateFields", "Promise", "resolve", "setTimeout", "success", "error", "mockOptimizationData", "overview", "totalItems", "optimizedItems", "totalSavings", "wasteReduction", "mergedOrders", "optimizationRate", "results", "id", "materialCode", "materialName", "originalQuantity", "optimizedQuantity", "packageSize", "moq", "unitPrice", "originalCost", "optimizedCost", "wasteAmount", "wasteType", "supplier", "leadTime", "urgency", "suggestions", "totalValue", "itemCount", "wastePercentage", "items", "optimizationColumns", "title", "dataIndex", "key", "width", "fixed", "render", "value", "toLocaleString", "record", "style", "color", "children", "concat", "type", "days", "suggestionColumns", "count", "percentage", "toFixed", "_", "size", "icon", "onClick", "confirm", "content", "onOk", "renderOverviewTab", "gutter", "marginBottom", "xs", "sm", "valueStyle", "prefix", "formatter", "Number", "suffix", "md", "strong", "percent", "status", "strokeColor", "fontSize", "fontWeight", "description", "showIcon", "renderOptimizationTab", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "renderSuggestionTab", "justify", "align", "level", "margin", "active<PERSON><PERSON>", "onChange", "tab", "open", "onCancel", "okText", "cancelText", "confirmLoading", "form", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "orientation", "valuePropName", "initialValue", "span", "min", "max", "Option"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/purchase/PurchaseOptimizationPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  InputNumber,\n  Select,\n  DatePicker,\n  Checkbox,\n  Divider,\n  Typography,\n  Statistic,\n  Progress,\n  Tabs,\n  Alert,\n  message,\n} from 'antd';\nimport {\n  SettingOutlined,\n  CalculatorOutlined,\n  ShoppingCartOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport { formatCurrency, formatDate } from '../../utils/format';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { RangePicker } = DatePicker;\n\ninterface OptimizationResult {\n  id: string;\n  materialCode: string;\n  materialName: string;\n  originalQuantity: number;\n  optimizedQuantity: number;\n  packageSize: number;\n  moq: number;\n  unitPrice: number;\n  originalCost: number;\n  optimizedCost: number;\n  wasteAmount: number;\n  wasteType: string;\n  supplier: string;\n  leadTime: number;\n  urgency: string;\n}\n\ninterface PurchaseSuggestion {\n  id: string;\n  supplier: string;\n  totalValue: number;\n  itemCount: number;\n  wasteAmount: number;\n  wastePercentage: number;\n  urgency: string;\n  leadTime: number;\n  items: OptimizationResult[];\n}\n\nconst PurchaseOptimizationPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [optimizationModalVisible, setOptimizationModalVisible] = useState(false);\n  const [optimizationForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('overview');\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = () => {\n    // TODO: 加载优化数据\n  };\n\n  const handleOptimize = async () => {\n    try {\n      const values = await optimizationForm.validateFields();\n      setLoading(true);\n      \n      // TODO: 调用优化API\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      setOptimizationModalVisible(false);\n      message.success('采购优化完成');\n    } catch (error) {\n      message.error('采购优化失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 模拟优化结果数据\n  const mockOptimizationData = {\n    overview: {\n      totalItems: 45,\n      optimizedItems: 38,\n      totalSavings: 125000,\n      wasteReduction: 18.5,\n      mergedOrders: 12,\n      optimizationRate: 84.4,\n    },\n    results: [\n      {\n        id: '1',\n        materialCode: 'ANT-MAIN-001',\n        materialName: '5G主天线单元',\n        originalQuantity: 75,\n        optimizedQuantity: 80,\n        packageSize: 10,\n        moq: 50,\n        unitPrice: 1500,\n        originalCost: 112500,\n        optimizedCost: 120000,\n        wasteAmount: 7500,\n        wasteType: '包装浪费',\n        supplier: '华为技术',\n        leadTime: 14,\n        urgency: 'HIGH',\n      },\n      {\n        id: '2',\n        materialCode: 'RF-AMP-001',\n        materialName: 'RF功率放大器',\n        originalQuantity: 35,\n        optimizedQuantity: 50,\n        packageSize: 5,\n        moq: 50,\n        unitPrice: 2500,\n        originalCost: 87500,\n        optimizedCost: 125000,\n        wasteAmount: 37500,\n        wasteType: 'MOQ浪费',\n        supplier: '中兴通讯',\n        leadTime: 21,\n        urgency: 'HIGH',\n      },\n      {\n        id: '3',\n        materialCode: 'CABLE-001',\n        materialName: '同轴电缆',\n        originalQuantity: 280,\n        optimizedQuantity: 300,\n        packageSize: 100,\n        moq: 200,\n        unitPrice: 25,\n        originalCost: 7000,\n        optimizedCost: 7500,\n        wasteAmount: 500,\n        wasteType: '包装浪费',\n        supplier: '电缆供应商',\n        leadTime: 7,\n        urgency: 'MEDIUM',\n      },\n    ],\n    suggestions: [\n      {\n        id: '1',\n        supplier: '华为技术',\n        totalValue: 245000,\n        itemCount: 8,\n        wasteAmount: 12500,\n        wastePercentage: 5.1,\n        urgency: 'HIGH',\n        leadTime: 14,\n        items: [],\n      },\n      {\n        id: '2',\n        supplier: '中兴通讯',\n        totalValue: 187500,\n        itemCount: 5,\n        wasteAmount: 37500,\n        wastePercentage: 20.0,\n        urgency: 'HIGH',\n        leadTime: 21,\n        items: [],\n      },\n      {\n        id: '3',\n        supplier: '电缆供应商',\n        totalValue: 45000,\n        itemCount: 12,\n        wasteAmount: 2500,\n        wastePercentage: 5.6,\n        urgency: 'MEDIUM',\n        leadTime: 7,\n        items: [],\n      },\n    ],\n  };\n\n  const optimizationColumns = [\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n      fixed: 'left' as const,\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      width: 200,\n    },\n    {\n      title: '原始需求',\n      dataIndex: 'originalQuantity',\n      key: 'originalQuantity',\n      width: 100,\n      render: (value: number) => value.toLocaleString(),\n    },\n    {\n      title: '优化数量',\n      dataIndex: 'optimizedQuantity',\n      key: 'optimizedQuantity',\n      width: 100,\n      render: (value: number, record: OptimizationResult) => (\n        <span style={{ color: value > record.originalQuantity ? '#faad14' : '#52c41a' }}>\n          {value.toLocaleString()}\n        </span>\n      ),\n    },\n    {\n      title: '包装规格',\n      dataIndex: 'packageSize',\n      key: 'packageSize',\n      width: 100,\n      render: (value: number) => `${value}/包`,\n    },\n    {\n      title: 'MOQ',\n      dataIndex: 'moq',\n      key: 'moq',\n      width: 80,\n      render: (value: number) => value.toLocaleString(),\n    },\n    {\n      title: '单价',\n      dataIndex: 'unitPrice',\n      key: 'unitPrice',\n      width: 100,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '原始成本',\n      dataIndex: 'originalCost',\n      key: 'originalCost',\n      width: 120,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '优化成本',\n      dataIndex: 'optimizedCost',\n      key: 'optimizedCost',\n      width: 120,\n      render: (value: number, record: OptimizationResult) => (\n        <span style={{ color: value > record.originalCost ? '#ff4d4f' : '#52c41a' }}>\n          {formatCurrency(value)}\n        </span>\n      ),\n    },\n    {\n      title: '浪费金额',\n      dataIndex: 'wasteAmount',\n      key: 'wasteAmount',\n      width: 120,\n      render: (value: number) => (\n        <span style={{ color: '#ff4d4f' }}>\n          {formatCurrency(value)}\n        </span>\n      ),\n    },\n    {\n      title: '浪费类型',\n      dataIndex: 'wasteType',\n      key: 'wasteType',\n      width: 100,\n      render: (type: string) => {\n        const color = type === '包装浪费' ? 'orange' : type === 'MOQ浪费' ? 'red' : 'blue';\n        return <Tag color={color}>{type}</Tag>;\n      },\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 120,\n    },\n    {\n      title: '交期',\n      dataIndex: 'leadTime',\n      key: 'leadTime',\n      width: 80,\n      render: (days: number) => `${days}天`,\n    },\n    {\n      title: '紧急度',\n      dataIndex: 'urgency',\n      key: 'urgency',\n      width: 80,\n      render: (urgency: string) => {\n        const color = urgency === 'HIGH' ? 'red' : urgency === 'MEDIUM' ? 'orange' : 'green';\n        return <Tag color={color}>{urgency}</Tag>;\n      },\n    },\n  ];\n\n  const suggestionColumns = [\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 150,\n    },\n    {\n      title: '采购金额',\n      dataIndex: 'totalValue',\n      key: 'totalValue',\n      width: 120,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '物料数量',\n      dataIndex: 'itemCount',\n      key: 'itemCount',\n      width: 80,\n      render: (count: number) => `${count}项`,\n    },\n    {\n      title: '浪费金额',\n      dataIndex: 'wasteAmount',\n      key: 'wasteAmount',\n      width: 120,\n      render: (value: number) => (\n        <span style={{ color: '#ff4d4f' }}>\n          {formatCurrency(value)}\n        </span>\n      ),\n    },\n    {\n      title: '浪费率',\n      dataIndex: 'wastePercentage',\n      key: 'wastePercentage',\n      width: 100,\n      render: (percentage: number) => (\n        <span style={{ color: percentage > 10 ? '#ff4d4f' : '#faad14' }}>\n          {percentage.toFixed(1)}%\n        </span>\n      ),\n    },\n    {\n      title: '紧急度',\n      dataIndex: 'urgency',\n      key: 'urgency',\n      width: 80,\n      render: (urgency: string) => {\n        const color = urgency === 'HIGH' ? 'red' : urgency === 'MEDIUM' ? 'orange' : 'green';\n        return <Tag color={color}>{urgency}</Tag>;\n      },\n    },\n    {\n      title: '交期',\n      dataIndex: 'leadTime',\n      key: 'leadTime',\n      width: 80,\n      render: (days: number) => `${days}天`,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      fixed: 'right' as const,\n      render: (_: any, record: PurchaseSuggestion) => (\n        <Space size=\"small\">\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<ShoppingCartOutlined />}\n            onClick={() => {\n              Modal.confirm({\n                title: '生成采购订单',\n                content: `确定为供应商 ${record.supplier} 生成采购订单吗？`,\n                onOk: () => {\n                  message.success('采购订单已生成');\n                },\n              });\n            }}\n          >\n            生成订单\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  const renderOverviewTab = () => (\n    <div>\n      {/* 优化概览 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总物料项\"\n              value={mockOptimizationData.overview.totalItems}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"已优化项\"\n              value={mockOptimizationData.overview.optimizedItems}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"节省金额\"\n              value={mockOptimizationData.overview.totalSavings}\n              prefix=\"¥\"\n              valueStyle={{ color: '#52c41a' }}\n              formatter={(value) => formatCurrency(Number(value))}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"浪费减少\"\n              value={mockOptimizationData.overview.wasteReduction}\n              suffix=\"%\"\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 优化进度 */}\n      <Card title=\"优化进度\" style={{ marginBottom: 24 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} md={12}>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>优化完成率</Text>\n              <Progress\n                percent={mockOptimizationData.overview.optimizationRate}\n                status=\"active\"\n                strokeColor=\"#52c41a\"\n              />\n            </div>\n          </Col>\n          <Col xs={24} md={12}>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>订单合并数</Text>\n              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>\n                {mockOptimizationData.overview.mergedOrders}\n              </div>\n            </div>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 优化建议 */}\n      <Card title=\"优化建议\">\n        <Alert\n          message=\"优化建议\"\n          description={\n            <ul>\n              <li>建议合并华为技术和中兴通讯的订单，可减少15%的采购成本</li>\n              <li>CABLE-001的包装规格可以调整，减少包装浪费</li>\n              <li>RF-AMP-001的MOQ较高，建议寻找替代供应商</li>\n              <li>建议设置紧急订单白名单，优先保证关键交付</li>\n            </ul>\n          }\n          type=\"info\"\n          showIcon\n        />\n      </Card>\n    </div>\n  );\n\n  const renderOptimizationTab = () => (\n    <div>\n      <Card title=\"优化结果详情\">\n        <Table\n          columns={optimizationColumns}\n          dataSource={mockOptimizationData.results}\n          rowKey=\"id\"\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  const renderSuggestionTab = () => (\n    <div>\n      <Card title=\"采购建议\">\n        <Table\n          columns={suggestionColumns}\n          dataSource={mockOptimizationData.suggestions}\n          rowKey=\"id\"\n          scroll={{ x: 1000 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              采购优化\n            </Title>\n            <Text type=\"secondary\">\n              包装优化、MOQ处理和采购建议生成\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<ReloadOutlined />} onClick={loadData}>\n                刷新\n              </Button>\n              <Button icon={<ExportOutlined />}>\n                导出\n              </Button>\n              <Button\n                type=\"primary\"\n                icon={<SettingOutlined />}\n                onClick={() => setOptimizationModalVisible(true)}\n              >\n                开始优化\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab=\"优化概览\" key=\"overview\">\n            {renderOverviewTab()}\n          </TabPane>\n          <TabPane tab=\"优化结果\" key=\"optimization\">\n            {renderOptimizationTab()}\n          </TabPane>\n          <TabPane tab=\"采购建议\" key=\"suggestion\">\n            {renderSuggestionTab()}\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 优化设置模态框 */}\n      <Modal\n        title=\"采购优化设置\"\n        open={optimizationModalVisible}\n        onOk={handleOptimize}\n        onCancel={() => setOptimizationModalVisible(false)}\n        width={600}\n        okText=\"开始优化\"\n        cancelText=\"取消\"\n        confirmLoading={loading}\n      >\n        <Form form={optimizationForm} layout=\"vertical\">\n          <Form.Item\n            name=\"dateRange\"\n            label=\"优化周期\"\n            rules={[{ required: true, message: '请选择优化周期' }]}\n          >\n            <RangePicker\n              style={{ width: '100%' }}\n              placeholder={['开始日期', '结束日期']}\n            />\n          </Form.Item>\n\n          <Divider orientation=\"left\">优化策略</Divider>\n\n          <Form.Item\n            name=\"enablePackageOptimization\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Checkbox>启用包装优化</Checkbox>\n          </Form.Item>\n\n          <Form.Item\n            name=\"enableMOQOptimization\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Checkbox>启用MOQ优化</Checkbox>\n          </Form.Item>\n\n          <Form.Item\n            name=\"enableOrderMerging\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Checkbox>启用订单合并</Checkbox>\n          </Form.Item>\n\n          <Form.Item\n            name=\"enableWasteTracking\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Checkbox>启用浪费归因跟踪</Checkbox>\n          </Form.Item>\n\n          <Divider orientation=\"left\">优化参数</Divider>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"wasteThreshold\"\n                label=\"浪费阈值(%)\"\n                initialValue={10}\n              >\n                <InputNumber\n                  min={0}\n                  max={50}\n                  style={{ width: '100%' }}\n                  placeholder=\"浪费阈值\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"mergingWindow\"\n                label=\"合并时间窗口(天)\"\n                initialValue={30}\n              >\n                <InputNumber\n                  min={1}\n                  max={90}\n                  style={{ width: '100%' }}\n                  placeholder=\"合并时间窗口\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"priorityLevel\"\n                label=\"优先级\"\n                initialValue=\"MEDIUM\"\n              >\n                <Select placeholder=\"选择优先级\">\n                  <Select.Option value=\"HIGH\">高</Select.Option>\n                  <Select.Option value=\"MEDIUM\">中</Select.Option>\n                  <Select.Option value=\"LOW\">低</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"urgentBypass\"\n                valuePropName=\"checked\"\n                initialValue={false}\n              >\n                <Checkbox>紧急订单绕过优化</Checkbox>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default PurchaseOptimizationPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,GAAG,CACHC,GAAG,CACHC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,GAAG,CACHC,KAAK,CACLC,IAAI,CAEJC,WAAW,CACXC,MAAM,CACNC,UAAU,CACVC,QAAQ,CACRC,OAAO,CACPC,UAAU,CACVC,SAAS,CACTC,QAAQ,CACRC,IAAI,CACJC,KAAK,CACLC,OAAO,KACF,MAAM,CACb,OACEC,eAAe,CAEfC,oBAAoB,CAGpBC,cAAc,CACdC,cAAc,KACT,mBAAmB,CAC1B,OAASC,cAAc,KAAoB,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhE,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGhB,UAAU,CAClC,KAAM,CAAEiB,OAAQ,CAAC,CAAGd,IAAI,CACxB,KAAM,CAAEe,WAAY,CAAC,CAAGrB,UAAU,CAgClC,KAAM,CAAAsB,wBAAkC,CAAGA,CAAA,GAAM,CAC/C,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACsC,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAC/E,KAAM,CAACwC,gBAAgB,CAAC,CAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC,CACzC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG3C,QAAQ,CAAC,UAAU,CAAC,CAEtDC,SAAS,CAAC,IAAM,CACd2C,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,QAAQ,CAAGA,CAAA,GAAM,CACrB;AAAA,CACD,CAED,KAAM,CAAAC,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAN,gBAAgB,CAACO,cAAc,CAAC,CAAC,CACtDV,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,IAAI,CAAAW,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvDV,2BAA2B,CAAC,KAAK,CAAC,CAClClB,OAAO,CAAC8B,OAAO,CAAC,QAAQ,CAAC,CAC3B,CAAE,MAAOC,KAAK,CAAE,CACd/B,OAAO,CAAC+B,KAAK,CAAC,QAAQ,CAAC,CACzB,CAAC,OAAS,CACRf,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAgB,oBAAoB,CAAG,CAC3BC,QAAQ,CAAE,CACRC,UAAU,CAAE,EAAE,CACdC,cAAc,CAAE,EAAE,CAClBC,YAAY,CAAE,MAAM,CACpBC,cAAc,CAAE,IAAI,CACpBC,YAAY,CAAE,EAAE,CAChBC,gBAAgB,CAAE,IACpB,CAAC,CACDC,OAAO,CAAE,CACP,CACEC,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,SAAS,CACvBC,gBAAgB,CAAE,EAAE,CACpBC,iBAAiB,CAAE,EAAE,CACrBC,WAAW,CAAE,EAAE,CACfC,GAAG,CAAE,EAAE,CACPC,SAAS,CAAE,IAAI,CACfC,YAAY,CAAE,MAAM,CACpBC,aAAa,CAAE,MAAM,CACrBC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,EAAE,CACZC,OAAO,CAAE,MACX,CAAC,CACD,CACEd,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,SAAS,CACvBC,gBAAgB,CAAE,EAAE,CACpBC,iBAAiB,CAAE,EAAE,CACrBC,WAAW,CAAE,CAAC,CACdC,GAAG,CAAE,EAAE,CACPC,SAAS,CAAE,IAAI,CACfC,YAAY,CAAE,KAAK,CACnBC,aAAa,CAAE,MAAM,CACrBC,WAAW,CAAE,KAAK,CAClBC,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,EAAE,CACZC,OAAO,CAAE,MACX,CAAC,CACD,CACEd,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,WAAW,CACzBC,YAAY,CAAE,MAAM,CACpBC,gBAAgB,CAAE,GAAG,CACrBC,iBAAiB,CAAE,GAAG,CACtBC,WAAW,CAAE,GAAG,CAChBC,GAAG,CAAE,GAAG,CACRC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,IAAI,CAClBC,aAAa,CAAE,IAAI,CACnBC,WAAW,CAAE,GAAG,CAChBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,QACX,CAAC,CACF,CACDC,WAAW,CAAE,CACX,CACEf,EAAE,CAAE,GAAG,CACPY,QAAQ,CAAE,MAAM,CAChBI,UAAU,CAAE,MAAM,CAClBC,SAAS,CAAE,CAAC,CACZP,WAAW,CAAE,KAAK,CAClBQ,eAAe,CAAE,GAAG,CACpBJ,OAAO,CAAE,MAAM,CACfD,QAAQ,CAAE,EAAE,CACZM,KAAK,CAAE,EACT,CAAC,CACD,CACEnB,EAAE,CAAE,GAAG,CACPY,QAAQ,CAAE,MAAM,CAChBI,UAAU,CAAE,MAAM,CAClBC,SAAS,CAAE,CAAC,CACZP,WAAW,CAAE,KAAK,CAClBQ,eAAe,CAAE,IAAI,CACrBJ,OAAO,CAAE,MAAM,CACfD,QAAQ,CAAE,EAAE,CACZM,KAAK,CAAE,EACT,CAAC,CACD,CACEnB,EAAE,CAAE,GAAG,CACPY,QAAQ,CAAE,OAAO,CACjBI,UAAU,CAAE,KAAK,CACjBC,SAAS,CAAE,EAAE,CACbP,WAAW,CAAE,IAAI,CACjBQ,eAAe,CAAE,GAAG,CACpBJ,OAAO,CAAE,QAAQ,CACjBD,QAAQ,CAAE,CAAC,CACXM,KAAK,CAAE,EACT,CAAC,CAEL,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAG,CAC1B,CACEC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,MACT,CAAC,CACD,CACEJ,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,kBAAkB,CAC7BC,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGC,KAAa,EAAKA,KAAK,CAACC,cAAc,CAAC,CAClD,CAAC,CACD,CACEP,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,mBAAmB,CAC9BC,GAAG,CAAE,mBAAmB,CACxBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACC,KAAa,CAAEE,MAA0B,gBAChD/D,IAAA,SAAMgE,KAAK,CAAE,CAAEC,KAAK,CAAEJ,KAAK,CAAGE,MAAM,CAAC1B,gBAAgB,CAAG,SAAS,CAAG,SAAU,CAAE,CAAA6B,QAAA,CAC7EL,KAAK,CAACC,cAAc,CAAC,CAAC,CACnB,CAEV,CAAC,CACD,CACEP,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGC,KAAa,KAAAM,MAAA,CAAQN,KAAK,WACrC,CAAC,CACD,CACEN,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,KAAK,CAChBC,GAAG,CAAE,KAAK,CACVC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGC,KAAa,EAAKA,KAAK,CAACC,cAAc,CAAC,CAClD,CAAC,CACD,CACEP,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGC,KAAa,EAAK/D,cAAc,CAAC+D,KAAK,CACjD,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGC,KAAa,EAAK/D,cAAc,CAAC+D,KAAK,CACjD,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAEA,CAACC,KAAa,CAAEE,MAA0B,gBAChD/D,IAAA,SAAMgE,KAAK,CAAE,CAAEC,KAAK,CAAEJ,KAAK,CAAGE,MAAM,CAACrB,YAAY,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAwB,QAAA,CACzEpE,cAAc,CAAC+D,KAAK,CAAC,CAClB,CAEV,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGC,KAAa,eACpB7D,IAAA,SAAMgE,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAC,QAAA,CAC/BpE,cAAc,CAAC+D,KAAK,CAAC,CAClB,CAEV,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGQ,IAAY,EAAK,CACxB,KAAM,CAAAH,KAAK,CAAGG,IAAI,GAAK,MAAM,CAAG,QAAQ,CAAGA,IAAI,GAAK,OAAO,CAAG,KAAK,CAAG,MAAM,CAC5E,mBAAOpE,IAAA,CAACpB,GAAG,EAACqF,KAAK,CAAEA,KAAM,CAAAC,QAAA,CAAEE,IAAI,CAAM,CAAC,CACxC,CACF,CAAC,CACD,CACEb,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGS,IAAY,KAAAF,MAAA,CAAQE,IAAI,UACnC,CAAC,CACD,CACEd,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGZ,OAAe,EAAK,CAC3B,KAAM,CAAAiB,KAAK,CAAGjB,OAAO,GAAK,MAAM,CAAG,KAAK,CAAGA,OAAO,GAAK,QAAQ,CAAG,QAAQ,CAAG,OAAO,CACpF,mBAAOhD,IAAA,CAACpB,GAAG,EAACqF,KAAK,CAAEA,KAAM,CAAAC,QAAA,CAAElB,OAAO,CAAM,CAAC,CAC3C,CACF,CAAC,CACF,CAED,KAAM,CAAAsB,iBAAiB,CAAG,CACxB,CACEf,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGC,KAAa,EAAK/D,cAAc,CAAC+D,KAAK,CACjD,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGW,KAAa,KAAAJ,MAAA,CAAQI,KAAK,UACrC,CAAC,CACD,CACEhB,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGC,KAAa,eACpB7D,IAAA,SAAMgE,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAC,QAAA,CAC/BpE,cAAc,CAAC+D,KAAK,CAAC,CAClB,CAEV,CAAC,CACD,CACEN,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,iBAAiB,CAC5BC,GAAG,CAAE,iBAAiB,CACtBC,KAAK,CAAE,GAAG,CACVE,MAAM,CAAGY,UAAkB,eACzBtE,KAAA,SAAM8D,KAAK,CAAE,CAAEC,KAAK,CAAEO,UAAU,CAAG,EAAE,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAN,QAAA,EAC7DM,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,GACzB,EAAM,CAEV,CAAC,CACD,CACElB,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,SAAS,CACpBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGZ,OAAe,EAAK,CAC3B,KAAM,CAAAiB,KAAK,CAAGjB,OAAO,GAAK,MAAM,CAAG,KAAK,CAAGA,OAAO,GAAK,QAAQ,CAAG,QAAQ,CAAG,OAAO,CACpF,mBAAOhD,IAAA,CAACpB,GAAG,EAACqF,KAAK,CAAEA,KAAM,CAAAC,QAAA,CAAElB,OAAO,CAAM,CAAC,CAC3C,CACF,CAAC,CACD,CACEO,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EAAE,CACTE,MAAM,CAAGS,IAAY,KAAAF,MAAA,CAAQE,IAAI,UACnC,CAAC,CACD,CACEd,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,OAAgB,CACvBC,MAAM,CAAEA,CAACc,CAAM,CAAEX,MAA0B,gBACzC/D,IAAA,CAACrB,KAAK,EAACgG,IAAI,CAAC,OAAO,CAAAT,QAAA,cACjBlE,IAAA,CAACvB,MAAM,EACL2F,IAAI,CAAC,SAAS,CACdO,IAAI,CAAC,OAAO,CACZC,IAAI,cAAE5E,IAAA,CAACL,oBAAoB,GAAE,CAAE,CAC/BkF,OAAO,CAAEA,CAAA,GAAM,CACbhG,KAAK,CAACiG,OAAO,CAAC,CACZvB,KAAK,CAAE,QAAQ,CACfwB,OAAO,yCAAAZ,MAAA,CAAYJ,MAAM,CAACjB,QAAQ,qDAAW,CAC7CkC,IAAI,CAAEA,CAAA,GAAM,CACVvF,OAAO,CAAC8B,OAAO,CAAC,SAAS,CAAC,CAC5B,CACF,CAAC,CAAC,CACJ,CAAE,CAAA2C,QAAA,CACH,0BAED,CAAQ,CAAC,CACJ,CAEX,CAAC,CACF,CAED,KAAM,CAAAe,iBAAiB,CAAGA,CAAA,gBACxB/E,KAAA,QAAAgE,QAAA,eAEEhE,KAAA,CAAC3B,GAAG,EAAC2G,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAClB,KAAK,CAAE,CAAEmB,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,eACjDlE,IAAA,CAACxB,GAAG,EAAC4G,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnB,QAAA,cACjBlE,IAAA,CAAC1B,IAAI,EAAA4F,QAAA,cACHlE,IAAA,CAACX,SAAS,EACRkE,KAAK,CAAC,0BAAM,CACZM,KAAK,CAAEpC,oBAAoB,CAACC,QAAQ,CAACC,UAAW,CAChD2D,UAAU,CAAE,CAAErB,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNjE,IAAA,CAACxB,GAAG,EAAC4G,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnB,QAAA,cACjBlE,IAAA,CAAC1B,IAAI,EAAA4F,QAAA,cACHlE,IAAA,CAACX,SAAS,EACRkE,KAAK,CAAC,0BAAM,CACZM,KAAK,CAAEpC,oBAAoB,CAACC,QAAQ,CAACE,cAAe,CACpD0D,UAAU,CAAE,CAAErB,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNjE,IAAA,CAACxB,GAAG,EAAC4G,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnB,QAAA,cACjBlE,IAAA,CAAC1B,IAAI,EAAA4F,QAAA,cACHlE,IAAA,CAACX,SAAS,EACRkE,KAAK,CAAC,0BAAM,CACZM,KAAK,CAAEpC,oBAAoB,CAACC,QAAQ,CAACG,YAAa,CAClD0D,MAAM,CAAC,MAAG,CACVD,UAAU,CAAE,CAAErB,KAAK,CAAE,SAAU,CAAE,CACjCuB,SAAS,CAAG3B,KAAK,EAAK/D,cAAc,CAAC2F,MAAM,CAAC5B,KAAK,CAAC,CAAE,CACrD,CAAC,CACE,CAAC,CACJ,CAAC,cACN7D,IAAA,CAACxB,GAAG,EAAC4G,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAnB,QAAA,cACjBlE,IAAA,CAAC1B,IAAI,EAAA4F,QAAA,cACHlE,IAAA,CAACX,SAAS,EACRkE,KAAK,CAAC,0BAAM,CACZM,KAAK,CAAEpC,oBAAoB,CAACC,QAAQ,CAACI,cAAe,CACpD4D,MAAM,CAAC,GAAG,CACVJ,UAAU,CAAE,CAAErB,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGNjE,IAAA,CAAC1B,IAAI,EAACiF,KAAK,CAAC,0BAAM,CAACS,KAAK,CAAE,CAAEmB,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,cAC7ChE,KAAA,CAAC3B,GAAG,EAAC2G,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAhB,QAAA,eACpBlE,IAAA,CAACxB,GAAG,EAAC4G,EAAE,CAAE,EAAG,CAACO,EAAE,CAAE,EAAG,CAAAzB,QAAA,cAClBhE,KAAA,QAAK8D,KAAK,CAAE,CAAEmB,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,eAC/BlE,IAAA,CAACI,IAAI,EAACwF,MAAM,MAAA1B,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBlE,IAAA,CAACV,QAAQ,EACPuG,OAAO,CAAEpE,oBAAoB,CAACC,QAAQ,CAACM,gBAAiB,CACxD8D,MAAM,CAAC,QAAQ,CACfC,WAAW,CAAC,SAAS,CACtB,CAAC,EACC,CAAC,CACH,CAAC,cACN/F,IAAA,CAACxB,GAAG,EAAC4G,EAAE,CAAE,EAAG,CAACO,EAAE,CAAE,EAAG,CAAAzB,QAAA,cAClBhE,KAAA,QAAK8D,KAAK,CAAE,CAAEmB,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,eAC/BlE,IAAA,CAACI,IAAI,EAACwF,MAAM,MAAA1B,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBlE,IAAA,QAAKgE,KAAK,CAAE,CAAEgC,QAAQ,CAAE,EAAE,CAAEC,UAAU,CAAE,MAAM,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAAAC,QAAA,CAChEzC,oBAAoB,CAACC,QAAQ,CAACK,YAAY,CACxC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAGP/B,IAAA,CAAC1B,IAAI,EAACiF,KAAK,CAAC,0BAAM,CAAAW,QAAA,cAChBlE,IAAA,CAACR,KAAK,EACJC,OAAO,CAAC,0BAAM,CACdyG,WAAW,cACThG,KAAA,OAAAgE,QAAA,eACElE,IAAA,OAAAkE,QAAA,CAAI,2JAA4B,CAAI,CAAC,cACrClE,IAAA,OAAAkE,QAAA,CAAI,2GAAyB,CAAI,CAAC,cAClClE,IAAA,OAAAkE,QAAA,CAAI,6FAA0B,CAAI,CAAC,cACnClE,IAAA,OAAAkE,QAAA,CAAI,0HAAoB,CAAI,CAAC,EAC3B,CACL,CACDE,IAAI,CAAC,MAAM,CACX+B,QAAQ,MACT,CAAC,CACE,CAAC,EACJ,CACN,CAED,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,gBAC5BpG,IAAA,QAAAkE,QAAA,cACElE,IAAA,CAAC1B,IAAI,EAACiF,KAAK,CAAC,sCAAQ,CAAAW,QAAA,cAClBlE,IAAA,CAACtB,KAAK,EACJ2H,OAAO,CAAE/C,mBAAoB,CAC7BgD,UAAU,CAAE7E,oBAAoB,CAACQ,OAAQ,CACzCsE,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAA5C,MAAA,CACjB4C,KAAK,CAAC,CAAC,CAAC,MAAA5C,MAAA,CAAI4C,KAAK,CAAC,CAAC,CAAC,oBAAA5C,MAAA,CAAQ2C,KAAK,WAC1C,CAAE,CACH,CAAC,CACE,CAAC,CACJ,CACN,CAED,KAAM,CAAAE,mBAAmB,CAAGA,CAAA,gBAC1BhH,IAAA,QAAAkE,QAAA,cACElE,IAAA,CAAC1B,IAAI,EAACiF,KAAK,CAAC,0BAAM,CAAAW,QAAA,cAChBlE,IAAA,CAACtB,KAAK,EACJ2H,OAAO,CAAE/B,iBAAkB,CAC3BgC,UAAU,CAAE7E,oBAAoB,CAACwB,WAAY,CAC7CsD,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACC,KAAK,CAAEC,KAAK,aAAA5C,MAAA,CACjB4C,KAAK,CAAC,CAAC,CAAC,MAAA5C,MAAA,CAAI4C,KAAK,CAAC,CAAC,CAAC,oBAAA5C,MAAA,CAAQ2C,KAAK,WAC1C,CAAE,CACH,CAAC,CACE,CAAC,CACJ,CACN,CAED,mBACE5G,KAAA,QAAAgE,QAAA,eACEhE,KAAA,CAAC5B,IAAI,EAAA4F,QAAA,eACHhE,KAAA,CAAC3B,GAAG,EAAC0I,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAAClD,KAAK,CAAE,CAAEmB,YAAY,CAAE,EAAG,CAAE,CAAAjB,QAAA,eACtEhE,KAAA,CAAC1B,GAAG,EAAA0F,QAAA,eACFlE,IAAA,CAACG,KAAK,EAACgH,KAAK,CAAE,CAAE,CAACnD,KAAK,CAAE,CAAEoD,MAAM,CAAE,CAAE,CAAE,CAAAlD,QAAA,CAAC,0BAEvC,CAAO,CAAC,cACRlE,IAAA,CAACI,IAAI,EAACgE,IAAI,CAAC,WAAW,CAAAF,QAAA,CAAC,yFAEvB,CAAM,CAAC,EACJ,CAAC,cACNlE,IAAA,CAACxB,GAAG,EAAA0F,QAAA,cACFhE,KAAA,CAACvB,KAAK,EAAAuF,QAAA,eACJlE,IAAA,CAACvB,MAAM,EAACmG,IAAI,cAAE5E,IAAA,CAACH,cAAc,GAAE,CAAE,CAACgF,OAAO,CAAE7D,QAAS,CAAAkD,QAAA,CAAC,cAErD,CAAQ,CAAC,cACTlE,IAAA,CAACvB,MAAM,EAACmG,IAAI,cAAE5E,IAAA,CAACJ,cAAc,GAAE,CAAE,CAAAsE,QAAA,CAAC,cAElC,CAAQ,CAAC,cACTlE,IAAA,CAACvB,MAAM,EACL2F,IAAI,CAAC,SAAS,CACdQ,IAAI,cAAE5E,IAAA,CAACN,eAAe,GAAE,CAAE,CAC1BmF,OAAO,CAAEA,CAAA,GAAMlE,2BAA2B,CAAC,IAAI,CAAE,CAAAuD,QAAA,CAClD,0BAED,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAENhE,KAAA,CAACX,IAAI,EAAC8H,SAAS,CAAEvG,SAAU,CAACwG,QAAQ,CAAEvG,YAAa,CAAAmD,QAAA,eACjDlE,IAAA,CAACK,OAAO,EAACkH,GAAG,CAAC,0BAAM,CAAArD,QAAA,CAChBe,iBAAiB,CAAC,CAAC,EADE,UAEf,CAAC,cACVjF,IAAA,CAACK,OAAO,EAACkH,GAAG,CAAC,0BAAM,CAAArD,QAAA,CAChBkC,qBAAqB,CAAC,CAAC,EADF,cAEf,CAAC,cACVpG,IAAA,CAACK,OAAO,EAACkH,GAAG,CAAC,0BAAM,CAAArD,QAAA,CAChB8C,mBAAmB,CAAC,CAAC,EADA,YAEf,CAAC,EACN,CAAC,EACH,CAAC,cAGPhH,IAAA,CAACnB,KAAK,EACJ0E,KAAK,CAAC,sCAAQ,CACdiE,IAAI,CAAE9G,wBAAyB,CAC/BsE,IAAI,CAAE/D,cAAe,CACrBwG,QAAQ,CAAEA,CAAA,GAAM9G,2BAA2B,CAAC,KAAK,CAAE,CACnD+C,KAAK,CAAE,GAAI,CACXgE,MAAM,CAAC,0BAAM,CACbC,UAAU,CAAC,cAAI,CACfC,cAAc,CAAEpH,OAAQ,CAAA0D,QAAA,cAExBhE,KAAA,CAACpB,IAAI,EAAC+I,IAAI,CAAEjH,gBAAiB,CAACkH,MAAM,CAAC,UAAU,CAAA5D,QAAA,eAC7ClE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE1I,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAyE,QAAA,cAEhDlE,IAAA,CAACM,WAAW,EACV0D,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CACzB0E,WAAW,CAAE,CAAC,MAAM,CAAE,MAAM,CAAE,CAC/B,CAAC,CACO,CAAC,cAEZpI,IAAA,CAACb,OAAO,EAACkJ,WAAW,CAAC,MAAM,CAAAnE,QAAA,CAAC,0BAAI,CAAS,CAAC,cAE1ClE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,2BAA2B,CAChCM,aAAa,CAAC,SAAS,CACvBC,YAAY,CAAE,IAAK,CAAArE,QAAA,cAEnBlE,IAAA,CAACd,QAAQ,EAAAgF,QAAA,CAAC,sCAAM,CAAU,CAAC,CAClB,CAAC,cAEZlE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,uBAAuB,CAC5BM,aAAa,CAAC,SAAS,CACvBC,YAAY,CAAE,IAAK,CAAArE,QAAA,cAEnBlE,IAAA,CAACd,QAAQ,EAAAgF,QAAA,CAAC,6BAAO,CAAU,CAAC,CACnB,CAAC,cAEZlE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,oBAAoB,CACzBM,aAAa,CAAC,SAAS,CACvBC,YAAY,CAAE,IAAK,CAAArE,QAAA,cAEnBlE,IAAA,CAACd,QAAQ,EAAAgF,QAAA,CAAC,sCAAM,CAAU,CAAC,CAClB,CAAC,cAEZlE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,qBAAqB,CAC1BM,aAAa,CAAC,SAAS,CACvBC,YAAY,CAAE,IAAK,CAAArE,QAAA,cAEnBlE,IAAA,CAACd,QAAQ,EAAAgF,QAAA,CAAC,kDAAQ,CAAU,CAAC,CACpB,CAAC,cAEZlE,IAAA,CAACb,OAAO,EAACkJ,WAAW,CAAC,MAAM,CAAAnE,QAAA,CAAC,0BAAI,CAAS,CAAC,cAE1ChE,KAAA,CAAC3B,GAAG,EAAC2G,MAAM,CAAE,EAAG,CAAAhB,QAAA,eACdlE,IAAA,CAACxB,GAAG,EAACgK,IAAI,CAAE,EAAG,CAAAtE,QAAA,cACZlE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAC,6BAAS,CACfM,YAAY,CAAE,EAAG,CAAArE,QAAA,cAEjBlE,IAAA,CAACjB,WAAW,EACV0J,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACR1E,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CACzB0E,WAAW,CAAC,0BAAM,CACnB,CAAC,CACO,CAAC,CACT,CAAC,cACNpI,IAAA,CAACxB,GAAG,EAACgK,IAAI,CAAE,EAAG,CAAAtE,QAAA,cACZlE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,eAAe,CACpBC,KAAK,CAAC,8CAAW,CACjBM,YAAY,CAAE,EAAG,CAAArE,QAAA,cAEjBlE,IAAA,CAACjB,WAAW,EACV0J,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACR1E,KAAK,CAAE,CAAEN,KAAK,CAAE,MAAO,CAAE,CACzB0E,WAAW,CAAC,sCAAQ,CACrB,CAAC,CACO,CAAC,CACT,CAAC,EACH,CAAC,cAENlI,KAAA,CAAC3B,GAAG,EAAC2G,MAAM,CAAE,EAAG,CAAAhB,QAAA,eACdlE,IAAA,CAACxB,GAAG,EAACgK,IAAI,CAAE,EAAG,CAAAtE,QAAA,cACZlE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,eAAe,CACpBC,KAAK,CAAC,oBAAK,CACXM,YAAY,CAAC,QAAQ,CAAArE,QAAA,cAErBhE,KAAA,CAAClB,MAAM,EAACoJ,WAAW,CAAC,gCAAO,CAAAlE,QAAA,eACzBlE,IAAA,CAAChB,MAAM,CAAC2J,MAAM,EAAC9E,KAAK,CAAC,MAAM,CAAAK,QAAA,CAAC,QAAC,CAAe,CAAC,cAC7ClE,IAAA,CAAChB,MAAM,CAAC2J,MAAM,EAAC9E,KAAK,CAAC,QAAQ,CAAAK,QAAA,CAAC,QAAC,CAAe,CAAC,cAC/ClE,IAAA,CAAChB,MAAM,CAAC2J,MAAM,EAAC9E,KAAK,CAAC,KAAK,CAAAK,QAAA,CAAC,QAAC,CAAe,CAAC,EACtC,CAAC,CACA,CAAC,CACT,CAAC,cACNlE,IAAA,CAACxB,GAAG,EAACgK,IAAI,CAAE,EAAG,CAAAtE,QAAA,cACZlE,IAAA,CAAClB,IAAI,CAACiJ,IAAI,EACRC,IAAI,CAAC,cAAc,CACnBM,aAAa,CAAC,SAAS,CACvBC,YAAY,CAAE,KAAM,CAAArE,QAAA,cAEpBlE,IAAA,CAACd,QAAQ,EAAAgF,QAAA,CAAC,kDAAQ,CAAU,CAAC,CACpB,CAAC,CACT,CAAC,EACH,CAAC,EACF,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3D,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}