{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\dashboard\\\\DashboardPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useMemo, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, Progress, Table, List, Badge, Typography, Space, Button, DatePicker } from 'antd';\nimport { ArrowDownOutlined, DollarOutlined, WarningOutlined, RiseOutlined, FileTextOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport dayjs from 'dayjs';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { formatCurrency, formatPercentage } from '../../utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\n\n// 模拟数据\nconst mockMetrics = {\n  totalOrders: 156,\n  totalRevenue: 2580000,\n  wasteReduction: 0.15,\n  costSavings: 380000,\n  inventoryTurnover: 8.5,\n  onTimeDelivery: 0.95\n};\nconst mockWasteTrend = [{\n  month: '1月',\n  packaging: 2.5,\n  moq: 3.2,\n  cutting: 1.8,\n  expiry: 0.5\n}, {\n  month: '2月',\n  packaging: 2.1,\n  moq: 2.8,\n  cutting: 1.5,\n  expiry: 0.3\n}, {\n  month: '3月',\n  packaging: 1.9,\n  moq: 2.5,\n  cutting: 1.3,\n  expiry: 0.4\n}, {\n  month: '4月',\n  packaging: 1.7,\n  moq: 2.2,\n  cutting: 1.1,\n  expiry: 0.2\n}, {\n  month: '5月',\n  packaging: 1.5,\n  moq: 2.0,\n  cutting: 1.0,\n  expiry: 0.3\n}, {\n  month: '6月',\n  packaging: 1.3,\n  moq: 1.8,\n  cutting: 0.9,\n  expiry: 0.1\n}];\nconst mockCostTrend = [{\n  month: '1月',\n  material: 850000,\n  labor: 120000,\n  overhead: 80000\n}, {\n  month: '2月',\n  material: 920000,\n  labor: 125000,\n  overhead: 82000\n}, {\n  month: '3月',\n  material: 880000,\n  labor: 118000,\n  overhead: 78000\n}, {\n  month: '4月',\n  material: 950000,\n  labor: 130000,\n  overhead: 85000\n}, {\n  month: '5月',\n  material: 890000,\n  labor: 122000,\n  overhead: 79000\n}, {\n  month: '6月',\n  material: 910000,\n  labor: 128000,\n  overhead: 83000\n}];\nconst mockOrderStatus = [{\n  name: '已完成',\n  value: 45,\n  color: '#52c41a'\n}, {\n  name: '进行中',\n  value: 30,\n  color: '#1890ff'\n}, {\n  name: '待开始',\n  value: 20,\n  color: '#faad14'\n}, {\n  name: '已延期',\n  value: 5,\n  color: '#f5222d'\n}];\nconst mockRecentOrders = [{\n  id: 'ORD-001',\n  customer: '华为技术',\n  amount: 125000,\n  status: '进行中',\n  date: '2024-01-15'\n}, {\n  id: 'ORD-002',\n  customer: '中兴通讯',\n  amount: 98000,\n  status: '已完成',\n  date: '2024-01-14'\n}, {\n  id: 'ORD-003',\n  customer: '大唐移动',\n  amount: 156000,\n  status: '待开始',\n  date: '2024-01-13'\n}, {\n  id: 'ORD-004',\n  customer: '爱立信',\n  amount: 89000,\n  status: '进行中',\n  date: '2024-01-12'\n}, {\n  id: 'ORD-005',\n  customer: '诺基亚',\n  amount: 234000,\n  status: '已完成',\n  date: '2024-01-11'\n}];\nconst mockAlerts = [{\n  id: 1,\n  type: 'warning',\n  message: '物料 ANT-001 库存不足，当前库存：50件',\n  time: '2小时前'\n}, {\n  id: 2,\n  type: 'error',\n  message: '订单 ORD-003 交期可能延误',\n  time: '4小时前'\n}, {\n  id: 3,\n  type: 'info',\n  message: 'ECN-2024-001 已通过审批',\n  time: '6小时前'\n}, {\n  id: 4,\n  type: 'warning',\n  message: '供应商价格异常波动：RF-002 上涨15%',\n  time: '8小时前'\n}];\nconst DashboardPage = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    user,\n    currentRole\n  } = useAppSelector(state => state.auth);\n  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'day'), dayjs()]);\n  useEffect(() => {\n    // 加载仪表板数据\n    // dispatch(fetchDashboardMetrics());\n  }, [dispatch, dateRange]);\n\n  // 使用useCallback优化函数，避免不必要的重新渲染\n  const getStatusColor = useCallback(status => {\n    switch (status) {\n      case '已完成':\n        return 'success';\n      case '进行中':\n        return 'processing';\n      case '待开始':\n        return 'warning';\n      case '已延期':\n        return 'error';\n      default:\n        return 'default';\n    }\n  }, []);\n  const getAlertIcon = useCallback(type => {\n    switch (type) {\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(WarningOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 30\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(WarningOutlined, {\n          style: {\n            color: '#f5222d'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 28\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 27\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 23\n        }, this);\n    }\n  }, []);\n\n  // 使用useMemo优化计算密集型数据\n  const processedMetrics = useMemo(() => ({\n    ...mockMetrics,\n    wasteReductionFormatted: formatPercentage(mockMetrics.wasteReduction),\n    totalRevenueFormatted: formatCurrency(mockMetrics.totalRevenue),\n    costSavingsFormatted: formatCurrency(mockMetrics.costSavings)\n  }), []);\n  const handleDateRangeChange = useCallback(dates => {\n    if (dates) {\n      setDateRange(dates);\n    }\n  }, []);\n  const handleRefreshData = useCallback(() => {\n    // TODO: 实现数据刷新逻辑\n    console.log('刷新数据');\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      justify: \"space-between\",\n      align: \"middle\",\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            margin: 0\n          },\n          children: \"\\u4EEA\\u8868\\u677F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [\"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\", user === null || user === void 0 ? void 0 : user.name, \"\\uFF01\\u5F53\\u524D\\u89D2\\u8272\\uFF1A\", currentRole === null || currentRole === void 0 ? void 0 : currentRole.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(RangePicker, {\n            value: dateRange,\n            onChange: handleDateRangeChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleRefreshData,\n            children: \"\\u5237\\u65B0\\u6570\\u636E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BA2\\u5355\\u6570\",\n            value: processedMetrics.totalOrders,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u4E2A\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u6536\\u5165\",\n            value: processedMetrics.totalRevenue,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 23\n            }, this),\n            formatter: () => processedMetrics.totalRevenueFormatted,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D6A\\u8D39\\u51CF\\u5C11\\u7387\",\n            value: processedMetrics.wasteReduction,\n            prefix: /*#__PURE__*/_jsxDEV(ArrowDownOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 23\n            }, this),\n            formatter: () => processedMetrics.wasteReductionFormatted,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6210\\u672C\\u8282\\u7EA6\",\n            value: processedMetrics.costSavings,\n            prefix: /*#__PURE__*/_jsxDEV(RiseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 23\n            }, this),\n            formatter: () => processedMetrics.costSavingsFormatted,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5E93\\u5B58\\u5468\\u8F6C\\u7387\",\n          extra: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u6B21/\\u5E74\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 38\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            value: processedMetrics.inventoryTurnover,\n            precision: 1,\n            valueStyle: {\n              fontSize: 32,\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: 85,\n            strokeColor: \"#1890ff\",\n            showInfo: false,\n            style: {\n              marginTop: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u76EE\\u6807\\uFF1A10\\u6B21/\\u5E74\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u51C6\\u65F6\\u4EA4\\u4ED8\\u7387\",\n          extra: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 38\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            value: processedMetrics.onTimeDelivery,\n            formatter: value => formatPercentage(Number(value)),\n            valueStyle: {\n              fontSize: 32,\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: 95,\n            strokeColor: \"#52c41a\",\n            showInfo: false,\n            style: {\n              marginTop: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u76EE\\u6807\\uFF1A\\u226595%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6D6A\\u8D39\\u8D8B\\u52BF\\u5206\\u6790\",\n          extra: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 39\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(AreaChart, {\n              data: mockWasteTrend,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"packaging\",\n                stackId: \"1\",\n                stroke: \"#8884d8\",\n                fill: \"#8884d8\",\n                name: \"\\u5305\\u88C5\\u6D6A\\u8D39\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"moq\",\n                stackId: \"1\",\n                stroke: \"#82ca9d\",\n                fill: \"#82ca9d\",\n                name: \"MOQ\\u6D6A\\u8D39\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"cutting\",\n                stackId: \"1\",\n                stroke: \"#ffc658\",\n                fill: \"#ffc658\",\n                name: \"\\u5207\\u5272\\u6D6A\\u8D39\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"expiry\",\n                stackId: \"1\",\n                stroke: \"#ff7300\",\n                fill: \"#ff7300\",\n                name: \"\\u8FC7\\u671F\\u6D6A\\u8D39\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6210\\u672C\\u6784\\u6210\\u8D8B\\u52BF\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: mockCostTrend,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                formatter: value => formatCurrency(Number(value))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"material\",\n                fill: \"#8884d8\",\n                name: \"\\u6750\\u6599\\u6210\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"labor\",\n                fill: \"#82ca9d\",\n                name: \"\\u4EBA\\u5DE5\\u6210\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"overhead\",\n                fill: \"#ffc658\",\n                name: \"\\u5236\\u9020\\u8D39\\u7528\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8BA2\\u5355\\u72B6\\u6001\\u5206\\u5E03\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 250,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: mockOrderStatus,\n                cx: \"50%\",\n                cy: \"50%\",\n                innerRadius: 60,\n                outerRadius: 100,\n                paddingAngle: 5,\n                dataKey: \"value\",\n                children: mockOrderStatus.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6700\\u8FD1\\u8BA2\\u5355\",\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 37\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: mockRecentOrders,\n            pagination: false,\n            size: \"small\",\n            columns: [{\n              title: '订单号',\n              dataIndex: 'id',\n              key: 'id',\n              render: text => /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 37\n              }, this)\n            }, {\n              title: '客户',\n              dataIndex: 'customer',\n              key: 'customer'\n            }, {\n              title: '金额',\n              dataIndex: 'amount',\n              key: 'amount',\n              render: value => formatCurrency(value)\n            }, {\n              title: '状态',\n              dataIndex: 'status',\n              key: 'status',\n              render: status => /*#__PURE__*/_jsxDEV(Badge, {\n                status: getStatusColor(status),\n                text: status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 21\n              }, this)\n            }, {\n              title: '日期',\n              dataIndex: 'date',\n              key: 'date'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7CFB\\u7EDF\\u9884\\u8B66\",\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 37\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: mockAlerts,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getAlertIcon(item.type),\n                title: item.message,\n                description: item.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardPage, \"LDHH8Nn0iF5Qgb0qpH1xkDAi8xk=\", false, function () {\n  return [useAppDispatch, useAppSelector];\n});\n_c = DashboardPage;\nexport default _c2 = /*#__PURE__*/React.memo(DashboardPage);\nvar _c, _c2;\n$RefreshReg$(_c, \"DashboardPage\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useMemo", "useCallback", "Row", "Col", "Card", "Statistic", "Progress", "Table", "List", "Badge", "Typography", "Space", "<PERSON><PERSON>", "DatePicker", "ArrowDownOutlined", "DollarOutlined", "WarningOutlined", "RiseOutlined", "FileTextOutlined", "ClockCircleOutlined", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "dayjs", "useAppDispatch", "useAppSelector", "formatCurrency", "formatPercentage", "jsxDEV", "_jsxDEV", "Title", "Text", "RangePicker", "mockMetrics", "totalOrders", "totalRevenue", "wasteReduction", "costSavings", "inventoryTurnover", "onTimeDelivery", "mockWasteTrend", "month", "packaging", "moq", "cutting", "expiry", "mockCostTrend", "material", "labor", "overhead", "mockOrderStatus", "name", "value", "color", "mockRecentOrders", "id", "customer", "amount", "status", "date", "mock<PERSON>ler<PERSON>", "type", "message", "time", "DashboardPage", "_s", "dispatch", "user", "currentRole", "state", "auth", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "getStatusColor", "getAlertIcon", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "processedMetrics", "wasteReductionFormatted", "totalRevenueFormatted", "costSavingsFormatted", "handleDateRangeChange", "dates", "handleRefreshData", "console", "log", "children", "justify", "align", "marginBottom", "level", "margin", "onChange", "onClick", "gutter", "xs", "sm", "lg", "title", "prefix", "suffix", "valueStyle", "formatter", "extra", "precision", "fontSize", "percent", "strokeColor", "showInfo", "marginTop", "Number", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stackId", "stroke", "fill", "cx", "cy", "innerRadius", "outerRadius", "paddingAngle", "map", "entry", "index", "dataSource", "pagination", "size", "columns", "dataIndex", "key", "render", "text", "strong", "span", "renderItem", "item", "<PERSON><PERSON>", "Meta", "avatar", "description", "_c", "_c2", "memo", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/dashboard/DashboardPage.tsx"], "sourcesContent": ["import React, { useEffect, useState, useMemo, useCallback } from 'react';\nimport {\n  Row,\n  Col,\n  Card,\n  Statistic,\n  Progress,\n  Table,\n  List,\n  Avatar,\n  Badge,\n  Typography,\n  Space,\n  Button,\n  Select,\n  DatePicker,\n  Tabs,\n} from 'antd';\nimport {\n  ArrowUpOutlined,\n  ArrowDownOutlined,\n  DollarOutlined,\n  ShoppingCartOutlined,\n  InboxOutlined,\n  WarningOutlined,\n  RiseOutlined,\n  FileTextOutlined,\n  ToolOutlined,\n  ClockCircleOutlined,\n} from '@ant-design/icons';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n} from 'recharts';\nimport dayjs from 'dayjs';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { formatCurrency, formatPercentage } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\n// 模拟数据\nconst mockMetrics = {\n  totalOrders: 156,\n  totalRevenue: 2580000,\n  wasteReduction: 0.15,\n  costSavings: 380000,\n  inventoryTurnover: 8.5,\n  onTimeDelivery: 0.95,\n};\n\nconst mockWasteTrend = [\n  { month: '1月', packaging: 2.5, moq: 3.2, cutting: 1.8, expiry: 0.5 },\n  { month: '2月', packaging: 2.1, moq: 2.8, cutting: 1.5, expiry: 0.3 },\n  { month: '3月', packaging: 1.9, moq: 2.5, cutting: 1.3, expiry: 0.4 },\n  { month: '4月', packaging: 1.7, moq: 2.2, cutting: 1.1, expiry: 0.2 },\n  { month: '5月', packaging: 1.5, moq: 2.0, cutting: 1.0, expiry: 0.3 },\n  { month: '6月', packaging: 1.3, moq: 1.8, cutting: 0.9, expiry: 0.1 },\n];\n\nconst mockCostTrend = [\n  { month: '1月', material: 850000, labor: 120000, overhead: 80000 },\n  { month: '2月', material: 920000, labor: 125000, overhead: 82000 },\n  { month: '3月', material: 880000, labor: 118000, overhead: 78000 },\n  { month: '4月', material: 950000, labor: 130000, overhead: 85000 },\n  { month: '5月', material: 890000, labor: 122000, overhead: 79000 },\n  { month: '6月', material: 910000, labor: 128000, overhead: 83000 },\n];\n\nconst mockOrderStatus = [\n  { name: '已完成', value: 45, color: '#52c41a' },\n  { name: '进行中', value: 30, color: '#1890ff' },\n  { name: '待开始', value: 20, color: '#faad14' },\n  { name: '已延期', value: 5, color: '#f5222d' },\n];\n\nconst mockRecentOrders = [\n  { id: 'ORD-001', customer: '华为技术', amount: 125000, status: '进行中', date: '2024-01-15' },\n  { id: 'ORD-002', customer: '中兴通讯', amount: 98000, status: '已完成', date: '2024-01-14' },\n  { id: 'ORD-003', customer: '大唐移动', amount: 156000, status: '待开始', date: '2024-01-13' },\n  { id: 'ORD-004', customer: '爱立信', amount: 89000, status: '进行中', date: '2024-01-12' },\n  { id: 'ORD-005', customer: '诺基亚', amount: 234000, status: '已完成', date: '2024-01-11' },\n];\n\nconst mockAlerts = [\n  { id: 1, type: 'warning', message: '物料 ANT-001 库存不足，当前库存：50件', time: '2小时前' },\n  { id: 2, type: 'error', message: '订单 ORD-003 交期可能延误', time: '4小时前' },\n  { id: 3, type: 'info', message: 'ECN-2024-001 已通过审批', time: '6小时前' },\n  { id: 4, type: 'warning', message: '供应商价格异常波动：RF-002 上涨15%', time: '8小时前' },\n];\n\nconst DashboardPage: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { user, currentRole } = useAppSelector(state => state.auth);\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([\n    dayjs().subtract(30, 'day'),\n    dayjs(),\n  ]);\n\n  useEffect(() => {\n    // 加载仪表板数据\n    // dispatch(fetchDashboardMetrics());\n  }, [dispatch, dateRange]);\n\n  // 使用useCallback优化函数，避免不必要的重新渲染\n  const getStatusColor = useCallback((status: string) => {\n    switch (status) {\n      case '已完成': return 'success';\n      case '进行中': return 'processing';\n      case '待开始': return 'warning';\n      case '已延期': return 'error';\n      default: return 'default';\n    }\n  }, []);\n\n  const getAlertIcon = useCallback((type: string) => {\n    switch (type) {\n      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;\n      case 'error': return <WarningOutlined style={{ color: '#f5222d' }} />;\n      case 'info': return <ClockCircleOutlined style={{ color: '#1890ff' }} />;\n      default: return <ClockCircleOutlined />;\n    }\n  }, []);\n\n  // 使用useMemo优化计算密集型数据\n  const processedMetrics = useMemo(() => ({\n    ...mockMetrics,\n    wasteReductionFormatted: formatPercentage(mockMetrics.wasteReduction),\n    totalRevenueFormatted: formatCurrency(mockMetrics.totalRevenue),\n    costSavingsFormatted: formatCurrency(mockMetrics.costSavings),\n  }), []);\n\n  const handleDateRangeChange = useCallback((dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {\n    if (dates) {\n      setDateRange(dates);\n    }\n  }, []);\n\n  const handleRefreshData = useCallback(() => {\n    // TODO: 实现数据刷新逻辑\n    console.log('刷新数据');\n  }, []);\n\n  return (\n    <div>\n      {/* 页面标题和控制 */}\n      <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n        <Col>\n          <Title level={2} style={{ margin: 0 }}>\n            仪表板\n          </Title>\n          <Text type=\"secondary\">\n            欢迎回来，{user?.name}！当前角色：{currentRole?.name}\n          </Text>\n        </Col>\n        <Col>\n          <Space>\n            <RangePicker\n              value={dateRange}\n              onChange={handleDateRangeChange}\n            />\n            <Button type=\"primary\" onClick={handleRefreshData}>\n              刷新数据\n            </Button>\n          </Space>\n        </Col>\n      </Row>\n\n      {/* 关键指标卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={processedMetrics.totalOrders}\n              prefix={<FileTextOutlined />}\n              suffix=\"个\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总收入\"\n              value={processedMetrics.totalRevenue}\n              prefix={<DollarOutlined />}\n              formatter={() => processedMetrics.totalRevenueFormatted}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"浪费减少率\"\n              value={processedMetrics.wasteReduction}\n              prefix={<ArrowDownOutlined />}\n              formatter={() => processedMetrics.wasteReductionFormatted}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"成本节约\"\n              value={processedMetrics.costSavings}\n              prefix={<RiseOutlined />}\n              formatter={() => processedMetrics.costSavingsFormatted}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 运营指标 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12}>\n          <Card title=\"库存周转率\" extra={<Text type=\"secondary\">次/年</Text>}>\n            <Statistic\n              value={processedMetrics.inventoryTurnover}\n              precision={1}\n              valueStyle={{ fontSize: 32, color: '#1890ff' }}\n            />\n            <Progress\n              percent={85}\n              strokeColor=\"#1890ff\"\n              showInfo={false}\n              style={{ marginTop: 16 }}\n            />\n            <Text type=\"secondary\">目标：10次/年</Text>\n          </Card>\n        </Col>\n        <Col xs={24} sm={12}>\n          <Card title=\"准时交付率\" extra={<Text type=\"secondary\">%</Text>}>\n            <Statistic\n              value={processedMetrics.onTimeDelivery}\n              formatter={(value) => formatPercentage(Number(value))}\n              valueStyle={{ fontSize: 32, color: '#52c41a' }}\n            />\n            <Progress\n              percent={95}\n              strokeColor=\"#52c41a\"\n              showInfo={false}\n              style={{ marginTop: 16 }}\n            />\n            <Text type=\"secondary\">目标：≥95%</Text>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 图表区域 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} lg={12}>\n          <Card title=\"浪费趋势分析\" extra={<Text type=\"secondary\">%</Text>}>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <AreaChart data={mockWasteTrend}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"packaging\"\n                  stackId=\"1\"\n                  stroke=\"#8884d8\"\n                  fill=\"#8884d8\"\n                  name=\"包装浪费\"\n                />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"moq\"\n                  stackId=\"1\"\n                  stroke=\"#82ca9d\"\n                  fill=\"#82ca9d\"\n                  name=\"MOQ浪费\"\n                />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"cutting\"\n                  stackId=\"1\"\n                  stroke=\"#ffc658\"\n                  fill=\"#ffc658\"\n                  name=\"切割浪费\"\n                />\n                <Area\n                  type=\"monotone\"\n                  dataKey=\"expiry\"\n                  stackId=\"1\"\n                  stroke=\"#ff7300\"\n                  fill=\"#ff7300\"\n                  name=\"过期浪费\"\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n        <Col xs={24} lg={12}>\n          <Card title=\"成本构成趋势\">\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={mockCostTrend}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip formatter={(value) => formatCurrency(Number(value))} />\n                <Legend />\n                <Bar dataKey=\"material\" fill=\"#8884d8\" name=\"材料成本\" />\n                <Bar dataKey=\"labor\" fill=\"#82ca9d\" name=\"人工成本\" />\n                <Bar dataKey=\"overhead\" fill=\"#ffc658\" name=\"制造费用\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 订单状态和最近订单 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} lg={8}>\n          <Card title=\"订单状态分布\">\n            <ResponsiveContainer width=\"100%\" height={250}>\n              <PieChart>\n                <Pie\n                  data={mockOrderStatus}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  innerRadius={60}\n                  outerRadius={100}\n                  paddingAngle={5}\n                  dataKey=\"value\"\n                >\n                  {mockOrderStatus.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n        <Col xs={24} lg={16}>\n          <Card title=\"最近订单\" extra={<Button type=\"link\">查看全部</Button>}>\n            <Table\n              dataSource={mockRecentOrders}\n              pagination={false}\n              size=\"small\"\n              columns={[\n                {\n                  title: '订单号',\n                  dataIndex: 'id',\n                  key: 'id',\n                  render: (text) => <Text strong>{text}</Text>,\n                },\n                {\n                  title: '客户',\n                  dataIndex: 'customer',\n                  key: 'customer',\n                },\n                {\n                  title: '金额',\n                  dataIndex: 'amount',\n                  key: 'amount',\n                  render: (value) => formatCurrency(value),\n                },\n                {\n                  title: '状态',\n                  dataIndex: 'status',\n                  key: 'status',\n                  render: (status) => (\n                    <Badge status={getStatusColor(status)} text={status} />\n                  ),\n                },\n                {\n                  title: '日期',\n                  dataIndex: 'date',\n                  key: 'date',\n                },\n              ]}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 系统预警 */}\n      <Row>\n        <Col span={24}>\n          <Card title=\"系统预警\" extra={<Button type=\"link\">查看全部</Button>}>\n            <List\n              dataSource={mockAlerts}\n              renderItem={(item) => (\n                <List.Item>\n                  <List.Item.Meta\n                    avatar={getAlertIcon(item.type)}\n                    title={item.message}\n                    description={item.time}\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default React.memo(DashboardPage);\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SACEC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EAEJC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,MAAM,EAENC,UAAU,QAEL,MAAM;AACb,SAEEC,iBAAiB,EACjBC,cAAc,EAGdC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAEhBC,mBAAmB,QACd,mBAAmB;AAC1B,SAGEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,QACd,UAAU;AACjB,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG/B,UAAU;AAClC,MAAM;EAAEgC;AAAY,CAAC,GAAG7B,UAAU;;AAElC;AACA,MAAM8B,WAAW,GAAG;EAClBC,WAAW,EAAE,GAAG;EAChBC,YAAY,EAAE,OAAO;EACrBC,cAAc,EAAE,IAAI;EACpBC,WAAW,EAAE,MAAM;EACnBC,iBAAiB,EAAE,GAAG;EACtBC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,cAAc,GAAG,CACrB;EAAEC,KAAK,EAAE,IAAI;EAAEC,SAAS,EAAE,GAAG;EAAEC,GAAG,EAAE,GAAG;EAAEC,OAAO,EAAE,GAAG;EAAEC,MAAM,EAAE;AAAI,CAAC,EACpE;EAAEJ,KAAK,EAAE,IAAI;EAAEC,SAAS,EAAE,GAAG;EAAEC,GAAG,EAAE,GAAG;EAAEC,OAAO,EAAE,GAAG;EAAEC,MAAM,EAAE;AAAI,CAAC,EACpE;EAAEJ,KAAK,EAAE,IAAI;EAAEC,SAAS,EAAE,GAAG;EAAEC,GAAG,EAAE,GAAG;EAAEC,OAAO,EAAE,GAAG;EAAEC,MAAM,EAAE;AAAI,CAAC,EACpE;EAAEJ,KAAK,EAAE,IAAI;EAAEC,SAAS,EAAE,GAAG;EAAEC,GAAG,EAAE,GAAG;EAAEC,OAAO,EAAE,GAAG;EAAEC,MAAM,EAAE;AAAI,CAAC,EACpE;EAAEJ,KAAK,EAAE,IAAI;EAAEC,SAAS,EAAE,GAAG;EAAEC,GAAG,EAAE,GAAG;EAAEC,OAAO,EAAE,GAAG;EAAEC,MAAM,EAAE;AAAI,CAAC,EACpE;EAAEJ,KAAK,EAAE,IAAI;EAAEC,SAAS,EAAE,GAAG;EAAEC,GAAG,EAAE,GAAG;EAAEC,OAAO,EAAE,GAAG;EAAEC,MAAM,EAAE;AAAI,CAAC,CACrE;AAED,MAAMC,aAAa,GAAG,CACpB;EAAEL,KAAK,EAAE,IAAI;EAAEM,QAAQ,EAAE,MAAM;EAAEC,KAAK,EAAE,MAAM;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjE;EAAER,KAAK,EAAE,IAAI;EAAEM,QAAQ,EAAE,MAAM;EAAEC,KAAK,EAAE,MAAM;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjE;EAAER,KAAK,EAAE,IAAI;EAAEM,QAAQ,EAAE,MAAM;EAAEC,KAAK,EAAE,MAAM;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjE;EAAER,KAAK,EAAE,IAAI;EAAEM,QAAQ,EAAE,MAAM;EAAEC,KAAK,EAAE,MAAM;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjE;EAAER,KAAK,EAAE,IAAI;EAAEM,QAAQ,EAAE,MAAM;EAAEC,KAAK,EAAE,MAAM;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjE;EAAER,KAAK,EAAE,IAAI;EAAEM,QAAQ,EAAE,MAAM;EAAEC,KAAK,EAAE,MAAM;EAAEC,QAAQ,EAAE;AAAM,CAAC,CAClE;AAED,MAAMC,eAAe,GAAG,CACtB;EAAEC,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAU,CAAC,EAC5C;EAAEF,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAU,CAAC,EAC5C;EAAEF,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAU,CAAC,EAC5C;EAAEF,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAU,CAAC,CAC5C;AAED,MAAMC,gBAAgB,GAAG,CACvB;EAAEC,EAAE,EAAE,SAAS;EAAEC,QAAQ,EAAE,MAAM;EAAEC,MAAM,EAAE,MAAM;EAAEC,MAAM,EAAE,KAAK;EAAEC,IAAI,EAAE;AAAa,CAAC,EACtF;EAAEJ,EAAE,EAAE,SAAS;EAAEC,QAAQ,EAAE,MAAM;EAAEC,MAAM,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,IAAI,EAAE;AAAa,CAAC,EACrF;EAAEJ,EAAE,EAAE,SAAS;EAAEC,QAAQ,EAAE,MAAM;EAAEC,MAAM,EAAE,MAAM;EAAEC,MAAM,EAAE,KAAK;EAAEC,IAAI,EAAE;AAAa,CAAC,EACtF;EAAEJ,EAAE,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,IAAI,EAAE;AAAa,CAAC,EACpF;EAAEJ,EAAE,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,MAAM,EAAE,MAAM;EAAEC,MAAM,EAAE,KAAK;EAAEC,IAAI,EAAE;AAAa,CAAC,CACtF;AAED,MAAMC,UAAU,GAAG,CACjB;EAAEL,EAAE,EAAE,CAAC;EAAEM,IAAI,EAAE,SAAS;EAAEC,OAAO,EAAE,0BAA0B;EAAEC,IAAI,EAAE;AAAO,CAAC,EAC7E;EAAER,EAAE,EAAE,CAAC;EAAEM,IAAI,EAAE,OAAO;EAAEC,OAAO,EAAE,mBAAmB;EAAEC,IAAI,EAAE;AAAO,CAAC,EACpE;EAAER,EAAE,EAAE,CAAC;EAAEM,IAAI,EAAE,MAAM;EAAEC,OAAO,EAAE,oBAAoB;EAAEC,IAAI,EAAE;AAAO,CAAC,EACpE;EAAER,EAAE,EAAE,CAAC;EAAEM,IAAI,EAAE,SAAS;EAAEC,OAAO,EAAE,wBAAwB;EAAEC,IAAI,EAAE;AAAO,CAAC,CAC5E;AAED,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAG1C,cAAc,CAAC,CAAC;EACjC,MAAM;IAAE2C,IAAI;IAAEC;EAAY,CAAC,GAAG3C,cAAc,CAAC4C,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EACjE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAA6B,CACrEkC,KAAK,CAAC,CAAC,CAACkD,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,EAC3BlD,KAAK,CAAC,CAAC,CACR,CAAC;EAEFnC,SAAS,CAAC,MAAM;IACd;IACA;EAAA,CACD,EAAE,CAAC8E,QAAQ,EAAEK,SAAS,CAAC,CAAC;;EAEzB;EACA,MAAMG,cAAc,GAAGnF,WAAW,CAAEmE,MAAc,IAAK;IACrD,QAAQA,MAAM;MACZ,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,KAAK;QAAE,OAAO,YAAY;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,KAAK;QAAE,OAAO,OAAO;MAC1B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,YAAY,GAAGpF,WAAW,CAAEsE,IAAY,IAAK;IACjD,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,oBAAOhC,OAAA,CAACvB,eAAe;UAACsE,KAAK,EAAE;YAAEvB,KAAK,EAAE;UAAU;QAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE,KAAK,OAAO;QAAE,oBAAOnD,OAAA,CAACvB,eAAe;UAACsE,KAAK,EAAE;YAAEvB,KAAK,EAAE;UAAU;QAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrE,KAAK,MAAM;QAAE,oBAAOnD,OAAA,CAACpB,mBAAmB;UAACmE,KAAK,EAAE;YAAEvB,KAAK,EAAE;UAAU;QAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxE;QAAS,oBAAOnD,OAAA,CAACpB,mBAAmB;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACzC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAG3F,OAAO,CAAC,OAAO;IACtC,GAAG2C,WAAW;IACdiD,uBAAuB,EAAEvD,gBAAgB,CAACM,WAAW,CAACG,cAAc,CAAC;IACrE+C,qBAAqB,EAAEzD,cAAc,CAACO,WAAW,CAACE,YAAY,CAAC;IAC/DiD,oBAAoB,EAAE1D,cAAc,CAACO,WAAW,CAACI,WAAW;EAC9D,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,MAAMgD,qBAAqB,GAAG9F,WAAW,CAAE+F,KAAwC,IAAK;IACtF,IAAIA,KAAK,EAAE;MACTd,YAAY,CAACc,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAGhG,WAAW,CAAC,MAAM;IAC1C;IACAiG,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE5D,OAAA;IAAA6D,QAAA,gBAEE7D,OAAA,CAACrC,GAAG;MAACmG,OAAO,EAAC,eAAe;MAACC,KAAK,EAAC,QAAQ;MAAChB,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBACtE7D,OAAA,CAACpC,GAAG;QAAAiG,QAAA,gBACF7D,OAAA,CAACC,KAAK;UAACgE,KAAK,EAAE,CAAE;UAAClB,KAAK,EAAE;YAAEmB,MAAM,EAAE;UAAE,CAAE;UAAAL,QAAA,EAAC;QAEvC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRnD,OAAA,CAACE,IAAI;UAAC8B,IAAI,EAAC,WAAW;UAAA6B,QAAA,GAAC,gCAChB,EAACvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhB,IAAI,EAAC,sCAAM,EAACiB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjB,IAAI;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACpC,GAAG;QAAAiG,QAAA,eACF7D,OAAA,CAAC5B,KAAK;UAAAyF,QAAA,gBACJ7D,OAAA,CAACG,WAAW;YACVoB,KAAK,EAAEmB,SAAU;YACjByB,QAAQ,EAAEX;UAAsB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFnD,OAAA,CAAC3B,MAAM;YAAC2D,IAAI,EAAC,SAAS;YAACoC,OAAO,EAAEV,iBAAkB;YAAAG,QAAA,EAAC;UAEnD;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA,CAACrC,GAAG;MAAC0G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACtB,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBACjD7D,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,eACzB7D,OAAA,CAACnC,IAAI;UAAAgG,QAAA,eACH7D,OAAA,CAAClC,SAAS;YACR2G,KAAK,EAAC,0BAAM;YACZlD,KAAK,EAAE6B,gBAAgB,CAAC/C,WAAY;YACpCqE,MAAM,eAAE1E,OAAA,CAACrB,gBAAgB;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BwB,MAAM,EAAC,QAAG;YACVC,UAAU,EAAE;cAAEpD,KAAK,EAAE;YAAU;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,eACzB7D,OAAA,CAACnC,IAAI;UAAAgG,QAAA,eACH7D,OAAA,CAAClC,SAAS;YACR2G,KAAK,EAAC,oBAAK;YACXlD,KAAK,EAAE6B,gBAAgB,CAAC9C,YAAa;YACrCoE,MAAM,eAAE1E,OAAA,CAACxB,cAAc;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3B0B,SAAS,EAAEA,CAAA,KAAMzB,gBAAgB,CAACE,qBAAsB;YACxDsB,UAAU,EAAE;cAAEpD,KAAK,EAAE;YAAU;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,eACzB7D,OAAA,CAACnC,IAAI;UAAAgG,QAAA,eACH7D,OAAA,CAAClC,SAAS;YACR2G,KAAK,EAAC,gCAAO;YACblD,KAAK,EAAE6B,gBAAgB,CAAC7C,cAAe;YACvCmE,MAAM,eAAE1E,OAAA,CAACzB,iBAAiB;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9B0B,SAAS,EAAEA,CAAA,KAAMzB,gBAAgB,CAACC,uBAAwB;YAC1DuB,UAAU,EAAE;cAAEpD,KAAK,EAAE;YAAU;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,eACzB7D,OAAA,CAACnC,IAAI;UAAAgG,QAAA,eACH7D,OAAA,CAAClC,SAAS;YACR2G,KAAK,EAAC,0BAAM;YACZlD,KAAK,EAAE6B,gBAAgB,CAAC5C,WAAY;YACpCkE,MAAM,eAAE1E,OAAA,CAACtB,YAAY;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB0B,SAAS,EAAEA,CAAA,KAAMzB,gBAAgB,CAACG,oBAAqB;YACvDqB,UAAU,EAAE;cAAEpD,KAAK,EAAE;YAAU;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA,CAACrC,GAAG;MAAC0G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACtB,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBACjD7D,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAV,QAAA,eAClB7D,OAAA,CAACnC,IAAI;UAAC4G,KAAK,EAAC,gCAAO;UAACK,KAAK,eAAE9E,OAAA,CAACE,IAAI;YAAC8B,IAAI,EAAC,WAAW;YAAA6B,QAAA,EAAC;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAU,QAAA,gBAC3D7D,OAAA,CAAClC,SAAS;YACRyD,KAAK,EAAE6B,gBAAgB,CAAC3C,iBAAkB;YAC1CsE,SAAS,EAAE,CAAE;YACbH,UAAU,EAAE;cAAEI,QAAQ,EAAE,EAAE;cAAExD,KAAK,EAAE;YAAU;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFnD,OAAA,CAACjC,QAAQ;YACPkH,OAAO,EAAE,EAAG;YACZC,WAAW,EAAC,SAAS;YACrBC,QAAQ,EAAE,KAAM;YAChBpC,KAAK,EAAE;cAAEqC,SAAS,EAAE;YAAG;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACFnD,OAAA,CAACE,IAAI;YAAC8B,IAAI,EAAC,WAAW;YAAA6B,QAAA,EAAC;UAAQ;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAV,QAAA,eAClB7D,OAAA,CAACnC,IAAI;UAAC4G,KAAK,EAAC,gCAAO;UAACK,KAAK,eAAE9E,OAAA,CAACE,IAAI;YAAC8B,IAAI,EAAC,WAAW;YAAA6B,QAAA,EAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAU,QAAA,gBACzD7D,OAAA,CAAClC,SAAS;YACRyD,KAAK,EAAE6B,gBAAgB,CAAC1C,cAAe;YACvCmE,SAAS,EAAGtD,KAAK,IAAKzB,gBAAgB,CAACuF,MAAM,CAAC9D,KAAK,CAAC,CAAE;YACtDqD,UAAU,EAAE;cAAEI,QAAQ,EAAE,EAAE;cAAExD,KAAK,EAAE;YAAU;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFnD,OAAA,CAACjC,QAAQ;YACPkH,OAAO,EAAE,EAAG;YACZC,WAAW,EAAC,SAAS;YACrBC,QAAQ,EAAE,KAAM;YAChBpC,KAAK,EAAE;cAAEqC,SAAS,EAAE;YAAG;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACFnD,OAAA,CAACE,IAAI;YAAC8B,IAAI,EAAC,WAAW;YAAA6B,QAAA,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA,CAACrC,GAAG;MAAC0G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACtB,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBACjD7D,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAX,QAAA,eAClB7D,OAAA,CAACnC,IAAI;UAAC4G,KAAK,EAAC,sCAAQ;UAACK,KAAK,eAAE9E,OAAA,CAACE,IAAI;YAAC8B,IAAI,EAAC,WAAW;YAAA6B,QAAA,EAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAU,QAAA,eAC1D7D,OAAA,CAACP,mBAAmB;YAAC6F,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA1B,QAAA,eAC5C7D,OAAA,CAACnB,SAAS;cAAC2G,IAAI,EAAE7E,cAAe;cAAAkD,QAAA,gBAC9B7D,OAAA,CAACV,aAAa;gBAACmG,eAAe,EAAC;cAAK;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACZ,KAAK;gBAACsG,OAAO,EAAC;cAAO;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBnD,OAAA,CAACX,KAAK;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnD,OAAA,CAACT,OAAO;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXnD,OAAA,CAACR,MAAM;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnD,OAAA,CAAClB,IAAI;gBACHkD,IAAI,EAAC,UAAU;gBACf0D,OAAO,EAAC,WAAW;gBACnBC,OAAO,EAAC,GAAG;gBACXC,MAAM,EAAC,SAAS;gBAChBC,IAAI,EAAC,SAAS;gBACdvE,IAAI,EAAC;cAAM;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFnD,OAAA,CAAClB,IAAI;gBACHkD,IAAI,EAAC,UAAU;gBACf0D,OAAO,EAAC,KAAK;gBACbC,OAAO,EAAC,GAAG;gBACXC,MAAM,EAAC,SAAS;gBAChBC,IAAI,EAAC,SAAS;gBACdvE,IAAI,EAAC;cAAO;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACFnD,OAAA,CAAClB,IAAI;gBACHkD,IAAI,EAAC,UAAU;gBACf0D,OAAO,EAAC,SAAS;gBACjBC,OAAO,EAAC,GAAG;gBACXC,MAAM,EAAC,SAAS;gBAChBC,IAAI,EAAC,SAAS;gBACdvE,IAAI,EAAC;cAAM;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFnD,OAAA,CAAClB,IAAI;gBACHkD,IAAI,EAAC,UAAU;gBACf0D,OAAO,EAAC,QAAQ;gBAChBC,OAAO,EAAC,GAAG;gBACXC,MAAM,EAAC,SAAS;gBAChBC,IAAI,EAAC,SAAS;gBACdvE,IAAI,EAAC;cAAM;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAX,QAAA,eAClB7D,OAAA,CAACnC,IAAI;UAAC4G,KAAK,EAAC,sCAAQ;UAAAZ,QAAA,eAClB7D,OAAA,CAACP,mBAAmB;YAAC6F,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA1B,QAAA,eAC5C7D,OAAA,CAACjB,QAAQ;cAACyG,IAAI,EAAEvE,aAAc;cAAA4C,QAAA,gBAC5B7D,OAAA,CAACV,aAAa;gBAACmG,eAAe,EAAC;cAAK;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACZ,KAAK;gBAACsG,OAAO,EAAC;cAAO;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBnD,OAAA,CAACX,KAAK;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnD,OAAA,CAACT,OAAO;gBAACsF,SAAS,EAAGtD,KAAK,IAAK1B,cAAc,CAACwF,MAAM,CAAC9D,KAAK,CAAC;cAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChEnD,OAAA,CAACR,MAAM;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnD,OAAA,CAAChB,GAAG;gBAAC0G,OAAO,EAAC,UAAU;gBAACG,IAAI,EAAC,SAAS;gBAACvE,IAAI,EAAC;cAAM;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDnD,OAAA,CAAChB,GAAG;gBAAC0G,OAAO,EAAC,OAAO;gBAACG,IAAI,EAAC,SAAS;gBAACvE,IAAI,EAAC;cAAM;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDnD,OAAA,CAAChB,GAAG;gBAAC0G,OAAO,EAAC,UAAU;gBAACG,IAAI,EAAC,SAAS;gBAACvE,IAAI,EAAC;cAAM;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA,CAACrC,GAAG;MAAC0G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACtB,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBACjD7D,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAX,QAAA,eACjB7D,OAAA,CAACnC,IAAI;UAAC4G,KAAK,EAAC,sCAAQ;UAAAZ,QAAA,eAClB7D,OAAA,CAACP,mBAAmB;YAAC6F,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAA1B,QAAA,eAC5C7D,OAAA,CAACf,QAAQ;cAAA4E,QAAA,gBACP7D,OAAA,CAACd,GAAG;gBACFsG,IAAI,EAAEnE,eAAgB;gBACtByE,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,WAAW,EAAE,EAAG;gBAChBC,WAAW,EAAE,GAAI;gBACjBC,YAAY,EAAE,CAAE;gBAChBR,OAAO,EAAC,OAAO;gBAAA7B,QAAA,EAEdxC,eAAe,CAAC8E,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAChCrG,OAAA,CAACb,IAAI;kBAAuB0G,IAAI,EAAEO,KAAK,CAAC5E;gBAAM,GAAnC,QAAQ6E,KAAK,EAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA,CAACT,OAAO;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXnD,OAAA,CAACR,MAAM;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACpC,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAX,QAAA,eAClB7D,OAAA,CAACnC,IAAI;UAAC4G,KAAK,EAAC,0BAAM;UAACK,KAAK,eAAE9E,OAAA,CAAC3B,MAAM;YAAC2D,IAAI,EAAC,MAAM;YAAA6B,QAAA,EAAC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAE;UAAAU,QAAA,eAC1D7D,OAAA,CAAChC,KAAK;YACJsI,UAAU,EAAE7E,gBAAiB;YAC7B8E,UAAU,EAAE,KAAM;YAClBC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAE,CACP;cACEhC,KAAK,EAAE,KAAK;cACZiC,SAAS,EAAE,IAAI;cACfC,GAAG,EAAE,IAAI;cACTC,MAAM,EAAGC,IAAI,iBAAK7G,OAAA,CAACE,IAAI;gBAAC4G,MAAM;gBAAAjD,QAAA,EAAEgD;cAAI;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAC7C,CAAC,EACD;cACEsB,KAAK,EAAE,IAAI;cACXiC,SAAS,EAAE,UAAU;cACrBC,GAAG,EAAE;YACP,CAAC,EACD;cACElC,KAAK,EAAE,IAAI;cACXiC,SAAS,EAAE,QAAQ;cACnBC,GAAG,EAAE,QAAQ;cACbC,MAAM,EAAGrF,KAAK,IAAK1B,cAAc,CAAC0B,KAAK;YACzC,CAAC,EACD;cACEkD,KAAK,EAAE,IAAI;cACXiC,SAAS,EAAE,QAAQ;cACnBC,GAAG,EAAE,QAAQ;cACbC,MAAM,EAAG/E,MAAM,iBACb7B,OAAA,CAAC9B,KAAK;gBAAC2D,MAAM,EAAEgB,cAAc,CAAChB,MAAM,CAAE;gBAACgF,IAAI,EAAEhF;cAAO;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAE1D,CAAC,EACD;cACEsB,KAAK,EAAE,IAAI;cACXiC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE;YACP,CAAC;UACD;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA,CAACrC,GAAG;MAAAkG,QAAA,eACF7D,OAAA,CAACpC,GAAG;QAACmJ,IAAI,EAAE,EAAG;QAAAlD,QAAA,eACZ7D,OAAA,CAACnC,IAAI;UAAC4G,KAAK,EAAC,0BAAM;UAACK,KAAK,eAAE9E,OAAA,CAAC3B,MAAM;YAAC2D,IAAI,EAAC,MAAM;YAAA6B,QAAA,EAAC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAE;UAAAU,QAAA,eAC1D7D,OAAA,CAAC/B,IAAI;YACHqI,UAAU,EAAEvE,UAAW;YACvBiF,UAAU,EAAGC,IAAI,iBACfjH,OAAA,CAAC/B,IAAI,CAACiJ,IAAI;cAAArD,QAAA,eACR7D,OAAA,CAAC/B,IAAI,CAACiJ,IAAI,CAACC,IAAI;gBACbC,MAAM,EAAEtE,YAAY,CAACmE,IAAI,CAACjF,IAAI,CAAE;gBAChCyC,KAAK,EAAEwC,IAAI,CAAChF,OAAQ;gBACpBoF,WAAW,EAAEJ,IAAI,CAAC/E;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACf,EAAA,CA3TID,aAAuB;EAAA,QACVxC,cAAc,EACDC,cAAc;AAAA;AAAA0H,EAAA,GAFxCnF,aAAuB;AA6T7B,eAAAoF,GAAA,gBAAejK,KAAK,CAACkK,IAAI,CAACrF,aAAa,CAAC;AAAC,IAAAmF,EAAA,EAAAC,GAAA;AAAAE,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}