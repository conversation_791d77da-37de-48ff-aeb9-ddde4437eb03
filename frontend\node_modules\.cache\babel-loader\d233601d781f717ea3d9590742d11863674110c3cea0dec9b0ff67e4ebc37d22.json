{"ast": null, "code": "var _ErrorHandler;\nimport { message, notification } from 'antd';\n/**\n * 错误类型枚举\n */\nexport let ErrorType = /*#__PURE__*/function (ErrorType) {\n  ErrorType[\"NETWORK\"] = \"NETWORK\";\n  ErrorType[\"VALIDATION\"] = \"VALIDATION\";\n  ErrorType[\"PERMISSION\"] = \"PERMISSION\";\n  ErrorType[\"BUSINESS\"] = \"BUSINESS\";\n  ErrorType[\"UNKNOWN\"] = \"UNKNOWN\";\n  return ErrorType;\n}({});\n\n/**\n * 错误信息接口\n */\n\n/**\n * 全局错误处理器\n * 提供统一的错误处理机制，支持多种错误类型的处理和日志记录\n * 使用单例模式确保全局唯一实例\n */\nclass ErrorHandler {\n  constructor() {\n    this.errorLog = [];\n  }\n  static getInstance() {\n    if (!ErrorHandler.instance) {\n      ErrorHandler.instance = new ErrorHandler();\n    }\n    return ErrorHandler.instance;\n  }\n\n  /**\n   * 处理HTTP错误\n   */\n  handleHttpError(error) {\n    var _error$response, _error$response2;\n    const errorInfo = {\n      type: ErrorType.NETWORK,\n      code: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) || 0,\n      message: this.getHttpErrorMessage(error),\n      details: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data,\n      timestamp: Date.now()\n    };\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    return errorInfo;\n  }\n\n  /**\n   * 处理业务逻辑错误\n   */\n  handleBusinessError(code, message, details) {\n    const errorInfo = {\n      type: ErrorType.BUSINESS,\n      code,\n      message,\n      details,\n      timestamp: Date.now()\n    };\n    this.logError(errorInfo);\n    this.showErrorMessage(message);\n    return errorInfo;\n  }\n\n  /**\n   * 处理验证错误\n   */\n  handleValidationError(message, details) {\n    const errorInfo = {\n      type: ErrorType.VALIDATION,\n      message,\n      details,\n      timestamp: Date.now()\n    };\n    this.logError(errorInfo);\n    this.showErrorMessage(message);\n    return errorInfo;\n  }\n\n  /**\n   * 处理权限错误\n   */\n  handlePermissionError(message = '您没有权限执行此操作') {\n    const errorInfo = {\n      type: ErrorType.PERMISSION,\n      code: 403,\n      message,\n      timestamp: Date.now()\n    };\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    return errorInfo;\n  }\n\n  /**\n   * 处理未知错误\n   */\n  handleUnknownError(error) {\n    const errorInfo = {\n      type: ErrorType.UNKNOWN,\n      message: error.message || '发生未知错误',\n      details: {\n        stack: error.stack,\n        name: error.name\n      },\n      timestamp: Date.now()\n    };\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    return errorInfo;\n  }\n\n  /**\n   * 获取HTTP错误消息\n   */\n  getHttpErrorMessage(error) {\n    var _error$response3, _error$response4;\n    const status = (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status;\n    const data = (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data;\n\n    // 优先使用服务器返回的错误消息\n    if (data !== null && data !== void 0 && data.message) {\n      return data.message;\n    }\n\n    // 根据状态码返回默认消息\n    switch (status) {\n      case 400:\n        return '请求参数错误';\n      case 401:\n        return '登录已过期，请重新登录';\n      case 403:\n        return '您没有权限执行此操作';\n      case 404:\n        return '请求的资源不存在';\n      case 408:\n        return '请求超时，请稍后重试';\n      case 409:\n        return '数据冲突，请刷新后重试';\n      case 422:\n        return '数据验证失败';\n      case 429:\n        return '请求过于频繁，请稍后重试';\n      case 500:\n        return '服务器内部错误';\n      case 502:\n        return '网关错误';\n      case 503:\n        return '服务暂时不可用';\n      case 504:\n        return '网关超时';\n      default:\n        if (error.code === 'NETWORK_ERROR' || !status) {\n          return '网络连接失败，请检查网络设置';\n        }\n        return `请求失败 (${status})`;\n    }\n  }\n\n  /**\n   * 显示错误消息\n   */\n  showErrorMessage(msg) {\n    message.error(msg);\n  }\n\n  /**\n   * 显示错误通知\n   */\n  showErrorNotification(errorInfo) {\n    notification.error({\n      message: '操作失败',\n      description: errorInfo.message,\n      duration: 4.5\n    });\n  }\n\n  /**\n   * 记录错误日志\n   */\n  logError(errorInfo) {\n    console.error('[ErrorHandler]', errorInfo);\n\n    // 保存到本地日志（最多保存100条）\n    this.errorLog.unshift(errorInfo);\n    if (this.errorLog.length > 100) {\n      this.errorLog = this.errorLog.slice(0, 100);\n    }\n\n    // 在生产环境中，可以在这里添加错误上报逻辑\n    if (process.env.NODE_ENV === 'production') {\n      this.reportError(errorInfo);\n    }\n  }\n\n  /**\n   * 上报错误（生产环境）\n   */\n  reportError(errorInfo) {\n    // TODO: 实现错误上报逻辑\n    // 可以发送到错误监控服务，如 Sentry、Bugsnag 等\n    console.log('报告错误到监控服务:', errorInfo);\n  }\n\n  /**\n   * 获取错误日志\n   */\n  getErrorLog() {\n    return [...this.errorLog];\n  }\n\n  /**\n   * 清空错误日志\n   */\n  clearErrorLog() {\n    this.errorLog = [];\n  }\n}\n\n// 导出单例实例\n_ErrorHandler = ErrorHandler;\nErrorHandler.instance = void 0;\nexport const errorHandler = ErrorHandler.getInstance();\n\n// 导出便捷方法\nexport const handleHttpError = error => errorHandler.handleHttpError(error);\nexport const handleBusinessError = (code, message, details) => errorHandler.handleBusinessError(code, message, details);\nexport const handleValidationError = (message, details) => errorHandler.handleValidationError(message, details);\nexport const handlePermissionError = message => errorHandler.handlePermissionError(message);\nexport const handleUnknownError = error => errorHandler.handleUnknownError(error);", "map": {"version": 3, "names": ["message", "notification", "ErrorType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "errorLog", "getInstance", "instance", "handleHttpError", "error", "_error$response", "_error$response2", "errorInfo", "type", "NETWORK", "code", "response", "status", "getHttpErrorMessage", "details", "data", "timestamp", "Date", "now", "logError", "showErrorNotification", "handleBusinessError", "BUSINESS", "showErrorMessage", "handleValidationError", "VALIDATION", "handlePermissionError", "PERMISSION", "handleUnknownError", "UNKNOWN", "stack", "name", "_error$response3", "_error$response4", "msg", "description", "duration", "console", "unshift", "length", "slice", "process", "env", "NODE_ENV", "reportError", "log", "getErrorLog", "clearErrorLog", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/utils/errorHandler.ts"], "sourcesContent": ["import { message, notification } from 'antd';\nimport { AxiosError } from 'axios';\n\n/**\n * 错误类型枚举\n */\nexport enum ErrorType {\n  NETWORK = 'NETWORK',\n  VALIDATION = 'VALIDATION',\n  PERMISSION = 'PERMISSION',\n  BUSINESS = 'BUSINESS',\n  UNKNOWN = 'UNKNOWN'\n}\n\n/**\n * 错误信息接口\n */\nexport interface ErrorInfo {\n  type: ErrorType;\n  code?: string | number;\n  message: string;\n  details?: any;\n  timestamp: number;\n}\n\n/**\n * 全局错误处理器\n * 提供统一的错误处理机制，支持多种错误类型的处理和日志记录\n * 使用单例模式确保全局唯一实例\n */\nclass ErrorHandler {\n  private static instance: ErrorHandler;\n  private errorLog: ErrorInfo[] = [];\n\n  private constructor() {}\n\n  static getInstance(): ErrorHandler {\n    if (!ErrorHandler.instance) {\n      ErrorHandler.instance = new ErrorHandler();\n    }\n    return ErrorHandler.instance;\n  }\n\n  /**\n   * 处理HTTP错误\n   */\n  handleHttpError(error: AxiosError): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.NETWORK,\n      code: error.response?.status || 0,\n      message: this.getHttpErrorMessage(error),\n      details: error.response?.data,\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 处理业务逻辑错误\n   */\n  handleBusinessError(code: string | number, message: string, details?: any): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.BUSINESS,\n      code,\n      message,\n      details,\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorMessage(message);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 处理验证错误\n   */\n  handleValidationError(message: string, details?: any): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.VALIDATION,\n      message,\n      details,\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorMessage(message);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 处理权限错误\n   */\n  handlePermissionError(message: string = '您没有权限执行此操作'): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.PERMISSION,\n      code: 403,\n      message,\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 处理未知错误\n   */\n  handleUnknownError(error: Error): ErrorInfo {\n    const errorInfo: ErrorInfo = {\n      type: ErrorType.UNKNOWN,\n      message: error.message || '发生未知错误',\n      details: {\n        stack: error.stack,\n        name: error.name\n      },\n      timestamp: Date.now()\n    };\n\n    this.logError(errorInfo);\n    this.showErrorNotification(errorInfo);\n    \n    return errorInfo;\n  }\n\n  /**\n   * 获取HTTP错误消息\n   */\n  private getHttpErrorMessage(error: AxiosError): string {\n    const status = error.response?.status;\n    const data = error.response?.data as any;\n\n    // 优先使用服务器返回的错误消息\n    if (data?.message) {\n      return data.message;\n    }\n\n    // 根据状态码返回默认消息\n    switch (status) {\n      case 400:\n        return '请求参数错误';\n      case 401:\n        return '登录已过期，请重新登录';\n      case 403:\n        return '您没有权限执行此操作';\n      case 404:\n        return '请求的资源不存在';\n      case 408:\n        return '请求超时，请稍后重试';\n      case 409:\n        return '数据冲突，请刷新后重试';\n      case 422:\n        return '数据验证失败';\n      case 429:\n        return '请求过于频繁，请稍后重试';\n      case 500:\n        return '服务器内部错误';\n      case 502:\n        return '网关错误';\n      case 503:\n        return '服务暂时不可用';\n      case 504:\n        return '网关超时';\n      default:\n        if (error.code === 'NETWORK_ERROR' || !status) {\n          return '网络连接失败，请检查网络设置';\n        }\n        return `请求失败 (${status})`;\n    }\n  }\n\n  /**\n   * 显示错误消息\n   */\n  private showErrorMessage(msg: string) {\n    message.error(msg);\n  }\n\n  /**\n   * 显示错误通知\n   */\n  private showErrorNotification(errorInfo: ErrorInfo) {\n    notification.error({\n      message: '操作失败',\n      description: errorInfo.message,\n      duration: 4.5,\n    });\n  }\n\n  /**\n   * 记录错误日志\n   */\n  private logError(errorInfo: ErrorInfo) {\n    console.error('[ErrorHandler]', errorInfo);\n    \n    // 保存到本地日志（最多保存100条）\n    this.errorLog.unshift(errorInfo);\n    if (this.errorLog.length > 100) {\n      this.errorLog = this.errorLog.slice(0, 100);\n    }\n\n    // 在生产环境中，可以在这里添加错误上报逻辑\n    if (process.env.NODE_ENV === 'production') {\n      this.reportError(errorInfo);\n    }\n  }\n\n  /**\n   * 上报错误（生产环境）\n   */\n  private reportError(errorInfo: ErrorInfo) {\n    // TODO: 实现错误上报逻辑\n    // 可以发送到错误监控服务，如 Sentry、Bugsnag 等\n    console.log('报告错误到监控服务:', errorInfo);\n  }\n\n  /**\n   * 获取错误日志\n   */\n  getErrorLog(): ErrorInfo[] {\n    return [...this.errorLog];\n  }\n\n  /**\n   * 清空错误日志\n   */\n  clearErrorLog() {\n    this.errorLog = [];\n  }\n}\n\n// 导出单例实例\nexport const errorHandler = ErrorHandler.getInstance();\n\n// 导出便捷方法\nexport const handleHttpError = (error: AxiosError) => errorHandler.handleHttpError(error);\nexport const handleBusinessError = (code: string | number, message: string, details?: any) => \n  errorHandler.handleBusinessError(code, message, details);\nexport const handleValidationError = (message: string, details?: any) => \n  errorHandler.handleValidationError(message, details);\nexport const handlePermissionError = (message?: string) => \n  errorHandler.handlePermissionError(message);\nexport const handleUnknownError = (error: Error) => \n  errorHandler.handleUnknownError(error);"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,YAAY,QAAQ,MAAM;AAG5C;AACA;AACA;AACA,WAAYC,SAAS,0BAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAATA,SAAS;EAAA,OAATA,SAAS;AAAA;;AAQrB;AACA;AACA;;AASA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EAITC,WAAWA,CAAA,EAAG;IAAA,KAFdC,QAAQ,GAAgB,EAAE;EAEX;EAEvB,OAAOC,WAAWA,CAAA,EAAiB;IACjC,IAAI,CAACH,YAAY,CAACI,QAAQ,EAAE;MAC1BJ,YAAY,CAACI,QAAQ,GAAG,IAAIJ,YAAY,CAAC,CAAC;IAC5C;IACA,OAAOA,YAAY,CAACI,QAAQ;EAC9B;;EAEA;AACF;AACA;EACEC,eAAeA,CAACC,KAAiB,EAAa;IAAA,IAAAC,eAAA,EAAAC,gBAAA;IAC5C,MAAMC,SAAoB,GAAG;MAC3BC,IAAI,EAAEX,SAAS,CAACY,OAAO;MACvBC,IAAI,EAAE,EAAAL,eAAA,GAAAD,KAAK,CAACO,QAAQ,cAAAN,eAAA,uBAAdA,eAAA,CAAgBO,MAAM,KAAI,CAAC;MACjCjB,OAAO,EAAE,IAAI,CAACkB,mBAAmB,CAACT,KAAK,CAAC;MACxCU,OAAO,GAAAR,gBAAA,GAAEF,KAAK,CAACO,QAAQ,cAAAL,gBAAA,uBAAdA,gBAAA,CAAgBS,IAAI;MAC7BC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC;IACxB,IAAI,CAACa,qBAAqB,CAACb,SAAS,CAAC;IAErC,OAAOA,SAAS;EAClB;;EAEA;AACF;AACA;EACEc,mBAAmBA,CAACX,IAAqB,EAAEf,OAAe,EAAEmB,OAAa,EAAa;IACpF,MAAMP,SAAoB,GAAG;MAC3BC,IAAI,EAAEX,SAAS,CAACyB,QAAQ;MACxBZ,IAAI;MACJf,OAAO;MACPmB,OAAO;MACPE,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC;IACxB,IAAI,CAACgB,gBAAgB,CAAC5B,OAAO,CAAC;IAE9B,OAAOY,SAAS;EAClB;;EAEA;AACF;AACA;EACEiB,qBAAqBA,CAAC7B,OAAe,EAAEmB,OAAa,EAAa;IAC/D,MAAMP,SAAoB,GAAG;MAC3BC,IAAI,EAAEX,SAAS,CAAC4B,UAAU;MAC1B9B,OAAO;MACPmB,OAAO;MACPE,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC;IACxB,IAAI,CAACgB,gBAAgB,CAAC5B,OAAO,CAAC;IAE9B,OAAOY,SAAS;EAClB;;EAEA;AACF;AACA;EACEmB,qBAAqBA,CAAC/B,OAAe,GAAG,YAAY,EAAa;IAC/D,MAAMY,SAAoB,GAAG;MAC3BC,IAAI,EAAEX,SAAS,CAAC8B,UAAU;MAC1BjB,IAAI,EAAE,GAAG;MACTf,OAAO;MACPqB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC;IACxB,IAAI,CAACa,qBAAqB,CAACb,SAAS,CAAC;IAErC,OAAOA,SAAS;EAClB;;EAEA;AACF;AACA;EACEqB,kBAAkBA,CAACxB,KAAY,EAAa;IAC1C,MAAMG,SAAoB,GAAG;MAC3BC,IAAI,EAAEX,SAAS,CAACgC,OAAO;MACvBlC,OAAO,EAAES,KAAK,CAACT,OAAO,IAAI,QAAQ;MAClCmB,OAAO,EAAE;QACPgB,KAAK,EAAE1B,KAAK,CAAC0B,KAAK;QAClBC,IAAI,EAAE3B,KAAK,CAAC2B;MACd,CAAC;MACDf,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,IAAI,CAACC,QAAQ,CAACZ,SAAS,CAAC;IACxB,IAAI,CAACa,qBAAqB,CAACb,SAAS,CAAC;IAErC,OAAOA,SAAS;EAClB;;EAEA;AACF;AACA;EACUM,mBAAmBA,CAACT,KAAiB,EAAU;IAAA,IAAA4B,gBAAA,EAAAC,gBAAA;IACrD,MAAMrB,MAAM,IAAAoB,gBAAA,GAAG5B,KAAK,CAACO,QAAQ,cAAAqB,gBAAA,uBAAdA,gBAAA,CAAgBpB,MAAM;IACrC,MAAMG,IAAI,IAAAkB,gBAAA,GAAG7B,KAAK,CAACO,QAAQ,cAAAsB,gBAAA,uBAAdA,gBAAA,CAAgBlB,IAAW;;IAExC;IACA,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEpB,OAAO,EAAE;MACjB,OAAOoB,IAAI,CAACpB,OAAO;IACrB;;IAEA;IACA,QAAQiB,MAAM;MACZ,KAAK,GAAG;QACN,OAAO,QAAQ;MACjB,KAAK,GAAG;QACN,OAAO,aAAa;MACtB,KAAK,GAAG;QACN,OAAO,YAAY;MACrB,KAAK,GAAG;QACN,OAAO,UAAU;MACnB,KAAK,GAAG;QACN,OAAO,YAAY;MACrB,KAAK,GAAG;QACN,OAAO,aAAa;MACtB,KAAK,GAAG;QACN,OAAO,QAAQ;MACjB,KAAK,GAAG;QACN,OAAO,cAAc;MACvB,KAAK,GAAG;QACN,OAAO,SAAS;MAClB,KAAK,GAAG;QACN,OAAO,MAAM;MACf,KAAK,GAAG;QACN,OAAO,SAAS;MAClB,KAAK,GAAG;QACN,OAAO,MAAM;MACf;QACE,IAAIR,KAAK,CAACM,IAAI,KAAK,eAAe,IAAI,CAACE,MAAM,EAAE;UAC7C,OAAO,gBAAgB;QACzB;QACA,OAAO,SAASA,MAAM,GAAG;IAC7B;EACF;;EAEA;AACF;AACA;EACUW,gBAAgBA,CAACW,GAAW,EAAE;IACpCvC,OAAO,CAACS,KAAK,CAAC8B,GAAG,CAAC;EACpB;;EAEA;AACF;AACA;EACUd,qBAAqBA,CAACb,SAAoB,EAAE;IAClDX,YAAY,CAACQ,KAAK,CAAC;MACjBT,OAAO,EAAE,MAAM;MACfwC,WAAW,EAAE5B,SAAS,CAACZ,OAAO;MAC9ByC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACUjB,QAAQA,CAACZ,SAAoB,EAAE;IACrC8B,OAAO,CAACjC,KAAK,CAAC,gBAAgB,EAAEG,SAAS,CAAC;;IAE1C;IACA,IAAI,CAACP,QAAQ,CAACsC,OAAO,CAAC/B,SAAS,CAAC;IAChC,IAAI,IAAI,CAACP,QAAQ,CAACuC,MAAM,GAAG,GAAG,EAAE;MAC9B,IAAI,CAACvC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACwC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7C;;IAEA;IACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACC,WAAW,CAACrC,SAAS,CAAC;IAC7B;EACF;;EAEA;AACF;AACA;EACUqC,WAAWA,CAACrC,SAAoB,EAAE;IACxC;IACA;IACA8B,OAAO,CAACQ,GAAG,CAAC,YAAY,EAAEtC,SAAS,CAAC;EACtC;;EAEA;AACF;AACA;EACEuC,WAAWA,CAAA,EAAgB;IACzB,OAAO,CAAC,GAAG,IAAI,CAAC9C,QAAQ,CAAC;EAC3B;;EAEA;AACF;AACA;EACE+C,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC/C,QAAQ,GAAG,EAAE;EACpB;AACF;;AAEA;AAAAgD,aAAA,GAjNMlD,YAAY;AAAZA,YAAY,CACDI,QAAQ;AAiNzB,OAAO,MAAM+C,YAAY,GAAGnD,YAAY,CAACG,WAAW,CAAC,CAAC;;AAEtD;AACA,OAAO,MAAME,eAAe,GAAIC,KAAiB,IAAK6C,YAAY,CAAC9C,eAAe,CAACC,KAAK,CAAC;AACzF,OAAO,MAAMiB,mBAAmB,GAAGA,CAACX,IAAqB,EAAEf,OAAe,EAAEmB,OAAa,KACvFmC,YAAY,CAAC5B,mBAAmB,CAACX,IAAI,EAAEf,OAAO,EAAEmB,OAAO,CAAC;AAC1D,OAAO,MAAMU,qBAAqB,GAAGA,CAAC7B,OAAe,EAAEmB,OAAa,KAClEmC,YAAY,CAACzB,qBAAqB,CAAC7B,OAAO,EAAEmB,OAAO,CAAC;AACtD,OAAO,MAAMY,qBAAqB,GAAI/B,OAAgB,IACpDsD,YAAY,CAACvB,qBAAqB,CAAC/B,OAAO,CAAC;AAC7C,OAAO,MAAMiC,kBAAkB,GAAIxB,KAAY,IAC7C6C,YAAY,CAACrB,kBAAkB,CAACxB,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}