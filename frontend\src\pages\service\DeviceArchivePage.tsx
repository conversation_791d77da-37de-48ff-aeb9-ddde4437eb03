import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Select,
  Row,
  Col,
  Tag,
  Modal,
  Form,
  DatePicker,
  Upload,
  Image,
  Descriptions,
  Tabs,
  Timeline,
  Alert,
  QRCode,
  Tooltip,
  Divider,
  Checkbox,
} from 'antd';
import * as XLSX from 'xlsx';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  QrcodeOutlined,
  UploadOutlined,
  DownloadOutlined,
  PrinterOutlined,
  SearchOutlined,
  ToolOutlined,
  HistoryOutlined,
  FileTextOutlined,
  CameraOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchDeviceArchives } from '../../store/slices/serviceSlice';
import { formatDate } from '../../utils';

const { Title, Text } = Typography;
const { Search } = Input;
const { TabPane } = Tabs;

const DeviceArchivePage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { deviceArchives, loading } = useAppSelector(state => state.service);

  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [deviceModalVisible, setDeviceModalVisible] = useState(false);
  const [qrModalVisible, setQrModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingDevice, setEditingDevice] = useState<any>(null);
  const [selectedDevice, setSelectedDevice] = useState<any>(null);
  const [deviceForm] = Form.useForm();
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');
  const [exportFields, setExportFields] = useState<string[]>(['deviceCode', 'deviceName', 'deviceType', 'model', 'serialNumber', 'manufacturer', 'installLocation', 'status']);

  useEffect(() => {
    loadData();
  }, [searchKeyword, statusFilter]);

  const loadData = () => {
    dispatch(fetchDeviceArchives({
      keyword: searchKeyword,
      status: statusFilter,
    }));
  };

  const handleAddDevice = () => {
    setEditingDevice(null);
    deviceForm.resetFields();
    setDeviceModalVisible(true);
  };

  const handleEditDevice = (record: any) => {
    setEditingDevice(record);
    deviceForm.setFieldsValue({
      ...record,
      installDate: record.installDate ? dayjs(record.installDate) : null,
      warrantyEndDate: record.warrantyEndDate ? dayjs(record.warrantyEndDate) : null,
    });
    setDeviceModalVisible(true);
  };

  const handleViewDevice = (record: any) => {
    setSelectedDevice(record);
    setDetailModalVisible(true);
  };

  const handleShowQR = (record: any) => {
    setSelectedDevice(record);
    setQrModalVisible(true);
  };

  const handleDeviceModalOk = async () => {
    try {
      const values = await deviceForm.validateFields();

      if (editingDevice) {
        // TODO: 实现更新API
        Modal.success({
          title: '更新成功',
          content: '设备档案已更新',
        });
      } else {
        // TODO: 实现创建API
        Modal.success({
          title: '添加成功',
          content: '设备档案已添加',
        });
      }

      setDeviceModalVisible(false);
      loadData();
    } catch (error) {
      Modal.error({
        title: '操作失败',
        content: '保存设备档案时发生错误',
      });
    }
  };

  const handleDeleteDevice = async (record: any) => {
    try {
      // TODO: 实现删除API
      Modal.success({
        title: '删除成功',
        content: '设备档案已删除',
      });
      loadData();
    } catch (error) {
      Modal.error({
        title: '删除失败',
        content: '删除设备档案时发生错误',
      });
    }
  };

  // 处理导出
  const handleExport = () => {
    setExportModalVisible(true);
  };

  const executeExport = () => {
    try {
      // 准备导出数据
      const exportData = mockDevices.map(device => {
        const data: any = {};
        
        if (exportFields.includes('deviceCode')) data['设备编码'] = device.deviceCode;
        if (exportFields.includes('deviceName')) data['设备名称'] = device.deviceName;
        if (exportFields.includes('deviceType')) data['设备类型'] = device.deviceType;
        if (exportFields.includes('model')) data['设备型号'] = device.model;
        if (exportFields.includes('serialNumber')) data['序列号'] = device.serialNumber;
        if (exportFields.includes('manufacturer')) data['制造商'] = device.manufacturer;
        if (exportFields.includes('installLocation')) data['安装位置'] = device.installLocation;
        if (exportFields.includes('installDate')) data['安装日期'] = formatDate(device.installDate);
        if (exportFields.includes('warrantyEndDate')) data['保修到期'] = formatDate(device.warrantyEndDate);
        if (exportFields.includes('status')) data['状态'] = device.status;
        if (exportFields.includes('responsiblePerson')) data['责任人'] = device.responsiblePerson;
        if (exportFields.includes('lastMaintenanceDate')) data['上次维护'] = formatDate(device.lastMaintenanceDate);
        if (exportFields.includes('nextMaintenanceDate')) data['下次维护'] = formatDate(device.nextMaintenanceDate);
        
        return data;
      });

      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '设备档案');

      // 下载文件
      const fileName = `设备档案_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;
      XLSX.writeFile(wb, fileName);
      
      Modal.success({
        title: '导出成功',
        content: '设备档案数据已导出',
      });
      setExportModalVisible(false);
    } catch (error) {
      Modal.error({
        title: '导出失败',
        content: '导出设备档案数据时发生错误',
      });
    }
  };

  // 模拟设备档案数据
  const mockDevices = [
    {
      id: '1',
      deviceCode: 'DEV-ANT-001',
      deviceName: '5G基站天线系统',
      deviceType: '天线设备',
      model: 'ANT-5G-V2.0',
      serialNumber: 'SN20240001',
      manufacturer: '华为技术有限公司',
      installLocation: '北京市朝阳区CBD基站',
      installDate: '2024-01-15T00:00:00Z',
      warrantyEndDate: '2027-01-15T00:00:00Z',
      status: '运行中',
      responsiblePerson: 'service_tech',
      lastMaintenanceDate: '2024-03-01T00:00:00Z',
      nextMaintenanceDate: '2024-06-01T00:00:00Z',
      qrCode: 'QR-DEV-ANT-001',
      photos: ['/api/photos/device1.jpg'],
      specifications: {
        frequency: '3.5GHz',
        gain: '18dBi',
        power: '200W',
        weight: '25kg',
      },
      maintenanceRecords: [
        {
          id: '1',
          date: '2024-03-01T00:00:00Z',
          type: '定期保养',
          description: '清洁天线表面，检查连接器',
          technician: 'service_tech',
          status: '已完成',
        },
        {
          id: '2',
          date: '2024-01-15T00:00:00Z',
          type: '安装调试',
          description: '设备安装及初始调试',
          technician: 'service_tech',
          status: '已完成',
        },
      ],
    },
    {
      id: '2',
      deviceCode: 'DEV-RF-002',
      deviceName: 'RF功率放大器',
      deviceType: 'RF设备',
      model: 'RF-AMP-100W',
      serialNumber: 'SN20240002',
      manufacturer: '中兴通讯股份有限公司',
      installLocation: '上海市浦东新区陆家嘴基站',
      installDate: '2024-02-01T00:00:00Z',
      warrantyEndDate: '2027-02-01T00:00:00Z',
      status: '维护中',
      responsiblePerson: 'service_tech',
      lastMaintenanceDate: '2024-03-15T00:00:00Z',
      nextMaintenanceDate: '2024-06-15T00:00:00Z',
      qrCode: 'QR-DEV-RF-002',
      photos: ['/api/photos/device2.jpg'],
      specifications: {
        frequency: '2.6GHz',
        power: '100W',
        efficiency: '45%',
        weight: '15kg',
      },
      maintenanceRecords: [
        {
          id: '1',
          date: '2024-03-15T00:00:00Z',
          type: '故障维修',
          description: '功率输出异常，更换功率模块',
          technician: 'service_tech',
          status: '进行中',
        },
      ],
    },
    {
      id: '3',
      deviceCode: 'DEV-CTL-003',
      deviceName: '基站控制器',
      deviceType: '控制设备',
      model: 'BSC-V3.0',
      serialNumber: 'SN20240003',
      manufacturer: '大唐移动通信设备有限公司',
      installLocation: '广州市天河区珠江新城基站',
      installDate: '2024-01-20T00:00:00Z',
      warrantyEndDate: '2027-01-20T00:00:00Z',
      status: '运行中',
      responsiblePerson: 'service_tech',
      lastMaintenanceDate: '2024-02-20T00:00:00Z',
      nextMaintenanceDate: '2024-05-20T00:00:00Z',
      qrCode: 'QR-DEV-CTL-003',
      photos: ['/api/photos/device3.jpg'],
      specifications: {
        channels: '64',
        capacity: '1000用户',
        power: '500W',
        weight: '50kg',
      },
      maintenanceRecords: [
        {
          id: '1',
          date: '2024-02-20T00:00:00Z',
          type: '定期保养',
          description: '系统软件更新，硬件检查',
          technician: 'service_tech',
          status: '已完成',
        },
      ],
    },
  ];

  const deviceColumns = [
    {
      title: '设备编码',
      dataIndex: 'deviceCode',
      key: 'deviceCode',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: '设备信息',
      key: 'deviceInfo',
      width: 200,
      render: (_: any, record: any) => (
        <div>
          <Text strong>{record.deviceName}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.model}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            SN: {record.serialNumber}
          </Text>
        </div>
      ),
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      key: 'deviceType',
      width: 100,
      render: (type: string) => (
        <Tag color={type === '天线设备' ? 'blue' : type === 'RF设备' ? 'green' : 'orange'}>
          {type}
        </Tag>
      ),
    },
    {
      title: '制造商',
      dataIndex: 'manufacturer',
      key: 'manufacturer',
      ellipsis: true,
    },
    {
      title: '安装位置',
      dataIndex: 'installLocation',
      key: 'installLocation',
      ellipsis: true,
    },
    {
      title: '安装日期',
      dataIndex: 'installDate',
      key: 'installDate',
      width: 100,
      render: (date: string) => formatDate(date),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const color = status === '运行中' ? 'green' :
                     status === '维护中' ? 'orange' :
                     status === '故障' ? 'red' : 'default';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '下次维护',
      dataIndex: 'nextMaintenanceDate',
      key: 'nextMaintenanceDate',
      width: 100,
      render: (date: string) => {
        const isOverdue = dayjs(date).isBefore(dayjs());
        return (
          <Text style={{ color: isOverdue ? '#ff4d4f' : undefined }}>
            {formatDate(date)}
          </Text>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right' as const,
      render: (_: any, record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDevice(record)}
            />
          </Tooltip>
          <Tooltip title="二维码">
            <Button
              type="text"
              icon={<QrcodeOutlined />}
              onClick={() => handleShowQR(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditDevice(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: '确定要删除这个设备档案吗？',
                  onOk: () => handleDeleteDevice(record),
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              设备档案
            </Title>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索设备编码、名称"
                allowClear
                style={{ width: 200 }}
                onSearch={setSearchKeyword}
              />
              <Select
                placeholder="设备状态"
                allowClear
                style={{ width: 120 }}
                value={statusFilter}
                onChange={setStatusFilter}
                options={[
                  { label: '运行中', value: '运行中' },
                  { label: '维护中', value: '维护中' },
                  { label: '故障', value: '故障' },
                  { label: '停用', value: '停用' },
                ]}
              />
              <Button icon={<DownloadOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddDevice}>
                新增设备
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={deviceColumns}
          dataSource={mockDevices}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1400 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 设备档案模态框 */}
      <Modal
        title={editingDevice ? '编辑设备档案' : '新增设备档案'}
        open={deviceModalVisible}
        onOk={handleDeviceModalOk}
        onCancel={() => setDeviceModalVisible(false)}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <Form form={deviceForm} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="deviceCode"
                label="设备编码"
                rules={[{ required: true, message: '请输入设备编码' }]}
              >
                <Input placeholder="请输入设备编码" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="deviceName"
                label="设备名称"
                rules={[{ required: true, message: '请输入设备名称' }]}
              >
                <Input placeholder="请输入设备名称" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="deviceType"
                label="设备类型"
                rules={[{ required: true, message: '请选择设备类型' }]}
              >
                <Select placeholder="请选择设备类型">
                  <Select.Option value="天线设备">天线设备</Select.Option>
                  <Select.Option value="RF设备">RF设备</Select.Option>
                  <Select.Option value="控制设备">控制设备</Select.Option>
                  <Select.Option value="传输设备">传输设备</Select.Option>
                  <Select.Option value="电源设备">电源设备</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="model"
                label="设备型号"
                rules={[{ required: true, message: '请输入设备型号' }]}
              >
                <Input placeholder="请输入设备型号" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="serialNumber"
                label="序列号"
                rules={[{ required: true, message: '请输入序列号' }]}
              >
                <Input placeholder="请输入序列号" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="manufacturer"
                label="制造商"
                rules={[{ required: true, message: '请输入制造商' }]}
              >
                <Input placeholder="请输入制造商" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="installLocation"
                label="安装位置"
                rules={[{ required: true, message: '请输入安装位置' }]}
              >
                <Input placeholder="请输入安装位置" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="installDate"
                label="安装日期"
                rules={[{ required: true, message: '请选择安装日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="warrantyEndDate"
                label="保修到期日期"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="status"
                label="设备状态"
                initialValue="运行中"
              >
                <Select>
                  <Select.Option value="运行中">运行中</Select.Option>
                  <Select.Option value="维护中">维护中</Select.Option>
                  <Select.Option value="故障">故障</Select.Option>
                  <Select.Option value="停用">停用</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="responsiblePerson"
                label="责任人"
                rules={[{ required: true, message: '请输入责任人' }]}
              >
                <Input placeholder="请输入责任人" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="photos" label="设备照片">
                <Upload
                  listType="picture-card"
                  maxCount={5}
                  beforeUpload={() => false}
                >
                  <div>
                    <CameraOutlined />
                    <div style={{ marginTop: 8 }}>上传照片</div>
                  </div>
                </Upload>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 设备详情模态框 */}
      <Modal
        title="设备详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          <Button key="print" icon={<PrinterOutlined />}>
            打印
          </Button>,
          <Button key="qr" icon={<QrcodeOutlined />} onClick={() => handleShowQR(selectedDevice)}>
            二维码
          </Button>,
        ]}
      >
        {selectedDevice && (
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="设备编码">{selectedDevice.deviceCode}</Descriptions.Item>
                <Descriptions.Item label="设备名称">{selectedDevice.deviceName}</Descriptions.Item>
                <Descriptions.Item label="设备类型">{selectedDevice.deviceType}</Descriptions.Item>
                <Descriptions.Item label="设备型号">{selectedDevice.model}</Descriptions.Item>
                <Descriptions.Item label="序列号">{selectedDevice.serialNumber}</Descriptions.Item>
                <Descriptions.Item label="制造商">{selectedDevice.manufacturer}</Descriptions.Item>
                <Descriptions.Item label="安装位置" span={2}>{selectedDevice.installLocation}</Descriptions.Item>
                <Descriptions.Item label="安装日期">{formatDate(selectedDevice.installDate)}</Descriptions.Item>
                <Descriptions.Item label="保修到期">{formatDate(selectedDevice.warrantyEndDate)}</Descriptions.Item>
                <Descriptions.Item label="设备状态">
                  <Tag color={selectedDevice.status === '运行中' ? 'green' : 'orange'}>
                    {selectedDevice.status}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="责任人">{selectedDevice.responsiblePerson}</Descriptions.Item>
              </Descriptions>

              <Divider orientation="left">技术规格</Divider>
              <Descriptions bordered column={2}>
                {Object.entries(selectedDevice.specifications || {}).map(([key, value]) => (
                  <Descriptions.Item key={key} label={key}>
                    {String(value)}
                  </Descriptions.Item>
                ))}
              </Descriptions>

              {selectedDevice.photos && selectedDevice.photos.length > 0 && (
                <>
                  <Divider orientation="left">设备照片</Divider>
                  <Space>
                    {selectedDevice.photos.map((photo: string, index: number) => (
                      <Image
                        key={index}
                        width={100}
                        height={100}
                        src={photo}
                        fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                      />
                    ))}
                  </Space>
                </>
              )}
            </TabPane>
            <TabPane tab="维护记录" key="maintenance">
              <Timeline>
                {selectedDevice.maintenanceRecords?.map((record: any) => (
                  <Timeline.Item
                    key={record.id}
                    color={record.status === '已完成' ? 'green' : 'blue'}
                    dot={record.status === '已完成' ? <ToolOutlined /> : <HistoryOutlined />}
                  >
                    <div>
                      <Text strong>{record.type}</Text>
                      <Text type="secondary" style={{ marginLeft: 8 }}>
                        {formatDate(record.date)}
                      </Text>
                    </div>
                    <div style={{ marginTop: 4 }}>
                      <Text>{record.description}</Text>
                    </div>
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary">技术员: {record.technician}</Text>
                      <Tag
                        color={record.status === '已完成' ? 'green' : 'processing'}
                        style={{ marginLeft: 8 }}
                      >
                        {record.status}
                      </Tag>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </TabPane>
            <TabPane tab="服务BOM" key="serviceBom">
              <Alert
                message="服务BOM"
                description="显示该设备的服务BOM信息，包括备件清单、维护工具等"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              <Text type="secondary">服务BOM功能正在开发中...</Text>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 二维码模态框 */}
      <Modal
        title="设备二维码"
        open={qrModalVisible}
        onCancel={() => setQrModalVisible(false)}
        width={400}
        footer={[
          <Button key="close" onClick={() => setQrModalVisible(false)}>
            关闭
          </Button>,
          <Button key="download" type="primary" icon={<DownloadOutlined />}>
            下载
          </Button>,
          <Button key="print" icon={<PrinterOutlined />}>
            打印
          </Button>,
        ]}
      >
        {selectedDevice && (
          <div style={{ textAlign: 'center' }}>
            <QRCode
              value={`${window.location.origin}/device/${selectedDevice.id}`}
              size={200}
            />
            <div style={{ marginTop: 16 }}>
              <Text strong>{selectedDevice.deviceCode}</Text>
              <br />
              <Text type="secondary">{selectedDevice.deviceName}</Text>
            </div>
          </div>
        )}
      </Modal>

      {/* 导出模态框 */}
        <Modal
          title="导出设备档案"
          open={exportModalVisible}
          onOk={executeExport}
          onCancel={() => setExportModalVisible(false)}
          width={600}
        >
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 8 }}>导出格式：</label>
            <Select
              value={exportFormat}
              onChange={setExportFormat}
              style={{ width: '100%' }}
            >
              <Select.Option value="excel">Excel (.xlsx)</Select.Option>
              <Select.Option value="csv">CSV (.csv)</Select.Option>
            </Select>
          </div>
          
          <div>
            <label style={{ display: 'block', marginBottom: 8 }}>选择导出字段：</label>
            <Checkbox.Group
              value={exportFields}
              onChange={setExportFields}
              style={{ width: '100%' }}
            >
              <Row gutter={[16, 8]}>
                <Col span={12}>
                  <Checkbox value="deviceCode">设备编码</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="deviceName">设备名称</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="deviceType">设备类型</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="model">设备型号</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="serialNumber">序列号</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="manufacturer">制造商</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="installLocation">安装位置</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="installDate">安装日期</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="warrantyEndDate">保修到期</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="status">状态</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="responsiblePerson">责任人</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="lastMaintenanceDate">上次维护</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="nextMaintenanceDate">下次维护</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </div>
        </Modal>
    </div>
  );
};

export default DeviceArchivePage;
