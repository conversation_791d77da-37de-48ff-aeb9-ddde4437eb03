{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\service\\\\DeviceArchivePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Table, Button, Space, Input, Select, Row, Col, Tag, Modal, Form, DatePicker, Upload, Image, Descriptions, Tabs, Timeline, Alert, QRCode, Tooltip, Divider } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, QrcodeOutlined, DownloadOutlined, PrinterOutlined, ToolOutlined, HistoryOutlined, CameraOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchDeviceArchives } from '../../store/slices/serviceSlice';\nimport { formatDate } from '../../utils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Search\n} = Input;\nconst {\n  TabPane\n} = Tabs;\nconst DeviceArchivePage = () => {\n  _s();\n  var _selectedDevice$maint;\n  const dispatch = useAppDispatch();\n  const {\n    deviceArchives,\n    loading\n  } = useAppSelector(state => state.service);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [deviceModalVisible, setDeviceModalVisible] = useState(false);\n  const [qrModalVisible, setQrModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [selectedDevice, setSelectedDevice] = useState(null);\n  const [deviceForm] = Form.useForm();\n  useEffect(() => {\n    loadData();\n  }, [searchKeyword, statusFilter]);\n  const loadData = () => {\n    dispatch(fetchDeviceArchives({\n      keyword: searchKeyword,\n      status: statusFilter\n    }));\n  };\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    deviceForm.resetFields();\n    setDeviceModalVisible(true);\n  };\n  const handleEditDevice = record => {\n    setEditingDevice(record);\n    deviceForm.setFieldsValue({\n      ...record,\n      installDate: record.installDate ? dayjs(record.installDate) : null,\n      warrantyEndDate: record.warrantyEndDate ? dayjs(record.warrantyEndDate) : null\n    });\n    setDeviceModalVisible(true);\n  };\n  const handleViewDevice = record => {\n    setSelectedDevice(record);\n    setDetailModalVisible(true);\n  };\n  const handleShowQR = record => {\n    setSelectedDevice(record);\n    setQrModalVisible(true);\n  };\n  const handleDeviceModalOk = async () => {\n    try {\n      const values = await deviceForm.validateFields();\n      if (editingDevice) {\n        // TODO: 实现更新API\n        Modal.success({\n          title: '更新成功',\n          content: '设备档案已更新'\n        });\n      } else {\n        // TODO: 实现创建API\n        Modal.success({\n          title: '添加成功',\n          content: '设备档案已添加'\n        });\n      }\n      setDeviceModalVisible(false);\n      loadData();\n    } catch (error) {\n      Modal.error({\n        title: '操作失败',\n        content: '保存设备档案时发生错误'\n      });\n    }\n  };\n  const handleDeleteDevice = async record => {\n    try {\n      // TODO: 实现删除API\n      Modal.success({\n        title: '删除成功',\n        content: '设备档案已删除'\n      });\n      loadData();\n    } catch (error) {\n      Modal.error({\n        title: '删除失败',\n        content: '删除设备档案时发生错误'\n      });\n    }\n  };\n\n  // 模拟设备档案数据\n  const mockDevices = [{\n    id: '1',\n    deviceCode: 'DEV-ANT-001',\n    deviceName: '5G基站天线系统',\n    deviceType: '天线设备',\n    model: 'ANT-5G-V2.0',\n    serialNumber: '**********',\n    manufacturer: '华为技术有限公司',\n    installLocation: '北京市朝阳区CBD基站',\n    installDate: '2024-01-15T00:00:00Z',\n    warrantyEndDate: '2027-01-15T00:00:00Z',\n    status: '运行中',\n    responsiblePerson: 'service_tech',\n    lastMaintenanceDate: '2024-03-01T00:00:00Z',\n    nextMaintenanceDate: '2024-06-01T00:00:00Z',\n    qrCode: 'QR-DEV-ANT-001',\n    photos: ['/api/photos/device1.jpg'],\n    specifications: {\n      frequency: '3.5GHz',\n      gain: '18dBi',\n      power: '200W',\n      weight: '25kg'\n    },\n    maintenanceRecords: [{\n      id: '1',\n      date: '2024-03-01T00:00:00Z',\n      type: '定期保养',\n      description: '清洁天线表面，检查连接器',\n      technician: 'service_tech',\n      status: '已完成'\n    }, {\n      id: '2',\n      date: '2024-01-15T00:00:00Z',\n      type: '安装调试',\n      description: '设备安装及初始调试',\n      technician: 'service_tech',\n      status: '已完成'\n    }]\n  }, {\n    id: '2',\n    deviceCode: 'DEV-RF-002',\n    deviceName: 'RF功率放大器',\n    deviceType: 'RF设备',\n    model: 'RF-AMP-100W',\n    serialNumber: 'SN20240002',\n    manufacturer: '中兴通讯股份有限公司',\n    installLocation: '上海市浦东新区陆家嘴基站',\n    installDate: '2024-02-01T00:00:00Z',\n    warrantyEndDate: '2027-02-01T00:00:00Z',\n    status: '维护中',\n    responsiblePerson: 'service_tech',\n    lastMaintenanceDate: '2024-03-15T00:00:00Z',\n    nextMaintenanceDate: '2024-06-15T00:00:00Z',\n    qrCode: 'QR-DEV-RF-002',\n    photos: ['/api/photos/device2.jpg'],\n    specifications: {\n      frequency: '2.6GHz',\n      power: '100W',\n      efficiency: '45%',\n      weight: '15kg'\n    },\n    maintenanceRecords: [{\n      id: '1',\n      date: '2024-03-15T00:00:00Z',\n      type: '故障维修',\n      description: '功率输出异常，更换功率模块',\n      technician: 'service_tech',\n      status: '进行中'\n    }]\n  }, {\n    id: '3',\n    deviceCode: 'DEV-CTL-003',\n    deviceName: '基站控制器',\n    deviceType: '控制设备',\n    model: 'BSC-V3.0',\n    serialNumber: 'SN20240003',\n    manufacturer: '大唐移动通信设备有限公司',\n    installLocation: '广州市天河区珠江新城基站',\n    installDate: '2024-01-20T00:00:00Z',\n    warrantyEndDate: '2027-01-20T00:00:00Z',\n    status: '运行中',\n    responsiblePerson: 'service_tech',\n    lastMaintenanceDate: '2024-02-20T00:00:00Z',\n    nextMaintenanceDate: '2024-05-20T00:00:00Z',\n    qrCode: 'QR-DEV-CTL-003',\n    photos: ['/api/photos/device3.jpg'],\n    specifications: {\n      channels: '64',\n      capacity: '1000用户',\n      power: '500W',\n      weight: '50kg'\n    },\n    maintenanceRecords: [{\n      id: '1',\n      date: '2024-02-20T00:00:00Z',\n      type: '定期保养',\n      description: '系统软件更新，硬件检查',\n      technician: 'service_tech',\n      status: '已完成'\n    }]\n  }];\n  const deviceColumns = [{\n    title: '设备编码',\n    dataIndex: 'deviceCode',\n    key: 'deviceCode',\n    width: 120,\n    fixed: 'left'\n  }, {\n    title: '设备信息',\n    key: 'deviceInfo',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: record.deviceName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: record.model\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [\"SN: \", record.serialNumber]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '设备类型',\n    dataIndex: 'deviceType',\n    key: 'deviceType',\n    width: 100,\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: type === '天线设备' ? 'blue' : type === 'RF设备' ? 'green' : 'orange',\n      children: type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '制造商',\n    dataIndex: 'manufacturer',\n    key: 'manufacturer',\n    ellipsis: true\n  }, {\n    title: '安装位置',\n    dataIndex: 'installLocation',\n    key: 'installLocation',\n    ellipsis: true\n  }, {\n    title: '安装日期',\n    dataIndex: 'installDate',\n    key: 'installDate',\n    width: 100,\n    render: date => formatDate(date)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => {\n      const color = status === '运行中' ? 'green' : status === '维护中' ? 'orange' : status === '故障' ? 'red' : 'default';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '下次维护',\n    dataIndex: 'nextMaintenanceDate',\n    key: 'nextMaintenanceDate',\n    width: 100,\n    render: date => {\n      const isOverdue = dayjs(date).isBefore(dayjs());\n      return /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: isOverdue ? '#ff4d4f' : undefined\n        },\n        children: formatDate(date)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewDevice(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u4E8C\\u7EF4\\u7801\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleShowQR(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEditDevice(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 21\n          }, this),\n          onClick: () => {\n            Modal.confirm({\n              title: '确认删除',\n              content: '确定要删除这个设备档案吗？',\n              onOk: () => handleDeleteDevice(record)\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u8BBE\\u5907\\u6863\\u6848\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              placeholder: \"\\u641C\\u7D22\\u8BBE\\u5907\\u7F16\\u7801\\u3001\\u540D\\u79F0\",\n              allowClear: true,\n              style: {\n                width: 200\n              },\n              onSearch: setSearchKeyword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n              allowClear: true,\n              style: {\n                width: 120\n              },\n              value: statusFilter,\n              onChange: setStatusFilter,\n              options: [{\n                label: '运行中',\n                value: '运行中'\n              }, {\n                label: '维护中',\n                value: '维护中'\n              }, {\n                label: '故障',\n                value: '故障'\n              }, {\n                label: '停用',\n                value: '停用'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 44\n              }, this),\n              onClick: handleAddDevice,\n              children: \"\\u65B0\\u589E\\u8BBE\\u5907\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: deviceColumns,\n        dataSource: mockDevices,\n        loading: loading,\n        rowKey: \"id\",\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingDevice ? '编辑设备档案' : '新增设备档案',\n      open: deviceModalVisible,\n      onOk: handleDeviceModalOk,\n      onCancel: () => setDeviceModalVisible(false),\n      width: 800,\n      okText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: deviceForm,\n        layout: \"vertical\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"deviceCode\",\n              label: \"\\u8BBE\\u5907\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入设备编码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u7F16\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"deviceName\",\n              label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入设备名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"deviceType\",\n              label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择设备类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",\n                children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"\\u5929\\u7EBF\\u8BBE\\u5907\",\n                  children: \"\\u5929\\u7EBF\\u8BBE\\u5907\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"RF\\u8BBE\\u5907\",\n                  children: \"RF\\u8BBE\\u5907\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"\\u63A7\\u5236\\u8BBE\\u5907\",\n                  children: \"\\u63A7\\u5236\\u8BBE\\u5907\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"\\u4F20\\u8F93\\u8BBE\\u5907\",\n                  children: \"\\u4F20\\u8F93\\u8BBE\\u5907\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"\\u7535\\u6E90\\u8BBE\\u5907\",\n                  children: \"\\u7535\\u6E90\\u8BBE\\u5907\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"model\",\n              label: \"\\u8BBE\\u5907\\u578B\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入设备型号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u578B\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"serialNumber\",\n              label: \"\\u5E8F\\u5217\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入序列号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5E8F\\u5217\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"manufacturer\",\n              label: \"\\u5236\\u9020\\u5546\",\n              rules: [{\n                required: true,\n                message: '请输入制造商'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"installLocation\",\n              label: \"\\u5B89\\u88C5\\u4F4D\\u7F6E\",\n              rules: [{\n                required: true,\n                message: '请输入安装位置'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B89\\u88C5\\u4F4D\\u7F6E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"installDate\",\n              label: \"\\u5B89\\u88C5\\u65E5\\u671F\",\n              rules: [{\n                required: true,\n                message: '请选择安装日期'\n              }],\n              children: /*#__PURE__*/_jsxDEV(DatePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"warrantyEndDate\",\n              label: \"\\u4FDD\\u4FEE\\u5230\\u671F\\u65E5\\u671F\",\n              children: /*#__PURE__*/_jsxDEV(DatePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n              initialValue: \"\\u8FD0\\u884C\\u4E2D\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"\\u8FD0\\u884C\\u4E2D\",\n                  children: \"\\u8FD0\\u884C\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"\\u7EF4\\u62A4\\u4E2D\",\n                  children: \"\\u7EF4\\u62A4\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"\\u6545\\u969C\",\n                  children: \"\\u6545\\u969C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n                  value: \"\\u505C\\u7528\",\n                  children: \"\\u505C\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"responsiblePerson\",\n              label: \"\\u8D23\\u4EFB\\u4EBA\",\n              rules: [{\n                required: true,\n                message: '请输入责任人'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D23\\u4EFB\\u4EBA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"photos\",\n              label: \"\\u8BBE\\u5907\\u7167\\u7247\",\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                listType: \"picture-card\",\n                maxCount: 5,\n                beforeUpload: () => false,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(CameraOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: 8\n                    },\n                    children: \"\\u4E0A\\u4F20\\u7167\\u7247\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BBE\\u5907\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      width: 1000,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(PrinterOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 37\n        }, this),\n        children: \"\\u6253\\u5370\"\n      }, \"print\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 34\n        }, this),\n        onClick: () => handleShowQR(selectedDevice),\n        children: \"\\u4E8C\\u7EF4\\u7801\"\n      }, \"qr\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 11\n      }, this)],\n      children: selectedDevice && /*#__PURE__*/_jsxDEV(Tabs, {\n        defaultActiveKey: \"basic\",\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 2,\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u7F16\\u7801\",\n              children: selectedDevice.deviceCode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n              children: selectedDevice.deviceName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n              children: selectedDevice.deviceType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u578B\\u53F7\",\n              children: selectedDevice.model\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5E8F\\u5217\\u53F7\",\n              children: selectedDevice.serialNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5236\\u9020\\u5546\",\n              children: selectedDevice.manufacturer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5B89\\u88C5\\u4F4D\\u7F6E\",\n              span: 2,\n              children: selectedDevice.installLocation\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5B89\\u88C5\\u65E5\\u671F\",\n              children: formatDate(selectedDevice.installDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u4FDD\\u4FEE\\u5230\\u671F\",\n              children: formatDate(selectedDevice.warrantyEndDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: selectedDevice.status === '运行中' ? 'green' : 'orange',\n                children: selectedDevice.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8D23\\u4EFB\\u4EBA\",\n              children: selectedDevice.responsiblePerson\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            orientation: \"left\",\n            children: \"\\u6280\\u672F\\u89C4\\u683C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 2,\n            children: Object.entries(selectedDevice.specifications || {}).map(([key, value]) => /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: key,\n              children: String(value)\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 15\n          }, this), selectedDevice.photos && selectedDevice.photos.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"left\",\n              children: \"\\u8BBE\\u5907\\u7167\\u7247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: selectedDevice.photos.map((photo, index) => /*#__PURE__*/_jsxDEV(Image, {\n                width: 100,\n                height: 100,\n                src: photo,\n                fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, \"basic\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u7EF4\\u62A4\\u8BB0\\u5F55\",\n          children: /*#__PURE__*/_jsxDEV(Timeline, {\n            children: (_selectedDevice$maint = selectedDevice.maintenanceRecords) === null || _selectedDevice$maint === void 0 ? void 0 : _selectedDevice$maint.map(record => /*#__PURE__*/_jsxDEV(Timeline.Item, {\n              color: record.status === '已完成' ? 'green' : 'blue',\n              dot: record.status === '已完成' ? /*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 52\n              }, this) : /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 71\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: record.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    marginLeft: 8\n                  },\n                  children: formatDate(record.date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  children: record.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u6280\\u672F\\u5458: \", record.technician]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: record.status === '已完成' ? 'green' : 'processing',\n                  style: {\n                    marginLeft: 8\n                  },\n                  children: record.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 21\n              }, this)]\n            }, record.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this)\n        }, \"maintenance\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u670D\\u52A1BOM\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u670D\\u52A1BOM\",\n            description: \"\\u663E\\u793A\\u8BE5\\u8BBE\\u5907\\u7684\\u670D\\u52A1BOM\\u4FE1\\u606F\\uFF0C\\u5305\\u62EC\\u5907\\u4EF6\\u6E05\\u5355\\u3001\\u7EF4\\u62A4\\u5DE5\\u5177\\u7B49\",\n            type: \"info\",\n            showIcon: true,\n            style: {\n              marginBottom: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u670D\\u52A1BOM\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 15\n          }, this)]\n        }, \"serviceBom\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BBE\\u5907\\u4E8C\\u7EF4\\u7801\",\n      open: qrModalVisible,\n      onCancel: () => setQrModalVisible(false),\n      width: 400,\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setQrModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 703,\n          columnNumber: 55\n        }, this),\n        children: \"\\u4E0B\\u8F7D\"\n      }, \"download\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(PrinterOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 37\n        }, this),\n        children: \"\\u6253\\u5370\"\n      }, \"print\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 11\n      }, this)],\n      children: selectedDevice && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(QRCode, {\n          value: `${window.location.origin}/device/${selectedDevice.id}`,\n          size: 200\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: selectedDevice.deviceCode\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: selectedDevice.deviceName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 391,\n    columnNumber: 5\n  }, this);\n};\n_s(DeviceArchivePage, \"I6h/1rpfQCtpFA6QtwQQSOitudA=\", false, function () {\n  return [useAppDispatch, useAppSelector, Form.useForm];\n});\n_c = DeviceArchivePage;\nexport default DeviceArchivePage;\nvar _c;\n$RefreshReg$(_c, \"DeviceArchivePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Row", "Col", "Tag", "Modal", "Form", "DatePicker", "Upload", "Image", "Descriptions", "Tabs", "Timeline", "<PERSON><PERSON>", "QRCode", "<PERSON><PERSON><PERSON>", "Divider", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "QrcodeOutlined", "DownloadOutlined", "PrinterOutlined", "ToolOutlined", "HistoryOutlined", "CameraOutlined", "dayjs", "useAppDispatch", "useAppSelector", "fetchDeviceArchives", "formatDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Search", "TabPane", "DeviceArchivePage", "_s", "_selectedDevice$maint", "dispatch", "deviceArchives", "loading", "state", "service", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "deviceModalVisible", "setDeviceModalVisible", "qrModalVisible", "setQrModalVisible", "detailModalVisible", "setDetailModalVisible", "editingDevice", "setEditingDevice", "selected<PERSON><PERSON><PERSON>", "setSelectedDevice", "deviceForm", "useForm", "loadData", "keyword", "status", "handleAddDevice", "resetFields", "handleEditDevice", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "installDate", "warrantyEndDate", "handleViewDevice", "handleShowQR", "handleDeviceModalOk", "values", "validateFields", "success", "title", "content", "error", "handleDeleteDevice", "mockDevices", "id", "deviceCode", "deviceName", "deviceType", "model", "serialNumber", "manufacturer", "installLocation", "<PERSON><PERSON><PERSON>", "lastMaintenanceDate", "nextMaintenanceDate", "qrCode", "photos", "specifications", "frequency", "gain", "power", "weight", "maintenanceRecords", "date", "type", "description", "technician", "efficiency", "channels", "capacity", "deviceColumns", "dataIndex", "key", "width", "fixed", "render", "_", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "ellipsis", "isOverdue", "isBefore", "undefined", "size", "icon", "onClick", "danger", "confirm", "onOk", "justify", "align", "marginBottom", "level", "margin", "placeholder", "allowClear", "onSearch", "value", "onChange", "options", "label", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onCancel", "okText", "cancelText", "form", "layout", "gutter", "xs", "md", "<PERSON><PERSON>", "name", "rules", "required", "message", "Option", "initialValue", "listType", "maxCount", "beforeUpload", "marginTop", "footer", "defaultActiveKey", "tab", "bordered", "column", "span", "orientation", "Object", "entries", "map", "String", "length", "photo", "index", "height", "src", "fallback", "dot", "marginLeft", "showIcon", "textAlign", "window", "location", "origin", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/service/DeviceArchivePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Row,\n  Col,\n  Tag,\n  Modal,\n  Form,\n  DatePicker,\n  Upload,\n  Image,\n  Descriptions,\n  Tabs,\n  Timeline,\n  Alert,\n  QRCode,\n  Tooltip,\n  Divider,\n  Checkbox,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  QrcodeOutlined,\n  UploadOutlined,\n  DownloadOutlined,\n  PrinterOutlined,\n  SearchOutlined,\n  ToolOutlined,\n  HistoryOutlined,\n  FileTextOutlined,\n  CameraOutlined,\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchDeviceArchives } from '../../store/slices/serviceSlice';\nimport { formatDate } from '../../utils';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { TabPane } = Tabs;\n\nconst DeviceArchivePage: React.FC = () => {\n  const dispatch = useAppDispatch();\n  const { deviceArchives, loading } = useAppSelector(state => state.service);\n\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [deviceModalVisible, setDeviceModalVisible] = useState(false);\n  const [qrModalVisible, setQrModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingDevice, setEditingDevice] = useState<any>(null);\n  const [selectedDevice, setSelectedDevice] = useState<any>(null);\n  const [deviceForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [searchKeyword, statusFilter]);\n\n  const loadData = () => {\n    dispatch(fetchDeviceArchives({\n      keyword: searchKeyword,\n      status: statusFilter,\n    }));\n  };\n\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    deviceForm.resetFields();\n    setDeviceModalVisible(true);\n  };\n\n  const handleEditDevice = (record: any) => {\n    setEditingDevice(record);\n    deviceForm.setFieldsValue({\n      ...record,\n      installDate: record.installDate ? dayjs(record.installDate) : null,\n      warrantyEndDate: record.warrantyEndDate ? dayjs(record.warrantyEndDate) : null,\n    });\n    setDeviceModalVisible(true);\n  };\n\n  const handleViewDevice = (record: any) => {\n    setSelectedDevice(record);\n    setDetailModalVisible(true);\n  };\n\n  const handleShowQR = (record: any) => {\n    setSelectedDevice(record);\n    setQrModalVisible(true);\n  };\n\n  const handleDeviceModalOk = async () => {\n    try {\n      const values = await deviceForm.validateFields();\n\n      if (editingDevice) {\n        // TODO: 实现更新API\n        Modal.success({\n          title: '更新成功',\n          content: '设备档案已更新',\n        });\n      } else {\n        // TODO: 实现创建API\n        Modal.success({\n          title: '添加成功',\n          content: '设备档案已添加',\n        });\n      }\n\n      setDeviceModalVisible(false);\n      loadData();\n    } catch (error) {\n      Modal.error({\n        title: '操作失败',\n        content: '保存设备档案时发生错误',\n      });\n    }\n  };\n\n  const handleDeleteDevice = async (record: any) => {\n    try {\n      // TODO: 实现删除API\n      Modal.success({\n        title: '删除成功',\n        content: '设备档案已删除',\n      });\n      loadData();\n    } catch (error) {\n      Modal.error({\n        title: '删除失败',\n        content: '删除设备档案时发生错误',\n      });\n    }\n  };\n\n  // 模拟设备档案数据\n  const mockDevices = [\n    {\n      id: '1',\n      deviceCode: 'DEV-ANT-001',\n      deviceName: '5G基站天线系统',\n      deviceType: '天线设备',\n      model: 'ANT-5G-V2.0',\n      serialNumber: '**********',\n      manufacturer: '华为技术有限公司',\n      installLocation: '北京市朝阳区CBD基站',\n      installDate: '2024-01-15T00:00:00Z',\n      warrantyEndDate: '2027-01-15T00:00:00Z',\n      status: '运行中',\n      responsiblePerson: 'service_tech',\n      lastMaintenanceDate: '2024-03-01T00:00:00Z',\n      nextMaintenanceDate: '2024-06-01T00:00:00Z',\n      qrCode: 'QR-DEV-ANT-001',\n      photos: ['/api/photos/device1.jpg'],\n      specifications: {\n        frequency: '3.5GHz',\n        gain: '18dBi',\n        power: '200W',\n        weight: '25kg',\n      },\n      maintenanceRecords: [\n        {\n          id: '1',\n          date: '2024-03-01T00:00:00Z',\n          type: '定期保养',\n          description: '清洁天线表面，检查连接器',\n          technician: 'service_tech',\n          status: '已完成',\n        },\n        {\n          id: '2',\n          date: '2024-01-15T00:00:00Z',\n          type: '安装调试',\n          description: '设备安装及初始调试',\n          technician: 'service_tech',\n          status: '已完成',\n        },\n      ],\n    },\n    {\n      id: '2',\n      deviceCode: 'DEV-RF-002',\n      deviceName: 'RF功率放大器',\n      deviceType: 'RF设备',\n      model: 'RF-AMP-100W',\n      serialNumber: 'SN20240002',\n      manufacturer: '中兴通讯股份有限公司',\n      installLocation: '上海市浦东新区陆家嘴基站',\n      installDate: '2024-02-01T00:00:00Z',\n      warrantyEndDate: '2027-02-01T00:00:00Z',\n      status: '维护中',\n      responsiblePerson: 'service_tech',\n      lastMaintenanceDate: '2024-03-15T00:00:00Z',\n      nextMaintenanceDate: '2024-06-15T00:00:00Z',\n      qrCode: 'QR-DEV-RF-002',\n      photos: ['/api/photos/device2.jpg'],\n      specifications: {\n        frequency: '2.6GHz',\n        power: '100W',\n        efficiency: '45%',\n        weight: '15kg',\n      },\n      maintenanceRecords: [\n        {\n          id: '1',\n          date: '2024-03-15T00:00:00Z',\n          type: '故障维修',\n          description: '功率输出异常，更换功率模块',\n          technician: 'service_tech',\n          status: '进行中',\n        },\n      ],\n    },\n    {\n      id: '3',\n      deviceCode: 'DEV-CTL-003',\n      deviceName: '基站控制器',\n      deviceType: '控制设备',\n      model: 'BSC-V3.0',\n      serialNumber: 'SN20240003',\n      manufacturer: '大唐移动通信设备有限公司',\n      installLocation: '广州市天河区珠江新城基站',\n      installDate: '2024-01-20T00:00:00Z',\n      warrantyEndDate: '2027-01-20T00:00:00Z',\n      status: '运行中',\n      responsiblePerson: 'service_tech',\n      lastMaintenanceDate: '2024-02-20T00:00:00Z',\n      nextMaintenanceDate: '2024-05-20T00:00:00Z',\n      qrCode: 'QR-DEV-CTL-003',\n      photos: ['/api/photos/device3.jpg'],\n      specifications: {\n        channels: '64',\n        capacity: '1000用户',\n        power: '500W',\n        weight: '50kg',\n      },\n      maintenanceRecords: [\n        {\n          id: '1',\n          date: '2024-02-20T00:00:00Z',\n          type: '定期保养',\n          description: '系统软件更新，硬件检查',\n          technician: 'service_tech',\n          status: '已完成',\n        },\n      ],\n    },\n  ];\n\n  const deviceColumns = [\n    {\n      title: '设备编码',\n      dataIndex: 'deviceCode',\n      key: 'deviceCode',\n      width: 120,\n      fixed: 'left' as const,\n    },\n    {\n      title: '设备信息',\n      key: 'deviceInfo',\n      width: 200,\n      render: (_: any, record: any) => (\n        <div>\n          <Text strong>{record.deviceName}</Text>\n          <br />\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.model}\n          </Text>\n          <br />\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            SN: {record.serialNumber}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '设备类型',\n      dataIndex: 'deviceType',\n      key: 'deviceType',\n      width: 100,\n      render: (type: string) => (\n        <Tag color={type === '天线设备' ? 'blue' : type === 'RF设备' ? 'green' : 'orange'}>\n          {type}\n        </Tag>\n      ),\n    },\n    {\n      title: '制造商',\n      dataIndex: 'manufacturer',\n      key: 'manufacturer',\n      ellipsis: true,\n    },\n    {\n      title: '安装位置',\n      dataIndex: 'installLocation',\n      key: 'installLocation',\n      ellipsis: true,\n    },\n    {\n      title: '安装日期',\n      dataIndex: 'installDate',\n      key: 'installDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => {\n        const color = status === '运行中' ? 'green' :\n                     status === '维护中' ? 'orange' :\n                     status === '故障' ? 'red' : 'default';\n        return <Tag color={color}>{status}</Tag>;\n      },\n    },\n    {\n      title: '下次维护',\n      dataIndex: 'nextMaintenanceDate',\n      key: 'nextMaintenanceDate',\n      width: 100,\n      render: (date: string) => {\n        const isOverdue = dayjs(date).isBefore(dayjs());\n        return (\n          <Text style={{ color: isOverdue ? '#ff4d4f' : undefined }}>\n            {formatDate(date)}\n          </Text>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right' as const,\n      render: (_: any, record: any) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewDevice(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"二维码\">\n            <Button\n              type=\"text\"\n              icon={<QrcodeOutlined />}\n              onClick={() => handleShowQR(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditDevice(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => {\n                Modal.confirm({\n                  title: '确认删除',\n                  content: '确定要删除这个设备档案吗？',\n                  onOk: () => handleDeleteDevice(record),\n                });\n              }}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              设备档案\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索设备编码、名称\"\n                allowClear\n                style={{ width: 200 }}\n                onSearch={setSearchKeyword}\n              />\n              <Select\n                placeholder=\"设备状态\"\n                allowClear\n                style={{ width: 120 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '运行中', value: '运行中' },\n                  { label: '维护中', value: '维护中' },\n                  { label: '故障', value: '故障' },\n                  { label: '停用', value: '停用' },\n                ]}\n              />\n              <Button icon={<DownloadOutlined />}>\n                导出\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddDevice}>\n                新增设备\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Table\n          columns={deviceColumns}\n          dataSource={mockDevices}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1400 }}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 设备档案模态框 */}\n      <Modal\n        title={editingDevice ? '编辑设备档案' : '新增设备档案'}\n        open={deviceModalVisible}\n        onOk={handleDeviceModalOk}\n        onCancel={() => setDeviceModalVisible(false)}\n        width={800}\n        okText=\"确定\"\n        cancelText=\"取消\"\n      >\n        <Form form={deviceForm} layout=\"vertical\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"deviceCode\"\n                label=\"设备编码\"\n                rules={[{ required: true, message: '请输入设备编码' }]}\n              >\n                <Input placeholder=\"请输入设备编码\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"deviceName\"\n                label=\"设备名称\"\n                rules={[{ required: true, message: '请输入设备名称' }]}\n              >\n                <Input placeholder=\"请输入设备名称\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"deviceType\"\n                label=\"设备类型\"\n                rules={[{ required: true, message: '请选择设备类型' }]}\n              >\n                <Select placeholder=\"请选择设备类型\">\n                  <Select.Option value=\"天线设备\">天线设备</Select.Option>\n                  <Select.Option value=\"RF设备\">RF设备</Select.Option>\n                  <Select.Option value=\"控制设备\">控制设备</Select.Option>\n                  <Select.Option value=\"传输设备\">传输设备</Select.Option>\n                  <Select.Option value=\"电源设备\">电源设备</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"model\"\n                label=\"设备型号\"\n                rules={[{ required: true, message: '请输入设备型号' }]}\n              >\n                <Input placeholder=\"请输入设备型号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"serialNumber\"\n                label=\"序列号\"\n                rules={[{ required: true, message: '请输入序列号' }]}\n              >\n                <Input placeholder=\"请输入序列号\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"manufacturer\"\n                label=\"制造商\"\n                rules={[{ required: true, message: '请输入制造商' }]}\n              >\n                <Input placeholder=\"请输入制造商\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24}>\n              <Form.Item\n                name=\"installLocation\"\n                label=\"安装位置\"\n                rules={[{ required: true, message: '请输入安装位置' }]}\n              >\n                <Input placeholder=\"请输入安装位置\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"installDate\"\n                label=\"安装日期\"\n                rules={[{ required: true, message: '请选择安装日期' }]}\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"warrantyEndDate\"\n                label=\"保修到期日期\"\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"设备状态\"\n                initialValue=\"运行中\"\n              >\n                <Select>\n                  <Select.Option value=\"运行中\">运行中</Select.Option>\n                  <Select.Option value=\"维护中\">维护中</Select.Option>\n                  <Select.Option value=\"故障\">故障</Select.Option>\n                  <Select.Option value=\"停用\">停用</Select.Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"responsiblePerson\"\n                label=\"责任人\"\n                rules={[{ required: true, message: '请输入责任人' }]}\n              >\n                <Input placeholder=\"请输入责任人\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24}>\n              <Form.Item name=\"photos\" label=\"设备照片\">\n                <Upload\n                  listType=\"picture-card\"\n                  maxCount={5}\n                  beforeUpload={() => false}\n                >\n                  <div>\n                    <CameraOutlined />\n                    <div style={{ marginTop: 8 }}>上传照片</div>\n                  </div>\n                </Upload>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n\n      {/* 设备详情模态框 */}\n      <Modal\n        title=\"设备详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        width={1000}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"print\" icon={<PrinterOutlined />}>\n            打印\n          </Button>,\n          <Button key=\"qr\" icon={<QrcodeOutlined />} onClick={() => handleShowQR(selectedDevice)}>\n            二维码\n          </Button>,\n        ]}\n      >\n        {selectedDevice && (\n          <Tabs defaultActiveKey=\"basic\">\n            <TabPane tab=\"基本信息\" key=\"basic\">\n              <Descriptions bordered column={2}>\n                <Descriptions.Item label=\"设备编码\">{selectedDevice.deviceCode}</Descriptions.Item>\n                <Descriptions.Item label=\"设备名称\">{selectedDevice.deviceName}</Descriptions.Item>\n                <Descriptions.Item label=\"设备类型\">{selectedDevice.deviceType}</Descriptions.Item>\n                <Descriptions.Item label=\"设备型号\">{selectedDevice.model}</Descriptions.Item>\n                <Descriptions.Item label=\"序列号\">{selectedDevice.serialNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"制造商\">{selectedDevice.manufacturer}</Descriptions.Item>\n                <Descriptions.Item label=\"安装位置\" span={2}>{selectedDevice.installLocation}</Descriptions.Item>\n                <Descriptions.Item label=\"安装日期\">{formatDate(selectedDevice.installDate)}</Descriptions.Item>\n                <Descriptions.Item label=\"保修到期\">{formatDate(selectedDevice.warrantyEndDate)}</Descriptions.Item>\n                <Descriptions.Item label=\"设备状态\">\n                  <Tag color={selectedDevice.status === '运行中' ? 'green' : 'orange'}>\n                    {selectedDevice.status}\n                  </Tag>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"责任人\">{selectedDevice.responsiblePerson}</Descriptions.Item>\n              </Descriptions>\n\n              <Divider orientation=\"left\">技术规格</Divider>\n              <Descriptions bordered column={2}>\n                {Object.entries(selectedDevice.specifications || {}).map(([key, value]) => (\n                  <Descriptions.Item key={key} label={key}>\n                    {String(value)}\n                  </Descriptions.Item>\n                ))}\n              </Descriptions>\n\n              {selectedDevice.photos && selectedDevice.photos.length > 0 && (\n                <>\n                  <Divider orientation=\"left\">设备照片</Divider>\n                  <Space>\n                    {selectedDevice.photos.map((photo: string, index: number) => (\n                      <Image\n                        key={index}\n                        width={100}\n                        height={100}\n                        src={photo}\n                        fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n                      />\n                    ))}\n                  </Space>\n                </>\n              )}\n            </TabPane>\n            <TabPane tab=\"维护记录\" key=\"maintenance\">\n              <Timeline>\n                {selectedDevice.maintenanceRecords?.map((record: any) => (\n                  <Timeline.Item\n                    key={record.id}\n                    color={record.status === '已完成' ? 'green' : 'blue'}\n                    dot={record.status === '已完成' ? <ToolOutlined /> : <HistoryOutlined />}\n                  >\n                    <div>\n                      <Text strong>{record.type}</Text>\n                      <Text type=\"secondary\" style={{ marginLeft: 8 }}>\n                        {formatDate(record.date)}\n                      </Text>\n                    </div>\n                    <div style={{ marginTop: 4 }}>\n                      <Text>{record.description}</Text>\n                    </div>\n                    <div style={{ marginTop: 4 }}>\n                      <Text type=\"secondary\">技术员: {record.technician}</Text>\n                      <Tag\n                        color={record.status === '已完成' ? 'green' : 'processing'}\n                        style={{ marginLeft: 8 }}\n                      >\n                        {record.status}\n                      </Tag>\n                    </div>\n                  </Timeline.Item>\n                ))}\n              </Timeline>\n            </TabPane>\n            <TabPane tab=\"服务BOM\" key=\"serviceBom\">\n              <Alert\n                message=\"服务BOM\"\n                description=\"显示该设备的服务BOM信息，包括备件清单、维护工具等\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n              <Text type=\"secondary\">服务BOM功能正在开发中...</Text>\n            </TabPane>\n          </Tabs>\n        )}\n      </Modal>\n\n      {/* 二维码模态框 */}\n      <Modal\n        title=\"设备二维码\"\n        open={qrModalVisible}\n        onCancel={() => setQrModalVisible(false)}\n        width={400}\n        footer={[\n          <Button key=\"close\" onClick={() => setQrModalVisible(false)}>\n            关闭\n          </Button>,\n          <Button key=\"download\" type=\"primary\" icon={<DownloadOutlined />}>\n            下载\n          </Button>,\n          <Button key=\"print\" icon={<PrinterOutlined />}>\n            打印\n          </Button>,\n        ]}\n      >\n        {selectedDevice && (\n          <div style={{ textAlign: 'center' }}>\n            <QRCode\n              value={`${window.location.origin}/device/${selectedDevice.id}`}\n              size={200}\n            />\n            <div style={{ marginTop: 16 }}>\n              <Text strong>{selectedDevice.deviceCode}</Text>\n              <br />\n              <Text type=\"secondary\">{selectedDevice.deviceName}</Text>\n            </div>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default DeviceArchivePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,OAAO,QAEF,MAAM;AAEb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,cAAc,EAEdC,gBAAgB,EAChBC,eAAe,EAEfC,YAAY,EACZC,eAAe,EAEfC,cAAc,QACT,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,UAAU,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGzC,UAAU;AAClC,MAAM;EAAE0C;AAAO,CAAC,GAAGtC,KAAK;AACxB,MAAM;EAAEuC;AAAQ,CAAC,GAAG5B,IAAI;AAExB,MAAM6B,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACxC,MAAMC,QAAQ,GAAGf,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEgB,cAAc;IAAEC;EAAQ,CAAC,GAAGhB,cAAc,CAACiB,KAAK,IAAIA,KAAK,CAACC,OAAO,CAAC;EAE1E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAAC2D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAM,IAAI,CAAC;EAC/D,MAAM,CAACqE,UAAU,CAAC,GAAGxD,IAAI,CAACyD,OAAO,CAAC,CAAC;EAEnCrE,SAAS,CAAC,MAAM;IACdsE,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAChB,aAAa,EAAEE,YAAY,CAAC,CAAC;EAEjC,MAAMc,QAAQ,GAAGA,CAAA,KAAM;IACrBrB,QAAQ,CAACb,mBAAmB,CAAC;MAC3BmC,OAAO,EAAEjB,aAAa;MACtBkB,MAAM,EAAEhB;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5BR,gBAAgB,CAAC,IAAI,CAAC;IACtBG,UAAU,CAACM,WAAW,CAAC,CAAC;IACxBf,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMgB,gBAAgB,GAAIC,MAAW,IAAK;IACxCX,gBAAgB,CAACW,MAAM,CAAC;IACxBR,UAAU,CAACS,cAAc,CAAC;MACxB,GAAGD,MAAM;MACTE,WAAW,EAAEF,MAAM,CAACE,WAAW,GAAG7C,KAAK,CAAC2C,MAAM,CAACE,WAAW,CAAC,GAAG,IAAI;MAClEC,eAAe,EAAEH,MAAM,CAACG,eAAe,GAAG9C,KAAK,CAAC2C,MAAM,CAACG,eAAe,CAAC,GAAG;IAC5E,CAAC,CAAC;IACFpB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMqB,gBAAgB,GAAIJ,MAAW,IAAK;IACxCT,iBAAiB,CAACS,MAAM,CAAC;IACzBb,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMkB,YAAY,GAAIL,MAAW,IAAK;IACpCT,iBAAiB,CAACS,MAAM,CAAC;IACzBf,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMqB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMf,UAAU,CAACgB,cAAc,CAAC,CAAC;MAEhD,IAAIpB,aAAa,EAAE;QACjB;QACArD,KAAK,CAAC0E,OAAO,CAAC;UACZC,KAAK,EAAE,MAAM;UACbC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA5E,KAAK,CAAC0E,OAAO,CAAC;UACZC,KAAK,EAAE,MAAM;UACbC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MAEA5B,qBAAqB,CAAC,KAAK,CAAC;MAC5BW,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACd7E,KAAK,CAAC6E,KAAK,CAAC;QACVF,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAME,kBAAkB,GAAG,MAAOb,MAAW,IAAK;IAChD,IAAI;MACF;MACAjE,KAAK,CAAC0E,OAAO,CAAC;QACZC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE;MACX,CAAC,CAAC;MACFjB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACd7E,KAAK,CAAC6E,KAAK,CAAC;QACVF,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,UAAU;IACtBC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,aAAa;IACpBC,YAAY,EAAE,YAAY;IAC1BC,YAAY,EAAE,UAAU;IACxBC,eAAe,EAAE,aAAa;IAC9BpB,WAAW,EAAE,sBAAsB;IACnCC,eAAe,EAAE,sBAAsB;IACvCP,MAAM,EAAE,KAAK;IACb2B,iBAAiB,EAAE,cAAc;IACjCC,mBAAmB,EAAE,sBAAsB;IAC3CC,mBAAmB,EAAE,sBAAsB;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,MAAM,EAAE,CAAC,yBAAyB,CAAC;IACnCC,cAAc,EAAE;MACdC,SAAS,EAAE,QAAQ;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;IACV,CAAC;IACDC,kBAAkB,EAAE,CAClB;MACElB,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE,cAAc;MAC3BC,UAAU,EAAE,cAAc;MAC1BzC,MAAM,EAAE;IACV,CAAC,EACD;MACEmB,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE,WAAW;MACxBC,UAAU,EAAE,cAAc;MAC1BzC,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,EACD;IACEmB,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,aAAa;IACpBC,YAAY,EAAE,YAAY;IAC1BC,YAAY,EAAE,YAAY;IAC1BC,eAAe,EAAE,cAAc;IAC/BpB,WAAW,EAAE,sBAAsB;IACnCC,eAAe,EAAE,sBAAsB;IACvCP,MAAM,EAAE,KAAK;IACb2B,iBAAiB,EAAE,cAAc;IACjCC,mBAAmB,EAAE,sBAAsB;IAC3CC,mBAAmB,EAAE,sBAAsB;IAC3CC,MAAM,EAAE,eAAe;IACvBC,MAAM,EAAE,CAAC,yBAAyB,CAAC;IACnCC,cAAc,EAAE;MACdC,SAAS,EAAE,QAAQ;MACnBE,KAAK,EAAE,MAAM;MACbO,UAAU,EAAE,KAAK;MACjBN,MAAM,EAAE;IACV,CAAC;IACDC,kBAAkB,EAAE,CAClB;MACElB,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE,eAAe;MAC5BC,UAAU,EAAE,cAAc;MAC1BzC,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,EACD;IACEmB,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,UAAU;IACjBC,YAAY,EAAE,YAAY;IAC1BC,YAAY,EAAE,cAAc;IAC5BC,eAAe,EAAE,cAAc;IAC/BpB,WAAW,EAAE,sBAAsB;IACnCC,eAAe,EAAE,sBAAsB;IACvCP,MAAM,EAAE,KAAK;IACb2B,iBAAiB,EAAE,cAAc;IACjCC,mBAAmB,EAAE,sBAAsB;IAC3CC,mBAAmB,EAAE,sBAAsB;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,MAAM,EAAE,CAAC,yBAAyB,CAAC;IACnCC,cAAc,EAAE;MACdW,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,QAAQ;MAClBT,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;IACV,CAAC;IACDC,kBAAkB,EAAE,CAClB;MACElB,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE,aAAa;MAC1BC,UAAU,EAAE,cAAc;MAC1BzC,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,CACF;EAED,MAAM6C,aAAa,GAAG,CACpB;IACE/B,KAAK,EAAE,MAAM;IACbgC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEnC,KAAK,EAAE,MAAM;IACbiC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACC,CAAM,EAAE/C,MAAW,kBAC1BrC,OAAA;MAAAqF,QAAA,gBACErF,OAAA,CAACI,IAAI;QAACkF,MAAM;QAAAD,QAAA,EAAEhD,MAAM,CAACiB;MAAU;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvC1F,OAAA;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN1F,OAAA,CAACI,IAAI;QAACoE,IAAI,EAAC,WAAW;QAACmB,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAAAP,QAAA,EAC5ChD,MAAM,CAACmB;MAAK;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACP1F,OAAA;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN1F,OAAA,CAACI,IAAI;QAACoE,IAAI,EAAC,WAAW;QAACmB,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAAAP,QAAA,GAAC,MAC1C,EAAChD,MAAM,CAACoB,YAAY;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACE3C,KAAK,EAAE,MAAM;IACbgC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGX,IAAY,iBACnBxE,OAAA,CAAC7B,GAAG;MAAC0H,KAAK,EAAErB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAGA,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,QAAS;MAAAa,QAAA,EACzEb;IAAI;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,EACD;IACE3C,KAAK,EAAE,KAAK;IACZgC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBc,QAAQ,EAAE;EACZ,CAAC,EACD;IACE/C,KAAK,EAAE,MAAM;IACbgC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBc,QAAQ,EAAE;EACZ,CAAC,EACD;IACE/C,KAAK,EAAE,MAAM;IACbgC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGZ,IAAY,IAAKzE,UAAU,CAACyE,IAAI;EAC3C,CAAC,EACD;IACExB,KAAK,EAAE,IAAI;IACXgC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGlD,MAAc,IAAK;MAC1B,MAAM4D,KAAK,GAAG5D,MAAM,KAAK,KAAK,GAAG,OAAO,GAC3BA,MAAM,KAAK,KAAK,GAAG,QAAQ,GAC3BA,MAAM,KAAK,IAAI,GAAG,KAAK,GAAG,SAAS;MAChD,oBAAOjC,OAAA,CAAC7B,GAAG;QAAC0H,KAAK,EAAEA,KAAM;QAAAR,QAAA,EAAEpD;MAAM;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC1C;EACF,CAAC,EACD;IACE3C,KAAK,EAAE,MAAM;IACbgC,SAAS,EAAE,qBAAqB;IAChCC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGZ,IAAY,IAAK;MACxB,MAAMwB,SAAS,GAAGrG,KAAK,CAAC6E,IAAI,CAAC,CAACyB,QAAQ,CAACtG,KAAK,CAAC,CAAC,CAAC;MAC/C,oBACEM,OAAA,CAACI,IAAI;QAACuF,KAAK,EAAE;UAAEE,KAAK,EAAEE,SAAS,GAAG,SAAS,GAAGE;QAAU,CAAE;QAAAZ,QAAA,EACvDvF,UAAU,CAACyE,IAAI;MAAC;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAEX;EACF,CAAC,EACD;IACE3C,KAAK,EAAE,IAAI;IACXiC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAgB;IACvBC,MAAM,EAAEA,CAACC,CAAM,EAAE/C,MAAW,kBAC1BrC,OAAA,CAAClC,KAAK;MAACoI,IAAI,EAAC,OAAO;MAAAb,QAAA,gBACjBrF,OAAA,CAAClB,OAAO;QAACiE,KAAK,EAAC,0BAAM;QAAAsC,QAAA,eACnBrF,OAAA,CAACnC,MAAM;UACL2G,IAAI,EAAC,MAAM;UACX2B,IAAI,eAAEnG,OAAA,CAACb,WAAW;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBU,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAACJ,MAAM;QAAE;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV1F,OAAA,CAAClB,OAAO;QAACiE,KAAK,EAAC,oBAAK;QAAAsC,QAAA,eAClBrF,OAAA,CAACnC,MAAM;UACL2G,IAAI,EAAC,MAAM;UACX2B,IAAI,eAAEnG,OAAA,CAACZ,cAAc;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBU,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAACL,MAAM;QAAE;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV1F,OAAA,CAAClB,OAAO;QAACiE,KAAK,EAAC,cAAI;QAAAsC,QAAA,eACjBrF,OAAA,CAACnC,MAAM;UACL2G,IAAI,EAAC,MAAM;UACX2B,IAAI,eAAEnG,OAAA,CAACf,YAAY;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBU,OAAO,EAAEA,CAAA,KAAMhE,gBAAgB,CAACC,MAAM;QAAE;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV1F,OAAA,CAAClB,OAAO;QAACiE,KAAK,EAAC,cAAI;QAAAsC,QAAA,eACjBrF,OAAA,CAACnC,MAAM;UACL2G,IAAI,EAAC,MAAM;UACX6B,MAAM;UACNF,IAAI,eAAEnG,OAAA,CAACd,cAAc;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBU,OAAO,EAAEA,CAAA,KAAM;YACbhI,KAAK,CAACkI,OAAO,CAAC;cACZvD,KAAK,EAAE,MAAM;cACbC,OAAO,EAAE,eAAe;cACxBuD,IAAI,EAAEA,CAAA,KAAMrD,kBAAkB,CAACb,MAAM;YACvC,CAAC,CAAC;UACJ;QAAE;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;EAED,oBACE1F,OAAA;IAAAqF,QAAA,gBACErF,OAAA,CAACtC,IAAI;MAAA2H,QAAA,gBACHrF,OAAA,CAAC/B,GAAG;QAACuI,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACd,KAAK,EAAE;UAAEe,YAAY,EAAE;QAAG,CAAE;QAAArB,QAAA,gBACtErF,OAAA,CAAC9B,GAAG;UAAAmH,QAAA,eACFrF,OAAA,CAACG,KAAK;YAACwG,KAAK,EAAE,CAAE;YAAChB,KAAK,EAAE;cAAEiB,MAAM,EAAE;YAAE,CAAE;YAAAvB,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN1F,OAAA,CAAC9B,GAAG;UAAAmH,QAAA,eACFrF,OAAA,CAAClC,KAAK;YAAAuH,QAAA,gBACJrF,OAAA,CAACK,MAAM;cACLwG,WAAW,EAAC,wDAAW;cACvBC,UAAU;cACVnB,KAAK,EAAE;gBAAEV,KAAK,EAAE;cAAI,CAAE;cACtB8B,QAAQ,EAAE/F;YAAiB;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACF1F,OAAA,CAAChC,MAAM;cACL6I,WAAW,EAAC,0BAAM;cAClBC,UAAU;cACVnB,KAAK,EAAE;gBAAEV,KAAK,EAAE;cAAI,CAAE;cACtB+B,KAAK,EAAE/F,YAAa;cACpBgG,QAAQ,EAAE/F,eAAgB;cAC1BgG,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAM,CAAC,EAC9B;gBAAEG,KAAK,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAM,CAAC,EAC9B;gBAAEG,KAAK,EAAE,IAAI;gBAAEH,KAAK,EAAE;cAAK,CAAC,EAC5B;gBAAEG,KAAK,EAAE,IAAI;gBAAEH,KAAK,EAAE;cAAK,CAAC;YAC5B;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF1F,OAAA,CAACnC,MAAM;cAACsI,IAAI,eAAEnG,OAAA,CAACX,gBAAgB;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAL,QAAA,EAAC;YAEpC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1F,OAAA,CAACnC,MAAM;cAAC2G,IAAI,EAAC,SAAS;cAAC2B,IAAI,eAAEnG,OAAA,CAAChB,YAAY;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACU,OAAO,EAAElE,eAAgB;cAAAmD,QAAA,EAAC;YAEzE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1F,OAAA,CAACpC,KAAK;QACJwJ,OAAO,EAAEtC,aAAc;QACvBuC,UAAU,EAAElE,WAAY;QACxBvC,OAAO,EAAEA,OAAQ;QACjB0G,MAAM,EAAC,IAAI;QACXC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP1F,OAAA,CAAC5B,KAAK;MACJ2E,KAAK,EAAEtB,aAAa,GAAG,QAAQ,GAAG,QAAS;MAC3CsG,IAAI,EAAE5G,kBAAmB;MACzBoF,IAAI,EAAE5D,mBAAoB;MAC1BqF,QAAQ,EAAEA,CAAA,KAAM5G,qBAAqB,CAAC,KAAK,CAAE;MAC7C6D,KAAK,EAAE,GAAI;MACXgD,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAA7C,QAAA,eAEfrF,OAAA,CAAC3B,IAAI;QAAC8J,IAAI,EAAEtG,UAAW;QAACuG,MAAM,EAAC,UAAU;QAAA/C,QAAA,eACvCrF,OAAA,CAAC/B,GAAG;UAACoK,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAhD,QAAA,gBACpBrF,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBtB,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAvD,QAAA,eAEhDrF,OAAA,CAACjC,KAAK;gBAAC8I,WAAW,EAAC;cAAS;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBtB,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAvD,QAAA,eAEhDrF,OAAA,CAACjC,KAAK;gBAAC8I,WAAW,EAAC;cAAS;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBtB,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAvD,QAAA,eAEhDrF,OAAA,CAAChC,MAAM;gBAAC6I,WAAW,EAAC,4CAAS;gBAAAxB,QAAA,gBAC3BrF,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,0BAAM;kBAAA3B,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChD1F,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,gBAAM;kBAAA3B,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChD1F,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,0BAAM;kBAAA3B,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChD1F,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,0BAAM;kBAAA3B,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChD1F,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,0BAAM;kBAAA3B,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,OAAO;cACZtB,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAvD,QAAA,eAEhDrF,OAAA,CAACjC,KAAK;gBAAC8I,WAAW,EAAC;cAAS;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBtB,KAAK,EAAC,oBAAK;cACXuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAvD,QAAA,eAE/CrF,OAAA,CAACjC,KAAK;gBAAC8I,WAAW,EAAC;cAAQ;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBtB,KAAK,EAAC,oBAAK;cACXuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAvD,QAAA,eAE/CrF,OAAA,CAACjC,KAAK;gBAAC8I,WAAW,EAAC;cAAQ;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAAAjD,QAAA,eACVrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,iBAAiB;cACtBtB,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAvD,QAAA,eAEhDrF,OAAA,CAACjC,KAAK;gBAAC8I,WAAW,EAAC;cAAS;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,aAAa;cAClBtB,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAvD,QAAA,eAEhDrF,OAAA,CAAC1B,UAAU;gBAACqH,KAAK,EAAE;kBAAEV,KAAK,EAAE;gBAAO;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,iBAAiB;cACtBtB,KAAK,EAAC,sCAAQ;cAAA9B,QAAA,eAEdrF,OAAA,CAAC1B,UAAU;gBAACqH,KAAK,EAAE;kBAAEV,KAAK,EAAE;gBAAO;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,QAAQ;cACbtB,KAAK,EAAC,0BAAM;cACZ2B,YAAY,EAAC,oBAAK;cAAAzD,QAAA,eAElBrF,OAAA,CAAChC,MAAM;gBAAAqH,QAAA,gBACLrF,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,oBAAK;kBAAA3B,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC9C1F,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,oBAAK;kBAAA3B,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC9C1F,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,cAAI;kBAAA3B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAC5C1F,OAAA,CAAChC,MAAM,CAAC6K,MAAM;kBAAC7B,KAAK,EAAC,cAAI;kBAAA3B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlD,QAAA,eAClBrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cACRC,IAAI,EAAC,mBAAmB;cACxBtB,KAAK,EAAC,oBAAK;cACXuB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAvD,QAAA,eAE/CrF,OAAA,CAACjC,KAAK;gBAAC8I,WAAW,EAAC;cAAQ;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1F,OAAA,CAAC9B,GAAG;YAACoK,EAAE,EAAE,EAAG;YAAAjD,QAAA,eACVrF,OAAA,CAAC3B,IAAI,CAACmK,IAAI;cAACC,IAAI,EAAC,QAAQ;cAACtB,KAAK,EAAC,0BAAM;cAAA9B,QAAA,eACnCrF,OAAA,CAACzB,MAAM;gBACLwK,QAAQ,EAAC,cAAc;gBACvBC,QAAQ,EAAE,CAAE;gBACZC,YAAY,EAAEA,CAAA,KAAM,KAAM;gBAAA5D,QAAA,eAE1BrF,OAAA;kBAAAqF,QAAA,gBACErF,OAAA,CAACP,cAAc;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClB1F,OAAA;oBAAK2F,KAAK,EAAE;sBAAEuD,SAAS,EAAE;oBAAE,CAAE;oBAAA7D,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR1F,OAAA,CAAC5B,KAAK;MACJ2E,KAAK,EAAC,0BAAM;MACZgF,IAAI,EAAExG,kBAAmB;MACzByG,QAAQ,EAAEA,CAAA,KAAMxG,qBAAqB,CAAC,KAAK,CAAE;MAC7CyD,KAAK,EAAE,IAAK;MACZkE,MAAM,EAAE,cACNnJ,OAAA,CAACnC,MAAM;QAAauI,OAAO,EAAEA,CAAA,KAAM5E,qBAAqB,CAAC,KAAK,CAAE;QAAA6D,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACT1F,OAAA,CAACnC,MAAM;QAAasI,IAAI,eAAEnG,OAAA,CAACV,eAAe;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,EAAC;MAE/C,GAFY,OAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACT1F,OAAA,CAACnC,MAAM;QAAUsI,IAAI,eAAEnG,OAAA,CAACZ,cAAc;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACU,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAACf,cAAc,CAAE;QAAA0D,QAAA,EAAC;MAExF,GAFY,IAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC,CACT;MAAAL,QAAA,EAED1D,cAAc,iBACb3B,OAAA,CAACtB,IAAI;QAAC0K,gBAAgB,EAAC,OAAO;QAAA/D,QAAA,gBAC5BrF,OAAA,CAACM,OAAO;UAAC+I,GAAG,EAAC,0BAAM;UAAAhE,QAAA,gBACjBrF,OAAA,CAACvB,YAAY;YAAC6K,QAAQ;YAACC,MAAM,EAAE,CAAE;YAAAlE,QAAA,gBAC/BrF,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,0BAAM;cAAA9B,QAAA,EAAE1D,cAAc,CAAC0B;YAAU;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC/E1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,0BAAM;cAAA9B,QAAA,EAAE1D,cAAc,CAAC2B;YAAU;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC/E1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,0BAAM;cAAA9B,QAAA,EAAE1D,cAAc,CAAC4B;YAAU;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC/E1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,0BAAM;cAAA9B,QAAA,EAAE1D,cAAc,CAAC6B;YAAK;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC1E1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,oBAAK;cAAA9B,QAAA,EAAE1D,cAAc,CAAC8B;YAAY;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChF1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,oBAAK;cAAA9B,QAAA,EAAE1D,cAAc,CAAC+B;YAAY;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChF1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,0BAAM;cAACqC,IAAI,EAAE,CAAE;cAAAnE,QAAA,EAAE1D,cAAc,CAACgC;YAAe;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC7F1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,0BAAM;cAAA9B,QAAA,EAAEvF,UAAU,CAAC6B,cAAc,CAACY,WAAW;YAAC;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC5F1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,0BAAM;cAAA9B,QAAA,EAAEvF,UAAU,CAAC6B,cAAc,CAACa,eAAe;YAAC;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChG1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,0BAAM;cAAA9B,QAAA,eAC7BrF,OAAA,CAAC7B,GAAG;gBAAC0H,KAAK,EAAElE,cAAc,CAACM,MAAM,KAAK,KAAK,GAAG,OAAO,GAAG,QAAS;gBAAAoD,QAAA,EAC9D1D,cAAc,CAACM;cAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACpB1F,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAACrB,KAAK,EAAC,oBAAK;cAAA9B,QAAA,EAAE1D,cAAc,CAACiC;YAAiB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAEf1F,OAAA,CAACjB,OAAO;YAAC0K,WAAW,EAAC,MAAM;YAAApE,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC1C1F,OAAA,CAACvB,YAAY;YAAC6K,QAAQ;YAACC,MAAM,EAAE,CAAE;YAAAlE,QAAA,EAC9BqE,MAAM,CAACC,OAAO,CAAChI,cAAc,CAACsC,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC2F,GAAG,CAAC,CAAC,CAAC5E,GAAG,EAAEgC,KAAK,CAAC,kBACpEhH,OAAA,CAACvB,YAAY,CAAC+J,IAAI;cAAWrB,KAAK,EAAEnC,GAAI;cAAAK,QAAA,EACrCwE,MAAM,CAAC7C,KAAK;YAAC,GADQhC,GAAG;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAER,CACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,EAEd/D,cAAc,CAACqC,MAAM,IAAIrC,cAAc,CAACqC,MAAM,CAAC8F,MAAM,GAAG,CAAC,iBACxD9J,OAAA,CAAAE,SAAA;YAAAmF,QAAA,gBACErF,OAAA,CAACjB,OAAO;cAAC0K,WAAW,EAAC,MAAM;cAAApE,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC1C1F,OAAA,CAAClC,KAAK;cAAAuH,QAAA,EACH1D,cAAc,CAACqC,MAAM,CAAC4F,GAAG,CAAC,CAACG,KAAa,EAAEC,KAAa,kBACtDhK,OAAA,CAACxB,KAAK;gBAEJyG,KAAK,EAAE,GAAI;gBACXgF,MAAM,EAAE,GAAI;gBACZC,GAAG,EAAEH,KAAM;gBACXI,QAAQ,EAAC;cAAgoB,GAJpoBH,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,eACR,CACH;QAAA,GA3CqB,OAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4CtB,CAAC,eACV1F,OAAA,CAACM,OAAO;UAAC+I,GAAG,EAAC,0BAAM;UAAAhE,QAAA,eACjBrF,OAAA,CAACrB,QAAQ;YAAA0G,QAAA,GAAA5E,qBAAA,GACNkB,cAAc,CAAC2C,kBAAkB,cAAA7D,qBAAA,uBAAjCA,qBAAA,CAAmCmJ,GAAG,CAAEvH,MAAW,iBAClDrC,OAAA,CAACrB,QAAQ,CAAC6J,IAAI;cAEZ3C,KAAK,EAAExD,MAAM,CAACJ,MAAM,KAAK,KAAK,GAAG,OAAO,GAAG,MAAO;cAClDmI,GAAG,EAAE/H,MAAM,CAACJ,MAAM,KAAK,KAAK,gBAAGjC,OAAA,CAACT,YAAY;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG1F,OAAA,CAACR,eAAe;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAL,QAAA,gBAEtErF,OAAA;gBAAAqF,QAAA,gBACErF,OAAA,CAACI,IAAI;kBAACkF,MAAM;kBAAAD,QAAA,EAAEhD,MAAM,CAACmC;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjC1F,OAAA,CAACI,IAAI;kBAACoE,IAAI,EAAC,WAAW;kBAACmB,KAAK,EAAE;oBAAE0E,UAAU,EAAE;kBAAE,CAAE;kBAAAhF,QAAA,EAC7CvF,UAAU,CAACuC,MAAM,CAACkC,IAAI;gBAAC;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1F,OAAA;gBAAK2F,KAAK,EAAE;kBAAEuD,SAAS,EAAE;gBAAE,CAAE;gBAAA7D,QAAA,eAC3BrF,OAAA,CAACI,IAAI;kBAAAiF,QAAA,EAAEhD,MAAM,CAACoC;gBAAW;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACN1F,OAAA;gBAAK2F,KAAK,EAAE;kBAAEuD,SAAS,EAAE;gBAAE,CAAE;gBAAA7D,QAAA,gBAC3BrF,OAAA,CAACI,IAAI;kBAACoE,IAAI,EAAC,WAAW;kBAAAa,QAAA,GAAC,sBAAK,EAAChD,MAAM,CAACqC,UAAU;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtD1F,OAAA,CAAC7B,GAAG;kBACF0H,KAAK,EAAExD,MAAM,CAACJ,MAAM,KAAK,KAAK,GAAG,OAAO,GAAG,YAAa;kBACxD0D,KAAK,EAAE;oBAAE0E,UAAU,EAAE;kBAAE,CAAE;kBAAAhF,QAAA,EAExBhD,MAAM,CAACJ;gBAAM;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GArBDrD,MAAM,CAACe,EAAE;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBD,CAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC,GA5BW,aAAa;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6B5B,CAAC,eACV1F,OAAA,CAACM,OAAO;UAAC+I,GAAG,EAAC,iBAAO;UAAAhE,QAAA,gBAClBrF,OAAA,CAACpB,KAAK;YACJgK,OAAO,EAAC,iBAAO;YACfnE,WAAW,EAAC,+IAA4B;YACxCD,IAAI,EAAC,MAAM;YACX8F,QAAQ;YACR3E,KAAK,EAAE;cAAEe,YAAY,EAAE;YAAG;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACF1F,OAAA,CAACI,IAAI;YAACoE,IAAI,EAAC,WAAW;YAAAa,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GARtB,YAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAS5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGR1F,OAAA,CAAC5B,KAAK;MACJ2E,KAAK,EAAC,gCAAO;MACbgF,IAAI,EAAE1G,cAAe;MACrB2G,QAAQ,EAAEA,CAAA,KAAM1G,iBAAiB,CAAC,KAAK,CAAE;MACzC2D,KAAK,EAAE,GAAI;MACXkE,MAAM,EAAE,cACNnJ,OAAA,CAACnC,MAAM;QAAauI,OAAO,EAAEA,CAAA,KAAM9E,iBAAiB,CAAC,KAAK,CAAE;QAAA+D,QAAA,EAAC;MAE7D,GAFY,OAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACT1F,OAAA,CAACnC,MAAM;QAAgB2G,IAAI,EAAC,SAAS;QAAC2B,IAAI,eAAEnG,OAAA,CAACX,gBAAgB;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,EAAC;MAElE,GAFY,UAAU;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CAAC,eACT1F,OAAA,CAACnC,MAAM;QAAasI,IAAI,eAAEnG,OAAA,CAACV,eAAe;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,EAAC;MAE/C,GAFY,OAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MAAAL,QAAA,EAED1D,cAAc,iBACb3B,OAAA;QAAK2F,KAAK,EAAE;UAAE4E,SAAS,EAAE;QAAS,CAAE;QAAAlF,QAAA,gBAClCrF,OAAA,CAACnB,MAAM;UACLmI,KAAK,EAAE,GAAGwD,MAAM,CAACC,QAAQ,CAACC,MAAM,WAAW/I,cAAc,CAACyB,EAAE,EAAG;UAC/D8C,IAAI,EAAE;QAAI;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACF1F,OAAA;UAAK2F,KAAK,EAAE;YAAEuD,SAAS,EAAE;UAAG,CAAE;UAAA7D,QAAA,gBAC5BrF,OAAA,CAACI,IAAI;YAACkF,MAAM;YAAAD,QAAA,EAAE1D,cAAc,CAAC0B;UAAU;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/C1F,OAAA;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN1F,OAAA,CAACI,IAAI;YAACoE,IAAI,EAAC,WAAW;YAAAa,QAAA,EAAE1D,cAAc,CAAC2B;UAAU;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAClF,EAAA,CAlqBID,iBAA2B;EAAA,QACdZ,cAAc,EACKC,cAAc,EAS7BvB,IAAI,CAACyD,OAAO;AAAA;AAAA6I,EAAA,GAX7BpK,iBAA2B;AAoqBjC,eAAeA,iBAAiB;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}