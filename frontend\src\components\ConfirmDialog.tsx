import React from 'react';
import { Modal, ModalProps } from 'antd';
import { ExclamationCircleOutlined, QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';

/**
 * 确认对话框组件属性接口
 * 继承自 Ant Design Modal 组件，但排除了 onOk 和 onCancel 属性
 */
interface ConfirmDialogProps extends Omit<ModalProps, 'onOk' | 'onCancel'> {
  /** 对话框类型，决定图标和按钮样式 */
  type?: 'warning' | 'info' | 'confirm' | 'error';
  /** 确认回调函数，支持异步操作 */
  onConfirm?: () => void | Promise<void>;
  /** 取消回调函数 */
  onCancel?: () => void;
  /** 确认按钮文本，默认为"确定" */
  confirmText?: string;
  /** 取消按钮文本，默认为"取消" */
  cancelText?: string;
  /** 是否显示确认按钮加载状态 */
  confirmLoading?: boolean;
}

/**
 * 确认对话框组件
 * 提供统一的确认操作界面，支持不同类型的确认场景
 * 
 * @param type - 对话框类型，影响图标和按钮样式
 * @param onConfirm - 确认操作回调，支持异步
 * @param onCancel - 取消操作回调
 * @param confirmText - 确认按钮文本
 * @param cancelText - 取消按钮文本
 * @param confirmLoading - 确认按钮加载状态
 * @param title - 对话框标题
 * @param children - 对话框内容
 * @param props - 其他 Modal 属性
 */
interface ConfirmDialogComponent extends React.FC<ConfirmDialogProps> {
  confirm: (config: {
    title: string;
    content: React.ReactNode;
    onConfirm?: () => void | Promise<void>;
    onCancel?: () => void;
    type?: 'warning' | 'info' | 'confirm' | 'error';
  }) => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  type = 'confirm',
  onConfirm,
  onCancel,
  confirmText = '确定',
  cancelText = '取消',
  confirmLoading = false,
  title,
  children,
  ...props
}) => {
  const getIcon = () => {
    switch (type) {
      case 'warning':
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      case 'confirm':
      default:
        return <QuestionCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const handleOk = async () => {
    if (onConfirm) {
      await onConfirm();
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {getIcon()}
          <span>{title}</span>
        </div>
      }
      onOk={handleOk}
      onCancel={handleCancel}
      okText={confirmText}
      cancelText={cancelText}
      confirmLoading={confirmLoading}
      okButtonProps={{
        danger: type === 'warning' || type === 'error'
      }}
      {...props}
    >
      {children}
    </Modal>
  );
};

/**
 * 静态方法：快速调用确认对话框
 * 无需手动管理组件状态，直接调用即可显示确认对话框
 * 
 * @param config - 配置对象
 * @param config.title - 对话框标题
 * @param config.content - 对话框内容
 * @param config.onConfirm - 确认回调函数
 * @param config.onCancel - 取消回调函数
 * @param config.type - 对话框类型
 * @returns Modal.confirm 实例
 * 
 * @example
 * ```tsx
 * ConfirmDialog.confirm({
 *   title: '确认删除',
 *   content: '确定要删除这个项目吗？',
 *   type: 'warning',
 *   onConfirm: () => {
 *     // 执行删除操作
 *   }
 * });
 * ```
 */
(ConfirmDialog as ConfirmDialogComponent).confirm = (config: {
  title: string;
  content: React.ReactNode;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
  type?: 'warning' | 'info' | 'confirm' | 'error';
}) => {
  const { confirm } = Modal;
  
  return confirm({
    title: config.title,
    content: config.content,
    onOk: config.onConfirm,
    onCancel: config.onCancel,
    okButtonProps: {
      danger: config.type === 'warning' || config.type === 'error'
    },
    icon: (() => {
      switch (config.type) {
        case 'warning':
        case 'error':
          return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
        case 'info':
          return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
        case 'confirm':
        default:
          return <QuestionCircleOutlined style={{ color: '#faad14' }} />;
      }
    })()
  });
};

export default ConfirmDialog as ConfirmDialogComponent;