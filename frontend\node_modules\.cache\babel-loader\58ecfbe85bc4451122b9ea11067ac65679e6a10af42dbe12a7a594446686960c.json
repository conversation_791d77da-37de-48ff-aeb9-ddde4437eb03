{"ast": null, "code": "import _objectSpread from\"D:/customerDemo/Link-BOM-S/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Input,Select,DatePicker,Modal,Form,InputNumber,Row,Col,Statistic,Progress,Alert,Tag,Typography,Tabs,Tooltip,message}from'antd';import{PlusOutlined,EditOutlined,DeleteOutlined,ExportOutlined,WarningOutlined,CheckCircleOutlined,CloseCircleOutlined,SyncOutlined,DollarOutlined,ArrowUpOutlined,ArrowDownOutlined}from'@ant-design/icons';import dayjs from'dayjs';import{formatCurrency}from'../../utils/format';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Search}=Input;const{RangePicker}=DatePicker;const{Title,Text}=Typography;const{TabPane}=Tabs;const StandardCostPage=()=>{const[loading,setLoading]=useState(false);const[searchText,setSearchText]=useState('');const[selectedCategory,setSelectedCategory]=useState('');const[dateRange,setDateRange]=useState(null);const[isModalVisible,setIsModalVisible]=useState(false);const[editingRecord,setEditingRecord]=useState(null);const[activeTab,setActiveTab]=useState('standards');const[form]=Form.useForm();// 模拟标准成本数据\nconst mockStandardCosts=[{id:'1',materialCode:'ANT-MAIN-001',materialName:'5G主天线单元',specification:'3.5GHz 64T64R',unit:'PCS',standardCost:12500,actualCost:13200,variance:700,variancePercentage:5.6,lastUpdated:'2024-03-15T00:00:00Z',status:'warning',category:'天线组件',supplier:'华为技术',effectiveDate:'2024-01-01',expiryDate:'2024-12-31'},{id:'2',materialCode:'RRU-001',materialName:'射频拉远单元',specification:'200W 3.5GHz',unit:'PCS',standardCost:8500,actualCost:8200,variance:-300,variancePercentage:-3.5,lastUpdated:'2024-03-14T00:00:00Z',status:'normal',category:'射频器件',supplier:'中兴通讯',effectiveDate:'2024-01-01',expiryDate:'2024-12-31'},{id:'3',materialCode:'CABLE-001',materialName:'同轴电缆',specification:'7/8\" 50Ω',unit:'M',standardCost:45,actualCost:52,variance:7,variancePercentage:15.6,lastUpdated:'2024-03-13T00:00:00Z',status:'alert',category:'连接器件',supplier:'安费诺',effectiveDate:'2024-01-01',expiryDate:'2024-12-31'}];// 模拟成本差异数据\nconst mockVariances=[{id:'1',orderNumber:'ORD-2024-001',materialCode:'ANT-MAIN-001',materialName:'5G主天线单元',standardCost:12500,actualCost:13200,variance:700,variancePercentage:5.6,quantity:10,totalVariance:7000,reason:'供应商价格上涨',responsiblePerson:'采购经理',status:'investigating',createdAt:'2024-03-15T00:00:00Z'},{id:'2',orderNumber:'ORD-2024-002',materialCode:'CABLE-001',materialName:'同轴电缆',standardCost:45,actualCost:52,variance:7,variancePercentage:15.6,quantity:500,totalVariance:3500,reason:'汇率波动影响',responsiblePerson:'财务经理',status:'pending',createdAt:'2024-03-13T00:00:00Z'}];// 模拟概览数据\nconst mockOverview={totalMaterials:156,normalCount:120,warningCount:25,alertCount:11,avgVariance:3.2,totalVarianceAmount:45600,lastUpdateTime:'2024-03-15T10:30:00Z'};useEffect(()=>{loadData();},[]);const loadData=async()=>{setLoading(true);// 模拟API调用\nsetTimeout(()=>{setLoading(false);},1000);};const handleAdd=()=>{setEditingRecord(null);form.resetFields();setIsModalVisible(true);};const handleEdit=record=>{setEditingRecord(record);form.setFieldsValue(_objectSpread(_objectSpread({},record),{},{effectiveDate:dayjs(record.effectiveDate),expiryDate:dayjs(record.expiryDate)}));setIsModalVisible(true);};const handleDelete=id=>{Modal.confirm({title:'确认删除',content:'确定要删除这条标准成本记录吗？',onOk:()=>{message.success('删除成功');}});};const handleSubmit=async values=>{try{setLoading(true);// 模拟API调用\nawait new Promise(resolve=>setTimeout(resolve,1000));message.success(editingRecord?'更新成功':'创建成功');setIsModalVisible(false);loadData();}catch(error){message.error('操作失败');}finally{setLoading(false);}};const handleRecalculate=()=>{Modal.confirm({title:'重新计算标准成本',content:'这将基于最新的采购价格和市场数据重新计算所有标准成本，确定继续吗？',onOk:()=>{message.success('重新计算完成');loadData();}});};const getStatusColor=status=>{switch(status){case'normal':return'green';case'warning':return'orange';case'alert':return'red';default:return'default';}};const getStatusText=status=>{switch(status){case'normal':return'正常';case'warning':return'预警';case'alert':return'异常';default:return'未知';}};const getVarianceIcon=variance=>{if(variance>0){return/*#__PURE__*/_jsx(ArrowUpOutlined,{style:{color:'#ff4d4f'}});}else if(variance<0){return/*#__PURE__*/_jsx(ArrowDownOutlined,{style:{color:'#52c41a'}});}return/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'#1890ff'}});};const standardCostColumns=[{title:'物料编码',dataIndex:'materialCode',key:'materialCode',width:120,fixed:'left'},{title:'物料名称',dataIndex:'materialName',key:'materialName',ellipsis:true},{title:'规格型号',dataIndex:'specification',key:'specification',ellipsis:true},{title:'单位',dataIndex:'unit',key:'unit',width:60},{title:'标准成本',dataIndex:'standardCost',key:'standardCost',width:100,render:cost=>formatCurrency(cost)},{title:'实际成本',dataIndex:'actualCost',key:'actualCost',width:100,render:cost=>formatCurrency(cost)},{title:'差异',dataIndex:'variance',key:'variance',width:120,render:(variance,record)=>/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:0,children:[/*#__PURE__*/_jsxs(Space,{children:[getVarianceIcon(variance),/*#__PURE__*/_jsx(Text,{style:{color:variance>0?'#ff4d4f':variance<0?'#52c41a':'#1890ff'},children:formatCurrency(Math.abs(variance))})]}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:12},children:[record.variancePercentage>0?'+':'',record.variancePercentage.toFixed(1),\"%\"]})]})},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>/*#__PURE__*/_jsx(Tag,{color:getStatusColor(status),children:getStatusText(status)})},{title:'供应商',dataIndex:'supplier',key:'supplier',width:100},{title:'更新时间',dataIndex:'lastUpdated',key:'lastUpdated',width:120,render:date=>dayjs(date).format('YYYY-MM-DD')},{title:'操作',key:'action',width:120,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record),children:\"\\u7F16\\u8F91\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>handleDelete(record.id),children:\"\\u5220\\u9664\"})]})}];const varianceColumns=[{title:'订单号',dataIndex:'orderNumber',key:'orderNumber',width:120},{title:'物料编码',dataIndex:'materialCode',key:'materialCode',width:120},{title:'物料名称',dataIndex:'materialName',key:'materialName',ellipsis:true},{title:'标准成本',dataIndex:'standardCost',key:'standardCost',width:100,render:cost=>formatCurrency(cost)},{title:'实际成本',dataIndex:'actualCost',key:'actualCost',width:100,render:cost=>formatCurrency(cost)},{title:'单位差异',dataIndex:'variance',key:'variance',width:100,render:(variance,record)=>/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:0,children:[/*#__PURE__*/_jsx(Text,{style:{color:variance>0?'#ff4d4f':'#52c41a'},children:formatCurrency(Math.abs(variance))}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:12},children:[record.variancePercentage>0?'+':'',record.variancePercentage.toFixed(1),\"%\"]})]})},{title:'数量',dataIndex:'quantity',key:'quantity',width:80},{title:'总差异',dataIndex:'totalVariance',key:'totalVariance',width:100,render:variance=>/*#__PURE__*/_jsx(Text,{style:{color:variance>0?'#ff4d4f':'#52c41a'},children:formatCurrency(Math.abs(variance))})},{title:'差异原因',dataIndex:'reason',key:'reason',ellipsis:true},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>{const statusMap={pending:{color:'orange',text:'待处理'},investigating:{color:'blue',text:'调查中'},resolved:{color:'green',text:'已解决'}};const config=statusMap[status];return/*#__PURE__*/_jsx(Tag,{color:config.color,children:config.text});}}];const renderOverviewTab=()=>/*#__PURE__*/_jsxs(\"div\",{children:[mockOverview.alertCount>0&&/*#__PURE__*/_jsx(Alert,{message:\"\\u6210\\u672C\\u5F02\\u5E38\\u9884\\u8B66\",description:\"\\u53D1\\u73B0 \".concat(mockOverview.alertCount,\" \\u4E2A\\u7269\\u6599\\u6210\\u672C\\u5DEE\\u5F02\\u8D85\\u8FC7\\u9884\\u8B66\\u9608\\u503C\\uFF0C\\u5EFA\\u8BAE\\u53CA\\u65F6\\u5904\\u7406\\u3002\"),type:\"error\",showIcon:true,closable:true,style:{marginBottom:16}}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7269\\u6599\\u603B\\u6570\",value:mockOverview.totalMaterials,prefix:/*#__PURE__*/_jsx(DollarOutlined,{}),valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Statistic,{title:\"\\u6B63\\u5E38\\u7269\\u6599\",value:mockOverview.normalCount,valueStyle:{color:'#52c41a'},suffix:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5360\\u6BD4: \".concat((mockOverview.normalCount/mockOverview.totalMaterials*100).toFixed(1),\"%\"),children:/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Progress,{percent:mockOverview.normalCount/mockOverview.totalMaterials*100,size:\"small\",showInfo:false,strokeColor:\"#52c41a\"})]})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Statistic,{title:\"\\u9884\\u8B66\\u7269\\u6599\",value:mockOverview.warningCount,valueStyle:{color:'#faad14'},suffix:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5360\\u6BD4: \".concat((mockOverview.warningCount/mockOverview.totalMaterials*100).toFixed(1),\"%\"),children:/*#__PURE__*/_jsx(WarningOutlined,{style:{color:'#faad14'}})})}),/*#__PURE__*/_jsx(Progress,{percent:mockOverview.warningCount/mockOverview.totalMaterials*100,size:\"small\",showInfo:false,strokeColor:\"#faad14\"})]})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F02\\u5E38\\u7269\\u6599\",value:mockOverview.alertCount,valueStyle:{color:'#ff4d4f'},suffix:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5360\\u6BD4: \".concat((mockOverview.alertCount/mockOverview.totalMaterials*100).toFixed(1),\"%\"),children:/*#__PURE__*/_jsx(CloseCircleOutlined,{style:{color:'#ff4d4f'}})})}),/*#__PURE__*/_jsx(Progress,{percent:mockOverview.alertCount/mockOverview.totalMaterials*100,size:\"small\",showInfo:false,strokeColor:\"#ff4d4f\"})]})})]}),/*#__PURE__*/_jsx(Card,{title:\"\\u6807\\u51C6\\u6210\\u672C\\u7BA1\\u7406\",extra:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(SyncOutlined,{}),onClick:handleRecalculate,children:\"\\u91CD\\u65B0\\u8BA1\\u7B97\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAdd,children:\"\\u65B0\\u589E\\u6807\\u51C6\\u6210\\u672C\"})]}),children:/*#__PURE__*/_jsx(Table,{columns:standardCostColumns,dataSource:mockStandardCosts,rowKey:\"id\",loading:loading,scroll:{x:1200},pagination:{total:mockStandardCosts.length,pageSize:10,showSizeChanger:true,showQuickJumper:true,showTotal:total=>\"\\u5171 \".concat(total,\" \\u6761\\u8BB0\\u5F55\")}})})]});const renderVarianceTab=()=>/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Card,{title:\"\\u6210\\u672C\\u5DEE\\u5F02\\u5206\\u6790\",extra:/*#__PURE__*/_jsx(Space,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\\u5DEE\\u5F02\\u62A5\\u544A\"})}),children:/*#__PURE__*/_jsx(Table,{columns:varianceColumns,dataSource:mockVariances,rowKey:\"id\",loading:loading,pagination:{total:mockVariances.length,pageSize:10,showSizeChanger:true,showQuickJumper:true,showTotal:total=>\"\\u5171 \".concat(total,\" \\u6761\\u8BB0\\u5F55\")}})})});return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u6807\\u51C6\\u6210\\u672C\\u7BA1\\u7406\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u7269\\u6599\\u7F16\\u7801\\u6216\\u540D\\u79F0\",allowClear:true,style:{width:200},value:searchText,onChange:e=>setSearchText(e.target.value)}),/*#__PURE__*/_jsx(Select,{placeholder:\"\\u7269\\u6599\\u7C7B\\u522B\",allowClear:true,style:{width:120},value:selectedCategory,onChange:setSelectedCategory,options:[{label:'天线组件',value:'天线组件'},{label:'射频器件',value:'射频器件'},{label:'连接器件',value:'连接器件'}]}),/*#__PURE__*/_jsx(RangePicker,{value:dateRange,onChange:dates=>setDateRange(dates),style:{width:240}}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\"})]})})]}),/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,children:[/*#__PURE__*/_jsx(TabPane,{tab:\"\\u6807\\u51C6\\u6210\\u672C\",children:renderOverviewTab()},\"standards\"),/*#__PURE__*/_jsx(TabPane,{tab:\"\\u6210\\u672C\\u5DEE\\u5F02\",children:renderVarianceTab()},\"variances\")]})]}),/*#__PURE__*/_jsx(Modal,{title:editingRecord?'编辑标准成本':'新增标准成本',open:isModalVisible,onCancel:()=>setIsModalVisible(false),footer:null,width:600,children:/*#__PURE__*/_jsxs(Form,{form:form,layout:\"vertical\",onFinish:handleSubmit,children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"materialCode\",label:\"\\u7269\\u6599\\u7F16\\u7801\",rules:[{required:true,message:'请输入物料编码'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u7F16\\u7801\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"materialName\",label:\"\\u7269\\u6599\\u540D\\u79F0\",rules:[{required:true,message:'请输入物料名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u7269\\u6599\\u540D\\u79F0\"})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"specification\",label:\"\\u89C4\\u683C\\u578B\\u53F7\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u89C4\\u683C\\u578B\\u53F7\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"unit\",label:\"\\u5355\\u4F4D\",rules:[{required:true,message:'请选择单位'}],children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u5355\\u4F4D\",options:[{label:'PCS',value:'PCS'},{label:'M',value:'M'},{label:'KG',value:'KG'},{label:'SET',value:'SET'}]})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"standardCost\",label:\"\\u6807\\u51C6\\u6210\\u672C\",rules:[{required:true,message:'请输入标准成本'}],children:/*#__PURE__*/_jsx(InputNumber,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u6807\\u51C6\\u6210\\u672C\",style:{width:'100%'},min:0,precision:2,formatter:value=>\"\\xA5 \".concat(value).replace(/\\B(?=(\\d{3})+(?!\\d))/g,','),parser:value=>{const parsed=parseFloat(value.replace(/¥\\s?|(,*)/g,''));return isNaN(parsed)?0:parsed;}})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"category\",label:\"\\u7269\\u6599\\u7C7B\\u522B\",rules:[{required:true,message:'请选择物料类别'}],children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u7269\\u6599\\u7C7B\\u522B\",options:[{label:'天线组件',value:'天线组件'},{label:'射频器件',value:'射频器件'},{label:'连接器件',value:'连接器件'}]})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"supplier\",label:\"\\u4E3B\\u8981\\u4F9B\\u5E94\\u5546\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u4E3B\\u8981\\u4F9B\\u5E94\\u5546\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"effectiveDate\",label:\"\\u751F\\u6548\\u65E5\\u671F\",rules:[{required:true,message:'请选择生效日期'}],children:/*#__PURE__*/_jsx(DatePicker,{style:{width:'100%'}})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"expiryDate\",label:\"\\u5931\\u6548\\u65E5\\u671F\",children:/*#__PURE__*/_jsx(DatePicker,{style:{width:'100%'}})}),/*#__PURE__*/_jsx(Form.Item,{style:{marginBottom:0,textAlign:'right'},children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setIsModalVisible(false),children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,children:editingRecord?'更新':'创建'})]})})]})})]});};export default StandardCostPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "DatePicker", "Modal", "Form", "InputNumber", "Row", "Col", "Statistic", "Progress", "<PERSON><PERSON>", "Tag", "Typography", "Tabs", "<PERSON><PERSON><PERSON>", "message", "PlusOutlined", "EditOutlined", "DeleteOutlined", "ExportOutlined", "WarningOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "SyncOutlined", "DollarOutlined", "ArrowUpOutlined", "ArrowDownOutlined", "dayjs", "formatCurrency", "jsx", "_jsx", "jsxs", "_jsxs", "Search", "RangePicker", "Title", "Text", "TabPane", "StandardCostPage", "loading", "setLoading", "searchText", "setSearchText", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "date<PERSON><PERSON><PERSON>", "setDateRange", "isModalVisible", "setIsModalVisible", "editingRecord", "setEditingRecord", "activeTab", "setActiveTab", "form", "useForm", "mockStandardCosts", "id", "materialCode", "materialName", "specification", "unit", "standardCost", "actualCost", "variance", "variancePercentage", "lastUpdated", "status", "category", "supplier", "effectiveDate", "expiryDate", "mockVariances", "orderNumber", "quantity", "totalVariance", "reason", "<PERSON><PERSON><PERSON>", "createdAt", "mockOverview", "totalMaterials", "normalCount", "warningCount", "alertCount", "avg<PERSON><PERSON><PERSON>", "totalVarianceAmount", "lastUpdateTime", "loadData", "setTimeout", "handleAdd", "resetFields", "handleEdit", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectSpread", "handleDelete", "confirm", "title", "content", "onOk", "success", "handleSubmit", "values", "Promise", "resolve", "error", "handleRecalculate", "getStatusColor", "getStatusText", "getVarianceIcon", "style", "color", "standardCostColumns", "dataIndex", "key", "width", "fixed", "ellipsis", "render", "cost", "direction", "size", "children", "Math", "abs", "type", "fontSize", "toFixed", "date", "format", "_", "icon", "onClick", "danger", "varianceColumns", "statusMap", "pending", "text", "investigating", "resolved", "config", "renderOverviewTab", "description", "concat", "showIcon", "closable", "marginBottom", "gutter", "xs", "sm", "value", "prefix", "valueStyle", "suffix", "percent", "showInfo", "strokeColor", "extra", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "total", "length", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "renderVarianceTab", "justify", "align", "level", "margin", "placeholder", "allowClear", "onChange", "e", "target", "options", "label", "dates", "active<PERSON><PERSON>", "tab", "open", "onCancel", "footer", "layout", "onFinish", "span", "<PERSON><PERSON>", "name", "rules", "required", "min", "precision", "formatter", "replace", "parser", "parsed", "parseFloat", "isNaN", "textAlign", "htmlType"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/cost/StandardCostPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  DatePicker,\n  Modal,\n  Form,\n  InputNumber,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Alert,\n  Tag,\n  Typography,\n  Tabs,\n  Tooltip,\n  message,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ExportOutlined,\n  CalculatorOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  SyncOutlined,\n  DollarOutlined,\n  ArrowUpOutlined,\n  ArrowDownOutlined,\n} from '@ant-design/icons';\nimport { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\nimport { formatCurrency } from '../../utils/format';\n\nconst { Search } = Input;\nconst { RangePicker } = DatePicker;\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\n\ninterface StandardCost {\n  id: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  unit: string;\n  standardCost: number;\n  actualCost: number;\n  variance: number;\n  variancePercentage: number;\n  lastUpdated: string;\n  status: 'normal' | 'warning' | 'alert';\n  category: string;\n  supplier: string;\n  effectiveDate: string;\n  expiryDate: string;\n}\n\ninterface CostVariance {\n  id: string;\n  orderNumber: string;\n  materialCode: string;\n  materialName: string;\n  standardCost: number;\n  actualCost: number;\n  variance: number;\n  variancePercentage: number;\n  quantity: number;\n  totalVariance: number;\n  reason: string;\n  responsiblePerson: string;\n  status: 'pending' | 'investigating' | 'resolved';\n  createdAt: string;\n}\n\nconst StandardCostPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [searchText, setSearchText] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingRecord, setEditingRecord] = useState<StandardCost | null>(null);\n  const [activeTab, setActiveTab] = useState('standards');\n  const [form] = Form.useForm();\n\n  // 模拟标准成本数据\n  const mockStandardCosts: StandardCost[] = [\n    {\n      id: '1',\n      materialCode: 'ANT-MAIN-001',\n      materialName: '5G主天线单元',\n      specification: '3.5GHz 64T64R',\n      unit: 'PCS',\n      standardCost: 12500,\n      actualCost: 13200,\n      variance: 700,\n      variancePercentage: 5.6,\n      lastUpdated: '2024-03-15T00:00:00Z',\n      status: 'warning',\n      category: '天线组件',\n      supplier: '华为技术',\n      effectiveDate: '2024-01-01',\n      expiryDate: '2024-12-31',\n    },\n    {\n      id: '2',\n      materialCode: 'RRU-001',\n      materialName: '射频拉远单元',\n      specification: '200W 3.5GHz',\n      unit: 'PCS',\n      standardCost: 8500,\n      actualCost: 8200,\n      variance: -300,\n      variancePercentage: -3.5,\n      lastUpdated: '2024-03-14T00:00:00Z',\n      status: 'normal',\n      category: '射频器件',\n      supplier: '中兴通讯',\n      effectiveDate: '2024-01-01',\n      expiryDate: '2024-12-31',\n    },\n    {\n      id: '3',\n      materialCode: 'CABLE-001',\n      materialName: '同轴电缆',\n      specification: '7/8\" 50Ω',\n      unit: 'M',\n      standardCost: 45,\n      actualCost: 52,\n      variance: 7,\n      variancePercentage: 15.6,\n      lastUpdated: '2024-03-13T00:00:00Z',\n      status: 'alert',\n      category: '连接器件',\n      supplier: '安费诺',\n      effectiveDate: '2024-01-01',\n      expiryDate: '2024-12-31',\n    },\n  ];\n\n  // 模拟成本差异数据\n  const mockVariances: CostVariance[] = [\n    {\n      id: '1',\n      orderNumber: 'ORD-2024-001',\n      materialCode: 'ANT-MAIN-001',\n      materialName: '5G主天线单元',\n      standardCost: 12500,\n      actualCost: 13200,\n      variance: 700,\n      variancePercentage: 5.6,\n      quantity: 10,\n      totalVariance: 7000,\n      reason: '供应商价格上涨',\n      responsiblePerson: '采购经理',\n      status: 'investigating',\n      createdAt: '2024-03-15T00:00:00Z',\n    },\n    {\n      id: '2',\n      orderNumber: 'ORD-2024-002',\n      materialCode: 'CABLE-001',\n      materialName: '同轴电缆',\n      standardCost: 45,\n      actualCost: 52,\n      variance: 7,\n      variancePercentage: 15.6,\n      quantity: 500,\n      totalVariance: 3500,\n      reason: '汇率波动影响',\n      responsiblePerson: '财务经理',\n      status: 'pending',\n      createdAt: '2024-03-13T00:00:00Z',\n    },\n  ];\n\n  // 模拟概览数据\n  const mockOverview = {\n    totalMaterials: 156,\n    normalCount: 120,\n    warningCount: 25,\n    alertCount: 11,\n    avgVariance: 3.2,\n    totalVarianceAmount: 45600,\n    lastUpdateTime: '2024-03-15T10:30:00Z',\n  };\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    // 模拟API调用\n    setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n  };\n\n  const handleAdd = () => {\n    setEditingRecord(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEdit = (record: StandardCost) => {\n    setEditingRecord(record);\n    form.setFieldsValue({\n      ...record,\n      effectiveDate: dayjs(record.effectiveDate),\n      expiryDate: dayjs(record.expiryDate),\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDelete = (id: string) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: '确定要删除这条标准成本记录吗？',\n      onOk: () => {\n        message.success('删除成功');\n      },\n    });\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      setLoading(true);\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      message.success(editingRecord ? '更新成功' : '创建成功');\n      setIsModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('操作失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRecalculate = () => {\n    Modal.confirm({\n      title: '重新计算标准成本',\n      content: '这将基于最新的采购价格和市场数据重新计算所有标准成本，确定继续吗？',\n      onOk: () => {\n        message.success('重新计算完成');\n        loadData();\n      },\n    });\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'normal': return 'green';\n      case 'warning': return 'orange';\n      case 'alert': return 'red';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'normal': return '正常';\n      case 'warning': return '预警';\n      case 'alert': return '异常';\n      default: return '未知';\n    }\n  };\n\n  const getVarianceIcon = (variance: number) => {\n    if (variance > 0) {\n      return <ArrowUpOutlined style={{ color: '#ff4d4f' }} />;\n    } else if (variance < 0) {\n      return <ArrowDownOutlined style={{ color: '#52c41a' }} />;\n    }\n    return <CheckCircleOutlined style={{ color: '#1890ff' }} />;\n  };\n\n  const standardCostColumns: ColumnsType<StandardCost> = [\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n      fixed: 'left',\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      ellipsis: true,\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      key: 'specification',\n      ellipsis: true,\n    },\n    {\n      title: '单位',\n      dataIndex: 'unit',\n      key: 'unit',\n      width: 60,\n    },\n    {\n      title: '标准成本',\n      dataIndex: 'standardCost',\n      key: 'standardCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '实际成本',\n      dataIndex: 'actualCost',\n      key: 'actualCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '差异',\n      dataIndex: 'variance',\n      key: 'variance',\n      width: 120,\n      render: (variance: number, record: StandardCost) => (\n        <Space direction=\"vertical\" size={0}>\n          <Space>\n            {getVarianceIcon(variance)}\n            <Text style={{ color: variance > 0 ? '#ff4d4f' : variance < 0 ? '#52c41a' : '#1890ff' }}>\n              {formatCurrency(Math.abs(variance))}\n            </Text>\n          </Space>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.variancePercentage > 0 ? '+' : ''}{record.variancePercentage.toFixed(1)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 100,\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'lastUpdated',\n      key: 'lastUpdated',\n      width: 120,\n      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      fixed: 'right',\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n          >\n            删除\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  const varianceColumns: ColumnsType<CostVariance> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNumber',\n      key: 'orderNumber',\n      width: 120,\n    },\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      key: 'materialCode',\n      width: 120,\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      key: 'materialName',\n      ellipsis: true,\n    },\n    {\n      title: '标准成本',\n      dataIndex: 'standardCost',\n      key: 'standardCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '实际成本',\n      dataIndex: 'actualCost',\n      key: 'actualCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '单位差异',\n      dataIndex: 'variance',\n      key: 'variance',\n      width: 100,\n      render: (variance: number, record: CostVariance) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text style={{ color: variance > 0 ? '#ff4d4f' : '#52c41a' }}>\n            {formatCurrency(Math.abs(variance))}\n          </Text>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.variancePercentage > 0 ? '+' : ''}{record.variancePercentage.toFixed(1)}%\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      width: 80,\n    },\n    {\n      title: '总差异',\n      dataIndex: 'totalVariance',\n      key: 'totalVariance',\n      width: 100,\n      render: (variance: number) => (\n        <Text style={{ color: variance > 0 ? '#ff4d4f' : '#52c41a' }}>\n          {formatCurrency(Math.abs(variance))}\n        </Text>\n      ),\n    },\n    {\n      title: '差异原因',\n      dataIndex: 'reason',\n      key: 'reason',\n      ellipsis: true,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => {\n        const statusMap = {\n          pending: { color: 'orange', text: '待处理' },\n          investigating: { color: 'blue', text: '调查中' },\n          resolved: { color: 'green', text: '已解决' },\n        };\n        const config = statusMap[status as keyof typeof statusMap];\n        return <Tag color={config.color}>{config.text}</Tag>;\n      },\n    },\n  ];\n\n  const renderOverviewTab = () => (\n    <div>\n      {/* 预警信息 */}\n      {mockOverview.alertCount > 0 && (\n        <Alert\n          message=\"成本异常预警\"\n          description={`发现 ${mockOverview.alertCount} 个物料成本差异超过预警阈值，建议及时处理。`}\n          type=\"error\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n      )}\n\n      {/* 概览统计 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"物料总数\"\n              value={mockOverview.totalMaterials}\n              prefix={<DollarOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"正常物料\"\n              value={mockOverview.normalCount}\n              valueStyle={{ color: '#52c41a' }}\n              suffix={\n                <Tooltip title={`占比: ${((mockOverview.normalCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>\n                  <CheckCircleOutlined style={{ color: '#52c41a' }} />\n                </Tooltip>\n              }\n            />\n            <Progress\n              percent={(mockOverview.normalCount / mockOverview.totalMaterials) * 100}\n              size=\"small\"\n              showInfo={false}\n              strokeColor=\"#52c41a\"\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"预警物料\"\n              value={mockOverview.warningCount}\n              valueStyle={{ color: '#faad14' }}\n              suffix={\n                <Tooltip title={`占比: ${((mockOverview.warningCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>\n                  <WarningOutlined style={{ color: '#faad14' }} />\n                </Tooltip>\n              }\n            />\n            <Progress\n              percent={(mockOverview.warningCount / mockOverview.totalMaterials) * 100}\n              size=\"small\"\n              showInfo={false}\n              strokeColor=\"#faad14\"\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"异常物料\"\n              value={mockOverview.alertCount}\n              valueStyle={{ color: '#ff4d4f' }}\n              suffix={\n                <Tooltip title={`占比: ${((mockOverview.alertCount / mockOverview.totalMaterials) * 100).toFixed(1)}%`}>\n                  <CloseCircleOutlined style={{ color: '#ff4d4f' }} />\n                </Tooltip>\n              }\n            />\n            <Progress\n              percent={(mockOverview.alertCount / mockOverview.totalMaterials) * 100}\n              size=\"small\"\n              showInfo={false}\n              strokeColor=\"#ff4d4f\"\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 标准成本列表 */}\n      <Card title=\"标准成本管理\" extra={\n        <Space>\n          <Button icon={<SyncOutlined />} onClick={handleRecalculate}>\n            重新计算\n          </Button>\n          <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAdd}>\n            新增标准成本\n          </Button>\n        </Space>\n      }>\n        <Table\n          columns={standardCostColumns}\n          dataSource={mockStandardCosts}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            total: mockStandardCosts.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  const renderVarianceTab = () => (\n    <div>\n      <Card title=\"成本差异分析\" extra={\n        <Space>\n          <Button icon={<ExportOutlined />}>\n            导出差异报告\n          </Button>\n        </Space>\n      }>\n        <Table\n          columns={varianceColumns}\n          dataSource={mockVariances}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            total: mockVariances.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n    </div>\n  );\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 24 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              标准成本管理\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索物料编码或名称\"\n                allowClear\n                style={{ width: 200 }}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n              />\n              <Select\n                placeholder=\"物料类别\"\n                allowClear\n                style={{ width: 120 }}\n                value={selectedCategory}\n                onChange={setSelectedCategory}\n                options={[\n                  { label: '天线组件', value: '天线组件' },\n                  { label: '射频器件', value: '射频器件' },\n                  { label: '连接器件', value: '连接器件' },\n                ]}\n              />\n              <RangePicker\n                value={dateRange}\n                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n                style={{ width: 240 }}\n              />\n              <Button icon={<ExportOutlined />}>\n                导出\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab=\"标准成本\" key=\"standards\">\n            {renderOverviewTab()}\n          </TabPane>\n          <TabPane tab=\"成本差异\" key=\"variances\">\n            {renderVarianceTab()}\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 新增/编辑模态框 */}\n      <Modal\n        title={editingRecord ? '编辑标准成本' : '新增标准成本'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"materialCode\"\n                label=\"物料编码\"\n                rules={[{ required: true, message: '请输入物料编码' }]}\n              >\n                <Input placeholder=\"请输入物料编码\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"materialName\"\n                label=\"物料名称\"\n                rules={[{ required: true, message: '请输入物料名称' }]}\n              >\n                <Input placeholder=\"请输入物料名称\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"specification\"\n                label=\"规格型号\"\n              >\n                <Input placeholder=\"请输入规格型号\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"unit\"\n                label=\"单位\"\n                rules={[{ required: true, message: '请选择单位' }]}\n              >\n                <Select\n                  placeholder=\"请选择单位\"\n                  options={[\n                    { label: 'PCS', value: 'PCS' },\n                    { label: 'M', value: 'M' },\n                    { label: 'KG', value: 'KG' },\n                    { label: 'SET', value: 'SET' },\n                  ]}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"standardCost\"\n                label=\"标准成本\"\n                rules={[{ required: true, message: '请输入标准成本' }]}\n              >\n                <InputNumber\n                  placeholder=\"请输入标准成本\"\n                  style={{ width: '100%' }}\n                  min={0}\n                  precision={2}\n                  formatter={(value) => `¥ ${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n                  parser={((value: string | undefined) => {\n                    const parsed = parseFloat(value!.replace(/¥\\s?|(,*)/g, ''));\n                    return isNaN(parsed) ? 0 : parsed;\n                  }) as any}\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"category\"\n                label=\"物料类别\"\n                rules={[{ required: true, message: '请选择物料类别' }]}\n              >\n                <Select\n                  placeholder=\"请选择物料类别\"\n                  options={[\n                    { label: '天线组件', value: '天线组件' },\n                    { label: '射频器件', value: '射频器件' },\n                    { label: '连接器件', value: '连接器件' },\n                  ]}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"supplier\"\n                label=\"主要供应商\"\n              >\n                <Input placeholder=\"请输入主要供应商\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"effectiveDate\"\n                label=\"生效日期\"\n                rules={[{ required: true, message: '请选择生效日期' }]}\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"expiryDate\"\n            label=\"失效日期\"\n          >\n            <DatePicker style={{ width: '100%' }} />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setIsModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n                {editingRecord ? '更新' : '创建'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default StandardCostPage;"], "mappings": "wHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,WAAW,CACXC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,QAAQ,CACRC,KAAK,CACLC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,OAAO,CACPC,OAAO,KACF,MAAM,CACb,OACEC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,cAAc,CAEdC,eAAe,CACfC,mBAAmB,CACnBC,mBAAmB,CACnBC,YAAY,CACZC,cAAc,CACdC,eAAe,CACfC,iBAAiB,KACZ,mBAAmB,CAE1B,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAEC,MAAO,CAAC,CAAGjC,KAAK,CACxB,KAAM,CAAEkC,WAAY,CAAC,CAAGhC,UAAU,CAClC,KAAM,CAAEiC,KAAK,CAAEC,IAAK,CAAC,CAAGxB,UAAU,CAClC,KAAM,CAAEyB,OAAQ,CAAC,CAAGxB,IAAI,CAqCxB,KAAM,CAAAyB,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC+C,UAAU,CAAEC,aAAa,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlD,QAAQ,CAAS,EAAE,CAAC,CACpE,KAAM,CAACmD,SAAS,CAAEC,YAAY,CAAC,CAAGpD,QAAQ,CAAoC,IAAI,CAAC,CACnF,KAAM,CAACqD,cAAc,CAAEC,iBAAiB,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACuD,aAAa,CAAEC,gBAAgB,CAAC,CAAGxD,QAAQ,CAAsB,IAAI,CAAC,CAC7E,KAAM,CAACyD,SAAS,CAAEC,YAAY,CAAC,CAAG1D,QAAQ,CAAC,WAAW,CAAC,CACvD,KAAM,CAAC2D,IAAI,CAAC,CAAGjD,IAAI,CAACkD,OAAO,CAAC,CAAC,CAE7B;AACA,KAAM,CAAAC,iBAAiC,CAAG,CACxC,CACEC,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,SAAS,CACvBC,aAAa,CAAE,eAAe,CAC9BC,IAAI,CAAE,KAAK,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAE,GAAG,CACbC,kBAAkB,CAAE,GAAG,CACvBC,WAAW,CAAE,sBAAsB,CACnCC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,MAAM,CAChBC,aAAa,CAAE,YAAY,CAC3BC,UAAU,CAAE,YACd,CAAC,CACD,CACEd,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,SAAS,CACvBC,YAAY,CAAE,QAAQ,CACtBC,aAAa,CAAE,aAAa,CAC5BC,IAAI,CAAE,KAAK,CACXC,YAAY,CAAE,IAAI,CAClBC,UAAU,CAAE,IAAI,CAChBC,QAAQ,CAAE,CAAC,GAAG,CACdC,kBAAkB,CAAE,CAAC,GAAG,CACxBC,WAAW,CAAE,sBAAsB,CACnCC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,MAAM,CAChBC,aAAa,CAAE,YAAY,CAC3BC,UAAU,CAAE,YACd,CAAC,CACD,CACEd,EAAE,CAAE,GAAG,CACPC,YAAY,CAAE,WAAW,CACzBC,YAAY,CAAE,MAAM,CACpBC,aAAa,CAAE,UAAU,CACzBC,IAAI,CAAE,GAAG,CACTC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,CAAC,CACXC,kBAAkB,CAAE,IAAI,CACxBC,WAAW,CAAE,sBAAsB,CACnCC,MAAM,CAAE,OAAO,CACfC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,KAAK,CACfC,aAAa,CAAE,YAAY,CAC3BC,UAAU,CAAE,YACd,CAAC,CACF,CAED;AACA,KAAM,CAAAC,aAA6B,CAAG,CACpC,CACEf,EAAE,CAAE,GAAG,CACPgB,WAAW,CAAE,cAAc,CAC3Bf,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,SAAS,CACvBG,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAE,GAAG,CACbC,kBAAkB,CAAE,GAAG,CACvBS,QAAQ,CAAE,EAAE,CACZC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,SAAS,CACjBC,iBAAiB,CAAE,MAAM,CACzBV,MAAM,CAAE,eAAe,CACvBW,SAAS,CAAE,sBACb,CAAC,CACD,CACErB,EAAE,CAAE,GAAG,CACPgB,WAAW,CAAE,cAAc,CAC3Bf,YAAY,CAAE,WAAW,CACzBC,YAAY,CAAE,MAAM,CACpBG,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,CAAC,CACXC,kBAAkB,CAAE,IAAI,CACxBS,QAAQ,CAAE,GAAG,CACbC,aAAa,CAAE,IAAI,CACnBC,MAAM,CAAE,QAAQ,CAChBC,iBAAiB,CAAE,MAAM,CACzBV,MAAM,CAAE,SAAS,CACjBW,SAAS,CAAE,sBACb,CAAC,CACF,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,CACnBC,cAAc,CAAE,GAAG,CACnBC,WAAW,CAAE,GAAG,CAChBC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EAAE,CACdC,WAAW,CAAE,GAAG,CAChBC,mBAAmB,CAAE,KAAK,CAC1BC,cAAc,CAAE,sBAClB,CAAC,CAED1F,SAAS,CAAC,IAAM,CACd2F,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B9C,UAAU,CAAC,IAAI,CAAC,CAChB;AACA+C,UAAU,CAAC,IAAM,CACf/C,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,KAAM,CAAAgD,SAAS,CAAGA,CAAA,GAAM,CACtBtC,gBAAgB,CAAC,IAAI,CAAC,CACtBG,IAAI,CAACoC,WAAW,CAAC,CAAC,CAClBzC,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAA0C,UAAU,CAAIC,MAAoB,EAAK,CAC3CzC,gBAAgB,CAACyC,MAAM,CAAC,CACxBtC,IAAI,CAACuC,cAAc,CAAAC,aAAA,CAAAA,aAAA,IACdF,MAAM,MACTtB,aAAa,CAAE1C,KAAK,CAACgE,MAAM,CAACtB,aAAa,CAAC,CAC1CC,UAAU,CAAE3C,KAAK,CAACgE,MAAM,CAACrB,UAAU,CAAC,EACrC,CAAC,CACFtB,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAA8C,YAAY,CAAItC,EAAU,EAAK,CACnCrD,KAAK,CAAC4F,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,iBAAiB,CAC1BC,IAAI,CAAEA,CAAA,GAAM,CACVnF,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC,CACzB,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,MAAW,EAAK,CAC1C,GAAI,CACF7D,UAAU,CAAC,IAAI,CAAC,CAChB;AACA,KAAM,IAAI,CAAA8D,OAAO,CAACC,OAAO,EAAIhB,UAAU,CAACgB,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvDxF,OAAO,CAACoF,OAAO,CAAClD,aAAa,CAAG,MAAM,CAAG,MAAM,CAAC,CAChDD,iBAAiB,CAAC,KAAK,CAAC,CACxBsC,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOkB,KAAK,CAAE,CACdzF,OAAO,CAACyF,KAAK,CAAC,MAAM,CAAC,CACvB,CAAC,OAAS,CACRhE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAiE,iBAAiB,CAAGA,CAAA,GAAM,CAC9BtG,KAAK,CAAC4F,OAAO,CAAC,CACZC,KAAK,CAAE,UAAU,CACjBC,OAAO,CAAE,mCAAmC,CAC5CC,IAAI,CAAEA,CAAA,GAAM,CACVnF,OAAO,CAACoF,OAAO,CAAC,QAAQ,CAAC,CACzBb,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAoB,cAAc,CAAIxC,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,QAAQ,CAAE,MAAO,OAAO,CAC7B,IAAK,SAAS,CAAE,MAAO,QAAQ,CAC/B,IAAK,OAAO,CAAE,MAAO,KAAK,CAC1B,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAyC,aAAa,CAAIzC,MAAc,EAAK,CACxC,OAAQA,MAAM,EACZ,IAAK,QAAQ,CAAE,MAAO,IAAI,CAC1B,IAAK,SAAS,CAAE,MAAO,IAAI,CAC3B,IAAK,OAAO,CAAE,MAAO,IAAI,CACzB,QAAS,MAAO,IAAI,CACtB,CACF,CAAC,CAED,KAAM,CAAA0C,eAAe,CAAI7C,QAAgB,EAAK,CAC5C,GAAIA,QAAQ,CAAG,CAAC,CAAE,CAChB,mBAAOjC,IAAA,CAACL,eAAe,EAACoF,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACzD,CAAC,IAAM,IAAI/C,QAAQ,CAAG,CAAC,CAAE,CACvB,mBAAOjC,IAAA,CAACJ,iBAAiB,EAACmF,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC3D,CACA,mBAAOhF,IAAA,CAACT,mBAAmB,EAACwF,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC7D,CAAC,CAED,KAAM,CAAAC,mBAA8C,CAAG,CACrD,CACEf,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,MACT,CAAC,CACD,CACEnB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBG,QAAQ,CAAE,IACZ,CAAC,CACD,CACEpB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBG,QAAQ,CAAE,IACZ,CAAC,CACD,CACEpB,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,EACT,CAAC,CACD,CACElB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGC,IAAY,EAAK1F,cAAc,CAAC0F,IAAI,CAC/C,CAAC,CACD,CACEtB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGC,IAAY,EAAK1F,cAAc,CAAC0F,IAAI,CAC/C,CAAC,CACD,CACEtB,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAEA,CAACtD,QAAgB,CAAE4B,MAAoB,gBAC7C3D,KAAA,CAACjC,KAAK,EAACwH,SAAS,CAAC,UAAU,CAACC,IAAI,CAAE,CAAE,CAAAC,QAAA,eAClCzF,KAAA,CAACjC,KAAK,EAAA0H,QAAA,EACHb,eAAe,CAAC7C,QAAQ,CAAC,cAC1BjC,IAAA,CAACM,IAAI,EAACyE,KAAK,CAAE,CAAEC,KAAK,CAAE/C,QAAQ,CAAG,CAAC,CAAG,SAAS,CAAGA,QAAQ,CAAG,CAAC,CAAG,SAAS,CAAG,SAAU,CAAE,CAAA0D,QAAA,CACrF7F,cAAc,CAAC8F,IAAI,CAACC,GAAG,CAAC5D,QAAQ,CAAC,CAAC,CAC/B,CAAC,EACF,CAAC,cACR/B,KAAA,CAACI,IAAI,EAACwF,IAAI,CAAC,WAAW,CAACf,KAAK,CAAE,CAAEgB,QAAQ,CAAE,EAAG,CAAE,CAAAJ,QAAA,EAC5C9B,MAAM,CAAC3B,kBAAkB,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAE2B,MAAM,CAAC3B,kBAAkB,CAAC8D,OAAO,CAAC,CAAC,CAAC,CAAC,GAClF,EAAM,CAAC,EACF,CAEX,CAAC,CACD,CACE9B,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,EAAE,CACTG,MAAM,CAAGnD,MAAc,eACrBpC,IAAA,CAACnB,GAAG,EAACmG,KAAK,CAAEJ,cAAc,CAACxC,MAAM,CAAE,CAAAuD,QAAA,CAChCd,aAAa,CAACzC,MAAM,CAAC,CACnB,CAET,CAAC,CACD,CACE8B,KAAK,CAAE,KAAK,CACZgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GACT,CAAC,CACD,CACElB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGU,IAAY,EAAKpG,KAAK,CAACoG,IAAI,CAAC,CAACC,MAAM,CAAC,YAAY,CAC3D,CAAC,CACD,CACEhC,KAAK,CAAE,IAAI,CACXiB,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,OAAO,CACdE,MAAM,CAAEA,CAACY,CAAC,CAAEtC,MAAM,gBAChB3D,KAAA,CAACjC,KAAK,EAAA0H,QAAA,eACJ3F,IAAA,CAAChC,MAAM,EACL8H,IAAI,CAAC,MAAM,CACXJ,IAAI,CAAC,OAAO,CACZU,IAAI,cAAEpG,IAAA,CAACb,YAAY,GAAE,CAAE,CACvBkH,OAAO,CAAEA,CAAA,GAAMzC,UAAU,CAACC,MAAM,CAAE,CAAA8B,QAAA,CACnC,cAED,CAAQ,CAAC,cACT3F,IAAA,CAAChC,MAAM,EACL8H,IAAI,CAAC,MAAM,CACXJ,IAAI,CAAC,OAAO,CACZY,MAAM,MACNF,IAAI,cAAEpG,IAAA,CAACZ,cAAc,GAAE,CAAE,CACzBiH,OAAO,CAAEA,CAAA,GAAMrC,YAAY,CAACH,MAAM,CAACnC,EAAE,CAAE,CAAAiE,QAAA,CACxC,cAED,CAAQ,CAAC,EACJ,CAEX,CAAC,CACF,CAED,KAAM,CAAAY,eAA0C,CAAG,CACjD,CACErC,KAAK,CAAE,KAAK,CACZgB,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GACT,CAAC,CACD,CACElB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GACT,CAAC,CACD,CACElB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBG,QAAQ,CAAE,IACZ,CAAC,CACD,CACEpB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGC,IAAY,EAAK1F,cAAc,CAAC0F,IAAI,CAC/C,CAAC,CACD,CACEtB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGC,IAAY,EAAK1F,cAAc,CAAC0F,IAAI,CAC/C,CAAC,CACD,CACEtB,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAEA,CAACtD,QAAgB,CAAE4B,MAAoB,gBAC7C3D,KAAA,CAACjC,KAAK,EAACwH,SAAS,CAAC,UAAU,CAACC,IAAI,CAAE,CAAE,CAAAC,QAAA,eAClC3F,IAAA,CAACM,IAAI,EAACyE,KAAK,CAAE,CAAEC,KAAK,CAAE/C,QAAQ,CAAG,CAAC,CAAG,SAAS,CAAG,SAAU,CAAE,CAAA0D,QAAA,CAC1D7F,cAAc,CAAC8F,IAAI,CAACC,GAAG,CAAC5D,QAAQ,CAAC,CAAC,CAC/B,CAAC,cACP/B,KAAA,CAACI,IAAI,EAACwF,IAAI,CAAC,WAAW,CAACf,KAAK,CAAE,CAAEgB,QAAQ,CAAE,EAAG,CAAE,CAAAJ,QAAA,EAC5C9B,MAAM,CAAC3B,kBAAkB,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAE2B,MAAM,CAAC3B,kBAAkB,CAAC8D,OAAO,CAAC,CAAC,CAAC,CAAC,GAClF,EAAM,CAAC,EACF,CAEX,CAAC,CACD,CACE9B,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,EACT,CAAC,CACD,CACElB,KAAK,CAAE,KAAK,CACZgB,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,GAAG,CACVG,MAAM,CAAGtD,QAAgB,eACvBjC,IAAA,CAACM,IAAI,EAACyE,KAAK,CAAE,CAAEC,KAAK,CAAE/C,QAAQ,CAAG,CAAC,CAAG,SAAS,CAAG,SAAU,CAAE,CAAA0D,QAAA,CAC1D7F,cAAc,CAAC8F,IAAI,CAACC,GAAG,CAAC5D,QAAQ,CAAC,CAAC,CAC/B,CAEV,CAAC,CACD,CACEiC,KAAK,CAAE,MAAM,CACbgB,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbG,QAAQ,CAAE,IACZ,CAAC,CACD,CACEpB,KAAK,CAAE,IAAI,CACXgB,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,EAAE,CACTG,MAAM,CAAGnD,MAAc,EAAK,CAC1B,KAAM,CAAAoE,SAAS,CAAG,CAChBC,OAAO,CAAE,CAAEzB,KAAK,CAAE,QAAQ,CAAE0B,IAAI,CAAE,KAAM,CAAC,CACzCC,aAAa,CAAE,CAAE3B,KAAK,CAAE,MAAM,CAAE0B,IAAI,CAAE,KAAM,CAAC,CAC7CE,QAAQ,CAAE,CAAE5B,KAAK,CAAE,OAAO,CAAE0B,IAAI,CAAE,KAAM,CAC1C,CAAC,CACD,KAAM,CAAAG,MAAM,CAAGL,SAAS,CAACpE,MAAM,CAA2B,CAC1D,mBAAOpC,IAAA,CAACnB,GAAG,EAACmG,KAAK,CAAE6B,MAAM,CAAC7B,KAAM,CAAAW,QAAA,CAAEkB,MAAM,CAACH,IAAI,CAAM,CAAC,CACtD,CACF,CAAC,CACF,CAED,KAAM,CAAAI,iBAAiB,CAAGA,CAAA,gBACxB5G,KAAA,QAAAyF,QAAA,EAEG3C,YAAY,CAACI,UAAU,CAAG,CAAC,eAC1BpD,IAAA,CAACpB,KAAK,EACJK,OAAO,CAAC,sCAAQ,CAChB8H,WAAW,iBAAAC,MAAA,CAAQhE,YAAY,CAACI,UAAU,mIAAyB,CACnE0C,IAAI,CAAC,OAAO,CACZmB,QAAQ,MACRC,QAAQ,MACRnC,KAAK,CAAE,CAAEoC,YAAY,CAAE,EAAG,CAAE,CAC7B,CACF,cAGDjH,KAAA,CAAC1B,GAAG,EAAC4I,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACrC,KAAK,CAAE,CAAEoC,YAAY,CAAE,EAAG,CAAE,CAAAxB,QAAA,eACjD3F,IAAA,CAACvB,GAAG,EAAC4I,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA3B,QAAA,cACjB3F,IAAA,CAAClC,IAAI,EAAA6H,QAAA,cACH3F,IAAA,CAACtB,SAAS,EACRwF,KAAK,CAAC,0BAAM,CACZqD,KAAK,CAAEvE,YAAY,CAACC,cAAe,CACnCuE,MAAM,cAAExH,IAAA,CAACN,cAAc,GAAE,CAAE,CAC3B+H,UAAU,CAAE,CAAEzC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNhF,IAAA,CAACvB,GAAG,EAAC4I,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA3B,QAAA,cACjBzF,KAAA,CAACpC,IAAI,EAAA6H,QAAA,eACH3F,IAAA,CAACtB,SAAS,EACRwF,KAAK,CAAC,0BAAM,CACZqD,KAAK,CAAEvE,YAAY,CAACE,WAAY,CAChCuE,UAAU,CAAE,CAAEzC,KAAK,CAAE,SAAU,CAAE,CACjC0C,MAAM,cACJ1H,IAAA,CAAChB,OAAO,EAACkF,KAAK,kBAAA8C,MAAA,CAAS,CAAEhE,YAAY,CAACE,WAAW,CAAGF,YAAY,CAACC,cAAc,CAAI,GAAG,EAAE+C,OAAO,CAAC,CAAC,CAAC,KAAI,CAAAL,QAAA,cACpG3F,IAAA,CAACT,mBAAmB,EAACwF,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC7C,CACV,CACF,CAAC,cACFhF,IAAA,CAACrB,QAAQ,EACPgJ,OAAO,CAAG3E,YAAY,CAACE,WAAW,CAAGF,YAAY,CAACC,cAAc,CAAI,GAAI,CACxEyC,IAAI,CAAC,OAAO,CACZkC,QAAQ,CAAE,KAAM,CAChBC,WAAW,CAAC,SAAS,CACtB,CAAC,EACE,CAAC,CACJ,CAAC,cACN7H,IAAA,CAACvB,GAAG,EAAC4I,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA3B,QAAA,cACjBzF,KAAA,CAACpC,IAAI,EAAA6H,QAAA,eACH3F,IAAA,CAACtB,SAAS,EACRwF,KAAK,CAAC,0BAAM,CACZqD,KAAK,CAAEvE,YAAY,CAACG,YAAa,CACjCsE,UAAU,CAAE,CAAEzC,KAAK,CAAE,SAAU,CAAE,CACjC0C,MAAM,cACJ1H,IAAA,CAAChB,OAAO,EAACkF,KAAK,kBAAA8C,MAAA,CAAS,CAAEhE,YAAY,CAACG,YAAY,CAAGH,YAAY,CAACC,cAAc,CAAI,GAAG,EAAE+C,OAAO,CAAC,CAAC,CAAC,KAAI,CAAAL,QAAA,cACrG3F,IAAA,CAACV,eAAe,EAACyF,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACzC,CACV,CACF,CAAC,cACFhF,IAAA,CAACrB,QAAQ,EACPgJ,OAAO,CAAG3E,YAAY,CAACG,YAAY,CAAGH,YAAY,CAACC,cAAc,CAAI,GAAI,CACzEyC,IAAI,CAAC,OAAO,CACZkC,QAAQ,CAAE,KAAM,CAChBC,WAAW,CAAC,SAAS,CACtB,CAAC,EACE,CAAC,CACJ,CAAC,cACN7H,IAAA,CAACvB,GAAG,EAAC4I,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA3B,QAAA,cACjBzF,KAAA,CAACpC,IAAI,EAAA6H,QAAA,eACH3F,IAAA,CAACtB,SAAS,EACRwF,KAAK,CAAC,0BAAM,CACZqD,KAAK,CAAEvE,YAAY,CAACI,UAAW,CAC/BqE,UAAU,CAAE,CAAEzC,KAAK,CAAE,SAAU,CAAE,CACjC0C,MAAM,cACJ1H,IAAA,CAAChB,OAAO,EAACkF,KAAK,kBAAA8C,MAAA,CAAS,CAAEhE,YAAY,CAACI,UAAU,CAAGJ,YAAY,CAACC,cAAc,CAAI,GAAG,EAAE+C,OAAO,CAAC,CAAC,CAAC,KAAI,CAAAL,QAAA,cACnG3F,IAAA,CAACR,mBAAmB,EAACuF,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC7C,CACV,CACF,CAAC,cACFhF,IAAA,CAACrB,QAAQ,EACPgJ,OAAO,CAAG3E,YAAY,CAACI,UAAU,CAAGJ,YAAY,CAACC,cAAc,CAAI,GAAI,CACvEyC,IAAI,CAAC,OAAO,CACZkC,QAAQ,CAAE,KAAM,CAChBC,WAAW,CAAC,SAAS,CACtB,CAAC,EACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGN7H,IAAA,CAAClC,IAAI,EAACoG,KAAK,CAAC,sCAAQ,CAAC4D,KAAK,cACxB5H,KAAA,CAACjC,KAAK,EAAA0H,QAAA,eACJ3F,IAAA,CAAChC,MAAM,EAACoI,IAAI,cAAEpG,IAAA,CAACP,YAAY,GAAE,CAAE,CAAC4G,OAAO,CAAE1B,iBAAkB,CAAAgB,QAAA,CAAC,0BAE5D,CAAQ,CAAC,cACT3F,IAAA,CAAChC,MAAM,EAAC8H,IAAI,CAAC,SAAS,CAACM,IAAI,cAAEpG,IAAA,CAACd,YAAY,GAAE,CAAE,CAACmH,OAAO,CAAE3C,SAAU,CAAAiC,QAAA,CAAC,sCAEnE,CAAQ,CAAC,EACJ,CACR,CAAAA,QAAA,cACC3F,IAAA,CAACjC,KAAK,EACJgK,OAAO,CAAE9C,mBAAoB,CAC7B+C,UAAU,CAAEvG,iBAAkB,CAC9BwG,MAAM,CAAC,IAAI,CACXxH,OAAO,CAAEA,OAAQ,CACjByH,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,KAAK,CAAE5G,iBAAiB,CAAC6G,MAAM,CAC/BC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAGL,KAAK,YAAArB,MAAA,CAAUqB,KAAK,uBAClC,CAAE,CACH,CAAC,CACE,CAAC,EACJ,CACN,CAED,KAAM,CAAAM,iBAAiB,CAAGA,CAAA,gBACxB3I,IAAA,QAAA2F,QAAA,cACE3F,IAAA,CAAClC,IAAI,EAACoG,KAAK,CAAC,sCAAQ,CAAC4D,KAAK,cACxB9H,IAAA,CAAC/B,KAAK,EAAA0H,QAAA,cACJ3F,IAAA,CAAChC,MAAM,EAACoI,IAAI,cAAEpG,IAAA,CAACX,cAAc,GAAE,CAAE,CAAAsG,QAAA,CAAC,sCAElC,CAAQ,CAAC,CACJ,CACR,CAAAA,QAAA,cACC3F,IAAA,CAACjC,KAAK,EACJgK,OAAO,CAAExB,eAAgB,CACzByB,UAAU,CAAEvF,aAAc,CAC1BwF,MAAM,CAAC,IAAI,CACXxH,OAAO,CAAEA,OAAQ,CACjB2H,UAAU,CAAE,CACVC,KAAK,CAAE5F,aAAa,CAAC6F,MAAM,CAC3BC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAGL,KAAK,YAAArB,MAAA,CAAUqB,KAAK,uBAClC,CAAE,CACH,CAAC,CACE,CAAC,CACJ,CACN,CAED,mBACEnI,KAAA,QAAAyF,QAAA,eACEzF,KAAA,CAACpC,IAAI,EAAA6H,QAAA,eACHzF,KAAA,CAAC1B,GAAG,EAACoK,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAAC9D,KAAK,CAAE,CAAEoC,YAAY,CAAE,EAAG,CAAE,CAAAxB,QAAA,eACtE3F,IAAA,CAACvB,GAAG,EAAAkH,QAAA,cACF3F,IAAA,CAACK,KAAK,EAACyI,KAAK,CAAE,CAAE,CAAC/D,KAAK,CAAE,CAAEgE,MAAM,CAAE,CAAE,CAAE,CAAApD,QAAA,CAAC,sCAEvC,CAAO,CAAC,CACL,CAAC,cACN3F,IAAA,CAACvB,GAAG,EAAAkH,QAAA,cACFzF,KAAA,CAACjC,KAAK,EAAA0H,QAAA,eACJ3F,IAAA,CAACG,MAAM,EACL6I,WAAW,CAAC,wDAAW,CACvBC,UAAU,MACVlE,KAAK,CAAE,CAAEK,KAAK,CAAE,GAAI,CAAE,CACtBmC,KAAK,CAAE5G,UAAW,CAClBuI,QAAQ,CAAGC,CAAC,EAAKvI,aAAa,CAACuI,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE,CAChD,CAAC,cACFvH,IAAA,CAAC7B,MAAM,EACL6K,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVlE,KAAK,CAAE,CAAEK,KAAK,CAAE,GAAI,CAAE,CACtBmC,KAAK,CAAE1G,gBAAiB,CACxBqI,QAAQ,CAAEpI,mBAAoB,CAC9BuI,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,MAAM,CAAE/B,KAAK,CAAE,MAAO,CAAC,CAChC,CAAE+B,KAAK,CAAE,MAAM,CAAE/B,KAAK,CAAE,MAAO,CAAC,CAChC,CAAE+B,KAAK,CAAE,MAAM,CAAE/B,KAAK,CAAE,MAAO,CAAC,CAChC,CACH,CAAC,cACFvH,IAAA,CAACI,WAAW,EACVmH,KAAK,CAAExG,SAAU,CACjBmI,QAAQ,CAAGK,KAAK,EAAKvI,YAAY,CAACuI,KAAmC,CAAE,CACvExE,KAAK,CAAE,CAAEK,KAAK,CAAE,GAAI,CAAE,CACvB,CAAC,cACFpF,IAAA,CAAChC,MAAM,EAACoI,IAAI,cAAEpG,IAAA,CAACX,cAAc,GAAE,CAAE,CAAAsG,QAAA,CAAC,cAElC,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAENzF,KAAA,CAACnB,IAAI,EAACyK,SAAS,CAAEnI,SAAU,CAAC6H,QAAQ,CAAE5H,YAAa,CAAAqE,QAAA,eACjD3F,IAAA,CAACO,OAAO,EAACkJ,GAAG,CAAC,0BAAM,CAAA9D,QAAA,CAChBmB,iBAAiB,CAAC,CAAC,EADE,WAEf,CAAC,cACV9G,IAAA,CAACO,OAAO,EAACkJ,GAAG,CAAC,0BAAM,CAAA9D,QAAA,CAChBgD,iBAAiB,CAAC,CAAC,EADE,WAEf,CAAC,EACN,CAAC,EACH,CAAC,cAGP3I,IAAA,CAAC3B,KAAK,EACJ6F,KAAK,CAAE/C,aAAa,CAAG,QAAQ,CAAG,QAAS,CAC3CuI,IAAI,CAAEzI,cAAe,CACrB0I,QAAQ,CAAEA,CAAA,GAAMzI,iBAAiB,CAAC,KAAK,CAAE,CACzC0I,MAAM,CAAE,IAAK,CACbxE,KAAK,CAAE,GAAI,CAAAO,QAAA,cAEXzF,KAAA,CAAC5B,IAAI,EACHiD,IAAI,CAAEA,IAAK,CACXsI,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAExF,YAAa,CAAAqB,QAAA,eAEvBzF,KAAA,CAAC1B,GAAG,EAAC4I,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACd3F,IAAA,CAACvB,GAAG,EAACsL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ3F,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,cAAc,CACnBX,KAAK,CAAC,0BAAM,CACZY,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA0G,QAAA,cAEhD3F,IAAA,CAAC9B,KAAK,EAAC8K,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNhJ,IAAA,CAACvB,GAAG,EAACsL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ3F,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,cAAc,CACnBX,KAAK,CAAC,0BAAM,CACZY,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA0G,QAAA,cAEhD3F,IAAA,CAAC9B,KAAK,EAAC8K,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,EACH,CAAC,cAEN9I,KAAA,CAAC1B,GAAG,EAAC4I,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACd3F,IAAA,CAACvB,GAAG,EAACsL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ3F,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,eAAe,CACpBX,KAAK,CAAC,0BAAM,CAAA3D,QAAA,cAEZ3F,IAAA,CAAC9B,KAAK,EAAC8K,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACNhJ,IAAA,CAACvB,GAAG,EAACsL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ3F,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,MAAM,CACXX,KAAK,CAAC,cAAI,CACVY,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElL,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA0G,QAAA,cAE9C3F,IAAA,CAAC7B,MAAM,EACL6K,WAAW,CAAC,gCAAO,CACnBK,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,KAAK,CAAE/B,KAAK,CAAE,KAAM,CAAC,CAC9B,CAAE+B,KAAK,CAAE,GAAG,CAAE/B,KAAK,CAAE,GAAI,CAAC,CAC1B,CAAE+B,KAAK,CAAE,IAAI,CAAE/B,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAE+B,KAAK,CAAE,KAAK,CAAE/B,KAAK,CAAE,KAAM,CAAC,CAC9B,CACH,CAAC,CACO,CAAC,CACT,CAAC,EACH,CAAC,cAENrH,KAAA,CAAC1B,GAAG,EAAC4I,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACd3F,IAAA,CAACvB,GAAG,EAACsL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ3F,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,cAAc,CACnBX,KAAK,CAAC,0BAAM,CACZY,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA0G,QAAA,cAEhD3F,IAAA,CAACzB,WAAW,EACVyK,WAAW,CAAC,4CAAS,CACrBjE,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CACzBgF,GAAG,CAAE,CAAE,CACPC,SAAS,CAAE,CAAE,CACbC,SAAS,CAAG/C,KAAK,EAAK,QAAAP,MAAA,CAAKO,KAAK,EAAGgD,OAAO,CAAC,uBAAuB,CAAE,GAAG,CAAE,CACzEC,MAAM,CAAIjD,KAAyB,EAAK,CACtC,KAAM,CAAAkD,MAAM,CAAGC,UAAU,CAACnD,KAAK,CAAEgD,OAAO,CAAC,YAAY,CAAE,EAAE,CAAC,CAAC,CAC3D,MAAO,CAAAI,KAAK,CAACF,MAAM,CAAC,CAAG,CAAC,CAAGA,MAAM,CACnC,CAAU,CACX,CAAC,CACO,CAAC,CACT,CAAC,cACNzK,IAAA,CAACvB,GAAG,EAACsL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ3F,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,UAAU,CACfX,KAAK,CAAC,0BAAM,CACZY,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA0G,QAAA,cAEhD3F,IAAA,CAAC7B,MAAM,EACL6K,WAAW,CAAC,4CAAS,CACrBK,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,MAAM,CAAE/B,KAAK,CAAE,MAAO,CAAC,CAChC,CAAE+B,KAAK,CAAE,MAAM,CAAE/B,KAAK,CAAE,MAAO,CAAC,CAChC,CAAE+B,KAAK,CAAE,MAAM,CAAE/B,KAAK,CAAE,MAAO,CAAC,CAChC,CACH,CAAC,CACO,CAAC,CACT,CAAC,EACH,CAAC,cAENrH,KAAA,CAAC1B,GAAG,EAAC4I,MAAM,CAAE,EAAG,CAAAzB,QAAA,eACd3F,IAAA,CAACvB,GAAG,EAACsL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ3F,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,UAAU,CACfX,KAAK,CAAC,gCAAO,CAAA3D,QAAA,cAEb3F,IAAA,CAAC9B,KAAK,EAAC8K,WAAW,CAAC,kDAAU,CAAE,CAAC,CACvB,CAAC,CACT,CAAC,cACNhJ,IAAA,CAACvB,GAAG,EAACsL,IAAI,CAAE,EAAG,CAAApE,QAAA,cACZ3F,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,eAAe,CACpBX,KAAK,CAAC,0BAAM,CACZY,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAElL,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA0G,QAAA,cAEhD3F,IAAA,CAAC5B,UAAU,EAAC2G,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAC/B,CAAC,CACT,CAAC,EACH,CAAC,cAENpF,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EACRC,IAAI,CAAC,YAAY,CACjBX,KAAK,CAAC,0BAAM,CAAA3D,QAAA,cAEZ3F,IAAA,CAAC5B,UAAU,EAAC2G,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAC/B,CAAC,cAEZpF,IAAA,CAAC1B,IAAI,CAAC0L,IAAI,EAACjF,KAAK,CAAE,CAAEoC,YAAY,CAAE,CAAC,CAAEyD,SAAS,CAAE,OAAQ,CAAE,CAAAjF,QAAA,cACxDzF,KAAA,CAACjC,KAAK,EAAA0H,QAAA,eACJ3F,IAAA,CAAChC,MAAM,EAACqI,OAAO,CAAEA,CAAA,GAAMnF,iBAAiB,CAAC,KAAK,CAAE,CAAAyE,QAAA,CAAC,cAEjD,CAAQ,CAAC,cACT3F,IAAA,CAAChC,MAAM,EAAC8H,IAAI,CAAC,SAAS,CAAC+E,QAAQ,CAAC,QAAQ,CAACpK,OAAO,CAAEA,OAAQ,CAAAkF,QAAA,CACvDxE,aAAa,CAAG,IAAI,CAAG,IAAI,CACtB,CAAC,EACJ,CAAC,CACC,CAAC,EACR,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}