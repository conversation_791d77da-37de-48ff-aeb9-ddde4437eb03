import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Checkbox,
  Divider,
  Typography,
  Statistic,
  Progress,
  Tabs,
  Alert,
  message,
} from 'antd';
import {
  SettingOutlined,
  CalculatorOutlined,
  ShoppingCartOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExportOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { formatCurrency, formatDate } from '../../utils/format';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface OptimizationResult {
  id: string;
  materialCode: string;
  materialName: string;
  originalQuantity: number;
  optimizedQuantity: number;
  packageSize: number;
  moq: number;
  unitPrice: number;
  originalCost: number;
  optimizedCost: number;
  wasteAmount: number;
  wasteType: string;
  supplier: string;
  leadTime: number;
  urgency: string;
}

interface PurchaseSuggestion {
  id: string;
  supplier: string;
  totalValue: number;
  itemCount: number;
  wasteAmount: number;
  wastePercentage: number;
  urgency: string;
  leadTime: number;
  items: OptimizationResult[];
}

const PurchaseOptimizationPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [optimizationModalVisible, setOptimizationModalVisible] = useState(false);
  const [optimizationForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    // TODO: 加载优化数据
  };

  const handleOptimize = async () => {
    try {
      const values = await optimizationForm.validateFields();
      setLoading(true);
      
      // TODO: 调用优化API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setOptimizationModalVisible(false);
      message.success('采购优化完成');
    } catch (error) {
      message.error('采购优化失败');
    } finally {
      setLoading(false);
    }
  };

  // 模拟优化结果数据
  const mockOptimizationData = {
    overview: {
      totalItems: 45,
      optimizedItems: 38,
      totalSavings: 125000,
      wasteReduction: 18.5,
      mergedOrders: 12,
      optimizationRate: 84.4,
    },
    results: [
      {
        id: '1',
        materialCode: 'ANT-MAIN-001',
        materialName: '5G主天线单元',
        originalQuantity: 75,
        optimizedQuantity: 80,
        packageSize: 10,
        moq: 50,
        unitPrice: 1500,
        originalCost: 112500,
        optimizedCost: 120000,
        wasteAmount: 7500,
        wasteType: '包装浪费',
        supplier: '华为技术',
        leadTime: 14,
        urgency: 'HIGH',
      },
      {
        id: '2',
        materialCode: 'RF-AMP-001',
        materialName: 'RF功率放大器',
        originalQuantity: 35,
        optimizedQuantity: 50,
        packageSize: 5,
        moq: 50,
        unitPrice: 2500,
        originalCost: 87500,
        optimizedCost: 125000,
        wasteAmount: 37500,
        wasteType: 'MOQ浪费',
        supplier: '中兴通讯',
        leadTime: 21,
        urgency: 'HIGH',
      },
      {
        id: '3',
        materialCode: 'CABLE-001',
        materialName: '同轴电缆',
        originalQuantity: 280,
        optimizedQuantity: 300,
        packageSize: 100,
        moq: 200,
        unitPrice: 25,
        originalCost: 7000,
        optimizedCost: 7500,
        wasteAmount: 500,
        wasteType: '包装浪费',
        supplier: '电缆供应商',
        leadTime: 7,
        urgency: 'MEDIUM',
      },
    ],
    suggestions: [
      {
        id: '1',
        supplier: '华为技术',
        totalValue: 245000,
        itemCount: 8,
        wasteAmount: 12500,
        wastePercentage: 5.1,
        urgency: 'HIGH',
        leadTime: 14,
        items: [],
      },
      {
        id: '2',
        supplier: '中兴通讯',
        totalValue: 187500,
        itemCount: 5,
        wasteAmount: 37500,
        wastePercentage: 20.0,
        urgency: 'HIGH',
        leadTime: 21,
        items: [],
      },
      {
        id: '3',
        supplier: '电缆供应商',
        totalValue: 45000,
        itemCount: 12,
        wasteAmount: 2500,
        wastePercentage: 5.6,
        urgency: 'MEDIUM',
        leadTime: 7,
        items: [],
      },
    ],
  };

  const optimizationColumns = [
    {
      title: '物料编码',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: '物料名称',
      dataIndex: 'materialName',
      key: 'materialName',
      width: 200,
    },
    {
      title: '原始需求',
      dataIndex: 'originalQuantity',
      key: 'originalQuantity',
      width: 100,
      render: (value: number) => value.toLocaleString(),
    },
    {
      title: '优化数量',
      dataIndex: 'optimizedQuantity',
      key: 'optimizedQuantity',
      width: 100,
      render: (value: number, record: OptimizationResult) => (
        <span style={{ color: value > record.originalQuantity ? '#faad14' : '#52c41a' }}>
          {value.toLocaleString()}
        </span>
      ),
    },
    {
      title: '包装规格',
      dataIndex: 'packageSize',
      key: 'packageSize',
      width: 100,
      render: (value: number) => `${value}/包`,
    },
    {
      title: 'MOQ',
      dataIndex: 'moq',
      key: 'moq',
      width: 80,
      render: (value: number) => value.toLocaleString(),
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (value: number) => formatCurrency(value),
    },
    {
      title: '原始成本',
      dataIndex: 'originalCost',
      key: 'originalCost',
      width: 120,
      render: (value: number) => formatCurrency(value),
    },
    {
      title: '优化成本',
      dataIndex: 'optimizedCost',
      key: 'optimizedCost',
      width: 120,
      render: (value: number, record: OptimizationResult) => (
        <span style={{ color: value > record.originalCost ? '#ff4d4f' : '#52c41a' }}>
          {formatCurrency(value)}
        </span>
      ),
    },
    {
      title: '浪费金额',
      dataIndex: 'wasteAmount',
      key: 'wasteAmount',
      width: 120,
      render: (value: number) => (
        <span style={{ color: '#ff4d4f' }}>
          {formatCurrency(value)}
        </span>
      ),
    },
    {
      title: '浪费类型',
      dataIndex: 'wasteType',
      key: 'wasteType',
      width: 100,
      render: (type: string) => {
        const color = type === '包装浪费' ? 'orange' : type === 'MOQ浪费' ? 'red' : 'blue';
        return <Tag color={color}>{type}</Tag>;
      },
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 120,
    },
    {
      title: '交期',
      dataIndex: 'leadTime',
      key: 'leadTime',
      width: 80,
      render: (days: number) => `${days}天`,
    },
    {
      title: '紧急度',
      dataIndex: 'urgency',
      key: 'urgency',
      width: 80,
      render: (urgency: string) => {
        const color = urgency === 'HIGH' ? 'red' : urgency === 'MEDIUM' ? 'orange' : 'green';
        return <Tag color={color}>{urgency}</Tag>;
      },
    },
  ];

  const suggestionColumns = [
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 150,
    },
    {
      title: '采购金额',
      dataIndex: 'totalValue',
      key: 'totalValue',
      width: 120,
      render: (value: number) => formatCurrency(value),
    },
    {
      title: '物料数量',
      dataIndex: 'itemCount',
      key: 'itemCount',
      width: 80,
      render: (count: number) => `${count}项`,
    },
    {
      title: '浪费金额',
      dataIndex: 'wasteAmount',
      key: 'wasteAmount',
      width: 120,
      render: (value: number) => (
        <span style={{ color: '#ff4d4f' }}>
          {formatCurrency(value)}
        </span>
      ),
    },
    {
      title: '浪费率',
      dataIndex: 'wastePercentage',
      key: 'wastePercentage',
      width: 100,
      render: (percentage: number) => (
        <span style={{ color: percentage > 10 ? '#ff4d4f' : '#faad14' }}>
          {percentage.toFixed(1)}%
        </span>
      ),
    },
    {
      title: '紧急度',
      dataIndex: 'urgency',
      key: 'urgency',
      width: 80,
      render: (urgency: string) => {
        const color = urgency === 'HIGH' ? 'red' : urgency === 'MEDIUM' ? 'orange' : 'green';
        return <Tag color={color}>{urgency}</Tag>;
      },
    },
    {
      title: '交期',
      dataIndex: 'leadTime',
      key: 'leadTime',
      width: 80,
      render: (days: number) => `${days}天`,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: PurchaseSuggestion) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<ShoppingCartOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '生成采购订单',
                content: `确定为供应商 ${record.supplier} 生成采购订单吗？`,
                onOk: () => {
                  message.success('采购订单已生成');
                },
              });
            }}
          >
            生成订单
          </Button>
        </Space>
      ),
    },
  ];

  const renderOverviewTab = () => (
    <div>
      {/* 优化概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总物料项"
              value={mockOptimizationData.overview.totalItems}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已优化项"
              value={mockOptimizationData.overview.optimizedItems}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="节省金额"
              value={mockOptimizationData.overview.totalSavings}
              prefix="¥"
              valueStyle={{ color: '#52c41a' }}
              formatter={(value) => formatCurrency(Number(value))}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="浪费减少"
              value={mockOptimizationData.overview.wasteReduction}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 优化进度 */}
      <Card title="优化进度" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <div style={{ marginBottom: 16 }}>
              <Text strong>优化完成率</Text>
              <Progress
                percent={mockOptimizationData.overview.optimizationRate}
                status="active"
                strokeColor="#52c41a"
              />
            </div>
          </Col>
          <Col xs={24} md={12}>
            <div style={{ marginBottom: 16 }}>
              <Text strong>订单合并数</Text>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                {mockOptimizationData.overview.mergedOrders}
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 优化建议 */}
      <Card title="优化建议">
        <Alert
          message="优化建议"
          description={
            <ul>
              <li>建议合并华为技术和中兴通讯的订单，可减少15%的采购成本</li>
              <li>CABLE-001的包装规格可以调整，减少包装浪费</li>
              <li>RF-AMP-001的MOQ较高，建议寻找替代供应商</li>
              <li>建议设置紧急订单白名单，优先保证关键交付</li>
            </ul>
          }
          type="info"
          showIcon
        />
      </Card>
    </div>
  );

  const renderOptimizationTab = () => (
    <div>
      <Card title="优化结果详情">
        <Table
          columns={optimizationColumns}
          dataSource={mockOptimizationData.results}
          rowKey="id"
          scroll={{ x: 1400 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>
    </div>
  );

  const renderSuggestionTab = () => (
    <div>
      <Card title="采购建议">
        <Table
          columns={suggestionColumns}
          dataSource={mockOptimizationData.suggestions}
          rowKey="id"
          scroll={{ x: 1000 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>
    </div>
  );

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              采购优化
            </Title>
            <Text type="secondary">
              包装优化、MOQ处理和采购建议生成
            </Text>
          </Col>
          <Col>
            <Space>
              <Button icon={<ReloadOutlined />} onClick={loadData}>
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={() => setOptimizationModalVisible(true)}
              >
                开始优化
              </Button>
            </Space>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="优化概览" key="overview">
            {renderOverviewTab()}
          </TabPane>
          <TabPane tab="优化结果" key="optimization">
            {renderOptimizationTab()}
          </TabPane>
          <TabPane tab="采购建议" key="suggestion">
            {renderSuggestionTab()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 优化设置模态框 */}
      <Modal
        title="采购优化设置"
        open={optimizationModalVisible}
        onOk={handleOptimize}
        onCancel={() => setOptimizationModalVisible(false)}
        width={600}
        okText="开始优化"
        cancelText="取消"
        confirmLoading={loading}
      >
        <Form form={optimizationForm} layout="vertical">
          <Form.Item
            name="dateRange"
            label="优化周期"
            rules={[{ required: true, message: '请选择优化周期' }]}
          >
            <RangePicker
              style={{ width: '100%' }}
              placeholder={['开始日期', '结束日期']}
            />
          </Form.Item>

          <Divider orientation="left">优化策略</Divider>

          <Form.Item
            name="enablePackageOptimization"
            valuePropName="checked"
            initialValue={true}
          >
            <Checkbox>启用包装优化</Checkbox>
          </Form.Item>

          <Form.Item
            name="enableMOQOptimization"
            valuePropName="checked"
            initialValue={true}
          >
            <Checkbox>启用MOQ优化</Checkbox>
          </Form.Item>

          <Form.Item
            name="enableOrderMerging"
            valuePropName="checked"
            initialValue={true}
          >
            <Checkbox>启用订单合并</Checkbox>
          </Form.Item>

          <Form.Item
            name="enableWasteTracking"
            valuePropName="checked"
            initialValue={true}
          >
            <Checkbox>启用浪费归因跟踪</Checkbox>
          </Form.Item>

          <Divider orientation="left">优化参数</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="wasteThreshold"
                label="浪费阈值(%)"
                initialValue={10}
              >
                <InputNumber
                  min={0}
                  max={50}
                  style={{ width: '100%' }}
                  placeholder="浪费阈值"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="mergingWindow"
                label="合并时间窗口(天)"
                initialValue={30}
              >
                <InputNumber
                  min={1}
                  max={90}
                  style={{ width: '100%' }}
                  placeholder="合并时间窗口"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priorityLevel"
                label="优先级"
                initialValue="MEDIUM"
              >
                <Select placeholder="选择优先级">
                  <Select.Option value="HIGH">高</Select.Option>
                  <Select.Option value="MEDIUM">中</Select.Option>
                  <Select.Option value="LOW">低</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="urgentBypass"
                valuePropName="checked"
                initialValue={false}
              >
                <Checkbox>紧急订单绕过优化</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default PurchaseOptimizationPage;