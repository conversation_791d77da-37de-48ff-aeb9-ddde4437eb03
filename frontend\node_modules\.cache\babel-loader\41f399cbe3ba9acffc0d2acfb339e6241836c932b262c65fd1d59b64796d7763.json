{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\auth\\\\LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Form, Input, Button, Card, Typography, Space, Alert, Checkbox, Divider, Row, Col } from 'antd';\nimport { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { login, clearError } from '../../store/slices/authSlice';\nimport { ROUTES } from '../../constants';\nimport { LoadingSpinner } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst LoginPage = () => {\n  _s();\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const {\n    loading,\n    error,\n    isAuthenticated\n  } = useAppSelector(state => state.auth);\n\n  // 如果已经登录，重定向到目标页面或仪表板\n  useEffect(() => {\n    if (isAuthenticated) {\n      var _location$state, _location$state$from;\n      const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || ROUTES.DASHBOARD;\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate, location]);\n\n  // 清除错误信息\n  useEffect(() => {\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n\n  // 处理登录\n  const handleLogin = async values => {\n    try {\n      await dispatch(login({\n        username: values.username,\n        password: values.password\n      })).unwrap();\n\n      // 登录成功后会通过useEffect重定向\n    } catch (error) {\n      // 使用新的错误处理器\n      errorHandler.handleError({\n        type: ErrorType.BUSINESS,\n        message: error.message || '登录失败，请检查用户名和密码',\n        details: error,\n        timestamp: Date.now()\n      });\n    }\n  };\n\n  // 演示账号登录\n  const handleDemoLogin = role => {\n    const demoAccounts = {\n      admin: {\n        username: 'admin',\n        password: 'admin123'\n      },\n      bom_manager: {\n        username: 'bom_manager',\n        password: 'bom123'\n      },\n      sales_pmc: {\n        username: 'sales_pmc',\n        password: 'sales123'\n      },\n      purchase_manager: {\n        username: 'purchase_manager',\n        password: 'purchase123'\n      },\n      warehouse_manager: {\n        username: 'warehouse_manager',\n        password: 'warehouse123'\n      },\n      finance_manager: {\n        username: 'finance_manager',\n        password: 'finance123'\n      },\n      service_technician: {\n        username: 'service_tech',\n        password: 'service123'\n      },\n      operator: {\n        username: 'operator',\n        password: 'operator123'\n      }\n    };\n    const account = demoAccounts[role];\n    if (account) {\n      form.setFieldsValue(account);\n      handleLogin({\n        ...account,\n        remember: false\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      fullscreen: true,\n      overlay: true,\n      text: \"\\u6B63\\u5728\\u767B\\u5F55...\",\n      size: \"large\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '20px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [32, 32],\n        style: {\n          width: '100%',\n          maxWidth: 1200\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 12,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: 'white',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 1,\n              style: {\n                color: 'white',\n                marginBottom: 24\n              },\n              children: \"Link-BOM-S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 3,\n              style: {\n                color: 'white',\n                fontWeight: 'normal',\n                marginBottom: 32\n              },\n              children: \"\\u5929\\u7EBF\\u884C\\u4E1ABOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              size: \"large\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: 'rgba(255,255,255,0.9)',\n                  fontSize: 16\n                },\n                children: \"\\u4E13\\u4E3A\\u5929\\u7EBF\\u884C\\u4E1A\\u5C0F\\u5FAE\\u5236\\u9020\\u4F01\\u4E1A\\u8BBE\\u8BA1\\u7684\\u7EFC\\u5408BOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'left',\n                  maxWidth: 400,\n                  margin: '0 auto'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: 'rgba(255,255,255,0.8)',\n                    display: 'block',\n                    marginBottom: 8\n                  },\n                  children: \"\\u2713 \\u6838\\u5FC3BOM\\uFF08150%\\uFF09+ \\u8BA2\\u5355\\u5FEB\\u901F\\u6D3E\\u751F\\uFF08100%\\uFF09\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: 'rgba(255,255,255,0.8)',\n                    display: 'block',\n                    marginBottom: 8\n                  },\n                  children: \"\\u2713 \\u6309\\u5355\\u5408\\u5355\\u91C7\\u8D2D + \\u5305\\u88C5/MOQ\\u53D6\\u6574\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: 'rgba(255,255,255,0.8)',\n                    display: 'block',\n                    marginBottom: 8\n                  },\n                  children: \"\\u2713 \\u957F\\u5EA6\\u578B\\u5207\\u5272\\u4F18\\u5316 + \\u4F59\\u6599\\u53F0\\u8D26\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: 'rgba(255,255,255,0.8)',\n                    display: 'block',\n                    marginBottom: 8\n                  },\n                  children: \"\\u2713 \\u670D\\u52A1BOM + \\u6210\\u672C\\u900F\\u660E\\u5EA6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            style: {\n              maxWidth: 400,\n              margin: '0 auto',\n              borderRadius: 12,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.1)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginBottom: 32\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 3,\n                style: {\n                  marginBottom: 8\n                },\n                children: \"\\u7528\\u6237\\u767B\\u5F55\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u8BF7\\u8F93\\u5165\\u60A8\\u7684\\u8D26\\u53F7\\u548C\\u5BC6\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 13\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              message: error,\n              type: \"error\",\n              showIcon: true,\n              style: {\n                marginBottom: 24\n              },\n              closable: true,\n              onClose: () => dispatch(clearError())\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              name: \"login\",\n              onFinish: handleLogin,\n              autoComplete: \"off\",\n              size: \"large\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"username\",\n                rules: [{\n                  required: true,\n                  message: '请输入用户名'\n                }, {\n                  min: 3,\n                  message: '用户名至少3个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 27\n                  }, this),\n                  placeholder: \"\\u7528\\u6237\\u540D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"password\",\n                rules: [{\n                  required: true,\n                  message: '请输入密码'\n                }, {\n                  min: 6,\n                  message: '密码至少6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                  prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 27\n                  }, this),\n                  placeholder: \"\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"remember\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  children: \"\\u8BB0\\u4F4F\\u6211\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  loading: loading,\n                  icon: /*#__PURE__*/_jsxDEV(LoginOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this),\n                  block: true,\n                  children: \"\\u767B\\u5F55\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              children: \"\\u6F14\\u793A\\u8D26\\u53F7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                gutter: [8, 8],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    block: true,\n                    onClick: () => handleDemoLogin('admin'),\n                    children: \"\\u7CFB\\u7EDF\\u7BA1\\u7406\\u5458\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    block: true,\n                    onClick: () => handleDemoLogin('bom_manager'),\n                    children: \"BOM\\u7BA1\\u7406\\u5458\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [8, 8],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    block: true,\n                    onClick: () => handleDemoLogin('sales_pmc'),\n                    children: \"\\u9500\\u552E/PMC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    block: true,\n                    onClick: () => handleDemoLogin('purchase_manager'),\n                    children: \"\\u91C7\\u8D2D\\u7ECF\\u7406\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [8, 8],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    block: true,\n                    onClick: () => handleDemoLogin('warehouse_manager'),\n                    children: \"\\u4ED3\\u5E93\\u7ECF\\u7406\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    block: true,\n                    onClick: () => handleDemoLogin('finance_manager'),\n                    children: \"\\u8D22\\u52A1\\u7ECF\\u7406\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [8, 8],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    block: true,\n                    onClick: () => handleDemoLogin('service_technician'),\n                    children: \"\\u670D\\u52A1\\u6280\\u672F\\u5458\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    block: true,\n                    onClick: () => handleDemoLogin('operator'),\n                    children: \"\\u64CD\\u4F5C\\u5458\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginTop: 24\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: 12\n                },\n                children: \"\\xA9 2024 Link-BOM-S. \\u5929\\u7EBF\\u884C\\u4E1ABOM\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(LoginPage, \"uBQjfJ27qGa1B09nmWXAOPNNFes=\", false, function () {\n  return [Form.useForm, useNavigate, useLocation, useAppDispatch, useAppSelector];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useLocation", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "Space", "<PERSON><PERSON>", "Checkbox", "Divider", "Row", "Col", "UserOutlined", "LockOutlined", "LoginOutlined", "useAppDispatch", "useAppSelector", "login", "clearError", "ROUTES", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "LoginPage", "_s", "form", "useForm", "navigate", "location", "dispatch", "loading", "error", "isAuthenticated", "state", "auth", "_location$state", "_location$state$from", "from", "pathname", "DASHBOARD", "replace", "handleLogin", "values", "username", "password", "unwrap", "handleError", "type", "BUSINESS", "message", "details", "timestamp", "Date", "now", "handleDemoLogin", "role", "demoAccounts", "admin", "bom_manager", "sales_pmc", "purchase_manager", "warehouse_manager", "finance_manager", "service_technician", "operator", "account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remember", "children", "fullscreen", "overlay", "text", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "minHeight", "background", "display", "alignItems", "justifyContent", "padding", "gutter", "width", "max<PERSON><PERSON><PERSON>", "xs", "lg", "color", "textAlign", "level", "marginBottom", "fontWeight", "direction", "fontSize", "margin", "borderRadius", "boxShadow", "showIcon", "closable", "onClose", "name", "onFinish", "autoComplete", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "Password", "valuePropName", "htmlType", "icon", "block", "span", "onClick", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/auth/LoginPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Form,\n  Input,\n  Button,\n  Card,\n  Typography,\n  Space,\n  Alert,\n  Checkbox,\n  Divider,\n  Row,\n  Col,\n} from 'antd';\nimport {\n  UserOutlined,\n  LockOutlined,\n  LoginOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { login, clearError } from '../../store/slices/authSlice';\nimport { ROUTES } from '../../constants';\nimport { LoadingSpinner } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title, Text } = Typography;\n\ninterface LoginFormData {\n  username: string;\n  password: string;\n  remember: boolean;\n}\n\nconst LoginPage: React.FC = () => {\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useAppDispatch();\n  const { loading, error, isAuthenticated } = useAppSelector(state => state.auth);\n\n  // 如果已经登录，重定向到目标页面或仪表板\n  useEffect(() => {\n    if (isAuthenticated) {\n      const from = (location.state as any)?.from?.pathname || ROUTES.DASHBOARD;\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, navigate, location]);\n\n  // 清除错误信息\n  useEffect(() => {\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n\n  // 处理登录\n  const handleLogin = async (values: LoginFormData) => {\n    try {\n      await dispatch(login({\n        username: values.username,\n        password: values.password,\n      })).unwrap();\n      \n      // 登录成功后会通过useEffect重定向\n    } catch (error: any) {\n      // 使用新的错误处理器\n      errorHandler.handleError({\n        type: ErrorType.BUSINESS,\n        message: error.message || '登录失败，请检查用户名和密码',\n        details: error,\n        timestamp: Date.now()\n      });\n    }\n  };\n\n  // 演示账号登录\n  const handleDemoLogin = (role: string) => {\n    const demoAccounts = {\n      admin: { username: 'admin', password: 'admin123' },\n      bom_manager: { username: 'bom_manager', password: 'bom123' },\n      sales_pmc: { username: 'sales_pmc', password: 'sales123' },\n      purchase_manager: { username: 'purchase_manager', password: 'purchase123' },\n      warehouse_manager: { username: 'warehouse_manager', password: 'warehouse123' },\n      finance_manager: { username: 'finance_manager', password: 'finance123' },\n      service_technician: { username: 'service_tech', password: 'service123' },\n      operator: { username: 'operator', password: 'operator123' },\n    };\n\n    const account = demoAccounts[role as keyof typeof demoAccounts];\n    if (account) {\n      form.setFieldsValue(account);\n      handleLogin({ ...account, remember: false });\n    }\n  };\n\n  return (\n    <>\n      {loading && (\n        <LoadingSpinner\n          fullscreen\n          overlay\n          text=\"正在登录...\"\n          size=\"large\"\n        />\n      )}\n      <div style={{\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '20px',\n      }}>\n      <Row gutter={[32, 32]} style={{ width: '100%', maxWidth: 1200 }}>\n        {/* 左侧：系统介绍 */}\n        <Col xs={24} lg={12}>\n          <div style={{ color: 'white', textAlign: 'center' }}>\n            <Title level={1} style={{ color: 'white', marginBottom: 24 }}>\n              Link-BOM-S\n            </Title>\n            <Title level={3} style={{ color: 'white', fontWeight: 'normal', marginBottom: 32 }}>\n              天线行业BOM管理系统\n            </Title>\n            <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n              <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: 16 }}>\n                专为天线行业小微制造企业设计的综合BOM管理系统\n              </Text>\n              <div style={{ textAlign: 'left', maxWidth: 400, margin: '0 auto' }}>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 核心BOM（150%）+ 订单快速派生（100%）\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 按单合单采购 + 包装/MOQ取整\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 长度型切割优化 + 余料台账\n                </Text>\n                <Text style={{ color: 'rgba(255,255,255,0.8)', display: 'block', marginBottom: 8 }}>\n                  ✓ 服务BOM + 成本透明度\n                </Text>\n              </div>\n            </Space>\n          </div>\n        </Col>\n\n        {/* 右侧：登录表单 */}\n        <Col xs={24} lg={12}>\n          <Card\n            style={{\n              maxWidth: 400,\n              margin: '0 auto',\n              borderRadius: 12,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n            }}\n          >\n            <div style={{ textAlign: 'center', marginBottom: 32 }}>\n              <Title level={3} style={{ marginBottom: 8 }}>\n                用户登录\n              </Title>\n              <Text type=\"secondary\">\n                请输入您的账号和密码\n              </Text>\n            </div>\n\n            {error && (\n              <Alert\n                message={error}\n                type=\"error\"\n                showIcon\n                style={{ marginBottom: 24 }}\n                closable\n                onClose={() => dispatch(clearError())}\n              />\n            )}\n\n            <Form\n              form={form}\n              name=\"login\"\n              onFinish={handleLogin}\n              autoComplete=\"off\"\n              size=\"large\"\n            >\n              <Form.Item\n                name=\"username\"\n                rules={[\n                  { required: true, message: '请输入用户名' },\n                  { min: 3, message: '用户名至少3个字符' },\n                ]}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  placeholder=\"用户名\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"password\"\n                rules={[\n                  { required: true, message: '请输入密码' },\n                  { min: 6, message: '密码至少6个字符' },\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"密码\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"remember\" valuePropName=\"checked\">\n                <Checkbox>记住我</Checkbox>\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<LoginOutlined />}\n                  block\n                >\n                  登录\n                </Button>\n              </Form.Item>\n            </Form>\n\n            <Divider>演示账号</Divider>\n\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"small\">\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('admin')}\n                  >\n                    系统管理员\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('bom_manager')}\n                  >\n                    BOM管理员\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('sales_pmc')}\n                  >\n                    销售/PMC\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('purchase_manager')}\n                  >\n                    采购经理\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('warehouse_manager')}\n                  >\n                    仓库经理\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('finance_manager')}\n                  >\n                    财务经理\n                  </Button>\n                </Col>\n              </Row>\n              <Row gutter={[8, 8]}>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('service_technician')}\n                  >\n                    服务技术员\n                  </Button>\n                </Col>\n                <Col span={12}>\n                  <Button\n                    size=\"small\"\n                    block\n                    onClick={() => handleDemoLogin('operator')}\n                  >\n                    操作员\n                  </Button>\n                </Col>\n              </Row>\n            </Space>\n\n            <div style={{ textAlign: 'center', marginTop: 24 }}>\n              <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                © 2024 Link-BOM-S. 天线行业BOM管理系统\n              </Text>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n    </>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,aAAa,QACR,mBAAmB;AAE1B,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,KAAK,EAAEC,UAAU,QAAQ,8BAA8B;AAChE,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,EAAEC,SAAS,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGvB,UAAU;AAQlC,MAAMwB,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,CAAC,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGpB,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEqB,OAAO;IAAEC,KAAK;IAAEC;EAAgB,CAAC,GAAGtB,cAAc,CAACuB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;;EAE/E;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIwC,eAAe,EAAE;MAAA,IAAAG,eAAA,EAAAC,oBAAA;MACnB,MAAMC,IAAI,GAAG,EAAAF,eAAA,GAACP,QAAQ,CAACK,KAAK,cAAAE,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBE,IAAI,cAAAD,oBAAA,uBAA7BA,oBAAA,CAA+BE,QAAQ,KAAIzB,MAAM,CAAC0B,SAAS;MACxEZ,QAAQ,CAACU,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACR,eAAe,EAAEL,QAAQ,EAAEC,QAAQ,CAAC,CAAC;;EAEzC;EACApC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXqC,QAAQ,CAACjB,UAAU,CAAC,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACiB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMY,WAAW,GAAG,MAAOC,MAAqB,IAAK;IACnD,IAAI;MACF,MAAMb,QAAQ,CAAClB,KAAK,CAAC;QACnBgC,QAAQ,EAAED,MAAM,CAACC,QAAQ;QACzBC,QAAQ,EAAEF,MAAM,CAACE;MACnB,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;;MAEZ;IACF,CAAC,CAAC,OAAOd,KAAU,EAAE;MACnB;MACAhB,YAAY,CAAC+B,WAAW,CAAC;QACvBC,IAAI,EAAE/B,SAAS,CAACgC,QAAQ;QACxBC,OAAO,EAAElB,KAAK,CAACkB,OAAO,IAAI,gBAAgB;QAC1CC,OAAO,EAAEnB,KAAK;QACdoB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,IAAY,IAAK;IACxC,MAAMC,YAAY,GAAG;MACnBC,KAAK,EAAE;QAAEd,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAW,CAAC;MAClDc,WAAW,EAAE;QAAEf,QAAQ,EAAE,aAAa;QAAEC,QAAQ,EAAE;MAAS,CAAC;MAC5De,SAAS,EAAE;QAAEhB,QAAQ,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAW,CAAC;MAC1DgB,gBAAgB,EAAE;QAAEjB,QAAQ,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAc,CAAC;MAC3EiB,iBAAiB,EAAE;QAAElB,QAAQ,EAAE,mBAAmB;QAAEC,QAAQ,EAAE;MAAe,CAAC;MAC9EkB,eAAe,EAAE;QAAEnB,QAAQ,EAAE,iBAAiB;QAAEC,QAAQ,EAAE;MAAa,CAAC;MACxEmB,kBAAkB,EAAE;QAAEpB,QAAQ,EAAE,cAAc;QAAEC,QAAQ,EAAE;MAAa,CAAC;MACxEoB,QAAQ,EAAE;QAAErB,QAAQ,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAc;IAC5D,CAAC;IAED,MAAMqB,OAAO,GAAGT,YAAY,CAACD,IAAI,CAA8B;IAC/D,IAAIU,OAAO,EAAE;MACXxC,IAAI,CAACyC,cAAc,CAACD,OAAO,CAAC;MAC5BxB,WAAW,CAAC;QAAE,GAAGwB,OAAO;QAAEE,QAAQ,EAAE;MAAM,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,oBACEjD,OAAA,CAAAE,SAAA;IAAAgD,QAAA,GACGtC,OAAO,iBACNZ,OAAA,CAACJ,cAAc;MACbuD,UAAU;MACVC,OAAO;MACPC,IAAI,EAAC,6BAAS;MACdC,IAAI,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CACF,eACD1D,OAAA;MAAK2D,KAAK,EAAE;QACVC,SAAS,EAAE,OAAO;QAClBC,UAAU,EAAE,mDAAmD;QAC/DC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,OAAO,EAAE;MACX,CAAE;MAAAf,QAAA,eACFlD,OAAA,CAACd,GAAG;QAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACP,KAAK,EAAE;UAAEQ,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAK,CAAE;QAAAlB,QAAA,gBAE9DlD,OAAA,CAACb,GAAG;UAACkF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAApB,QAAA,eAClBlD,OAAA;YAAK2D,KAAK,EAAE;cAAEY,KAAK,EAAE,OAAO;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAtB,QAAA,gBAClDlD,OAAA,CAACG,KAAK;cAACsE,KAAK,EAAE,CAAE;cAACd,KAAK,EAAE;gBAAEY,KAAK,EAAE,OAAO;gBAAEG,YAAY,EAAE;cAAG,CAAE;cAAAxB,QAAA,EAAC;YAE9D;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1D,OAAA,CAACG,KAAK;cAACsE,KAAK,EAAE,CAAE;cAACd,KAAK,EAAE;gBAAEY,KAAK,EAAE,OAAO;gBAAEI,UAAU,EAAE,QAAQ;gBAAED,YAAY,EAAE;cAAG,CAAE;cAAAxB,QAAA,EAAC;YAEpF;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1D,OAAA,CAAClB,KAAK;cAAC8F,SAAS,EAAC,UAAU;cAACtB,IAAI,EAAC,OAAO;cAACK,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAO,CAAE;cAAAjB,QAAA,gBAChElD,OAAA,CAACI,IAAI;gBAACuD,KAAK,EAAE;kBAAEY,KAAK,EAAE,uBAAuB;kBAAEM,QAAQ,EAAE;gBAAG,CAAE;gBAAA3B,QAAA,EAAC;cAE/D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP1D,OAAA;gBAAK2D,KAAK,EAAE;kBAAEa,SAAS,EAAE,MAAM;kBAAEJ,QAAQ,EAAE,GAAG;kBAAEU,MAAM,EAAE;gBAAS,CAAE;gBAAA5B,QAAA,gBACjElD,OAAA,CAACI,IAAI;kBAACuD,KAAK,EAAE;oBAAEY,KAAK,EAAE,uBAAuB;oBAAET,OAAO,EAAE,OAAO;oBAAEY,YAAY,EAAE;kBAAE,CAAE;kBAAAxB,QAAA,EAAC;gBAEpF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACP1D,OAAA,CAACI,IAAI;kBAACuD,KAAK,EAAE;oBAAEY,KAAK,EAAE,uBAAuB;oBAAET,OAAO,EAAE,OAAO;oBAAEY,YAAY,EAAE;kBAAE,CAAE;kBAAAxB,QAAA,EAAC;gBAEpF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACP1D,OAAA,CAACI,IAAI;kBAACuD,KAAK,EAAE;oBAAEY,KAAK,EAAE,uBAAuB;oBAAET,OAAO,EAAE,OAAO;oBAAEY,YAAY,EAAE;kBAAE,CAAE;kBAAAxB,QAAA,EAAC;gBAEpF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACP1D,OAAA,CAACI,IAAI;kBAACuD,KAAK,EAAE;oBAAEY,KAAK,EAAE,uBAAuB;oBAAET,OAAO,EAAE,OAAO;oBAAEY,YAAY,EAAE;kBAAE,CAAE;kBAAAxB,QAAA,EAAC;gBAEpF;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1D,OAAA,CAACb,GAAG;UAACkF,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAApB,QAAA,eAClBlD,OAAA,CAACpB,IAAI;YACH+E,KAAK,EAAE;cACLS,QAAQ,EAAE,GAAG;cACbU,MAAM,EAAE,QAAQ;cAChBC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAE;YAAA9B,QAAA,gBAEFlD,OAAA;cAAK2D,KAAK,EAAE;gBAAEa,SAAS,EAAE,QAAQ;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAxB,QAAA,gBACpDlD,OAAA,CAACG,KAAK;gBAACsE,KAAK,EAAE,CAAE;gBAACd,KAAK,EAAE;kBAAEe,YAAY,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,EAAC;cAE7C;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1D,OAAA,CAACI,IAAI;gBAACyB,IAAI,EAAC,WAAW;gBAAAqB,QAAA,EAAC;cAEvB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEL7C,KAAK,iBACJb,OAAA,CAACjB,KAAK;cACJgD,OAAO,EAAElB,KAAM;cACfgB,IAAI,EAAC,OAAO;cACZoD,QAAQ;cACRtB,KAAK,EAAE;gBAAEe,YAAY,EAAE;cAAG,CAAE;cAC5BQ,QAAQ;cACRC,OAAO,EAAEA,CAAA,KAAMxE,QAAQ,CAACjB,UAAU,CAAC,CAAC;YAAE;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACF,eAED1D,OAAA,CAACvB,IAAI;cACH8B,IAAI,EAAEA,IAAK;cACX6E,IAAI,EAAC,OAAO;cACZC,QAAQ,EAAE9D,WAAY;cACtB+D,YAAY,EAAC,KAAK;cAClBhC,IAAI,EAAC,OAAO;cAAAJ,QAAA,gBAEZlD,OAAA,CAACvB,IAAI,CAAC8G,IAAI;gBACRH,IAAI,EAAC,UAAU;gBACfI,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE1D,OAAO,EAAE;gBAAS,CAAC,EACrC;kBAAE2D,GAAG,EAAE,CAAC;kBAAE3D,OAAO,EAAE;gBAAY,CAAC,CAChC;gBAAAmB,QAAA,eAEFlD,OAAA,CAACtB,KAAK;kBACJiH,MAAM,eAAE3F,OAAA,CAACZ,YAAY;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBkC,WAAW,EAAC;gBAAK;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZ1D,OAAA,CAACvB,IAAI,CAAC8G,IAAI;gBACRH,IAAI,EAAC,UAAU;gBACfI,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE1D,OAAO,EAAE;gBAAQ,CAAC,EACpC;kBAAE2D,GAAG,EAAE,CAAC;kBAAE3D,OAAO,EAAE;gBAAW,CAAC,CAC/B;gBAAAmB,QAAA,eAEFlD,OAAA,CAACtB,KAAK,CAACmH,QAAQ;kBACbF,MAAM,eAAE3F,OAAA,CAACX,YAAY;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBkC,WAAW,EAAC;gBAAI;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZ1D,OAAA,CAACvB,IAAI,CAAC8G,IAAI;gBAACH,IAAI,EAAC,UAAU;gBAACU,aAAa,EAAC,SAAS;gBAAA5C,QAAA,eAChDlD,OAAA,CAAChB,QAAQ;kBAAAkE,QAAA,EAAC;gBAAG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAEZ1D,OAAA,CAACvB,IAAI,CAAC8G,IAAI;gBAAArC,QAAA,eACRlD,OAAA,CAACrB,MAAM;kBACLkD,IAAI,EAAC,SAAS;kBACdkE,QAAQ,EAAC,QAAQ;kBACjBnF,OAAO,EAAEA,OAAQ;kBACjBoF,IAAI,eAAEhG,OAAA,CAACV,aAAa;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBuC,KAAK;kBAAA/C,QAAA,EACN;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEP1D,OAAA,CAACf,OAAO;cAAAiE,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAEvB1D,OAAA,CAAClB,KAAK;cAAC8F,SAAS,EAAC,UAAU;cAACjB,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAO,CAAE;cAACb,IAAI,EAAC,OAAO;cAAAJ,QAAA,gBAChElD,OAAA,CAACd,GAAG;gBAACgF,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;gBAAAhB,QAAA,gBAClBlD,OAAA,CAACb,GAAG;kBAAC+G,IAAI,EAAE,EAAG;kBAAAhD,QAAA,eACZlD,OAAA,CAACrB,MAAM;oBACL2E,IAAI,EAAC,OAAO;oBACZ2C,KAAK;oBACLE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,OAAO,CAAE;oBAAAc,QAAA,EACzC;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN1D,OAAA,CAACb,GAAG;kBAAC+G,IAAI,EAAE,EAAG;kBAAAhD,QAAA,eACZlD,OAAA,CAACrB,MAAM;oBACL2E,IAAI,EAAC,OAAO;oBACZ2C,KAAK;oBACLE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,aAAa,CAAE;oBAAAc,QAAA,EAC/C;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1D,OAAA,CAACd,GAAG;gBAACgF,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;gBAAAhB,QAAA,gBAClBlD,OAAA,CAACb,GAAG;kBAAC+G,IAAI,EAAE,EAAG;kBAAAhD,QAAA,eACZlD,OAAA,CAACrB,MAAM;oBACL2E,IAAI,EAAC,OAAO;oBACZ2C,KAAK;oBACLE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,WAAW,CAAE;oBAAAc,QAAA,EAC7C;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN1D,OAAA,CAACb,GAAG;kBAAC+G,IAAI,EAAE,EAAG;kBAAAhD,QAAA,eACZlD,OAAA,CAACrB,MAAM;oBACL2E,IAAI,EAAC,OAAO;oBACZ2C,KAAK;oBACLE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,kBAAkB,CAAE;oBAAAc,QAAA,EACpD;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1D,OAAA,CAACd,GAAG;gBAACgF,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;gBAAAhB,QAAA,gBAClBlD,OAAA,CAACb,GAAG;kBAAC+G,IAAI,EAAE,EAAG;kBAAAhD,QAAA,eACZlD,OAAA,CAACrB,MAAM;oBACL2E,IAAI,EAAC,OAAO;oBACZ2C,KAAK;oBACLE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,mBAAmB,CAAE;oBAAAc,QAAA,EACrD;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN1D,OAAA,CAACb,GAAG;kBAAC+G,IAAI,EAAE,EAAG;kBAAAhD,QAAA,eACZlD,OAAA,CAACrB,MAAM;oBACL2E,IAAI,EAAC,OAAO;oBACZ2C,KAAK;oBACLE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,iBAAiB,CAAE;oBAAAc,QAAA,EACnD;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1D,OAAA,CAACd,GAAG;gBAACgF,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;gBAAAhB,QAAA,gBAClBlD,OAAA,CAACb,GAAG;kBAAC+G,IAAI,EAAE,EAAG;kBAAAhD,QAAA,eACZlD,OAAA,CAACrB,MAAM;oBACL2E,IAAI,EAAC,OAAO;oBACZ2C,KAAK;oBACLE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,oBAAoB,CAAE;oBAAAc,QAAA,EACtD;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN1D,OAAA,CAACb,GAAG;kBAAC+G,IAAI,EAAE,EAAG;kBAAAhD,QAAA,eACZlD,OAAA,CAACrB,MAAM;oBACL2E,IAAI,EAAC,OAAO;oBACZ2C,KAAK;oBACLE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAAC,UAAU,CAAE;oBAAAc,QAAA,EAC5C;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAER1D,OAAA;cAAK2D,KAAK,EAAE;gBAAEa,SAAS,EAAE,QAAQ;gBAAE4B,SAAS,EAAE;cAAG,CAAE;cAAAlD,QAAA,eACjDlD,OAAA,CAACI,IAAI;gBAACyB,IAAI,EAAC,WAAW;gBAAC8B,KAAK,EAAE;kBAAEkB,QAAQ,EAAE;gBAAG,CAAE;gBAAA3B,QAAA,EAAC;cAEhD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAACpD,EAAA,CAhSID,SAAmB;EAAA,QACR5B,IAAI,CAAC+B,OAAO,EACVjC,WAAW,EACXC,WAAW,EACXe,cAAc,EACaC,cAAc;AAAA;AAAA6G,EAAA,GALtDhG,SAAmB;AAkSzB,eAAeA,SAAS;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}