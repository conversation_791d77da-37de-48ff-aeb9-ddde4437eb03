{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useNavigate}from'react-router-dom';import{Table,Button,Space,Input,Select,Card,Tag,message,Modal,Form,Typography,Row,Col,Tooltip,Dropdown,Statistic,Checkbox}from'antd';import*as XLSX from'xlsx';import{PlusOutlined,EditOutlined,DeleteOutlined,EyeOutlined,CopyOutlined,LockOutlined,UnlockOutlined,MoreOutlined,ExportOutlined,ImportOutlined,DollarOutlined,CalendarOutlined}from'@ant-design/icons';import{useAppDispatch,useAppSelector}from'../../hooks/redux';import{fetchOrderBOMs,deleteOrderBOM,freezeOrderBOM}from'../../store/slices/bomSlice';import{ROUTES,ORDER_STATUS}from'../../constants';import{formatDate,formatCurrency}from'../../utils';import{ConfirmDialog}from'../../components';import{errorHandler,ErrorType}from'../../utils/errorHandler';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const{Search}=Input;const OrderBOMListPage=()=>{const navigate=useNavigate();const dispatch=useAppDispatch();const{orderBOMs,loading,pagination}=useAppSelector(state=>state.bom);const[searchKeyword,setSearchKeyword]=useState('');const[statusFilter,setStatusFilter]=useState('');const[customerFilter,setCustomerFilter]=useState('');const[exportModalVisible,setExportModalVisible]=useState(false);const[exportFormat,setExportFormat]=useState('excel');const[exportFields,setExportFields]=useState(['orderNumber','customerName','coreBOMVersion','status','totalCost','deliveryDate']);const[copyModalVisible,setCopyModalVisible]=useState(false);const[copyingOrder,setCopyingOrder]=useState(null);const[copyForm]=Form.useForm();useEffect(()=>{loadData();},[pagination.current,pagination.pageSize,searchKeyword,statusFilter,customerFilter]);const loadData=()=>{dispatch(fetchOrderBOMs({page:pagination.current,pageSize:pagination.pageSize,keyword:searchKeyword}));};const handleSearch=value=>{setSearchKeyword(value);};const handleStatusFilter=value=>{setStatusFilter(value);};const handleCustomerFilter=value=>{setCustomerFilter(value);};const handleCreate=()=>{navigate(ROUTES.ORDER_BOM_CREATE);};const handleDerive=()=>{// 显示核心BOM选择对话框\nModal.info({title:'选择核心BOM',content:'请先选择要派生的核心BOM',onOk:()=>{navigate('/bom/core');// 跳转到核心BOM列表选择\n}});};const handleEdit=record=>{navigate(\"/bom/order/edit/\".concat(record.id));};const handleView=record=>{navigate(ROUTES.ORDER_BOM_VIEW.replace(':id',record.id));};const handleDelete=async record=>{ConfirmDialog.confirm({title:'确认删除',content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u4EE5\\u4E0B\\u8BA2\\u5355BOM\\u5417\\uFF1F\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BA2\\u5355\\u53F7\\uFF1A\"}),record.orderNumber]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5BA2\\u6237\\u540D\\u79F0\\uFF1A\"}),record.customerName]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6838\\u5FC3BOM\\uFF1A\"}),record.coreBOMVersion]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#ff4d4f',marginTop:12},children:\"\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\uFF01\"})]}),type:'warning',onConfirm:async()=>{try{await dispatch(deleteOrderBOM(record.id)).unwrap();message.success('删除成功');loadData();}catch(error){errorHandler.handleError({type:ErrorType.BUSINESS,message:error.message||'删除失败',details:error});}}});};const handleFreeze=async record=>{const isActive=record.status!==ORDER_STATUS.FROZEN;const action=isActive?'冻结':'解冻';ConfirmDialog.confirm({title:\"\\u786E\\u8BA4\".concat(action),content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u786E\\u5B9A\\u8981\",action,\"\\u4EE5\\u4E0B\\u8BA2\\u5355BOM\\u5417\\uFF1F\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BA2\\u5355\\u53F7\\uFF1A\"}),record.orderNumber]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5BA2\\u6237\\u540D\\u79F0\\uFF1A\"}),record.customerName]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6838\\u5FC3BOM\\uFF1A\"}),record.coreBOMVersion]}),isActive&&/*#__PURE__*/_jsx(\"p\",{style:{color:'#faad14',marginTop:12},children:\"\\u51BB\\u7ED3\\u540E\\u8BE5\\u8BA2\\u5355BOM\\u5C06\\u65E0\\u6CD5\\u88AB\\u7F16\\u8F91\\uFF01\"})]}),type:isActive?'warning':'info',onConfirm:async()=>{try{await dispatch(freezeOrderBOM(record.id)).unwrap();message.success(\"\".concat(action,\"\\u6210\\u529F\"));loadData();}catch(error){errorHandler.handleError({type:ErrorType.BUSINESS,message:error.message||\"\".concat(action,\"\\u5931\\u8D25\"),details:error});}}});};const handleExport=record=>{setExportModalVisible(true);};const executeExport=()=>{try{// 准备导出数据\nconst exportData=mockData.map(order=>{const data={};if(exportFields.includes('orderNumber'))data['订单号']=order.orderNumber;if(exportFields.includes('customerName'))data['客户名称']=order.customerName;if(exportFields.includes('coreBOMVersion'))data['核心BOM']=order.coreBOMVersion;if(exportFields.includes('status'))data['状态']=getStatusText(order.status);if(exportFields.includes('totalCost'))data['总成本']=order.totalCost;if(exportFields.includes('estimatedMargin'))data['预估毛利']=\"\".concat((order.estimatedMargin*100).toFixed(1),\"%\");if(exportFields.includes('deliveryDate'))data['交期']=formatDate(order.deliveryDate);if(exportFields.includes('createdAt'))data['创建时间']=formatDate(order.createdAt);return data;});// 创建工作簿\nconst ws=XLSX.utils.json_to_sheet(exportData);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'订单BOM列表');// 下载文件\nconst fileName=\"\\u8BA2\\u5355BOM\\u5217\\u8868_\".concat(new Date().toISOString().split('T')[0],\".\").concat(exportFormat==='excel'?'xlsx':'csv');XLSX.writeFile(wb,fileName);message.success('导出成功');setExportModalVisible(false);}catch(error){message.error('导出失败');}};const handleCopy=record=>{setCopyingOrder(record);copyForm.setFieldsValue({orderNumber:\"\".concat(record.orderNumber,\"-COPY\"),customerName:record.customerName,deliveryDate:null});setCopyModalVisible(true);};const executeCopy=async()=>{try{const values=await copyForm.validateFields();// TODO: 实现复制API调用\nmessage.success('复制成功');setCopyModalVisible(false);loadData();}catch(error){message.error('复制失败');}};const getStatusColor=status=>{switch(status){case ORDER_STATUS.DRAFT:return'default';case ORDER_STATUS.CONFIRMED:return'processing';case ORDER_STATUS.FROZEN:return'success';case ORDER_STATUS.CANCELLED:return'error';default:return'default';}};const getStatusText=status=>{switch(status){case ORDER_STATUS.DRAFT:return'草稿';case ORDER_STATUS.CONFIRMED:return'已确认';case ORDER_STATUS.FROZEN:return'已冻结';case ORDER_STATUS.CANCELLED:return'已取消';default:return status;}};const getActionMenuItems=record=>[{key:'copy',icon:/*#__PURE__*/_jsx(CopyOutlined,{}),label:'复制',onClick:()=>handleCopy(record)},{key:'export',icon:/*#__PURE__*/_jsx(ExportOutlined,{}),label:'导出',onClick:()=>handleExport(record)},{key:'freeze',icon:record.status===ORDER_STATUS.FROZEN?/*#__PURE__*/_jsx(UnlockOutlined,{}):/*#__PURE__*/_jsx(LockOutlined,{}),label:record.status===ORDER_STATUS.FROZEN?'解冻':'冻结',onClick:()=>handleFreeze(record),disabled:record.status===ORDER_STATUS.DRAFT}];const columns=[{title:'订单号',dataIndex:'orderNumber',key:'orderNumber',width:120,render:(text,record)=>/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:()=>handleView(record),children:text})},{title:'客户名称',dataIndex:'customerName',key:'customerName',ellipsis:true},{title:'核心BOM',dataIndex:'coreBOMId',key:'coreBOMId',width:120,render:(coreBOMId,record)=>/*#__PURE__*/_jsx(\"span\",{children:record.coreBOMVersion})},{title:'状态',dataIndex:'status',key:'status',width:80,render:status=>/*#__PURE__*/_jsx(Tag,{color:getStatusColor(status),children:getStatusText(status)})},{title:'总成本',dataIndex:'totalCost',key:'totalCost',width:100,render:cost=>formatCurrency(cost)},{title:'预估毛利',dataIndex:'estimatedMargin',key:'estimatedMargin',width:80,render:margin=>\"\".concat((margin*100).toFixed(1),\"%\")},{title:'交期',dataIndex:'deliveryDate',key:'deliveryDate',width:100,render:date=>formatDate(date)},{title:'创建时间',dataIndex:'createdAt',key:'createdAt',width:120,render:date=>formatDate(date)},{title:'操作',key:'action',width:150,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record),disabled:record.status===ORDER_STATUS.FROZEN})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>handleDelete(record),disabled:record.status!==ORDER_STATUS.DRAFT})}),/*#__PURE__*/_jsx(Dropdown,{menu:{items:getActionMenuItems(record)},trigger:['click'],children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:/*#__PURE__*/_jsx(MoreOutlined,{})})})]})}];// 模拟数据\nconst mockData=[{id:'1',orderNumber:'ORD-2024-001',customerName:'华为技术有限公司',customerConfig:{frequency:'5G',power:100,antenna_type:'directional'},coreBOMId:'1',coreBOMVersion:'ANT-5G-001 V1.0',items:[],status:'CONFIRMED',totalCost:125000,estimatedMargin:0.25,deliveryDate:'2024-02-15',createdBy:'sales_pmc',createdAt:'2024-01-15T00:00:00Z',confirmedAt:'2024-01-16T00:00:00Z'},{id:'2',orderNumber:'ORD-2024-002',customerName:'中兴通讯股份有限公司',customerConfig:{frequency:'4G',power:80,antenna_type:'omnidirectional'},coreBOMId:'2',coreBOMVersion:'ANT-4G-002 V2.1',items:[],status:'FROZEN',totalCost:98000,estimatedMargin:0.30,deliveryDate:'2024-02-20',createdBy:'sales_pmc',createdAt:'2024-01-16T00:00:00Z',confirmedAt:'2024-01-17T00:00:00Z',frozenAt:'2024-01-18T00:00:00Z'},{id:'3',orderNumber:'ORD-2024-003',customerName:'大唐移动通信设备有限公司',customerConfig:{frequency:'5G',power:120,antenna_type:'smart'},coreBOMId:'1',coreBOMVersion:'ANT-5G-001 V1.0',items:[],status:'DRAFT',totalCost:156000,estimatedMargin:0.22,deliveryDate:'2024-03-01',createdBy:'sales_pmc',createdAt:'2024-01-17T00:00:00Z'}];// 统计数据\nconst stats={total:mockData.length,draft:mockData.filter(item=>item.status===ORDER_STATUS.DRAFT).length,confirmed:mockData.filter(item=>item.status===ORDER_STATUS.CONFIRMED).length,frozen:mockData.filter(item=>item.status===ORDER_STATUS.FROZEN).length,totalValue:mockData.reduce((sum,item)=>sum+item.totalCost,0)};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u8BA2\\u5355\\u6570\",value:stats.total,prefix:/*#__PURE__*/_jsx(CalendarOutlined,{}),valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8349\\u7A3F\",value:stats.draft,valueStyle:{color:'#faad14'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u786E\\u8BA4\",value:stats.confirmed,valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u4EF7\\u503C\",value:stats.totalValue,prefix:/*#__PURE__*/_jsx(DollarOutlined,{}),formatter:value=>formatCurrency(Number(value)),valueStyle:{color:'#722ed1'}})})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Title,{level:4,style:{margin:0},children:\"\\u8BA2\\u5355BOM\\u7BA1\\u7406\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ImportOutlined,{}),children:\"\\u5BFC\\u5165\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),onClick:()=>handleExport(),children:\"\\u6279\\u91CF\\u5BFC\\u51FA\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleDerive,children:\"\\u6D3E\\u751F\\u8BA2\\u5355BOM\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleCreate,children:\"\\u624B\\u52A8\\u521B\\u5EFA\"})]})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:8,md:6,children:/*#__PURE__*/_jsx(Search,{placeholder:\"\\u641C\\u7D22\\u8BA2\\u5355\\u53F7\\u6216\\u5BA2\\u6237\\u540D\\u79F0\",allowClear:true,onSearch:handleSearch,style:{width:'100%'}})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:8,md:6,children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u72B6\\u6001\\u7B5B\\u9009\",allowClear:true,style:{width:'100%'},onChange:handleStatusFilter,options:[{label:'草稿',value:ORDER_STATUS.DRAFT},{label:'已确认',value:ORDER_STATUS.CONFIRMED},{label:'已冻结',value:ORDER_STATUS.FROZEN},{label:'已取消',value:ORDER_STATUS.CANCELLED}]})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:8,md:6,children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u5BA2\\u6237\\u7B5B\\u9009\",allowClear:true,style:{width:'100%'},onChange:handleCustomerFilter,options:[{label:'华为技术',value:'华为技术有限公司'},{label:'中兴通讯',value:'中兴通讯股份有限公司'},{label:'大唐移动',value:'大唐移动通信设备有限公司'},{label:'爱立信',value:'爱立信（中国）通信有限公司'}]})})]}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:mockData,loading:loading,rowKey:\"id\",scroll:{x:1200},pagination:{current:pagination.current,pageSize:pagination.pageSize,total:pagination.total,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\")}})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u5BFC\\u51FA\\u8BA2\\u5355BOM\\u6570\\u636E\",open:exportModalVisible,onOk:executeExport,onCancel:()=>setExportModalVisible(false),okText:\"\\u5BFC\\u51FA\",cancelText:\"\\u53D6\\u6D88\",width:600,children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Typography.Text,{strong:true,children:\"\\u5BFC\\u51FA\\u683C\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Select,{value:exportFormat,onChange:setExportFormat,style:{width:120,marginLeft:8},children:[/*#__PURE__*/_jsx(Select.Option,{value:\"excel\",children:\"Excel\"}),/*#__PURE__*/_jsx(Select.Option,{value:\"csv\",children:\"CSV\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Typography.Text,{strong:true,children:\"\\u5BFC\\u51FA\\u5B57\\u6BB5\\uFF1A\"}),/*#__PURE__*/_jsx(Checkbox.Group,{value:exportFields,onChange:setExportFields,style:{marginTop:8},children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"orderNumber\",children:\"\\u8BA2\\u5355\\u53F7\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"customerName\",children:\"\\u5BA2\\u6237\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"coreBOMVersion\",children:\"\\u6838\\u5FC3BOM\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"status\",children:\"\\u72B6\\u6001\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"totalCost\",children:\"\\u603B\\u6210\\u672C\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"estimatedMargin\",children:\"\\u9884\\u4F30\\u6BDB\\u5229\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"deliveryDate\",children:\"\\u4EA4\\u671F\"})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Checkbox,{value:\"createdAt\",children:\"\\u521B\\u5EFA\\u65F6\\u95F4\"})})]})})]})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u590D\\u5236\\u8BA2\\u5355BOM\",open:copyModalVisible,onOk:executeCopy,onCancel:()=>setCopyModalVisible(false),okText:\"\\u786E\\u8BA4\\u590D\\u5236\",cancelText:\"\\u53D6\\u6D88\",width:500,children:/*#__PURE__*/_jsxs(Form,{form:copyForm,layout:\"vertical\",initialValues:{orderNumber:'',customerName:'',deliveryDate:null},children:[/*#__PURE__*/_jsx(Form.Item,{label:\"\\u65B0\\u8BA2\\u5355\\u53F7\",name:\"orderNumber\",rules:[{required:true,message:'请输入订单号'},{pattern:/^[A-Z0-9-]+$/,message:'订单号只能包含大写字母、数字和连字符'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u65B0\\u7684\\u8BA2\\u5355\\u53F7\"})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BA2\\u6237\\u540D\\u79F0\",name:\"customerName\",rules:[{required:true,message:'请输入客户名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u4EA4\\u671F\",name:\"deliveryDate\",rules:[{required:true,message:'请选择交期'}],children:/*#__PURE__*/_jsx(Input,{type:\"date\"})}),/*#__PURE__*/_jsx(Typography.Text,{type:\"secondary\",children:\"\\u6CE8\\u610F\\uFF1A\\u590D\\u5236\\u5C06\\u521B\\u5EFA\\u4E00\\u4E2A\\u65B0\\u7684\\u8BA2\\u5355BOM\\uFF0C\\u5305\\u542B\\u539F\\u8BA2\\u5355\\u7684\\u6240\\u6709\\u914D\\u7F6E\\u548C\\u7269\\u6599\\u6E05\\u5355\\uFF0C\\u4F46\\u72B6\\u6001\\u5C06\\u91CD\\u7F6E\\u4E3A\\u8349\\u7A3F\\u3002\"})]})})]});};export default OrderBOMListPage;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Card", "Tag", "message", "Modal", "Form", "Typography", "Row", "Col", "<PERSON><PERSON><PERSON>", "Dropdown", "Statistic", "Checkbox", "XLSX", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "LockOutlined", "UnlockOutlined", "MoreOutlined", "ExportOutlined", "ImportOutlined", "DollarOutlined", "CalendarOutlined", "useAppDispatch", "useAppSelector", "fetchOrderBOMs", "deleteOrderBOM", "freezeOrderBOM", "ROUTES", "ORDER_STATUS", "formatDate", "formatCurrency", "ConfirmDialog", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Search", "OrderBOMListPage", "navigate", "dispatch", "orderBOMs", "loading", "pagination", "state", "bom", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "customerFilter", "setCustomerFilter", "exportModalVisible", "setExportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "copyModalVisible", "setCopyModalVisible", "copyingOrder", "setCopyingOrder", "copyForm", "useForm", "loadData", "current", "pageSize", "page", "keyword", "handleSearch", "value", "handleStatusFilter", "handleCustomerFilter", "handleCreate", "ORDER_BOM_CREATE", "handleDerive", "info", "title", "content", "onOk", "handleEdit", "record", "concat", "id", "handleView", "ORDER_BOM_VIEW", "replace", "handleDelete", "confirm", "children", "orderNumber", "customerName", "coreBOMVersion", "style", "color", "marginTop", "type", "onConfirm", "unwrap", "success", "error", "handleError", "BUSINESS", "details", "handleFreeze", "isActive", "status", "FROZEN", "action", "handleExport", "executeExport", "exportData", "mockData", "map", "order", "data", "includes", "getStatusText", "totalCost", "<PERSON><PERSON><PERSON><PERSON>", "toFixed", "deliveryDate", "createdAt", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "fileName", "Date", "toISOString", "split", "writeFile", "handleCopy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "executeCopy", "values", "validateFields", "getStatusColor", "DRAFT", "CONFIRMED", "CANCELLED", "getActionMenuItems", "key", "icon", "label", "onClick", "disabled", "columns", "dataIndex", "width", "render", "text", "ellipsis", "coreBOMId", "cost", "margin", "date", "fixed", "_", "size", "danger", "menu", "items", "trigger", "customerConfig", "frequency", "power", "antenna_type", "created<PERSON>y", "confirmedAt", "frozenAt", "stats", "total", "length", "draft", "filter", "item", "confirmed", "frozen", "totalValue", "reduce", "sum", "gutter", "marginBottom", "xs", "sm", "prefix", "valueStyle", "formatter", "Number", "justify", "align", "level", "md", "placeholder", "allowClear", "onSearch", "onChange", "options", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "okText", "cancelText", "direction", "Text", "strong", "marginLeft", "Option", "Group", "span", "form", "layout", "initialValues", "<PERSON><PERSON>", "name", "rules", "required", "pattern"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/OrderBOMListPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Card,\n  Tag,\n  message,\n  Modal,\n  Form,\n  Typography,\n  Row,\n  Col,\n  Tooltip,\n  Dropdown,\n  MenuProps,\n  Progress,\n  Statistic,\n  Checkbox,\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  LockOutlined,\n  UnlockOutlined,\n  MoreOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  DollarOutlined,\n  CalendarOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\n\nimport { useAppDispatch, useAppSelector } from '../../hooks/redux';\nimport { fetchOrderBOMs, deleteOrderBOM, freezeOrderBOM } from '../../store/slices/bomSlice';\nimport { OrderBOM } from '../../types';\nimport { ROUTES, ORDER_STATUS } from '../../constants';\nimport { formatDate, formatCurrency } from '../../utils';\nimport { ConfirmDialog } from '../../components';\nimport { errorHandler, ErrorType } from '../../utils/errorHandler';\n\nconst { Title } = Typography;\nconst { Search } = Input;\n\nconst OrderBOMListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const { orderBOMs, loading, pagination } = useAppSelector(state => state.bom);\n\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n  const [customerFilter, setCustomerFilter] = useState<string>('');\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['orderNumber', 'customerName', 'coreBOMVersion', 'status', 'totalCost', 'deliveryDate']);\n  const [copyModalVisible, setCopyModalVisible] = useState(false);\n  const [copyingOrder, setCopyingOrder] = useState<OrderBOM | null>(null);\n  const [copyForm] = Form.useForm();\n\n  useEffect(() => {\n    loadData();\n  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter, customerFilter]);\n\n  const loadData = () => {\n    dispatch(fetchOrderBOMs({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      keyword: searchKeyword,\n    }));\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchKeyword(value);\n  };\n\n  const handleStatusFilter = (value: string) => {\n    setStatusFilter(value);\n  };\n\n  const handleCustomerFilter = (value: string) => {\n    setCustomerFilter(value);\n  };\n\n  const handleCreate = () => {\n    navigate(ROUTES.ORDER_BOM_CREATE);\n  };\n\n  const handleDerive = () => {\n    // 显示核心BOM选择对话框\n    Modal.info({\n      title: '选择核心BOM',\n      content: '请先选择要派生的核心BOM',\n      onOk: () => {\n        navigate('/bom/core'); // 跳转到核心BOM列表选择\n      },\n    });\n  };\n\n  const handleEdit = (record: OrderBOM) => {\n    navigate(`/bom/order/edit/${record.id}`);\n  };\n\n  const handleView = (record: OrderBOM) => {\n    navigate(ROUTES.ORDER_BOM_VIEW.replace(':id', record.id));\n  };\n\n  const handleDelete = async (record: OrderBOM) => {\n    ConfirmDialog.confirm({\n      title: '确认删除',\n      content: (\n        <div>\n          <p>确定要删除以下订单BOM吗？</p>\n          <p><strong>订单号：</strong>{record.orderNumber}</p>\n          <p><strong>客户名称：</strong>{record.customerName}</p>\n          <p><strong>核心BOM：</strong>{record.coreBOMVersion}</p>\n          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复，请谨慎操作！</p>\n        </div>\n      ),\n      type: 'warning',\n      onConfirm: async () => {\n        try {\n          await dispatch(deleteOrderBOM(record.id)).unwrap();\n          message.success('删除成功');\n          loadData();\n        } catch (error: any) {\n          errorHandler.handleError({\n            type: ErrorType.BUSINESS,\n            message: error.message || '删除失败',\n            details: error\n          });\n        }\n      }\n    });\n  };\n\n  const handleFreeze = async (record: OrderBOM) => {\n    const isActive = record.status !== ORDER_STATUS.FROZEN;\n    const action = isActive ? '冻结' : '解冻';\n    \n    ConfirmDialog.confirm({\n      title: `确认${action}`,\n      content: (\n        <div>\n          <p>确定要{action}以下订单BOM吗？</p>\n          <p><strong>订单号：</strong>{record.orderNumber}</p>\n          <p><strong>客户名称：</strong>{record.customerName}</p>\n          <p><strong>核心BOM：</strong>{record.coreBOMVersion}</p>\n          {isActive && (\n            <p style={{ color: '#faad14', marginTop: 12 }}>冻结后该订单BOM将无法被编辑！</p>\n          )}\n        </div>\n      ),\n      type: isActive ? 'warning' : 'info',\n      onConfirm: async () => {\n        try {\n          await dispatch(freezeOrderBOM(record.id)).unwrap();\n          message.success(`${action}成功`);\n          loadData();\n        } catch (error: any) {\n          errorHandler.handleError({\n            type: ErrorType.BUSINESS,\n            message: error.message || `${action}失败`,\n            details: error\n          });\n        }\n      }\n    });\n  };\n\n  const handleExport = (record?: OrderBOM) => {\n    setExportModalVisible(true);\n  };\n\n  const executeExport = () => {\n    try {\n      // 准备导出数据\n      const exportData = mockData.map(order => {\n        const data: any = {};\n        \n        if (exportFields.includes('orderNumber')) data['订单号'] = order.orderNumber;\n        if (exportFields.includes('customerName')) data['客户名称'] = order.customerName;\n        if (exportFields.includes('coreBOMVersion')) data['核心BOM'] = order.coreBOMVersion;\n        if (exportFields.includes('status')) data['状态'] = getStatusText(order.status);\n        if (exportFields.includes('totalCost')) data['总成本'] = order.totalCost;\n        if (exportFields.includes('estimatedMargin')) data['预估毛利'] = `${(order.estimatedMargin * 100).toFixed(1)}%`;\n        if (exportFields.includes('deliveryDate')) data['交期'] = formatDate(order.deliveryDate);\n        if (exportFields.includes('createdAt')) data['创建时间'] = formatDate(order.createdAt);\n        \n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, '订单BOM列表');\n\n      // 下载文件\n      const fileName = `订单BOM列表_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      \n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  const handleCopy = (record: OrderBOM) => {\n    setCopyingOrder(record);\n    copyForm.setFieldsValue({\n      orderNumber: `${record.orderNumber}-COPY`,\n      customerName: record.customerName,\n      deliveryDate: null,\n    });\n    setCopyModalVisible(true);\n  };\n\n  const executeCopy = async () => {\n    try {\n      const values = await copyForm.validateFields();\n      // TODO: 实现复制API调用\n      message.success('复制成功');\n      setCopyModalVisible(false);\n      loadData();\n    } catch (error) {\n      message.error('复制失败');\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case ORDER_STATUS.DRAFT: return 'default';\n      case ORDER_STATUS.CONFIRMED: return 'processing';\n      case ORDER_STATUS.FROZEN: return 'success';\n      case ORDER_STATUS.CANCELLED: return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case ORDER_STATUS.DRAFT: return '草稿';\n      case ORDER_STATUS.CONFIRMED: return '已确认';\n      case ORDER_STATUS.FROZEN: return '已冻结';\n      case ORDER_STATUS.CANCELLED: return '已取消';\n      default: return status;\n    }\n  };\n\n  const getActionMenuItems = (record: OrderBOM): MenuProps['items'] => [\n    {\n      key: 'copy',\n      icon: <CopyOutlined />,\n      label: '复制',\n      onClick: () => handleCopy(record),\n    },\n    {\n      key: 'export',\n      icon: <ExportOutlined />,\n      label: '导出',\n      onClick: () => handleExport(record),\n    },\n    {\n      key: 'freeze',\n      icon: record.status === ORDER_STATUS.FROZEN ? <UnlockOutlined /> : <LockOutlined />,\n      label: record.status === ORDER_STATUS.FROZEN ? '解冻' : '冻结',\n      onClick: () => handleFreeze(record),\n      disabled: record.status === ORDER_STATUS.DRAFT,\n    },\n  ];\n\n  const columns = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNumber',\n      key: 'orderNumber',\n      width: 120,\n      render: (text: string, record: OrderBOM) => (\n        <Button type=\"link\" onClick={() => handleView(record)}>\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: '客户名称',\n      dataIndex: 'customerName',\n      key: 'customerName',\n      ellipsis: true,\n    },\n    {\n      title: '核心BOM',\n      dataIndex: 'coreBOMId',\n      key: 'coreBOMId',\n      width: 120,\n      render: (coreBOMId: string, record: OrderBOM) => (\n        <span>{record.coreBOMVersion}</span>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '总成本',\n      dataIndex: 'totalCost',\n      key: 'totalCost',\n      width: 100,\n      render: (cost: number) => formatCurrency(cost),\n    },\n    {\n      title: '预估毛利',\n      dataIndex: 'estimatedMargin',\n      key: 'estimatedMargin',\n      width: 80,\n      render: (margin: number) => `${(margin * 100).toFixed(1)}%`,\n    },\n    {\n      title: '交期',\n      dataIndex: 'deliveryDate',\n      key: 'deliveryDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 120,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right' as const,\n      render: (_: any, record: OrderBOM) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n              disabled={record.status === ORDER_STATUS.FROZEN}\n            />\n          </Tooltip>\n          <Tooltip title=\"删除\">\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => handleDelete(record)}\n              disabled={record.status !== ORDER_STATUS.DRAFT}\n            />\n          </Tooltip>\n          <Dropdown\n            menu={{ items: getActionMenuItems(record) }}\n            trigger={['click']}\n          >\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </Space>\n      ),\n    },\n  ];\n\n  // 模拟数据\n  const mockData: OrderBOM[] = [\n    {\n      id: '1',\n      orderNumber: 'ORD-2024-001',\n      customerName: '华为技术有限公司',\n      customerConfig: { frequency: '5G', power: 100, antenna_type: 'directional' },\n      coreBOMId: '1',\n      coreBOMVersion: 'ANT-5G-001 V1.0',\n      items: [],\n      status: 'CONFIRMED',\n      totalCost: 125000,\n      estimatedMargin: 0.25,\n      deliveryDate: '2024-02-15',\n      createdBy: 'sales_pmc',\n      createdAt: '2024-01-15T00:00:00Z',\n      confirmedAt: '2024-01-16T00:00:00Z',\n    },\n    {\n      id: '2',\n      orderNumber: 'ORD-2024-002',\n      customerName: '中兴通讯股份有限公司',\n      customerConfig: { frequency: '4G', power: 80, antenna_type: 'omnidirectional' },\n      coreBOMId: '2',\n      coreBOMVersion: 'ANT-4G-002 V2.1',\n      items: [],\n      status: 'FROZEN',\n      totalCost: 98000,\n      estimatedMargin: 0.30,\n      deliveryDate: '2024-02-20',\n      createdBy: 'sales_pmc',\n      createdAt: '2024-01-16T00:00:00Z',\n      confirmedAt: '2024-01-17T00:00:00Z',\n      frozenAt: '2024-01-18T00:00:00Z',\n    },\n    {\n      id: '3',\n      orderNumber: 'ORD-2024-003',\n      customerName: '大唐移动通信设备有限公司',\n      customerConfig: { frequency: '5G', power: 120, antenna_type: 'smart' },\n      coreBOMId: '1',\n      coreBOMVersion: 'ANT-5G-001 V1.0',\n      items: [],\n      status: 'DRAFT',\n      totalCost: 156000,\n      estimatedMargin: 0.22,\n      deliveryDate: '2024-03-01',\n      createdBy: 'sales_pmc',\n      createdAt: '2024-01-17T00:00:00Z',\n    },\n  ];\n\n  // 统计数据\n  const stats = {\n    total: mockData.length,\n    draft: mockData.filter(item => item.status === ORDER_STATUS.DRAFT).length,\n    confirmed: mockData.filter(item => item.status === ORDER_STATUS.CONFIRMED).length,\n    frozen: mockData.filter(item => item.status === ORDER_STATUS.FROZEN).length,\n    totalValue: mockData.reduce((sum, item) => sum + item.totalCost, 0),\n  };\n\n  return (\n    <div>\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总订单数\"\n              value={stats.total}\n              prefix={<CalendarOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"草稿\"\n              value={stats.draft}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"已确认\"\n              value={stats.confirmed}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title=\"总价值\"\n              value={stats.totalValue}\n              prefix={<DollarOutlined />}\n              formatter={(value) => formatCurrency(Number(value))}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              订单BOM管理\n            </Title>\n          </Col>\n          <Col>\n            <Space>\n              <Button icon={<ImportOutlined />}>\n                导入\n              </Button>\n              <Button icon={<ExportOutlined />} onClick={() => handleExport()}>\n                批量导出\n              </Button>\n              <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleDerive}>\n                派生订单BOM\n              </Button>\n              <Button icon={<PlusOutlined />} onClick={handleCreate}>\n                手动创建\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n          <Col xs={24} sm={8} md={6}>\n            <Search\n              placeholder=\"搜索订单号或客户名称\"\n              allowClear\n              onSearch={handleSearch}\n              style={{ width: '100%' }}\n            />\n          </Col>\n          <Col xs={24} sm={8} md={6}>\n            <Select\n              placeholder=\"状态筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              onChange={handleStatusFilter}\n              options={[\n                { label: '草稿', value: ORDER_STATUS.DRAFT },\n                { label: '已确认', value: ORDER_STATUS.CONFIRMED },\n                { label: '已冻结', value: ORDER_STATUS.FROZEN },\n                { label: '已取消', value: ORDER_STATUS.CANCELLED },\n              ]}\n            />\n          </Col>\n          <Col xs={24} sm={8} md={6}>\n            <Select\n              placeholder=\"客户筛选\"\n              allowClear\n              style={{ width: '100%' }}\n              onChange={handleCustomerFilter}\n              options={[\n                { label: '华为技术', value: '华为技术有限公司' },\n                { label: '中兴通讯', value: '中兴通讯股份有限公司' },\n                { label: '大唐移动', value: '大唐移动通信设备有限公司' },\n                { label: '爱立信', value: '爱立信（中国）通信有限公司' },\n              ]}\n            />\n          </Col>\n        </Row>\n\n        <Table\n          columns={columns}\n          dataSource={mockData}\n          loading={loading}\n          rowKey=\"id\"\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 导出模态框 */}\n      <Modal\n        title=\"导出订单BOM数据\"\n        open={exportModalVisible}\n        onOk={executeExport}\n        onCancel={() => setExportModalVisible(false)}\n        okText=\"导出\"\n        cancelText=\"取消\"\n        width={600}\n      >\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <Typography.Text strong>导出格式：</Typography.Text>\n            <Select\n              value={exportFormat}\n              onChange={setExportFormat}\n              style={{ width: 120, marginLeft: 8 }}\n            >\n              <Select.Option value=\"excel\">Excel</Select.Option>\n              <Select.Option value=\"csv\">CSV</Select.Option>\n            </Select>\n          </div>\n          \n          <div>\n            <Typography.Text strong>导出字段：</Typography.Text>\n            <Checkbox.Group\n              value={exportFields}\n              onChange={setExportFields}\n              style={{ marginTop: 8 }}\n            >\n              <Row>\n                <Col span={12}>\n                  <Checkbox value=\"orderNumber\">订单号</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"customerName\">客户名称</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"coreBOMVersion\">核心BOM</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"status\">状态</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"totalCost\">总成本</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"estimatedMargin\">预估毛利</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"deliveryDate\">交期</Checkbox>\n                </Col>\n                <Col span={12}>\n                  <Checkbox value=\"createdAt\">创建时间</Checkbox>\n                </Col>\n              </Row>\n            </Checkbox.Group>\n          </div>\n        </Space>\n      </Modal>\n\n      {/* 复制模态框 */}\n      <Modal\n        title=\"复制订单BOM\"\n        open={copyModalVisible}\n        onOk={executeCopy}\n        onCancel={() => setCopyModalVisible(false)}\n        okText=\"确认复制\"\n        cancelText=\"取消\"\n        width={500}\n      >\n        <Form\n          form={copyForm}\n          layout=\"vertical\"\n          initialValues={{\n            orderNumber: '',\n            customerName: '',\n            deliveryDate: null,\n          }}\n        >\n          <Form.Item\n            label=\"新订单号\"\n            name=\"orderNumber\"\n            rules={[\n              { required: true, message: '请输入订单号' },\n              { pattern: /^[A-Z0-9-]+$/, message: '订单号只能包含大写字母、数字和连字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入新的订单号\" />\n          </Form.Item>\n          \n          <Form.Item\n            label=\"客户名称\"\n            name=\"customerName\"\n            rules={[{ required: true, message: '请输入客户名称' }]}\n          >\n            <Input placeholder=\"请输入客户名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            label=\"交期\"\n            name=\"deliveryDate\"\n            rules={[{ required: true, message: '请选择交期' }]}\n          >\n            <Input type=\"date\" />\n          </Form.Item>\n          \n          <Typography.Text type=\"secondary\">\n            注意：复制将创建一个新的订单BOM，包含原订单的所有配置和物料清单，但状态将重置为草稿。\n          </Typography.Text>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderBOMListPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,OAAO,CACPC,KAAK,CACLC,IAAI,CACJC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,OAAO,CACPC,QAAQ,CAGRC,SAAS,CACTC,QAAQ,KACH,MAAM,CACb,MAAO,GAAK,CAAAC,IAAI,KAAM,MAAM,CAC5B,OACEC,YAAY,CAEZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,YAAY,CACZC,cAAc,CACdC,cAAc,CACdC,cAAc,CACdC,gBAAgB,KAEX,mBAAmB,CAE1B,OAASC,cAAc,CAAEC,cAAc,KAAQ,mBAAmB,CAClE,OAASC,cAAc,CAAEC,cAAc,CAAEC,cAAc,KAAQ,6BAA6B,CAE5F,OAASC,MAAM,CAAEC,YAAY,KAAQ,iBAAiB,CACtD,OAASC,UAAU,CAAEC,cAAc,KAAQ,aAAa,CACxD,OAASC,aAAa,KAAQ,kBAAkB,CAChD,OAASC,YAAY,CAAEC,SAAS,KAAQ,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAEC,KAAM,CAAC,CAAGpC,UAAU,CAC5B,KAAM,CAAEqC,MAAO,CAAC,CAAG5C,KAAK,CAExB,KAAM,CAAA6C,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAAAC,QAAQ,CAAGlD,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAmD,QAAQ,CAAGpB,cAAc,CAAC,CAAC,CACjC,KAAM,CAAEqB,SAAS,CAAEC,OAAO,CAAEC,UAAW,CAAC,CAAGtB,cAAc,CAACuB,KAAK,EAAIA,KAAK,CAACC,GAAG,CAAC,CAE7E,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC4D,YAAY,CAAEC,eAAe,CAAC,CAAG7D,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAAC8D,cAAc,CAAEC,iBAAiB,CAAC,CAAG/D,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACgE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAkB,OAAO,CAAC,CAC1E,KAAM,CAACoE,YAAY,CAAEC,eAAe,CAAC,CAAGrE,QAAQ,CAAW,CAAC,aAAa,CAAE,cAAc,CAAE,gBAAgB,CAAE,QAAQ,CAAE,WAAW,CAAE,cAAc,CAAC,CAAC,CACpJ,KAAM,CAACsE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACwE,YAAY,CAAEC,eAAe,CAAC,CAAGzE,QAAQ,CAAkB,IAAI,CAAC,CACvE,KAAM,CAAC0E,QAAQ,CAAC,CAAG/D,IAAI,CAACgE,OAAO,CAAC,CAAC,CAEjC5E,SAAS,CAAC,IAAM,CACd6E,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACrB,UAAU,CAACsB,OAAO,CAAEtB,UAAU,CAACuB,QAAQ,CAAEpB,aAAa,CAAEE,YAAY,CAAEE,cAAc,CAAC,CAAC,CAE1F,KAAM,CAAAc,QAAQ,CAAGA,CAAA,GAAM,CACrBxB,QAAQ,CAAClB,cAAc,CAAC,CACtB6C,IAAI,CAAExB,UAAU,CAACsB,OAAO,CACxBC,QAAQ,CAAEvB,UAAU,CAACuB,QAAQ,CAC7BE,OAAO,CAAEtB,aACX,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAuB,YAAY,CAAIC,KAAa,EAAK,CACtCvB,gBAAgB,CAACuB,KAAK,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAID,KAAa,EAAK,CAC5CrB,eAAe,CAACqB,KAAK,CAAC,CACxB,CAAC,CAED,KAAM,CAAAE,oBAAoB,CAAIF,KAAa,EAAK,CAC9CnB,iBAAiB,CAACmB,KAAK,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAG,YAAY,CAAGA,CAAA,GAAM,CACzBlC,QAAQ,CAACd,MAAM,CAACiD,gBAAgB,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB;AACA7E,KAAK,CAAC8E,IAAI,CAAC,CACTC,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,eAAe,CACxBC,IAAI,CAAEA,CAAA,GAAM,CACVxC,QAAQ,CAAC,WAAW,CAAC,CAAE;AACzB,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAyC,UAAU,CAAIC,MAAgB,EAAK,CACvC1C,QAAQ,oBAAA2C,MAAA,CAAoBD,MAAM,CAACE,EAAE,CAAE,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIH,MAAgB,EAAK,CACvC1C,QAAQ,CAACd,MAAM,CAAC4D,cAAc,CAACC,OAAO,CAAC,KAAK,CAAEL,MAAM,CAACE,EAAE,CAAC,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAI,YAAY,CAAG,KAAO,CAAAN,MAAgB,EAAK,CAC/CpD,aAAa,CAAC2D,OAAO,CAAC,CACpBX,KAAK,CAAE,MAAM,CACbC,OAAO,cACL3C,KAAA,QAAAsD,QAAA,eACExD,IAAA,MAAAwD,QAAA,CAAG,uEAAc,CAAG,CAAC,cACrBtD,KAAA,MAAAsD,QAAA,eAAGxD,IAAA,WAAAwD,QAAA,CAAQ,0BAAI,CAAQ,CAAC,CAACR,MAAM,CAACS,WAAW,EAAI,CAAC,cAChDvD,KAAA,MAAAsD,QAAA,eAAGxD,IAAA,WAAAwD,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAACR,MAAM,CAACU,YAAY,EAAI,CAAC,cAClDxD,KAAA,MAAAsD,QAAA,eAAGxD,IAAA,WAAAwD,QAAA,CAAQ,uBAAM,CAAQ,CAAC,CAACR,MAAM,CAACW,cAAc,EAAI,CAAC,cACrD3D,IAAA,MAAG4D,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,EAAG,CAAE,CAAAN,QAAA,CAAC,sFAAc,CAAG,CAAC,EAC9D,CACN,CACDO,IAAI,CAAE,SAAS,CACfC,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI,CACF,KAAM,CAAAzD,QAAQ,CAACjB,cAAc,CAAC0D,MAAM,CAACE,EAAE,CAAC,CAAC,CAACe,MAAM,CAAC,CAAC,CAClDrG,OAAO,CAACsG,OAAO,CAAC,MAAM,CAAC,CACvBnC,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOoC,KAAU,CAAE,CACnBtE,YAAY,CAACuE,WAAW,CAAC,CACvBL,IAAI,CAAEjE,SAAS,CAACuE,QAAQ,CACxBzG,OAAO,CAAEuG,KAAK,CAACvG,OAAO,EAAI,MAAM,CAChC0G,OAAO,CAAEH,KACX,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAI,YAAY,CAAG,KAAO,CAAAvB,MAAgB,EAAK,CAC/C,KAAM,CAAAwB,QAAQ,CAAGxB,MAAM,CAACyB,MAAM,GAAKhF,YAAY,CAACiF,MAAM,CACtD,KAAM,CAAAC,MAAM,CAAGH,QAAQ,CAAG,IAAI,CAAG,IAAI,CAErC5E,aAAa,CAAC2D,OAAO,CAAC,CACpBX,KAAK,gBAAAK,MAAA,CAAO0B,MAAM,CAAE,CACpB9B,OAAO,cACL3C,KAAA,QAAAsD,QAAA,eACEtD,KAAA,MAAAsD,QAAA,EAAG,oBAAG,CAACmB,MAAM,CAAC,yCAAS,EAAG,CAAC,cAC3BzE,KAAA,MAAAsD,QAAA,eAAGxD,IAAA,WAAAwD,QAAA,CAAQ,0BAAI,CAAQ,CAAC,CAACR,MAAM,CAACS,WAAW,EAAI,CAAC,cAChDvD,KAAA,MAAAsD,QAAA,eAAGxD,IAAA,WAAAwD,QAAA,CAAQ,gCAAK,CAAQ,CAAC,CAACR,MAAM,CAACU,YAAY,EAAI,CAAC,cAClDxD,KAAA,MAAAsD,QAAA,eAAGxD,IAAA,WAAAwD,QAAA,CAAQ,uBAAM,CAAQ,CAAC,CAACR,MAAM,CAACW,cAAc,EAAI,CAAC,CACpDa,QAAQ,eACPxE,IAAA,MAAG4D,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,EAAG,CAAE,CAAAN,QAAA,CAAC,mFAAgB,CAAG,CACnE,EACE,CACN,CACDO,IAAI,CAAES,QAAQ,CAAG,SAAS,CAAG,MAAM,CACnCR,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI,CACF,KAAM,CAAAzD,QAAQ,CAAChB,cAAc,CAACyD,MAAM,CAACE,EAAE,CAAC,CAAC,CAACe,MAAM,CAAC,CAAC,CAClDrG,OAAO,CAACsG,OAAO,IAAAjB,MAAA,CAAI0B,MAAM,gBAAI,CAAC,CAC9B5C,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOoC,KAAU,CAAE,CACnBtE,YAAY,CAACuE,WAAW,CAAC,CACvBL,IAAI,CAAEjE,SAAS,CAACuE,QAAQ,CACxBzG,OAAO,CAAEuG,KAAK,CAACvG,OAAO,KAAAqF,MAAA,CAAO0B,MAAM,gBAAI,CACvCL,OAAO,CAAEH,KACX,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAS,YAAY,CAAI5B,MAAiB,EAAK,CAC1C5B,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAyD,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAGC,QAAQ,CAACC,GAAG,CAACC,KAAK,EAAI,CACvC,KAAM,CAAAC,IAAS,CAAG,CAAC,CAAC,CAEpB,GAAI3D,YAAY,CAAC4D,QAAQ,CAAC,aAAa,CAAC,CAAED,IAAI,CAAC,KAAK,CAAC,CAAGD,KAAK,CAACxB,WAAW,CACzE,GAAIlC,YAAY,CAAC4D,QAAQ,CAAC,cAAc,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGD,KAAK,CAACvB,YAAY,CAC5E,GAAInC,YAAY,CAAC4D,QAAQ,CAAC,gBAAgB,CAAC,CAAED,IAAI,CAAC,OAAO,CAAC,CAAGD,KAAK,CAACtB,cAAc,CACjF,GAAIpC,YAAY,CAAC4D,QAAQ,CAAC,QAAQ,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGE,aAAa,CAACH,KAAK,CAACR,MAAM,CAAC,CAC7E,GAAIlD,YAAY,CAAC4D,QAAQ,CAAC,WAAW,CAAC,CAAED,IAAI,CAAC,KAAK,CAAC,CAAGD,KAAK,CAACI,SAAS,CACrE,GAAI9D,YAAY,CAAC4D,QAAQ,CAAC,iBAAiB,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,IAAAjC,MAAA,CAAM,CAACgC,KAAK,CAACK,eAAe,CAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAG,CAC3G,GAAIhE,YAAY,CAAC4D,QAAQ,CAAC,cAAc,CAAC,CAAED,IAAI,CAAC,IAAI,CAAC,CAAGxF,UAAU,CAACuF,KAAK,CAACO,YAAY,CAAC,CACtF,GAAIjE,YAAY,CAAC4D,QAAQ,CAAC,WAAW,CAAC,CAAED,IAAI,CAAC,MAAM,CAAC,CAAGxF,UAAU,CAACuF,KAAK,CAACQ,SAAS,CAAC,CAElF,MAAO,CAAAP,IAAI,CACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAAQ,EAAE,CAAGpH,IAAI,CAACqH,KAAK,CAACC,aAAa,CAACd,UAAU,CAAC,CAC/C,KAAM,CAAAe,EAAE,CAAGvH,IAAI,CAACqH,KAAK,CAACG,QAAQ,CAAC,CAAC,CAChCxH,IAAI,CAACqH,KAAK,CAACI,iBAAiB,CAACF,EAAE,CAAEH,EAAE,CAAE,SAAS,CAAC,CAE/C;AACA,KAAM,CAAAM,QAAQ,gCAAA/C,MAAA,CAAc,GAAI,CAAAgD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAAlD,MAAA,CAAI5B,YAAY,GAAK,OAAO,CAAG,MAAM,CAAG,KAAK,CAAE,CACjH/C,IAAI,CAAC8H,SAAS,CAACP,EAAE,CAAEG,QAAQ,CAAC,CAE5BpI,OAAO,CAACsG,OAAO,CAAC,MAAM,CAAC,CACvB9C,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,MAAO+C,KAAK,CAAE,CACdvG,OAAO,CAACuG,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAkC,UAAU,CAAIrD,MAAgB,EAAK,CACvCpB,eAAe,CAACoB,MAAM,CAAC,CACvBnB,QAAQ,CAACyE,cAAc,CAAC,CACtB7C,WAAW,IAAAR,MAAA,CAAKD,MAAM,CAACS,WAAW,SAAO,CACzCC,YAAY,CAAEV,MAAM,CAACU,YAAY,CACjC8B,YAAY,CAAE,IAChB,CAAC,CAAC,CACF9D,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAA6E,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA3E,QAAQ,CAAC4E,cAAc,CAAC,CAAC,CAC9C;AACA7I,OAAO,CAACsG,OAAO,CAAC,MAAM,CAAC,CACvBxC,mBAAmB,CAAC,KAAK,CAAC,CAC1BK,QAAQ,CAAC,CAAC,CACZ,CAAE,MAAOoC,KAAK,CAAE,CACdvG,OAAO,CAACuG,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAuC,cAAc,CAAIjC,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,CAAAhF,YAAY,CAACkH,KAAK,CAAE,MAAO,SAAS,CACzC,IAAK,CAAAlH,YAAY,CAACmH,SAAS,CAAE,MAAO,YAAY,CAChD,IAAK,CAAAnH,YAAY,CAACiF,MAAM,CAAE,MAAO,SAAS,CAC1C,IAAK,CAAAjF,YAAY,CAACoH,SAAS,CAAE,MAAO,OAAO,CAC3C,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAzB,aAAa,CAAIX,MAAc,EAAK,CACxC,OAAQA,MAAM,EACZ,IAAK,CAAAhF,YAAY,CAACkH,KAAK,CAAE,MAAO,IAAI,CACpC,IAAK,CAAAlH,YAAY,CAACmH,SAAS,CAAE,MAAO,KAAK,CACzC,IAAK,CAAAnH,YAAY,CAACiF,MAAM,CAAE,MAAO,KAAK,CACtC,IAAK,CAAAjF,YAAY,CAACoH,SAAS,CAAE,MAAO,KAAK,CACzC,QAAS,MAAO,CAAApC,MAAM,CACxB,CACF,CAAC,CAED,KAAM,CAAAqC,kBAAkB,CAAI9D,MAAgB,EAAyB,CACnE,CACE+D,GAAG,CAAE,MAAM,CACXC,IAAI,cAAEhH,IAAA,CAACrB,YAAY,GAAE,CAAC,CACtBsI,KAAK,CAAE,IAAI,CACXC,OAAO,CAAEA,CAAA,GAAMb,UAAU,CAACrD,MAAM,CAClC,CAAC,CACD,CACE+D,GAAG,CAAE,QAAQ,CACbC,IAAI,cAAEhH,IAAA,CAACjB,cAAc,GAAE,CAAC,CACxBkI,KAAK,CAAE,IAAI,CACXC,OAAO,CAAEA,CAAA,GAAMtC,YAAY,CAAC5B,MAAM,CACpC,CAAC,CACD,CACE+D,GAAG,CAAE,QAAQ,CACbC,IAAI,CAAEhE,MAAM,CAACyB,MAAM,GAAKhF,YAAY,CAACiF,MAAM,cAAG1E,IAAA,CAACnB,cAAc,GAAE,CAAC,cAAGmB,IAAA,CAACpB,YAAY,GAAE,CAAC,CACnFqI,KAAK,CAAEjE,MAAM,CAACyB,MAAM,GAAKhF,YAAY,CAACiF,MAAM,CAAG,IAAI,CAAG,IAAI,CAC1DwC,OAAO,CAAEA,CAAA,GAAM3C,YAAY,CAACvB,MAAM,CAAC,CACnCmE,QAAQ,CAAEnE,MAAM,CAACyB,MAAM,GAAKhF,YAAY,CAACkH,KAC3C,CAAC,CACF,CAED,KAAM,CAAAS,OAAO,CAAG,CACd,CACExE,KAAK,CAAE,KAAK,CACZyE,SAAS,CAAE,aAAa,CACxBN,GAAG,CAAE,aAAa,CAClBO,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACC,IAAY,CAAExE,MAAgB,gBACrChD,IAAA,CAAC1C,MAAM,EAACyG,IAAI,CAAC,MAAM,CAACmD,OAAO,CAAEA,CAAA,GAAM/D,UAAU,CAACH,MAAM,CAAE,CAAAQ,QAAA,CACnDgE,IAAI,CACC,CAEZ,CAAC,CACD,CACE5E,KAAK,CAAE,MAAM,CACbyE,SAAS,CAAE,cAAc,CACzBN,GAAG,CAAE,cAAc,CACnBU,QAAQ,CAAE,IACZ,CAAC,CACD,CACE7E,KAAK,CAAE,OAAO,CACdyE,SAAS,CAAE,WAAW,CACtBN,GAAG,CAAE,WAAW,CAChBO,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACG,SAAiB,CAAE1E,MAAgB,gBAC1ChD,IAAA,SAAAwD,QAAA,CAAOR,MAAM,CAACW,cAAc,CAAO,CAEvC,CAAC,CACD,CACEf,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,QAAQ,CACnBN,GAAG,CAAE,QAAQ,CACbO,KAAK,CAAE,EAAE,CACTC,MAAM,CAAG9C,MAAc,eACrBzE,IAAA,CAACrC,GAAG,EAACkG,KAAK,CAAE6C,cAAc,CAACjC,MAAM,CAAE,CAAAjB,QAAA,CAChC4B,aAAa,CAACX,MAAM,CAAC,CACnB,CAET,CAAC,CACD,CACE7B,KAAK,CAAE,KAAK,CACZyE,SAAS,CAAE,WAAW,CACtBN,GAAG,CAAE,WAAW,CAChBO,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGI,IAAY,EAAKhI,cAAc,CAACgI,IAAI,CAC/C,CAAC,CACD,CACE/E,KAAK,CAAE,MAAM,CACbyE,SAAS,CAAE,iBAAiB,CAC5BN,GAAG,CAAE,iBAAiB,CACtBO,KAAK,CAAE,EAAE,CACTC,MAAM,CAAGK,MAAc,KAAA3E,MAAA,CAAQ,CAAC2E,MAAM,CAAG,GAAG,EAAErC,OAAO,CAAC,CAAC,CAAC,KAC1D,CAAC,CACD,CACE3C,KAAK,CAAE,IAAI,CACXyE,SAAS,CAAE,cAAc,CACzBN,GAAG,CAAE,cAAc,CACnBO,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGM,IAAY,EAAKnI,UAAU,CAACmI,IAAI,CAC3C,CAAC,CACD,CACEjF,KAAK,CAAE,MAAM,CACbyE,SAAS,CAAE,WAAW,CACtBN,GAAG,CAAE,WAAW,CAChBO,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGM,IAAY,EAAKnI,UAAU,CAACmI,IAAI,CAC3C,CAAC,CACD,CACEjF,KAAK,CAAE,IAAI,CACXmE,GAAG,CAAE,QAAQ,CACbO,KAAK,CAAE,GAAG,CACVQ,KAAK,CAAE,OAAgB,CACvBP,MAAM,CAAEA,CAACQ,CAAM,CAAE/E,MAAgB,gBAC/B9C,KAAA,CAAC3C,KAAK,EAACyK,IAAI,CAAC,OAAO,CAAAxE,QAAA,eACjBxD,IAAA,CAAC9B,OAAO,EAAC0E,KAAK,CAAC,cAAI,CAAAY,QAAA,cACjBxD,IAAA,CAAC1C,MAAM,EACLyG,IAAI,CAAC,MAAM,CACXiD,IAAI,cAAEhH,IAAA,CAACtB,WAAW,GAAE,CAAE,CACtBwI,OAAO,CAAEA,CAAA,GAAM/D,UAAU,CAACH,MAAM,CAAE,CACnC,CAAC,CACK,CAAC,cACVhD,IAAA,CAAC9B,OAAO,EAAC0E,KAAK,CAAC,cAAI,CAAAY,QAAA,cACjBxD,IAAA,CAAC1C,MAAM,EACLyG,IAAI,CAAC,MAAM,CACXiD,IAAI,cAAEhH,IAAA,CAACxB,YAAY,GAAE,CAAE,CACvB0I,OAAO,CAAEA,CAAA,GAAMnE,UAAU,CAACC,MAAM,CAAE,CAClCmE,QAAQ,CAAEnE,MAAM,CAACyB,MAAM,GAAKhF,YAAY,CAACiF,MAAO,CACjD,CAAC,CACK,CAAC,cACV1E,IAAA,CAAC9B,OAAO,EAAC0E,KAAK,CAAC,cAAI,CAAAY,QAAA,cACjBxD,IAAA,CAAC1C,MAAM,EACLyG,IAAI,CAAC,MAAM,CACXkE,MAAM,MACNjB,IAAI,cAAEhH,IAAA,CAACvB,cAAc,GAAE,CAAE,CACzByI,OAAO,CAAEA,CAAA,GAAM5D,YAAY,CAACN,MAAM,CAAE,CACpCmE,QAAQ,CAAEnE,MAAM,CAACyB,MAAM,GAAKhF,YAAY,CAACkH,KAAM,CAChD,CAAC,CACK,CAAC,cACV3G,IAAA,CAAC7B,QAAQ,EACP+J,IAAI,CAAE,CAAEC,KAAK,CAAErB,kBAAkB,CAAC9D,MAAM,CAAE,CAAE,CAC5CoF,OAAO,CAAE,CAAC,OAAO,CAAE,CAAA5E,QAAA,cAEnBxD,IAAA,CAAC1C,MAAM,EAACyG,IAAI,CAAC,MAAM,CAACiD,IAAI,cAAEhH,IAAA,CAAClB,YAAY,GAAE,CAAE,CAAE,CAAC,CACtC,CAAC,EACN,CAEX,CAAC,CACF,CAED;AACA,KAAM,CAAAiG,QAAoB,CAAG,CAC3B,CACE7B,EAAE,CAAE,GAAG,CACPO,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,UAAU,CACxB2E,cAAc,CAAE,CAAEC,SAAS,CAAE,IAAI,CAAEC,KAAK,CAAE,GAAG,CAAEC,YAAY,CAAE,aAAc,CAAC,CAC5Ed,SAAS,CAAE,GAAG,CACd/D,cAAc,CAAE,iBAAiB,CACjCwE,KAAK,CAAE,EAAE,CACT1D,MAAM,CAAE,WAAW,CACnBY,SAAS,CAAE,MAAM,CACjBC,eAAe,CAAE,IAAI,CACrBE,YAAY,CAAE,YAAY,CAC1BiD,SAAS,CAAE,WAAW,CACtBhD,SAAS,CAAE,sBAAsB,CACjCiD,WAAW,CAAE,sBACf,CAAC,CACD,CACExF,EAAE,CAAE,GAAG,CACPO,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,YAAY,CAC1B2E,cAAc,CAAE,CAAEC,SAAS,CAAE,IAAI,CAAEC,KAAK,CAAE,EAAE,CAAEC,YAAY,CAAE,iBAAkB,CAAC,CAC/Ed,SAAS,CAAE,GAAG,CACd/D,cAAc,CAAE,iBAAiB,CACjCwE,KAAK,CAAE,EAAE,CACT1D,MAAM,CAAE,QAAQ,CAChBY,SAAS,CAAE,KAAK,CAChBC,eAAe,CAAE,IAAI,CACrBE,YAAY,CAAE,YAAY,CAC1BiD,SAAS,CAAE,WAAW,CACtBhD,SAAS,CAAE,sBAAsB,CACjCiD,WAAW,CAAE,sBAAsB,CACnCC,QAAQ,CAAE,sBACZ,CAAC,CACD,CACEzF,EAAE,CAAE,GAAG,CACPO,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,cAAc,CAC5B2E,cAAc,CAAE,CAAEC,SAAS,CAAE,IAAI,CAAEC,KAAK,CAAE,GAAG,CAAEC,YAAY,CAAE,OAAQ,CAAC,CACtEd,SAAS,CAAE,GAAG,CACd/D,cAAc,CAAE,iBAAiB,CACjCwE,KAAK,CAAE,EAAE,CACT1D,MAAM,CAAE,OAAO,CACfY,SAAS,CAAE,MAAM,CACjBC,eAAe,CAAE,IAAI,CACrBE,YAAY,CAAE,YAAY,CAC1BiD,SAAS,CAAE,WAAW,CACtBhD,SAAS,CAAE,sBACb,CAAC,CACF,CAED;AACA,KAAM,CAAAmD,KAAK,CAAG,CACZC,KAAK,CAAE9D,QAAQ,CAAC+D,MAAM,CACtBC,KAAK,CAAEhE,QAAQ,CAACiE,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACxE,MAAM,GAAKhF,YAAY,CAACkH,KAAK,CAAC,CAACmC,MAAM,CACzEI,SAAS,CAAEnE,QAAQ,CAACiE,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACxE,MAAM,GAAKhF,YAAY,CAACmH,SAAS,CAAC,CAACkC,MAAM,CACjFK,MAAM,CAAEpE,QAAQ,CAACiE,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACxE,MAAM,GAAKhF,YAAY,CAACiF,MAAM,CAAC,CAACoE,MAAM,CAC3EM,UAAU,CAAErE,QAAQ,CAACsE,MAAM,CAAC,CAACC,GAAG,CAAEL,IAAI,GAAKK,GAAG,CAAGL,IAAI,CAAC5D,SAAS,CAAE,CAAC,CACpE,CAAC,CAED,mBACEnF,KAAA,QAAAsD,QAAA,eAEEtD,KAAA,CAAClC,GAAG,EAACuL,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC3F,KAAK,CAAE,CAAE4F,YAAY,CAAE,EAAG,CAAE,CAAAhG,QAAA,eACjDxD,IAAA,CAAC/B,GAAG,EAACwL,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlG,QAAA,cACjBxD,IAAA,CAACtC,IAAI,EAAA8F,QAAA,cACHxD,IAAA,CAAC5B,SAAS,EACRwE,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEuG,KAAK,CAACC,KAAM,CACnBc,MAAM,cAAE3J,IAAA,CAACd,gBAAgB,GAAE,CAAE,CAC7B0K,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN7D,IAAA,CAAC/B,GAAG,EAACwL,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlG,QAAA,cACjBxD,IAAA,CAACtC,IAAI,EAAA8F,QAAA,cACHxD,IAAA,CAAC5B,SAAS,EACRwE,KAAK,CAAC,cAAI,CACVP,KAAK,CAAEuG,KAAK,CAACG,KAAM,CACnBa,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN7D,IAAA,CAAC/B,GAAG,EAACwL,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlG,QAAA,cACjBxD,IAAA,CAACtC,IAAI,EAAA8F,QAAA,cACHxD,IAAA,CAAC5B,SAAS,EACRwE,KAAK,CAAC,oBAAK,CACXP,KAAK,CAAEuG,KAAK,CAACM,SAAU,CACvBU,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN7D,IAAA,CAAC/B,GAAG,EAACwL,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlG,QAAA,cACjBxD,IAAA,CAACtC,IAAI,EAAA8F,QAAA,cACHxD,IAAA,CAAC5B,SAAS,EACRwE,KAAK,CAAC,oBAAK,CACXP,KAAK,CAAEuG,KAAK,CAACQ,UAAW,CACxBO,MAAM,cAAE3J,IAAA,CAACf,cAAc,GAAE,CAAE,CAC3B4K,SAAS,CAAGxH,KAAK,EAAK1C,cAAc,CAACmK,MAAM,CAACzH,KAAK,CAAC,CAAE,CACpDuH,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAEN3D,KAAA,CAACxC,IAAI,EAAA8F,QAAA,eACHtD,KAAA,CAAClC,GAAG,EAAC+L,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAACpG,KAAK,CAAE,CAAE4F,YAAY,CAAE,EAAG,CAAE,CAAAhG,QAAA,eACtExD,IAAA,CAAC/B,GAAG,EAAAuF,QAAA,cACFxD,IAAA,CAACG,KAAK,EAAC8J,KAAK,CAAE,CAAE,CAACrG,KAAK,CAAE,CAAEgE,MAAM,CAAE,CAAE,CAAE,CAAApE,QAAA,CAAC,6BAEvC,CAAO,CAAC,CACL,CAAC,cACNxD,IAAA,CAAC/B,GAAG,EAAAuF,QAAA,cACFtD,KAAA,CAAC3C,KAAK,EAAAiG,QAAA,eACJxD,IAAA,CAAC1C,MAAM,EAAC0J,IAAI,cAAEhH,IAAA,CAAChB,cAAc,GAAE,CAAE,CAAAwE,QAAA,CAAC,cAElC,CAAQ,CAAC,cACTxD,IAAA,CAAC1C,MAAM,EAAC0J,IAAI,cAAEhH,IAAA,CAACjB,cAAc,GAAE,CAAE,CAACmI,OAAO,CAAEA,CAAA,GAAMtC,YAAY,CAAC,CAAE,CAAApB,QAAA,CAAC,0BAEjE,CAAQ,CAAC,cACTxD,IAAA,CAAC1C,MAAM,EAACyG,IAAI,CAAC,SAAS,CAACiD,IAAI,cAAEhH,IAAA,CAACzB,YAAY,GAAE,CAAE,CAAC2I,OAAO,CAAExE,YAAa,CAAAc,QAAA,CAAC,6BAEtE,CAAQ,CAAC,cACTxD,IAAA,CAAC1C,MAAM,EAAC0J,IAAI,cAAEhH,IAAA,CAACzB,YAAY,GAAE,CAAE,CAAC2I,OAAO,CAAE1E,YAAa,CAAAgB,QAAA,CAAC,0BAEvD,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACH,CAAC,cAENtD,KAAA,CAAClC,GAAG,EAACuL,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC3F,KAAK,CAAE,CAAE4F,YAAY,CAAE,EAAG,CAAE,CAAAhG,QAAA,eACjDxD,IAAA,CAAC/B,GAAG,EAACwL,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACQ,EAAE,CAAE,CAAE,CAAA1G,QAAA,cACxBxD,IAAA,CAACI,MAAM,EACL+J,WAAW,CAAC,8DAAY,CACxBC,UAAU,MACVC,QAAQ,CAAEjI,YAAa,CACvBwB,KAAK,CAAE,CAAE0D,KAAK,CAAE,MAAO,CAAE,CAC1B,CAAC,CACC,CAAC,cACNtH,IAAA,CAAC/B,GAAG,EAACwL,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACQ,EAAE,CAAE,CAAE,CAAA1G,QAAA,cACxBxD,IAAA,CAACvC,MAAM,EACL0M,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVxG,KAAK,CAAE,CAAE0D,KAAK,CAAE,MAAO,CAAE,CACzBgD,QAAQ,CAAEhI,kBAAmB,CAC7BiI,OAAO,CAAE,CACP,CAAEtD,KAAK,CAAE,IAAI,CAAE5E,KAAK,CAAE5C,YAAY,CAACkH,KAAM,CAAC,CAC1C,CAAEM,KAAK,CAAE,KAAK,CAAE5E,KAAK,CAAE5C,YAAY,CAACmH,SAAU,CAAC,CAC/C,CAAEK,KAAK,CAAE,KAAK,CAAE5E,KAAK,CAAE5C,YAAY,CAACiF,MAAO,CAAC,CAC5C,CAAEuC,KAAK,CAAE,KAAK,CAAE5E,KAAK,CAAE5C,YAAY,CAACoH,SAAU,CAAC,CAC/C,CACH,CAAC,CACC,CAAC,cACN7G,IAAA,CAAC/B,GAAG,EAACwL,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACQ,EAAE,CAAE,CAAE,CAAA1G,QAAA,cACxBxD,IAAA,CAACvC,MAAM,EACL0M,WAAW,CAAC,0BAAM,CAClBC,UAAU,MACVxG,KAAK,CAAE,CAAE0D,KAAK,CAAE,MAAO,CAAE,CACzBgD,QAAQ,CAAE/H,oBAAqB,CAC/BgI,OAAO,CAAE,CACP,CAAEtD,KAAK,CAAE,MAAM,CAAE5E,KAAK,CAAE,UAAW,CAAC,CACpC,CAAE4E,KAAK,CAAE,MAAM,CAAE5E,KAAK,CAAE,YAAa,CAAC,CACtC,CAAE4E,KAAK,CAAE,MAAM,CAAE5E,KAAK,CAAE,cAAe,CAAC,CACxC,CAAE4E,KAAK,CAAE,KAAK,CAAE5E,KAAK,CAAE,eAAgB,CAAC,CACxC,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAENrC,IAAA,CAAC3C,KAAK,EACJ+J,OAAO,CAAEA,OAAQ,CACjBoD,UAAU,CAAEzF,QAAS,CACrBtE,OAAO,CAAEA,OAAQ,CACjBgK,MAAM,CAAC,IAAI,CACXC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBjK,UAAU,CAAE,CACVsB,OAAO,CAAEtB,UAAU,CAACsB,OAAO,CAC3BC,QAAQ,CAAEvB,UAAU,CAACuB,QAAQ,CAC7B4G,KAAK,CAAEnI,UAAU,CAACmI,KAAK,CACvB+B,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACjC,KAAK,CAAEkC,KAAK,aAAA9H,MAAA,CACjB8H,KAAK,CAAC,CAAC,CAAC,MAAA9H,MAAA,CAAI8H,KAAK,CAAC,CAAC,CAAC,oBAAA9H,MAAA,CAAQ4F,KAAK,WAC1C,CAAE,CACH,CAAC,EACE,CAAC,cAGP7I,IAAA,CAACnC,KAAK,EACJ+E,KAAK,CAAC,yCAAW,CACjBoI,IAAI,CAAE7J,kBAAmB,CACzB2B,IAAI,CAAE+B,aAAc,CACpBoG,QAAQ,CAAEA,CAAA,GAAM7J,qBAAqB,CAAC,KAAK,CAAE,CAC7C8J,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CACf7D,KAAK,CAAE,GAAI,CAAA9D,QAAA,cAEXtD,KAAA,CAAC3C,KAAK,EAAC6N,SAAS,CAAC,UAAU,CAACxH,KAAK,CAAE,CAAE0D,KAAK,CAAE,MAAO,CAAE,CAAA9D,QAAA,eACnDtD,KAAA,QAAAsD,QAAA,eACExD,IAAA,CAACjC,UAAU,CAACsN,IAAI,EAACC,MAAM,MAAA9H,QAAA,CAAC,gCAAK,CAAiB,CAAC,cAC/CtD,KAAA,CAACzC,MAAM,EACL4E,KAAK,CAAEhB,YAAa,CACpBiJ,QAAQ,CAAEhJ,eAAgB,CAC1BsC,KAAK,CAAE,CAAE0D,KAAK,CAAE,GAAG,CAAEiE,UAAU,CAAE,CAAE,CAAE,CAAA/H,QAAA,eAErCxD,IAAA,CAACvC,MAAM,CAAC+N,MAAM,EAACnJ,KAAK,CAAC,OAAO,CAAAmB,QAAA,CAAC,OAAK,CAAe,CAAC,cAClDxD,IAAA,CAACvC,MAAM,CAAC+N,MAAM,EAACnJ,KAAK,CAAC,KAAK,CAAAmB,QAAA,CAAC,KAAG,CAAe,CAAC,EACxC,CAAC,EACN,CAAC,cAENtD,KAAA,QAAAsD,QAAA,eACExD,IAAA,CAACjC,UAAU,CAACsN,IAAI,EAACC,MAAM,MAAA9H,QAAA,CAAC,gCAAK,CAAiB,CAAC,cAC/CxD,IAAA,CAAC3B,QAAQ,CAACoN,KAAK,EACbpJ,KAAK,CAAEd,YAAa,CACpB+I,QAAQ,CAAE9I,eAAgB,CAC1BoC,KAAK,CAAE,CAAEE,SAAS,CAAE,CAAE,CAAE,CAAAN,QAAA,cAExBtD,KAAA,CAAClC,GAAG,EAAAwF,QAAA,eACFxD,IAAA,CAAC/B,GAAG,EAACyN,IAAI,CAAE,EAAG,CAAAlI,QAAA,cACZxD,IAAA,CAAC3B,QAAQ,EAACgE,KAAK,CAAC,aAAa,CAAAmB,QAAA,CAAC,oBAAG,CAAU,CAAC,CACzC,CAAC,cACNxD,IAAA,CAAC/B,GAAG,EAACyN,IAAI,CAAE,EAAG,CAAAlI,QAAA,cACZxD,IAAA,CAAC3B,QAAQ,EAACgE,KAAK,CAAC,cAAc,CAAAmB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC3C,CAAC,cACNxD,IAAA,CAAC/B,GAAG,EAACyN,IAAI,CAAE,EAAG,CAAAlI,QAAA,cACZxD,IAAA,CAAC3B,QAAQ,EAACgE,KAAK,CAAC,gBAAgB,CAAAmB,QAAA,CAAC,iBAAK,CAAU,CAAC,CAC9C,CAAC,cACNxD,IAAA,CAAC/B,GAAG,EAACyN,IAAI,CAAE,EAAG,CAAAlI,QAAA,cACZxD,IAAA,CAAC3B,QAAQ,EAACgE,KAAK,CAAC,QAAQ,CAAAmB,QAAA,CAAC,cAAE,CAAU,CAAC,CACnC,CAAC,cACNxD,IAAA,CAAC/B,GAAG,EAACyN,IAAI,CAAE,EAAG,CAAAlI,QAAA,cACZxD,IAAA,CAAC3B,QAAQ,EAACgE,KAAK,CAAC,WAAW,CAAAmB,QAAA,CAAC,oBAAG,CAAU,CAAC,CACvC,CAAC,cACNxD,IAAA,CAAC/B,GAAG,EAACyN,IAAI,CAAE,EAAG,CAAAlI,QAAA,cACZxD,IAAA,CAAC3B,QAAQ,EAACgE,KAAK,CAAC,iBAAiB,CAAAmB,QAAA,CAAC,0BAAI,CAAU,CAAC,CAC9C,CAAC,cACNxD,IAAA,CAAC/B,GAAG,EAACyN,IAAI,CAAE,EAAG,CAAAlI,QAAA,cACZxD,IAAA,CAAC3B,QAAQ,EAACgE,KAAK,CAAC,cAAc,CAAAmB,QAAA,CAAC,cAAE,CAAU,CAAC,CACzC,CAAC,cACNxD,IAAA,CAAC/B,GAAG,EAACyN,IAAI,CAAE,EAAG,CAAAlI,QAAA,cACZxD,IAAA,CAAC3B,QAAQ,EAACgE,KAAK,CAAC,WAAW,CAAAmB,QAAA,CAAC,0BAAI,CAAU,CAAC,CACxC,CAAC,EACH,CAAC,CACQ,CAAC,EACd,CAAC,EACD,CAAC,CACH,CAAC,cAGRxD,IAAA,CAACnC,KAAK,EACJ+E,KAAK,CAAC,6BAAS,CACfoI,IAAI,CAAEvJ,gBAAiB,CACvBqB,IAAI,CAAEyD,WAAY,CAClB0E,QAAQ,CAAEA,CAAA,GAAMvJ,mBAAmB,CAAC,KAAK,CAAE,CAC3CwJ,MAAM,CAAC,0BAAM,CACbC,UAAU,CAAC,cAAI,CACf7D,KAAK,CAAE,GAAI,CAAA9D,QAAA,cAEXtD,KAAA,CAACpC,IAAI,EACH6N,IAAI,CAAE9J,QAAS,CACf+J,MAAM,CAAC,UAAU,CACjBC,aAAa,CAAE,CACbpI,WAAW,CAAE,EAAE,CACfC,YAAY,CAAE,EAAE,CAChB8B,YAAY,CAAE,IAChB,CAAE,CAAAhC,QAAA,eAEFxD,IAAA,CAAClC,IAAI,CAACgO,IAAI,EACR7E,KAAK,CAAC,0BAAM,CACZ8E,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAErO,OAAO,CAAE,QAAS,CAAC,CACrC,CAAEsO,OAAO,CAAE,cAAc,CAAEtO,OAAO,CAAE,oBAAqB,CAAC,CAC1D,CAAA4F,QAAA,cAEFxD,IAAA,CAACxC,KAAK,EAAC2M,WAAW,CAAC,kDAAU,CAAE,CAAC,CACvB,CAAC,cAEZnK,IAAA,CAAClC,IAAI,CAACgO,IAAI,EACR7E,KAAK,CAAC,0BAAM,CACZ8E,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErO,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA4F,QAAA,cAEhDxD,IAAA,CAACxC,KAAK,EAAC2M,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,cAEZnK,IAAA,CAAClC,IAAI,CAACgO,IAAI,EACR7E,KAAK,CAAC,cAAI,CACV8E,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErO,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA4F,QAAA,cAE9CxD,IAAA,CAACxC,KAAK,EAACuG,IAAI,CAAC,MAAM,CAAE,CAAC,CACZ,CAAC,cAEZ/D,IAAA,CAACjC,UAAU,CAACsN,IAAI,EAACtH,IAAI,CAAC,WAAW,CAAAP,QAAA,CAAC,2PAElC,CAAiB,CAAC,EACd,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}