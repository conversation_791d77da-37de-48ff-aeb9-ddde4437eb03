{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\inventory\\\\BatchTrackingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, DatePicker, Modal, Row, Col, Typography, Tag, Tooltip, message, Tabs, Timeline, Descriptions, Alert, Statistic, Progress } from 'antd';\nimport { EyeOutlined, QrcodeOutlined, ExportOutlined, FilterOutlined, BarChartOutlined, AlertOutlined } from '@ant-design/icons';\nimport { formatCurrency, formatDate } from '../../utils/format';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Search\n} = Input;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  RangePicker\n} = DatePicker;\nconst BatchTrackingPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [batches, setBatches] = useState([]);\n  const [statistics, setStatistics] = useState({\n    totalBatches: 0,\n    activeBatches: 0,\n    expiredBatches: 0,\n    lowStockBatches: 0,\n    totalValue: 0,\n    averageAge: 0,\n    turnoverRate: 0\n  });\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState();\n  const [supplierFilter, setSupplierFilter] = useState();\n  const [dateRange, setDateRange] = useState(null);\n  const [selectedBatch, setSelectedBatch] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [activeTab, setActiveTab] = useState('list');\n\n  // 模拟数据\n  const mockBatches = [{\n    id: '1',\n    batchNumber: 'B2024030001',\n    materialId: 'M001',\n    materialCode: 'RF-PA-001',\n    materialName: 'RF功率放大器',\n    specification: '2.4GHz 20W',\n    supplier: '深圳射频科技',\n    receivedDate: '2024-03-01T08:00:00Z',\n    expiryDate: '2025-03-01T00:00:00Z',\n    originalQuantity: 100,\n    currentQuantity: 75,\n    reservedQuantity: 10,\n    availableQuantity: 65,\n    unit: 'PCS',\n    unitPrice: 150.00,\n    totalValue: 11250.00,\n    location: 'A区-01-03',\n    status: 'AVAILABLE',\n    qualityStatus: 'PASSED',\n    qrCode: 'QR_B2024030001',\n    transactions: [{\n      id: 'T001',\n      type: 'RECEIVE',\n      quantity: 100,\n      remainingQuantity: 100,\n      operator: '张三',\n      department: '仓储部',\n      reference: 'PO-2024-001',\n      timestamp: '2024-03-01T08:00:00Z',\n      location: 'A区-01-03'\n    }, {\n      id: 'T002',\n      type: 'ISSUE',\n      quantity: -25,\n      remainingQuantity: 75,\n      operator: '李四',\n      department: '生产部',\n      workOrder: 'WO-2024-001',\n      reference: 'ISS-2024-001',\n      timestamp: '2024-03-05T10:30:00Z',\n      location: 'A区-01-03'\n    }]\n  }, {\n    id: '2',\n    batchNumber: 'B2024030002',\n    materialId: 'M002',\n    materialCode: 'PCB-MAIN-001',\n    materialName: '主控PCB板',\n    specification: '4层板 FR4',\n    supplier: '苏州电路板厂',\n    receivedDate: '2024-03-02T09:15:00Z',\n    expiryDate: '2026-03-02T00:00:00Z',\n    originalQuantity: 200,\n    currentQuantity: 180,\n    reservedQuantity: 20,\n    availableQuantity: 160,\n    unit: 'PCS',\n    unitPrice: 85.00,\n    totalValue: 15300.00,\n    location: 'A区-02-01',\n    status: 'AVAILABLE',\n    qualityStatus: 'PASSED',\n    qrCode: 'QR_B2024030002',\n    transactions: [{\n      id: 'T003',\n      type: 'RECEIVE',\n      quantity: 200,\n      remainingQuantity: 200,\n      operator: '王五',\n      department: '仓储部',\n      reference: 'PO-2024-002',\n      timestamp: '2024-03-02T09:15:00Z',\n      location: 'A区-02-01'\n    }, {\n      id: 'T004',\n      type: 'ISSUE',\n      quantity: -20,\n      remainingQuantity: 180,\n      operator: '赵六',\n      department: '生产部',\n      workOrder: 'WO-2024-002',\n      reference: 'ISS-2024-002',\n      timestamp: '2024-03-06T14:20:00Z',\n      location: 'A区-02-01'\n    }]\n  }, {\n    id: '3',\n    batchNumber: 'B2024020015',\n    materialId: 'M003',\n    materialCode: 'CAP-CER-001',\n    materialName: '陶瓷电容',\n    specification: '0805 10uF',\n    supplier: '村田制作所',\n    receivedDate: '2024-02-15T11:30:00Z',\n    expiryDate: '2024-04-15T00:00:00Z',\n    originalQuantity: 5000,\n    currentQuantity: 500,\n    reservedQuantity: 0,\n    availableQuantity: 500,\n    unit: 'PCS',\n    unitPrice: 0.25,\n    totalValue: 125.00,\n    location: 'B区-03-05',\n    status: 'EXPIRED',\n    qualityStatus: 'PENDING',\n    qrCode: 'QR_B2024020015',\n    transactions: [{\n      id: 'T005',\n      type: 'RECEIVE',\n      quantity: 5000,\n      remainingQuantity: 5000,\n      operator: '孙七',\n      department: '仓储部',\n      reference: 'PO-2024-003',\n      timestamp: '2024-02-15T11:30:00Z',\n      location: 'B区-03-05'\n    }, {\n      id: 'T006',\n      type: 'ISSUE',\n      quantity: -4500,\n      remainingQuantity: 500,\n      operator: '周八',\n      department: '生产部',\n      workOrder: 'WO-2024-003',\n      reference: 'ISS-2024-003',\n      timestamp: '2024-03-10T16:45:00Z',\n      location: 'B区-03-05'\n    }]\n  }];\n  useEffect(() => {\n    loadBatchData();\n    loadStatistics();\n  }, []);\n  const loadBatchData = async () => {\n    try {\n      setLoading(true);\n      // TODO: 调用API获取批次数据\n      setBatches(mockBatches);\n    } catch (error) {\n      message.error('加载批次数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistics = async () => {\n    try {\n      // TODO: 调用API获取统计数据\n      const stats = {\n        totalBatches: mockBatches.length,\n        activeBatches: mockBatches.filter(b => b.status === 'AVAILABLE').length,\n        expiredBatches: mockBatches.filter(b => b.status === 'EXPIRED').length,\n        lowStockBatches: mockBatches.filter(b => b.currentQuantity < b.originalQuantity * 0.2).length,\n        totalValue: mockBatches.reduce((sum, b) => sum + b.totalValue, 0),\n        averageAge: 45,\n        turnoverRate: 2.5\n      };\n      setStatistics(stats);\n    } catch (error) {\n      message.error('加载统计数据失败');\n    }\n  };\n  const handleViewDetail = batch => {\n    setSelectedBatch(batch);\n    setDetailModalVisible(true);\n  };\n  const handleExport = () => {\n    // TODO: 实现导出功能\n    message.success('导出功能开发中');\n  };\n  const getStatusColor = status => {\n    const colors = {\n      AVAILABLE: 'green',\n      RESERVED: 'blue',\n      EXPIRED: 'red',\n      DAMAGED: 'orange',\n      CONSUMED: 'gray'\n    };\n    return colors[status] || 'default';\n  };\n  const getQualityStatusColor = status => {\n    const colors = {\n      PASSED: 'green',\n      PENDING: 'orange',\n      FAILED: 'red'\n    };\n    return colors[status] || 'default';\n  };\n  const getTransactionTypeText = type => {\n    const types = {\n      RECEIVE: '入库',\n      ISSUE: '出库',\n      ADJUST: '调整',\n      TRANSFER: '转移',\n      RETURN: '退库'\n    };\n    return types[type] || type;\n  };\n  const getTransactionTypeColor = type => {\n    const colors = {\n      RECEIVE: 'green',\n      ISSUE: 'red',\n      ADJUST: 'orange',\n      TRANSFER: 'blue',\n      RETURN: 'purple'\n    };\n    return colors[type] || 'default';\n  };\n  const filteredBatches = batches.filter(batch => {\n    if (searchKeyword && !(batch.batchNumber.toLowerCase().includes(searchKeyword.toLowerCase()) || batch.materialCode.toLowerCase().includes(searchKeyword.toLowerCase()) || batch.materialName.toLowerCase().includes(searchKeyword.toLowerCase()) || batch.supplier.toLowerCase().includes(searchKeyword.toLowerCase()))) {\n      return false;\n    }\n    if (statusFilter && batch.status !== statusFilter) {\n      return false;\n    }\n    if (supplierFilter && batch.supplier !== supplierFilter) {\n      return false;\n    }\n    if (dateRange && dateRange[0] && dateRange[1]) {\n      const receivedDate = new Date(batch.receivedDate);\n      if (receivedDate < dateRange[0].toDate() || receivedDate > dateRange[1].toDate()) {\n        return false;\n      }\n    }\n    return true;\n  });\n  const batchColumns = [{\n    title: '批次号',\n    dataIndex: 'batchNumber',\n    key: 'batchNumber',\n    width: 120,\n    fixed: 'left',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: record.qrCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物料信息',\n    key: 'material',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: record.materialCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: record.materialName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: record.specification\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '供应商',\n    dataIndex: 'supplier',\n    key: 'supplier',\n    width: 120\n  }, {\n    title: '收货日期',\n    dataIndex: 'receivedDate',\n    key: 'receivedDate',\n    width: 100,\n    render: date => formatDate(date)\n  }, {\n    title: '有效期',\n    dataIndex: 'expiryDate',\n    key: 'expiryDate',\n    width: 100,\n    render: date => date ? formatDate(date) : '-'\n  }, {\n    title: '库存数量',\n    key: 'quantity',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        children: [\"\\u5F53\\u524D: \", record.currentQuantity, \" \", record.unit]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: [\"\\u53EF\\u7528: \", record.availableQuantity, \" | \\u9884\\u7559: \", record.reservedQuantity]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '库存率',\n    key: 'stockRate',\n    width: 100,\n    render: (_, record) => {\n      const rate = record.currentQuantity / record.originalQuantity * 100;\n      return /*#__PURE__*/_jsxDEV(Progress, {\n        percent: rate,\n        size: \"small\",\n        status: rate < 20 ? 'exception' : rate < 50 ? 'active' : 'success',\n        showInfo: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '库位',\n    dataIndex: 'location',\n    key: 'location',\n    width: 100\n  }, {\n    title: '状态',\n    key: 'status',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: 0,\n      children: [/*#__PURE__*/_jsxDEV(Tag, {\n        color: getStatusColor(record.status),\n        children: record.status === 'AVAILABLE' ? '可用' : record.status === 'RESERVED' ? '预留' : record.status === 'EXPIRED' ? '过期' : record.status === 'DAMAGED' ? '损坏' : '已消耗'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: getQualityStatusColor(record.qualityStatus),\n        size: \"small\",\n        children: record.qualityStatus === 'PASSED' ? '合格' : record.qualityStatus === 'PENDING' ? '待检' : '不合格'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '总价值',\n    dataIndex: 'totalValue',\n    key: 'totalValue',\n    width: 100,\n    render: value => formatCurrency(value)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 120,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewDetail(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u4E8C\\u7EF4\\u7801\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 21\n          }, this),\n          onClick: () => {\n            // TODO: 显示二维码\n            message.info('二维码功能开发中');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 9\n    }, this)\n  }];\n  const renderStatistics = () => /*#__PURE__*/_jsxDEV(Row, {\n    gutter: [16, 16],\n    style: {\n      marginBottom: 16\n    },\n    children: [/*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 8,\n      md: 6,\n      lg: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u603B\\u6279\\u6B21\\u6570\",\n          value: statistics.totalBatches,\n          prefix: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 8,\n      md: 6,\n      lg: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u6D3B\\u8DC3\\u6279\\u6B21\",\n          value: statistics.activeBatches,\n          valueStyle: {\n            color: '#3f8600'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 8,\n      md: 6,\n      lg: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u8FC7\\u671F\\u6279\\u6B21\",\n          value: statistics.expiredBatches,\n          valueStyle: {\n            color: '#cf1322'\n          },\n          prefix: /*#__PURE__*/_jsxDEV(AlertOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 8,\n      md: 6,\n      lg: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u4F4E\\u5E93\\u5B58\\u6279\\u6B21\",\n          value: statistics.lowStockBatches,\n          valueStyle: {\n            color: '#fa8c16'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 8,\n      md: 6,\n      lg: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u603B\\u4EF7\\u503C\",\n          value: statistics.totalValue,\n          formatter: value => formatCurrency(Number(value))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      xs: 12,\n      sm: 8,\n      md: 6,\n      lg: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u5E73\\u5747\\u5E93\\u9F84\",\n          value: statistics.averageAge,\n          suffix: \"\\u5929\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 507,\n    columnNumber: 5\n  }, this);\n  const renderBatchDetail = () => {\n    if (!selectedBatch) return null;\n    return /*#__PURE__*/_jsxDEV(Modal, {\n      title: `批次详情 - ${selectedBatch.batchNumber}`,\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        defaultActiveKey: \"info\",\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          children: /*#__PURE__*/_jsxDEV(Descriptions, {\n            column: 2,\n            bordered: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6279\\u6B21\\u53F7\",\n              children: selectedBatch.batchNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u4E8C\\u7EF4\\u7801\",\n              children: selectedBatch.qrCode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7269\\u6599\\u7F16\\u7801\",\n              children: selectedBatch.materialCode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7269\\u6599\\u540D\\u79F0\",\n              children: selectedBatch.materialName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u89C4\\u683C\",\n              children: selectedBatch.specification\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u4F9B\\u5E94\\u5546\",\n              children: selectedBatch.supplier\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6536\\u8D27\\u65E5\\u671F\",\n              children: formatDate(selectedBatch.receivedDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6709\\u6548\\u671F\",\n              children: selectedBatch.expiryDate ? formatDate(selectedBatch.expiryDate) : '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u539F\\u59CB\\u6570\\u91CF\",\n              children: [selectedBatch.originalQuantity, \" \", selectedBatch.unit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5F53\\u524D\\u6570\\u91CF\",\n              children: [selectedBatch.currentQuantity, \" \", selectedBatch.unit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u53EF\\u7528\\u6570\\u91CF\",\n              children: [selectedBatch.availableQuantity, \" \", selectedBatch.unit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u9884\\u7559\\u6570\\u91CF\",\n              children: [selectedBatch.reservedQuantity, \" \", selectedBatch.unit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5355\\u4EF7\",\n              children: formatCurrency(selectedBatch.unitPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u603B\\u4EF7\\u503C\",\n              children: formatCurrency(selectedBatch.totalValue)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5E93\\u4F4D\",\n              children: selectedBatch.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: getStatusColor(selectedBatch.status),\n                  children: selectedBatch.status === 'AVAILABLE' ? '可用' : selectedBatch.status === 'RESERVED' ? '预留' : selectedBatch.status === 'EXPIRED' ? '过期' : selectedBatch.status === 'DAMAGED' ? '损坏' : '已消耗'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: getQualityStatusColor(selectedBatch.qualityStatus),\n                  children: selectedBatch.qualityStatus === 'PASSED' ? '合格' : selectedBatch.qualityStatus === 'PENDING' ? '待检' : '不合格'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)\n        }, \"info\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u4EA4\\u6613\\u8BB0\\u5F55\",\n          children: /*#__PURE__*/_jsxDEV(Timeline, {\n            children: selectedBatch.transactions.map(transaction => /*#__PURE__*/_jsxDEV(Timeline.Item, {\n              color: getTransactionTypeColor(transaction.type),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Tag, {\n                    color: getTransactionTypeColor(transaction.type),\n                    children: getTransactionTypeText(transaction.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: [transaction.quantity > 0 ? '+' : '', transaction.quantity, \" \", selectedBatch.unit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u4F59\\u91CF: \", transaction.remainingQuantity, \" \", selectedBatch.unit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 4\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: [formatDate(transaction.timestamp), \" | \", transaction.operator, \" | \", transaction.department]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: [\"\\u53C2\\u8003: \", transaction.reference, transaction.workOrder && ` | 工单: ${transaction.workOrder}`, transaction.reason && ` | 原因: ${transaction.reason}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: [\"\\u5E93\\u4F4D: \", transaction.location]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 657,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 19\n              }, this)\n            }, transaction.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this)\n        }, \"transactions\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u6279\\u6B21\\u8DDF\\u8E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u5168\\u9762\\u8DDF\\u8E2A\\u7269\\u6599\\u6279\\u6B21\\u7684\\u5165\\u5E93\\u3001\\u51FA\\u5E93\\u3001\\u8C03\\u6574\\u7B49\\u5168\\u751F\\u547D\\u5468\\u671F\\u8BB0\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              placeholder: \"\\u641C\\u7D22\\u6279\\u6B21\\u53F7\\u3001\\u7269\\u6599\\u3001\\u4F9B\\u5E94\\u5546\",\n              allowClear: true,\n              style: {\n                width: 250\n              },\n              onSearch: setSearchKeyword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u72B6\\u6001\",\n              allowClear: true,\n              style: {\n                width: 100\n              },\n              value: statusFilter,\n              onChange: setStatusFilter,\n              options: [{\n                label: '可用',\n                value: 'AVAILABLE'\n              }, {\n                label: '预留',\n                value: 'RESERVED'\n              }, {\n                label: '过期',\n                value: 'EXPIRED'\n              }, {\n                label: '损坏',\n                value: 'DAMAGED'\n              }, {\n                label: '已消耗',\n                value: 'CONSUMED'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u4F9B\\u5E94\\u5546\",\n              allowClear: true,\n              style: {\n                width: 120\n              },\n              value: supplierFilter,\n              onChange: setSupplierFilter,\n              options: [{\n                label: '深圳射频科技',\n                value: '深圳射频科技'\n              }, {\n                label: '苏州电路板厂',\n                value: '苏州电路板厂'\n              }, {\n                label: '村田制作所',\n                value: '村田制作所'\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              placeholder: ['开始日期', '结束日期'],\n              value: dateRange,\n              onChange: setDateRange,\n              style: {\n                width: 240\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(FilterOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 29\n              }, this),\n              children: \"\\u9AD8\\u7EA7\\u7B5B\\u9009\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 29\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 674,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6279\\u6B21\\u8DDF\\u8E2A\\u63D0\\u793A\",\n        description: \"\\u7CFB\\u7EDF\\u81EA\\u52A8\\u8BB0\\u5F55\\u6BCF\\u4E2A\\u6279\\u6B21\\u7684\\u5B8C\\u6574\\u751F\\u547D\\u5468\\u671F\\uFF0C\\u5305\\u62EC\\u5165\\u5E93\\u3001\\u51FA\\u5E93\\u3001\\u8C03\\u6574\\u3001\\u8F6C\\u79FB\\u7B49\\u6240\\u6709\\u64CD\\u4F5C\\uFF0C\\u786E\\u4FDD\\u7269\\u6599\\u7684\\u5B8C\\u5168\\u53EF\\u8FFD\\u6EAF\\u6027\\u3002\",\n        type: \"info\",\n        showIcon: true,\n        closable: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 31\n            }, this), \"\\u7EDF\\u8BA1\\u6982\\u89C8\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 25\n          }, this),\n          children: [renderStatistics(), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [16, 16],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              md: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u6279\\u6B21\\u72B6\\u6001\\u5206\\u5E03\",\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u6279\\u6B21\\u72B6\\u6001\\u5206\\u5E03\\u56FE\\u8868\\u5C06\\u5728\\u6B64\\u663E\\u793A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              md: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u5E93\\u9F84\\u5206\\u6790\",\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u5E93\\u9F84\\u5206\\u6790\\u56FE\\u8868\\u5C06\\u5728\\u6B64\\u663E\\u793A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this)]\n        }, \"overview\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u6279\\u6B21\\u6E05\\u5355\",\n          children: [renderStatistics(), /*#__PURE__*/_jsxDEV(Table, {\n            columns: batchColumns,\n            dataSource: filteredBatches,\n            loading: loading,\n            rowKey: \"id\",\n            scroll: {\n              x: 1400\n            },\n            pagination: {\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 13\n          }, this)]\n        }, \"list\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 742,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 7\n    }, this), renderBatchDetail()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 672,\n    columnNumber: 5\n  }, this);\n};\n_s(BatchTrackingPage, \"O5CtIsN9q7oDa4wbTLjM7xLwyKk=\");\n_c = BatchTrackingPage;\nexport default BatchTrackingPage;\nvar _c;\n$RefreshReg$(_c, \"BatchTrackingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "DatePicker", "Modal", "Row", "Col", "Typography", "Tag", "<PERSON><PERSON><PERSON>", "message", "Tabs", "Timeline", "Descriptions", "<PERSON><PERSON>", "Statistic", "Progress", "EyeOutlined", "QrcodeOutlined", "ExportOutlined", "FilterOutlined", "BarChartOutlined", "Alert<PERSON>ut<PERSON>", "formatCurrency", "formatDate", "jsxDEV", "_jsxDEV", "Title", "Text", "Search", "TabPane", "RangePicker", "BatchTrackingPage", "_s", "loading", "setLoading", "batches", "setBatches", "statistics", "setStatistics", "totalBatches", "activeBatches", "expiredBatches", "lowStockBatches", "totalValue", "averageAge", "turnoverRate", "searchKeyword", "setSearchKeyword", "statusFilter", "setStatus<PERSON>ilter", "supplierFilter", "setSupplier<PERSON><PERSON>er", "date<PERSON><PERSON><PERSON>", "setDateRange", "selected<PERSON><PERSON>", "setSelectedBatch", "detailModalVisible", "setDetailModalVisible", "activeTab", "setActiveTab", "mockBatches", "id", "batchNumber", "materialId", "materialCode", "materialName", "specification", "supplier", "receivedDate", "expiryDate", "originalQuantity", "currentQuantity", "reservedQuantity", "availableQuantity", "unit", "unitPrice", "location", "status", "qualityStatus", "qrCode", "transactions", "type", "quantity", "remainingQuantity", "operator", "department", "reference", "timestamp", "workOrder", "loadBatchData", "loadStatistics", "error", "stats", "length", "filter", "b", "reduce", "sum", "handleViewDetail", "batch", "handleExport", "success", "getStatusColor", "colors", "AVAILABLE", "RESERVED", "EXPIRED", "DAMAGED", "CONSUMED", "getQualityStatusColor", "PASSED", "PENDING", "FAILED", "getTransactionTypeText", "types", "RECEIVE", "ISSUE", "ADJUST", "TRANSFER", "RETURN", "getTransactionTypeColor", "filteredBatches", "toLowerCase", "includes", "Date", "toDate", "batchColumns", "title", "dataIndex", "key", "width", "fixed", "render", "text", "record", "direction", "size", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "_", "date", "rate", "percent", "showInfo", "color", "value", "icon", "onClick", "info", "renderStatistics", "gutter", "marginBottom", "xs", "sm", "md", "lg", "prefix", "valueStyle", "formatter", "Number", "suffix", "renderBatchDetail", "open", "onCancel", "footer", "defaultActiveKey", "tab", "column", "bordered", "<PERSON><PERSON>", "label", "map", "transaction", "marginTop", "reason", "justify", "align", "level", "margin", "placeholder", "allowClear", "onSearch", "onChange", "options", "description", "showIcon", "closable", "active<PERSON><PERSON>", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/inventory/BatchTrackingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  DatePicker,\n  Modal,\n  Form,\n  Row,\n  Col,\n  Typography,\n  Tag,\n  Tooltip,\n  message,\n  Tabs,\n  Timeline,\n  Descriptions,\n  Alert,\n  Statistic,\n  Progress,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  HistoryOutlined,\n  QrcodeOutlined,\n  ExportOutlined,\n  FilterOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  AlertOutlined,\n} from '@ant-design/icons';\nimport { formatCurrency, formatDate } from '../../utils/format';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { TabPane } = Tabs;\nconst { RangePicker } = DatePicker;\n\ninterface BatchInfo {\n  id: string;\n  batchNumber: string;\n  materialId: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  supplier: string;\n  receivedDate: string;\n  expiryDate?: string;\n  originalQuantity: number;\n  currentQuantity: number;\n  reservedQuantity: number;\n  availableQuantity: number;\n  unit: string;\n  unitPrice: number;\n  totalValue: number;\n  location: string;\n  status: 'AVAILABLE' | 'RESERVED' | 'EXPIRED' | 'DAMAGED' | 'CONSUMED';\n  qualityStatus: 'PASSED' | 'PENDING' | 'FAILED';\n  transactions: BatchTransaction[];\n  qrCode: string;\n}\n\ninterface BatchTransaction {\n  id: string;\n  type: 'RECEIVE' | 'ISSUE' | 'ADJUST' | 'TRANSFER' | 'RETURN';\n  quantity: number;\n  remainingQuantity: number;\n  operator: string;\n  department: string;\n  workOrder?: string;\n  reference: string;\n  reason?: string;\n  timestamp: string;\n  location: string;\n}\n\ninterface BatchStatistics {\n  totalBatches: number;\n  activeBatches: number;\n  expiredBatches: number;\n  lowStockBatches: number;\n  totalValue: number;\n  averageAge: number;\n  turnoverRate: number;\n}\n\nconst BatchTrackingPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [batches, setBatches] = useState<BatchInfo[]>([]);\n  const [statistics, setStatistics] = useState<BatchStatistics>({\n    totalBatches: 0,\n    activeBatches: 0,\n    expiredBatches: 0,\n    lowStockBatches: 0,\n    totalValue: 0,\n    averageAge: 0,\n    turnoverRate: 0,\n  });\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string | undefined>();\n  const [supplierFilter, setSupplierFilter] = useState<string | undefined>();\n  const [dateRange, setDateRange] = useState<[any, any] | null>(null);\n  const [selectedBatch, setSelectedBatch] = useState<BatchInfo | null>(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [activeTab, setActiveTab] = useState('list');\n\n  // 模拟数据\n  const mockBatches: BatchInfo[] = [\n    {\n      id: '1',\n      batchNumber: 'B2024030001',\n      materialId: 'M001',\n      materialCode: 'RF-PA-001',\n      materialName: 'RF功率放大器',\n      specification: '2.4GHz 20W',\n      supplier: '深圳射频科技',\n      receivedDate: '2024-03-01T08:00:00Z',\n      expiryDate: '2025-03-01T00:00:00Z',\n      originalQuantity: 100,\n      currentQuantity: 75,\n      reservedQuantity: 10,\n      availableQuantity: 65,\n      unit: 'PCS',\n      unitPrice: 150.00,\n      totalValue: 11250.00,\n      location: 'A区-01-03',\n      status: 'AVAILABLE',\n      qualityStatus: 'PASSED',\n      qrCode: 'QR_B2024030001',\n      transactions: [\n        {\n          id: 'T001',\n          type: 'RECEIVE',\n          quantity: 100,\n          remainingQuantity: 100,\n          operator: '张三',\n          department: '仓储部',\n          reference: 'PO-2024-001',\n          timestamp: '2024-03-01T08:00:00Z',\n          location: 'A区-01-03',\n        },\n        {\n          id: 'T002',\n          type: 'ISSUE',\n          quantity: -25,\n          remainingQuantity: 75,\n          operator: '李四',\n          department: '生产部',\n          workOrder: 'WO-2024-001',\n          reference: 'ISS-2024-001',\n          timestamp: '2024-03-05T10:30:00Z',\n          location: 'A区-01-03',\n        },\n      ],\n    },\n    {\n      id: '2',\n      batchNumber: 'B2024030002',\n      materialId: 'M002',\n      materialCode: 'PCB-MAIN-001',\n      materialName: '主控PCB板',\n      specification: '4层板 FR4',\n      supplier: '苏州电路板厂',\n      receivedDate: '2024-03-02T09:15:00Z',\n      expiryDate: '2026-03-02T00:00:00Z',\n      originalQuantity: 200,\n      currentQuantity: 180,\n      reservedQuantity: 20,\n      availableQuantity: 160,\n      unit: 'PCS',\n      unitPrice: 85.00,\n      totalValue: 15300.00,\n      location: 'A区-02-01',\n      status: 'AVAILABLE',\n      qualityStatus: 'PASSED',\n      qrCode: 'QR_B2024030002',\n      transactions: [\n        {\n          id: 'T003',\n          type: 'RECEIVE',\n          quantity: 200,\n          remainingQuantity: 200,\n          operator: '王五',\n          department: '仓储部',\n          reference: 'PO-2024-002',\n          timestamp: '2024-03-02T09:15:00Z',\n          location: 'A区-02-01',\n        },\n        {\n          id: 'T004',\n          type: 'ISSUE',\n          quantity: -20,\n          remainingQuantity: 180,\n          operator: '赵六',\n          department: '生产部',\n          workOrder: 'WO-2024-002',\n          reference: 'ISS-2024-002',\n          timestamp: '2024-03-06T14:20:00Z',\n          location: 'A区-02-01',\n        },\n      ],\n    },\n    {\n      id: '3',\n      batchNumber: 'B2024020015',\n      materialId: 'M003',\n      materialCode: 'CAP-CER-001',\n      materialName: '陶瓷电容',\n      specification: '0805 10uF',\n      supplier: '村田制作所',\n      receivedDate: '2024-02-15T11:30:00Z',\n      expiryDate: '2024-04-15T00:00:00Z',\n      originalQuantity: 5000,\n      currentQuantity: 500,\n      reservedQuantity: 0,\n      availableQuantity: 500,\n      unit: 'PCS',\n      unitPrice: 0.25,\n      totalValue: 125.00,\n      location: 'B区-03-05',\n      status: 'EXPIRED',\n      qualityStatus: 'PENDING',\n      qrCode: 'QR_B2024020015',\n      transactions: [\n        {\n          id: 'T005',\n          type: 'RECEIVE',\n          quantity: 5000,\n          remainingQuantity: 5000,\n          operator: '孙七',\n          department: '仓储部',\n          reference: 'PO-2024-003',\n          timestamp: '2024-02-15T11:30:00Z',\n          location: 'B区-03-05',\n        },\n        {\n          id: 'T006',\n          type: 'ISSUE',\n          quantity: -4500,\n          remainingQuantity: 500,\n          operator: '周八',\n          department: '生产部',\n          workOrder: 'WO-2024-003',\n          reference: 'ISS-2024-003',\n          timestamp: '2024-03-10T16:45:00Z',\n          location: 'B区-03-05',\n        },\n      ],\n    },\n  ];\n\n  useEffect(() => {\n    loadBatchData();\n    loadStatistics();\n  }, []);\n\n  const loadBatchData = async () => {\n    try {\n      setLoading(true);\n      // TODO: 调用API获取批次数据\n      setBatches(mockBatches);\n    } catch (error) {\n      message.error('加载批次数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistics = async () => {\n    try {\n      // TODO: 调用API获取统计数据\n      const stats: BatchStatistics = {\n        totalBatches: mockBatches.length,\n        activeBatches: mockBatches.filter(b => b.status === 'AVAILABLE').length,\n        expiredBatches: mockBatches.filter(b => b.status === 'EXPIRED').length,\n        lowStockBatches: mockBatches.filter(b => b.currentQuantity < b.originalQuantity * 0.2).length,\n        totalValue: mockBatches.reduce((sum, b) => sum + b.totalValue, 0),\n        averageAge: 45,\n        turnoverRate: 2.5,\n      };\n      setStatistics(stats);\n    } catch (error) {\n      message.error('加载统计数据失败');\n    }\n  };\n\n  const handleViewDetail = (batch: BatchInfo) => {\n    setSelectedBatch(batch);\n    setDetailModalVisible(true);\n  };\n\n  const handleExport = () => {\n    // TODO: 实现导出功能\n    message.success('导出功能开发中');\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors = {\n      AVAILABLE: 'green',\n      RESERVED: 'blue',\n      EXPIRED: 'red',\n      DAMAGED: 'orange',\n      CONSUMED: 'gray',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n  const getQualityStatusColor = (status: string) => {\n    const colors = {\n      PASSED: 'green',\n      PENDING: 'orange',\n      FAILED: 'red',\n    };\n    return colors[status as keyof typeof colors] || 'default';\n  };\n\n  const getTransactionTypeText = (type: string) => {\n    const types = {\n      RECEIVE: '入库',\n      ISSUE: '出库',\n      ADJUST: '调整',\n      TRANSFER: '转移',\n      RETURN: '退库',\n    };\n    return types[type as keyof typeof types] || type;\n  };\n\n  const getTransactionTypeColor = (type: string) => {\n    const colors = {\n      RECEIVE: 'green',\n      ISSUE: 'red',\n      ADJUST: 'orange',\n      TRANSFER: 'blue',\n      RETURN: 'purple',\n    };\n    return colors[type as keyof typeof colors] || 'default';\n  };\n\n  const filteredBatches = batches.filter(batch => {\n    if (searchKeyword && !(\n      batch.batchNumber.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n      batch.materialCode.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n      batch.materialName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\n      batch.supplier.toLowerCase().includes(searchKeyword.toLowerCase())\n    )) {\n      return false;\n    }\n    if (statusFilter && batch.status !== statusFilter) {\n      return false;\n    }\n    if (supplierFilter && batch.supplier !== supplierFilter) {\n      return false;\n    }\n    if (dateRange && dateRange[0] && dateRange[1]) {\n      const receivedDate = new Date(batch.receivedDate);\n      if (receivedDate < dateRange[0].toDate() || receivedDate > dateRange[1].toDate()) {\n        return false;\n      }\n    }\n    return true;\n  });\n\n  const batchColumns = [\n    {\n      title: '批次号',\n      dataIndex: 'batchNumber',\n      key: 'batchNumber',\n      width: 120,\n      fixed: 'left' as const,\n      render: (text: string, record: BatchInfo) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{text}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.qrCode}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '物料信息',\n      key: 'material',\n      width: 200,\n      render: (_: any, record: BatchInfo) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{record.materialCode}</Text>\n          <Text>{record.materialName}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.specification}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      key: 'supplier',\n      width: 120,\n    },\n    {\n      title: '收货日期',\n      dataIndex: 'receivedDate',\n      key: 'receivedDate',\n      width: 100,\n      render: (date: string) => formatDate(date),\n    },\n    {\n      title: '有效期',\n      dataIndex: 'expiryDate',\n      key: 'expiryDate',\n      width: 100,\n      render: (date: string) => date ? formatDate(date) : '-',\n    },\n    {\n      title: '库存数量',\n      key: 'quantity',\n      width: 120,\n      render: (_: any, record: BatchInfo) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text>当前: {record.currentQuantity} {record.unit}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            可用: {record.availableQuantity} | 预留: {record.reservedQuantity}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '库存率',\n      key: 'stockRate',\n      width: 100,\n      render: (_: any, record: BatchInfo) => {\n        const rate = (record.currentQuantity / record.originalQuantity) * 100;\n        return (\n          <Progress\n            percent={rate}\n            size=\"small\"\n            status={rate < 20 ? 'exception' : rate < 50 ? 'active' : 'success'}\n            showInfo={false}\n          />\n        );\n      },\n    },\n    {\n      title: '库位',\n      dataIndex: 'location',\n      key: 'location',\n      width: 100,\n    },\n    {\n      title: '状态',\n      key: 'status',\n      width: 120,\n      render: (_: any, record: BatchInfo) => (\n        <Space direction=\"vertical\" size={0}>\n          <Tag color={getStatusColor(record.status)}>\n            {record.status === 'AVAILABLE' ? '可用' :\n             record.status === 'RESERVED' ? '预留' :\n             record.status === 'EXPIRED' ? '过期' :\n             record.status === 'DAMAGED' ? '损坏' : '已消耗'}\n          </Tag>\n          <Tag color={getQualityStatusColor(record.qualityStatus)} size=\"small\">\n            {record.qualityStatus === 'PASSED' ? '合格' :\n             record.qualityStatus === 'PENDING' ? '待检' : '不合格'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '总价值',\n      dataIndex: 'totalValue',\n      key: 'totalValue',\n      width: 100,\n      render: (value: number) => formatCurrency(value),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      fixed: 'right' as const,\n      render: (_: any, record: BatchInfo) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewDetail(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"查看二维码\">\n            <Button\n              type=\"text\"\n              icon={<QrcodeOutlined />}\n              onClick={() => {\n                // TODO: 显示二维码\n                message.info('二维码功能开发中');\n              }}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  const renderStatistics = () => (\n    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"总批次数\"\n            value={statistics.totalBatches}\n            prefix={<BarChartOutlined />}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"活跃批次\"\n            value={statistics.activeBatches}\n            valueStyle={{ color: '#3f8600' }}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"过期批次\"\n            value={statistics.expiredBatches}\n            valueStyle={{ color: '#cf1322' }}\n            prefix={<AlertOutlined />}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"低库存批次\"\n            value={statistics.lowStockBatches}\n            valueStyle={{ color: '#fa8c16' }}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"总价值\"\n            value={statistics.totalValue}\n            formatter={(value) => formatCurrency(Number(value))}\n          />\n        </Card>\n      </Col>\n      <Col xs={12} sm={8} md={6} lg={4}>\n        <Card size=\"small\">\n          <Statistic\n            title=\"平均库龄\"\n            value={statistics.averageAge}\n            suffix=\"天\"\n          />\n        </Card>\n      </Col>\n    </Row>\n  );\n\n  const renderBatchDetail = () => {\n    if (!selectedBatch) return null;\n\n    return (\n      <Modal\n        title={`批次详情 - ${selectedBatch.batchNumber}`}\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>,\n        ]}\n        width={800}\n      >\n        <Tabs defaultActiveKey=\"info\">\n          <TabPane tab=\"基本信息\" key=\"info\">\n            <Descriptions column={2} bordered size=\"small\">\n              <Descriptions.Item label=\"批次号\">{selectedBatch.batchNumber}</Descriptions.Item>\n              <Descriptions.Item label=\"二维码\">{selectedBatch.qrCode}</Descriptions.Item>\n              <Descriptions.Item label=\"物料编码\">{selectedBatch.materialCode}</Descriptions.Item>\n              <Descriptions.Item label=\"物料名称\">{selectedBatch.materialName}</Descriptions.Item>\n              <Descriptions.Item label=\"规格\">{selectedBatch.specification}</Descriptions.Item>\n              <Descriptions.Item label=\"供应商\">{selectedBatch.supplier}</Descriptions.Item>\n              <Descriptions.Item label=\"收货日期\">{formatDate(selectedBatch.receivedDate)}</Descriptions.Item>\n              <Descriptions.Item label=\"有效期\">\n                {selectedBatch.expiryDate ? formatDate(selectedBatch.expiryDate) : '-'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"原始数量\">\n                {selectedBatch.originalQuantity} {selectedBatch.unit}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"当前数量\">\n                {selectedBatch.currentQuantity} {selectedBatch.unit}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"可用数量\">\n                {selectedBatch.availableQuantity} {selectedBatch.unit}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"预留数量\">\n                {selectedBatch.reservedQuantity} {selectedBatch.unit}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"单价\">{formatCurrency(selectedBatch.unitPrice)}</Descriptions.Item>\n              <Descriptions.Item label=\"总价值\">{formatCurrency(selectedBatch.totalValue)}</Descriptions.Item>\n              <Descriptions.Item label=\"库位\">{selectedBatch.location}</Descriptions.Item>\n              <Descriptions.Item label=\"状态\">\n                <Space>\n                  <Tag color={getStatusColor(selectedBatch.status)}>\n                    {selectedBatch.status === 'AVAILABLE' ? '可用' :\n                     selectedBatch.status === 'RESERVED' ? '预留' :\n                     selectedBatch.status === 'EXPIRED' ? '过期' :\n                     selectedBatch.status === 'DAMAGED' ? '损坏' : '已消耗'}\n                  </Tag>\n                  <Tag color={getQualityStatusColor(selectedBatch.qualityStatus)}>\n                    {selectedBatch.qualityStatus === 'PASSED' ? '合格' :\n                     selectedBatch.qualityStatus === 'PENDING' ? '待检' : '不合格'}\n                  </Tag>\n                </Space>\n              </Descriptions.Item>\n            </Descriptions>\n          </TabPane>\n          <TabPane tab=\"交易记录\" key=\"transactions\">\n            <Timeline>\n              {selectedBatch.transactions.map((transaction) => (\n                <Timeline.Item\n                  key={transaction.id}\n                  color={getTransactionTypeColor(transaction.type)}\n                >\n                  <div>\n                    <Space>\n                      <Tag color={getTransactionTypeColor(transaction.type)}>\n                        {getTransactionTypeText(transaction.type)}\n                      </Tag>\n                      <Text strong>\n                        {transaction.quantity > 0 ? '+' : ''}{transaction.quantity} {selectedBatch.unit}\n                      </Text>\n                      <Text type=\"secondary\">\n                        余量: {transaction.remainingQuantity} {selectedBatch.unit}\n                      </Text>\n                    </Space>\n                    <div style={{ marginTop: 4 }}>\n                      <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                        {formatDate(transaction.timestamp)} | {transaction.operator} | {transaction.department}\n                      </Text>\n                    </div>\n                    <div style={{ marginTop: 2 }}>\n                      <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                        参考: {transaction.reference}\n                        {transaction.workOrder && ` | 工单: ${transaction.workOrder}`}\n                        {transaction.reason && ` | 原因: ${transaction.reason}`}\n                      </Text>\n                    </div>\n                    <div style={{ marginTop: 2 }}>\n                      <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                        库位: {transaction.location}\n                      </Text>\n                    </div>\n                  </div>\n                </Timeline.Item>\n              ))}\n            </Timeline>\n          </TabPane>\n        </Tabs>\n      </Modal>\n    );\n  };\n\n  return (\n    <div>\n      <Card>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginBottom: 16 }}>\n          <Col>\n            <Title level={4} style={{ margin: 0 }}>\n              批次跟踪\n            </Title>\n            <Text type=\"secondary\">\n              全面跟踪物料批次的入库、出库、调整等全生命周期记录\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Search\n                placeholder=\"搜索批次号、物料、供应商\"\n                allowClear\n                style={{ width: 250 }}\n                onSearch={setSearchKeyword}\n              />\n              <Select\n                placeholder=\"状态\"\n                allowClear\n                style={{ width: 100 }}\n                value={statusFilter}\n                onChange={setStatusFilter}\n                options={[\n                  { label: '可用', value: 'AVAILABLE' },\n                  { label: '预留', value: 'RESERVED' },\n                  { label: '过期', value: 'EXPIRED' },\n                  { label: '损坏', value: 'DAMAGED' },\n                  { label: '已消耗', value: 'CONSUMED' },\n                ]}\n              />\n              <Select\n                placeholder=\"供应商\"\n                allowClear\n                style={{ width: 120 }}\n                value={supplierFilter}\n                onChange={setSupplierFilter}\n                options={[\n                  { label: '深圳射频科技', value: '深圳射频科技' },\n                  { label: '苏州电路板厂', value: '苏州电路板厂' },\n                  { label: '村田制作所', value: '村田制作所' },\n                ]}\n              />\n              <RangePicker\n                placeholder={['开始日期', '结束日期']}\n                value={dateRange}\n                onChange={setDateRange}\n                style={{ width: 240 }}\n              />\n              <Button icon={<FilterOutlined />}>\n                高级筛选\n              </Button>\n              <Button icon={<ExportOutlined />} onClick={handleExport}>\n                导出\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n\n        <Alert\n          message=\"批次跟踪提示\"\n          description=\"系统自动记录每个批次的完整生命周期，包括入库、出库、调整、转移等所有操作，确保物料的完全可追溯性。\"\n          type=\"info\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab={<span><BarChartOutlined />统计概览</span>} key=\"overview\">\n            {renderStatistics()}\n            <Row gutter={[16, 16]}>\n              <Col xs={24} md={12}>\n                <Card title=\"批次状态分布\" size=\"small\">\n                  <Text type=\"secondary\">批次状态分布图表将在此显示</Text>\n                </Card>\n              </Col>\n              <Col xs={24} md={12}>\n                <Card title=\"库龄分析\" size=\"small\">\n                  <Text type=\"secondary\">库龄分析图表将在此显示</Text>\n                </Card>\n              </Col>\n            </Row>\n          </TabPane>\n\n          <TabPane tab=\"批次清单\" key=\"list\">\n            {renderStatistics()}\n            <Table\n              columns={batchColumns}\n              dataSource={filteredBatches}\n              loading={loading}\n              rowKey=\"id\"\n              scroll={{ x: 1400 }}\n              pagination={{\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) =>\n                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,\n              }}\n            />\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {renderBatchDetail()}\n    </div>\n  );\n};\n\nexport default BatchTrackingPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EAELC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,KAAK,EACLC,SAAS,EACTC,QAAQ,QACH,MAAM;AACb,SAEEC,WAAW,EAEXC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,aAAa,QACR,mBAAmB;AAC1B,SAASC,cAAc,EAAEC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGrB,UAAU;AAClC,MAAM;EAAEsB;AAAO,CAAC,GAAG5B,KAAK;AACxB,MAAM;EAAE6B;AAAQ,CAAC,GAAGnB,IAAI;AACxB,MAAM;EAAEoB;AAAY,CAAC,GAAG5B,UAAU;AAkDlC,MAAM6B,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAc,EAAE,CAAC;EACvD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAkB;IAC5D6C,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAqB,CAAC;EACtE,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAqB,CAAC;EAC1E,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAoB,IAAI,CAAC;EACnE,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAmB,IAAI,CAAC;EAC1E,MAAM,CAAC8D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,MAAM,CAAC;;EAElD;EACA,MAAMkE,WAAwB,GAAG,CAC/B;IACEC,EAAE,EAAE,GAAG;IACPC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,WAAW;IACzBC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,YAAY;IAC3BC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,sBAAsB;IACpCC,UAAU,EAAE,sBAAsB;IAClCC,gBAAgB,EAAE,GAAG;IACrBC,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,MAAM;IACjBhC,UAAU,EAAE,QAAQ;IACpBiC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,WAAW;IACnBC,aAAa,EAAE,QAAQ;IACvBC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,CACZ;MACEnB,EAAE,EAAE,MAAM;MACVoB,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,GAAG;MACbC,iBAAiB,EAAE,GAAG;MACtBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,aAAa;MACxBC,SAAS,EAAE,sBAAsB;MACjCX,QAAQ,EAAE;IACZ,CAAC,EACD;MACEf,EAAE,EAAE,MAAM;MACVoB,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,EAAE;MACbC,iBAAiB,EAAE,EAAE;MACrBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,KAAK;MACjBG,SAAS,EAAE,aAAa;MACxBF,SAAS,EAAE,cAAc;MACzBC,SAAS,EAAE,sBAAsB;MACjCX,QAAQ,EAAE;IACZ,CAAC;EAEL,CAAC,EACD;IACEf,EAAE,EAAE,GAAG;IACPC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,QAAQ;IACtBC,aAAa,EAAE,SAAS;IACxBC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,sBAAsB;IACpCC,UAAU,EAAE,sBAAsB;IAClCC,gBAAgB,EAAE,GAAG;IACrBC,eAAe,EAAE,GAAG;IACpBC,gBAAgB,EAAE,EAAE;IACpBC,iBAAiB,EAAE,GAAG;IACtBC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,KAAK;IAChBhC,UAAU,EAAE,QAAQ;IACpBiC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,WAAW;IACnBC,aAAa,EAAE,QAAQ;IACvBC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,CACZ;MACEnB,EAAE,EAAE,MAAM;MACVoB,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,GAAG;MACbC,iBAAiB,EAAE,GAAG;MACtBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,aAAa;MACxBC,SAAS,EAAE,sBAAsB;MACjCX,QAAQ,EAAE;IACZ,CAAC,EACD;MACEf,EAAE,EAAE,MAAM;MACVoB,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,EAAE;MACbC,iBAAiB,EAAE,GAAG;MACtBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,KAAK;MACjBG,SAAS,EAAE,aAAa;MACxBF,SAAS,EAAE,cAAc;MACzBC,SAAS,EAAE,sBAAsB;MACjCX,QAAQ,EAAE;IACZ,CAAC;EAEL,CAAC,EACD;IACEf,EAAE,EAAE,GAAG;IACPC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,aAAa;IAC3BC,YAAY,EAAE,MAAM;IACpBC,aAAa,EAAE,WAAW;IAC1BC,QAAQ,EAAE,OAAO;IACjBC,YAAY,EAAE,sBAAsB;IACpCC,UAAU,EAAE,sBAAsB;IAClCC,gBAAgB,EAAE,IAAI;IACtBC,eAAe,EAAE,GAAG;IACpBC,gBAAgB,EAAE,CAAC;IACnBC,iBAAiB,EAAE,GAAG;IACtBC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,IAAI;IACfhC,UAAU,EAAE,MAAM;IAClBiC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,SAAS;IACxBC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,CACZ;MACEnB,EAAE,EAAE,MAAM;MACVoB,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAE,IAAI;MACvBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,aAAa;MACxBC,SAAS,EAAE,sBAAsB;MACjCX,QAAQ,EAAE;IACZ,CAAC,EACD;MACEf,EAAE,EAAE,MAAM;MACVoB,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,IAAI;MACfC,iBAAiB,EAAE,GAAG;MACtBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,KAAK;MACjBG,SAAS,EAAE,aAAa;MACxBF,SAAS,EAAE,cAAc;MACzBC,SAAS,EAAE,sBAAsB;MACjCX,QAAQ,EAAE;IACZ,CAAC;EAEL,CAAC,CACF;EAEDjF,SAAS,CAAC,MAAM;IACd8F,aAAa,CAAC,CAAC;IACfC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFvD,UAAU,CAAC,IAAI,CAAC;MAChB;MACAE,UAAU,CAACwB,WAAW,CAAC;IACzB,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdlF,OAAO,CAACkF,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,MAAME,KAAsB,GAAG;QAC7BrD,YAAY,EAAEqB,WAAW,CAACiC,MAAM;QAChCrD,aAAa,EAAEoB,WAAW,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,MAAM,KAAK,WAAW,CAAC,CAACgB,MAAM;QACvEpD,cAAc,EAAEmB,WAAW,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,MAAM,KAAK,SAAS,CAAC,CAACgB,MAAM;QACtEnD,eAAe,EAAEkB,WAAW,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxB,eAAe,GAAGwB,CAAC,CAACzB,gBAAgB,GAAG,GAAG,CAAC,CAACuB,MAAM;QAC7FlD,UAAU,EAAEiB,WAAW,CAACoC,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAACpD,UAAU,EAAE,CAAC,CAAC;QACjEC,UAAU,EAAE,EAAE;QACdC,YAAY,EAAE;MAChB,CAAC;MACDP,aAAa,CAACsD,KAAK,CAAC;IACtB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdlF,OAAO,CAACkF,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMO,gBAAgB,GAAIC,KAAgB,IAAK;IAC7C5C,gBAAgB,CAAC4C,KAAK,CAAC;IACvB1C,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM2C,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA3F,OAAO,CAAC4F,OAAO,CAAC,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMC,cAAc,GAAIzB,MAAc,IAAK;IACzC,MAAM0B,MAAM,GAAG;MACbC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOL,MAAM,CAAC1B,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;EAED,MAAMgC,qBAAqB,GAAIhC,MAAc,IAAK;IAChD,MAAM0B,MAAM,GAAG;MACbO,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE;IACV,CAAC;IACD,OAAOT,MAAM,CAAC1B,MAAM,CAAwB,IAAI,SAAS;EAC3D,CAAC;EAED,MAAMoC,sBAAsB,GAAIhC,IAAY,IAAK;IAC/C,MAAMiC,KAAK,GAAG;MACZC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAOL,KAAK,CAACjC,IAAI,CAAuB,IAAIA,IAAI;EAClD,CAAC;EAED,MAAMuC,uBAAuB,GAAIvC,IAAY,IAAK;IAChD,MAAMsB,MAAM,GAAG;MACbY,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,QAAQ;MAChBC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,OAAOhB,MAAM,CAACtB,IAAI,CAAwB,IAAI,SAAS;EACzD,CAAC;EAED,MAAMwC,eAAe,GAAGtF,OAAO,CAAC2D,MAAM,CAACK,KAAK,IAAI;IAC9C,IAAIrD,aAAa,IAAI,EACnBqD,KAAK,CAACrC,WAAW,CAAC4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,aAAa,CAAC4E,WAAW,CAAC,CAAC,CAAC,IACrEvB,KAAK,CAACnC,YAAY,CAAC0D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,aAAa,CAAC4E,WAAW,CAAC,CAAC,CAAC,IACtEvB,KAAK,CAAClC,YAAY,CAACyD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,aAAa,CAAC4E,WAAW,CAAC,CAAC,CAAC,IACtEvB,KAAK,CAAChC,QAAQ,CAACuD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,aAAa,CAAC4E,WAAW,CAAC,CAAC,CAAC,CACnE,EAAE;MACD,OAAO,KAAK;IACd;IACA,IAAI1E,YAAY,IAAImD,KAAK,CAACtB,MAAM,KAAK7B,YAAY,EAAE;MACjD,OAAO,KAAK;IACd;IACA,IAAIE,cAAc,IAAIiD,KAAK,CAAChC,QAAQ,KAAKjB,cAAc,EAAE;MACvD,OAAO,KAAK;IACd;IACA,IAAIE,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,IAAIA,SAAS,CAAC,CAAC,CAAC,EAAE;MAC7C,MAAMgB,YAAY,GAAG,IAAIwD,IAAI,CAACzB,KAAK,CAAC/B,YAAY,CAAC;MACjD,IAAIA,YAAY,GAAGhB,SAAS,CAAC,CAAC,CAAC,CAACyE,MAAM,CAAC,CAAC,IAAIzD,YAAY,GAAGhB,SAAS,CAAC,CAAC,CAAC,CAACyE,MAAM,CAAC,CAAC,EAAE;QAChF,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,CACnB;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,MAAe;IACtBC,MAAM,EAAEA,CAACC,IAAY,EAAEC,MAAiB,kBACtC7G,OAAA,CAAC1B,KAAK;MAACwI,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,gBAClChH,OAAA,CAACE,IAAI;QAAC+G,MAAM;QAAAD,QAAA,EAAEJ;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BrH,OAAA,CAACE,IAAI;QAACsD,IAAI,EAAC,WAAW;QAAC8D,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAChDH,MAAM,CAACvD;MAAM;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACa,CAAM,EAAEX,MAAiB,kBAChC7G,OAAA,CAAC1B,KAAK;MAACwI,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,gBAClChH,OAAA,CAACE,IAAI;QAAC+G,MAAM;QAAAD,QAAA,EAAEH,MAAM,CAACtE;MAAY;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACzCrH,OAAA,CAACE,IAAI;QAAA8G,QAAA,EAAEH,MAAM,CAACrE;MAAY;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClCrH,OAAA,CAACE,IAAI;QAACsD,IAAI,EAAC,WAAW;QAAC8D,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,EAChDH,MAAM,CAACpE;MAAa;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGc,IAAY,IAAK3H,UAAU,CAAC2H,IAAI;EAC3C,CAAC,EACD;IACEnB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGc,IAAY,IAAKA,IAAI,GAAG3H,UAAU,CAAC2H,IAAI,CAAC,GAAG;EACtD,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACa,CAAM,EAAEX,MAAiB,kBAChC7G,OAAA,CAAC1B,KAAK;MAACwI,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,gBAClChH,OAAA,CAACE,IAAI;QAAA8G,QAAA,GAAC,gBAAI,EAACH,MAAM,CAAC/D,eAAe,EAAC,GAAC,EAAC+D,MAAM,CAAC5D,IAAI;MAAA;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvDrH,OAAA,CAACE,IAAI;QAACsD,IAAI,EAAC,WAAW;QAAC8D,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAP,QAAA,GAAC,gBAC9C,EAACH,MAAM,CAAC7D,iBAAiB,EAAC,mBAAO,EAAC6D,MAAM,CAAC9D,gBAAgB;MAAA;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEX,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZE,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACa,CAAM,EAAEX,MAAiB,KAAK;MACrC,MAAMa,IAAI,GAAIb,MAAM,CAAC/D,eAAe,GAAG+D,MAAM,CAAChE,gBAAgB,GAAI,GAAG;MACrE,oBACE7C,OAAA,CAACV,QAAQ;QACPqI,OAAO,EAAED,IAAK;QACdX,IAAI,EAAC,OAAO;QACZ3D,MAAM,EAAEsE,IAAI,GAAG,EAAE,GAAG,WAAW,GAAGA,IAAI,GAAG,EAAE,GAAG,QAAQ,GAAG,SAAU;QACnEE,QAAQ,EAAE;MAAM;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEN;EACF,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACa,CAAM,EAAEX,MAAiB,kBAChC7G,OAAA,CAAC1B,KAAK;MAACwI,SAAS,EAAC,UAAU;MAACC,IAAI,EAAE,CAAE;MAAAC,QAAA,gBAClChH,OAAA,CAAClB,GAAG;QAAC+I,KAAK,EAAEhD,cAAc,CAACgC,MAAM,CAACzD,MAAM,CAAE;QAAA4D,QAAA,EACvCH,MAAM,CAACzD,MAAM,KAAK,WAAW,GAAG,IAAI,GACpCyD,MAAM,CAACzD,MAAM,KAAK,UAAU,GAAG,IAAI,GACnCyD,MAAM,CAACzD,MAAM,KAAK,SAAS,GAAG,IAAI,GAClCyD,MAAM,CAACzD,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG;MAAK;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACNrH,OAAA,CAAClB,GAAG;QAAC+I,KAAK,EAAEzC,qBAAqB,CAACyB,MAAM,CAACxD,aAAa,CAAE;QAAC0D,IAAI,EAAC,OAAO;QAAAC,QAAA,EAClEH,MAAM,CAACxD,aAAa,KAAK,QAAQ,GAAG,IAAI,GACxCwD,MAAM,CAACxD,aAAa,KAAK,SAAS,GAAG,IAAI,GAAG;MAAK;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGmB,KAAa,IAAKjI,cAAc,CAACiI,KAAK;EACjD,CAAC,EACD;IACExB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAgB;IACvBC,MAAM,EAAEA,CAACa,CAAM,EAAEX,MAAiB,kBAChC7G,OAAA,CAAC1B,KAAK;MAACyI,IAAI,EAAC,OAAO;MAAAC,QAAA,gBACjBhH,OAAA,CAACjB,OAAO;QAACuH,KAAK,EAAC,0BAAM;QAAAU,QAAA,eACnBhH,OAAA,CAAC3B,MAAM;UACLmF,IAAI,EAAC,MAAM;UACXuE,IAAI,eAAE/H,OAAA,CAACT,WAAW;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBW,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAACoC,MAAM;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVrH,OAAA,CAACjB,OAAO;QAACuH,KAAK,EAAC,gCAAO;QAAAU,QAAA,eACpBhH,OAAA,CAAC3B,MAAM;UACLmF,IAAI,EAAC,MAAM;UACXuE,IAAI,eAAE/H,OAAA,CAACR,cAAc;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBW,OAAO,EAAEA,CAAA,KAAM;YACb;YACAhJ,OAAO,CAACiJ,IAAI,CAAC,UAAU,CAAC;UAC1B;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;EAED,MAAMa,gBAAgB,GAAGA,CAAA,kBACvBlI,OAAA,CAACrB,GAAG;IAACwJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;IAACb,KAAK,EAAE;MAAEc,YAAY,EAAE;IAAG,CAAE;IAAApB,QAAA,gBACjDhH,OAAA,CAACpB,GAAG;MAACyJ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAxB,QAAA,eAC/BhH,OAAA,CAAC7B,IAAI;QAAC4I,IAAI,EAAC,OAAO;QAAAC,QAAA,eAChBhH,OAAA,CAACX,SAAS;UACRiH,KAAK,EAAC,0BAAM;UACZwB,KAAK,EAAElH,UAAU,CAACE,YAAa;UAC/B2H,MAAM,eAAEzI,OAAA,CAACL,gBAAgB;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNrH,OAAA,CAACpB,GAAG;MAACyJ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAxB,QAAA,eAC/BhH,OAAA,CAAC7B,IAAI;QAAC4I,IAAI,EAAC,OAAO;QAAAC,QAAA,eAChBhH,OAAA,CAACX,SAAS;UACRiH,KAAK,EAAC,0BAAM;UACZwB,KAAK,EAAElH,UAAU,CAACG,aAAc;UAChC2H,UAAU,EAAE;YAAEb,KAAK,EAAE;UAAU;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNrH,OAAA,CAACpB,GAAG;MAACyJ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAxB,QAAA,eAC/BhH,OAAA,CAAC7B,IAAI;QAAC4I,IAAI,EAAC,OAAO;QAAAC,QAAA,eAChBhH,OAAA,CAACX,SAAS;UACRiH,KAAK,EAAC,0BAAM;UACZwB,KAAK,EAAElH,UAAU,CAACI,cAAe;UACjC0H,UAAU,EAAE;YAAEb,KAAK,EAAE;UAAU,CAAE;UACjCY,MAAM,eAAEzI,OAAA,CAACJ,aAAa;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNrH,OAAA,CAACpB,GAAG;MAACyJ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAxB,QAAA,eAC/BhH,OAAA,CAAC7B,IAAI;QAAC4I,IAAI,EAAC,OAAO;QAAAC,QAAA,eAChBhH,OAAA,CAACX,SAAS;UACRiH,KAAK,EAAC,gCAAO;UACbwB,KAAK,EAAElH,UAAU,CAACK,eAAgB;UAClCyH,UAAU,EAAE;YAAEb,KAAK,EAAE;UAAU;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNrH,OAAA,CAACpB,GAAG;MAACyJ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAxB,QAAA,eAC/BhH,OAAA,CAAC7B,IAAI;QAAC4I,IAAI,EAAC,OAAO;QAAAC,QAAA,eAChBhH,OAAA,CAACX,SAAS;UACRiH,KAAK,EAAC,oBAAK;UACXwB,KAAK,EAAElH,UAAU,CAACM,UAAW;UAC7ByH,SAAS,EAAGb,KAAK,IAAKjI,cAAc,CAAC+I,MAAM,CAACd,KAAK,CAAC;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNrH,OAAA,CAACpB,GAAG;MAACyJ,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAxB,QAAA,eAC/BhH,OAAA,CAAC7B,IAAI;QAAC4I,IAAI,EAAC,OAAO;QAAAC,QAAA,eAChBhH,OAAA,CAACX,SAAS;UACRiH,KAAK,EAAC,0BAAM;UACZwB,KAAK,EAAElH,UAAU,CAACO,UAAW;UAC7B0H,MAAM,EAAC;QAAG;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMyB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACjH,aAAa,EAAE,OAAO,IAAI;IAE/B,oBACE7B,OAAA,CAACtB,KAAK;MACJ4H,KAAK,EAAE,UAAUzE,aAAa,CAACQ,WAAW,EAAG;MAC7C0G,IAAI,EAAEhH,kBAAmB;MACzBiH,QAAQ,EAAEA,CAAA,KAAMhH,qBAAqB,CAAC,KAAK,CAAE;MAC7CiH,MAAM,EAAE,cACNjJ,OAAA,CAAC3B,MAAM;QAAa2J,OAAO,EAAEA,CAAA,KAAMhG,qBAAqB,CAAC,KAAK,CAAE;QAAAgF,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFZ,KAAK,EAAE,GAAI;MAAAO,QAAA,eAEXhH,OAAA,CAACf,IAAI;QAACiK,gBAAgB,EAAC,MAAM;QAAAlC,QAAA,gBAC3BhH,OAAA,CAACI,OAAO;UAAC+I,GAAG,EAAC,0BAAM;UAAAnC,QAAA,eACjBhH,OAAA,CAACb,YAAY;YAACiK,MAAM,EAAE,CAAE;YAACC,QAAQ;YAACtC,IAAI,EAAC,OAAO;YAAAC,QAAA,gBAC5ChH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAAvC,QAAA,EAAEnF,aAAa,CAACQ;YAAW;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC9ErH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAAvC,QAAA,EAAEnF,aAAa,CAACyB;YAAM;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACzErH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,EAAEnF,aAAa,CAACU;YAAY;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChFrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,EAAEnF,aAAa,CAACW;YAAY;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChFrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,cAAI;cAAAvC,QAAA,EAAEnF,aAAa,CAACY;YAAa;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC/ErH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAAvC,QAAA,EAAEnF,aAAa,CAACa;YAAQ;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC3ErH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,EAAElH,UAAU,CAAC+B,aAAa,CAACc,YAAY;YAAC;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC5FrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAAvC,QAAA,EAC3BnF,aAAa,CAACe,UAAU,GAAG9C,UAAU,CAAC+B,aAAa,CAACe,UAAU,CAAC,GAAG;YAAG;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACpBrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,GAC5BnF,aAAa,CAACgB,gBAAgB,EAAC,GAAC,EAAChB,aAAa,CAACoB,IAAI;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACpBrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,GAC5BnF,aAAa,CAACiB,eAAe,EAAC,GAAC,EAACjB,aAAa,CAACoB,IAAI;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACpBrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,GAC5BnF,aAAa,CAACmB,iBAAiB,EAAC,GAAC,EAACnB,aAAa,CAACoB,IAAI;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACpBrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAvC,QAAA,GAC5BnF,aAAa,CAACkB,gBAAgB,EAAC,GAAC,EAAClB,aAAa,CAACoB,IAAI;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACpBrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,cAAI;cAAAvC,QAAA,EAAEnH,cAAc,CAACgC,aAAa,CAACqB,SAAS;YAAC;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC3FrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAAvC,QAAA,EAAEnH,cAAc,CAACgC,aAAa,CAACX,UAAU;YAAC;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC7FrH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,cAAI;cAAAvC,QAAA,EAAEnF,aAAa,CAACsB;YAAQ;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC1ErH,OAAA,CAACb,YAAY,CAACmK,IAAI;cAACC,KAAK,EAAC,cAAI;cAAAvC,QAAA,eAC3BhH,OAAA,CAAC1B,KAAK;gBAAA0I,QAAA,gBACJhH,OAAA,CAAClB,GAAG;kBAAC+I,KAAK,EAAEhD,cAAc,CAAChD,aAAa,CAACuB,MAAM,CAAE;kBAAA4D,QAAA,EAC9CnF,aAAa,CAACuB,MAAM,KAAK,WAAW,GAAG,IAAI,GAC3CvB,aAAa,CAACuB,MAAM,KAAK,UAAU,GAAG,IAAI,GAC1CvB,aAAa,CAACuB,MAAM,KAAK,SAAS,GAAG,IAAI,GACzCvB,aAAa,CAACuB,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG;gBAAK;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNrH,OAAA,CAAClB,GAAG;kBAAC+I,KAAK,EAAEzC,qBAAqB,CAACvD,aAAa,CAACwB,aAAa,CAAE;kBAAA2D,QAAA,EAC5DnF,aAAa,CAACwB,aAAa,KAAK,QAAQ,GAAG,IAAI,GAC/CxB,aAAa,CAACwB,aAAa,KAAK,SAAS,GAAG,IAAI,GAAG;gBAAK;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC,GAzCO,MAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0CrB,CAAC,eACVrH,OAAA,CAACI,OAAO;UAAC+I,GAAG,EAAC,0BAAM;UAAAnC,QAAA,eACjBhH,OAAA,CAACd,QAAQ;YAAA8H,QAAA,EACNnF,aAAa,CAAC0B,YAAY,CAACiG,GAAG,CAAEC,WAAW,iBAC1CzJ,OAAA,CAACd,QAAQ,CAACoK,IAAI;cAEZzB,KAAK,EAAE9B,uBAAuB,CAAC0D,WAAW,CAACjG,IAAI,CAAE;cAAAwD,QAAA,eAEjDhH,OAAA;gBAAAgH,QAAA,gBACEhH,OAAA,CAAC1B,KAAK;kBAAA0I,QAAA,gBACJhH,OAAA,CAAClB,GAAG;oBAAC+I,KAAK,EAAE9B,uBAAuB,CAAC0D,WAAW,CAACjG,IAAI,CAAE;oBAAAwD,QAAA,EACnDxB,sBAAsB,CAACiE,WAAW,CAACjG,IAAI;kBAAC;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACNrH,OAAA,CAACE,IAAI;oBAAC+G,MAAM;oBAAAD,QAAA,GACTyC,WAAW,CAAChG,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEgG,WAAW,CAAChG,QAAQ,EAAC,GAAC,EAAC5B,aAAa,CAACoB,IAAI;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eACPrH,OAAA,CAACE,IAAI;oBAACsD,IAAI,EAAC,WAAW;oBAAAwD,QAAA,GAAC,gBACjB,EAACyC,WAAW,CAAC/F,iBAAiB,EAAC,GAAC,EAAC7B,aAAa,CAACoB,IAAI;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACRrH,OAAA;kBAAKsH,KAAK,EAAE;oBAAEoC,SAAS,EAAE;kBAAE,CAAE;kBAAA1C,QAAA,eAC3BhH,OAAA,CAACE,IAAI;oBAACsD,IAAI,EAAC,WAAW;oBAAC8D,KAAK,EAAE;sBAAEC,QAAQ,EAAE;oBAAO,CAAE;oBAAAP,QAAA,GAChDlH,UAAU,CAAC2J,WAAW,CAAC3F,SAAS,CAAC,EAAC,KAAG,EAAC2F,WAAW,CAAC9F,QAAQ,EAAC,KAAG,EAAC8F,WAAW,CAAC7F,UAAU;kBAAA;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNrH,OAAA;kBAAKsH,KAAK,EAAE;oBAAEoC,SAAS,EAAE;kBAAE,CAAE;kBAAA1C,QAAA,eAC3BhH,OAAA,CAACE,IAAI;oBAACsD,IAAI,EAAC,WAAW;oBAAC8D,KAAK,EAAE;sBAAEC,QAAQ,EAAE;oBAAO,CAAE;oBAAAP,QAAA,GAAC,gBAC9C,EAACyC,WAAW,CAAC5F,SAAS,EACzB4F,WAAW,CAAC1F,SAAS,IAAI,UAAU0F,WAAW,CAAC1F,SAAS,EAAE,EAC1D0F,WAAW,CAACE,MAAM,IAAI,UAAUF,WAAW,CAACE,MAAM,EAAE;kBAAA;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNrH,OAAA;kBAAKsH,KAAK,EAAE;oBAAEoC,SAAS,EAAE;kBAAE,CAAE;kBAAA1C,QAAA,eAC3BhH,OAAA,CAACE,IAAI;oBAACsD,IAAI,EAAC,WAAW;oBAAC8D,KAAK,EAAE;sBAAEC,QAAQ,EAAE;oBAAO,CAAE;oBAAAP,QAAA,GAAC,gBAC9C,EAACyC,WAAW,CAACtG,QAAQ;kBAAA;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAhCDoC,WAAW,CAACrH,EAAE;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiCN,CAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC,GAvCW,cAAc;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,oBACErH,OAAA;IAAAgH,QAAA,gBACEhH,OAAA,CAAC7B,IAAI;MAAA6I,QAAA,gBACHhH,OAAA,CAACrB,GAAG;QAACiL,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACvC,KAAK,EAAE;UAAEc,YAAY,EAAE;QAAG,CAAE;QAAApB,QAAA,gBACtEhH,OAAA,CAACpB,GAAG;UAAAoI,QAAA,gBACFhH,OAAA,CAACC,KAAK;YAAC6J,KAAK,EAAE,CAAE;YAACxC,KAAK,EAAE;cAAEyC,MAAM,EAAE;YAAE,CAAE;YAAA/C,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrH,OAAA,CAACE,IAAI;YAACsD,IAAI,EAAC,WAAW;YAAAwD,QAAA,EAAC;UAEvB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNrH,OAAA,CAACpB,GAAG;UAAAoI,QAAA,eACFhH,OAAA,CAAC1B,KAAK;YAAA0I,QAAA,gBACJhH,OAAA,CAACG,MAAM;cACL6J,WAAW,EAAC,0EAAc;cAC1BC,UAAU;cACV3C,KAAK,EAAE;gBAAEb,KAAK,EAAE;cAAI,CAAE;cACtByD,QAAQ,EAAE5I;YAAiB;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFrH,OAAA,CAACxB,MAAM;cACLwL,WAAW,EAAC,cAAI;cAChBC,UAAU;cACV3C,KAAK,EAAE;gBAAEb,KAAK,EAAE;cAAI,CAAE;cACtBqB,KAAK,EAAEvG,YAAa;cACpB4I,QAAQ,EAAE3I,eAAgB;cAC1B4I,OAAO,EAAE,CACP;gBAAEb,KAAK,EAAE,IAAI;gBAAEzB,KAAK,EAAE;cAAY,CAAC,EACnC;gBAAEyB,KAAK,EAAE,IAAI;gBAAEzB,KAAK,EAAE;cAAW,CAAC,EAClC;gBAAEyB,KAAK,EAAE,IAAI;gBAAEzB,KAAK,EAAE;cAAU,CAAC,EACjC;gBAAEyB,KAAK,EAAE,IAAI;gBAAEzB,KAAK,EAAE;cAAU,CAAC,EACjC;gBAAEyB,KAAK,EAAE,KAAK;gBAAEzB,KAAK,EAAE;cAAW,CAAC;YACnC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrH,OAAA,CAACxB,MAAM;cACLwL,WAAW,EAAC,oBAAK;cACjBC,UAAU;cACV3C,KAAK,EAAE;gBAAEb,KAAK,EAAE;cAAI,CAAE;cACtBqB,KAAK,EAAErG,cAAe;cACtB0I,QAAQ,EAAEzI,iBAAkB;cAC5B0I,OAAO,EAAE,CACP;gBAAEb,KAAK,EAAE,QAAQ;gBAAEzB,KAAK,EAAE;cAAS,CAAC,EACpC;gBAAEyB,KAAK,EAAE,QAAQ;gBAAEzB,KAAK,EAAE;cAAS,CAAC,EACpC;gBAAEyB,KAAK,EAAE,OAAO;gBAAEzB,KAAK,EAAE;cAAQ,CAAC;YAClC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrH,OAAA,CAACK,WAAW;cACV2J,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAE;cAC9BlC,KAAK,EAAEnG,SAAU;cACjBwI,QAAQ,EAAEvI,YAAa;cACvB0F,KAAK,EAAE;gBAAEb,KAAK,EAAE;cAAI;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFrH,OAAA,CAAC3B,MAAM;cAAC0J,IAAI,eAAE/H,OAAA,CAACN,cAAc;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAL,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrH,OAAA,CAAC3B,MAAM;cAAC0J,IAAI,eAAE/H,OAAA,CAACP,cAAc;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACW,OAAO,EAAErD,YAAa;cAAAqC,QAAA,EAAC;YAEzD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrH,OAAA,CAACZ,KAAK;QACJJ,OAAO,EAAC,sCAAQ;QAChBqL,WAAW,EAAC,wSAAmD;QAC/D7G,IAAI,EAAC,MAAM;QACX8G,QAAQ;QACRC,QAAQ;QACRjD,KAAK,EAAE;UAAEc,YAAY,EAAE;QAAG;MAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEFrH,OAAA,CAACf,IAAI;QAACuL,SAAS,EAAEvI,SAAU;QAACkI,QAAQ,EAAEjI,YAAa;QAAA8E,QAAA,gBACjDhH,OAAA,CAACI,OAAO;UAAC+I,GAAG,eAAEnJ,OAAA;YAAAgH,QAAA,gBAAMhH,OAAA,CAACL,gBAAgB;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAL,QAAA,GACjDkB,gBAAgB,CAAC,CAAC,eACnBlI,OAAA,CAACrB,GAAG;YAACwJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAAnB,QAAA,gBACpBhH,OAAA,CAACpB,GAAG;cAACyJ,EAAE,EAAE,EAAG;cAACE,EAAE,EAAE,EAAG;cAAAvB,QAAA,eAClBhH,OAAA,CAAC7B,IAAI;gBAACmI,KAAK,EAAC,sCAAQ;gBAACS,IAAI,EAAC,OAAO;gBAAAC,QAAA,eAC/BhH,OAAA,CAACE,IAAI;kBAACsD,IAAI,EAAC,WAAW;kBAAAwD,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrH,OAAA,CAACpB,GAAG;cAACyJ,EAAE,EAAE,EAAG;cAACE,EAAE,EAAE,EAAG;cAAAvB,QAAA,eAClBhH,OAAA,CAAC7B,IAAI;gBAACmI,KAAK,EAAC,0BAAM;gBAACS,IAAI,EAAC,OAAO;gBAAAC,QAAA,eAC7BhH,OAAA,CAACE,IAAI;kBAACsD,IAAI,EAAC,WAAW;kBAAAwD,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAbiD,UAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAc1D,CAAC,eAEVrH,OAAA,CAACI,OAAO;UAAC+I,GAAG,EAAC,0BAAM;UAAAnC,QAAA,GAChBkB,gBAAgB,CAAC,CAAC,eACnBlI,OAAA,CAAC5B,KAAK;YACJqM,OAAO,EAAEpE,YAAa;YACtBqE,UAAU,EAAE1E,eAAgB;YAC5BxF,OAAO,EAAEA,OAAQ;YACjBmK,MAAM,EAAC,IAAI;YACXC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAK,CAAE;YACpBC,UAAU,EAAE;cACVC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;YAC1C;UAAE;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAdoB,MAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAerB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAENyB,iBAAiB,CAAC,CAAC;EAAA;IAAA5B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEV,CAAC;AAAC9G,EAAA,CAnrBID,iBAA2B;AAAA8K,EAAA,GAA3B9K,iBAA2B;AAqrBjC,eAAeA,iBAAiB;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}