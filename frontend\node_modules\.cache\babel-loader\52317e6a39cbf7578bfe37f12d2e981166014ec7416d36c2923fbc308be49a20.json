{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\pages\\\\bom\\\\OrderBOMViewPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Typography, Button, Table, Space, Descriptions, Tag, Divider, Row, Col, Statistic, message, Spin, Tooltip, Modal, Progress, Select, Checkbox } from 'antd';\nimport * as XLSX from 'xlsx';\nimport { ArrowLeftOutlined, EditOutlined, PrinterOutlined, DownloadOutlined, ShareAltOutlined, CopyOutlined, CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined, StopOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst OrderBOMViewPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [bomData, setBomData] = useState(null);\n  const [expandedRowKeys, setExpandedRowKeys] = useState([]);\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState('excel');\n  const [exportFields, setExportFields] = useState(['materialCode', 'materialName', 'specification', 'unit', 'quantity', 'unitPrice', 'totalPrice', 'supplier']);\n\n  // 模拟数据加载\n  useEffect(() => {\n    const loadBOMData = async () => {\n      setLoading(true);\n      try {\n        // TODO: 从API获取订单BOM数据\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // 模拟数据\n        const mockData = {\n          id: id || '1',\n          bomCode: 'BOM202401001',\n          bomName: '智能控制器订单BOM',\n          version: '1.0',\n          productCode: 'P001',\n          productName: '智能控制器 V1.0',\n          customerCode: 'C001',\n          customerName: '华为技术有限公司',\n          orderNumber: 'ORD202401001',\n          orderQuantity: 100,\n          deliveryDate: '2024-02-15',\n          priority: 'high',\n          status: 'approved',\n          description: '华为智能控制器项目专用BOM，包含主控芯片、传感器模块等核心组件',\n          totalAmount: 4580.00,\n          createdBy: '张三',\n          createdAt: '2024-01-15 10:30:00',\n          updatedAt: '2024-01-16 14:20:00',\n          approvedBy: '李四',\n          approvedAt: '2024-01-16 16:45:00',\n          items: [{\n            key: '1',\n            materialCode: 'M003',\n            materialName: '集成电路 STM32',\n            specification: 'STM32F407VGT6',\n            unit: '片',\n            quantity: 1,\n            unitPrice: 25.00,\n            totalPrice: 25.00,\n            supplier: '意法半导体',\n            deliveryDays: 7,\n            remark: '主控芯片',\n            level: 1,\n            status: 'available'\n          }, {\n            key: '2',\n            materialCode: 'M004',\n            materialName: 'PCB板 主板',\n            specification: '4层板 100x80mm',\n            unit: '块',\n            quantity: 1,\n            unitPrice: 15.80,\n            totalPrice: 15.80,\n            supplier: '深圳PCB厂',\n            deliveryDays: 5,\n            remark: '主控板',\n            level: 1,\n            status: 'available'\n          }, {\n            key: '3',\n            materialCode: 'M001',\n            materialName: '电阻器 10KΩ',\n            specification: '0603 1% 1/10W',\n            unit: '个',\n            quantity: 10,\n            unitPrice: 0.50,\n            totalPrice: 5.00,\n            supplier: '国巨电子',\n            deliveryDays: 3,\n            remark: '上拉电阻',\n            level: 2,\n            parentKey: '2',\n            status: 'available'\n          }, {\n            key: '4',\n            materialCode: 'M002',\n            materialName: '电容器 100μF',\n            specification: '25V 电解电容',\n            unit: '个',\n            quantity: 5,\n            unitPrice: 1.20,\n            totalPrice: 6.00,\n            supplier: '松下电器',\n            deliveryDays: 4,\n            remark: '滤波电容',\n            level: 2,\n            parentKey: '2',\n            status: 'shortage'\n          }, {\n            key: '5',\n            materialCode: 'M005',\n            materialName: '外壳 塑料',\n            specification: 'ABS 黑色',\n            unit: '个',\n            quantity: 1,\n            unitPrice: 8.50,\n            totalPrice: 8.50,\n            supplier: '塑料制品厂',\n            deliveryDays: 10,\n            remark: '产品外壳',\n            level: 1,\n            status: 'pending'\n          }]\n        };\n        setBomData(mockData);\n      } catch (error) {\n        message.error('加载BOM数据失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id) {\n      loadBOMData();\n    }\n  }, [id]);\n\n  // 状态标签渲染\n  const renderStatusTag = status => {\n    const statusConfig = {\n      draft: {\n        color: 'default',\n        text: '草稿',\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 52\n        }, this)\n      },\n      pending: {\n        color: 'processing',\n        text: '待审核',\n        icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 58\n        }, this)\n      },\n      approved: {\n        color: 'success',\n        text: '已审核',\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 56\n        }, this)\n      },\n      rejected: {\n        color: 'error',\n        text: '已拒绝',\n        icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 54\n        }, this)\n      },\n      in_production: {\n        color: 'warning',\n        text: '生产中',\n        icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 61\n        }, this)\n      },\n      completed: {\n        color: 'success',\n        text: '已完成',\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 57\n        }, this)\n      }\n    };\n    const config = statusConfig[status] || statusConfig.draft;\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: config.color,\n      icon: config.icon,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 优先级标签渲染\n  const renderPriorityTag = priority => {\n    const priorityConfig = {\n      high: {\n        color: 'red',\n        text: '高'\n      },\n      medium: {\n        color: 'orange',\n        text: '中'\n      },\n      low: {\n        color: 'green',\n        text: '低'\n      }\n    };\n    const config = priorityConfig[priority] || priorityConfig.medium;\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: config.color,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 物料状态标签渲染\n  const renderMaterialStatusTag = status => {\n    const statusConfig = {\n      available: {\n        color: 'success',\n        text: '可用'\n      },\n      shortage: {\n        color: 'error',\n        text: '缺料'\n      },\n      pending: {\n        color: 'warning',\n        text: '待采购'\n      }\n    };\n    const config = statusConfig[status] || statusConfig.available;\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: config.color,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '层级',\n    dataIndex: 'level',\n    width: 60,\n    render: level => /*#__PURE__*/_jsxDEV(Tag, {\n      color: level === 1 ? 'blue' : level === 2 ? 'green' : 'orange',\n      children: [\"L\", level]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物料编码',\n    dataIndex: 'materialCode',\n    width: 120,\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        paddingLeft: (record.level - 1) * 20\n      },\n      children: /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '物料名称',\n    dataIndex: 'materialName',\n    width: 200\n  }, {\n    title: '规格型号',\n    dataIndex: 'specification',\n    width: 150\n  }, {\n    title: '单位',\n    dataIndex: 'unit',\n    width: 80\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    width: 100,\n    render: value => value.toFixed(2)\n  }, {\n    title: '单价(元)',\n    dataIndex: 'unitPrice',\n    width: 100,\n    render: value => `¥${value.toFixed(2)}`\n  }, {\n    title: '总价(元)',\n    dataIndex: 'totalPrice',\n    width: 120,\n    render: value => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#1890ff'\n      },\n      children: [\"\\xA5\", value.toFixed(2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '供应商',\n    dataIndex: 'supplier',\n    width: 120\n  }, {\n    title: '交期(天)',\n    dataIndex: 'deliveryDays',\n    width: 100\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    width: 100,\n    render: status => renderMaterialStatusTag(status)\n  }, {\n    title: '备注',\n    dataIndex: 'remark',\n    width: 150,\n    ellipsis: true\n  }];\n\n  // 处理编辑\n  const handleEdit = () => {\n    navigate(`/bom/order-bom-edit/${id}`);\n  };\n\n  // 处理复制\n  const handleCopy = () => {\n    Modal.confirm({\n      title: '确认复制',\n      content: '确定要复制这个订单BOM吗？',\n      onOk: () => {\n        // TODO: 实现复制功能\n        message.success('BOM复制成功');\n        navigate('/bom/order-bom-create');\n      }\n    });\n  };\n\n  // 处理打印\n  const handlePrint = () => {\n    window.print();\n  };\n\n  // 处理导出\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n  const executeExport = () => {\n    if (!bomData) return;\n    try {\n      // 准备导出数据\n      const exportData = bomData.items.map(item => {\n        const data = {};\n        if (exportFields.includes('materialCode')) data['物料编码'] = item.materialCode;\n        if (exportFields.includes('materialName')) data['物料名称'] = item.materialName;\n        if (exportFields.includes('specification')) data['规格型号'] = item.specification;\n        if (exportFields.includes('unit')) data['单位'] = item.unit;\n        if (exportFields.includes('quantity')) data['数量'] = item.quantity;\n        if (exportFields.includes('unitPrice')) data['单价'] = item.unitPrice;\n        if (exportFields.includes('totalPrice')) data['总价'] = item.totalPrice;\n        if (exportFields.includes('supplier')) data['供应商'] = item.supplier;\n        if (exportFields.includes('deliveryDays')) data['交期(天)'] = item.deliveryDays;\n        if (exportFields.includes('status')) data['状态'] = item.status === 'available' ? '可用' : item.status === 'shortage' ? '缺料' : '待采购';\n        if (exportFields.includes('remark')) data['备注'] = item.remark || '';\n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, 'BOM明细');\n\n      // 下载文件\n      const fileName = `${bomData.bomCode}_BOM明细_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  // 处理分享\n  const handleShare = () => {\n    // TODO: 实现分享功能\n    message.info('分享功能开发中...');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '24px',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 7\n    }, this);\n  }\n  if (!bomData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '24px',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u672A\\u627E\\u5230\\u8BA2\\u5355BOM\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 计算统计数据\n  const totalItems = bomData.items.length;\n  const availableItems = bomData.items.filter(item => item.status === 'available').length;\n  const shortageItems = bomData.items.filter(item => item.status === 'shortage').length;\n  const pendingItems = bomData.items.filter(item => item.status === 'pending').length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16,\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/bom/order-bom-list'),\n            style: {\n              marginRight: 16\n            },\n            children: \"\\u8FD4\\u56DE\\u5217\\u8868\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: \"\\u67E5\\u770B\\u8BA2\\u5355BOM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u7F16\\u8F91\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 23\n              }, this),\n              onClick: handleEdit,\n              disabled: bomData.status === 'completed',\n              children: \"\\u7F16\\u8F91\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u590D\\u5236\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 23\n              }, this),\n              onClick: handleCopy,\n              children: \"\\u590D\\u5236\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u6253\\u5370\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(PrinterOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 23\n              }, this),\n              onClick: handlePrint,\n              children: \"\\u6253\\u5370\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u5BFC\\u51FA\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 23\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u5206\\u4EAB\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ShareAltOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 23\n              }, this),\n              onClick: handleShare,\n              children: \"\\u5206\\u4EAB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Descriptions, {\n        title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n        bordered: true,\n        column: 3,\n        size: \"small\",\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"BOM\\u7F16\\u7801\",\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: bomData.bomCode\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"BOM\\u540D\\u79F0\",\n          children: bomData.bomName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u7248\\u672C\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: bomData.version\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u54C1\\u7F16\\u7801\",\n          children: bomData.productCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u54C1\\u540D\\u79F0\",\n          children: bomData.productName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u72B6\\u6001\",\n          children: renderStatusTag(bomData.status)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u7F16\\u7801\",\n          children: bomData.customerCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u540D\\u79F0\",\n          children: bomData.customerName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4F18\\u5148\\u7EA7\",\n          children: renderPriorityTag(bomData.priority)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: bomData.orderNumber\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BA2\\u5355\\u6570\\u91CF\",\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: bomData.orderQuantity\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA4\\u4ED8\\u65E5\\u671F\",\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            style: {\n              color: '#1890ff'\n            },\n            children: dayjs(bomData.deliveryDate).format('YYYY-MM-DD')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u4EBA\",\n          children: bomData.createdBy\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: bomData.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: bomData.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this), bomData.approvedBy && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5BA1\\u6838\\u4EBA\",\n            children: bomData.approvedBy\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5BA1\\u6838\\u65F6\\u95F4\",\n            children: bomData.approvedAt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u63CF\\u8FF0\",\n          span: bomData.approvedBy ? 1 : 3,\n          children: bomData.description || '-'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u91D1\\u989D\",\n            value: bomData.totalAmount,\n            precision: 2,\n            prefix: \"\\xA5\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7269\\u6599\\u603B\\u6570\",\n            value: totalItems,\n            suffix: \"\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u53EF\\u7528\\u7269\\u6599\",\n            value: availableItems,\n            suffix: \"\\u9879\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7F3A\\u6599\\u6570\\u91CF\",\n            value: shortageItems,\n            suffix: \"\\u9879\",\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u7269\\u6599\\u53EF\\u7528\\u6027: \"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: Math.round(availableItems / totalItems * 100),\n          status: shortageItems > 0 ? 'exception' : 'success',\n          format: percent => `${availableItems}/${totalItems} (${percent}%)`,\n          style: {\n            width: 300,\n            marginLeft: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        children: \"BOM\\u660E\\u7EC6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: bomData.items,\n        pagination: false,\n        scroll: {\n          x: 1400\n        },\n        size: \"small\",\n        bordered: true,\n        expandable: {\n          expandedRowKeys,\n          onExpandedRowsChange: keys => setExpandedRowKeys(keys),\n          childrenColumnName: 'children',\n          indentSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16,\n          textAlign: 'right'\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            fontSize: 16,\n            color: '#1890ff'\n          },\n          children: [\"\\u603B\\u91D1\\u989D: \\xA5\", bomData.totalAmount.toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5BFC\\u51FABOM\\u660E\\u7EC6\",\n      open: exportModalVisible,\n      onOk: executeExport,\n      onCancel: () => setExportModalVisible(false),\n      okText: \"\\u5BFC\\u51FA\",\n      cancelText: \"\\u53D6\\u6D88\",\n      width: 600,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: 8\n          },\n          children: \"\\u5BFC\\u51FA\\u683C\\u5F0F\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: exportFormat,\n          onChange: setExportFormat,\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n            value: \"excel\",\n            children: \"Excel (.xlsx)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n            value: \"csv\",\n            children: \"CSV (.csv)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: 8\n          },\n          children: \"\\u5BFC\\u51FA\\u5B57\\u6BB5\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n          value: exportFields,\n          onChange: setExportFields,\n          style: {\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"materialCode\",\n                children: \"\\u7269\\u6599\\u7F16\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"materialName\",\n                children: \"\\u7269\\u6599\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"specification\",\n                children: \"\\u89C4\\u683C\\u578B\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"unit\",\n                children: \"\\u5355\\u4F4D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"quantity\",\n                children: \"\\u6570\\u91CF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"unitPrice\",\n                children: \"\\u5355\\u4EF7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"totalPrice\",\n                children: \"\\u603B\\u4EF7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"supplier\",\n                children: \"\\u4F9B\\u5E94\\u5546\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"deliveryDays\",\n                children: \"\\u4EA4\\u671F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"status\",\n                children: \"\\u72B6\\u6001\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"remark\",\n                children: \"\\u5907\\u6CE8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 439,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderBOMViewPage, \"huBjtWBJmRKcr2FrtR8asT+W7OY=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = OrderBOMViewPage;\nexport default OrderBOMViewPage;\nvar _c;\n$RefreshReg$(_c, \"OrderBOMViewPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Descriptions", "Tag", "Divider", "Row", "Col", "Statistic", "message", "Spin", "<PERSON><PERSON><PERSON>", "Modal", "Progress", "Select", "Checkbox", "XLSX", "ArrowLeftOutlined", "EditOutlined", "PrinterOutlined", "DownloadOutlined", "ShareAltOutlined", "CopyOutlined", "CheckCircleOutlined", "ClockCircleOutlined", "ExclamationCircleOutlined", "StopOutlined", "dayjs", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "OrderBOMViewPage", "_s", "id", "navigate", "loading", "setLoading", "bomData", "setBomData", "expandedRowKeys", "setExpandedRowKeys", "exportModalVisible", "setExportModalVisible", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "loadBOMData", "Promise", "resolve", "setTimeout", "mockData", "bomCode", "bom<PERSON>ame", "version", "productCode", "productName", "customerCode", "customerName", "orderNumber", "orderQuantity", "deliveryDate", "priority", "status", "description", "totalAmount", "created<PERSON>y", "createdAt", "updatedAt", "approvedBy", "approvedAt", "items", "key", "materialCode", "materialName", "specification", "unit", "quantity", "unitPrice", "totalPrice", "supplier", "deliveryDays", "remark", "level", "parent<PERSON><PERSON>", "error", "renderStatusTag", "statusConfig", "draft", "color", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pending", "approved", "rejected", "in_production", "completed", "config", "children", "renderPriorityTag", "priorityConfig", "high", "medium", "low", "renderMaterialStatusTag", "available", "shortage", "columns", "title", "dataIndex", "width", "render", "record", "style", "paddingLeft", "strong", "value", "toFixed", "ellipsis", "handleEdit", "handleCopy", "confirm", "content", "onOk", "success", "handlePrint", "window", "print", "handleExport", "executeExport", "exportData", "map", "item", "data", "includes", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "Date", "toISOString", "split", "writeFile", "handleShare", "info", "padding", "textAlign", "size", "marginTop", "type", "totalItems", "length", "availableItems", "filter", "shortageItems", "pendingItems", "marginBottom", "display", "justifyContent", "alignItems", "onClick", "marginRight", "margin", "disabled", "bordered", "column", "<PERSON><PERSON>", "label", "format", "span", "gutter", "precision", "prefix", "valueStyle", "suffix", "percent", "Math", "round", "marginLeft", "dataSource", "pagination", "scroll", "x", "expandable", "onExpandedRowsChange", "keys", "childrenColumnName", "indentSize", "fontSize", "open", "onCancel", "okText", "cancelText", "onChange", "Option", "Group", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/pages/bom/OrderBOMViewPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Descriptions,\n  Tag,\n  Divider,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Spin,\n  Tooltip,\n  Modal,\n  Progress,\n  Select,\n  Checkbox\n} from 'antd';\nimport * as XLSX from 'xlsx';\nimport {\n  ArrowLeftOutlined,\n  EditOutlined,\n  PrinterOutlined,\n  DownloadOutlined,\n  ShareAltOutlined,\n  CopyOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  ExclamationCircleOutlined,\n  StopOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\n\ninterface BOMItem {\n  key: string;\n  materialCode: string;\n  materialName: string;\n  specification: string;\n  unit: string;\n  quantity: number;\n  unitPrice: number;\n  totalPrice: number;\n  supplier: string;\n  deliveryDays: number;\n  remark?: string;\n  level: number;\n  parentKey?: string;\n  status: 'available' | 'shortage' | 'pending';\n}\n\ninterface OrderBOMData {\n  id: string;\n  bomCode: string;\n  bomName: string;\n  version: string;\n  productCode: string;\n  productName: string;\n  customerCode: string;\n  customerName: string;\n  orderNumber: string;\n  orderQuantity: number;\n  deliveryDate: string;\n  priority: 'high' | 'medium' | 'low';\n  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'in_production' | 'completed';\n  description?: string;\n  items: BOMItem[];\n  totalAmount: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  approvedBy?: string;\n  approvedAt?: string;\n}\n\nconst OrderBOMViewPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [bomData, setBomData] = useState<OrderBOMData | null>(null);\n  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);\n  const [exportModalVisible, setExportModalVisible] = useState(false);\n  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');\n  const [exportFields, setExportFields] = useState<string[]>(['materialCode', 'materialName', 'specification', 'unit', 'quantity', 'unitPrice', 'totalPrice', 'supplier']);\n\n  // 模拟数据加载\n  useEffect(() => {\n    const loadBOMData = async () => {\n      setLoading(true);\n      try {\n        // TODO: 从API获取订单BOM数据\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        // 模拟数据\n        const mockData: OrderBOMData = {\n          id: id || '1',\n          bomCode: 'BOM202401001',\n          bomName: '智能控制器订单BOM',\n          version: '1.0',\n          productCode: 'P001',\n          productName: '智能控制器 V1.0',\n          customerCode: 'C001',\n          customerName: '华为技术有限公司',\n          orderNumber: 'ORD202401001',\n          orderQuantity: 100,\n          deliveryDate: '2024-02-15',\n          priority: 'high',\n          status: 'approved',\n          description: '华为智能控制器项目专用BOM，包含主控芯片、传感器模块等核心组件',\n          totalAmount: 4580.00,\n          createdBy: '张三',\n          createdAt: '2024-01-15 10:30:00',\n          updatedAt: '2024-01-16 14:20:00',\n          approvedBy: '李四',\n          approvedAt: '2024-01-16 16:45:00',\n          items: [\n            {\n              key: '1',\n              materialCode: 'M003',\n              materialName: '集成电路 STM32',\n              specification: 'STM32F407VGT6',\n              unit: '片',\n              quantity: 1,\n              unitPrice: 25.00,\n              totalPrice: 25.00,\n              supplier: '意法半导体',\n              deliveryDays: 7,\n              remark: '主控芯片',\n              level: 1,\n              status: 'available'\n            },\n            {\n              key: '2',\n              materialCode: 'M004',\n              materialName: 'PCB板 主板',\n              specification: '4层板 100x80mm',\n              unit: '块',\n              quantity: 1,\n              unitPrice: 15.80,\n              totalPrice: 15.80,\n              supplier: '深圳PCB厂',\n              deliveryDays: 5,\n              remark: '主控板',\n              level: 1,\n              status: 'available'\n            },\n            {\n              key: '3',\n              materialCode: 'M001',\n              materialName: '电阻器 10KΩ',\n              specification: '0603 1% 1/10W',\n              unit: '个',\n              quantity: 10,\n              unitPrice: 0.50,\n              totalPrice: 5.00,\n              supplier: '国巨电子',\n              deliveryDays: 3,\n              remark: '上拉电阻',\n              level: 2,\n              parentKey: '2',\n              status: 'available'\n            },\n            {\n              key: '4',\n              materialCode: 'M002',\n              materialName: '电容器 100μF',\n              specification: '25V 电解电容',\n              unit: '个',\n              quantity: 5,\n              unitPrice: 1.20,\n              totalPrice: 6.00,\n              supplier: '松下电器',\n              deliveryDays: 4,\n              remark: '滤波电容',\n              level: 2,\n              parentKey: '2',\n              status: 'shortage'\n            },\n            {\n              key: '5',\n              materialCode: 'M005',\n              materialName: '外壳 塑料',\n              specification: 'ABS 黑色',\n              unit: '个',\n              quantity: 1,\n              unitPrice: 8.50,\n              totalPrice: 8.50,\n              supplier: '塑料制品厂',\n              deliveryDays: 10,\n              remark: '产品外壳',\n              level: 1,\n              status: 'pending'\n            }\n          ]\n        };\n        \n        setBomData(mockData);\n      } catch (error) {\n        message.error('加载BOM数据失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id) {\n      loadBOMData();\n    }\n  }, [id]);\n\n  // 状态标签渲染\n  const renderStatusTag = (status: string) => {\n    const statusConfig = {\n      draft: { color: 'default', text: '草稿', icon: <EditOutlined /> },\n      pending: { color: 'processing', text: '待审核', icon: <ClockCircleOutlined /> },\n      approved: { color: 'success', text: '已审核', icon: <CheckCircleOutlined /> },\n      rejected: { color: 'error', text: '已拒绝', icon: <StopOutlined /> },\n      in_production: { color: 'warning', text: '生产中', icon: <ExclamationCircleOutlined /> },\n      completed: { color: 'success', text: '已完成', icon: <CheckCircleOutlined /> }\n    };\n    \n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;\n    return (\n      <Tag color={config.color} icon={config.icon}>\n        {config.text}\n      </Tag>\n    );\n  };\n\n  // 优先级标签渲染\n  const renderPriorityTag = (priority: string) => {\n    const priorityConfig = {\n      high: { color: 'red', text: '高' },\n      medium: { color: 'orange', text: '中' },\n      low: { color: 'green', text: '低' }\n    };\n    \n    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  // 物料状态标签渲染\n  const renderMaterialStatusTag = (status: string) => {\n    const statusConfig = {\n      available: { color: 'success', text: '可用' },\n      shortage: { color: 'error', text: '缺料' },\n      pending: { color: 'warning', text: '待采购' }\n    };\n    \n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.available;\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<BOMItem> = [\n    {\n      title: '层级',\n      dataIndex: 'level',\n      width: 60,\n      render: (level: number) => (\n        <Tag color={level === 1 ? 'blue' : level === 2 ? 'green' : 'orange'}>\n          L{level}\n        </Tag>\n      )\n    },\n    {\n      title: '物料编码',\n      dataIndex: 'materialCode',\n      width: 120,\n      render: (text: string, record: BOMItem) => (\n        <div style={{ paddingLeft: (record.level - 1) * 20 }}>\n          <Text strong>{text}</Text>\n        </div>\n      )\n    },\n    {\n      title: '物料名称',\n      dataIndex: 'materialName',\n      width: 200\n    },\n    {\n      title: '规格型号',\n      dataIndex: 'specification',\n      width: 150\n    },\n    {\n      title: '单位',\n      dataIndex: 'unit',\n      width: 80\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      width: 100,\n      render: (value: number) => value.toFixed(2)\n    },\n    {\n      title: '单价(元)',\n      dataIndex: 'unitPrice',\n      width: 100,\n      render: (value: number) => `¥${value.toFixed(2)}`\n    },\n    {\n      title: '总价(元)',\n      dataIndex: 'totalPrice',\n      width: 120,\n      render: (value: number) => (\n        <Text strong style={{ color: '#1890ff' }}>\n          ¥{value.toFixed(2)}\n        </Text>\n      )\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplier',\n      width: 120\n    },\n    {\n      title: '交期(天)',\n      dataIndex: 'deliveryDays',\n      width: 100\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      width: 100,\n      render: (status: string) => renderMaterialStatusTag(status)\n    },\n    {\n      title: '备注',\n      dataIndex: 'remark',\n      width: 150,\n      ellipsis: true\n    }\n  ];\n\n  // 处理编辑\n  const handleEdit = () => {\n    navigate(`/bom/order-bom-edit/${id}`);\n  };\n\n  // 处理复制\n  const handleCopy = () => {\n    Modal.confirm({\n      title: '确认复制',\n      content: '确定要复制这个订单BOM吗？',\n      onOk: () => {\n        // TODO: 实现复制功能\n        message.success('BOM复制成功');\n        navigate('/bom/order-bom-create');\n      }\n    });\n  };\n\n  // 处理打印\n  const handlePrint = () => {\n    window.print();\n  };\n\n  // 处理导出\n  const handleExport = () => {\n    setExportModalVisible(true);\n  };\n\n  const executeExport = () => {\n    if (!bomData) return;\n    \n    try {\n      // 准备导出数据\n      const exportData = bomData.items.map(item => {\n        const data: any = {};\n        \n        if (exportFields.includes('materialCode')) data['物料编码'] = item.materialCode;\n        if (exportFields.includes('materialName')) data['物料名称'] = item.materialName;\n        if (exportFields.includes('specification')) data['规格型号'] = item.specification;\n        if (exportFields.includes('unit')) data['单位'] = item.unit;\n        if (exportFields.includes('quantity')) data['数量'] = item.quantity;\n        if (exportFields.includes('unitPrice')) data['单价'] = item.unitPrice;\n        if (exportFields.includes('totalPrice')) data['总价'] = item.totalPrice;\n        if (exportFields.includes('supplier')) data['供应商'] = item.supplier;\n        if (exportFields.includes('deliveryDays')) data['交期(天)'] = item.deliveryDays;\n        if (exportFields.includes('status')) data['状态'] = item.status === 'available' ? '可用' : item.status === 'shortage' ? '缺料' : '待采购';\n        if (exportFields.includes('remark')) data['备注'] = item.remark || '';\n        \n        return data;\n      });\n\n      // 创建工作簿\n      const ws = XLSX.utils.json_to_sheet(exportData);\n      const wb = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(wb, ws, 'BOM明细');\n\n      // 下载文件\n      const fileName = `${bomData.bomCode}_BOM明细_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;\n      XLSX.writeFile(wb, fileName);\n      \n      message.success('导出成功');\n      setExportModalVisible(false);\n    } catch (error) {\n      message.error('导出失败');\n    }\n  };\n\n  // 处理分享\n  const handleShare = () => {\n    // TODO: 实现分享功能\n    message.info('分享功能开发中...');\n  };\n\n  if (loading) {\n    return (\n      <div style={{ padding: '24px', textAlign: 'center' }}>\n        <Spin size=\"large\" />\n        <p style={{ marginTop: 16 }}>加载中...</p>\n      </div>\n    );\n  }\n\n  if (!bomData) {\n    return (\n      <div style={{ padding: '24px', textAlign: 'center' }}>\n        <Text type=\"secondary\">未找到订单BOM数据</Text>\n      </div>\n    );\n  }\n\n  // 计算统计数据\n  const totalItems = bomData.items.length;\n  const availableItems = bomData.items.filter(item => item.status === 'available').length;\n  const shortageItems = bomData.items.filter(item => item.status === 'shortage').length;\n  const pendingItems = bomData.items.filter(item => item.status === 'pending').length;\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Card>\n        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <Button\n              icon={<ArrowLeftOutlined />}\n              onClick={() => navigate('/bom/order-bom-list')}\n              style={{ marginRight: 16 }}\n            >\n              返回列表\n            </Button>\n            <Title level={4} style={{ margin: 0 }}>查看订单BOM</Title>\n          </div>\n          <Space>\n            <Tooltip title=\"编辑\">\n              <Button\n                icon={<EditOutlined />}\n                onClick={handleEdit}\n                disabled={bomData.status === 'completed'}\n              >\n                编辑\n              </Button>\n            </Tooltip>\n            <Tooltip title=\"复制\">\n              <Button\n                icon={<CopyOutlined />}\n                onClick={handleCopy}\n              >\n                复制\n              </Button>\n            </Tooltip>\n            <Tooltip title=\"打印\">\n              <Button\n                icon={<PrinterOutlined />}\n                onClick={handlePrint}\n              >\n                打印\n              </Button>\n            </Tooltip>\n            <Tooltip title=\"导出\">\n              <Button\n                icon={<DownloadOutlined />}\n                onClick={handleExport}\n              >\n                导出\n              </Button>\n            </Tooltip>\n            <Tooltip title=\"分享\">\n              <Button\n                icon={<ShareAltOutlined />}\n                onClick={handleShare}\n              >\n                分享\n              </Button>\n            </Tooltip>\n          </Space>\n        </div>\n\n        {/* 基本信息 */}\n        <Descriptions\n          title=\"基本信息\"\n          bordered\n          column={3}\n          size=\"small\"\n          style={{ marginBottom: 24 }}\n        >\n          <Descriptions.Item label=\"BOM编码\">\n            <Text strong>{bomData.bomCode}</Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"BOM名称\">\n            {bomData.bomName}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"版本号\">\n            <Tag color=\"blue\">{bomData.version}</Tag>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"产品编码\">\n            {bomData.productCode}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"产品名称\">\n            {bomData.productName}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"状态\">\n            {renderStatusTag(bomData.status)}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"客户编码\">\n            {bomData.customerCode}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"客户名称\">\n            {bomData.customerName}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"优先级\">\n            {renderPriorityTag(bomData.priority)}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"订单号\">\n            <Text strong>{bomData.orderNumber}</Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"订单数量\">\n            <Text strong>{bomData.orderQuantity}</Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"交付日期\">\n            <Text strong style={{ color: '#1890ff' }}>\n              {dayjs(bomData.deliveryDate).format('YYYY-MM-DD')}\n            </Text>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建人\">\n            {bomData.createdBy}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {bomData.createdAt}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"更新时间\">\n            {bomData.updatedAt}\n          </Descriptions.Item>\n          {bomData.approvedBy && (\n            <>\n              <Descriptions.Item label=\"审核人\">\n                {bomData.approvedBy}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"审核时间\">\n                {bomData.approvedAt}\n              </Descriptions.Item>\n            </>\n          )}\n          <Descriptions.Item label=\"描述\" span={bomData.approvedBy ? 1 : 3}>\n            {bomData.description || '-'}\n          </Descriptions.Item>\n        </Descriptions>\n\n        {/* 统计信息 */}\n        <Row gutter={16} style={{ marginBottom: 24 }}>\n          <Col span={6}>\n            <Statistic\n              title=\"总金额\"\n              value={bomData.totalAmount}\n              precision={2}\n              prefix=\"¥\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"物料总数\"\n              value={totalItems}\n              suffix=\"项\"\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"可用物料\"\n              value={availableItems}\n              suffix=\"项\"\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"缺料数量\"\n              value={shortageItems}\n              suffix=\"项\"\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n        </Row>\n\n        {/* 物料可用性进度 */}\n        <div style={{ marginBottom: 24 }}>\n          <Text strong>物料可用性: </Text>\n          <Progress\n            percent={Math.round((availableItems / totalItems) * 100)}\n            status={shortageItems > 0 ? 'exception' : 'success'}\n            format={(percent) => `${availableItems}/${totalItems} (${percent}%)`}\n            style={{ width: 300, marginLeft: 16 }}\n          />\n        </div>\n\n        <Divider>BOM明细</Divider>\n\n        <Table\n          columns={columns}\n          dataSource={bomData.items}\n          pagination={false}\n          scroll={{ x: 1400 }}\n          size=\"small\"\n          bordered\n          expandable={{\n            expandedRowKeys,\n            onExpandedRowsChange: (keys) => setExpandedRowKeys(keys as React.Key[]),\n            childrenColumnName: 'children',\n            indentSize: 20\n          }}\n        />\n\n        <div style={{ marginTop: 16, textAlign: 'right' }}>\n          <Text strong style={{ fontSize: 16, color: '#1890ff' }}>\n            总金额: ¥{bomData.totalAmount.toFixed(2)}\n          </Text>\n        </div>\n      </Card>\n\n      {/* 导出模态框 */}\n      <Modal\n        title=\"导出BOM明细\"\n        open={exportModalVisible}\n        onOk={executeExport}\n        onCancel={() => setExportModalVisible(false)}\n        okText=\"导出\"\n        cancelText=\"取消\"\n        width={600}\n      >\n        <div style={{ marginBottom: 16 }}>\n          <label style={{ display: 'block', marginBottom: 8 }}>导出格式：</label>\n          <Select\n            value={exportFormat}\n            onChange={setExportFormat}\n            style={{ width: '100%' }}\n          >\n            <Select.Option value=\"excel\">Excel (.xlsx)</Select.Option>\n            <Select.Option value=\"csv\">CSV (.csv)</Select.Option>\n          </Select>\n        </div>\n        \n        <div>\n          <label style={{ display: 'block', marginBottom: 8 }}>导出字段：</label>\n          <Checkbox.Group\n            value={exportFields}\n            onChange={setExportFields}\n            style={{ width: '100%' }}\n          >\n            <Row>\n              <Col span={12}>\n                <Checkbox value=\"materialCode\">物料编码</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"materialName\">物料名称</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"specification\">规格型号</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"unit\">单位</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"quantity\">数量</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"unitPrice\">单价</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"totalPrice\">总价</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"supplier\">供应商</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"deliveryDays\">交期</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"status\">状态</Checkbox>\n              </Col>\n              <Col span={12}>\n                <Checkbox value=\"remark\">备注</Checkbox>\n              </Col>\n            </Row>\n          </Checkbox.Group>\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default OrderBOMViewPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,YAAY,EACZC,GAAG,EACHC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,QAAQ,QACH,MAAM;AACb,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SACEC,iBAAiB,EACjBC,YAAY,EACZC,eAAe,EACfC,gBAAgB,EAChBC,gBAAgB,EAChBC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,yBAAyB,EACzBC,YAAY,QACP,mBAAmB;AAE1B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGlC,UAAU;AA2ClC,MAAMmC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAG,CAAC,GAAGxC,SAAS,CAAiB,CAAC;EAC1C,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAsB,IAAI,CAAC;EACjE,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAc,EAAE,CAAC;EACvE,MAAM,CAACkD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAkB,OAAO,CAAC;EAC1E,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAW,CAAC,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;;EAExK;EACAC,SAAS,CAAC,MAAM;IACd,MAAMuD,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9BX,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM,IAAIY,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACA,MAAME,QAAsB,GAAG;UAC7BlB,EAAE,EAAEA,EAAE,IAAI,GAAG;UACbmB,OAAO,EAAE,cAAc;UACvBC,OAAO,EAAE,YAAY;UACrBC,OAAO,EAAE,KAAK;UACdC,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE,YAAY;UACzBC,YAAY,EAAE,MAAM;UACpBC,YAAY,EAAE,UAAU;UACxBC,WAAW,EAAE,cAAc;UAC3BC,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE,YAAY;UAC1BC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,UAAU;UAClBC,WAAW,EAAE,kCAAkC;UAC/CC,WAAW,EAAE,OAAO;UACpBC,SAAS,EAAE,IAAI;UACfC,SAAS,EAAE,qBAAqB;UAChCC,SAAS,EAAE,qBAAqB;UAChCC,UAAU,EAAE,IAAI;UAChBC,UAAU,EAAE,qBAAqB;UACjCC,KAAK,EAAE,CACL;YACEC,GAAG,EAAE,GAAG;YACRC,YAAY,EAAE,MAAM;YACpBC,YAAY,EAAE,YAAY;YAC1BC,aAAa,EAAE,eAAe;YAC9BC,IAAI,EAAE,GAAG;YACTC,QAAQ,EAAE,CAAC;YACXC,SAAS,EAAE,KAAK;YAChBC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,OAAO;YACjBC,YAAY,EAAE,CAAC;YACfC,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,CAAC;YACRpB,MAAM,EAAE;UACV,CAAC,EACD;YACES,GAAG,EAAE,GAAG;YACRC,YAAY,EAAE,MAAM;YACpBC,YAAY,EAAE,SAAS;YACvBC,aAAa,EAAE,cAAc;YAC7BC,IAAI,EAAE,GAAG;YACTC,QAAQ,EAAE,CAAC;YACXC,SAAS,EAAE,KAAK;YAChBC,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,QAAQ;YAClBC,YAAY,EAAE,CAAC;YACfC,MAAM,EAAE,KAAK;YACbC,KAAK,EAAE,CAAC;YACRpB,MAAM,EAAE;UACV,CAAC,EACD;YACES,GAAG,EAAE,GAAG;YACRC,YAAY,EAAE,MAAM;YACpBC,YAAY,EAAE,UAAU;YACxBC,aAAa,EAAE,eAAe;YAC9BC,IAAI,EAAE,GAAG;YACTC,QAAQ,EAAE,EAAE;YACZC,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,IAAI;YAChBC,QAAQ,EAAE,MAAM;YAChBC,YAAY,EAAE,CAAC;YACfC,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,CAAC;YACRC,SAAS,EAAE,GAAG;YACdrB,MAAM,EAAE;UACV,CAAC,EACD;YACES,GAAG,EAAE,GAAG;YACRC,YAAY,EAAE,MAAM;YACpBC,YAAY,EAAE,WAAW;YACzBC,aAAa,EAAE,UAAU;YACzBC,IAAI,EAAE,GAAG;YACTC,QAAQ,EAAE,CAAC;YACXC,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,IAAI;YAChBC,QAAQ,EAAE,MAAM;YAChBC,YAAY,EAAE,CAAC;YACfC,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,CAAC;YACRC,SAAS,EAAE,GAAG;YACdrB,MAAM,EAAE;UACV,CAAC,EACD;YACES,GAAG,EAAE,GAAG;YACRC,YAAY,EAAE,MAAM;YACpBC,YAAY,EAAE,OAAO;YACrBC,aAAa,EAAE,QAAQ;YACvBC,IAAI,EAAE,GAAG;YACTC,QAAQ,EAAE,CAAC;YACXC,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,IAAI;YAChBC,QAAQ,EAAE,OAAO;YACjBC,YAAY,EAAE,EAAE;YAChBC,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,CAAC;YACRpB,MAAM,EAAE;UACV,CAAC;QAEL,CAAC;QAEDzB,UAAU,CAACa,QAAQ,CAAC;MACtB,CAAC,CAAC,OAAOkC,KAAK,EAAE;QACd/E,OAAO,CAAC+E,KAAK,CAAC,WAAW,CAAC;MAC5B,CAAC,SAAS;QACRjD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,EAAE,EAAE;MACNc,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACd,EAAE,CAAC,CAAC;;EAER;EACA,MAAMqD,eAAe,GAAIvB,MAAc,IAAK;IAC1C,MAAMwB,YAAY,GAAG;MACnBC,KAAK,EAAE;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,IAAI;QAAEC,IAAI,eAAEjE,OAAA,CAACX,YAAY;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE,CAAC;MAC/DC,OAAO,EAAE;QAAEP,KAAK,EAAE,YAAY;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,eAAEjE,OAAA,CAACL,mBAAmB;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE,CAAC;MAC5EE,QAAQ,EAAE;QAAER,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,eAAEjE,OAAA,CAACN,mBAAmB;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE,CAAC;MAC1EG,QAAQ,EAAE;QAAET,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,eAAEjE,OAAA,CAACH,YAAY;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE,CAAC;MACjEI,aAAa,EAAE;QAAEV,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,eAAEjE,OAAA,CAACJ,yBAAyB;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE,CAAC;MACrFK,SAAS,EAAE;QAAEX,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,eAAEjE,OAAA,CAACN,mBAAmB;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;IAC5E,CAAC;IAED,MAAMM,MAAM,GAAGd,YAAY,CAACxB,MAAM,CAA8B,IAAIwB,YAAY,CAACC,KAAK;IACtF,oBACE9D,OAAA,CAACzB,GAAG;MAACwF,KAAK,EAAEY,MAAM,CAACZ,KAAM;MAACE,IAAI,EAAEU,MAAM,CAACV,IAAK;MAAAW,QAAA,EACzCD,MAAM,CAACX;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEV,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAIzC,QAAgB,IAAK;IAC9C,MAAM0C,cAAc,GAAG;MACrBC,IAAI,EAAE;QAAEhB,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAI,CAAC;MACjCgB,MAAM,EAAE;QAAEjB,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAI,CAAC;MACtCiB,GAAG,EAAE;QAAElB,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAI;IACnC,CAAC;IAED,MAAMW,MAAM,GAAGG,cAAc,CAAC1C,QAAQ,CAAgC,IAAI0C,cAAc,CAACE,MAAM;IAC/F,oBAAOhF,OAAA,CAACzB,GAAG;MAACwF,KAAK,EAAEY,MAAM,CAACZ,KAAM;MAAAa,QAAA,EAAED,MAAM,CAACX;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACtD,CAAC;;EAED;EACA,MAAMa,uBAAuB,GAAI7C,MAAc,IAAK;IAClD,MAAMwB,YAAY,GAAG;MACnBsB,SAAS,EAAE;QAAEpB,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAK,CAAC;MAC3CoB,QAAQ,EAAE;QAAErB,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACxCM,OAAO,EAAE;QAAEP,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAM;IAC3C,CAAC;IAED,MAAMW,MAAM,GAAGd,YAAY,CAACxB,MAAM,CAA8B,IAAIwB,YAAY,CAACsB,SAAS;IAC1F,oBAAOnF,OAAA,CAACzB,GAAG;MAACwF,KAAK,EAAEY,MAAM,CAACZ,KAAM;MAAAa,QAAA,EAAED,MAAM,CAACX;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACtD,CAAC;;EAED;EACA,MAAMgB,OAA6B,GAAG,CACpC;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGhC,KAAa,iBACpBzD,OAAA,CAACzB,GAAG;MAACwF,KAAK,EAAEN,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,QAAS;MAAAmB,QAAA,GAAC,GAClE,EAACnB,KAAK;IAAA;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACEiB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACzB,IAAY,EAAE0B,MAAe,kBACpC1F,OAAA;MAAK2F,KAAK,EAAE;QAAEC,WAAW,EAAE,CAACF,MAAM,CAACjC,KAAK,GAAG,CAAC,IAAI;MAAG,CAAE;MAAAmB,QAAA,eACnD5E,OAAA,CAACI,IAAI;QAACyF,MAAM;QAAAjB,QAAA,EAAEZ;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB;EAET,CAAC,EACD;IACEiB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGK,KAAa,IAAKA,KAAK,CAACC,OAAO,CAAC,CAAC;EAC5C,CAAC,EACD;IACET,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,WAAW;IACtBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGK,KAAa,IAAK,IAAIA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;EACjD,CAAC,EACD;IACET,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,YAAY;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGK,KAAa,iBACpB9F,OAAA,CAACI,IAAI;MAACyF,MAAM;MAACF,KAAK,EAAE;QAAE5B,KAAK,EAAE;MAAU,CAAE;MAAAa,QAAA,GAAC,MACvC,EAACkB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;IAAA;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd;EAEV,CAAC,EACD;IACEiB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGpD,MAAc,IAAK6C,uBAAuB,CAAC7C,MAAM;EAC5D,CAAC,EACD;IACEiD,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVQ,QAAQ,EAAE;EACZ,CAAC,CACF;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBzF,QAAQ,CAAC,uBAAuBD,EAAE,EAAE,CAAC;EACvC,CAAC;;EAED;EACA,MAAM2F,UAAU,GAAGA,CAAA,KAAM;IACvBnH,KAAK,CAACoH,OAAO,CAAC;MACZb,KAAK,EAAE,MAAM;MACbc,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAEA,CAAA,KAAM;QACV;QACAzH,OAAO,CAAC0H,OAAO,CAAC,SAAS,CAAC;QAC1B9F,QAAQ,CAAC,uBAAuB,CAAC;MACnC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM+F,WAAW,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,KAAK,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB1F,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM2F,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAChG,OAAO,EAAE;IAEd,IAAI;MACF;MACA,MAAMiG,UAAU,GAAGjG,OAAO,CAACkC,KAAK,CAACgE,GAAG,CAACC,IAAI,IAAI;QAC3C,MAAMC,IAAS,GAAG,CAAC,CAAC;QAEpB,IAAI5F,YAAY,CAAC6F,QAAQ,CAAC,cAAc,CAAC,EAAED,IAAI,CAAC,MAAM,CAAC,GAAGD,IAAI,CAAC/D,YAAY;QAC3E,IAAI5B,YAAY,CAAC6F,QAAQ,CAAC,cAAc,CAAC,EAAED,IAAI,CAAC,MAAM,CAAC,GAAGD,IAAI,CAAC9D,YAAY;QAC3E,IAAI7B,YAAY,CAAC6F,QAAQ,CAAC,eAAe,CAAC,EAAED,IAAI,CAAC,MAAM,CAAC,GAAGD,IAAI,CAAC7D,aAAa;QAC7E,IAAI9B,YAAY,CAAC6F,QAAQ,CAAC,MAAM,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,GAAGD,IAAI,CAAC5D,IAAI;QACzD,IAAI/B,YAAY,CAAC6F,QAAQ,CAAC,UAAU,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,GAAGD,IAAI,CAAC3D,QAAQ;QACjE,IAAIhC,YAAY,CAAC6F,QAAQ,CAAC,WAAW,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,GAAGD,IAAI,CAAC1D,SAAS;QACnE,IAAIjC,YAAY,CAAC6F,QAAQ,CAAC,YAAY,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,GAAGD,IAAI,CAACzD,UAAU;QACrE,IAAIlC,YAAY,CAAC6F,QAAQ,CAAC,UAAU,CAAC,EAAED,IAAI,CAAC,KAAK,CAAC,GAAGD,IAAI,CAACxD,QAAQ;QAClE,IAAInC,YAAY,CAAC6F,QAAQ,CAAC,cAAc,CAAC,EAAED,IAAI,CAAC,OAAO,CAAC,GAAGD,IAAI,CAACvD,YAAY;QAC5E,IAAIpC,YAAY,CAAC6F,QAAQ,CAAC,QAAQ,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,GAAGD,IAAI,CAACzE,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGyE,IAAI,CAACzE,MAAM,KAAK,UAAU,GAAG,IAAI,GAAG,KAAK;QAChI,IAAIlB,YAAY,CAAC6F,QAAQ,CAAC,QAAQ,CAAC,EAAED,IAAI,CAAC,IAAI,CAAC,GAAGD,IAAI,CAACtD,MAAM,IAAI,EAAE;QAEnE,OAAOuD,IAAI;MACb,CAAC,CAAC;;MAEF;MACA,MAAME,EAAE,GAAG9H,IAAI,CAAC+H,KAAK,CAACC,aAAa,CAACP,UAAU,CAAC;MAC/C,MAAMQ,EAAE,GAAGjI,IAAI,CAAC+H,KAAK,CAACG,QAAQ,CAAC,CAAC;MAChClI,IAAI,CAAC+H,KAAK,CAACI,iBAAiB,CAACF,EAAE,EAAEH,EAAE,EAAE,OAAO,CAAC;;MAE7C;MACA,MAAM/C,QAAQ,GAAG,GAAGvD,OAAO,CAACe,OAAO,UAAU,IAAI6F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIxG,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK,EAAE;MAClI9B,IAAI,CAACuI,SAAS,CAACN,EAAE,EAAElD,QAAQ,CAAC;MAE5BtF,OAAO,CAAC0H,OAAO,CAAC,MAAM,CAAC;MACvBtF,qBAAqB,CAAC,KAAK,CAAC;IAC9B,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACd/E,OAAO,CAAC+E,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMgE,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA/I,OAAO,CAACgJ,IAAI,CAAC,YAAY,CAAC;EAC5B,CAAC;EAED,IAAInH,OAAO,EAAE;IACX,oBACET,OAAA;MAAK2F,KAAK,EAAE;QAAEkC,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAlD,QAAA,gBACnD5E,OAAA,CAACnB,IAAI;QAACkJ,IAAI,EAAC;MAAO;QAAA7D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBrE,OAAA;QAAG2F,KAAK,EAAE;UAAEqC,SAAS,EAAE;QAAG,CAAE;QAAApD,QAAA,EAAC;MAAM;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,IAAI,CAAC1D,OAAO,EAAE;IACZ,oBACEX,OAAA;MAAK2F,KAAK,EAAE;QAAEkC,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAlD,QAAA,eACnD5E,OAAA,CAACI,IAAI;QAAC6H,IAAI,EAAC,WAAW;QAAArD,QAAA,EAAC;MAAU;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAEV;;EAEA;EACA,MAAM6D,UAAU,GAAGvH,OAAO,CAACkC,KAAK,CAACsF,MAAM;EACvC,MAAMC,cAAc,GAAGzH,OAAO,CAACkC,KAAK,CAACwF,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACzE,MAAM,KAAK,WAAW,CAAC,CAAC8F,MAAM;EACvF,MAAMG,aAAa,GAAG3H,OAAO,CAACkC,KAAK,CAACwF,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACzE,MAAM,KAAK,UAAU,CAAC,CAAC8F,MAAM;EACrF,MAAMI,YAAY,GAAG5H,OAAO,CAACkC,KAAK,CAACwF,MAAM,CAACvB,IAAI,IAAIA,IAAI,CAACzE,MAAM,KAAK,SAAS,CAAC,CAAC8F,MAAM;EAEnF,oBACEnI,OAAA;IAAK2F,KAAK,EAAE;MAAEkC,OAAO,EAAE;IAAO,CAAE;IAAAjD,QAAA,gBAC9B5E,OAAA,CAAC/B,IAAI;MAAA2G,QAAA,gBACH5E,OAAA;QAAK2F,KAAK,EAAE;UAAE6C,YAAY,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA/D,QAAA,gBACvG5E,OAAA;UAAK2F,KAAK,EAAE;YAAE8C,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAA/D,QAAA,gBACpD5E,OAAA,CAAC7B,MAAM;YACL8F,IAAI,eAAEjE,OAAA,CAACZ,iBAAiB;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuE,OAAO,EAAEA,CAAA,KAAMpI,QAAQ,CAAC,qBAAqB,CAAE;YAC/CmF,KAAK,EAAE;cAAEkD,WAAW,EAAE;YAAG,CAAE;YAAAjE,QAAA,EAC5B;UAED;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrE,OAAA,CAACG,KAAK;YAACsD,KAAK,EAAE,CAAE;YAACkC,KAAK,EAAE;cAAEmD,MAAM,EAAE;YAAE,CAAE;YAAAlE,QAAA,EAAC;UAAO;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNrE,OAAA,CAAC3B,KAAK;UAAAuG,QAAA,gBACJ5E,OAAA,CAAClB,OAAO;YAACwG,KAAK,EAAC,cAAI;YAAAV,QAAA,eACjB5E,OAAA,CAAC7B,MAAM;cACL8F,IAAI,eAAEjE,OAAA,CAACX,YAAY;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBuE,OAAO,EAAE3C,UAAW;cACpB8C,QAAQ,EAAEpI,OAAO,CAAC0B,MAAM,KAAK,WAAY;cAAAuC,QAAA,EAC1C;YAED;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACVrE,OAAA,CAAClB,OAAO;YAACwG,KAAK,EAAC,cAAI;YAAAV,QAAA,eACjB5E,OAAA,CAAC7B,MAAM;cACL8F,IAAI,eAAEjE,OAAA,CAACP,YAAY;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBuE,OAAO,EAAE1C,UAAW;cAAAtB,QAAA,EACrB;YAED;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACVrE,OAAA,CAAClB,OAAO;YAACwG,KAAK,EAAC,cAAI;YAAAV,QAAA,eACjB5E,OAAA,CAAC7B,MAAM;cACL8F,IAAI,eAAEjE,OAAA,CAACV,eAAe;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BuE,OAAO,EAAErC,WAAY;cAAA3B,QAAA,EACtB;YAED;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACVrE,OAAA,CAAClB,OAAO;YAACwG,KAAK,EAAC,cAAI;YAAAV,QAAA,eACjB5E,OAAA,CAAC7B,MAAM;cACL8F,IAAI,eAAEjE,OAAA,CAACT,gBAAgB;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuE,OAAO,EAAElC,YAAa;cAAA9B,QAAA,EACvB;YAED;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACVrE,OAAA,CAAClB,OAAO;YAACwG,KAAK,EAAC,cAAI;YAAAV,QAAA,eACjB5E,OAAA,CAAC7B,MAAM;cACL8F,IAAI,eAAEjE,OAAA,CAACR,gBAAgB;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuE,OAAO,EAAEjB,WAAY;cAAA/C,QAAA,EACtB;YAED;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrE,OAAA,CAAC1B,YAAY;QACXgH,KAAK,EAAC,0BAAM;QACZ0D,QAAQ;QACRC,MAAM,EAAE,CAAE;QACVlB,IAAI,EAAC,OAAO;QACZpC,KAAK,EAAE;UAAE6C,YAAY,EAAE;QAAG,CAAE;QAAA5D,QAAA,gBAE5B5E,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,iBAAO;UAAAvE,QAAA,eAC9B5E,OAAA,CAACI,IAAI;YAACyF,MAAM;YAAAjB,QAAA,EAAEjE,OAAO,CAACe;UAAO;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,iBAAO;UAAAvE,QAAA,EAC7BjE,OAAO,CAACgB;QAAO;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAAvE,QAAA,eAC5B5E,OAAA,CAACzB,GAAG;YAACwF,KAAK,EAAC,MAAM;YAAAa,QAAA,EAAEjE,OAAO,CAACiB;UAAO;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAvE,QAAA,EAC5BjE,OAAO,CAACkB;QAAW;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAvE,QAAA,EAC5BjE,OAAO,CAACmB;QAAW;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,cAAI;UAAAvE,QAAA,EAC1BhB,eAAe,CAACjD,OAAO,CAAC0B,MAAM;QAAC;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAvE,QAAA,EAC5BjE,OAAO,CAACoB;QAAY;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAvE,QAAA,EAC5BjE,OAAO,CAACqB;QAAY;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAAvE,QAAA,EAC3BC,iBAAiB,CAAClE,OAAO,CAACyB,QAAQ;QAAC;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAAvE,QAAA,eAC5B5E,OAAA,CAACI,IAAI;YAACyF,MAAM;YAAAjB,QAAA,EAAEjE,OAAO,CAACsB;UAAW;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAvE,QAAA,eAC7B5E,OAAA,CAACI,IAAI;YAACyF,MAAM;YAAAjB,QAAA,EAAEjE,OAAO,CAACuB;UAAa;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAvE,QAAA,eAC7B5E,OAAA,CAACI,IAAI;YAACyF,MAAM;YAACF,KAAK,EAAE;cAAE5B,KAAK,EAAE;YAAU,CAAE;YAAAa,QAAA,EACtC9E,KAAK,CAACa,OAAO,CAACwB,YAAY,CAAC,CAACiH,MAAM,CAAC,YAAY;UAAC;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAAvE,QAAA,EAC3BjE,OAAO,CAAC6B;QAAS;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAvE,QAAA,EAC5BjE,OAAO,CAAC8B;QAAS;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAvE,QAAA,EAC5BjE,OAAO,CAAC+B;QAAS;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACnB1D,OAAO,CAACgC,UAAU,iBACjB3C,OAAA,CAAAE,SAAA;UAAA0E,QAAA,gBACE5E,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;YAACC,KAAK,EAAC,oBAAK;YAAAvE,QAAA,EAC3BjE,OAAO,CAACgC;UAAU;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACpBrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAvE,QAAA,EAC5BjE,OAAO,CAACiC;UAAU;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,eACpB,CACH,eACDrE,OAAA,CAAC1B,YAAY,CAAC4K,IAAI;UAACC,KAAK,EAAC,cAAI;UAACE,IAAI,EAAE1I,OAAO,CAACgC,UAAU,GAAG,CAAC,GAAG,CAAE;UAAAiC,QAAA,EAC5DjE,OAAO,CAAC2B,WAAW,IAAI;QAAG;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGfrE,OAAA,CAACvB,GAAG;QAAC6K,MAAM,EAAE,EAAG;QAAC3D,KAAK,EAAE;UAAE6C,YAAY,EAAE;QAAG,CAAE;QAAA5D,QAAA,gBAC3C5E,OAAA,CAACtB,GAAG;UAAC2K,IAAI,EAAE,CAAE;UAAAzE,QAAA,eACX5E,OAAA,CAACrB,SAAS;YACR2G,KAAK,EAAC,oBAAK;YACXQ,KAAK,EAAEnF,OAAO,CAAC4B,WAAY;YAC3BgH,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC,MAAG;YACVC,UAAU,EAAE;cAAE1F,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrE,OAAA,CAACtB,GAAG;UAAC2K,IAAI,EAAE,CAAE;UAAAzE,QAAA,eACX5E,OAAA,CAACrB,SAAS;YACR2G,KAAK,EAAC,0BAAM;YACZQ,KAAK,EAAEoC,UAAW;YAClBwB,MAAM,EAAC;UAAG;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrE,OAAA,CAACtB,GAAG;UAAC2K,IAAI,EAAE,CAAE;UAAAzE,QAAA,eACX5E,OAAA,CAACrB,SAAS;YACR2G,KAAK,EAAC,0BAAM;YACZQ,KAAK,EAAEsC,cAAe;YACtBsB,MAAM,EAAC,QAAG;YACVD,UAAU,EAAE;cAAE1F,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrE,OAAA,CAACtB,GAAG;UAAC2K,IAAI,EAAE,CAAE;UAAAzE,QAAA,eACX5E,OAAA,CAACrB,SAAS;YACR2G,KAAK,EAAC,0BAAM;YACZQ,KAAK,EAAEwC,aAAc;YACrBoB,MAAM,EAAC,QAAG;YACVD,UAAU,EAAE;cAAE1F,KAAK,EAAE;YAAU;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAK2F,KAAK,EAAE;UAAE6C,YAAY,EAAE;QAAG,CAAE;QAAA5D,QAAA,gBAC/B5E,OAAA,CAACI,IAAI;UAACyF,MAAM;UAAAjB,QAAA,EAAC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3BrE,OAAA,CAAChB,QAAQ;UACP2K,OAAO,EAAEC,IAAI,CAACC,KAAK,CAAEzB,cAAc,GAAGF,UAAU,GAAI,GAAG,CAAE;UACzD7F,MAAM,EAAEiG,aAAa,GAAG,CAAC,GAAG,WAAW,GAAG,SAAU;UACpDc,MAAM,EAAGO,OAAO,IAAK,GAAGvB,cAAc,IAAIF,UAAU,KAAKyB,OAAO,IAAK;UACrEhE,KAAK,EAAE;YAAEH,KAAK,EAAE,GAAG;YAAEsE,UAAU,EAAE;UAAG;QAAE;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENrE,OAAA,CAACxB,OAAO;QAAAoG,QAAA,EAAC;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAExBrE,OAAA,CAAC5B,KAAK;QACJiH,OAAO,EAAEA,OAAQ;QACjB0E,UAAU,EAAEpJ,OAAO,CAACkC,KAAM;QAC1BmH,UAAU,EAAE,KAAM;QAClBC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBnC,IAAI,EAAC,OAAO;QACZiB,QAAQ;QACRmB,UAAU,EAAE;UACVtJ,eAAe;UACfuJ,oBAAoB,EAAGC,IAAI,IAAKvJ,kBAAkB,CAACuJ,IAAmB,CAAC;UACvEC,kBAAkB,EAAE,UAAU;UAC9BC,UAAU,EAAE;QACd;MAAE;QAAArG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFrE,OAAA;QAAK2F,KAAK,EAAE;UAAEqC,SAAS,EAAE,EAAE;UAAEF,SAAS,EAAE;QAAQ,CAAE;QAAAlD,QAAA,eAChD5E,OAAA,CAACI,IAAI;UAACyF,MAAM;UAACF,KAAK,EAAE;YAAE6E,QAAQ,EAAE,EAAE;YAAEzG,KAAK,EAAE;UAAU,CAAE;UAAAa,QAAA,GAAC,0BAChD,EAACjE,OAAO,CAAC4B,WAAW,CAACwD,OAAO,CAAC,CAAC,CAAC;QAAA;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPrE,OAAA,CAACjB,KAAK;MACJuG,KAAK,EAAC,6BAAS;MACfmF,IAAI,EAAE1J,kBAAmB;MACzBsF,IAAI,EAAEM,aAAc;MACpB+D,QAAQ,EAAEA,CAAA,KAAM1J,qBAAqB,CAAC,KAAK,CAAE;MAC7C2J,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MACfpF,KAAK,EAAE,GAAI;MAAAZ,QAAA,gBAEX5E,OAAA;QAAK2F,KAAK,EAAE;UAAE6C,YAAY,EAAE;QAAG,CAAE;QAAA5D,QAAA,gBAC/B5E,OAAA;UAAO2F,KAAK,EAAE;YAAE8C,OAAO,EAAE,OAAO;YAAED,YAAY,EAAE;UAAE,CAAE;UAAA5D,QAAA,EAAC;QAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClErE,OAAA,CAACf,MAAM;UACL6G,KAAK,EAAE7E,YAAa;UACpB4J,QAAQ,EAAE3J,eAAgB;UAC1ByE,KAAK,EAAE;YAAEH,KAAK,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBAEzB5E,OAAA,CAACf,MAAM,CAAC6L,MAAM;YAAChF,KAAK,EAAC,OAAO;YAAAlB,QAAA,EAAC;UAAa;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAC1DrE,OAAA,CAACf,MAAM,CAAC6L,MAAM;YAAChF,KAAK,EAAC,KAAK;YAAAlB,QAAA,EAAC;UAAU;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENrE,OAAA;QAAA4E,QAAA,gBACE5E,OAAA;UAAO2F,KAAK,EAAE;YAAE8C,OAAO,EAAE,OAAO;YAAED,YAAY,EAAE;UAAE,CAAE;UAAA5D,QAAA,EAAC;QAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClErE,OAAA,CAACd,QAAQ,CAAC6L,KAAK;UACbjF,KAAK,EAAE3E,YAAa;UACpB0J,QAAQ,EAAEzJ,eAAgB;UAC1BuE,KAAK,EAAE;YAAEH,KAAK,EAAE;UAAO,CAAE;UAAAZ,QAAA,eAEzB5E,OAAA,CAACvB,GAAG;YAAAmG,QAAA,gBACF5E,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,cAAc;gBAAAlB,QAAA,EAAC;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,cAAc;gBAAAlB,QAAA,EAAC;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,eAAe;gBAAAlB,QAAA,EAAC;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,MAAM;gBAAAlB,QAAA,EAAC;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,UAAU;gBAAAlB,QAAA,EAAC;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,WAAW;gBAAAlB,QAAA,EAAC;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,YAAY;gBAAAlB,QAAA,EAAC;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,UAAU;gBAAAlB,QAAA,EAAC;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,cAAc;gBAAAlB,QAAA,EAAC;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,QAAQ;gBAAAlB,QAAA,EAAC;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNrE,OAAA,CAACtB,GAAG;cAAC2K,IAAI,EAAE,EAAG;cAAAzE,QAAA,eACZ5E,OAAA,CAACd,QAAQ;gBAAC4G,KAAK,EAAC,QAAQ;gBAAAlB,QAAA,EAAC;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/D,EAAA,CAjnBID,gBAA0B;EAAA,QACftC,SAAS,EACPC,WAAW;AAAA;AAAAgN,EAAA,GAFxB3K,gBAA0B;AAmnBhC,eAAeA,gBAAgB;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}