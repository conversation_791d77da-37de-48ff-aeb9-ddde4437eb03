import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Button,
  Space,
  Input,
  Select,
  Card,
  Tag,
  message,
  Modal,
  Form,
  Typography,
  Row,
  Col,
  Tooltip,
  Dropdown,
  MenuProps,
  Upload,
  Progress,
  Alert,
  Divider,
  Timeline,
  Descriptions,
  Checkbox,
  Radio
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined,
  ExportOutlined,
  ImportOutlined,
  HistoryOutlined,
  DownloadOutlined,
  UploadOutlined,
  FileExcelOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchCoreBOMs, deleteCoreBOM, freezeCoreBOM } from '../../store/slices/bomSlice';
import { CoreBOM } from '../../types';
import { ROUTES, BOM_STATUS } from '../../constants';
import { formatDate } from '../../utils';
import { ConfirmDialog } from '../../components';
import { errorHandler, ErrorType } from '../../utils/errorHandler';

const { Title } = Typography;
const { Search } = Input;

const CoreBOMListPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { coreBOMs, loading, pagination } = useAppSelector(state => state.bom);
  
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [copyModalVisible, setCopyModalVisible] = useState(false);
  const [copyingBOM, setCopyingBOM] = useState<CoreBOM | null>(null);
  const [copyForm] = Form.useForm();
  
  // 版本历史相关状态
  const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);
  const [currentBOMForHistory, setCurrentBOMForHistory] = useState<CoreBOM | null>(null);
  
  // 导出导入相关状态
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [exportFormat, setExportFormat] = useState('excel');
  const [exportFields, setExportFields] = useState<string[]>(['code', 'name', 'version', 'status', 'description']);
  const [importProgress, setImportProgress] = useState(0);
  const [importStatus, setImportStatus] = useState<'uploading' | 'processing' | 'success' | 'error' | null>(null);
  const [importResult, setImportResult] = useState<any>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [exportForm] = Form.useForm();
  const [importForm] = Form.useForm();

  useEffect(() => {
    loadData();
  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter]);

  const loadData = useCallback(() => {
    dispatch(fetchCoreBOMs({
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchKeyword,
    }));
  }, [dispatch, pagination.current, pagination.pageSize, searchKeyword]);

  const handleSearch = useCallback((value: string) => {
    setSearchKeyword(value);
  }, []);

  const handleStatusFilter = useCallback((value: string) => {
    setStatusFilter(value);
  }, []);

  const handleCreate = useCallback(() => {
    navigate(ROUTES.CORE_BOM_CREATE);
  }, [navigate]);

  const handleEdit = useCallback((record: CoreBOM) => {
    navigate(ROUTES.CORE_BOM_EDIT.replace(':id', record.id));
  }, [navigate]);

  const handleView = useCallback((record: CoreBOM) => {
    navigate(ROUTES.CORE_BOM_VIEW.replace(':id', record.id));
  }, [navigate]);

  const handleDelete = useCallback(async (record: CoreBOM) => {
    Modal.confirm({
      title: '确认删除',
      content: (
        <div>
          <p>确定要删除以下BOM吗？</p>
          <p><strong>BOM编码：</strong>{record.code}</p>
          <p><strong>BOM名称：</strong>{record.name}</p>
          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复，请谨慎操作！</p>
        </div>
      ),
      type: 'warning',
      onOk: async () => {
        try {
          await dispatch(deleteCoreBOM(record.id)).unwrap();
          message.success('删除成功');
          loadData();
        } catch (error: any) {
          errorHandler.handleError({
            type: ErrorType.BUSINESS,
            message: error.message || '删除失败',
            details: error,
            timestamp: Date.now()
          });
        }
      }
    });
  }, [dispatch, loadData]);

  const handleFreeze = useCallback(async (record: CoreBOM) => {
    const isActive = record.status === BOM_STATUS.ACTIVE;
    const action = isActive ? '冻结' : '解冻';
    
    Modal.confirm({
      title: `确认${action}`,
      content: (
        <div>
          <p>确定要{action}以下BOM吗？</p>
          <p><strong>BOM编码：</strong>{record.code}</p>
          <p><strong>BOM名称：</strong>{record.name}</p>
          {isActive && (
            <p style={{ color: '#faad14', marginTop: 12 }}>冻结后该BOM将无法被使用！</p>
          )}
        </div>
      ),
      type: isActive ? 'warning' : 'info',
      onOk: async () => {
        try {
          await dispatch(freezeCoreBOM(record.id)).unwrap();
          message.success(`${action}成功`);
          loadData();
        } catch (error: any) {
          errorHandler.handleError({
            type: ErrorType.BUSINESS,
            message: error.message || `${action}失败`,
            details: error,
            timestamp: Date.now()
          });
        }
      }
    });
  }, [dispatch, loadData]);

  const handleCopy = useCallback((record: CoreBOM) => {
    setCopyingBOM(record);
    copyForm.setFieldsValue({
      name: `${record.name}_副本`,
      code: `${record.code}_COPY`,
    });
    setCopyModalVisible(true);
  }, [copyForm]);

  const handleCopyConfirm = async () => {
    try {
      const values = await copyForm.validateFields();
      // TODO: 实现复制BOM的API调用
      message.success('复制成功');
      setCopyModalVisible(false);
      loadData();
    } catch (error) {
      message.error('复制失败');
    }
  };

  const handleVersionHistory = (record: CoreBOM) => {
    setCurrentBOMForHistory(record);
    setVersionHistoryVisible(true);
  };

  const handleExport = () => {
    setExportModalVisible(true);
  };

  const handleImport = () => {
    setImportModalVisible(true);
  };
  
  // 下载导入模板
  const handleDownloadTemplate = () => {
    // TODO: 实现下载模板功能
    const templateData = [
      {
        'BOM编码': 'BOM001',
        'BOM名称': '示例BOM',
        '版本': 'V1.0',
        '状态': 'draft',
        '描述': '这是一个示例BOM'
      }
    ];
    
    // 创建CSV内容
    const headers = Object.keys(templateData[0]);
    const csvContent = [headers.join(','), ...templateData.map(row => Object.values(row).join(','))].join('\n');
    
    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'core_bom_template.csv';
    link.click();
    
    message.success('模板下载成功');
  };
  
  // 执行导出
  const handleExportConfirm = async () => {
    try {
      const values = await exportForm.validateFields();
      
      // 获取要导出的数据
      let exportData = mockData;
      if (selectedRowKeys.length > 0) {
        exportData = mockData.filter(item => selectedRowKeys.includes(item.id));
      }
      
      // 根据选择的字段过滤数据
      const filteredData = exportData.map(item => {
        const filteredItem: any = {};
        exportFields.forEach(field => {
          switch (field) {
            case 'code':
              filteredItem['BOM编码'] = item.code;
              break;
            case 'name':
              filteredItem['BOM名称'] = item.name;
              break;
            case 'version':
              filteredItem['版本'] = item.version;
              break;
            case 'status':
              filteredItem['状态'] = getStatusText(item.status);
              break;
            case 'description':
              filteredItem['描述'] = item.description;
              break;
            case 'createdBy':
              filteredItem['创建人'] = item.createdBy;
              break;
            case 'createdAt':
              filteredItem['创建时间'] = formatDate(item.createdAt);
              break;
            case 'updatedAt':
              filteredItem['更新时间'] = formatDate(item.updatedAt);
              break;
          }
        });
        return filteredItem;
      });
      
      // 创建并下载文件
      if (exportFormat === 'excel') {
        // TODO: 使用xlsx库导出Excel
        message.info('Excel导出功能需要集成xlsx库');
      } else {
        // CSV导出
        const headers = Object.keys(filteredData[0] || {});
        const csvContent = [headers.join(','), ...filteredData.map(row => Object.values(row).join(','))].join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `core_bom_export_${new Date().getTime()}.csv`;
        link.click();
      }
      
      setExportModalVisible(false);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };
  
  // 处理文件上传
  const handleFileUpload = (file: File) => {
    setImportStatus('uploading');
    setImportProgress(0);
    
    // 模拟上传进度
    const interval = setInterval(() => {
      setImportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setImportStatus('processing');
          
          // 模拟处理过程
          setTimeout(() => {
            processImportFile(file);
          }, 1000);
          
          return 100;
        }
        return prev + 10;
      });
    }, 200);
    
    return false; // 阻止默认上传
  };
  
  // 处理导入文件
  const processImportFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const lines = content.split('\n');
        const headers = lines[0].split(',');
        
        const importedData = [];
        const errors = [];
        
        for (let i = 1; i < lines.length; i++) {
          if (lines[i].trim()) {
            const values = lines[i].split(',');
            const row: any = {};
            
            headers.forEach((header, index) => {
              row[header.trim()] = values[index]?.trim();
            });
            
            // 验证数据
            if (!row['BOM编码'] || !row['BOM名称']) {
              errors.push(`第${i + 1}行：BOM编码和名称不能为空`);
            } else {
              importedData.push({
                code: row['BOM编码'],
                name: row['BOM名称'],
                version: row['版本'] || 'V1.0',
                status: row['状态'] || 'draft',
                description: row['描述'] || ''
              });
            }
          }
        }
        
        setImportResult({
          total: lines.length - 1,
          success: importedData.length,
          errors: errors.length,
          data: importedData,
          errorMessages: errors
        });
        
        setImportStatus(errors.length > 0 ? 'error' : 'success');
        
        if (errors.length === 0) {
          message.success(`成功导入${importedData.length}条数据`);
          // TODO: 调用API保存数据
        } else {
          message.warning(`导入完成，${errors.length}条数据有错误`);
        }
        
      } catch (error) {
        setImportStatus('error');
        setImportResult({
          total: 0,
          success: 0,
          errors: 1,
          errorMessages: ['文件格式错误，请检查文件内容']
        });
        message.error('文件解析失败');
      }
    };
    
    reader.readAsText(file);
  };
  
  // 表格行选择
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    onSelectAll: (selected: boolean, selectedRows: CoreBOM[], changeRows: CoreBOM[]) => {
      console.log('Select all:', selected, selectedRows, changeRows);
    },
  };
  
  // 清空选择
  const handleClearSelection = () => {
    setSelectedRowKeys([]);
  };
  
  // 导出选中项
  const handleExportSelected = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要导出的数据');
      return;
    }
    setExportModalVisible(true);
  };
  
  // 模拟版本历史数据
  const getVersionHistory = (bom: CoreBOM) => {
    return [
      {
        version: 'V1.2',
        status: 'active',
        description: '优化物料清单，更新供应商信息',
        createdBy: '李工程师',
        createdAt: '2024-03-25 14:30:00',
        changes: ['更新供应商信息', '调整物料数量', '优化成本结构']
      },
      {
        version: 'V1.1',
        status: 'frozen',
        description: '修复物料规格错误',
        createdBy: '王工程师',
        createdAt: '2024-03-20 10:15:00',
        changes: ['修正物料规格', '更新技术参数']
      },
      {
        version: 'V1.0',
        status: 'obsolete',
        description: '初始版本',
        createdBy: '张工程师',
        createdAt: '2024-03-15 09:00:00',
        changes: ['创建初始BOM结构']
      }
    ];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case BOM_STATUS.DRAFT: return 'default';
      case BOM_STATUS.ACTIVE: return 'success';
      case BOM_STATUS.FROZEN: return 'blue';
      case BOM_STATUS.OBSOLETE: return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case BOM_STATUS.DRAFT: return '草稿';
      case BOM_STATUS.ACTIVE: return '激活';
      case BOM_STATUS.FROZEN: return '冻结';
      case BOM_STATUS.OBSOLETE: return '废弃';
      default: return status;
    }
  };

  // 可导出字段选项
  const exportFieldOptions = [
    { label: 'BOM编码', value: 'code' },
    { label: 'BOM名称', value: 'name' },
    { label: '版本', value: 'version' },
    { label: '状态', value: 'status' },
    { label: '描述', value: 'description' },
    { label: '创建人', value: 'createdBy' },
    { label: '创建时间', value: 'createdAt' },
    { label: '更新时间', value: 'updatedAt' }
  ];

  const getActionMenuItems = (record: CoreBOM): MenuProps['items'] => [
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: '复制',
      onClick: () => handleCopy(record),
    },
    {
      key: 'history',
      icon: <HistoryOutlined />,
      label: '版本历史',
      onClick: () => handleVersionHistory(record),
    },
    {
      key: 'freeze',
      icon: record.status === BOM_STATUS.FROZEN ? <UnlockOutlined /> : <LockOutlined />,
      label: record.status === BOM_STATUS.FROZEN ? '解冻' : '冻结',
      onClick: () => handleFreeze(record),
      disabled: record.status === BOM_STATUS.DRAFT,
    },
  ];

  const columns = [
    {
      title: 'BOM编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (text: string, record: CoreBOM) => (
        <Button type="link" onClick={() => handleView(record)}>
          {text}
        </Button>
      ),
    },
    {
      title: 'BOM名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => formatDate(date),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 120,
      render: (date: string) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: CoreBOM) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={record.status === BOM_STATUS.FROZEN}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
              disabled={record.status !== BOM_STATUS.DRAFT}
            />
          </Tooltip>
          <Dropdown
            menu={{ items: getActionMenuItems(record) }}
            trigger={['click']}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // 模拟数据
  const mockData: CoreBOM[] = [
    {
      id: '1',
      name: '5G基站天线BOM',
      code: 'ANT-5G-001',
      version: 'V1.0',
      description: '5G基站用高增益天线物料清单',
      status: 'ACTIVE',
      items: [],
      configRules: [],
      createdBy: 'admin',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z',
    },
    {
      id: '2',
      name: '4G室内天线BOM',
      code: 'ANT-4G-002',
      version: 'V2.1',
      description: '4G室内覆盖天线物料清单',
      status: 'FROZEN',
      items: [],
      configRules: [],
      createdBy: 'bom_manager',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-16T00:00:00Z',
      frozenAt: '2024-01-16T00:00:00Z',
      frozenBy: 'admin',
    },
    {
      id: '3',
      name: 'WiFi天线BOM',
      code: 'ANT-WIFI-003',
      version: 'V1.5',
      description: 'WiFi6天线物料清单',
      status: 'DRAFT',
      items: [],
      configRules: [],
      createdBy: 'bom_manager',
      createdAt: '2024-01-03T00:00:00Z',
      updatedAt: '2024-01-17T00:00:00Z',
    },
  ];

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              核心BOM管理
            </Title>
          </Col>
          <Col>
            <Space>
              <Button icon={<ImportOutlined />} onClick={handleImport}>
                导入
              </Button>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                新建BOM
              </Button>
            </Space>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索BOM编码或名称"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              placeholder="状态筛选"
              allowClear
              style={{ width: '100%' }}
              onChange={handleStatusFilter}
              options={[
                { label: '草稿', value: BOM_STATUS.DRAFT },
                { label: '激活', value: BOM_STATUS.ACTIVE },
                { label: '冻结', value: BOM_STATUS.FROZEN },
                { label: '废弃', value: BOM_STATUS.OBSOLETE },
              ]}
            />
          </Col>
        </Row>

        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16, padding: '8px 16px', backgroundColor: '#f0f2f5', borderRadius: 6 }}>
            <span style={{ marginRight: 16 }}>已选择 {selectedRowKeys.length} 项</span>
            <Button size="small" onClick={handleClearSelection} style={{ marginRight: 8 }}>
              清空选择
            </Button>
            <Button size="small" type="primary" icon={<DownloadOutlined />} onClick={handleExportSelected}>
              导出选中
            </Button>
          </div>
        )}

        <Table
          columns={columns}
          dataSource={mockData}
          loading={loading}
          rowKey="id"
          rowSelection={rowSelection}
          scroll={{ x: 1200 }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 复制BOM模态框 */}
      <Modal
        title="复制BOM"
        open={copyModalVisible}
        onOk={handleCopyConfirm}
        onCancel={() => setCopyModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Form form={copyForm} layout="vertical">
          <Form.Item
            name="name"
            label="BOM名称"
            rules={[{ required: true, message: '请输入BOM名称' }]}
          >
            <Input placeholder="请输入BOM名称" />
          </Form.Item>
          <Form.Item
            name="code"
            label="BOM编码"
            rules={[{ required: true, message: '请输入BOM编码' }]}
          >
            <Input placeholder="请输入BOM编码" />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 版本历史模态框 */}
      <Modal
          title={`版本历史 - ${currentBOMForHistory?.name}`}
          open={versionHistoryVisible}
          onCancel={() => setVersionHistoryVisible(false)}
          footer={[
            <Button key="close" onClick={() => setVersionHistoryVisible(false)}>
              关闭
            </Button>
          ]}
          width={800}
        >
          {currentBOMForHistory && (
            <div>
              <Descriptions
                title="当前BOM信息"
                bordered
                size="small"
                style={{ marginBottom: 24 }}
              >
                <Descriptions.Item label="BOM编码">{currentBOMForHistory.code}</Descriptions.Item>
                <Descriptions.Item label="BOM名称">{currentBOMForHistory.name}</Descriptions.Item>
                <Descriptions.Item label="当前版本">{currentBOMForHistory.version}</Descriptions.Item>
                <Descriptions.Item label="状态" span={3}>
                  <Tag color={getStatusColor(currentBOMForHistory.status)}>
                    {getStatusText(currentBOMForHistory.status)}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
              
              <Divider>版本历史</Divider>
              
              <Timeline>
                {getVersionHistory(currentBOMForHistory).map((version, index) => (
                  <Timeline.Item
                    key={index}
                    dot={<ClockCircleOutlined style={{ fontSize: '16px' }} />}
                    color={version.status === 'active' ? 'green' : version.status === 'frozen' ? 'blue' : 'gray'}
                  >
                    <div style={{ marginBottom: 16 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <span style={{ fontSize: 16, fontWeight: 'bold' }}>{version.version}</span>
                        <Tag color={getStatusColor(version.status)}>
                          {getStatusText(version.status)}
                        </Tag>
                      </div>
                      <div style={{ color: '#666', marginBottom: 8 }}>
                        {version.createdBy} · {version.createdAt}
                      </div>
                      <div style={{ marginBottom: 8 }}>{version.description}</div>
                      {version.changes && version.changes.length > 0 && (
                        <div>
                          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>主要变更：</div>
                          <ul style={{ margin: 0, paddingLeft: 20 }}>
                            {version.changes.map((change, changeIndex) => (
                              <li key={changeIndex}>{change}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>
          )}
      </Modal>
      
      {/* 导出模态框 */}
      <Modal
          title="导出BOM数据"
          open={exportModalVisible}
          onOk={handleExportConfirm}
          onCancel={() => setExportModalVisible(false)}
          width={600}
        >
          <Form form={exportForm} layout="vertical">
            <Alert
              message="导出说明"
              description={selectedRowKeys.length > 0 ? `将导出已选择的 ${selectedRowKeys.length} 条BOM数据` : "将导出所有BOM数据"}
              type="info"
              style={{ marginBottom: 16 }}
            />
            
            <Form.Item label="导出格式" name="format" initialValue={exportFormat}>
              <Radio.Group onChange={(e) => setExportFormat(e.target.value)}>
                <Radio value="excel">Excel格式 (.xlsx)</Radio>
                <Radio value="csv">CSV格式 (.csv)</Radio>
              </Radio.Group>
            </Form.Item>
            
            <Form.Item label="导出字段" name="fields" initialValue={exportFields}>
              <Checkbox.Group
                options={exportFieldOptions}
                value={exportFields}
                onChange={setExportFields}
              />
            </Form.Item>
          </Form>
      </Modal>
      
      {/* 导入模态框 */}
      <Modal
          title="导入BOM数据"
          open={importModalVisible}
          onCancel={() => {
            setImportModalVisible(false);
            setImportStatus(null);
            setImportProgress(0);
            setImportResult(null);
            importForm.resetFields();
          }}
          footer={[
            <Button key="template" icon={<DownloadOutlined />} onClick={handleDownloadTemplate}>
              下载模板
            </Button>,
            <Button key="cancel" onClick={() => {
              setImportModalVisible(false);
              setImportStatus(null);
              setImportProgress(0);
              setImportResult(null);
              importForm.resetFields();
            }}>
              取消
            </Button>
          ]}
          width={600}
        >
          <Form form={importForm} layout="vertical">
            <Alert
              message="导入说明"
              description={
                <div>
                  <p>1. 请下载导入模板，按照模板格式填写数据</p>
                  <p>2. 支持CSV格式文件导入</p>
                  <p>3. BOM编码和名称为必填字段</p>
                  <p>4. 导入前请确保数据格式正确</p>
                </div>
              }
              type="info"
              style={{ marginBottom: 16 }}
            />
            
            <Form.Item label="选择文件">
              <Upload.Dragger
                accept=".csv,.xlsx,.xls"
                beforeUpload={handleFileUpload}
                showUploadList={false}
                disabled={importStatus === 'uploading' || importStatus === 'processing'}
              >
                <p className="ant-upload-drag-icon">
                  <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">支持CSV、Excel格式文件</p>
              </Upload.Dragger>
            </Form.Item>
            
            {importStatus && (
              <div style={{ marginTop: 16 }}>
                {importStatus === 'uploading' && (
                  <div>
                    <div style={{ marginBottom: 8 }}>正在上传文件...</div>
                    <Progress percent={importProgress} status="active" />
                  </div>
                )}
                
                {importStatus === 'processing' && (
                  <div>
                    <div style={{ marginBottom: 8 }}>正在处理数据...</div>
                    <Progress percent={100} status="active" />
                  </div>
                )}
                
                {importStatus === 'success' && importResult && (
                  <Alert
                    message="导入成功"
                    description={`成功导入 ${importResult.success} 条数据`}
                    type="success"
                    icon={<CheckCircleOutlined />}
                  />
                )}
                
                {importStatus === 'error' && importResult && (
                  <div>
                    <Alert
                      message="导入完成，部分数据有错误"
                      description={`成功：${importResult.success} 条，错误：${importResult.errors} 条`}
                      type="warning"
                      icon={<ExclamationCircleOutlined />}
                      style={{ marginBottom: 16 }}
                    />
                    
                    {importResult.errorMessages && importResult.errorMessages.length > 0 && (
                      <div>
                        <div style={{ fontWeight: 'bold', marginBottom: 8 }}>错误详情：</div>
                        <div style={{ maxHeight: 200, overflow: 'auto', backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                          {importResult.errorMessages.map((error: string, index: number) => (
                            <div key={index} style={{ color: '#ff4d4f', marginBottom: 4 }}>
                              {error}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </Form>
        </Modal>
    </div>
  );
};

export default React.memo(CoreBOMListPage);
