// API 基础配置
export const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api';

// 路由常量
export const ROUTES = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  
  // BOM管理
  CORE_BOM: '/bom/core',
  CORE_BOM_CREATE: '/bom/core/create',
  CORE_BOM_EDIT: '/bom/core/edit/:id',
  CORE_BOM_VIEW: '/bom/core/view/:id',
  
  ORDER_BOM: '/bom/order',
  ORDER_BOM_CREATE: '/bom/order/create',
  ORDER_BOM_DERIVE: '/bom/order/derive/:coreBomId',
  ORDER_BOM_VIEW: '/bom/order/view/:id',
  
  // 物料管理
  MATERIALS: '/materials',
  MATERIALS_CREATE: '/materials/create',
  MATERIALS_EDIT: '/materials/edit/:id',
  
  // 库存管理
  INVENTORY: '/inventory',
  INVENTORY_RECEIVE: '/inventory/receive',
  INVENTORY_ISSUE: '/inventory/issue',
  INVENTORY_ADJUST: '/inventory/adjust',
  
  REMNANTS: '/inventory/remnants',
  CUTTING_PLAN: '/inventory/cutting-plan',
  BATCH_TRACKING: '/inventory/batch-tracking',
  
  // 采购管理
  PURCHASE: '/purchase',
  PURCHASE_CREATE: '/purchase/create',
  PURCHASE_EDIT: '/purchase/edit/:id',
  PURCHASE_VIEW: '/purchase/view/:id',
  PURCHASE_REQUISITION: '/purchase/requisition',
  MRP_CALCULATION: '/purchase/mrp',
  PURCHASE_OPTIMIZATION: '/purchase/optimization',
  
  // 成本管理
  COST_ANALYSIS: '/cost/analysis',
  COST_REPORTS: '/cost/reports',
  WASTE_TRACKING: '/cost/waste',
  STANDARD_COST: '/cost/standard',
  AS_BUILT_BOM: '/service/as-built-bom',
  SPARE_PARTS: '/service/spare-parts',
  
  // 服务管理
  SERVICE_BOM: '/service/bom',
  DEVICE_ARCHIVE: '/service/devices',
  MAINTENANCE: '/service/maintenance',
  
  // ECN管理
  ECN: '/ecn',
  ECN_CREATE: '/ecn/create',
  ECN_REVIEW: '/ecn/review/:id',
  
  // 报告和分析
  REPORTS: '/reports',
  DASHBOARD_CONFIG: '/reports/dashboard',
  
  // 系统管理
  USERS: '/system/users',
  ROLES: '/system/roles',
  PERMISSIONS: '/system/permissions',
  SYSTEM_CONFIG: '/system/config',
  AUDIT_LOG: '/system/audit-log',
  
  // 移动端
  MOBILE: '/mobile',
  MOBILE_SCAN: '/mobile/scan',
  MOBILE_INVENTORY: '/mobile/inventory',
} as const;

// 用户角色常量
export const USER_ROLES = {
  ADMIN: 'ADMIN',
  BOM_MANAGER: 'BOM_MANAGER',
  SALES_PMC: 'SALES_PMC',
  PURCHASE_MANAGER: 'PURCHASE_MANAGER',
  PRODUCTION_PLANNER: 'PRODUCTION_PLANNER',
  WAREHOUSE_MANAGER: 'WAREHOUSE_MANAGER',
  FINANCE_MANAGER: 'FINANCE_MANAGER',
  SERVICE_TECHNICIAN: 'SERVICE_TECHNICIAN',
  QUALITY_MANAGER: 'QUALITY_MANAGER',
  OPERATOR: 'OPERATOR',
} as const;

// 权限常量
export const PERMISSIONS = {
  // BOM权限
  BOM_VIEW: 'BOM_VIEW',
  BOM_CREATE: 'BOM_CREATE',
  BOM_EDIT: 'BOM_EDIT',
  BOM_DELETE: 'BOM_DELETE',
  BOM_FREEZE: 'BOM_FREEZE',
  BOM_APPROVE: 'BOM_APPROVE',
  
  // 物料权限
  MATERIAL_VIEW: 'MATERIAL_VIEW',
  MATERIAL_CREATE: 'MATERIAL_CREATE',
  MATERIAL_EDIT: 'MATERIAL_EDIT',
  MATERIAL_DELETE: 'MATERIAL_DELETE',
  
  // 库存权限
  INVENTORY_VIEW: 'INVENTORY_VIEW',
  INVENTORY_RECEIVE: 'INVENTORY_RECEIVE',
  INVENTORY_ISSUE: 'INVENTORY_ISSUE',
  INVENTORY_ADJUST: 'INVENTORY_ADJUST',
  
  // 采购权限
  PURCHASE_VIEW: 'PURCHASE_VIEW',
  PURCHASE_CREATE: 'PURCHASE_CREATE',
  PURCHASE_APPROVE: 'PURCHASE_APPROVE',
  
  // 成本权限
  COST_VIEW: 'COST_VIEW',
  COST_ANALYSIS: 'COST_ANALYSIS',
  
  // ECN权限
  ECN_VIEW: 'ECN_VIEW',
  ECN_CREATE: 'ECN_CREATE',
  ECN_APPROVE: 'ECN_APPROVE',
  
  // 系统权限
  SYSTEM_CONFIG: 'SYSTEM_CONFIG',
  USER_MANAGE: 'USER_MANAGE',
  ROLE_MANAGE: 'ROLE_MANAGE',
} as const;

// 状态常量
export const BOM_STATUS = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  FROZEN: 'FROZEN',
  OBSOLETE: 'OBSOLETE',
} as const;

export const ORDER_STATUS = {
  DRAFT: 'DRAFT',
  CONFIRMED: 'CONFIRMED',
  FROZEN: 'FROZEN',
  CANCELLED: 'CANCELLED',
} as const;

export const ECN_STATUS = {
  DRAFT: 'DRAFT',
  REVIEW: 'REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  IMPLEMENTED: 'IMPLEMENTED',
} as const;

export const INVENTORY_STATUS = {
  AVAILABLE: 'AVAILABLE',
  RESERVED: 'RESERVED',
  EXPIRED: 'EXPIRED',
  DAMAGED: 'DAMAGED',
} as const;

// 业务常量
export const MATERIAL_CATEGORIES = [
  { value: '天线', label: '天线' },
  { value: '射频器件', label: '射频器件' },
  { value: '结构件', label: '结构件' },
  { value: '电子元器件', label: '电子元器件' },
  { value: '包装材料', label: '包装材料' },
  { value: '辅助材料', label: '辅助材料' },
] as const;

export const UNITS = [
  'PCS', // 个
  'SET', // 套
  'M',   // 米
  'KG',  // 千克
  'L',   // 升
  'M2',  // 平方米
  'M3',  // 立方米
] as const;

export const MATERIAL_UNITS = [
  { value: 'PCS', label: '个' },
  { value: 'SET', label: '套' },
  { value: 'M', label: '米' },
  { value: 'KG', label: '千克' },
  { value: 'L', label: '升' },
  { value: 'M2', label: '平方米' },
  { value: 'M3', label: '立方米' },
] as const;

export const WASTE_CATEGORIES = {
  PACKAGING: 'PACKAGING',
  MOQ: 'MOQ',
  CUTTING: 'CUTTING',
  EXPIRY: 'EXPIRY',
} as const;

export const PRIORITY_LEVELS = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT',
} as const;

// 表格配置
export const PAGE_SIZES = [10, 20, 50, 100] as const;
export const DEFAULT_PAGE_SIZE = 20;

// 主题配置
export const THEME_CONFIG = {
  primaryColor: '#1890ff',
  successColor: '#52c41a',
  warningColor: '#faad14',
  errorColor: '#f5222d',
  borderRadius: 6,
} as const;

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
    'application/pdf',
  ],
} as const;

// 本地存储键名
export const STORAGE_KEYS = {
  TOKEN: 'auth_token',
  USER_INFO: 'user_info',
  CURRENT_ROLE: 'current_role',
  THEME: 'theme',
  LANGUAGE: 'language',
  DASHBOARD_CONFIG: 'dashboard_config',
} as const;

// 消息类型
export const MESSAGE_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
} as const;

// 日期格式
export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIME: 'HH:mm:ss',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY',
} as const;

// 数值格式
export const NUMBER_FORMATS = {
  CURRENCY: '¥0,0.00',
  PERCENTAGE: '0.00%',
  INTEGER: '0,0',
  DECIMAL: '0,0.00',
} as const;

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  CODE: /^[A-Z0-9-]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
} as const;

// 错误代码
export const ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  SERVER_ERROR: 500,
} as const;

// 操作类型
export const OPERATION_TYPES = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  APPROVE: 'APPROVE',
  REJECT: 'REJECT',
  FREEZE: 'FREEZE',
  ACTIVATE: 'ACTIVATE',
} as const;
