import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Button,
  Space,
  Input,
  Select,
  Card,
  Tag,
  message,
  Modal,
  Form,
  Typography,
  Row,
  Col,
  Tooltip,
  Dropdown,
  MenuProps,
  Progress,
  Statistic,
  Checkbox,
} from 'antd';
import * as XLSX from 'xlsx';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined,
  ExportOutlined,
  ImportOutlined,
  DollarOutlined,
  CalendarOutlined,
  UserOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchOrderBOMs, deleteCoreBOM, freezeCoreBOM } from '../../store/slices/bomSlice';
import { OrderBOM } from '../../types';
import { ROUTES, ORDER_STATUS } from '../../constants';
import { formatDate, formatCurrency } from '../../utils';
import { ConfirmDialog } from '../../components';
import { errorHandler, ErrorType } from '../../utils/errorHandler';

const { Title } = Typography;
const { Search } = Input;

const OrderBOMListPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { orderBOMs, loading, pagination } = useAppSelector(state => state.bom);

  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [customerFilter, setCustomerFilter] = useState<string>('');
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportFormat, setExportFormat] = useState<'excel' | 'csv'>('excel');
  const [exportFields, setExportFields] = useState<string[]>(['orderNumber', 'customerName', 'coreBOMVersion', 'status', 'totalCost', 'deliveryDate']);
  const [copyModalVisible, setCopyModalVisible] = useState(false);
  const [copyingOrder, setCopyingOrder] = useState<OrderBOM | null>(null);
  const [copyForm] = Form.useForm();

  useEffect(() => {
    loadData();
  }, [pagination.current, pagination.pageSize, searchKeyword, statusFilter, customerFilter]);

  const loadData = () => {
    dispatch(fetchOrderBOMs({
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchKeyword,
    }));
  };

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
  };

  const handleCustomerFilter = (value: string) => {
    setCustomerFilter(value);
  };

  const handleCreate = () => {
    navigate(ROUTES.ORDER_BOM_CREATE);
  };

  const handleDerive = () => {
    // 显示核心BOM选择对话框
    Modal.info({
      title: '选择核心BOM',
      content: '请先选择要派生的核心BOM',
      onOk: () => {
        navigate('/bom/core'); // 跳转到核心BOM列表选择
      },
    });
  };

  const handleEdit = (record: OrderBOM) => {
    navigate(`/bom/order/edit/${record.id}`);
  };

  const handleView = (record: OrderBOM) => {
    navigate(ROUTES.ORDER_BOM_VIEW.replace(':id', record.id));
  };

  const handleDelete = async (record: OrderBOM) => {
    Modal.confirm({
      title: '确认删除',
      content: (
        <div>
          <p>确定要删除以下订单BOM吗？</p>
          <p><strong>订单号：</strong>{record.orderNumber}</p>
          <p><strong>客户名称：</strong>{record.customerName}</p>
          <p><strong>核心BOM：</strong>{record.coreBOMVersion}</p>
          <p style={{ color: '#ff4d4f', marginTop: 12 }}>此操作不可恢复，请谨慎操作！</p>
        </div>
      ),
      type: 'warning',
      onOk: async () => {
        try {
          await dispatch(deleteCoreBOM(record.id)).unwrap();
          message.success('删除成功');
          loadData();
        } catch (error: any) {
          errorHandler.handleError({
            type: ErrorType.BUSINESS,
            message: error.message || '删除失败',
            details: error,
            timestamp: Date.now()
          });
        }
      }
    });
  };

  const handleFreeze = async (record: OrderBOM) => {
    const isActive = record.status !== ORDER_STATUS.FROZEN;
    const action = isActive ? '冻结' : '解冻';
    
    Modal.confirm({
      title: `确认${action}`,
      content: (
        <div>
          <p>确定要{action}以下订单BOM吗？</p>
          <p><strong>订单号：</strong>{record.orderNumber}</p>
          <p><strong>客户名称：</strong>{record.customerName}</p>
          <p><strong>核心BOM：</strong>{record.coreBOMVersion}</p>
          {isActive && (
            <p style={{ color: '#faad14', marginTop: 12 }}>冻结后该订单BOM将无法被编辑！</p>
          )}
        </div>
      ),
      type: isActive ? 'warning' : 'info',
      onOk: async () => {
        try {
          await dispatch(freezeCoreBOM(record.id)).unwrap();
          message.success(`${action}成功`);
          loadData();
        } catch (error: any) {
          errorHandler.handleError({
            type: ErrorType.BUSINESS,
            message: error.message || `${action}失败`,
            details: error,
            timestamp: Date.now()
          });
        }
      }
    });
  };

  const handleExport = (record?: OrderBOM) => {
    setExportModalVisible(true);
  };

  const executeExport = () => {
    try {
      // 准备导出数据
      const exportData = mockData.map(order => {
        const data: any = {};
        
        if (exportFields.includes('orderNumber')) data['订单号'] = order.orderNumber;
        if (exportFields.includes('customerName')) data['客户名称'] = order.customerName;
        if (exportFields.includes('coreBOMVersion')) data['核心BOM'] = order.coreBOMVersion;
        if (exportFields.includes('status')) data['状态'] = getStatusText(order.status);
        if (exportFields.includes('totalCost')) data['总成本'] = order.totalCost;
        if (exportFields.includes('estimatedMargin')) data['预估毛利'] = `${(order.estimatedMargin * 100).toFixed(1)}%`;
        if (exportFields.includes('deliveryDate')) data['交期'] = formatDate(order.deliveryDate);
        if (exportFields.includes('createdAt')) data['创建时间'] = formatDate(order.createdAt);
        
        return data;
      });

      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '订单BOM列表');

      // 下载文件
      const fileName = `订单BOM列表_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;
      XLSX.writeFile(wb, fileName);
      
      message.success('导出成功');
      setExportModalVisible(false);
    } catch (error) {
      message.error('导出失败');
    }
  };

  const handleCopy = (record: OrderBOM) => {
    setCopyingOrder(record);
    copyForm.setFieldsValue({
      orderNumber: `${record.orderNumber}-COPY`,
      customerName: record.customerName,
      deliveryDate: null,
    });
    setCopyModalVisible(true);
  };

  const executeCopy = async () => {
    try {
      const values = await copyForm.validateFields();
      // TODO: 实现复制API调用
      message.success('复制成功');
      setCopyModalVisible(false);
      loadData();
    } catch (error) {
      message.error('复制失败');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case ORDER_STATUS.DRAFT: return 'default';
      case ORDER_STATUS.CONFIRMED: return 'processing';
      case ORDER_STATUS.FROZEN: return 'success';
      case ORDER_STATUS.CANCELLED: return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case ORDER_STATUS.DRAFT: return '草稿';
      case ORDER_STATUS.CONFIRMED: return '已确认';
      case ORDER_STATUS.FROZEN: return '已冻结';
      case ORDER_STATUS.CANCELLED: return '已取消';
      default: return status;
    }
  };

  const getActionMenuItems = (record: OrderBOM): MenuProps['items'] => [
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: '复制',
      onClick: () => handleCopy(record),
    },
    {
      key: 'export',
      icon: <ExportOutlined />,
      label: '导出',
      onClick: () => handleExport(record),
    },
    {
      key: 'freeze',
      icon: record.status === ORDER_STATUS.FROZEN ? <UnlockOutlined /> : <LockOutlined />,
      label: record.status === ORDER_STATUS.FROZEN ? '解冻' : '冻结',
      onClick: () => handleFreeze(record),
      disabled: record.status === ORDER_STATUS.DRAFT,
    },
  ];

  const columns = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120,
      render: (text: string, record: OrderBOM) => (
        <Button type="link" onClick={() => handleView(record)}>
          {text}
        </Button>
      ),
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      ellipsis: true,
    },
    {
      title: '核心BOM',
      dataIndex: 'coreBOMId',
      key: 'coreBOMId',
      width: 120,
      render: (coreBOMId: string, record: OrderBOM) => (
        <span>{record.coreBOMVersion}</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '总成本',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 100,
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: '预估毛利',
      dataIndex: 'estimatedMargin',
      key: 'estimatedMargin',
      width: 80,
      render: (margin: number) => `${(margin * 100).toFixed(1)}%`,
    },
    {
      title: '交期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      width: 100,
      render: (date: string) => formatDate(date),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: OrderBOM) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={record.status === ORDER_STATUS.FROZEN}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
              disabled={record.status !== ORDER_STATUS.DRAFT}
            />
          </Tooltip>
          <Dropdown
            menu={{ items: getActionMenuItems(record) }}
            trigger={['click']}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // 模拟数据
  const mockData: OrderBOM[] = [
    {
      id: '1',
      orderNumber: 'ORD-2024-001',
      customerName: '华为技术有限公司',
      customerConfig: { frequency: '5G', power: 100, antenna_type: 'directional' },
      coreBOMId: '1',
      coreBOMVersion: 'ANT-5G-001 V1.0',
      items: [],
      status: 'CONFIRMED',
      totalCost: 125000,
      estimatedMargin: 0.25,
      deliveryDate: '2024-02-15',
      createdBy: 'sales_pmc',
      createdAt: '2024-01-15T00:00:00Z',
      confirmedAt: '2024-01-16T00:00:00Z',
    },
    {
      id: '2',
      orderNumber: 'ORD-2024-002',
      customerName: '中兴通讯股份有限公司',
      customerConfig: { frequency: '4G', power: 80, antenna_type: 'omnidirectional' },
      coreBOMId: '2',
      coreBOMVersion: 'ANT-4G-002 V2.1',
      items: [],
      status: 'FROZEN',
      totalCost: 98000,
      estimatedMargin: 0.30,
      deliveryDate: '2024-02-20',
      createdBy: 'sales_pmc',
      createdAt: '2024-01-16T00:00:00Z',
      confirmedAt: '2024-01-17T00:00:00Z',
      frozenAt: '2024-01-18T00:00:00Z',
    },
    {
      id: '3',
      orderNumber: 'ORD-2024-003',
      customerName: '大唐移动通信设备有限公司',
      customerConfig: { frequency: '5G', power: 120, antenna_type: 'smart' },
      coreBOMId: '1',
      coreBOMVersion: 'ANT-5G-001 V1.0',
      items: [],
      status: 'DRAFT',
      totalCost: 156000,
      estimatedMargin: 0.22,
      deliveryDate: '2024-03-01',
      createdBy: 'sales_pmc',
      createdAt: '2024-01-17T00:00:00Z',
    },
  ];

  // 统计数据
  const stats = {
    total: mockData.length,
    draft: mockData.filter(item => item.status === ORDER_STATUS.DRAFT).length,
    confirmed: mockData.filter(item => item.status === ORDER_STATUS.CONFIRMED).length,
    frozen: mockData.filter(item => item.status === ORDER_STATUS.FROZEN).length,
    totalValue: mockData.reduce((sum, item) => sum + item.totalCost, 0),
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={stats.total}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="草稿"
              value={stats.draft}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已确认"
              value={stats.confirmed}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总价值"
              value={stats.totalValue}
              prefix={<DollarOutlined />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              订单BOM管理
            </Title>
          </Col>
          <Col>
            <Space>
              <Button icon={<ImportOutlined />}>
                导入
              </Button>
              <Button icon={<ExportOutlined />} onClick={() => handleExport()}>
                批量导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleDerive}>
                派生订单BOM
              </Button>
              <Button icon={<PlusOutlined />} onClick={handleCreate}>
                手动创建
              </Button>
            </Space>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={8} md={6}>
            <Search
              placeholder="搜索订单号或客户名称"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="状态筛选"
              allowClear
              style={{ width: '100%' }}
              onChange={handleStatusFilter}
              options={[
                { label: '草稿', value: ORDER_STATUS.DRAFT },
                { label: '已确认', value: ORDER_STATUS.CONFIRMED },
                { label: '已冻结', value: ORDER_STATUS.FROZEN },
                { label: '已取消', value: ORDER_STATUS.CANCELLED },
              ]}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="客户筛选"
              allowClear
              style={{ width: '100%' }}
              onChange={handleCustomerFilter}
              options={[
                { label: '华为技术', value: '华为技术有限公司' },
                { label: '中兴通讯', value: '中兴通讯股份有限公司' },
                { label: '大唐移动', value: '大唐移动通信设备有限公司' },
                { label: '爱立信', value: '爱立信（中国）通信有限公司' },
              ]}
            />
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={mockData}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1200 }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 导出模态框 */}
      <Modal
        title="导出订单BOM数据"
        open={exportModalVisible}
        onOk={executeExport}
        onCancel={() => setExportModalVisible(false)}
        okText="导出"
        cancelText="取消"
        width={600}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Typography.Text strong>导出格式：</Typography.Text>
            <Select
              value={exportFormat}
              onChange={setExportFormat}
              style={{ width: 120, marginLeft: 8 }}
            >
              <Select.Option value="excel">Excel</Select.Option>
              <Select.Option value="csv">CSV</Select.Option>
            </Select>
          </div>
          
          <div>
            <Typography.Text strong>导出字段：</Typography.Text>
            <Checkbox.Group
              value={exportFields}
              onChange={setExportFields}
              style={{ marginTop: 8 }}
            >
              <Row>
                <Col span={12}>
                  <Checkbox value="orderNumber">订单号</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="customerName">客户名称</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="coreBOMVersion">核心BOM</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="status">状态</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="totalCost">总成本</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="estimatedMargin">预估毛利</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="deliveryDate">交期</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="createdAt">创建时间</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </div>
        </Space>
      </Modal>

      {/* 复制模态框 */}
      <Modal
        title="复制订单BOM"
        open={copyModalVisible}
        onOk={executeCopy}
        onCancel={() => setCopyModalVisible(false)}
        okText="确认复制"
        cancelText="取消"
        width={500}
      >
        <Form
          form={copyForm}
          layout="vertical"
          initialValues={{
            orderNumber: '',
            customerName: '',
            deliveryDate: null,
          }}
        >
          <Form.Item
            label="新订单号"
            name="orderNumber"
            rules={[
              { required: true, message: '请输入订单号' },
              { pattern: /^[A-Z0-9-]+$/, message: '订单号只能包含大写字母、数字和连字符' },
            ]}
          >
            <Input placeholder="请输入新的订单号" />
          </Form.Item>
          
          <Form.Item
            label="客户名称"
            name="customerName"
            rules={[{ required: true, message: '请输入客户名称' }]}
          >
            <Input placeholder="请输入客户名称" />
          </Form.Item>
          
          <Form.Item
            label="交期"
            name="deliveryDate"
            rules={[{ required: true, message: '请选择交期' }]}
          >
            <Input type="date" />
          </Form.Item>
          
          <Typography.Text type="secondary">
            注意：复制将创建一个新的订单BOM，包含原订单的所有配置和物料清单，但状态将重置为草稿。
          </Typography.Text>
        </Form>
      </Modal>
    </div>
  );
};

export default OrderBOMListPage;
