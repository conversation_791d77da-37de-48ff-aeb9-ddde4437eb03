import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  Tag,
  Modal,
  Form,
  Input,
  Checkbox,
  Divider,
  Alert,
  Progress,
  Tooltip,
} from 'antd';
import * as XLSX from 'xlsx';
import {
  FileExcelOutlined,
  FilePdfOutlined,
  PrinterOutlined,
  DownloadOutlined,
  EyeOutlined,
  SettingOutlined,
  CalendarOutlined,
  DollarOutlined,
  RiseOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchCostReports } from '../../store/slices/costSlice';
import { formatCurrency, formatDate } from '../../utils';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const CostReportsPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { costReports, loading } = useAppSelector(state => state.cost);

  const [reportType, setReportType] = useState<string>('monthly');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(1, 'month'),
    dayjs(),
  ]);
  const [customReportVisible, setCustomReportVisible] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [customForm] = Form.useForm();

  useEffect(() => {
    loadData();
  }, [reportType, dateRange]);

  const loadData = () => {
    dispatch(fetchCostReports({
      type: reportType,
      startDate: dateRange[0].format('YYYY-MM-DD'),
      endDate: dateRange[1].format('YYYY-MM-DD'),
    }));
  };

  const handleExport = (format: string, reportId?: string) => {
    try {
      let exportData: any[] = [];
      let fileName = '';

      if (reportId) {
        // 导出特定报告
        const report = mockReports.find(r => r.id === reportId);
        if (report) {
          exportData = [{
            '报告名称': report.name,
            '报告类型': report.type,
            '生成时间': report.createdAt,
            '总成本': `¥${report.totalCost.toLocaleString()}`,
            '状态': report.status,
          }];
          fileName = `成本报告_${report.name}_${new Date().toISOString().split('T')[0]}`;
        }
      } else {
        // 导出所有报告
        exportData = mockReports.map(report => ({
          '报告名称': report.name,
          '报告类型': report.type,
          '生成时间': report.createdAt,
          '总成本': `¥${report.totalCost.toLocaleString()}`,
          '状态': report.status,
        }));
        fileName = `成本报告汇总_${new Date().toISOString().split('T')[0]}`;
      }

      if (format === 'excel') {
        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, '成本报告');
        XLSX.writeFile(wb, `${fileName}.xlsx`);
      } else if (format === 'csv') {
        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, '成本报告');
        XLSX.writeFile(wb, `${fileName}.csv`);
      }

      Modal.success({
        title: '导出成功',
        content: `报告已导出为 ${format.toUpperCase()} 格式`,
      });
    } catch (error) {
      Modal.error({
        title: '导出失败',
        content: '导出报告时发生错误',
      });
    }
  };

  const handlePreview = (report: any) => {
    setSelectedReport(report);
    setPreviewVisible(true);
  };

  const handleCustomReport = async () => {
    try {
      const values = await customForm.validateFields();
      // TODO: 生成自定义报告
      console.log('自定义报告参数:', values);
      setCustomReportVisible(false);
      Modal.success({
        title: '报告生成成功',
        content: '自定义报告已生成，请在报告列表中查看',
      });
    } catch (error) {
      console.error('生成报告失败:', error);
    }
  };

  // 模拟报告数据
  const mockReports = [
    {
      id: '1',
      name: '2024年3月成本分析报告',
      type: '月度报告',
      period: '2024-03',
      totalCost: 2850000,
      wasteAmount: 156000,
      wastePercentage: 5.47,
      marginAmount: 712500,
      marginPercentage: 25.0,
      status: '已完成',
      createdAt: '2024-04-01T00:00:00Z',
      createdBy: 'finance_manager',
    },
    {
      id: '2',
      name: '2024年Q1季度成本报告',
      type: '季度报告',
      period: '2024-Q1',
      totalCost: 8250000,
      wasteAmount: 452000,
      wastePercentage: 5.48,
      marginAmount: 2062500,
      marginPercentage: 25.0,
      status: '已完成',
      createdAt: '2024-04-05T00:00:00Z',
      createdBy: 'finance_manager',
    },
    {
      id: '3',
      name: '华为项目成本专项报告',
      type: '专项报告',
      period: '2024-03',
      totalCost: 1250000,
      wasteAmount: 68000,
      wastePercentage: 5.44,
      marginAmount: 312500,
      marginPercentage: 25.0,
      status: '进行中',
      createdAt: '2024-03-28T00:00:00Z',
      createdBy: 'finance_manager',
    },
  ];

  const reportColumns = [
    {
      title: '报告名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '报告类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={type === '月度报告' ? 'blue' : type === '季度报告' ? 'green' : 'orange'}>
          {type}
        </Tag>
      ),
    },
    {
      title: '报告期间',
      dataIndex: 'period',
      key: 'period',
      width: 100,
    },
    {
      title: '总成本',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 120,
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: '浪费金额',
      dataIndex: 'wasteAmount',
      key: 'wasteAmount',
      width: 120,
      render: (amount: number, record: any) => (
        <Space direction="vertical" size={0}>
          <Text style={{ color: '#ff4d4f' }}>{formatCurrency(amount)}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.wastePercentage.toFixed(2)}%
          </Text>
        </Space>
      ),
    },
    {
      title: '毛利',
      dataIndex: 'marginAmount',
      key: 'marginAmount',
      width: 120,
      render: (margin: number, record: any) => (
        <Space direction="vertical" size={0}>
          <Text style={{ color: '#52c41a' }}>{formatCurrency(margin)}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.marginPercentage.toFixed(1)}%
          </Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === '已完成' ? 'green' : 'processing'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right' as const,
      render: (_: any, record: any) => (
        <Space size="small">
          <Tooltip title="预览">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handlePreview(record)}
            />
          </Tooltip>
          <Tooltip title="导出Excel">
            <Button
              type="text"
              icon={<FileExcelOutlined />}
              onClick={() => handleExport('excel', record.id)}
            />
          </Tooltip>
          <Tooltip title="导出PDF">
            <Button
              type="text"
              icon={<FilePdfOutlined />}
              onClick={() => handleExport('pdf', record.id)}
            />
          </Tooltip>
          <Tooltip title="打印">
            <Button
              type="text"
              icon={<PrinterOutlined />}
              onClick={() => window.print()}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 统计数据
  const stats = {
    totalReports: mockReports.length,
    completedReports: mockReports.filter(r => r.status === '已完成').length,
    avgWasteRate: mockReports.reduce((sum, r) => sum + r.wastePercentage, 0) / mockReports.length,
    totalCostSum: mockReports.reduce((sum, r) => sum + r.totalCost, 0),
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="报告总数"
              value={stats.totalReports}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completedReports}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均浪费率"
              value={stats.avgWasteRate}
              precision={2}
              suffix="%"
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总成本"
              value={stats.totalCostSum}
              prefix={<DollarOutlined />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              成本报告
            </Title>
          </Col>
          <Col>
            <Space>
              <Select
                value={reportType}
                onChange={setReportType}
                style={{ width: 120 }}
                options={[
                  { label: '月度报告', value: 'monthly' },
                  { label: '季度报告', value: 'quarterly' },
                  { label: '年度报告', value: 'yearly' },
                  { label: '专项报告', value: 'special' },
                ]}
              />
              <RangePicker
                value={dateRange}
                onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                style={{ width: 240 }}
              />
              <Button
                icon={<SettingOutlined />}
                onClick={() => setCustomReportVisible(true)}
              >
                自定义报告
              </Button>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={() => handleExport('excel')}
              >
                批量导出
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={reportColumns}
          dataSource={mockReports}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1200 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 自定义报告模态框 */}
      <Modal
        title="生成自定义报告"
        open={customReportVisible}
        onOk={handleCustomReport}
        onCancel={() => setCustomReportVisible(false)}
        width={600}
        okText="生成报告"
        cancelText="取消"
      >
        <Form form={customForm} layout="vertical">
          <Form.Item
            name="reportName"
            label="报告名称"
            rules={[{ required: true, message: '请输入报告名称' }]}
          >
            <Input placeholder="请输入报告名称" />
          </Form.Item>

          <Form.Item
            name="reportType"
            label="报告类型"
            rules={[{ required: true, message: '请选择报告类型' }]}
          >
            <Select placeholder="请选择报告类型">
              <Select.Option value="cost_analysis">成本分析报告</Select.Option>
              <Select.Option value="waste_analysis">浪费分析报告</Select.Option>
              <Select.Option value="margin_analysis">毛利分析报告</Select.Option>
              <Select.Option value="trend_analysis">趋势分析报告</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="报告期间"
            rules={[{ required: true, message: '请选择报告期间' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="includeItems" label="包含内容">
            <Checkbox.Group>
              <Row>
                <Col span={12}>
                  <Checkbox value="cost_overview">成本概览</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="waste_analysis">浪费分析</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="trend_charts">趋势图表</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="order_details">订单明细</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="supplier_analysis">供应商分析</Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox value="recommendations">改善建议</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item name="description" label="报告描述">
            <TextArea rows={3} placeholder="请输入报告描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 报告预览模态框 */}
      <Modal
        title={`预览报告: ${selectedReport?.name}`}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button key="export" type="primary" icon={<DownloadOutlined />}>
            导出
          </Button>,
        ]}
      >
        {selectedReport && (
          <div>
            <Alert
              message="报告预览"
              description="这是报告的预览版本，完整内容请导出查看"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Divider orientation="left">报告概要</Divider>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Statistic
                  title="总成本"
                  value={selectedReport.totalCost}
                  formatter={(value) => formatCurrency(Number(value))}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="浪费金额"
                  value={selectedReport.wasteAmount}
                  formatter={(value) => formatCurrency(Number(value))}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="毛利"
                  value={selectedReport.marginAmount}
                  formatter={(value) => formatCurrency(Number(value))}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
            </Row>

            <Divider orientation="left">关键指标</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text>浪费率: </Text>
                <Progress
                  percent={selectedReport.wastePercentage}
                  strokeColor="#ff4d4f"
                  format={(percent) => `${percent?.toFixed(2)}%`}
                />
              </div>
              <div>
                <Text>毛利率: </Text>
                <Progress
                  percent={selectedReport.marginPercentage}
                  strokeColor="#52c41a"
                  format={(percent) => `${percent?.toFixed(1)}%`}
                />
              </div>
            </Space>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CostReportsPage;
