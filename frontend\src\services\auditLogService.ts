import { message } from 'antd';

// 审计日志类型定义
export interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  userRole: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValue?: any;
  newValue?: any;
  reason?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  status: 'success' | 'failed';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  module: string;
  details?: string;
}

// 审计统计信息
export interface AuditStatistics {
  totalLogs: number;
  todayLogs: number;
  highRiskLogs: number;
  failedOperations: number;
  topUsers: Array<{
    userId: string;
    userName: string;
    count: number;
  }>;
  topActions: Array<{
    action: string;
    count: number;
  }>;
  riskDistribution: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

// 审计查询参数
export interface AuditQueryParams {
  page?: number;
  pageSize?: number;
  userId?: string;
  action?: string;
  resource?: string;
  module?: string;
  riskLevel?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  keyword?: string;
}

// 审计日志服务类
export class AuditLogService {
  private static baseUrl = '/api/audit-logs';

  // 获取审计日志列表
  static async getAuditLogs(params: AuditQueryParams = {}) {
    try {
      // 模拟API调用
      const mockData = this.generateMockAuditLogs();
      
      // 模拟筛选
      let filteredData = mockData;
      
      if (params.userId) {
        filteredData = filteredData.filter(log => log.userId.includes(params.userId!));
      }
      
      if (params.action) {
        filteredData = filteredData.filter(log => log.action.includes(params.action!));
      }
      
      if (params.resource) {
        filteredData = filteredData.filter(log => log.resource.includes(params.resource!));
      }
      
      if (params.module) {
        filteredData = filteredData.filter(log => log.module === params.module);
      }
      
      if (params.riskLevel) {
        filteredData = filteredData.filter(log => log.riskLevel === params.riskLevel);
      }
      
      if (params.status) {
        filteredData = filteredData.filter(log => log.status === params.status);
      }
      
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase();
        filteredData = filteredData.filter(log => 
          log.userName.toLowerCase().includes(keyword) ||
          log.action.toLowerCase().includes(keyword) ||
          log.resource.toLowerCase().includes(keyword) ||
          log.details?.toLowerCase().includes(keyword)
        );
      }
      
      // 模拟分页
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      return {
        data: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      };
    } catch (error) {
      message.error('获取审计日志失败');
      throw error;
    }
  }

  // 获取审计日志详情
  static async getAuditLogDetail(id: string): Promise<AuditLog> {
    try {
      // 模拟API调用
      const mockData = this.generateMockAuditLogs();
      const log = mockData.find(item => item.id === id);
      
      if (!log) {
        throw new Error('审计日志不存在');
      }
      
      return log;
    } catch (error) {
      message.error('获取审计日志详情失败');
      throw error;
    }
  }

  // 获取审计统计信息
  static async getAuditStatistics(): Promise<AuditStatistics> {
    try {
      // 模拟API调用
      return {
        totalLogs: 15420,
        todayLogs: 156,
        highRiskLogs: 23,
        failedOperations: 8,
        topUsers: [
          { userId: 'user001', userName: '张三', count: 245 },
          { userId: 'user002', userName: '李四', count: 198 },
          { userId: 'user003', userName: '王五', count: 167 },
          { userId: 'user004', userName: '赵六', count: 134 },
          { userId: 'user005', userName: '钱七', count: 112 }
        ],
        topActions: [
          { action: '登录系统', count: 1245 },
          { action: '查看BOM', count: 856 },
          { action: '编辑物料', count: 634 },
          { action: '创建订单', count: 523 },
          { action: '删除记录', count: 234 }
        ],
        riskDistribution: {
          low: 12450,
          medium: 2340,
          high: 520,
          critical: 110
        }
      };
    } catch (error) {
      message.error('获取审计统计信息失败');
      throw error;
    }
  }

  // 导出审计日志
  static async exportAuditLogs(params: AuditQueryParams = {}) {
    try {
      // 模拟API调用
      message.success('审计日志导出成功');
      
      // 实际实现中应该返回文件下载链接或直接下载文件
      return {
        downloadUrl: '/api/audit-logs/export?token=xxx',
        fileName: `audit_logs_${new Date().toISOString().split('T')[0]}.xlsx`
      };
    } catch (error) {
      message.error('导出审计日志失败');
      throw error;
    }
  }

  // 清理过期日志
  static async cleanupExpiredLogs(retentionDays: number) {
    try {
      // 模拟API调用
      message.success(`已清理${retentionDays}天前的审计日志`);
      
      return {
        deletedCount: 1250,
        retentionDays
      };
    } catch (error) {
      message.error('清理过期日志失败');
      throw error;
    }
  }

  // 生成模拟审计日志数据
  private static generateMockAuditLogs(): AuditLog[] {
    const actions = [
      '登录系统', '退出系统', '创建BOM', '编辑BOM', '删除BOM', '查看BOM',
      '创建物料', '编辑物料', '删除物料', '查看物料', '库存调整', '库存盘点',
      '创建订单', '编辑订单', '删除订单', '审核订单', '创建用户', '编辑用户',
      '删除用户', '修改权限', '系统配置', '数据导出', '数据导入', '备份数据'
    ];
    
    const resources = [
      'BOM', '物料', '库存', '订单', '用户', '角色', '权限', '系统配置',
      '审计日志', '报告', '仪表板', '设备档案', 'ECN', '采购申请', '成本分析'
    ];
    
    const modules = [
      'BOM管理', '物料管理', '库存管理', '订单管理', '用户管理', '系统管理',
      '报告中心', '成本管理', '服务管理', 'ECN管理', '采购管理'
    ];
    
    const users = [
      { id: 'user001', name: '张三', role: '系统管理员' },
      { id: 'user002', name: '李四', role: 'BOM管理员' },
      { id: 'user003', name: '王五', role: '库存管理员' },
      { id: 'user004', name: '赵六', role: '采购员' },
      { id: 'user005', name: '钱七', role: '质检员' },
      { id: 'user006', name: '孙八', role: '操作员' }
    ];
    
    const riskLevels: Array<'low' | 'medium' | 'high' | 'critical'> = ['low', 'medium', 'high', 'critical'];
    const statuses: Array<'success' | 'failed'> = ['success', 'failed'];
    
    const logs: AuditLog[] = [];
    
    for (let i = 0; i < 100; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const action = actions[Math.floor(Math.random() * actions.length)];
      const resource = resources[Math.floor(Math.random() * resources.length)];
      const module = modules[Math.floor(Math.random() * modules.length)];
      const riskLevel = riskLevels[Math.floor(Math.random() * riskLevels.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      // 生成时间戳（最近30天内）
      const timestamp = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString();
      
      logs.push({
        id: `audit_${String(i + 1).padStart(6, '0')}`,
        userId: user.id,
        userName: user.name,
        userRole: user.role,
        action,
        resource,
        resourceId: `${resource.toLowerCase()}_${Math.floor(Math.random() * 1000)}`,
        oldValue: action.includes('编辑') ? { name: '旧值', status: '原状态' } : undefined,
        newValue: action.includes('编辑') ? { name: '新值', status: '新状态' } : undefined,
        reason: action.includes('删除') ? '数据清理' : undefined,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp,
        status,
        riskLevel,
        module,
        details: `用户${user.name}在${module}模块执行了${action}操作`
      });
    }
    
    // 按时间戳降序排序
    return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }
}

export default AuditLogService;